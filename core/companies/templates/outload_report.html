{% load static %}
{% load app_filters %}
{% with date_time_format="d/m/Y, h:i A" date_format="d/m/Y" table_width_for_pdf="1240px" table_width="900px"%}

<html>
  <head>
    <meta charset=utf-8>
  </head>
  <body>
    <p>Hi All,</p>
    {% with date_fmt="%b %d, %Y" tz=company.country.timezone %}
    {% with date_tz=date_fmt|add:"|"|add:tz %}
    {% if report_data %}
      <p> Please see below a summary of outloads for {{company.business_name}} for {{ send_date_time | localize_tz:date_tz}}  </p>
      {% if summary %}
        <hr style="border-top: 1px dashed #ccc; margin: 5px 0;">
        <span><b>Summary {% if farm_names %} for {{ farm_term|lower }} : {{ farm_names }}{% else %} across all {{ farm_term|lower }}:{% endif %}</b></span>
        <br/>
        {% for key, value in summary.items %}
            {% if key == 'Stocks remaining' %}
                Stocks remaining {% if farm_names %} on {{ farm_term|lower }} : {{ farm_names }}{% else %} across all {{ farm_term|lower }}:{% endif %}
                <br/>
                {% for sub_key, sub_value in value.items %}
                    <span>
                    <b>{{ sub_key }} : {{ sub_value }}</b>
                    </span>
                    <br/>
                {% endfor %}
            {% else %}
                {{ key }} :  <b>{{ value }}</b>
            {% endif %}
          <br/>
        {% endfor %}
        <hr style="border-top: 1px dashed #ccc; margin: 5px 0;">
      {% endif %}
          {% for data in report_data %}
          <br/>
            {% for key, value in data.items %}
          <br/>
                {% if key == 'grade_data' %}

                      {% for grade_record_data in value %}

                        {% for grade_record_data_key, grade_record_data_value in grade_record_data.items %}
                            {%if grade_record_data_key == 'Specs' %}
                                <br/>

                              {% if grade_record_data_value.items %}
                                <span style="padding-left: 70px;">
                                  {{ grade_record_data_key }} :
                                </span>
                              {% endif %}
                              <ul>
                              <ul>
                              <ul>
                              {% for spec, avg in grade_record_data_value.items %}
                                <li>
                                      <b>{{spec | upper}}</b> - {{avg | floatformat:2}}
                                </li>
                              {% endfor %}
                              </ul>
                              </ul>
                              </ul>
                            {% else %}
                              <br/>
                              <span style="padding-left: 70px;">
                              {{ grade_record_data_key }} :  <b>{{ grade_record_data_value }}</b>
                              </span>
                            {% endif %}

                        {% endfor %}
                      {% endfor %}

                {% else %}
                  {{ key }} :  <b>{{ value }}</b>
                {% endif %}
            {% endfor %}

          {% endfor %}
    {% else %}

      No outloads have been recorded for {{ send_date_time | localize_tz:date_tz}}.

    {% endif %}

    <br/>
    <br/>
    Have a great day!
    <br/>
    Thank you.
    <br/>
    <br/>
    AgriChain
  {% endwith %}
  {% endwith %}
  </body>
</html>
{% endwith %}