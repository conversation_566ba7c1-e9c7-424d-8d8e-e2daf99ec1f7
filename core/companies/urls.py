import os

from django.urls import path

from core.companies.views import web_views, mobile_views
from core.trucks.views import CompanyManagedTrucksView, CompanyManagedTruckView

app_name = 'core.companies'

urlpatterns = [
    path('global-search/<str:search_str>/', web_views.GlobalCompaniesView.as_view(), name='company-search'),
    path('', web_views.CompaniesView.as_view(), name='company'),
    path('groups/', web_views.CompaniesView.as_view(), name='company-groups'),
    path('stats/', web_views.CompanyStatsView.as_view(), name='company-stats'),
    path('BHCs/', web_views.BHCListView.as_view(), name='company-bhcs'),
    path('names/', web_views.CompaniesNameView.as_view(), name='company-names'),
    path('<integer:company_id>/groups/<int:group_id>/companies/', web_views.GroupCompaniesView.as_view(),
         name='company-group-companies'),
    path('<integer:company_id>/groups/<int:group_id>/companies/minimal/',
         web_views.GroupCompaniesMinimalisticView.as_view(),
         name='company-group-companies-minimal'),
    path('<integer:company_id>/groups/', web_views.CompanyGroupsView.as_view(), name='company-groups'),
    path('<integer:company_id>/history/', web_views.CompanyHistoryView.as_view(), name='company-history'),
    path('minimal/', web_views.CompaniesMinimalisticView.as_view(), name='company-minimal'),
    path('directory/names/', web_views.CompaniesDirectoryView.as_view(), name='company-directory'),
    path(
        '<integer:company_id>/add-to-directory/',
        web_views.AddCompaniesToDirectoryView.as_view(),
        name='company-add-to-directory'
    ),
    path('managed/companies/', web_views.ManagedCompaniesView.as_view(), name='company-managed-company'),
    path('download-stocks-upload/', web_views.CompaniesStockUploadCSV.as_view(), name='company-stocks-upload'),
    path(
        'minimal/all/',
        web_views.AllCompaniesMinimalisticView.as_view(),
        name='company-minimal'
    ),
    path(
        'minimal/registered/',
        web_views.AllRegisteredCompaniesMinimalisticView.as_view(),
        name='company-registered-all'
    ),
    path('types/', web_views.CompanyTypesView.as_view(), name='company-types'),
    path('<integer:company_id>/companies/', web_views.CompanyCompaniesView.as_view(), name='company-company'),
    path(
        '<integer:company_id>/external/portals/', web_views.CompanyExternalPortalsView.as_view(),
        name='external_portal-list'
    ),
    path(
        '<integer:company_id>/external/portals/<int:portal_id>/', web_views.CompanyExternalPortalView.as_view(),
        name='external_portal-company'
    ),
    path(
        '<integer:company_id>/impex-docs/authorize/', web_views.CompanyImpexDocsAuthorizeView.as_view(),
        name='company-impexdocs-connection'
    ),
    path(
        '<integer:company_id>/impex-docs/connections/', web_views.CompanyImpexDocsConnectionView.as_view(),
        name='company-impexdocs-connection'
    ),
    path('<integer:company_id>/impex-docs/mappings/products/', web_views.CompanyImpexDocsProductMappingsView.as_view(),
         name='company-impexdocs-mappings-product'),
    path('<integer:company_id>/impex-docs/product-master/', web_views.CompanyImpexDocsProductMasterView.as_view(),
         name='company-impexdocs-product-master'),
    path(
        '<integer:company_id>/impex-docs/mappings/<int:mapping_id>/',
        web_views.CompanyImpexDocsProductMappingView.as_view(),
        name='company-impexdocs_mapping-detail'
    ),
    path(
        '<integer:company_id>/commodities/most-used/',
        web_views.CompanyCommoditiesMostUsedListView.as_view(),
        name='company-commodity-most-used'
    ),
    path(
        '<integer:company_id>/commodities/specs/',
        web_views.CompanyCommoditiesSpecsMostUsedListView.as_view(),
        name='company-commodity-specs-most-used'
    ),
    path(
        '<integer:company_id>/xero/connections/fixtures/', web_views.CompanyXeroFixturesView.as_view(),
        name='company-xero-connection-fixtures'
    ),
    path(
        '<integer:company_id>/xero/connections/', web_views.CompanyXeroConnectionView.as_view(),
        name='company-xero-connection'
    ),
    path(
        '<integer:company_id>/xero/exchange-code/', web_views.CompanyXeroExchangeCodeView.as_view(),
        name='company-xero-exchange-code'
    ),
    path(
        '<integer:company_id>/xero/mappings/', web_views.CompanyXeroMappingsView.as_view(),
        name='xero_mapping-company-list'
    ),
    path(
        '<integer:company_id>/xero/mappings/<int:mapping_id>/', web_views.CompanyXeroMappingView.as_view(),
        name='xero_mapping-company-detail'
    ),
    path(
        '<integer:company_id>/xero/tracking-categories/', web_views.CompanyXeroTrackingCategoriesView.as_view(),
        name='xero_mapping-company-detail'
    ),
    path(
        '<integer:company_id>/power-bi/report/', web_views.CompanyPowerBIReportView.as_view(),
        name='company-power-bi-report'
    ),
    path(
        '<integer:company_id>/companies/minimal/',
        web_views.CompanyCompaniesMinimalisticView.as_view(),
        name='company-company-minimal'
    ),
    path(
        '<integer:company_id>/companies/web/',
        web_views.CompanyCompaniesWebView.as_view(),
        name='company-company-web'
    ),
    path(
        '<integer:company_id>/companies/web/minimal/',
        web_views.CompanyCompaniesWebMinimalisticView.as_view(),
        name='company-company-web-minimal'
    ),
    path(
        '<integer:company_id>/companies/web/search/',
        web_views.CompanyCompaniesSearchView.as_view(),
        name='company-company-search'
    ),
    path(
        '<integer:company_id>/groups/<int:group_id>/companies/web/search/',
        web_views.GroupCompaniesSearchView.as_view(),
        name='company-group-companies-search'
    ),
    path(
        '<integer:company_id>/farms/web/',
        web_views.CompanyFarmsWebView.as_view(),
        name='farm-company-web'
    ),
    path(
        '<integer:company_id>/farms/web/search/',
        web_views.CompanyFarmsSearchView.as_view(),
        name='farm-company-search'
    ),
    path(
        '<integer:company_id>/farms/',
        web_views.CompanyFarmsView.as_view(),
        name='farm-company'
    ),
    path('<integer:company_id>/report-recipients/',
         web_views.CompanyReportRecipientsView.as_view(),
         name='company-report-recipients'),
    path('<integer:company_id>/', web_views.CompanyView.as_view(), name='company-detail'),
    path('<integer:company_id>/overview/', web_views.CompanyOverviewView.as_view(), name='company-overview'),
    path(
        '<integer:company_id>/employees/',
        web_views.CompanyEmployeesView.as_view(),
        name='employee-company'
    ),
    path(
        'unregistered/<integer:company_id>/employees/',
        web_views.UnregisteredCompanyEmployeesView.as_view(),
        name='employee-company_unregistered'
    ),
    path(
        '<integer:company_id>/employees/minimal/',
        web_views.CompanyEmployeesMinimalisticView.as_view(),
        name='employee-company-minimal'
    ),
    path(
        '<integer:company_id>/employees/<integer:employee_id>/',
        web_views.CompanyEmployeeView.as_view(),
        name='employee-company-detail'
    ),
    path(
        'unregistered/<integer:company_id>/employees/<integer:employee_id>/',
        web_views.UnregisteredCompanyEmployeeView.as_view(),
        name='employee-company-unregistered-detail'
    ),
    path(
        '<integer:company_id>/trailers/unassigned/',
        web_views.CompanyUnassignedTrailersView.as_view(),
        name='trailer-unassigned-company'
    ),
    path(
        '<integer:company_id>/trailers/',
        web_views.CompanyTrailersView.as_view(),
        name='trailer-company'
    ),
    path(
        '<integer:company_id>/managed-trucks/',
        CompanyManagedTrucksView.as_view(),
        name='managed_truck-company'
    ),
    path(
        '<integer:company_id>/managed-trucks/<integer:truck_id>/',
        CompanyManagedTruckView.as_view(),
        name='managed_trucks-company-truck'
    ),
    path(
        '<integer:company_id>/trucks/web/',
        web_views.CompanyTrucksWebView.as_view(),
        name='truck-company-web'
    ),
    path(
        '<integer:company_id>/trucks/',
        web_views.CompanyTrucksView.as_view(),
        name='truck-company'
    ),
    path(
        '<integer:company_id>/trucks/minimal/',
        web_views.CompanyTrucksMinimalisticView.as_view(),
        name='truck-company-minimal'
    ),
    path(
        '<integer:company_id>/trucks/<int:truck_id>/',
        web_views.CompanyTruckView.as_view(),
        name='truck-company-detail'
    ),
    path(
        'bhc/locations/',
        web_views.BHCSitesView.as_view(),
        name='bhc-company-locations'
    ),
    path(
        '<integer:company_id>/ngrs/',
        web_views.CompanyNgrsView.as_view(),
        name='ngr-company'
    ),
    path(
        '<integer:company_id>/ngrs/credentials/',
        web_views.CompanyNGRCredentialsView.as_view(),
        name='ngr_portal_credential-company'
    ),
    path(
        '<integer:company_id>/ngrs/<str:number>/validate/',
        web_views.CompanyNGRNumberValidateView.as_view(),
        name='ngr-company-number-validate'
    ),
    path(
        '<integer:company_id>/ngrs/minimal/',
        web_views.CompanyNgrsMinimalisticView.as_view(),
        name='company-ngrs'
    ),
    path(
        '<integer:company_id>/ngrs/<int:ngr_id>/',
        web_views.CompanyNgrView.as_view(),
        name='ngr-company-detail'
    ),
    path(
        '<integer:company_id>/farm_ngrs/',
        web_views.CompanyFarmNgrsView.as_view(),
        name='ngr-company-farm'
    ),
    path(
        '<integer:company_id>/locations/',
        web_views.CompanyLocationsView.as_view(),
        name='location-company'
    ),
    path(
        '<integer:company_id>/locations/<integer:location_id>/',
        web_views.CompanyLocationView.as_view(),
        name='location-company-detail'
    ),
    path(
        '<integer:company_id>/company_sites/',
        web_views.CompanySitesView.as_view(),
        name='company_site-company'
    ),
    path(
        '<integer:company_id>/company_sites/minimal/',
        web_views.CompanySitesMinimalView.as_view(),
        name='company_site-company-minimal'
    ),
    path(
        '<integer:company_id>/company_sites/<int:company_site_id>/',
        web_views.CompanySiteView.as_view(),
        name='company_site-company-detail'
    ),
    path(
        '<integer:company_id>/company_sites/<int:company_site_id>/loads/',
        web_views.CompanySiteLoadsView.as_view(),
        name='company-site-loads'
    ),
    path(
        '<integer:company_id>/company_sites/<int:company_site_id>/loads/search/',
        web_views.CompanySiteLoadsSearchView.as_view(),
        name='company-site-loads-search'
    ),
    path(
        '<integer:company_id>/company_sites/<int:company_site_id>/loads/csv/',
        web_views.CompanySiteLoadsCSV.as_view(),
        name='company-site-loads-csv'
    ),
    path(
        '<integer:company_id>/register/',
        web_views.CompanyRegisterView.as_view(),
        name='regiser-company',
    ),
    path(
        '<integer:company_id>/unregister/',
        web_views.CompanyUnRegisterView.as_view(),
        name='unregiser-company',
    ),
    path(
        '<integer:company_id>/purge/',
        web_views.CompanyPurgeView.as_view(),
        name='purge-company',
    ),
    path(
        '<integer:company_id>/admins/',
        web_views.CompanyAdminsView.as_view(),
        name='company-admins',
    ),
    path(
        '<integer:company_id>/claim/',
        web_views.ClaimCompanyView.as_view(),
        name='employee-claim_company',
    ),
    path(
        '<integer:company_id>/bhc/locations/',
        web_views.CompanyBHCSitesView.as_view(),
        name='bhc-company-sites'
    ),
    path(
        '<integer:company_id>/transfer-asset/',
        web_views.CompanyTransferAssetView.as_view(),
        name='company-transfer-asset'
    ),
    path(
        '<integer:company_id>/directory-transfer/',
        web_views.CompanyDirectoryTransferView.as_view(),
        name='company-directory-transfer'
    ),
    path(
        '<integer:company_id>/cash_prices/<str:tab>/',
        web_views.CompanyCashPricesView.as_view(),
        name='company-cash_prices'
    ),
    path(
        '<integer:company_id>/contract_bids/<str:tab>/',
        web_views.CompanyContractBidsView.as_view(),
        name='company-contract_bids'
    ),
    path(
        '<integer:company_id>/brokers/',
        web_views.CompanyBrokersView.as_view(),
        name='company-broker'
    ),
    path(
       '<integer:company_id>/acquisition-recipients/',
       web_views.CompanyAcquisitionRecipientsView.as_view(),
       name='company-acquisition-recipients'
    ),
    path(
       '<integer:company_id>/ngrs/tags/<str:tag_name>/',
       web_views.CompanyTaggedNgrView.as_view(),
       name='company-tagged-ngr'
    ),
    path(
       '<integer:company_id>/application-rates/',
       web_views.CompanyApplicationRatesView.as_view(),
       name='company-application-rates'
    ),
    path(
       'application-rates/<integer:application_rate_id>/',
       web_views.CompanyApplicationRateView.as_view(),
       name='company-application-rate'
    ),
    # Mobile Specific Views
    path(
        'mobile/',
        mobile_views.CompaniesMobileView.as_view(),
        name='company-companies_mobiles'
    ),
    path(
        'stocks/mobile/',
        mobile_views.CompanyOwnershipStocks.as_view(),
        name='company-stocks_mobiles'
    ),
    path(
        '<integer:company_id>/employees/new/',
        web_views.CompanyCanCreateEmployeeView.as_view(),
        name='company-employees-new'
    ),
    path(
        '<integer:company_id>/ngrs/new/',
        web_views.CompanyCanCreateNgrView.as_view(),
        name='company-ngrs-new'
    ),
    path(
        '<integer:company_id>/site-management/settings/',
        web_views.CompanySiteManagementSettingsView.as_view(),
        name='site_management_settings-company'
    ),
    path(
        '<integer:company_id>/orders/',
        web_views.CompanyOrdersView.as_view(),
        name='company-orders'
    ),
    path(
        '<integer:company_id>/sso-setting/<str:platform>/',
        web_views.CompanySSOSettingView.as_view(),
        name='company-sso-setting'
    ),
    path(
        '<integer:company_id>/sso/employees/upsert/',
        web_views.CompanySSOEmployeeUpsertView.as_view(),
        name='employee-sso-upsert'
    ),
    path(
        '<integer:company_id>/orders/<str:search_str>/',
        web_views.CompanyOrdersSearchView.as_view(),
        name='company-orders-search'
    ),
    path(
        'abn/<str:abn>/approved-buyers/',
        web_views.CompanyApprovedBuyersByABNView.as_view(),
        name='company-approved_buyers-by-abn'
    ),
    path(
        '<int:company_id>/approved-buyers/',
        web_views.CompanyApprovedBuyersView.as_view(),
        name='company-approved_buyers'
    ),
    path(
        '<integer:company_id>/web/approved-buyers/',
        web_views.CompanyApprovedBuyersListView.as_view(),
        name='company-approved_buyers_list'
    ),
    path(
        'web/csv/',
        web_views.CompaniesCSVView.as_view(),
        name='company-csv'
    ),
    path(
        '<integer:company_id>/merge/',
        web_views.CompanyMergeView.as_view(),
        name='company-merge',
    ),
    path(
        'bulk-warehouse-invoices/create/',
        web_views.CreateBulkInvoiceNonProdProcessView.as_view(),
        name='create-bulk-warehouse-invoice'
    ),
    path(
        '<integer:company_id>/payment-run-levy-epr-preferences/',
        web_views.CompaniesPaymentRunEprLevyPreferencesView.as_view(),
        name='company-payment-run-levy-epr-preferences'
    ),
    path(
        'lookup/<str:identifier>/',
        web_views.CompanyLookupByABNOrNGRView.as_view(),
        name='company-lookup'
    ),
    path(
        '<int:company_id>/employees/names/',
        web_views.CompanyEmployeeNamesView.as_view(),
        name='company-employee-names'
    ),
]
if os.environ.get('ENV', 'dev').lower() != 'production':
    urlpatterns += [
        path(
            'ngrs/validate/all/',
            web_views.ValidateAllCompaniesNGRView.as_view(),
            name='company-ngrs-validate'
        ),
    ]
