# Generated by Django 4.2 on 2024-09-17 07:26

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0215_remove_contract_contract_number_uniq_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            """
            CREATE UNIQUE INDEX contract_unique_identifier_viewer_ids
            ON contracts (
                identifier,
                (array_remove(array_remove(array_remove(array_remove(viewer_company_ids, 1), -1), -2), -3))
            )
            WHERE status != 'void';
            """
        ),
        migrations.RunSQL(
            """
            CREATE UNIQUE INDEX contract_uniq_contract_number_viewer_ids
            ON contracts (
                contract_number,
                (array_remove(array_remove(array_remove(array_remove(viewer_company_ids, 1), -1), -2), -3))
            )
            WHERE status != 'void' and contract_number != '';
            """
        )
    ]
