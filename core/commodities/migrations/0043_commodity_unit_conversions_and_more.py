# Generated by Django 4.1.7 on 2023-05-23 11:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0042_commodity_country_historicalcommodity_country'),
    ]

    operations = [
        migrations.AddField(
            model_name='commodity',
            name='unit_conversions',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalcommodity',
            name='unit_conversions',
            field=models.J<PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='commodity',
            name='unit',
            field=models.CharField(choices=[('MT', 'MT'), ('Bales', 'Bales'), ('m³', 'm³'), ('BU', 'BU'), ('LB', 'LB'), ('CWT', 'CWT')], default='MT', max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalcommodity',
            name='unit',
            field=models.Char<PERSON>ield(choices=[('MT', 'MT'), ('Bales', 'Bales'), ('m³', 'm³'), ('BU', 'BU'), ('LB', 'LB'), ('CWT', 'CWT')], default='MT', max_length=100),
        ),
    ]
