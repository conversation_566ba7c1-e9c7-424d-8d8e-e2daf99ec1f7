# Generated by Django 4.0.3 on 2022-06-01 09:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0083_merge_20220516_0434'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='historicaladdedcompany',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical added company'},
        ),
        migrations.AlterModelOptions(
            name='historicalcompany',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical company'},
        ),
        migrations.AlterModelOptions(
            name='historicalcompanysetup',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical company setup'},
        ),
        migrations.AlterModelOptions(
            name='historicalcompanytype',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical company type'},
        ),
        migrations.AlterModelOptions(
            name='historicalfarmdirectory',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical farm directory'},
        ),
        migrations.AlterModelOptions(
            name='historicalsubcontractor',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical subcontractor'},
        ),
        migrations.AlterField(
            model_name='historicaladdedcompany',
            name='history_date',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='history_date',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='historicalcompanysetup',
            name='history_date',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='historicalcompanytype',
            name='history_date',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='historicalfarmdirectory',
            name='history_date',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='historicalsubcontractor',
            name='history_date',
            field=models.DateTimeField(),
        ),
    ]
