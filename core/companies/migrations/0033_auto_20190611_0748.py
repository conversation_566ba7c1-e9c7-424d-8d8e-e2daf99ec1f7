# Generated by Django 2.1.7 on 2019-06-11 07:48

from django.db import migrations

def enable_site_management_for_growers(apps, schema_editor):
    PlatformFeatures = apps.get_model('companies', 'PlatformFeatures')
    PlatformFeatures.objects.select_related('company').filter(
        company__type_id=1
    ).update(site_management=True)


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0032_auto_20190401_0931'),
    ]

    operations = [
        migrations.RunPython(enable_site_management_for_growers)
    ]
