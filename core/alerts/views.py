from django.db.utils import IntegrityError
from django.http import Http404
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from pydash import get
from core.alerts.models import Alert

from core.alerts.serializers import (
    AlertsListingSerializer, AlertSerializer
)
from core.common.views.web_views import BaseHistoryView


class AlertsView(APIView):
    def post(self, request):
        try:
            data = request.data
            alert = Alert.persist(data)
            if alert.errors:
                return Response(alert.to_dict(), status=status.HTTP_400_BAD_REQUEST)
            return Response(alert.to_dict(), status=status.HTTP_201_CREATED)
        except IntegrityError as ex:
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request):
        alert_name = get(request.query_params, 'alert_name')
        alerts = Alert.objects.filter(company_id=get(request.query_params, 'company_id'))
        serializer = AlertsListingSerializer(alerts, many=True)
        if alert_name:
            alert = alerts.filter(name=alert_name).first()
            serializer = AlertSerializer(alert, context={'request': request}, many=False)
        return Response(serializer.data, status=status.HTTP_200_OK)


class AlertView(APIView):
    def get_object(self):
        instance = Alert.objects.filter(id=self.kwargs['alert_id']).first()
        if not instance:
            raise Http404()
        return instance

    def get(self, request, alert_id):  # pylint: disable=unused-argument
        alert = self.get_object()
        serializer = AlertSerializer(alert, context={'request': request}, many=False)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, alert_id):  # pylint: disable=unused-argument
        alert = self.get_object()
        try:
            alert.update_alert(request.data)
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)
        return Response(status=status.HTTP_200_OK)


class AlertCanConfigureAlertView(APIView):
    def get(self, request, alert_name):
        alert = Alert.get_by_name_and_company(alert_name, get(request.query_params, 'company_id'))
        return Response({'can_configure_alert': not alert}, status=status.HTTP_200_OK)


class AlertHistoryView(BaseHistoryView):
    model = Alert
    lookup_url_kwarg = 'alert_id'
