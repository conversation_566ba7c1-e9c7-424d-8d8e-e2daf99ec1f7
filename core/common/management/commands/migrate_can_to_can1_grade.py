from django.core.management import BaseCommand

from core.cash_board.models import CashPrices


class Command(BaseCommand):
    help = 'Migrate CAN to CAN1 Grade'

    def handle(self, *args, **options):
        from core.contracts.models import Contract, TitleTransfer
        from core.freights.models import FreightOrder, FreightContract
        from core.farms.models import Storage
        from core.vendor_decs.models import CommodityVendorDec
        from core.company_sites.models import FreightSlot
        from core.loads.models import Load

        CAN_GRADE_ID = 40
        CAN1_GRADE_ID = 119

        def process(
                klass, grade_id_field='grade_id',
                manager_name='objects', process_spread=False
        ):
            print(f"****Processing {klass}****")
            filters = {f"{grade_id_field}": CAN_GRADE_ID}
            manager = getattr(klass, manager_name)
            queryset = manager.filter(**filters)
            if process_spread:
                for contract in queryset.filter(spread__isnull=False):
                    spread = contract.spread
                    new_details = []
                    for detail in spread.details:
                        if detail.get('id') == CAN_GRADE_ID:
                            detail['id'] = CAN1_GRADE_ID
                            detail['name'] = 'CAN1'
                        new_details.append(detail)
                    spread.details = new_details
                    spread.save()
            queryset.update(**{grade_id_field: CAN1_GRADE_ID})

        process(klass=Load)
        process(klass=FreightContract, grade_id_field='planned_grade_id')
        process(klass=FreightOrder, grade_id_field='planned_grade_id')
        process(klass=FreightSlot)
        process(klass=TitleTransfer)
        process(klass=Contract, process_spread=True)
        process(klass=Storage, manager_name='all')
        process(klass=CommodityVendorDec)
        process(klass=CashPrices)
