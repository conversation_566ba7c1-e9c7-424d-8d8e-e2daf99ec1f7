# Generated by Django 4.1.13 on 2024-08-08 10:51

import core.contract_bids.models
from django.db import migrations
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('contract_bids', '0002_alter_contractbid_managers'),
    ]

    operations = [
        migrations.AlterModelManagers(
            name='contractbid',
            managers=[
                ('active', core.contract_bids.models.ActiveContractBidManager()),
                ('objects', core.contract_bids.models.DefaultCompanyManager()),
                ('archived', core.contract_bids.models.ArchivedCashPricesManager()),
            ],
        ),
        migrations.AlterField(
            model_name='contractbid',
            name='status',
            field=django_fsm.FSMField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('completed', 'Completed')], db_index=True, default='active', max_length=40),
        ),
        migrations.AlterField(
            model_name='historicalcontractbid',
            name='status',
            field=django_fsm.FSMField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('completed', 'Completed')], db_index=True, default='active', max_length=40),
        ),
    ]
