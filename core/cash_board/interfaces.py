from pydash import get
from .constants import CASH_PRICE_AUDIT, CASH_PRICE_COMMON_AUDIT_MESSAGE
from .signals import cash_price_audit


class CashPriceAudit:
    def __init__(self, action, instance, user=None, **kwargs):
        self.action = action
        self.instance = instance
        self.user = user
        self.kwargs = kwargs

    def form_description(self):
        params = {
            'site': get(self.instance, 'site.name', 'NA'),
            'site_company': get(self.instance, 'site.company.name', 'NA'),
            'commodity': self.instance.commodity.display_name if self.instance.commodity_id else 'NA',
            'grade': get(self.instance, 'grade.name', 'NA'),
            'season': self.instance.season or 'NA',
            'limit': self.instance.limit or 'NA',
            'price': self.instance.price or 'NA',
            'sustainable': 'Non Sustainable' if self.instance.sustainable_commodity in ['N', 'No', 'F', False, '']
            else 'Sustainable',
            'buyer': get(self.instance, 'buyer.company.name', 'NA'),
        }
        if self.action == 'status':
            status = self.kwargs.get('status')
            if status:
                self.instance.status = status
        params.update({'tonnage': self.kwargs.get('tonnage', ''), 'seller': self.kwargs.get('seller', '')})

        message = CASH_PRICE_AUDIT.get(self.action, CASH_PRICE_COMMON_AUDIT_MESSAGE)
        return message.format(**params)

    def send(self):
        cash_price_audit.send(
            sender=self.instance.__class__,
            instance=self.instance,
            created_by=self.user.id if self.user else None,
            description_func=self.form_description,
            acceptance_request_id=self.kwargs.get('acceptance_request_id')
        )
