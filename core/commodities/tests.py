from datetime import datetime, timedelta

import pytz
from django.http.request import QueryDict
from django.test import tag
from django.test.testcases import TestCase
from django.urls import reverse
from mock import patch, Mock, PropertyMock
from freezegun import freeze_time

from core.common.tests import ACTestCase, AuthSetup
from core.timezones.utils import DateTimeUtil
from .models import (
    Commodity,
    Variety,
    get_grade_by_commodity_id_and_spec_params,
    get_entity_item_by_storage_levels_greatest_tonnage, Material, Grade,
)
from .utils import replace_unit_only, convert_in_string, convert_to_unit


@tag('model')
class CommodityTest(ACTestCase):
    def test_levy(self):
        self.assertEqual(Commodity(id=1).levy(), {'value': 0.01020, 'multiplier': 'contract_value'})
        self.assertEqual(Commodity(id=19).levy(), {'value': 4.06000, 'multiplier': 'tonnage'})
        self.assertEqual(Commodity(id=4000).levy(), {'value': 0, 'multiplier': 'contract_value'})

    def test_to_dict(self):
        _wheat = Commodity(name='wheat')
        self.assertEqual(
            _wheat.to_dict()['display_name'],
            'Wheat'
        )

    def test_everything(self):
        commodities = Commodity.everything()
        self.assertEqual(len(commodities), 203)
        self.assertEqual(len(commodities._prefetch_related_lookups), 0)

        commodities = Commodity.everything(inclusions=['variety', 'grade'])
        self.assertEqual(len(commodities), 203)
        self.assertEqual(len(commodities._prefetch_related_lookups), 2)
        self.assertEqual(commodities._prefetch_related_lookups[0], 'variety_set__epr_set')

    def test_delete(self):
        self.assertFalse(Commodity.objects.last().delete())

    def test_wheat_grades_errors(self):
        _wheat = Commodity.objects.get(id=1)
        self.assertEqual(
            _wheat.grades({'zone': 'foo'}),
            {
                'errors': [
                    'For Wheat commodity grades pass valid variety_id, zone in params'
                ]
            }
        )
        self.assertEqual(
            _wheat.grades({'zone': ''}),
            {
                'errors': [
                    'For Wheat commodity grades pass valid variety_id, zone in params'
                ]
            }
        )
        self.assertEqual(
            _wheat.grades({'variety_id': ''}),
            {
                'errors': [
                    'For Wheat commodity grades pass valid variety_id, zone in params'
                ]
            }
        )
        self.assertEqual(
            _wheat.grades({'variety_id': 123}),
            {
                'errors': [
                    'For Wheat commodity grades pass valid variety_id, zone in params'
                ]
            }
        )

    def test_wheat_grades_all(self):
        wheat = Commodity.objects.get(id=1)
        self.assertEqual(wheat.grades().count(), 133)

    def test_barley_grades_all(self):
        barley = Commodity.objects.get(id=2)

        self.assertEqual(barley.grades({}).count(), 173)
        self.assertEqual(barley.grades({'variety_id': ''}).count(), 173)

    def test_barley_grades(self):
        self.assertEqual(
            Variety.BARLEY_F_TYPES,
            ['BAR1', 'BAR2', 'BAR3', 'BFED1', 'BFED2', 'BFED3', 'BFDEC', 'BAR4', 'BARX',
             'Gradings - BARLEY', 'Waste - Barley']
        )
        self.assertEqual(Variety.BARLEY_NON_F_TYPES, ['Malt1', 'Malt2', 'Malt3'])

        variety_baudin_malt = Variety.objects.get(
            name='Baudin (Malt)',
            commodity_id=2
        )

        self.assertEqual(variety_baudin_malt.details["malt_1"], 'BA1')
        self.assertEqual(variety_baudin_malt.details["malt_2"], 'BA2')
        self.assertEqual(variety_baudin_malt.details["malt_3"], 'BA3')

        variety_fathom = Variety.objects.get(
            name='Fathom',
            commodity_id=2
        )

        self.assertEqual(variety_fathom.details['malt_1'], 'BAR1')
        self.assertEqual(variety_fathom.details['malt_2'], 'BAR2')
        self.assertEqual(variety_fathom.details['malt_3'], 'BAR3')

    def test_display_name(self):
        self.assertEqual(
            Commodity(name='wheat').display_name,
            'Wheat'
        )
        self.assertEqual(
            Commodity(name='barley').display_name,
            'Barley'
        )
        self.assertEqual(
            Commodity(name='canola').display_name,
            'Canola'
        )
        self.assertEqual(
            Commodity(name='sorghum').display_name,
            'Sorghum'
        )
        self.assertEqual(
            Commodity(name='lentils').display_name,
            'Lentils'
        )
        self.assertEqual(
            Commodity(name='chickpeas_desi').display_name,
            'Chickpeas (Desi)'
        )
        self.assertEqual(
            Commodity(name='chickpeas_kabuli').display_name,
            'Chickpeas (Kabuli)'
        )
        self.assertEqual(
            Commodity(name='foobar').display_name,
            'foobar'
        )

    def test_display_name_with_class(self):
        self.assertEqual(
            Commodity(name='foobar').display_name_with_class,
            'foobar (Class 3)'
        )
        self.assertEqual(
            Commodity(name='foobar', material_class='foobar').display_name_with_class,
            'foobar (foobar)'
        )

    def test_get_grade_by_commodity_id_and_spec_params(self):
        commodity_id = Commodity.objects.filter(name='wheat').first().id
        specs = {
            'mogr': 12,
            'prgr': 15,
            'twt': 82,
        }
        result_grade = get_grade_by_commodity_id_and_spec_params(commodity_id, specs)
        self.assertIsNotNone(result_grade)
        self.assertTrue(result_grade['name'] in ['APH1', 'APH2', 'SPE', 'SEC1'])

    def test_get_entity_item_by_storage_levels_greatest_tonnage_missing_args(self):
        self.assertIsNone(get_entity_item_by_storage_levels_greatest_tonnage([], 'grade', Variety))
        self.assertIsNone(get_entity_item_by_storage_levels_greatest_tonnage([1, 2, 3], None, None))

    def test_get_interchangeable_commodities(self):
        self.assertEqual(
            Commodity.get_interchangeable_commodities(0),
            []
        )
        # wheat
        self.assertEqual(
            list(Commodity.get_interchangeable_commodities(1).order_by('name').values_list('name', flat=True)),
            ['wheat', 'wheat_seed']
        )
        # wheat_seed
        self.assertEqual(
            list(Commodity.get_interchangeable_commodities(29).order_by('name').values_list('name', flat=True)),
            ['wheat', 'wheat_seed']
        )
        # barley
        self.assertEqual(
            list(Commodity.get_interchangeable_commodities(2).order_by('name').values_list('name', flat=True)),
            ['barley', 'barley_seed']
        )
        # canola
        self.assertEqual(
            sorted(list(Commodity.get_interchangeable_commodities(3).order_by('name').values_list('name', flat=True))),
            sorted(['canola', 'Canola Seed', 'canola_meal', 'Canola Oil'])
        )

    def test_ungraded(self):
        self.assertEqual(Commodity.objects.get(id=1).ungraded.name, 'UNGRADED - WHEAT')

    def test_is_mt_commodity(self):
        self.assertTrue(Commodity.is_mt_commodity(1))  # wheat
        self.assertTrue(Commodity.is_mt_commodity(2))  # barley
        self.assertTrue(Commodity.is_mt_commodity(3))  # canola
        self.assertFalse(Commodity.is_mt_commodity(38))  # chicken_manure
        self.assertFalse(Commodity.is_mt_commodity(39))  # canola_hay
        self.assertFalse(Commodity.is_mt_commodity(44))  # Cotton Lint

    def test_is_meter_cube_commodity(self):
        self.assertFalse(Commodity.is_meter_cube_commodity(1))  # wheat
        self.assertFalse(Commodity.is_meter_cube_commodity(2))  # barley
        self.assertFalse(Commodity.is_meter_cube_commodity(3))  # canola
        self.assertTrue(Commodity.is_meter_cube_commodity(38))  # chicken_manure
        self.assertFalse(Commodity.is_meter_cube_commodity(39))  # canola_hay
        self.assertFalse(Commodity.is_meter_cube_commodity(44))  # Cotton Lint

    def test_is_bales_commodity(self):
        self.assertFalse(Commodity.is_bales_commodity(1))  # wheat
        self.assertFalse(Commodity.is_bales_commodity(2))  # barley
        self.assertFalse(Commodity.is_bales_commodity(3))  # canola
        self.assertFalse(Commodity.is_bales_commodity(38))  # chicken_manure
        self.assertTrue(Commodity.is_bales_commodity(39))  # canola_hay
        self.assertTrue(Commodity.is_bales_commodity(44))  # Cotton Lint

    def test_is_modules_commodity(self):
        self.assertFalse(Commodity.is_modules_commodity(44))  # wheat
        self.assertTrue(Commodity.is_modules_commodity(257))  # wheat

    def test_get_unit_conversions(self):
        commodity = Commodity.objects.get(id=89)  # Organic Barley - BU
        self.assertEqual(
            commodity.get_unit_conversions(100),
            {
                'BU': 100,
                'LB': round(100 * 48, 5),
                'ST': round((100 * 48) / 2000, 5),
                'CWT': round((100 * 48) / 100, 5),
                'MT': round((100 * 48) / 2204.622, 5),
            }
        )

        commodity = Commodity.objects.get(id=106)  # Transitional / Non GMO Canola - CWT
        self.assertEqual(
            commodity.get_unit_conversions(100),
            {
                'CWT': 100,
                'LB': round(100 * 100, 5),
                'BU': round((100 * 100) / 50, 5),
                'ST': round((100 * 100) / 2000, 5),
                'MT': round((100 * 100) / 2204.622, 5),
            }
        )

        commodity = Commodity.objects.get(id=104)  # Transitional / Non GMO Buckwheat - LB
        self.assertEqual(
            commodity.get_unit_conversions(100),
            {
                'LB': 100,
                'BU': round(100 / 48, 5),
                'CWT': round(100 / 100, 5),
                'ST': round(100 / 2000, 5),
                'MT': round(100 / 2204.622, 5),
            }
        )

        commodity = Commodity.objects.get(id=1)  # Wheat - MT
        self.assertEqual(
            commodity.get_unit_conversions(100),
            {
                'MT': 100,
            }
        )

        commodity = Commodity(
            country_id=2,
            unit='CWT',
            unit_conversions=[
                {'from': 'BU', 'to': 'LB', 'conversion': 50.0, 'operator': '*'},
                {'from': 'CWT', 'to': 'LB', 'conversion': 50.0, 'operator': '*'}
            ]
        )  # Dummy - CWT
        self.assertEqual(
            commodity.get_unit_conversions(100),
            {
                'CWT': 100,
                'LB': round(100 * 50, Commodity.UNIT_PRECISION),
                'BU': round((100 * 50) / 50, Commodity.UNIT_PRECISION),
                'ST': round((100 * 50) / 2000, Commodity.UNIT_PRECISION),
                'MT': round((100 * 50) / 2204.622, Commodity.UNIT_PRECISION),
            }
        )

        commodity = Commodity(
            country_id=2,
            unit='CWT',
            unit_conversions=[
                {'from': 'CWT', 'to': 'LB', 'conversion': 50.0, 'operator': '*'}
            ]
        )  # Dummy - CWT
        self.assertEqual(
            commodity.get_unit_conversions(100),
            {
                'CWT': 100,
                'LB': round(100 * 50, Commodity.UNIT_PRECISION),
                'BU': None,
                'ST': round((100 * 50) / 2000, Commodity.UNIT_PRECISION),
                'MT': round((100 * 50) / 2204.622, Commodity.UNIT_PRECISION),
            }
        )

        commodity = Commodity(
            country_id=2,
            unit='CWT',
            unit_conversions=[
                {'from': 'CWT', 'to': 'ST', 'conversion': 10.0, 'operator': '*'}
            ]
        )  # Dummy - CWT
        self.assertEqual(
            commodity.get_unit_conversions(100),
            {
                'CWT': 100,
                'LB': round(100 * 100, Commodity.UNIT_PRECISION),
                'BU': None,
                'ST': round(100 * 10, Commodity.UNIT_PRECISION),
                'MT': round((100 * 100) / 2204.622, Commodity.UNIT_PRECISION),
            }
        )

        commodity = Commodity(
            country_id=2,
            unit='CWT',
            unit_conversions=[
                {'from': 'ST', 'to': 'CWT', 'conversion': 10.0, 'operator': '/'}
            ]
        )  # Dummy - CWT
        self.assertEqual(
            commodity.get_unit_conversions(100),
            {
                'CWT': 100,
                'LB': round(100 * 100, Commodity.UNIT_PRECISION),
                'BU': None,
                'ST': round(100 * 10, Commodity.UNIT_PRECISION),
                'MT': round((100 * 100) / 2204.622, Commodity.UNIT_PRECISION),
            }
        )

        commodity = Commodity(
            country_id=2,
            unit='CWT',
            unit_conversions=[
                {'from': 'LB', 'to': 'BU', 'conversion': 15.0, 'operator': '*'},
                {'from': 'LB', 'to': 'ST', 'conversion': 10.0, 'operator': '/'}
            ]
        )  # Dummy - CWT
        self.assertEqual(
            commodity.get_unit_conversions(100),
            {
                'CWT': 100,
                'LB': round(100 * 100, Commodity.UNIT_PRECISION),
                'BU': round(100 * 100 * 15, Commodity.UNIT_PRECISION),
                'ST': round((100 * 100) / 10, Commodity.UNIT_PRECISION),
                'MT': round((100 * 100) / 2204.622, Commodity.UNIT_PRECISION),
            }
        )

    def test_get_spec_unit(self):
        commodity = Commodity.objects.get(id=89)  # organic barley
        self.assertEqual(commodity.get_spec_unit('TW'), 'LB/BU')
        self.assertEqual(commodity.get_spec_unit('M'), '%')

        commodity = Commodity.objects.get(id=3)  # canola
        self.assertEqual(commodity.get_spec_unit('TWT'), 'kg/hl')
        self.assertEqual(commodity.get_spec_unit('IMPU'), '%')

    def test_get_spec_name(self):
        commodity = Commodity.objects.get(id=89)  # organic barley
        self.assertEqual(commodity.get_spec_name('TW'), 'Test Weight (LB/BU)')
        self.assertEqual(commodity.get_spec_name('M'), 'Moisture (%)')
        self.assertEqual(commodity.get_spec_name('m'), 'Moisture (%)')

    def test_replace_unit_only(self):
        class MockItem:
            pass

        item = MockItem()
        field = "tonnage"
        tonnage = "15.678 tons"
        unit = "kg"

        replace_unit_only(field, item, tonnage, unit)
        self.assertEqual(getattr(item, field), "15.68 kg")

    @patch("core.commodities.utils.set_")
    def test_convert_in_string_success(self, mock_set):
        mock_commodity = Mock()
        mock_commodity.convert_to.return_value = 50.25  # Mocked conversion result
        commodity = mock_commodity
        field = "weight"
        item = {}
        tonnage = "123.45 lbs"
        unit = "MT"

        convert_in_string(commodity, field, item, tonnage, unit)
        mock_commodity.convert_to.assert_called_once_with(123.45, "lbs", "MT", True)
        mock_set.assert_called_once_with(item, field, "50.25 MT")

    def test_convert_to_unit_successful_conversion(self):
        unit = "MT"
        item = {
            'commodityId': 89, 'tonnage': "100", 'tonnageDisplayValue': "100",
            'maxAllowedTonnageAndReason': {'maxAllowedTonnageOnOrder': "100"}, 'storageLevels': [{"tonnage": "100"}],
            'freightContracts': [{"tonnage": "100"}], 'progressTonnage': "100",
            'size': '100',
            'unit': unit
        }  # organic barley, commodity unit BU
        unit_to_convert = "MT"
        result = convert_to_unit(item, unit_to_convert)
        hundred_MT_to_BU = 2.17724  # commodity unit conversion
        self.assertEqual(
            result,
            {
                'commodityId': 89,
                'tonnage': hundred_MT_to_BU,
                'tonnageDisplayValue': '100',
                'maxAllowedTonnageAndReason': {'maxAllowedTonnageOnOrder': hundred_MT_to_BU},
                'storageLevels': [{'tonnage': hundred_MT_to_BU}], 'freightContracts': [{'tonnage': '100'}],
                'progressTonnage': hundred_MT_to_BU,
                'size': '100',
                'unit': 'MT',
                'requestedUnit': 'MT',
            },
        )

@tag('model')
class MaterialTest(ACTestCase):
    def test_entity(self):
        self.assertEqual(Material().entity, 'material')

    def test_display_name(self):
        self.assertEqual(Material(name=None).display_name, None)
        self.assertEqual(Material(name='not-display-name').display_name, 'not-display-name')

    def test_display_name_with_class(self):
        self.assertEqual(
            Material(name='not-display-name', material_class='Class X').display_name_with_class,
            'not-display-name (Class X)'
        )


@tag('model')
class GradeTest(TestCase):
    @patch('core.commodities.models.Grade.save')
    def test_create_by_name_and_commodity(self, save_mock):
        grade = Grade.create_by_name_and_commodity('foo', 1)

        save_mock.assert_called_once()
        self.assertEqual(grade.name, 'foo')
        self.assertEqual(grade.commodity_id, 1)
        self.assertEqual(grade.specs, [])
        self.assertEqual(grade.order, 100)


@tag('model')
class VarietyTest(ACTestCase):
    def test_find_by_name(self):
        variety = Variety.objects.get(name='Koorabup', commodity_id=59)
        self.assertEqual(Variety.find_by_name('Koorabup').id, variety.id)
        self.assertEqual(Variety.find_by_name('koorabup').id, variety.id)
        self.assertEqual(Variety.find_by_name('KoorAbup').id, variety.id)
        self.assertEqual(Variety.find_by_name('Koor abup').id, variety.id)
        self.assertEqual(Variety.find_by_name(' Ko_or-ab up ').id, variety.id)

    @patch('core.commodities.models.Variety.save')
    def test_create_by_name_and_commodity(self, save_mock):
        variety = Variety.create_by_name_and_commodity('foo', 1, {'foo': 'bar'})

        save_mock.assert_called_once()
        self.assertEqual(variety.name, 'foo')
        self.assertEqual(variety.commodity_id, 1)
        self.assertEqual(variety.details, {'foo': 'bar'})


@tag('view')
class CommoditiesViewTest(AuthSetup):
    @patch('core.commodities.views.Commodity')
    @patch('core.commodities.views.CommoditySerializer')
    def test_get_without_includes(self, commodity_serializer_mock, commodity_klass_mock):
        everything_mock = Mock(filter=Mock(return_value='everything'))
        commodity_klass_mock.everything = Mock(return_value=everything_mock)
        commodity_serializer_mock.return_value.data = {'foo': 'bar'}
        _url = reverse(
            'core.commodities:commodity'
        )

        _response = self.client.get(_url)

        self.assertEqual(_response.status_code, 200)
        self.assertEqual(_response.data, {'foo': 'bar'})
        commodity_klass_mock.everything.assert_called_once_with(
            include_unknown=False, inclusions=[],
        )
        commodity_serializer_mock.assert_called_once_with(
            'everything',
            many=True,
            context={'inclusions': []}
        )

    @patch('core.commodities.views.Variety')
    @patch('core.commodities.views.Commodity')
    @patch('core.commodities.views.CommoditySerializer')
    def test_get_with_includes(self, commodity_serializer_mock, commodity_klass_mock, variety_klass_mock):
        variety_klass_mock.mandatory_props = []
        everything_mock = Mock(filter=Mock(return_value='everything'))
        commodity_klass_mock.everything = Mock(return_value=everything_mock)
        commodity_serializer_mock.return_value.data = {'foo': 'bar'}
        _url = reverse(
            'core.commodities:commodity'
        )

        _response = self.client.get(_url + '?includes=foo,bar')

        self.assertEqual(_response.status_code, 200)
        self.assertEqual(_response.data, {'foo': 'bar'})
        commodity_klass_mock.everything.assert_called_once_with(
            include_unknown=False, inclusions=['foo', 'bar'],
        )
        commodity_serializer_mock.assert_called_once_with(
            'everything',
            many=True,
            context={'inclusions': ['foo', 'bar']}
        )
        self.assertEqual(variety_klass_mock.mandatory_props, [])


@tag('view')
class CommodityGradesView(AuthSetup):
    def test_commodity_404(self):
        _url = reverse(
            'core.commodities:commodity-grades',
            kwargs={
                'commodity_id': 786,
            }
        )
        _response = self.client.get(
            _url,
        )
        self.assertEqual(_response.status_code, 404)
        self.assertIsNone(_response.data)

    @patch('core.commodities.views.Commodity')
    def test_get_errors(self, commodity_klass_mock):
        _class_manager_mock = Mock()
        _commodity_obj_mock = Mock()
        _commodity_obj_mock.grades = Mock(return_value={'errors': 'awwww'})
        _class_manager_mock.get = Mock(return_value=_commodity_obj_mock)
        commodity_klass_mock.objects = _class_manager_mock
        commodity_klass_mock.qs2dict = Mock()

        _url = reverse(
            'core.commodities:commodity-grades',
            kwargs={
                'commodity_id': 786,
            }
        )
        _response = self.client.get(
            _url + '?foo=bar',
            kwargs={
                'commodity_id': 786,
            }
        )

        self.assertEqual(_response.status_code, 400)
        self.assertEqual(_response.data, {'errors': 'awwww'})

        _qdict = QueryDict('', mutable=True)
        _qdict.update({'foo': 'bar'})
        _class_manager_mock.get.assert_called_once_with(id=786)
        _commodity_obj_mock.grades.assert_called_once_with(params=_qdict)
        commodity_klass_mock.qs2dict.assert_not_called()

    @patch.object(Commodity, 'grades', new_callable=PropertyMock)
    def test_get_no_variety(self, grades_mock):
        Commodity.create({'name': 'wheat', 'id': 786, 'specs': [{'foo': 'bar'}], 'type': 'grain'})
        grades_mock.side_effect = Variety.DoesNotExist

        _url = reverse(
            'core.commodities:commodity-grades',
            kwargs={
                'commodity_id': 786,
            }
        )
        _response = self.client.get(
            _url + '?foo=bar',
            kwargs={
                'commodity_id': 786,
            }
        )

        self.assertEqual(_response.status_code, 400)
        self.assertEqual(_response.data, {'errors': ['Variety does not exist']})

    @patch('core.commodities.views.Commodity')
    def test_get_success(self, commodity_klass_mock):
        _class_manager_mock = Mock()
        _commodity_obj_mock = Mock()
        _commodity_obj_mock.grades = Mock(return_value='grades')
        _class_manager_mock.get = Mock(return_value=_commodity_obj_mock)
        commodity_klass_mock.objects = _class_manager_mock
        commodity_klass_mock.qs2dict = Mock(return_value={'foo': 'bar'})

        _url = reverse(
            'core.commodities:commodity-grades',
            kwargs={
                'commodity_id': 786,
            }
        )
        _response = self.client.get(
            _url + '?foo=bar',
            kwargs={
                'commodity_id': 786,
            }
        )

        self.assertEqual(_response.status_code, 200)
        self.assertEqual(_response.data, {'foo': 'bar'})

        _qdict = QueryDict('', mutable=True)
        _qdict.update({'foo': 'bar'})
        _class_manager_mock.get.assert_called_once_with(id=786)
        _commodity_obj_mock.grades.assert_called_once_with(params=_qdict)
        commodity_klass_mock.qs2dict.assert_called_once_with(queryset='grades')


@tag('view')
class CommoditiesMobileViewTest(AuthSetup):
    def test_get_ok(self):
        url = reverse('core.commodities:commodity-mobile')

        with self.assertNumQueries(4):
            # 1. Fetch all commodities
            # 2. Fetch all related varieties
            # 3. Fetch all related grades
            # 4. Exclude new commodity grades
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertGreater(len(response.data), 0)

        for commodity in response.data:
            self.assertIsInstance(commodity['grades'], list)
            self.assertIsInstance(commodity['varieties'], list)
            self.assertIsNotNone(commodity['grades'])
            self.assertIsNotNone(commodity['varieties'])

    def test_get_ok_with_epoch(self):
        url = reverse('core.commodities:commodity-mobile') + '?from=0'

        with self.assertNumQueries(7):
            # 1. Fetch latest updated_at for commodities
            # 2. Fetch latest updated_at for grades
            # 3. Fetch latest updated_at for varieties
            # 4. Fetch all related grades
            # 5. Fetch all related varieties
            # 6. Fetch all related grades
            # 7. Exclude new commodity grades
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertGreater(len(response.data), 0)

        for commodity in response.data:
            self.assertIsInstance(commodity['grades'], list)
            self.assertIsInstance(commodity['varieties'], list)
            self.assertIsNotNone(commodity['grades'])
            self.assertIsNotNone(commodity['varieties'])

    def test_get_ok_with_brief(self):
        url = reverse('core.commodities:commodity-mobile') + '?brief=true'

        with self.assertNumQueries(1):
            # 1. Fetch all commodities
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertGreater(len(response.data), 0)

        for commodity in response.data:
            self.assertIsNone(commodity.get('grades'))
            self.assertIsNone(commodity.get('varieties'))

    def test_get_ok_with_brief_and_epoch_of_past(self):
        time_10_minute_ago = datetime(2024, 12,27, 7, 50, 0, tzinfo=pytz.UTC)
        from_val = DateTimeUtil.get_epoch_from_datetime(time_10_minute_ago)
        commodity = Commodity.objects.get(id=1)

        with freeze_time('2024-12-27 08:00:00'):
            commodity.save()

        commodity.refresh_from_db()

        self.assertTrue(commodity.updated_at > time_10_minute_ago)

        url = reverse('core.commodities:commodity-mobile') + f"""?brief=true&from={from_val}"""

        with self.assertNumQueries(1):
            # 1. Fetch all commodities after the epoch time.
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertGreater(len(response.data), 0)

        for commodity in response.data:
            self.assertIsNone(commodity.get('grades'))
            self.assertIsNone(commodity.get('varieties'))

    def test_get_ok_with_brief_and_epoch_of_current_time(self):
        current_time = datetime.now(pytz.UTC)
        from_val = DateTimeUtil.get_epoch_from_datetime(current_time)
        Commodity.objects.filter().update(updated_at=current_time - timedelta(seconds=1))

        url = reverse('core.commodities:commodity-mobile') + f"""?brief=true&from={from_val}"""

        with self.assertNumQueries(1):
            # 1. Fetch latest updated_at for commodities
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertEqual(len(response.data), 0)


@tag('view')
class GradesMobileViewTest(AuthSetup):
    def test_get_ok(self):
        url = reverse('core.commodities:grades-mobile')

        with self.assertNumQueries(1):
            # 1. Fetch all grades
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertGreater(len(response.data), 0)

    def test_get_ok_with_epoch_of_past(self):
        time_1_minute_ago = datetime.now(pytz.UTC) - timedelta(minutes=1)
        from_val = DateTimeUtil.get_epoch_from_datetime(time_1_minute_ago)

        Grade.objects.last().save()

        url = reverse('core.commodities:grades-mobile') + f"""?from={from_val}"""

        with self.assertNumQueries(1):
            # 1. Fetch all updated grades after the epoch
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertGreaterEqual(len(response.data), 1)

    def test_get_ok_with_epoch_of_current_time(self):
        from_val = DateTimeUtil.get_epoch_from_datetime(datetime.now(pytz.UTC) + timedelta(days=3))

        url = reverse('core.commodities:grades-mobile') + f"""?from={from_val}"""

        with self.assertNumQueries(1):
            # 1. Fetch all grades after epoch
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertEqual(len(response.data), 0)

    def test_get_ok_with_commodity_id(self):
        commodity_id = 231
        url = reverse('core.commodities:grades-mobile') + f"""?commodity_id={commodity_id}"""

        with self.assertNumQueries(1):
            # 1. Fetch grades related with commodity_id
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertGreater(len(response.data), 0)

    def test_get_ok_with_commodity_id_with_no_grades(self):
        commodity_id = 23134
        url = reverse('core.commodities:grades-mobile') + f"""?commodity_id={commodity_id}"""

        with self.assertNumQueries(1):
            # 1. Fetch grades related with commodity_id
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertEqual(len(response.data), 0)

    def test_get_ok_with_commodity_id_and_epoch_after_updated_time(self):
        future_time = datetime.now(pytz.UTC) + timedelta(days=365*1000)
        from_val = DateTimeUtil.get_epoch_from_datetime(future_time)

        Grade.objects.filter(commodity_id=1, id=1).update(updated_at=future_time - timedelta(minutes=1))

        url = reverse('core.commodities:grades-mobile') + f"""?commodity_id=1&from={from_val}"""

        with self.assertNumQueries(1):
            # 1. Fetch grades related with commodity_id after epoch
            response = self.client.get(path=url)

        self.assertEqual(len(response.data), 0)

    def test_get_ok_with_commodity_id_and_epoch_before_updated_time(self):
        future_time = datetime.now(pytz.UTC) + timedelta(days=365*1000)
        from_val = DateTimeUtil.get_epoch_from_datetime(future_time)

        Grade.objects.filter(commodity_id=1, id=1).update(updated_at=future_time + timedelta(minutes=1))

        url = reverse('core.commodities:grades-mobile') + f"""?commodity_id=1&from={from_val}"""

        with self.assertNumQueries(1):
            # 1. Fetch grades related with commodity_id after epoch
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertGreaterEqual(len(response.data), 1)


@tag('view')
class VarietiesMobileViewTest(AuthSetup):
    def test_get_ok(self):
        url = reverse('core.commodities:varieties-mobile')

        with self.assertNumQueries(1):
            # 1. Fetch all varieties
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)

    def test_get_ok_with_epoch_of_past(self):
        time_1_minute_ago = datetime.now(pytz.UTC) - timedelta(minutes=1)
        from_val = DateTimeUtil.get_epoch_from_datetime(time_1_minute_ago)

        Variety.objects.last().save()

        url = reverse('core.commodities:varieties-mobile') + f"""?from={from_val}"""

        with self.assertNumQueries(1):
            # 1. Fetch all updated varieties after the epoch
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertGreaterEqual(len(response.data), 1)

    def test_get_ok_with_commodity_id(self):
        commodity_id = 231
        url = reverse('core.commodities:varieties-mobile') + f"""?commodity_id={commodity_id}"""

        with self.assertNumQueries(1):
            # 1. Fetch varieties related with commodity_id
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertGreater(len(response.data), 0)

    def test_get_ok_with_commodity_id_with_no_varieties(self):
        commodity_id = 23134
        url = reverse('core.commodities:varieties-mobile') + f"""?commodity_id={commodity_id}"""

        with self.assertNumQueries(1):
            # 1. Fetch varieties related with commodity_id
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertEqual(len(response.data), 0)

    def test_get_ok_with_commodity_id_and_epoch_of_past(self):
        commodity_id = 231
        time_1_minute_ago =  datetime.now(pytz.UTC) - timedelta(minutes=1)
        from_val = DateTimeUtil.get_epoch_from_datetime(time_1_minute_ago)

        Variety.objects.filter(commodity_id=commodity_id).last().save()

        url = reverse('core.commodities:varieties-mobile') + f"""?commodity_id={commodity_id}&from={from_val}"""

        with self.assertNumQueries(1):
            # 1. Fetch varieties related with commodity_id after epoch
            response = self.client.get(path=url)

        self.assertIsInstance(response.data, list)
        self.assertGreaterEqual(len(response.data), 1)
