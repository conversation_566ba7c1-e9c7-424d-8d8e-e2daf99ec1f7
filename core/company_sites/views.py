import time
from datetime import timed<PERSON><PERSON>, datetime

from django.db.models import Prefetch, Count, Sum, Q
from django.db.models.functions import Round, TruncMonth
from django.http import Http404
from django.utils import timezone
from drf_yasg.utils import swagger_auto_schema
from pydash import compact, get
from rest_framework import status
from rest_framework.generics import ListAPIView, RetrieveAPIView, get_object_or_404
from rest_framework.response import Response
from rest_framework.views import APIView

from core.common.permissions import IsSystemCompany, IsSystemCompanyOrCompanyAdmin
from core.common.swagger import start_param, end_param
from core.common.utils import get_request_country_id
from core.company_sites.constants import SLOT_OLD_VERSION, SLOT_SETTING_OLD_VERSION, MAXIMUM_SLOTS
from core.company_sites.serializers import (SlotHistorySerializer, FreightSlotVerboseSerializer,
                                            FreightSlotTruckConfigSerializer)
from core.company_sites.swagger_parameters import slot_status_param
from core.loads.constants import ALL_MOBILE_DEVICES, SYSTEM_NAME_HEADER
from core.profiles.models import Download
from core.timezones.utils import DateTimeUtil
from .models import FreightSlot, SlotComment, SiteManagementSettings
from .serializers import (FreightSlotSerializer, SlotCommentSerializer, FreightSlotPlannedSerializer,
                          FreightSlotExternalSerializer)
from ..common.exceptions import Http400, Http403
from ..common.views.web_views import AbstractEntityStatsView, BaseHistoryView
from ..countries.models import Country
from ..farms.models import Farm


class FreightSlotAbstractView:
    serializer_class = FreightSlotSerializer

    def get_queryset(self):
        return FreightSlot.objects.filter(id=self.kwargs['slot_id']).select_related('truck__company')

    def get_object(self):
        if instance := self.get_queryset().first():
            return instance
        else:
            raise Http404()


class CompanySiteAbstractView:
    serializer_class = FreightSlotSerializer

    def get_queryset(self):
        return Farm.objects.filter(id=self.kwargs['site_id'])

    def get_object(self):
        if site := self.get_queryset().first():
            return site
        else:
            raise Http404()


class GenericFreightSlotsView(APIView):
    def get(self, request, queryset_func):  # pylint: disable=too-many-locals
        query_params = request.query_params.dict()
        company_id = query_params.get('company_id', None)
        site_id = query_params.get('site_id', None)
        site_company_id = query_params.get('site_company_id', None)
        start = query_params.get('start', None)
        end = query_params.get('end', None)
        rego = query_params.get('rego', None)
        is_external = query_params.get('source') == 'external'
        exclude_statuses = dict(self.request.query_params).get('exclude_statuses', [])
        include_fleet = query_params.get('include_fleet', None) in ['true', 'True', True]
        if not company_id:
            company_id = request.user.company_id
        slots = (queryset_func(company_id, start, end, site_company_id, site_id, rego, include_fleet, exclude_statuses)
        .select_related(
            'freight_provider', 'truck__company', 'commodity', 'grade', 'driver', 'sub_freight_provider',
            'order__freight_delivery__consignee__handler__company__sitemanagementsettings',
            'order__freight_pickup__consignor__handler__company__sitemanagementsettings',
            'order__variety',
            'movement__commodity_contract',
            'site__company__sitemanagementsettings'
        ).prefetch_related(
            Prefetch('slotcomment_set', queryset=SlotComment.objects.filter(archived=False))
        ))

        serializer_class = FreightSlotExternalSerializer if is_external else FreightSlotSerializer
        serializer = serializer_class(slots, context={'request': request}, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)


class SiteFreightSlotsView(GenericFreightSlotsView):
    def get(self, request):  # pylint: disable=arguments-differ
        return super().get(
            request,
            FreightSlot.company_slots,
        )

    # for inload and outload slots booking creating single FM (from mobile)
    @staticmethod
    def put(request):  # pylint: disable=too-many-return-statements
        slots = request.data.copy()
        if not isinstance(slots, list) or len(slots) != 2:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        outload_slot_data = get([slot for slot in slots if get(slot, 'type') == 'outload'], '0')
        inload_slot_data = get([slot for slot in slots if get(slot, 'type') == 'inload'], '0')
        outload_slot_id = get(outload_slot_data, 'id')
        inload_slot_id = get(inload_slot_data, 'id')

        if not outload_slot_id or not inload_slot_id:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        outload_slot = FreightSlot.objects.filter(id=outload_slot_id).first()
        inload_slot = FreightSlot.objects.filter(id=inload_slot_id).first()
        if not outload_slot or not inload_slot:
            return Response(status=status.HTTP_404_NOT_FOUND)

        is_superuser = request.user.is_superuser

        outload_updated_at = outload_slot_data.pop('updated_at', 0)
        outload_settings_updated_at = outload_slot_data.pop('settings_updated_at', 0)
        inload_updated_at = inload_slot_data.pop('updated_at', 0)
        inload_settings_updated_at = inload_slot_data.pop('settings_updated_at', 0)

        if not is_superuser and outload_slot.is_old_version(outload_updated_at):
            return Response(
                {'errors': {'outload': {'version':  SLOT_OLD_VERSION}}}, status=status.HTTP_400_BAD_REQUEST
            )
        if not is_superuser and outload_slot.is_setting_updated_since(outload_settings_updated_at):
            return Response(
                {'errors': {'outload': {'version':  SLOT_SETTING_OLD_VERSION}}}, status=status.HTTP_400_BAD_REQUEST
            )
        if not is_superuser and inload_slot.is_old_version(inload_updated_at):
            return Response(
                {'errors': {'inload': {'version':  SLOT_OLD_VERSION}}}, status=status.HTTP_400_BAD_REQUEST
            )
        if not is_superuser and inload_slot.is_setting_updated_since(inload_settings_updated_at):
            return Response(
                {'errors': {'inload': {'version':  SLOT_SETTING_OLD_VERSION}}}, status=status.HTTP_400_BAD_REQUEST
            )

        success = outload_slot_data.book_with_inload(inload_slot, outload_slot_data, inload_slot_data)
        if success:
            outload_slot.refresh_from_db()
            inload_slot.refresh_from_db()

            return Response(
                FreightSlotSerializer([outload_slot, inload_slot], many=True, context={'request': request}).data
            )

        return Response(status=status.HTTP_400_BAD_REQUEST)


class SiteOpenOrCompanyBookedFreightSlotsView(GenericFreightSlotsView):
    def get(self, request):  # pylint: disable=arguments-differ
        return super().get(
            request,
            FreightSlot.open_or_company_booked_slots,
        )


class CompanySiteFreightSlotsView(CompanySiteAbstractView, APIView):
    serializer_class = FreightSlotExternalSerializer

    def post(self, request, site_id):  # pylint: disable=unused-argument
        site = self.get_object()
        slot_data = request.data.copy() if isinstance(request.data, list) else [request.data.copy()]
        request_origin = 'Mobile' if request.META.get(SYSTEM_NAME_HEADER, '').lower() in ALL_MOBILE_DEVICES else 'Web'
        if len(slot_data) <= MAXIMUM_SLOTS:
            slots = FreightSlot.persist_many(site_id=site.id, data=slot_data, return_slots=True,
                                             request_origin=request_origin)
            persisted = bool(slots)
            slot_ids = [slot.id for slot in slots] if isinstance(slots, (list, set, tuple)) else []

            status_code = status.HTTP_201_CREATED if persisted else status.HTTP_400_BAD_REQUEST
            return Response({'persisted': persisted, 'ids': slot_ids}, status=status_code)
        else:
            return Response(
                {'error': f'You are trying to create more than {MAXIMUM_SLOTS} slots. Please adjust the duration.'},
                status=status.HTTP_400_BAD_REQUEST)

    # used for external APIs
    @swagger_auto_schema(
        responses={200: FreightSlotExternalSerializer(many=True)},
        manual_parameters=[
            start_param, end_param, slot_status_param
        ]
    )
    def get(self, request, site_id):
        site = self.get_object()
        if request.user.is_staff or request.user.company_id == site.company_id:
            query_params = request.query_params.dict()
            now = timezone.now()
            start = query_params.get('start', None) or now - timedelta(days=30)
            end = query_params.get('end', None) or now
            statuses = compact(query_params.get('status', '').split(','))
            queryset = FreightSlot.company_slots(
                site.company_id, start, end, None, site_id=site_id).select_related(
                'freight_provider', 'truck', 'commodity', 'grade', 'driver', 'sub_freight_provider',
                'order', 'movement__commodity_contract').order_by('-updated_at')
            if statuses:
                queryset = queryset.filter(status__in=statuses)
            serializer = FreightSlotExternalSerializer(queryset, context={'request': request}, many=True)

            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, site_id):  # pylint: disable=unused-argument, too-many-locals
        from core.jobs.models import Job
        site = self.get_object()
        data = request.data
        if not isinstance(data, list):
            return Response(status=status.HTTP_400_BAD_REQUEST)

        result = {}
        is_booking = False
        for _data in request.data:
            slot_id = _data.get('id')
            slot = FreightSlot.objects.filter(id=slot_id, site_id=site_id).first()
            if not slot:
                raise Http404()
            is_cancel_request = _data.get(
                'status', None) == 'planned' and slot.status != 'planned' and slot.movement_id
            if is_cancel_request:
                if reasons := slot.movement.cannot_raise_slot_cancel_request_reasons(request.user, slot, _data):
                    result[str(slot_id)] = {'status': status.HTTP_400_BAD_REQUEST, 'errors': reasons}
                    continue
            if _data.get('status', None) == 'booked' and slot.status == 'planned' and not slot.movement_id:
                is_booking = True
            success, instance = FreightSlot.update_from_view(request.user, _data, None, site, True, None,
                                                             is_cancel_request)
            if success:
                _status = status.HTTP_200_OK
            else:
                _status = status.HTTP_400_BAD_REQUEST if instance else status.HTTP_404_NOT_FOUND
            errors = None if success else (get(instance, 'errors') or instance)
            result[str(slot_id)] = {'status': _status, 'errors': errors}
        all_statuses_200 = all(entry['status'] == status.HTTP_200_OK for entry in result.values())
        if is_booking and all_statuses_200:
            slot_ids = [slot['id'] for slot in request.data]
            Job.schedule_job_for_task(
                'send_slot_update_alerts', params={'action': 'Book', 'outload_slot_ids': slot_ids})

        return Response(result)


class CompanySitePlannedFreightSlotsView(ListAPIView):
    serializer_class = FreightSlotPlannedSerializer

    def get_queryset(self):
        site_id = self.kwargs['site_id']
        query_params = self.request.query_params.dict().copy()
        include_slot_ids = query_params.pop('include_slot_ids', None)
        include_slot_id = query_params.pop('include_slot_id', None)
        if include_slot_id:
            include_slot_ids = str(include_slot_id)
        is_recommendation = query_params.pop('isRecommendation', None)
        slots = FreightSlot.get_planned_slots(
            site_id,
            query_params.pop('start', None),
            query_params.pop('end', None),
            query_params.pop('load_type', None),
            query_params.pop('freight_provider_id', None),
            **query_params
        )
        if is_recommendation:
            slots = slots.order_by('start')[:1]
        if include_slot_ids:
            slots |= FreightSlot.objects.filter(id__in=include_slot_ids.split(','))
            slots.order_by('start')

        return slots


class CompanySiteFreightSlotView(APIView):
    @staticmethod
    def put(request, site_id, slot_id):  # pylint: disable=too-many-return-statements, too-many-locals, too-many-branches
        params = request.query_params.dict()
        data = request.data
        site_id = data.get('site_id') or site_id
        slot = FreightSlot.objects.filter(id=slot_id, site_id=site_id).first()
        if not slot:
            raise Http404()
        counter_slot = slot.counter_slot
        counter_slot_id = get(counter_slot, 'id')
        is_cancel_request = data.get('status', None) == 'planned' and slot.status != 'planned' and slot.movement_id
        if is_cancel_request:
            if reasons := slot.movement.cannot_raise_slot_cancel_request_reasons(request.user, slot, data):
                return Response({'reasons': reasons}, status=status.HTTP_400_BAD_REQUEST)
        counter_slot_success = True
        new_slot_id = params.get('new_slot_id')
        new_counter_slot_id = params.get('new_counter_slot_id')
        success = False
        instance = slot
        if new_slot_id or new_counter_slot_id:
            movement = slot.movement
            if not movement:
                raise Http404()
            updated_at = data.get('updated_at')
            settings_updated_at = data.get('settings_updated_at')
            if new_slot_id:
                if result := slot.is_slot_or_site_old_version(request.user, updated_at, settings_updated_at):
                    return Response({'reasons': result[1]}, status=status.HTTP_400_BAD_REQUEST)
                new_slot_data = movement.revert_old_slot_and_get_new_slot_data(slot_id, new_slot_id, data)
                success, instance = FreightSlot.update_from_view(
                    request.user, new_slot_data, new_slot_id, None, False, movement.id
                )
            if new_counter_slot_id and counter_slot:
                counter_slot_data = data.copy()
                counter_slot_data['id'] = new_counter_slot_id
                counter_slot_data['type'] = counter_slot.type
                counter_slot_data['site_id'] = counter_slot.site_id
                counter_slot_data['trailer_slots'] = counter_slot_data.pop('counter_trailer_slots', None) or {}
                counter_slot_updated_at = data.pop('counter_slot_updated_at', None) or (
                    updated_at if not new_slot_id else None)
                counter_slot_settings_updated_at = data.pop('counter_slot_settings_updated_at', None) or (
                    settings_updated_at if not new_slot_id else None)
                if result := counter_slot.is_slot_or_site_old_version(request.user, counter_slot_updated_at,
                                                                      counter_slot_settings_updated_at):
                    return Response({'reasons': result[1]}, status=status.HTTP_400_BAD_REQUEST)
                new_counter_slot_data = movement.revert_old_slot_and_get_new_slot_data(counter_slot_id,
                                                                                       new_counter_slot_id,
                                                                                       counter_slot_data)
                counter_slot_success, counter_slot_instance = FreightSlot.update_from_view(
                    request.user, new_counter_slot_data, new_counter_slot_id, counter_slot.site, False, movement.id
                )
                if not new_slot_id:
                    success = counter_slot_success
                    instance = counter_slot_instance.counter_slot
        else:
            is_booking = (data.get('status', None) == 'booked' and slot.status == 'planned' and
                          not slot.movement_id)
            request_origin = 'Mobile' if request.META.get(SYSTEM_NAME_HEADER, '').lower() in ALL_MOBILE_DEVICES else 'Web' #pylint: disable=line-too-long
            success, instance = FreightSlot.update_from_view(
                request.user, data, slot_id, None, False, None, is_cancel_request,
                is_booking, request_origin)

        if success and counter_slot_success and instance:
            return Response(FreightSlotSerializer(instance, context={'request': request}).data)

        if not instance:
            raise Http404()

        if isinstance(instance, list):
            return Response({'reasons': instance}, status=status.HTTP_400_BAD_REQUEST)

        if isinstance(instance, dict):
            return Response({'errors': instance}, status=status.HTTP_400_BAD_REQUEST)

        return Response(
            {**FreightSlotSerializer(instance, context={'request': request}).data, 'errors': instance.errors},
            status=status.HTTP_400_BAD_REQUEST
        )

    @staticmethod
    def delete(request, site_id, slot_id):
        queryset = FreightSlot.objects.filter(id=slot_id, site_id=site_id)
        if queryset.exists():
            slot = queryset.first()
            if slot and slot.can_hard_delete(request.user):
                queryset.delete()
                slot.publish_delete_to_ably()
                return Response(status=status.HTTP_204_NO_CONTENT)
            else:
                return Response(
                    {'alert': 'Slot needs to be in planned status for delete.'}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)


class CompanySiteFreightSlotSiblingDeleteViewMixin(FreightSlotAbstractView, APIView):
    siblings_property = None

    def get_siblings(self, instance):
        if siblings := get(instance, self.siblings_property):
            return FreightSlot.objects.filter(id__in=siblings, status=instance.status)

    def delete(self, request, site_id, slot_id):  # pylint: disable=unused-argument
        slot = self.get_object()
        siblings = self.get_siblings(slot)
        sibling_ids = list(siblings.values_list('id', flat=True))
        if len(sibling_ids) <= MAXIMUM_SLOTS:
            siblings.delete()
            slot.publish_delete_to_ably(sibling_ids)
            return Response({'ids': sibling_ids}, status=status.HTTP_200_OK)
        else:
            return Response({'error': f'You can only delete {MAXIMUM_SLOTS} slots maximum in one request, '
                                      f'please select less slots and try again.'}, status=status.HTTP_400_BAD_REQUEST)


class CompanySiteFreightSlotForwardDeleteView(CompanySiteFreightSlotSiblingDeleteViewMixin):
    siblings_property = 'forward_siblings'


class CompanySiteFreightSlotAllDeleteView(CompanySiteFreightSlotSiblingDeleteViewMixin):
    siblings_property = 'siblings'

class CompanySiteFreightSlotMixedDeleteView(APIView):
    def delete(self, request, site_id):
        site = get_object_or_404(Farm.objects.filter(id=site_id))
        slot_ids = request.data.get('slot_ids', [])

        if not slot_ids:
            raise Http400()
        if not site.company_id == request.user.company_id:
            raise Http403()

        try:
            deleted_slot_ids = FreightSlot.bulk_delete(site.id, slot_ids)
            return Response({'ids': deleted_slot_ids}, status=status.HTTP_200_OK)
        except Exception as ex:
            raise Http400(f'Error deleting slots: {ex}') from ex


class FreightSlotCommentsView(APIView):
    serializer_class = SlotCommentSerializer
    @swagger_auto_schema(
        responses={200: SlotCommentSerializer(many=True)},
    )
    def get(self, request, site_id, slot_id):  # pylint: disable=unused-argument
        include_archived = request.user.is_staff or Farm.objects.filter(
            id=site_id, company_id=request.user.company_id
        ).exists()
        comments = SlotComment.objects.select_related('created_by__company').filter(
            slot_id=slot_id
        )
        if not include_archived:
            comments = comments.filter(archived=False)
        serializer = SlotCommentSerializer(comments, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, site_id, slot_id): # pylint: disable=unused-argument
        comment = SlotComment.create({**request.data, 'slot_id': slot_id})
        serializer = SlotCommentSerializer(comment)

        if comment.persisted:
            return Response(serializer.data, status=status.HTTP_200_OK)

        return Response(serializer.data, status=status.HTTP_400_BAD_REQUEST)


class FreightSlotCommentView(APIView):
    def put(self, request, comment_id):
        try:
            comment = SlotComment.update(data=request.data, instance_id=comment_id)
        except SlotComment.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        return Response(SlotCommentSerializer(comment).data, status=status.HTTP_200_OK)


class SiteSlotsCSVView(APIView):
    def get(self, request):
        params = dict(request.query_params)
        site_ids = params.get('site_id', None)
        start = params.get('start', None)
        end = params.get('end', None)
        Download.orchestrate(
            module='slots',
            user=request.user,
            task='generate_slots_csv',
            task_params={
                'tz': request.META.get('HTTP_REFERER_TZ', request.user.country.timezone),
                'site_ids': site_ids, 'start': start, 'end': end
            }
        )

        return Response(status.HTTP_200_OK)


class SiteBookingCancelledSlotsCSVView(APIView):
    def get(self, request):
        params = dict(request.query_params)
        site_ids = params.get('site_id', None)
        start = params.get('start', None)
        end = params.get('end', None)
        Download.orchestrate(
            module='slots',
            file_name_prefix='cancelled_slots',
            user=request.user,
            task='generate_booking_cancelled_slots_csv',
            task_params={
                'tz': request.META.get('HTTP_REFERER_TZ', request.user.country.timezone),
                'site_ids': site_ids, 'start': start, 'end': end
            }
        )

        return Response(status.HTTP_200_OK)


class SlotOrderDeliveryReportView(APIView):
    @staticmethod
    def get(request):
        params = request.query_params.dict()
        start = params.get('start', None)
        end = params.get('end', None)
        site_ids = dict(request.query_params).get('site_ids', None)
        seller_ids = dict(request.query_params).get('seller_ids', None)
        partial_date_range = params.get('partial_date_range', None) in ['true', True]
        Download.orchestrate(
            module='slots',
            user=request.user,
            task='generate_orders_delivery_csv',
            task_params={
                'tz': request.META.get('HTTP_REFERER_TZ', request.user.country.timezone),
                'site_ids': site_ids,
                'company_id': request.user.company_id, 'start': start, 'end': end, 'seller_ids': seller_ids,
                'partial_date_range': partial_date_range
            },
            file_name_prefix='order_delivery_report'
        )

        return Response(status.HTTP_200_OK)


class SlotOrdersView(FreightSlotAbstractView, APIView):
    def get(self, request, slot_id):  # pylint: disable=unused-argument
        if slot := self.get_queryset().first():
            return Response(slot.get_order_refs(request.user), status=status.HTTP_200_OK)
        return Response(status=status.HTTP_404_NOT_FOUND)


class CompanySiteFreightSlotHistoryView(FreightSlotAbstractView, APIView):
    @swagger_auto_schema(
        responses={200: SlotHistorySerializer(many=True)},
    )
    def get(self, request, site_id, slot_id):  # pylint: disable=unused-argument
        slot = self.get_object()
        tz = request.query_params.dict().get(
            'timezone', None
        ) or request.META.get('HTTP_REFERER_TZ', None) or slot.timezone or request.user.country.timezone

        return Response(
            slot.history_changeset(tz=tz, user=request.user), status=status.HTTP_200_OK
        )


class CompanySiteFreightSlotsHistoryView(APIView):
    @staticmethod
    def post(request, site_id):  # pylint: disable=too-many-locals
        slot_ids = request.data.get('slot_ids', None)
        start = request.data.get('start', None)
        end = request.data.get('end', None)
        site = Farm.objects.filter(id=site_id).first()

        if not site:
            return Response(status=status.HTTP_404_NOT_FOUND)

        slots = FreightSlot.objects.filter(site_id=site_id, id__in=slot_ids)
        if not slots.exists():
            return Response(status=status.HTTP_200_OK)

        tz = request.data.get(
            'timezone', None
        ) or request.META.get(
            'HTTP_REFERER_TZ', None) or get(site, '_timezone.location') or request.user.country.timezone

        from core.company_sites.models import HistoricalFreightSlot  # pylint: disable=no-name-in-module
        try:
            max_history_date = DateTimeUtil.localize_date(
                HistoricalFreightSlot.objects.filter(id__in=slot_ids).latest('history_date').history_date,
                tz, Country.get_country_format("date")
            )
        except:  # pylint: disable=bare-except
            max_history_date = None

        result = {'max_history_date': max_history_date, 'history': {}}
        for slot in slots:
            audit_history = slot.audit_history(tz=tz, user=request.user, start=start, end=end)
            if changeset := audit_history.changeset():
                for date, history in changeset.items():
                    if date not in result['history']:
                        result['history'][date] = []
                    result['history'][date].append({**history, 'id': slot.id})
        result['history'] = dict(sorted(
            result['history'].items(),
            key=lambda x: datetime.strptime(x[0], Country.get_country_format("datetime_with_seconds")),
            reverse=True
        ))

        return Response(result, status=status.HTTP_200_OK)


class SlotsMovementView(APIView):
    permission_classes = [IsSystemCompany]
    @staticmethod
    def put(request):
        if not request.data:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        data = {int(k): int(v) if v else v for k, v in request.data.items()}

        def exclude(d, keys):
            return {k: v for k, v in d.items() if k not in keys}

        not_found = FreightSlot.ids_not_in(data.keys())
        data = exclude(data, not_found)
        with_fms = FreightSlot.objects.filter(id__in=data.keys(), movement_id__isnull=False)
        fm_already_exists = with_fms.values('id', 'order_id', 'movement_id')
        data = exclude(data, with_fms.values_list('id', flat=True))
        errors = []
        success = []
        for slot_id, order_id in data.items():
            if slot_id:
                slot = FreightSlot.objects.filter(id=slot_id).first()
                if order_id:
                    slot.order_id = order_id
                time.sleep(1)
                slot._FreightSlot__create_movement()
                success.append(slot_id) if slot.movement_id else errors.append(slot_id)

        return Response(
            {
                'success': success,
                'errors': errors,
                'not_found': not_found,
                'fm_already_exists': fm_already_exists,
            },
            status=status.HTTP_200_OK
        )


class SlotMarkBookFromForwardStatusView(APIView):  # TODO: rename if better name strikes - Sny
    permission_classes = (IsSystemCompanyOrCompanyAdmin, )
    @staticmethod
    def put(request):
        if not request.data:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        slot_ids = compact(request.data.get('ids'))
        not_found = []
        errors = []
        success = []
        error_reason = ''
        item = []

        for slot_id in slot_ids:
            slot = FreightSlot.objects.filter(id=slot_id).first()
            if not slot:
                not_found.append(slot_id)
            if get(slot, 'parent_slot_id'):
                slot = FreightSlot.objects.filter(id=slot.parent_slot_id).first()
            if not slot.movement_id or (
                    not slot.movement.is_freight_invoiced() and
                    not slot.movement.is_commodity_contract_invoiced and
                    not slot.movement.is_brokerage_invoiced(None)
            ):
                updated_slot = FreightSlot.update(instance=slot, data={'status': 'booked'})
                item = FreightSlotSerializer(updated_slot, context={'request': request}).data
                errors.append(slot_id) if updated_slot.errors else success.append(slot_id)
            else:
                errors.append(slot_id)
                error_reason = 'Movement has been invoiced. Please void the invoice and then mark the slot booked.'

        return Response(
            {
                'success': success,
                'item': item,
                'errors': errors,
                'not_found': not_found,
                'error_reason': error_reason
            },
            status=status.HTTP_200_OK
        )


class SiteComparisonReportView(APIView):
    @staticmethod
    def get(request):
        params = request.query_params
        company_id = params.get('company_id', None)
        site_ids = dict(params).get('site_ids', None)
        provider_ids = dict(params).get('provider_ids', None)
        if provider_ids and isinstance(provider_ids, list):
            provider_ids = [int(provider_id) for provider_id in provider_ids]
        start = params.get('start', None)
        end = params.get('end', None)

        if not company_id and not site_ids:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        if not start or not end:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        extra_filters = {}

        if provider_ids:
            extra_filters['freight_provider_id__in'] = provider_ids

        Download.orchestrate(
            module='slots',
            file_name_prefix='site_comparison_report',
            user=request.user,
            task='generate_site_comparison_report',
            task_params={
                'tz': request.META.get('HTTP_REFERER_TZ', request.user.country.timezone),
                'start': start,
                'end': end,
                'extra_filters': extra_filters,
                'site_ids': site_ids,
                'company_id': company_id,
            }
        )
        return Response(status.HTTP_200_OK)



class CarrierComparisonReportView(APIView):
    @staticmethod
    def get(request):
        params = request.query_params
        company_id = params.get('company_id', None)
        site_ids = dict(params).get('site_ids', None)
        provider_ids = dict(params).get('provider_ids', None)
        if provider_ids and isinstance(provider_ids, list):
            provider_ids = [int(provider_id) for provider_id in provider_ids]
        start = params.get('start', None)
        end = params.get('end', None)

        if not company_id and not site_ids:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        if not start or not end:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        Download.orchestrate(
            module='slots',
            file_name_prefix='carrier_comparison_report',
            user=request.user,
            task='generate_carrier_comparison_report',
            task_params={
                'tz': request.META.get('HTTP_REFERER_TZ', request.user.country.timezone),
                'start': start,
                'end': end,
                'provider_ids': provider_ids,
                'site_ids': site_ids,
                'company_id': company_id,
            }
        )
        return Response(status.HTTP_200_OK)


class CompanySiteSettingsView(CompanySiteAbstractView, APIView):
    def get_object(self):
        company_site = super().get_object()
        instance = company_site.sitemanagementsettings

        if not instance:
            instance = SiteManagementSettings(company_id=company_site.company_id, minimum_tonnage=0.1)
            instance.save()
            if instance.errors:
                return Response(instance.errors, status=status.HTTP_400_BAD_REQUEST)

        return instance

    def get(self, request, site_id):  # pylint: disable=unused-argument
        site = self.get_object()
        return Response({**site.to_dict(), 'site_id': site.id}, status=status.HTTP_200_OK)


class FreightSlotVerboseView(FreightSlotAbstractView, RetrieveAPIView):
    serializer_class = FreightSlotVerboseSerializer


class FreightSlotTruckConfigView(FreightSlotAbstractView, RetrieveAPIView):
    """For slot truck config"""
    serializer_class = FreightSlotTruckConfigSerializer

    def put(self, request, slot_id):  # pylint: disable=unused-argument
        slot = self.get_object()
        is_mobile = self.request.META.get(SYSTEM_NAME_HEADER, '').lower() in ALL_MOBILE_DEVICES
        category_id = request.data.get('category_id', None)
        steer_1_point_1 = request.data.get('steer_1_point_1', None)
        steer_point_5 = request.data.get('steer_point_5', None)
        permit_number = request.data.get('permit_number', None)
        declared_mass_limit = request.data.get('declared_mass_limit', None)
        accreditation_number = request.data.get('accreditation_number', None)
        load_sharing = request.data.get('load_sharing', is_mobile)
        # load_sharing is absent is not implemented in mobile -> negating the value passed form mobile payload
        if is_mobile:
            load_sharing = not load_sharing
        notice = request.data.get('notice', None)
        notice_number = request.data.get('notice_number', None)
        restricted = request.data.get('restricted', None)
        if (
                (slot.category_id != category_id) or (steer_1_point_1 != slot.steer_1_point_1) or
                (steer_point_5 != slot.steer_point_5) or (permit_number != slot.permit_number) or
                (declared_mass_limit != slot.declared_mass_limit) or
                (accreditation_number != slot.accreditation_number) or (load_sharing != slot.load_sharing) or
                (notice != slot.notice) or (notice_number != slot.notice_number) or (restricted != slot.restricted)
        ):
            slot._category_id = category_id
            slot._steer_point_5 = steer_point_5
            slot._steer_1_point_1 = steer_1_point_1
            slot._permit_number = permit_number
            slot._declared_mass_limit = declared_mass_limit
            slot._accreditation_number = accreditation_number
            slot._load_sharing = load_sharing
            slot._notice = notice
            slot._notice_number = notice_number
            slot._restricted = restricted
            slot.upsert_unsaved_truck_config()
        return Response(FreightSlotTruckConfigSerializer(slot, context={'request': request}).data)


class FreightSlotView(FreightSlotAbstractView, RetrieveAPIView):
    def get_serializer_class(self):
        if self.request.query_params.get('verbose', '').lower() in ['true', True]:
            return FreightSlotVerboseSerializer
        return FreightSlotSerializer


class FreightSlotStatsView(AbstractEntityStatsView):
    def get_queryset(self):
        queryset = FreightSlot.objects.exclude(status__in=['cancelled', 'rejected', 'restricted'])
        company_id = self.request.query_params.get('company_id', None)
        if company_id:
            queryset = queryset.filter(site__company_id=company_id)
        else:
            from core.companies.models import Company
            exclude_company_ids = list(Company.get_test_company_ids())
            queryset = queryset.exclude(
                site__company_id__in=exclude_company_ids)
            queryset = queryset.filter(site__company__country_id=get_request_country_id())

        return self.filter_queryset(queryset)

    def get_aggregate_criteria(self):
        return {
            'count': Count('id'),
            'tonnage': Round(Sum(self.tonnage_field)),
            'completed': Count('id', filter=Q(status='completed'))
        }

    def get_monthly_distribution(self, queryset):
        return queryset.annotate(
            month=TruncMonth(self.created_date_field)
        ).values('month').annotate(
            count=Count('id')
        ).annotate(
            completed=Count('id', filter=Q(status='completed'))
        ).order_by('month').values('month', 'count', 'completed') if queryset is not None else None


class FreightSlotCanCancelView(APIView):
    def get(self, _, slot_id):
        try:
            freight_slot = FreightSlot.objects.get(id=slot_id)
        except FreightSlot.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        reasons = freight_slot.cannot_cancel_reasons()
        return Response(
            {
                'result': len(reasons) == 0,
                'reasons': reasons,
            },
            status=status.HTTP_200_OK,
        )


class SlotRawHistoryView(BaseHistoryView):
    model = FreightSlot
    lookup_url_kwarg = 'slot_id'
