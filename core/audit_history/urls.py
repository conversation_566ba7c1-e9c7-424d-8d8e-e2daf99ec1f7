from django.urls import path

from . import views

app_name = 'core.audit_history'
urlpatterns = [
    path('contracts/<int:contract_id>/', views.ContractAuditHistoryView.as_view(), name='audit_history-contract'),
    path('orders/<int:order_id>/', views.OrderAuditHistoryView.as_view(), name='audit_history_order'),
    path('movements/<int:movement_id>/', views.MovementAuditHistoryView.as_view(), name='audit_history_movement'),
    path('invoices/<int:invoice_id>/', views.InvoiceAuditHistoryView.as_view(), name='audit_history_invoice'),
    path('vendor_decs/<int:vendor_dec_id>/',
         views.VendorDecAuditHistoryView.as_view(),
         name='audit_history_vendor_dec'),
    path('cash_prices/<int:cash_price_id>/',
         views.CashPriceAuditHistoryView.as_view(),
         name='audit_history_cash_price'),
    path('title-transfers/<int:title_transfer_id>/',
         views.TitleTransferAuditHistoryView.as_view(),
         name='audit_history_title_transfer'),
]
