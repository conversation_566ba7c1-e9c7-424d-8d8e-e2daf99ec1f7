# Generated by Django 4.1.13 on 2024-02-20 07:50

import core.common.models
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0198_historicaltitletransfer_viewer_company_ids_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ThroughWarehouseAllocation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tonnage', core.common.models.RoundedFloatField()),
            ],
        ),
        migrations.RemoveField(
            model_name='contract',
            name='contract_fulfillments',
        ),
        migrations.DeleteModel(
            name='ContractFulfillment',
        ),
        migrations.AddField(
            model_name='throughwarehouseallocation',
            name='purchase',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='throughwarehousesalecontract_set', to='contracts.contract'),
        ),
        migrations.AddField(
            model_name='throughwarehouseallocation',
            name='sale',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='throughwarehousepurchasecontract_set', to='contracts.contract'),
        ),
        migrations.AddField(
            model_name='contract',
            name='through_warehouse_allocations',
            field=models.ManyToManyField(related_name='through_warehouse_contracts', through='contracts.ThroughWarehouseAllocation', to='contracts.contract'),
        ),
        migrations.AlterUniqueTogether(
            name='throughwarehouseallocation',
            unique_together={('purchase', 'sale')},
        ),
    ]
