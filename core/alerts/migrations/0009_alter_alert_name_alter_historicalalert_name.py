# Generated by Django 4.1.7 on 2023-04-14 03:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0008_alter_alert_name_alter_historicalalert_name_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='alert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit'), ('inload_done', 'Inload Done'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Information'), ('delivery_information', 'Delivery Information'), ('outload_report', 'Outload Report')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalalert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit'), ('inload_done', 'Inload Done'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Information'), ('delivery_information', 'Delivery Information'), ('outload_report', 'Outload Report')], max_length=100),
        ),
    ]
