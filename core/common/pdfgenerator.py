#!/user/bin/python
from django.template.loader import render_to_string

from core.services.internal.errbit import ERRBIT_LOGGER
# Importing app related pdf interfaces
from core.contracts.interfaces import ContractPDF
from core.freights.interfaces import FreightOrderPDF, FreightContractPDF
from core.invoices.interfaces import InvoicePD<PERSON>
from core.services.external.aws import S3
from core.services.internal.pdf import HTMLToPDF


class PDFGenerator:
    """Class helps to generate the PDF based on the given input.
    """
    entity_mapper = {
        'contract': ContractPDF,
        'freightorder': FreightOrderPDF,
        'freightcontract': FreightContractPDF,
        'invoice': InvoicePDF
    }

    def __init__(self, entity, user=None, is_amend=False, upload=False, party=None):
        self.pdf = None
        self.entity = entity
        self.user = user

        # Assuming all interfaces mapped properly.
        self.interface = self.entity_mapper[self.entity.entity](entity, user, is_amend, upload, party)

    def get_pdf_name(self):
        """func helps to get the pdf name"""
        return self.interface.get_pdf_name()

    def generate(self):
        """Func helps to generate the PDF file"""
        try:
            html_for_pdf = render_to_string(
                self.interface.get_template(),
                self.interface.get_template_args()
            )
            self.pdf = HTMLToPDF.from_string(html_for_pdf, None)

            if self.interface.uploadable():
                self.upload()

        except Exception as exception:  # pylint: disable=broad-except
            ERRBIT_LOGGER.log(exception)
        return self.pdf

    def upload(self):
        """Func helps to upload the pdf at AWS (S3)"""
        try:
            S3.upload(self.interface.get_path(), self.pdf)
            return True
        except Exception as exception:  # pylint: disable=broad-except
            ERRBIT_LOGGER.log(exception)
        return False
