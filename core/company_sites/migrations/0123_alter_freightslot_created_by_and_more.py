# Generated by Django 4.0.8 on 2023-02-24 06:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0116_alter_addedcompany_created_by_and_more'),
        ('freights', '0126_alter_freightcontract_freight_delivery_and_more'),
        ('trucks', '0032_alter_managedtruck_created_by_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('company_sites', '0122_freightslot_excluded_grade_ids_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='freightslot',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='driver',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='freight_provider',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='contracted_slot_set', to='companies.company'),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='movement',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='freights.freightcontract'),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='order',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='freights.freightorder'),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='sub_freight_provider',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subcontracted_slot_set', to='companies.company'),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='truck',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='trucks.truck'),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='updated_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='sitemanagementsettings',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='sitemanagementsettings',
            name='updated_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='slotcomment',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='slotcomment',
            name='updated_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL),
        ),
    ]
