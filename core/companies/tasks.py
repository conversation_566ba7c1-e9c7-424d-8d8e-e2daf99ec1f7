import csv
import datetime
import io
import json
import logging
import time

import pytz
from celery.schedules import crontab
from django.conf import settings
from django.core.mail import EmailMessage
from django.core.serializers.json import DjangoJ<PERSON>NEncoder
from django.db.models import Q, OuterRef
from django.template.loader import render_to_string
from django.utils import timezone
from pydash import get
from rest_framework.authtoken.models import Token

from core.alerts.constants import (EMAIL_CHANNEL, SPEC_AVG_REPORT_ALERT_CODE, OUTLOAD_REPORT_ALERT_CODE,
                                   ACQUISITION_REPORT_ALERT_CODE, STOCK_AUTO_UPDATE_ALERT_CODE)
from core.alerts.models import Alert
from core.celeryconf import app
from core.commodities.models import Variety, Grade
from core.common.constants import FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME, SYSTEM_COMPANY_IDS, GROWER_TYPE_ID, \
    SUPPORT_EMAIL
from core.common.utils import is_production, save_download_locally
from core.companies.constants import (ADMIN_COMMODITY_CSV_HEADERS, COMMODITY, EMPLOYEES, NGR, SITES,
                                      STORAGES, get_csv_headers, ADMIN_GRADE_CSV_HEADERS, ADMIN_VARIETY_CSV_HEADERS,
                                      ADMIN_SITES_CSV_HEADERS,
                                      ADMIN_NGR_CSV_HEADERS, ADMIN_STORAGE_CSV_HEADERS, ADMIN_EMPLOYEE_CSV_HEADERS,
                                      WAREHOUSE_INVOICE_MONTHLY_FREQUENCY, WAREHOUSE_INVOICE_WEEKLY_FREQUENCY)
from core.companies.models import Company
from core.jobs.decorators import update_job
from core.services.external.ably import ABLY
from core.services.external.aws import S3
from core.services.internal.errbit import ERRBIT_LOGGER
from core.timezones.utils import DateTimeUtil

logger = logging.getLogger(__name__)


@app.task(ignore_result=True)
def expire_tokens():
    from core.farms.models import Farm

    now = timezone.now()
    farm_subquery =  Farm.objects.filter(
        company_id=OuterRef('id'),
        externally_sync_source__isnull=False
    ).values('company_id')

    companies = Company.objects.filter(
        _expire_token_in_days__isnull=False
    ).exclude(
        Q(sso_setting__is_active=True) |
        Q(id__in=farm_subquery)
    )

    for company in companies:
        days_back = (now - datetime.timedelta(days=company._expire_token_in_days)).date()
        Token.objects.filter(user__company=company, created__date__lt=days_back).delete()


def date_time_filter_calculation(farm):
    utc_time_zone = pytz.utc
    user_time_zone = DateTimeUtil.to_pytz_timezone(farm.timezone.get('location'))
    previous_date = timezone.now().astimezone(user_time_zone) - datetime.timedelta(days=1)
    range_date_start = previous_date.replace(
        hour=0, minute=0, second=0, microsecond=0
        ).astimezone(
            utc_time_zone
        )

    range_date_stop = previous_date.replace(
        hour=23, minute=59, second=59, microsecond=59
        ).astimezone(
            utc_time_zone
        )

    return Q(**{'date_time__gte': range_date_start, 'date_time__lte': range_date_stop})

@app.task(ignore_result=True)
def daily_email_acquisition_alert():
    for alert in Alert.get_by_name(ACQUISITION_REPORT_ALERT_CODE).filter(
            is_active=True, channel=EMAIL_CHANNEL
    ):
        try:
            alert.process()
            time.sleep(2)
        except Exception as ex:  # pylint: disable=broad-except
            ERRBIT_LOGGER.raise_errbit(f"daily_email_acquisition_export failed for Company ABN: {alert.company.abn}")
            ERRBIT_LOGGER.log(ex)

@app.task(ignore_result=True)
def daily_report_spec_avg():
    for alert in Alert.get_by_name(SPEC_AVG_REPORT_ALERT_CODE).filter(
            is_active=True, channel=EMAIL_CHANNEL
    ):
        try:
            alert.process()
            time.sleep(2)
        except Exception as ex:  # pylint: disable=broad-except
            ERRBIT_LOGGER.log(ex)


@app.task(ignore_result=True)
def daily_outload_report():
    for alert in Alert.get_by_name(OUTLOAD_REPORT_ALERT_CODE).filter(
            is_active=True, channel=EMAIL_CHANNEL
    ):
        try:
            alert.process()
            time.sleep(2)
        except Exception as ex:  # pylint: disable=broad-except
            ERRBIT_LOGGER.log(ex)


@app.task(ignore_result=True)
def create_warehouse_invoices(current_month=False, scheduled=True):
    now = timezone.now().date()
    if now.day == 1 or not scheduled:  # For companies with monthly warehouse invoice setup
        last_month = now - datetime.timedelta(days=now.day + 1)
        target_date = now if current_month else last_month
        year = target_date.year
        companies = Company.objects.filter(
            Q(bulk_invoicing_from__year__lt=year) |
            Q(bulk_invoicing_from__month__lte=target_date.month, bulk_invoicing_from__year=year))
        companies = companies.filter(warehouse_invoice_frequency=WAREHOUSE_INVOICE_MONTHLY_FREQUENCY)
        start_date = DateTimeUtil.get_first_date_of_month(target_date)
        end_date = DateTimeUtil.get_last_date_of_month(target_date)
        start_date = datetime.datetime(start_date.year, start_date.month, start_date.day)
        end_date = datetime.datetime(end_date.year, end_date.month, end_date.day)
        for company in companies:
            company.bulk_invoice_for_tenure(start_date, end_date)

    #For companies with weekly warehouse invoice setup
    start_date = now - datetime.timedelta(days=7)
    end_date = now - datetime.timedelta(days=1)
    week_day = now.weekday()
    week_day = (week_day + 1) % 7 or 7
    companies = Company.objects.filter(
        Q(bulk_invoicing_from__year__lt=start_date.year) |
        Q(bulk_invoicing_from__month__lte=start_date.month, bulk_invoicing_from__year=start_date.year))
    companies = companies.filter(
        warehouse_invoice_frequency=WAREHOUSE_INVOICE_WEEKLY_FREQUENCY,
        warehouse_invoice_start_of_week=week_day
    )
    start_date = datetime.datetime(start_date.year, start_date.month, start_date.day)
    end_date = datetime.datetime(end_date.year, end_date.month, end_date.day)
    for company in companies:
        company.bulk_invoice_for_tenure(start_date, end_date)


def _update_not_invoiced_warehouse_invoices(
        company_id, start_date, end_date,
        commodity_ids=None, grade_ids=None, seasons=None, for_company_ids=None, for_group_ids=None
):
    company = Company.objects.filter(id=company_id).first()
    loads = company.get_third_party_loads()
    if commodity_ids:
        loads = loads.filter(commodity_id__in=commodity_ids)
    if grade_ids:
        loads = loads.filter(grade_id__in=commodity_ids)
    if seasons:
        loads = loads.filter(season__in=seasons)
    payer_company_ids = []
    if for_company_ids:
        payer_company_ids = [*for_company_ids]
    if for_group_ids:
        payer_company_ids = [*payer_company_ids,
                             *company.owned_groups.filter(id__in=for_group_ids).values_list('companies', flat=True)]
    if payer_company_ids:
        loads = loads.filter(ngr__owner_company_ids__overlap=payer_company_ids)
    ngr_ids = set(loads.values_list('ngr_id', flat=True))

    if company and company.warehouse_invoice_frequency == WAREHOUSE_INVOICE_MONTHLY_FREQUENCY and ngr_ids:
        target_dates = DateTimeUtil.get_months_in_date_range(start_date=start_date, end_date=end_date)
        for target_date in target_dates:
            start_date = DateTimeUtil.get_first_date_of_month(target_date)
            end_date = DateTimeUtil.get_last_date_of_month(target_date)
            start_date = datetime.datetime(start_date.year, start_date.month, start_date.day)
            end_date = datetime.datetime(end_date.year, end_date.month, end_date.day)
            company.bulk_invoice_for_tenure(start_date, end_date, ngr_ids)


@app.task(ignore_result=True)
@update_job
def update_not_invoiced_warehouse_invoices(params_list):
    for params in params_list:
        _update_not_invoiced_warehouse_invoices(**params)


@app.task(ignore_result=True)
@update_job
def update_shrinkage_with_updated_business_type(company_id, companies_with_affecting_stocks):
    from core.stocks.models import Stock
    if company := Company.objects.filter(id=company_id).first():
        company.update_all_ngr_relations(update_related_models=False)
        updated_stocks = Stock.objects.filter(
            Stock.company_all_stocks_criteria(company_id),
            load__shrinkage__isnull=False, load__shrinkage__gt=0, storage__is_active=True
        )
        unique_stocks = Stock.objects.filter(id__in=updated_stocks.values_list('id', flat=True))
        updated_stocks_site_company_ids = list(set(unique_stocks.values_list('farm__company_id', flat=True)))
        companies_with_affecting_stocks.extend(updated_stocks_site_company_ids)
        business_change_text = "Non Grower to Grower" if company.type_id is GROWER_TYPE_ID else "Grower to Non Grower"
        for alert in Alert.get_by_name(STOCK_AUTO_UPDATE_ALERT_CODE).filter(
                is_active=True, company_id__in=set(companies_with_affecting_stocks)):
            alert.process(stock_owner_company_name=company.name, business_change_text=business_change_text)


@app.task(ignore_result=True)
def update_company_ngr_from_portal():  # pylint: disable=too-many-locals,too-many-statements,too-many-branches
    from core.jobs.models import Job
    from core.ngrs.models import Ngr, NgrPortalCredential
    from core.services.external.ngr import NGRLookUp
    from core.services.internal.ngr import NGRService
    from core.ngrs.constants import NGR_REGISTERED_STATUS

    job = Job.create({'status': 'started', 'type': 'update_company_ngr_from_portal'})
    try:
        all_credentials = NgrPortalCredential.objects.select_related('company')
        for credential in all_credentials:
            logger.info("Fetching NGR data for company: %s", credential.company.abn)
            credential.set_raw_password()
            ngrs_data = NGRLookUp.fetch_all(credential)
            if not ngrs_data:
                continue
            credential_company = credential.company
            country = credential_company.country
            root_user = country.root_user
            auto_subscription_toggle = credential_company.auto_subscribe_ngr
            for ngr in ngrs_data:
                ngr_number = get(ngr, 'grn')
                logger.info(ngr)
                ngr_status = get(ngr, 'grn_status', None)
                if ngr_status != NGR_REGISTERED_STATUS:
                    Ngr.log_ngr_error_to_slack(credential_company, ngr_number, "NGR is not registered",)
                    continue
                payees = NGRLookUp.get_payees_from_record(ngr)
                single_payee_kwargs = {
                    'latitude': get(ngr, 'gps_latitude') or None,
                    'longitude': get(ngr, 'gps_longitude') or None
                }
                try:
                    NGRService.create_companies_and_ngr_from_payees(
                        credential_company, ngr_number, payees, root_user, True, False,
                        primary_payee_id=get(ngr, 'primary_payee_id'), **single_payee_kwargs
                    )
                except Exception as ex:  # pylint: disable=broad-except
                    logger.info("Error while creating companies and ngr from payees: %s", ex)
                    ERRBIT_LOGGER.log(ex)
                if auto_subscription_toggle:
                    NGRLookUp.subscribe_ngr_declarations(
                        credential_company.id, ngr_number, NGRLookUp.SUSTAINABLE_DECLARATION_OPTION)

        job.status = 'finished'
    except Exception as ex:  # pylint: disable=broad-except
        ERRBIT_LOGGER.log(ex)
        job.status = 'failed'
    finally:
        job.save()


@app.task(ignore_result=True)
def process_ngr_declarations():
    from core.toggles.models import Toggle
    if Toggle.get('PROCESS_NGR_DECLARATION_TOGGLE'):
        from core.jobs.models import Job
        if Job.objects.filter(
                status='started',
                type='process_ngr_declarations',
                created_at__gte=timezone.now() - datetime.timedelta(minutes=59)
        ).exists():
            return
        from core.ngrs.models import NgrPortalCredential
        from core.services.external.ngr import NGRLookUp

        job = Job.create({'type': 'process_ngr_declarations', 'status': 'started'})
        try:
            for credential in NgrPortalCredential.objects.filter():
                NGRLookUp.process_ngr_declarations(credential.company_id)
            job.status = 'finished'
        except Exception as ex:  # pylint: disable=broad-except
            ERRBIT_LOGGER.log(ex)
            job.status = 'failed'
        finally:
            job.save()

@app.task(ignore_result=True)
@update_job
def trigger_update_company_ngr_from_portal():
    update_company_ngr_from_portal()


@app.on_after_finalize.connect
def setup_periodic_tasks(**kwargs):  # pylint: disable=unused-argument
    # it was logging out mobile users who were creating loads in offline mode for more than 24 hours (default value)
    app.add_periodic_task(
        crontab(hour=18, minute=0), expire_tokens.s(), name='expire_tokens'
    )
    app.add_periodic_task(
        crontab(hour=19, minute=0), daily_report_spec_avg.s(), name='daily_report_spec_avg'
    )
    app.add_periodic_task(
        crontab(hour=19, minute=0), daily_outload_report.s(), name='daily_outload_report'
    )
    app.add_periodic_task(
        crontab(minute=1, hour=0), create_warehouse_invoices.s(), name='create_warehouse_invoices'
    )
    # every one hour
    app.add_periodic_task(
        crontab(minute=0), process_ngr_declarations.s(), name='process_ngr_declarations'
    )
    # at 1700 UTC or 2230 IST
    app.add_periodic_task(
        crontab(hour=17, minute=0), update_company_ngr_from_portal.s(), name='update_company_ngr_from_portal'
    )
    # 25/10/2022 16:01 UTC => 26/10/2022 00:01 AWST
    app.add_periodic_task(
        crontab(hour=16, minute=1), daily_email_acquisition_alert.s(), name='daily_email_acquisition_alert'
    )
    # every 12th of month at 00:01 UTC
    app.add_periodic_task(
        crontab(day_of_month=12, hour=0, minute=1),
        generate_subscribers_csv_internal.s(), name='generate_subscribers_csv_internal'
    )


@app.task(ignore_result=True)
@update_job
def generate_companies_csv(download_id):
    from core.profiles.models import Download
    download_qs = Download.objects.filter(
        id=download_id).select_related('employee')
    download = download_qs.first()
    user = download.employee
    country = user.company.country
    try:
        csv_headers = get_csv_headers(country)
        if user.is_staff:
            companies = Company.objects.filter(country_id=country.id).select_related('created_by__company')
            csv_headers += ['Created By', 'Created By Company', 'Subscriber', 'Mobile Subscriber', 'ID']
        else:
            companies = Company.directory_companies(user.company_id, country_code=country.code)
        companies = companies.select_related('address', 'type', 'country').order_by('business_name')
        buff = io.StringIO()
        writer = csv.writer(buff, dialect='excel', delimiter=',')
        writer.writerow(csv_headers)
        for company in companies:
            writer.writerow(company.to_csv_row(user, csv_headers))
        buff2 = io.BytesIO(buff.getvalue().encode())
        save_download_locally(download, buff2)
        S3.upload(download.file_path, buff2)
        download_qs.update(status='ready')
    except Exception as ex:  # pylint: disable=broad-except
        ERRBIT_LOGGER.log(ex)
        download_qs.update(status='failed', failure_reason=str(ex))

    ABLY.publish(
        'companies-csv-ready',
        str(user.id),
        json.dumps(
            download.to_dict(),
            sort_keys=True,
            indent=1,
            cls=DjangoJSONEncoder,
        )
    )


@app.task(ignore_result=True)
@update_job
def create_warehouse_invoice_for(company_id, tenure, ngr_ids=None, commodity_ids=None):
    if not company_id or not tenure:
        return

    company = Company.objects.filter(id=company_id).first()
    filtered_ngr_ids = []
    from core.ngrs.models import Ngr
    for ngr in Ngr.objects.filter(id__in=ngr_ids):
        if company_id not in [*SYSTEM_COMPANY_IDS, *ngr.primary_owner_company_ids]:
            filtered_ngr_ids.append(ngr.id)
    if company and filtered_ngr_ids:
        from core.invoices.models import Invoice
        start_date, end_date = Invoice.get_dates_from_tenure(tenure)
        company.bulk_invoice_for_tenure(start_date, end_date, filtered_ngr_ids, commodity_ids)


@app.task(ignore_result=True)
@update_job
def sync_ngr_from_portal(company_id, ngr_number):
    from core.services.external.ngr import NGRLookUp
    from core.services.internal.ngr import NGRService
    if ngr_number and not ngr_number.isdigit():
        return {'error': f'Invalid NGR number {ngr_number} provided.'}
    ngr_record = NGRLookUp.fetch(company_id, ngr_number, True)
    if get(ngr_record, 'error'):
        return ngr_record
    payees = NGRLookUp.get_payees_from_record(ngr_record)

    credentials_company = Company.objects.get(pk=company_id)
    country = credentials_company.country
    root_user = country.root_user
    single_payee_kwargs = {
        'latitude': get(ngr_record, 'gps_latitude') or None,
        'longitude': get(ngr_record, 'gps_longitude') or None
    }

    NGRService.create_companies_and_ngr_from_payees(
        credentials_company, ngr_number, payees, root_user, True, False,
        primary_payee_id=get(ngr_record, 'primary_payee_id'), **single_payee_kwargs
    )


@app.task(ignore_result=True)
@update_job
def generate_stocks_uploads_csv(download_id, query_params=None):  # pylint: disable=too-many-locals,too-many-statements,too-many-branches
    from core.profiles.models import Download
    from core.ngrs.models import Ngr
    from core.farms.models import Farm
    from core.profiles.models import Employee
    from core.farms.models import Storage
    from core.commodities.models import Commodity

    download_qs = Download.objects.filter(id=download_id).select_related('employee')
    download = download_qs.first()
    user = download.employee
    try:
        data_type = get(query_params, 'dataType')
        def _sanitize_param(param):
            if isinstance(param, list):
                return param[0]
            return param

        data_type = _sanitize_param(data_type)
        country_id = get(query_params, 'country_id')
        commodity_data = []
        grade_data = []
        variety_data = []
        buff = io.StringIO()
        writer = csv.writer(buff, dialect='excel', delimiter=',')
        if data_type == COMMODITY:
            commodities = Commodity.objects.filter(country_id=country_id).order_by('id')
            for commodity in commodities:
                commodity_name = commodity.display_name
                commodity_data.append([commodity.id, commodity_name, commodity.material_class, commodity.unit])
                for grade in Grade.objects.filter(commodity_id=commodity.id).order_by('id'):
                    grade_data.append([grade.id, grade.name, commodity.id, commodity_name])
                for variety in Variety.objects.filter(commodity_id=commodity.id).order_by('id'):
                    variety_data.append([variety.id, variety.name, commodity.id, commodity_name])

            def write_to_csv(data, headers, add_empty_line=True):
                writer.writerow(headers)
                for _data in data:
                    writer.writerow(_data)
                if add_empty_line:
                    writer.writerow(len(headers) * [''])

            write_to_csv(commodity_data, ADMIN_COMMODITY_CSV_HEADERS)
            write_to_csv(grade_data, ADMIN_GRADE_CSV_HEADERS)
            write_to_csv(variety_data, ADMIN_VARIETY_CSV_HEADERS, False)
        else:
            data_of = _sanitize_param(get(query_params, 'dataOf'))
            company_id = _sanitize_param(get(query_params, 'company'))
            companies = [company_id]
            if data_of != 'self':
                companies = Company.directory_companies(company_id).values_list('id')
            if data_type == SITES:
                result = Farm.objects.filter(
                    Q(company_id__in=companies) | Q(broker_company_id__in=companies)
                )
                csv_headers = ADMIN_SITES_CSV_HEADERS
            elif data_type == NGR:
                result = Ngr.objects.filter(company_id__in=companies).select_related('company')
                csv_headers = ADMIN_NGR_CSV_HEADERS
            elif data_type == STORAGES:
                result = Storage.objects.filter(farm__company_id__in=companies).select_related('farm__company')
                csv_headers = ADMIN_STORAGE_CSV_HEADERS
            elif data_type == EMPLOYEES:
                result = Employee.objects.filter(company_id__in=companies).select_related('type', 'company')
                csv_headers = ADMIN_EMPLOYEE_CSV_HEADERS
            else:
                return
            writer.writerow(csv_headers)
            for item in result:
                row = Company.to_admin_stocks_upload_csv_row(data_type, item)
                writer.writerow(row)
        buff2 = io.BytesIO(buff.getvalue().encode())
        S3.upload(download.file_path, buff2)
        download_qs.update(status='ready')
    except Exception as ex:  # pylint: disable=broad-except
        ERRBIT_LOGGER.log(ex)
        download_qs.update(status='failed', failure_reason=str(ex))

    ABLY.publish(
        'stocks',
        str(user.id),
        json.dumps(
            download.to_dict(),
            sort_keys=True,
            indent=1,
            cls=DjangoJSONEncoder,
        )
    )


@app.task(ignore_result=True)
def generate_subscribers_csv_internal():  # pragma: no cover
    if not is_production():
        return

    subscribers = Company.objects.filter(Q(transaction_participation=True) | Q(mobile_participation=True))
    if not subscribers.exists():
        return

    buff = io.StringIO()
    writer = csv.writer(buff, dialect='excel', delimiter=',')
    writer.writerow(Company.get_subscriber_csv_headers())

    traversed = []
    for subscriber in subscribers.order_by('business_name'):
        if subscriber.id in traversed:
            continue
        writer.writerow(subscriber.to_subscriber_setting_csv_row())
        traversed.append(subscriber.id)

    now = timezone.now().strftime('%Y-%m-%d')
    mail = EmailMessage(
        subject=f'{settings.ENV.upper()} Subscribers List - {now}',
        body='Please find the attached subscribers list.',
        from_email=FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME,
        to=['<EMAIL>', '<EMAIL>', '<EMAIL>']
    )
    mail.attach(f'subscribers-{now.replace("-", "")}.csv', buff.getvalue(), 'text/csv')
    return mail.send()

@app.task(ignore_result=True)
@update_job
def send_email_to_added_approved_buyer(company_id, buyer_company_id):  # pylint: disable=redefined-builtin,too-many-arguments
    company = Company.objects.filter(id=company_id).first()
    buyer_company = Company.objects.filter(id=buyer_company_id).first()
    recipients_emails = set(buyer_company.get_company_admins_with_email().values_list('email', flat=True))
    if len(recipients_emails) > 0:
        msg_html = render_to_string(
            'added_as_approved_buyer.html',
            {
                'company': company
            }
        )
        mail = EmailMessage(
            subject="Added as approved buyer",
            body=msg_html,
            from_email=FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME,
            to=recipients_emails,
            reply_to=[SUPPORT_EMAIL],
        )
        mail.content_subtype = "html"
        return mail.send()


COMPANY_TASK_MAPPING = {
    'generate_companies_csv': generate_companies_csv,
    'create_warehouse_invoice_for': create_warehouse_invoice_for,
    'trigger_update_company_ngr_from_portal': trigger_update_company_ngr_from_portal,
    'sync_ngr_from_portal': sync_ngr_from_portal,
    'generate_stocks_uploads_csv': generate_stocks_uploads_csv,
    'update_shrinkage_with_updated_business_type': update_shrinkage_with_updated_business_type,
    'update_not_invoiced_warehouse_invoices': update_not_invoiced_warehouse_invoices,
    'send_email_to_added_approved_buyer': send_email_to_added_approved_buyer,
}
