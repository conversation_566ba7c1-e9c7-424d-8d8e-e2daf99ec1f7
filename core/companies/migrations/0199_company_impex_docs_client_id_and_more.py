# Generated by Django 4.2.15 on 2025-01-15 09:20

import dirtyfields.dirtyfields
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0199_company_cor_texts_historicalcompany_cor_texts'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='impex_docs_client_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='impex_docs_client_secret',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='impex_docs_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='company',
            name='impex_docs_user_id',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='impex_docs_client_id',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='impex_docs_client_secret',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='impex_docs_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='impex_docs_user_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.CreateModel(
            name='ImpexDocsConnection',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('token', models.TextField()),
                ('expires_in', models.DateTimeField()),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.company')),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'company_impex_docs_connections',
            },
            bases=(dirtyfields.dirtyfields.DirtyFieldsMixin, models.Model),
        ),
    ]
