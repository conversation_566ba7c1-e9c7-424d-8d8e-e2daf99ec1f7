from django.core.management import BaseCommand
from django.db.models import F, Subquery, OuterRef, Q


class Command(BaseCommand):
    help = 'Correct Load/Stock Farm for transferred Storage/Field'

    def handle(self, *args, **options):
        from core.loads.models import Load

        Load.objects.filter(~Q(storage__farm=F('farm'))).filter(
            storage_id__isnull=False, farm_id__isnull=False
        ).update(farm_id=Subquery(Load.objects.filter(pk=OuterRef('pk')).values('storage__farm_id')[:1]))

        Load.objects.filter(~Q(farm_field__farm=F('farm'))).filter(
            storage_id__isnull=True, farm_id__isnull=False, farm_field_id__isnull=False
        ).update(farm_id=Subquery(Load.objects.filter(pk=OuterRef('pk')).values('farm_field__farm_id')[:1]))
