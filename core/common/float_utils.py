from decimal import Decimal, ROUND_HALF_EVEN, localcontext

def bankers_round(number, ndigits=0):
    """
    Simple banker's rounding function (round half even).
    Rounds to the nearest even number when the value is exactly halfway between two numbers.

    bankers_round(2.124, 2) → 2.12  (normal rounding)
    bankers_round(2.126, 2) → 2.13  (normal rounding)
    bankers_round(2.125, 2) → 2.12  (2.125 is halfway between 2.12 and 2.13, rounds to even)
    bankers_round(2.135, 2) → 2.14  (2.135 is halfway between 2.13 and 2.14, rounds to even)
    """

    if not number:
        return number

    decimal_number = Decimal(str(number))

    existing_decimal_places = abs(min(0, decimal_number.as_tuple().exponent))

    if ndigits > existing_decimal_places:
        return float(f"{float(decimal_number):.{ndigits}f}")

    with localcontext() as ctx:
        # Number of significant digits before and after the decimal
        ctx.prec = 60
        if ndigits > 0:
            exponent = Decimal('0.' + '0' * (ndigits-1) + '1')
        else:
            exponent = Decimal('1')

        result = decimal_number.quantize(
            exponent,
            rounding=ROUND_HALF_EVEN
        )

    if result == 0:
        result = abs(result)

    if ndigits == 0:
        return int(result)

    return float(result)
