# Generated by Django 2.2.13 on 2020-09-01 14:15

import django.contrib.postgres.fields
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0087_auto_20200720_0559'),
    ]

    operations = [
        migrations.AlterField(
            model_name='freightslot',
            name='provider_updated_fields',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=list, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='freightslot',
            name='siblings',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='historicalfreightslot',
            name='provider_updated_fields',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=list, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='historicalfreightslot',
            name='siblings',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='sitemanagementsettings',
            name='pits',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, default=list, null=True, size=None),
        ),
    ]
