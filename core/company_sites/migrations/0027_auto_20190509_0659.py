# Generated by Django 2.1.7 on 2019-05-09 06:59

from django.db import migrations

def update_new_commodity_ids(apps, schema_editor):
    FreightSlot = apps.get_model('company_sites', 'FreightSlot')
    for slot in FreightSlot.objects.filter(commodity_id__gt=27):
        slot.commodity_id = 5000 + slot.commodity_id
        slot.save()

class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0026_auto_20190507_1751'),
    ]

    operations = [
        migrations.RunPython(update_new_commodity_ids)
    ]
