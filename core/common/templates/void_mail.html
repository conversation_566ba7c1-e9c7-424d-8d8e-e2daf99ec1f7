<!DOCTYPE html>
<html>
  <head>
    <meta charset=utf-8>
  </head>
  <body>
    <p>Dear {{ recipient_user.name }},</p>
    <p>{{ user.name }} from {{ user.company.name }} {% if acceptance_required %} has requested to mark the {% else %} has marked the {% endif %}
      {% if obj.entity == 'freightorder' and obj.type_id == 3 %}
        Grain Order
      {% elif obj.entity == 'freightorder' and obj.type_id == 6  %}
        Pack Order
      {% elif obj.entity == 'freightcontract' and obj.type_id == 6 %}
        Pack Movement
      {% else %}
        {{ obj.entity | title }}
      {% endif %}
      ({{ obj.identifier }})
      void because {{ request_reason }}.</p>
  </body>
  {% if obj.entity == 'contract' %}
    {% include "amend_preview.html" %}
  {% elif obj.entity == 'freightorder' and obj.type_id == 6 %}
    {% include "pack_order_preview.html" %}
  {% elif obj.entity == 'freightorder' and not obj.type_id == 6 %}
    {% include "freight_order_amend_preview.html" %}
  {% elif obj.entity == 'freightcontract' and obj.type_id == 6 %}
    {% include "pack_movement_preview.html" %}
  {% elif obj.entity == 'freightcontract' and not obj.type_id == 6 %}
    {% include "freight_contract_amend_preview.html" %}
  {% elif obj.entity == 'invoice' %}
    {% include "invoice.html" %}
  {% endif %}
</html>
