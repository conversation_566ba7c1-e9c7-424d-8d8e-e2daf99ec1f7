# Generated by Django 2.1.7 on 2019-05-21 10:24

import core.common.models
import core.company_sites.models
from django.conf import settings
import django.contrib.postgres.fields
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0030_auto_20190521_0432'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitemanagementsettings',
            name='editable_minutes_before_start',
            field=core.common.models.RoundedDecimalField(decimal_places=2, default=60, max_digits=5),
        ),
        migrations.AddField(
            model_name='sitemanagementsettings',
            name='fields',
            field=django.contrib.postgres.fields.jsonb.JSONField(default=core.company_sites.models.default_fields),
        ),
        migrations.AddField(
            model_name='sitemanagementsettings',
            name='labels',
            field=django.contrib.postgres.fields.jsonb.JSONField(default=[]),
        ),
        migrations.AddField(
            model_name='sitemanagementsettings',
            name='mandatory_fields',
            field=django.contrib.postgres.fields.jsonb.JSONField(default=[]),
        ),
        migrations.AddField(
            model_name='sitemanagementsettings',
            name='title_order',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=100), default=core.company_sites.models.default_title_order, size=None),
        ),
    ]
