# Generated by Django 2.1.9 on 2019-07-29 09:43

from django.db import migrations

def add_status_in_site_management_settings(apps, schema_editor):
    SiteManagementSettings = apps.get_model('company_sites', 'SiteManagementSettings')
    for settings in SiteManagementSettings.objects.all():
        settings.statuses.append({
            'id': 'rejected',
            'name': 'Rejected',
            'label': 'Rejected',
            'color': '#000000',
        })
        settings.save()


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0054_auto_20190729_0942'),
    ]

    operations = [
        migrations.RunPython(add_status_in_site_management_settings)
    ]
