# Generated by Django 2.1.9 on 2019-08-07 04:55

from django.db import migrations

def update_unreg_grower_company_setups(apps, schema_editor):
    CompanySetup = apps.get_model('companies', 'CompanySetup')

    for setup in CompanySetup.objects.filter(
            company__owner_company_id__isnull=False, company__type_id=1
    ):
        setup.assets.pop('conditions', None)
        setup.save()

class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0034_historicalsubcontractor_subcontractor'),
    ]

    operations = [
        migrations.RunPython(update_unreg_grower_company_setups)
    ]
