# Generated by Django 4.1.13 on 2023-12-08 10:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('countries', '0005_alter_country_code_alter_country_name'),
        ('banks', '0017_alter_bank_created_by_alter_bank_updated_by'),
    ]

    operations = [
        migrations.AddField(
            model_name='bank',
            name='country',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, to='countries.country'),
        ),
        migrations.AddField(
            model_name='historicalbank',
            name='country',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='countries.country'),
        ),
    ]
