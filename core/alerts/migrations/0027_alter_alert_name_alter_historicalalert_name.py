# Generated by Django 4.1.13 on 2024-07-23 08:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0026_alter_alert_name_alter_historicalalert_name'),
    ]

    operations = [
        migrations.AlterField(
            model_name='alert',
            name='name',
            field=models.CharField(choices=[('missing_docket_alert', 'Missing Docket Alert for Freight Providers'), ('missing_docket_pickup_site_alert', 'Missing Docket Alert for Pickup Site'), ('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit Breach'), ('pickup_confirmation_sms', 'Pickup Confirmation - SMS'), ('inload_done', 'Delivery Confirmation - SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Confirmation - Email'), ('delivery_information', 'Delivery Confirmation - Email'), ('outload_report', 'Outload Report'), ('slot_booking_alert', 'Slot Booking Updates'), ('truck_cleanliness_declaration_alert', 'Truck Cleanliness Declaration Not Filled'), ('docket_clarification_alert', 'Docket Clarifications'), ('stock_operations_update_alert', 'Stock Operations Update')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalalert',
            name='name',
            field=models.CharField(choices=[('missing_docket_alert', 'Missing Docket Alert for Freight Providers'), ('missing_docket_pickup_site_alert', 'Missing Docket Alert for Pickup Site'), ('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit Breach'), ('pickup_confirmation_sms', 'Pickup Confirmation - SMS'), ('inload_done', 'Delivery Confirmation - SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Confirmation - Email'), ('delivery_information', 'Delivery Confirmation - Email'), ('outload_report', 'Outload Report'), ('slot_booking_alert', 'Slot Booking Updates'), ('truck_cleanliness_declaration_alert', 'Truck Cleanliness Declaration Not Filled'), ('docket_clarification_alert', 'Docket Clarifications'), ('stock_operations_update_alert', 'Stock Operations Update')], max_length=100),
        ),
    ]
