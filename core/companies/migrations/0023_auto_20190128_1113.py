# Generated by Django 2.1.5 on 2019-01-28 11:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0022_bhcsite_historicalbhcsite'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicaladdedcompany',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_addedcompanys_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicaladdedcompany',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_addedcompanys_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalbhcsite',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_bhcsites_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalbhcsite',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_bhcsites_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_companys_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='payment_term',
            field=models.ForeignKey(blank=True, db_constraint=False, default=10, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='contracts.PaymentTerm'),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='type',
            field=models.ForeignKey(blank=True, db_constraint=False, default=5, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.CompanyType'),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_companys_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalcompanysetup',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_companysetups_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalcompanysetup',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_companysetups_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalcompanytype',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_companytypes_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalcompanytype',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_companytypes_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalfarmdirectory',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_farmdirectorys_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalfarmdirectory',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_farmdirectorys_updated_by', to=settings.AUTH_USER_MODEL),
        ),
    ]
