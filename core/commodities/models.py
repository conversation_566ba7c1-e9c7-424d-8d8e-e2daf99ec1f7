from numbers import Number

from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.core.cache import cache
from django.db import models
from django.db.models import Q, Sum
from django.http import QueryDict
from pydash import get

from core.commodities.constants import CLASS_3, UNIT_CHOICES, TYPE_CHOICES, INTERCHANGEABLE_COMMODITIES_MAPPING, \
    UNIT_ABBREVIATIONS, UNKNOWN_COMMODITY_ID
from core.common.constants import (MT, BALES, METER_CUBE, NUMBER_OF_BALES, VOLUME, POUND, SHORT_TONNE,
                                   LB_TO_SHORT_TONNE_MULTIPLIER, CENTURY_TONNE, LB_TO_MT_MULTIPLIER,
                                   LB_TO_CWT_MULTIPLIER, BUSHEL, KG, LITRE, MODULES, NUMBER_OF_MODULES,
                                   STRICT_QUANTITIES)
from core.common.expressions import CustomRegexWrapper
from core.common.models import BaseModel, MasterDataMixin
from core.common.utils import flatten, strip_special
from core.common.utils import to_display_attr
from core.countries.constants import AUSTRALIA_COUNTRY_ID
from core.invoices.constants import DAFF_LEVY

DEFAULT_LEVY = {'daff': {'value': 0, 'multiplier': 'contract_value'},
                'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
                'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
                'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}}

LEVY_MAP = {
    1: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
        'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
        'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
        'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    2: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
        'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
        'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
        'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    3: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
        'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
        'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
        'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    4: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
        'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
        'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
        'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    5: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
        'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
        'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
        'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    6: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
        'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
        'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
        'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    7: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
        'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
        'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
        'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    8: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
        'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
        'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
        'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    9: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
        'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
        'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
        'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    10: {'daff': {'value': 0.00720, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    11: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    12: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    13: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    14: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    15: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    16: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    17: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    18: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    19: {'daff': {'value': 4.06000, 'multiplier': 'tonnage'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    20: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    28: {'daff': {'value': 0.01005, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    54: {'daff': {'value': 0.01020, 'multiplier': 'contract_value'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    59: {'daff': {'value': 0.5, 'multiplier': 'tonnage'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    60: {'daff': {'value': 0.5, 'multiplier': 'tonnage'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    61: {'daff': {'value': 0.5, 'multiplier': 'tonnage'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    62: {'daff': {'value': 0.5, 'multiplier': 'tonnage'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    63: {'daff': {'value': 0.5, 'multiplier': 'tonnage'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    65: {'daff': {'value': 0.5, 'multiplier': 'tonnage'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    66: {'daff': {'value': 0.5, 'multiplier': 'tonnage'},
         'fis': {'wa': {'value': 0.25, 'multiplier': 'tonnage'}},
         'gif': {'sa':{'value': 0.001, 'multiplier': 'contract_value'}},
         'girdf': {'sa':{'value': 0.0012, 'multiplier': 'contract_value'}}},
    187: {'sask': {'saskatchewan': {'value': 0.75, 'multiplier': 'tonnage'}},
          'alberta': {'alberta': {'value': 1, 'multiplier': 'tonnage'}},
          'mcg': {'manitoba': {'value': 1, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 2, 'multiplier': 'tonnage'}}},
    188: {'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}},
          'gfo': {'ontario': {'value': 0.42, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.6, 'multiplier': 'tonnage'}}},
    189: {'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}},
          'gfo': {'ontario': {'value': 0.42, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.6, 'multiplier': 'tonnage'}}},
    190: {'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}},
          'gfo': {'ontario': {'value': 0.42, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.6, 'multiplier': 'tonnage'}}},
    191: {'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}},
          'gfo': {'ontario': {'value': 0.42, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.6, 'multiplier': 'tonnage'}}},
    192: {'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}},
          'gfo': {'ontario': {'value': 0.42, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.6, 'multiplier': 'tonnage'}}},
    193: {'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}},
          'gfo': {'ontario': {'value': 0.42, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.6, 'multiplier': 'tonnage'}}},
    194: {'sask': {'saskatchewan': {'value': 0.75, 'multiplier': 'tonnage'}},
          'aogc': {'alberta': {'value': 0.75, 'multiplier': 'tonnage'}},
          'moga': {'manitoba': {'value': 0.75, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.8, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    195: {'sask': {'saskatchewan': {'value': 0.75, 'multiplier': 'tonnage'}},
          'aogc': {'alberta': {'value': 0.75, 'multiplier': 'tonnage'}},
          'moga': {'manitoba': {'value': 0.75, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.8, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    196: {'sask': {'saskatchewan': {'value': 0.67, 'multiplier': 'contract_value'}},
          'pulse_grower': {'alberta': {'value': 0.75, 'multiplier': 'contract_value'}},
          'mpsg': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}},
          'gfo': {'ontario': {'value': 1.42, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 2, 'multiplier': 'tonnage'}}},
    197: {'sask': {'saskatchewan': {'value': 0.67, 'multiplier': 'contract_value'}},
          'pulse_grower': {'alberta': {'value': 0.75, 'multiplier': 'contract_value'}},
          'mpsg': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}},
          'gfo': {'ontario': {'value': 1.42, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 2, 'multiplier': 'tonnage'}}},
    198: {'sask': {'saskatchewan': {'value': 0.67, 'multiplier': 'contract_value'}},
          'pulse_grower': {'alberta': {'value': 0.75, 'multiplier': 'contract_value'}},
          'mpsg': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}},
          'gfo': {'ontario': {'value': 1.42, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 2, 'multiplier': 'tonnage'}}},
    199: {'sask': {'saskatchewan': {'value': 0.67, 'multiplier': 'contract_value'}},
          'pulse_grower': {'alberta': {'value': 0.75, 'multiplier': 'contract_value'}},
          'mpsg': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}},
          'gfo': {'ontario': {'value': 1.42, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 2, 'multiplier': 'tonnage'}}},
    200: {'sask': {'saskatchewan': {'value': 0.67, 'multiplier': 'contract_value'}},
          'pulse_grower': {'alberta': {'value': 0.75, 'multiplier': 'contract_value'}},
          'mpsg': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}},
          'gfo': {'ontario': {'value': 1.42, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 2, 'multiplier': 'tonnage'}}},
    201: {'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}}},
    202: {'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}}},
    203: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 1, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    204: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 1, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    205: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    206: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 0.98, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    207: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 1, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    208: {'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    209: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 1, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    210: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 1, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    211: {'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    212: {'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    213: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 1, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    214: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 0.98, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    215: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 0.98, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    216: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    217: {'sask': {'saskatchewan': {'value': 1, 'multiplier': 'tonnage'}},
          'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 0.98, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    218: {'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    219: {'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    220: {'ag': {'alberta': {'value': 1.09, 'multiplier': 'tonnage'}},
          'gfo': {'ontario': {'value': 0.79, 'multiplier': 'tonnage'}},
          'pgq': {'quebec': {'value': 1.9, 'multiplier': 'tonnage'}},
          'bc': {'british columbia': {'value': 0.48, 'multiplier': 'tonnage'}}},
    222: {'sask': {'saskatchewan': {'value': 2.36, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}}},
    223: {'sask': {'saskatchewan': {'value': 2.36, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}}},
    224: {'sask': {'saskatchewan': {'value': 2.36, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}}},
    225: {'sask': {'saskatchewan': {'value': 2.36, 'multiplier': 'tonnage'}},
          'mca': {'manitoba': {'value': 0.5, 'multiplier': 'contract_value'}}},
}


def get_entity_item_by_storage_levels_greatest_tonnage(storage_levels, entity, entity_klass, to_dict=True):
    if not storage_levels or not entity:
        return None

    attr = f"load__{entity}_id"
    attr_is_null = {f"{attr}__isnull": False}

    obj = entity_klass.objects.filter(
        id=storage_levels.filter(
            **attr_is_null
        ).values(
            attr
        ).annotate(
            Sum('tonnage')
        ).order_by(
            '-tonnage__sum'
        ).values_list(
            attr, flat=True
        ).first()
    ).first()

    if obj and to_dict:
        return obj.to_dict()

    return obj


def get_entity_item_by_loads_greatest_tonnage(loads, entity, entity_klass, to_dict=True):
    if not loads or not entity:
        return None

    attr = f"{entity}_id"
    attr_is_null = {f"{attr}__isnull": False}
    attr_value = get(max(loads.filter(**attr_is_null), key=lambda _: _.net_weight, default=None), attr)
    if attr_value:
        obj = entity_klass.objects.filter(id=attr_value).first()
        if obj and to_dict:
            return obj.to_dict()
        return obj


def get_variety_by_storage_levels_greatest_tonnage(storage_levels, to_dict=True):
    return get_entity_item_by_storage_levels_greatest_tonnage(storage_levels, 'variety', Variety, to_dict)


def get_variety_by_loads_greatest_tonnage(loads, to_dict=True):
    return get_entity_item_by_loads_greatest_tonnage(loads, 'variety', Variety, to_dict)


def get_grade_by_storage_levels_greatest_tonnage(storage_levels, to_dict=True):
    return get_entity_item_by_storage_levels_greatest_tonnage(storage_levels, 'grade', Grade, to_dict)


def get_grade_by_loads_greatest_tonnage(loads, to_dict=True):
    return get_entity_item_by_loads_greatest_tonnage(loads, 'grade', Grade, to_dict)


def get_grade_by_commodity_id_and_spec_params(commodity_id: int, spec_params: dict, to_dict=True):
    if not commodity_id:
        return None

    commodity_grades = Commodity.objects.get(id=commodity_id).grades()

    resulting_grade = None

    for grade in commodity_grades:
        for spec in grade.specs:
            spec_code_lower = spec.get('code', '').lower()

            if spec_code_lower not in spec_params or not isinstance(
                    spec_params[spec_code_lower], Number
            ):
                continue

            spec_val = float(spec_params[spec_code_lower] or 0)
            grade_spec_max_val = float(spec.get('max', 0) or 0)
            grade_spec_min_val = float(spec.get('min', 0) or 0)
            if (spec_val < grade_spec_min_val) or (spec_val > grade_spec_max_val):
                resulting_grade = None
                break

            resulting_grade = grade

        if resulting_grade:
            break

    if resulting_grade and to_dict:
        resulting_grade = resulting_grade.to_dict()

    return resulting_grade


class CommodityManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):
        return super().get_queryset().exclude(id=UNKNOWN_COMMODITY_ID)


class DefaultCommodityManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):  # pylint: disable=useless-super-delegation
        return super().get_queryset()


class Commodity(BaseModel, MasterDataMixin):

    objects = CommodityManager()
    all = DefaultCommodityManager()

    class Meta:
        app_label = 'commodities'
        db_table = 'commodities'
        ordering = ['id']

    UNIT_PRECISION = 5

    mandatory_props = BaseModel.mandatory_props + [
        'display_name', 'levy', 'is_quantity_based', 'price_unit', 'tonnage_unit', 'is_strict_quantity_based',
        'quantity_label', 'type'
    ]

    COMMODITIES = (
        ('wheat', 'Wheat'),
        ('barley', 'Barley'),
        ('canola', 'Canola'),
        ('sorghum', 'Sorghum'),
        ('lentils', 'Lentils'),
        ('chickpeas_desi', 'Chickpeas (Desi)'),
        ('chickpeas_kabuli', 'Chickpeas (Kabuli)'),
        ('oats', 'Oats'),
        ('triticale', 'Triticale'),
        ('maize', 'Maize'),
        ('broad_beans', 'Broad Beans'),
        ('faba_beans', 'Faba Beans'),
        ('lupins_albus', 'Lupins (Albus)'),
        ('lupins_angustifolius', 'Lupins (Angustifolius)'),
        ('mungbeans', 'Mung beans'),
        ('field_peas', 'Field Peas'),
        ('vetch', 'Vetch'),
        ('sunflower', 'Sunflower'),
        ('soybean', 'Soybean'),
        ('cottonseed', 'Cottonseed'),
        ('safflower', 'Safflower'),
        ('pke', 'PKE'),
        ('linseed', 'Linseed'),
        ('canola_meal', 'Canola Meal'),
        ('soybean_meal', 'Soybean Meal'),
        ('cereal_hay', 'Cereal Hay & Silage'),
        ('legume_hay', 'Legume and Pasture Hay & Silage'),
        ('fertiliser', 'Fertiliser'),
        ('cereal_rye', 'Rye'),
        ('wheat_seed', 'Wheat Seed'),
        ('barley_seed', 'Barley Seed'),
        ('quarry_material', 'Quarry Material'),
        ('garden_mulch', 'Garden Mulch'),
        ('firewood', 'Firewood'),
        ('poppy_meal', 'Poppy Meal'),
        ('almond_hulls', 'Almond Hulls'),
        ('ruminant_paddock', 'Ruminant Paddock Nuts'),
        ('rubbish', 'Rubbish'),
        ('chicken_manure', 'Chicken Manure'),
        ('canola_hay', 'Canola Hay'),
        ('soy_hulls', 'Soy Hulls'),
        ('corn', 'Corn'),
        ('straw', 'Straw'),
        ('Millet', 'Millet'),
        ('Millet', 'Millet'),
        ('cotton_lint', 'Cotton Lint'),
        ('Silage', 'Silage'),
        ('Organic Wheat', 'Organic Wheat'),
        ('Chickpeas Seed', 'Chickpeas Seed'),
        ('Fuel', 'Fuel'),
        ('Pea Pollard', 'Pea Pollard'),
        ('Malt Combings', 'Malt Combings'),
        ('Bean Kibble', 'Bean Kibble'),
        ('Dried Distillers Grain (DDG)', 'Dried Distillers Grain (DDG)'),
        ('Feed Pellets', 'Feed Pellets'),
    )
    name = models.CharField(max_length=100, null=False, blank=False,)
    material_class = models.CharField(default=CLASS_3, max_length=100)
    specs = ArrayField(models.JSONField(), null=True, blank=True, default=list)
    sustainable = models.BooleanField(default=False)
    unit = models.CharField(default=MT, choices=UNIT_CHOICES, max_length=100)
    country = models.ForeignKey('countries.Country', on_delete=models.SET_DEFAULT, default=AUSTRALIA_COUNTRY_ID)
    unit_conversions = models.JSONField(null=True, blank=True)
    type = models.CharField(default='grain', choices=TYPE_CHOICES, max_length=100)

    @property
    def precision(self):
        return self.country.config['precision'] or self.UNIT_PRECISION

    @property
    def should_use_entered_value(self):
        return self.precision > 2

    def get_spec_by_code(self, spec_code):
        return next((spec for spec in self.specs if spec['code'] == spec_code.upper()), None)  # pylint: disable=not-an-iterable

    def get_spec_name(self, spec_code):
        return get(self.get_spec_by_code(spec_code), 'name')

    def get_spec_unit(self, spec_code):
        spec = self.get_spec_by_code(spec_code)
        if '%' in spec['name']:
            return '%'
        return spec['name'].split('(')[1].replace(')', '')

    @classmethod
    def is_bales_commodity(cls, commodity_id):
        return bool(get(Commodity.objects.filter(id=commodity_id).first(), 'is_bales'))

    @classmethod
    def is_modules_commodity(cls, commodity_id):
        return bool(get(Commodity.objects.filter(id=commodity_id).first(), 'is_modules'))

    @classmethod
    def is_meter_cube_commodity(cls, commodity_id):
        return bool(get(Commodity.objects.filter(id=commodity_id).first(), 'is_meter_cube'))

    @classmethod
    def is_mt_commodity(cls, commodity_id):
        return bool(get(Commodity.objects.filter(id=commodity_id).first(), 'is_mt'))

    @property
    def is_quantity_based(self):
        return self.unit in [METER_CUBE, BALES, KG, LITRE, MODULES]

    @property
    def is_strict_quantity_based(self):
        return self.unit in STRICT_QUANTITIES

    @property
    def price_unit(self):
        if self.unit in [BALES, MODULES]:
            return MT
        return self.unit

    @property
    def tonnage_unit(self):
        if self.is_quantity_based:
            return MT
        return self.unit

    @property
    def quantity_label(self):
        if self.is_bales:
            return NUMBER_OF_BALES
        if self.is_modules:
            return NUMBER_OF_MODULES
        if self.is_meter_cube:
            return VOLUME
        return 'Quantity'

    @property
    def short_quantity_label(self):
        if self.is_bales:
            return BALES
        if self.is_modules:
            return MODULES
        if self.is_meter_cube:
            return VOLUME
        return 'Quantity'

    @property
    def is_meter_cube(self):
        return self.unit == METER_CUBE

    @property
    def is_kg(self):
        return self.unit == KG

    @property
    def is_litre(self):
        return self.unit == LITRE

    @property
    def is_bales(self):
        return self.unit == BALES

    @property
    def is_modules(self):
        return self.unit == MODULES

    @property
    def is_mt(self):
        return self.unit == MT

    @classmethod
    def find_by_name(cls, name, country_id=None):
        name = strip_special(name)
        expression = CustomRegexWrapper('name').expression
        queryset = cls.objects.annotate(_name=expression).filter(_name=name)
        if country_id:
            queryset = queryset.filter(country_id=country_id)
        return queryset.first()

    @classmethod
    def to_display_names(cls, commodity_ids):
        return [c.display_name for c in Commodity.objects.filter(id__in=commodity_ids)]

    @classmethod
    def everything(cls, inclusions=None, include_unknown=False):
        queryset = Commodity.all if include_unknown else Commodity.objects
        inclusions = inclusions or []

        if 'variety' in inclusions:
            queryset = queryset.prefetch_related('variety_set__epr_set')
        if 'grade' in inclusions:
            queryset = queryset.prefetch_related('grade_set')
        return queryset.filter()

    def _error_message_for_grade_params(self, expected_params, request_params):
        if not request_params:
            return None
        elif (expected_params - set(request_params)) \
           or not all(_p for _p in request_params.values()):
            return self._error_message(expected_params)
        return None

    def _error_message(self, params):
        return {
            'errors': ['For ' + self.display_name + \
            ' commodity grades pass valid ' + \
            ', '.join(sorted(params)) + \
            ' in params']
        }

    def grades(self, params=None):
        attr = '_' + self.name + '_grades'
        grades_func = getattr(self, attr, None) or self._commodity_grades_all
        grades = grades_func(params)

        if 'errors' not in grades:
            grades = self.order_specs(grades)

        return grades

    @staticmethod
    def order_specs(grades):
        for grade in grades:
            grade.specs = sorted(grade.specs, key=lambda k: k.get('order'))

        return grades

    def _wheat_grades(self, params):
        if isinstance(params, QueryDict):
            params = params.dict()
        expected_params = {"zone", "variety_id"}
        errors = self._error_message_for_grade_params(expected_params, params)
        if errors:
            return errors

        if params:
            variety = self.variety_set.get(id=params['variety_id'])
            category = get(variety, f"details.categories.{params['zone']}", None)

            if category:
                return Grade.objects.filter(Q(category=category) | Q(name='UNGRADED - WHEAT'), commodity_id=self.id)
        else:
            return self._commodity_grades_all()

        return self._error_message(expected_params)

    def _commodity_grades_all(self, _=None):
        return Grade.objects.filter(commodity_id=self.id).order_by('order')

    _canola_grades = _commodity_grades_all
    _lentils_grades = _commodity_grades_all
    _sorghum_grades = _commodity_grades_all
    _chickpeas_desi_grades = _commodity_grades_all
    _chickpeas_kabuli_grades = _commodity_grades_all
    _oats_grades = _commodity_grades_all
    _triticale_grades = _commodity_grades_all
    _maize_grades = _commodity_grades_all
    _broad_beans_grades = _commodity_grades_all
    _faba_beans_grades = _commodity_grades_all
    _lupins_albus_grades = _commodity_grades_all
    _lupins_angustifolius_grades = _commodity_grades_all
    _mungbeans_grades = _commodity_grades_all
    _field_peas_grades = _commodity_grades_all
    _vetch_grades = _commodity_grades_all
    _sunflower_grades = _commodity_grades_all
    _soybean_grades = _commodity_grades_all
    _cottonseed_grades = _commodity_grades_all
    _safflower_grades = _commodity_grades_all

    @property
    def ungraded(self):
        return self.grade_set.filter(name__icontains='ungraded').first()

    @property
    def display_name(self):
        return to_display_attr(self.COMMODITIES, self.name) or self.name

    @property
    def display_name_with_class(self):
        return f"{self.display_name} ({self.material_class})"

    def levy(self, levy_type=DAFF_LEVY):
        return get(LEVY_MAP.get(self.id), levy_type) or get(get(self.country, 'config.default_levies'), levy_type)

    @classmethod
    def get_interchangeable_commodities(cls, commodity_id):
        commodity = Commodity.objects.filter(pk=commodity_id).first()
        if commodity:
            commodity_names = flatten(
                [item if commodity.name in item else commodity.name for item in INTERCHANGEABLE_COMMODITIES_MAPPING]
            )
            return Commodity.objects.filter(
                name__in=commodity_names, country_id=commodity.country_id).prefetch_related('grade_set')

        return []

    @classmethod
    def convert_to_self_unit(cls, commodity_id, tonnage, _round=False):
        commodity = cls.objects.get(id=commodity_id)
        if not commodity.country.config.get('do_not_convert_unit_in_api_response', False):
            return commodity.convert_to(tonnage, commodity.country.display_unit, commodity.unit, _round)
        return tonnage

    def convert_to(self, tonnage, from_unit=None, to_unit=POUND, _round=False):  # pylint: disable=too-many-branches
        if not tonnage:
            return tonnage
        from_unit = from_unit or self.unit
        if from_unit == to_unit or not from_unit or not to_unit:
            return tonnage
        show_conversions = self.country.should_convert_unit
        if not show_conversions:
            return tonnage
        tonnage = float(tonnage)
        if self.conversion_rule(from_unit, to_unit, True):
            tonnage = self.convert_via_rule(tonnage, from_unit, to_unit)
            return round(tonnage, self.precision) if _round else tonnage

        if from_unit in [MT, SHORT_TONNE, CENTURY_TONNE]:
            if from_unit == MT:
                value = self.convert_via_rule(tonnage, from_unit, POUND)
                if value is None:
                    value = self.mt_to_lb(tonnage)
                tonnage = value
            if from_unit == SHORT_TONNE:
                value = self.convert_via_rule(tonnage, from_unit, POUND)
                if value is None:
                    value = self.st_to_lb(tonnage)
                tonnage = value
            if from_unit == CENTURY_TONNE:
                value = self.convert_via_rule(tonnage, from_unit, POUND)
                if value is None:
                    value = self.cwt_to_lb(tonnage)
                tonnage = value
        else:
            tonnage = self.convert_via_rule(tonnage, from_unit, POUND)

        from_unit = POUND
        if to_unit == POUND:
            pass
        elif to_unit in [MT, SHORT_TONNE, CENTURY_TONNE] and not self.conversion_rule(from_unit, to_unit):
            if to_unit == MT:
                value = self.convert_via_rule(tonnage, from_unit, MT)
                if value is None:
                    value = self.lb_to_mt(tonnage)
                tonnage = value
            if to_unit == SHORT_TONNE:
                value = self.convert_via_rule(tonnage, from_unit, SHORT_TONNE)
                if value is None:
                    value = self.lb_to_st(tonnage)
                tonnage = value
            if to_unit == CENTURY_TONNE:
                value = self.convert_via_rule(tonnage, from_unit, CENTURY_TONNE)
                if value is None:
                    value = self.lb_to_cwt(tonnage)
                tonnage = value
        else:
            tonnage = self.convert_via_rule(tonnage, from_unit, to_unit)

        if tonnage:
            return round(tonnage, self.precision) if _round else tonnage

    def convert_via_rule(self, tonnage, from_unit, to_unit, _round=False):
        value = None
        if from_unit == to_unit:
            value = tonnage
        else:
            rule = self.conversion_rule(from_unit, to_unit)
            if rule:
                operator = Operator(get(rule, 'operator', None))
            else:
                rule = self.conversion_rule(to_unit, from_unit)  # reverse conversion rule
                operator = Operator(get(rule, 'operator', None)).opposite
            if rule and operator.operation:
                value = eval(f"{tonnage} {operator.operation} {rule['conversion']}")  # pylint: disable=eval-used
        if value:
            return round(value, self.precision) if _round else value

    def conversion_rule(self, from_unit, to_unit=POUND, check_reverse=False):
        if self.unit_conversions:
            forward_rule = self.get_rule_for(from_unit=from_unit, to_unit=to_unit)
            if forward_rule:
                return forward_rule
            if check_reverse:
                return self.get_rule_for(from_unit=to_unit, to_unit=from_unit)
        return None

    def get_rule_for(self, from_unit, to_unit):
        return next(
            (rule for rule in self.unit_conversions if rule['to'] == to_unit and rule['from'] == from_unit), None)  # pylint: disable=not-an-iterable

    def to_lb(self, tonnage, _round=True):
        return self.convert_to(tonnage, self.unit, POUND, True)

    def to_bu(self, tonnage, _round=True):
        return self.convert_to(tonnage, self.unit, BUSHEL, _round)

    def to_st(self, tonnage, _round=True):
        return self.convert_to(tonnage, self.unit, SHORT_TONNE, _round)

    def to_mt(self, tonnage, _round=True):
        return self.convert_to(tonnage, self.unit, MT, _round)

    def to_cwt(self, tonnage, _round=True):
        return self.convert_to(tonnage, self.unit, CENTURY_TONNE, _round)

    @staticmethod
    def st_to_lb(tonnage, _round=False):
        value = tonnage / LB_TO_SHORT_TONNE_MULTIPLIER
        return round(value, Commodity.UNIT_PRECISION) if _round else value

    @staticmethod
    def mt_to_lb(tonnage, _round=False):
        value = tonnage / LB_TO_MT_MULTIPLIER
        return round(value, Commodity.UNIT_PRECISION) if _round else value

    @staticmethod
    def cwt_to_lb(tonnage, _round=False):
        value = tonnage / LB_TO_CWT_MULTIPLIER
        return round(value, Commodity.UNIT_PRECISION) if _round else value

    @staticmethod
    def lb_to_st(tonnage, _round=False):
        value = tonnage * LB_TO_SHORT_TONNE_MULTIPLIER
        return round(value, Commodity.UNIT_PRECISION) if _round else value

    @staticmethod
    def lb_to_mt(tonnage, _round=False):
        value = tonnage * LB_TO_MT_MULTIPLIER
        return round(value, Commodity.UNIT_PRECISION) if _round else value

    @staticmethod
    def lb_to_cwt(tonnage, _round=False):
        value = tonnage * LB_TO_CWT_MULTIPLIER
        return round(value, Commodity.UNIT_PRECISION) if _round else value

    def _to_unit_via_lb(self, tonnage, to_unit, multiplier, _round=True):
        if self.unit == to_unit:
            return tonnage
        lb = self.to_lb(tonnage, False)

        if lb is not None:
            value = lb * multiplier
            return round(value, self.precision) if _round else value

        return lb

    def get_unit_conversions(self, tonnage):
        country = self.country
        if not self.unit or not country.should_convert_unit:
            return {
                self.unit: tonnage
            }
        units = country.config['units']
        conversions = {
            BUSHEL: self.to_bu(tonnage),
            POUND: self.to_lb(tonnage),
            SHORT_TONNE: self.to_st(tonnage),
            CENTURY_TONNE: self.to_cwt(tonnage),
            MT: self.to_mt(tonnage)
        }
        return {
            unit: conversions[unit] for unit in units if unit in conversions
        }

    @property
    def unit_abbr(self):
        return get(UNIT_ABBREVIATIONS, self.unit, self.unit)

    @property
    def levies(self):
        return get(LEVY_MAP, self.id) or {}


class Variety(BaseModel, MasterDataMixin):
    class Meta:
        app_label = 'commodities'
        db_table = 'commodity_varieties'
        ordering = ['name']

    BARLEY_F_TYPES = [
        'BAR1', 'BAR2', 'BAR3', 'BFED1', 'BFED2', 'BFED3', 'BFDEC', 'BAR4', 'BARX',
        'Gradings - BARLEY', 'Waste - Barley'
    ]
    BARLEY_NON_F_TYPES = ['Malt1', 'Malt2', 'Malt3']

    name = models.CharField(
        max_length=50,
        null=False,
        blank=False
    )
    commodity = models.ForeignKey(
        Commodity,
        null=False,
        blank=False,
        on_delete=models.CASCADE,
    )
    details = models.JSONField(null=True, blank=True)
    mandatory_props = BaseModel.mandatory_props + ['epr']

    FILLABLES = ['name', 'commodity_id']

    @property
    def epr(self):
        try:
            return self.epr_set.to_dict()
        except Variety.epr_set.RelatedObjectDoesNotExist:
            pass

    @property
    def f_type(self):  # Delete after Barley Refactoring (3450287538) is released  # pragma: no cover
        if not self.details:
            return False
        return self.details.get('malt_1', None) in self.BARLEY_F_TYPES or \
            self.details.get('malt_2', None) in self.BARLEY_F_TYPES or \
            self.details.get('malt_3', None) in self.BARLEY_F_TYPES

    @classmethod
    def create_by_name_and_commodity(cls, name, commodity_id, details=None):
        variety = None
        if name and commodity_id:
            variety = Variety.create({'name': name, 'commodity_id': commodity_id, 'details': details})
            cache.clear()

        return variety

    @classmethod
    def find_by_name(cls, name, queryset=None):
        queryset = queryset or cls.objects
        name = strip_special(name)
        expression = CustomRegexWrapper('name').expression
        return queryset.annotate(_name=expression).filter(_name=name).first()


class Grade(BaseModel, MasterDataMixin):
    class Meta:
        app_label = 'commodities'
        db_table = 'commodity_grades'
        ordering = ['order']
        unique_together = ('name', 'commodity')

    name = models.CharField(
        max_length=500,
        null=False,
        blank=False
    )
    specs = ArrayField(models.JSONField(), null=True, blank=True, default=list)
    gta_code = models.CharField(max_length=50, null=True, blank=True)
    order = models.FloatField(null=False, blank=False)
    category = models.CharField(
        max_length=50,
        null=True,
        blank=True,
    )
    commodity = models.ForeignKey(
        Commodity, null=False, blank=False, on_delete=models.CASCADE, default=1
    )

    @classmethod
    def find_by_name(cls, name, queryset=None):
        queryset = queryset or cls.objects
        name = strip_special(name)
        expression = CustomRegexWrapper('name').expression
        return queryset.annotate(_name=expression).filter(_name=name).first()

    @classmethod
    def create_by_name_and_commodity(cls, name, commodity_id, specs=None, order=100):
        if not specs:
            specs = []
        grade = None
        if name and commodity_id:
            if not cls.objects.filter(name__iexact=name, commodity_id=commodity_id).exists():
                if cls.objects.filter(name__iexact=name).exists():
                    commodity = Commodity.objects.filter(id=commodity_id).first()
                    if commodity:
                        name = f"{commodity.display_name} - {name}"
            grade = cls.find_by_name(name)
            if not grade:
                grade = cls.create({'name': name, 'commodity_id': commodity_id, 'specs': specs, 'order': order})
                cache.clear()
        return grade


class EPR(BaseModel, MasterDataMixin):
    class Meta:
        app_label = 'commodities'
        db_table = 'variety_eprs'

    variety = models.OneToOneField(Variety, on_delete=models.CASCADE, related_name='epr_set')
    rate = models.FloatField()
    manager = models.CharField(
        max_length=255,
    )


# unsupported commodities
class Material(models.Model):
    class Meta:
        db_table = 'materials'

    name = models.CharField(max_length=100)
    material_class = models.CharField(max_length=100)
    updated_at = models.DateTimeField(auto_now=True)
    country = models.ForeignKey('countries.Country', on_delete=models.SET_DEFAULT, default=AUSTRALIA_COUNTRY_ID)

    @property
    def display_name_with_class(self):
        return f"{self.display_name} ({self.material_class})"

    @property
    def display_name(self):
        return self.name

    @property
    def entity(self):
        return self.__class__.__name__.lower()


class Operator:
    def __init__(self, operation):
        self.operation = operation
    @property
    def opposite(self):
        if self.operation == '*':
            return Operator('/')
        if self.operation == '/':
            return Operator('*')
        if self.operation == '+':
            return Operator('-')
        if self.operation == '-':
            return Operator('+')


class InarixScenario(BaseModel, MasterDataMixin):
    class Meta:
        db_table = 'inarix_scenarios'

    scenario_id = models.IntegerField()
    commodity = models.ForeignKey(Commodity, on_delete=models.CASCADE)
    purpose = models.CharField(max_length=100, null=True, blank=True)
    specs = ArrayField(models.JSONField(), null=True, blank=True, default=list)
