{% load static %}
{% load app_filters %}
{% with  date_time_format="M d Y, h:i A"  date_format="M d, Y" %}
<html style="-webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; font-family: sans-serif; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);">
    <head>
        <meta charset="UTF-8">
        <style>
            table tr {page-break-inside: avoid}
        </style>
    </head>
    <body style="-webkit-box-sizing: border-box; margin:0; -moz-box-sizing: border-box; box-sizing: border-box; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; line-height: 1.42857; color: #000000; background-color: #fff;">
        <div style="padding: 20px;">
            <span>Hi All,</span><br/>
            <h3>{{load.truck.rego}} Unloaded with the following details:</h3>
            {% with date_fmt="%b %d, %Y" time_fmt='%I:%M %p' tz=load.timezone_location %}
            {% with date_tz=date_fmt|add:"|"|add:tz time_tz=time_fmt|add:"|"|add:tz %}
            <table>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Date:</td>
                    <td style="width: 50%; font-size: 10pt;">{{load.date_time | localize_tz:date_tz}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Time In:</td>
                    {% if time_in %}
                        <td style="width: 50%; font-size: 10pt;">{{time_in | localize_tz:time_tz}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Load Start Time:</td>
                    <td style="width: 50%; font-size: 10pt;">{{load.date_time | localize_tz:time_tz}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Time Out:</td>
                    {% if load.completed_at %}
                        <td style="width: 50%; font-size: 10pt;">{{load.completed_at | localize_tz:time_tz}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Site Operator:</td>
                    {% if site_operator %}
                        <td style="width: 50%; font-size: 10pt;">{{site_operator}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Site:</td>
                    <td style="width: 50%; font-size: 10pt;">{{load.storage.farm.display_name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">{{'truck_rego' | to_country_label:load.country_code}}:</td>
                    <td style="width: 50%; font-size: 10pt;">{{load.rego_display}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Truck Driver:</td>
                    {% if slot %}
                        <td style="width: 50%; font-size: 10pt;">{{slot.driver.name}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Customer:</td>
                    <td style="width: 50%; font-size: 10pt;">{{contract.customer.company.name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Order:</td>
                    {% if contract.order %}
                        <td style="width: 50%; font-size: 10pt;">{{contract.order.identifier}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Delivery Order:</td>
                    {% if load.order_number %}
                        <td style="width: 50%; font-size: 10pt;">{{load.order_number}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Booking / Movement:</td>
                    <td style="width: 50%; font-size: 10pt;">{{contract.identifier}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Commodity:</td>
                    <td style="width: 50%; font-size: 10pt;">{{load.commodity.display_name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Ordered Grade:</td>
                    <td style="width: 50%; font-size: 10pt;">{{contract.planned_grade_name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Unloaded Grade:</td>
                    <td style="width: 50%; font-size: 10pt;">{{load.grade_name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">{{'tonnage' | to_country_label:load.country_code}}:</td>
                    <td style="width: 50%; font-size: 10pt;">{{load.total_net_weight}} {{load.truck.unit}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Comment:</td>
                    <td style="width: 50%; font-size: 10pt;">{{load.comment}}</td>
                </tr>
            </table>
            {% endwith %}
            {% endwith %}
        </div>
    </body>
</html>
{% endwith %}
