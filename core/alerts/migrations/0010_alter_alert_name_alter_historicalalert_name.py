# Generated by Django 4.1.7 on 2023-05-02 08:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0009_alter_alert_name_alter_historicalalert_name'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='alert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit'), ('inload_done', 'Inload Alert SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Information'), ('delivery_information', 'Delivery Information'), ('outload_report', 'Outload Report')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalalert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit'), ('inload_done', 'Inload Alert SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Information'), ('delivery_information', 'Delivery Information'), ('outload_report', 'Outload Report')], max_length=100),
        ),
    ]
