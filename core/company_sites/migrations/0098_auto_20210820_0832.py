# Generated by Django 3.2.6 on 2021-08-20 08:32

import core.company_sites.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0097_auto_20210709_0859'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='freightslot',
            name='extras',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='provider_updated_fields',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='siblings',
            field=models.JSONField(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='historicalfreightslot',
            name='extras',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalfreightslot',
            name='provider_updated_fields',
            field=models.JSO<PERSON>ield(blank=True, default=list, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='historicalfreightslot',
            name='siblings',
            field=models.JSO<PERSON>ield(blank=True, default=list, null=True),
        ),
        migrations.AlterField(
            model_name='sitemanagementsettings',
            name='fields',
            field=models.JSONField(default=core.company_sites.models.default_fields),
        ),
        migrations.AlterField(
            model_name='sitemanagementsettings',
            name='statuses',
            field=models.JSONField(default=core.company_sites.models.default_statuses),
        ),
        migrations.AlterField(
            model_name='sitemanagementsettings',
            name='stocks_csv_headers_info',
            field=models.JSONField(blank=True, null=True),
        ),
    ]
