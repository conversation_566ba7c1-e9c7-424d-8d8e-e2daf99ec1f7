# Generated by Django 3.2.6 on 2021-10-11 08:08

from django.db import migrations


def create_agrichain_site_management_settings(apps, schema_editor):
    SiteManagementSettings = apps.get_model('company_sites', 'SiteManagementSettings')
    SiteManagementSettings(company_id=1, minimum_tonnage=0.1).save()


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0099_auto_20210901_0845'),
    ]

    operations = [
        migrations.RunPython(create_agrichain_site_management_settings)
    ]
