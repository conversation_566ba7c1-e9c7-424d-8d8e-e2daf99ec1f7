{% load static %}
{% load app_filters %}
{% load tz %}
{% with date_time_format="d/m/Y, h:i A" date_format="d/m/Y" table_width_for_pdf="1240px" table_width="900px" slot=slots.first first_movement=movement.first%}

<html style="-webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; font-family: sans-serif; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);">
    <head>
        <meta charset="UTF-8">
        <style>
            table tr {page-break-inside: avoid}
        </style>
    </head>
    <body style="-webkit-box-sizing: border-box; margin:0; -moz-box-sizing: border-box; box-sizing: border-box; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; line-height: 1.42857; color: #000000; background-color: #fff;">
        <div style="padding: 20px;">
            <span>Hi all,</span><br/>
            {% if action == 'Update' %}
                <p>The details of a slot have changed at {{slot.site.company.name}}.</p>
                <p>Please find the relevant details below:</p>
                <table style="border-collapse: collapse; width: 50%">
                    <tr>
                        <th style="width: 30%; font-size: 11pt; font-weight: 700; color: #666666; margin: 0;padding: 8px; border: 1px solid black;"></th>
                        <th style="width: 30%; font-size: 11pt; font-weight: 700; color: #666666; margin: 0;padding: 8px; border: 1px solid black;">CURRENT</th>
                        <th style="width: 30%; font-size: 11pt; font-weight: 700; color: #666666; margin: 0;padding: 8px; border: 1px solid black;">AMENDED</th>
                    </tr>
                    {% for data in updated_fields %}
                        <tr>
                            <td style="width: 30%; font-size: 11pt; text-align: center;padding: 8px; border: 1px solid black;">{{data.field}}</td>
                            <td style="width: 30%; font-size: 11pt; text-align: center;padding: 8px; border: 1px solid black;">{{data.prev_value}}</td>
                            <td style="width: 30%; font-size: 11pt; text-align: center;padding: 8px; border: 1px solid black;">{{data.current_value}}</td>
                        </tr>
                    {% endfor %}
                </table>
                <br/>
            {% endif %}
            {% if action == 'Book' %}
                {% if slots.count == 1 %}
                    <p>A slot has been booked at the site {{slot.site.name}} belonging to {{slot.site.company.name}} by
                        {{ slot.updated_by.name }} ({{ slot.updated_by.company.name }})
                    </p>
                    <p>Please find the relevant details below:</p>
                {% else %}
                    <p>Slots have been booked at the site {{slot.site.name}} belonging to {{slot.site.company.name}} by
                        {{ slot.updated_by.name }} ({{ slot.updated_by.company.name }})
                    </p>
                    <p>Please find the relevant details below:</p>
                {% endif %}
            {% endif %}
            {% if action == 'Cancel' %}
                {% if slots.count == 1 %}
                    <p>A slot has been cancelled at {{slot.site.name}} belonging to {{slot.site.company.name}} by
                        {{ slot.updated_by.name }} ({{ slot.updated_by.company.name }})
                    </p>
                    <p>Please find the relevant details of the cancelled slot below.</p>
                {% else %}
                    <p>Slots have been cancelled at {{slot.site.name}} belonging to {{slot.site.company.name}} by
                        {{ slot.updated_by.name }} ({{ slot.updated_by.company.name }})
                    </p>
                    <p>Please find the relevant details of the cancelled slots below.</p>
                {% endif %}
            {% endif %}
            <table>
                <tr>
                    <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Site:</td>
                    <td style="width: 50%; font-size: 11pt;">{{slot.site.name}}</td>
                </tr>
                <tr>
                    <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Type:</td>
                    <td style="width: 50%; font-size: 11pt;">{{slot.type | capfirst}}</td>
                </tr>
                <tr>
                    <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Order Number:</td>
                    <td style="width: 50%; font-size: 11pt;">
                        {% if action != 'Cancel' %}
                            {{slot.order.identifier}}
                        {% else %}
                            {{order.identifier}}
                        {% endif %}

                    </td>
                </tr>
                <tr>
                    <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0; vertical-align: top;">Movement Number:</td>
                    <td style="width: 50%; font-size: 11pt; vertical-align: top;">
                        {% if action != 'Cancel' %}
                            {% if slots.count == 1 %}
                                {{ slot.movement.identifier}}
                            {% else %}
                              {% for slot in slots %}
                                {{ slot.movement.identifier }}{% if not forloop.last %},{% endif %}
                                <br/>
                              {% endfor %}
                            {% endif %}
                        {% else %}
                            {% if movement.count == 1 %}
                                {{ first_movement.identifier }}
                            {% else %}
                                {% for mov in movement %}
                                    {{ mov.identifier }}{% if not forloop.last %},{% endif %}
                                    <br/>
                                {% endfor %}
                            {% endif %}
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0; vertical-align: top;">Slot time:</td>
                    <td style="width: 50%; font-size: 11pt; vertical-align: top;">
                        {% for slot in slots %}
                            {{ slot.date_based_on_timezone }}, {{ slot.start_time_based_on_timezone }} - {{ slot.end_time_based_on_timezone }}
                            {% if not forloop.last %}, {% endif %}
                            <br/>
                        {% endfor %}
                    </td>
                </tr>
                <tr>
                    <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Commodity:</td>
                    <td style="width: 50%; font-size: 11pt;">
                        {% firstof slot.commodity.display_name order.commodity.display_name %}
                    </td>
                </tr>
                <tr>
                    {% if slot.grade or order.grade.name %}
                        <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Grade:</td>
                        <td style="width: 50%; font-size: 11pt;">
                            {% firstof slot.parent_grade_name slot.grade.name order.grade.name %}
                        </td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Variety:</td>
                    <td style="width: 50%; font-size: 11pt;">
                        {% firstof order.variety.name slot.order.variety.name %}
                    </td>
                </tr>
                <tr>
                    <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Season:</td>
                    <td style="width: 50%; font-size: 11pt;">
                        {% firstof slot.season order.season %}
                    </td>
                </tr>
                <tr>
                    {% if slot.tonnage %}
                        <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">{{'tonnage' | to_country_label:slot.country_code}}:</td>
                        <td style="width: 50%; font-size: 11pt;">
                                {% if slots.count == 1 %}
                                    {{slot.tonnage}} {{slot.inferred_tonnage_unit}}
                                {% else %}
                                    {{slot.tonnage}} {{slot.inferred_tonnage_unit}} per slot
                                {% endif %}
                        </td>
                    {% elif first_movement.tonnage %}
                        <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">{{'tonnage' | to_country_label:movement.country_code}}:</td>
                        <td style="width: 50%; font-size: 11pt;">
                            {% if movement.count == 1 %}
                                {{first_movement.tonnage}} {{first_movement.inferred_tonnage_unit}}
                            {% else %}
                                {{first_movement.tonnage}} {{first_movement.inferred_tonnage_unit}} per slot
                            {% endif %}
                        </td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Freight Provider:</td>
                    <td style="width: 50%; font-size: 11pt;">
                        {% firstof slot.freight_provider.name  first_movement.provider.name %}
                    </td>
                </tr>
                {% if slot.sub_freight_provider %}
                    <tr>
                        <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Sub Freight Provider:</td>
                        <td style="width: 50%; font-size: 11pt;">{{slot.sub_freight_provider.name}}</td>
                    </tr>
                {% elif first_movement.provider_id and first_movement.planned_truck.company_id and first_movement.planned_truck.company_id != first_movement.provider_id %}
                    <tr>
                        <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Sub Freight Provider:</td>
                        <td style="width: 50%; font-size: 11pt;">{{first_movement.planned_truck.company.name}}</td>
                    </tr>
                {% endif %}
                <tr>
                    {% if slot.truck %}
                        <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Truck:</td>
                        <td style="width: 50%; font-size: 11pt;">{{slot.truck.rego}}</td>
                    {% endif %}
                </tr>
                <tr>
                    {% if slot.driver or first_movement.assign_to %}
                        <td style="width: 20%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Driver:</td>
                        <td style="width: 50%; font-size: 11pt;">{% firstof slot.driver.name first_movement.assign_to.name %}</td>
                    {% endif %}
                </tr>
            </table>
        </div>
        <br/>
            Have a great day!
        <br/>
            Thank you.
        <br/>
    </body>
</html>
{% endwith %}
