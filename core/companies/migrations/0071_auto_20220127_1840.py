# Generated by Django 3.2.6 on 2022-01-27 18:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0070_platformfeatures_freight_scheduler'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='company',
            name='charged_at_outload',
        ),
        migrations.RemoveField(
            model_name='historicalcompany',
            name='charged_at_outload',
        ),
        migrations.AddField(
            model_name='company',
            name='charged_at',
            field=models.CharField(choices=[('inload', 'Inload'), ('outload', 'Outload'), ('outload/transfer', 'Outload/Transfer(whichever earlier)')], default='outload', max_length=32),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='charged_at',
            field=models.CharField(choices=[('inload', 'Inload'), ('outload', 'Outload'), ('outload/transfer', 'Outload/Transfer(whichever earlier)')], default='outload', max_length=32),
        ),
    ]
