# Generated by Django 2.1.9 on 2019-06-18 07:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('companies', '0033_auto_20190611_0748'),
        ('conditions', '0008_auto_20190125_0452'),
    ]

    operations = [
        migrations.CreateModel(
            name='Condition',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(max_length=100)),
                ('details', models.TextField()),
                ('module', models.CharField(choices=[('contract', 'contract'), ('freight', 'freight'), ('invoice', 'invoice')], max_length=20)),
                ('type', models.CharField(choices=[('general', 'General'), ('special', 'Special')], max_length=20)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.Company')),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='conditions_condition_related_created_by', related_query_name='conditions_conditions_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='conditions_condition_related_updated_by', related_query_name='conditions_conditions_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'conditions',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='HistoricalCondition',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('name', models.CharField(max_length=100)),
                ('details', models.TextField()),
                ('module', models.CharField(choices=[('contract', 'contract'), ('freight', 'freight'), ('invoice', 'invoice')], max_length=20)),
                ('type', models.CharField(choices=[('general', 'General'), ('special', 'Special')], max_length=20)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('company', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.Company')),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='conditions_conditions_created_by', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='conditions_conditions_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical condition',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
