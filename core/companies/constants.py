
from core.devices.constants import NOTIFICATION_TYPE_CONTRACT
from core.common.utils import exclude_country_hidden_fields_in_csv


PREMIUM_PLAN = 'premium'
LITE_PLAN = 'lite'
LOGISTICS_LITE_PLAN = 'logistics_lite'
STORAGE = 'storage'
LOGISTICS = 'logistics'

PLAN_TYPE_CHOICES = (
    ('premium', 'Premium'),
    ('logistics_lite', 'Logistics Lite')
)

FEES = 'fees'
CONTRACT_BIDS = 'contract_bids'

BOOLEAN_ASSETS = ['trucks', 'ngrs', 'sites']
FARM_COUNT = 'number_of_farms'

SINGLE_FARM = 1
CONDITIONS = 'conditions'

SIMILARITY_SEARCH_SCORE = 0.25
DEFAULT_START_OF_WEEK = 1
DEFAULT_END_OF_WEEK = 5
DEFAULT_REFERENCE_NUMBER_LENGTH = 9
DEFAULT_REFERENCE_NUMBER_START = 1
TRANSACTION_TYPE_MAPPER = {
    NOTIFICATION_TYPE_CONTRACT: 'contract',
}

COMPANY_CLAIM_SLACK_MESSAGE = (
    "A new {status} company claim request has been received with the following details:\n"
    "*Environment*: {env}\n"
    "*Claimant Name*: {name}\n"
    "*Claimant Mobile*: {mobile}\n"
    "*Claimant Email*: {email}\n"
    "*Claimant Username*: {username}\n"
    "*Claimed Company ABN*: {abn}\n"
    "*Claimed Company*: {business_name}\n"
    "*Company Claim Details*: {claim_details}\n"
)

NEW_UNREG_COMPANY_SLACK_MESSAGE = (
    "A new user for unregistered company has registered on the system using {form_type} sign up:\n"
    "*Environment*: {env}\n"
    "*Name*: {name}\n"
    "*Mobile*: {mobile}\n"
    "*Email*: {email}\n"
    "*Username*: {username}\n"
    "*Company*: {business_name}\n"
    "*Abn*: {abn}\n"
    "*Country*: {country}\n"
    "*Company Type*: {business_type}\n"
    "---------------------------------------------------------"
)

OLD_UNREG_COMPANY_SLACK_MESSAGE = (
    "An existing user for unregistered company has registered on the system using {form_type} sign up:\n"
    "*Environment*: {env}\n"
    "*Name*: {name}\n"
    "*Mobile*: {mobile}\n"
    "*Email*: {email}\n"
    "*Username*: {username}\n"
    "*Company*: {business_name}\n"
    "*Abn*: {abn}\n"
    "*Country*: {country}\n"
    "*Company Type*: {business_type}\n"
    "---------------------------------------------------------"
)

CANNOT_CREATE_EMPLOYEE_FOR_REGISTERED_COMPANY_REASON = 'The selected party is registered on the system'
CANNOT_CREATE_EMPLOYEE_FOR_SUBSCRIBER_COMPANY_REASON = 'The selected party is a subscriber on the system'
CANNOT_CREATE_COMPANY_EMPLOYEE_BASED_ON_ROLE_REASON = 'Only {roles} can create'

CANNOT_CREATE_NGR_FOR_REGISTERED_COMPANY_REASON = 'The selected party is registered on the system'
CANNOT_CREATE_NGR_FOR_SUBSCRIBER_COMPANY_REASON = 'The selected party is a subscriber on the system'
CANNOT_CREATE_COMPANY_NGR_BASED_ON_ROLE_REASON = 'Only {roles} can create'


def get_csv_headers(country):
    headers = [
        "Business Name",
        "Entity Name",
        "ABN",
        "Business Type",
        "Registered",
        "Address",
        "Company Phone/Mobile",
        "Key Contact",
        "Key Contact Email",
        "Key Contact Mobile",
        "Payment Term",
        "Created On"
    ]
    return exclude_country_hidden_fields_in_csv(country, headers)


def get_site_loads_csv(country):
    rego_label = country.get_label('rego')
    headers = [
        'Date',
        'Time',
        'Type',
        'Sub Type',
        'Contract',
        'Order',
        'Identifier',
        'Location',
        'Storage',
        'Stock Owner',
        'NGR',
        'Freight Provider',
        '{}'.format(rego_label),
        'Commodity',
        'Sustainable',
        'Grade',
        'Season',
        'Variety',
        'Quantity',
        'Quantity with shrinkage',
        'Docket',
        'Creator',
        'Comment',
        'SP1 name',
        'SP1 value',
        'SP2 name',
        'SP2 value',
        'SP3 name',
        'SP3 value',
        'SP4 name',
        'SP4 value',
        'SP5 name',
        'SP5 value',
        'SP6 name',
        'SP6 value',
    ]
    return exclude_country_hidden_fields_in_csv(country, headers)


WAREHOUSE_INLOAD_FEES_CHARGED_AT = (
    ('inload', 'Inload'),
    ('outload', 'Outload'),
    ('outload/transfer', 'Outload/Transfer(whichever earlier)'),
    ('outload_or_transfer_until_date', 'Outload/Transfer until a specified date'),
)

NEW_COMPANY_SLACK_MESSAGE = (
    "Company Created:\n"
    "Environment: {env} \n"
    "Name: {company_name}\n"
    "ABN: {company_abn}\n"
    "Type: {company_type}\n"
    "Registered: {registered}\n"
    "Subscriber: {subscriber}\n"
    "Created By: {created_by}\n"
    "Created By Company: {created_by_company}\n"
    "------------------------------------------------------------"
)

SITES = 'sites'
NGR = 'ngr'
STORAGES = 'storages'
EMPLOYEES = 'employees'
COMMODITY = 'commodity'

COMMON_ADMIN_CSV_HEADERS = [
    'Company Name',
    'Company ID',
]

ADMIN_SITES_CSV_HEADERS = [
    *COMMON_ADMIN_CSV_HEADERS,
    'Company Site Name',
    'Company Site ID',
    'Address',
    'Market Zone',
    'Region',
]

ADMIN_NGR_CSV_HEADERS = [
    *COMMON_ADMIN_CSV_HEADERS,
    'NGR Number',
    'NGR ID',
]

ADMIN_STORAGE_CSV_HEADERS = [
    *COMMON_ADMIN_CSV_HEADERS,
    'Farm/Site Name',
    'Farm/Site ID',
    'Storage Name',
    'Storage ID',
    'Type',
    'Size',
]

ADMIN_EMPLOYEE_CSV_HEADERS = [
    *COMMON_ADMIN_CSV_HEADERS,
    'Employee Name',
    'Employee ID',
    'Employee Role',
    'Job Title',
    'Mobile',
    'Email',
]

ADMIN_COMMODITY_CSV_HEADERS = [
    'Commodity ID',
    'Commodity Name',
    'Material Class',
    'Unit'
]
ADMIN_GRADE_CSV_HEADERS = [
    'Grade ID',
    'Grade Name',
    'Commodity ID',
    'Commodity Name',
]
ADMIN_VARIETY_CSV_HEADERS = [
    'Variety ID',
    'Variety Name',
    'Commodity ID',
    'Commodity Name',
]

VITERRA = 'viterra'
ALLIED = 'allied'

THROUGH_WAREHOUSE = 'through_warehouse'
DIRECT_TO_BUYER = 'direct_to_buyer'
STOCK = 'stock'

THROUGH_WAREHOUSE_DISPLAY_NAME = 'Through Warehouse'
DIRECT_TO_BUYER_DISPLAY_NAME = 'Direct To Buyer'

CONTRACT_ALLOCATION_TYPES = (
    (THROUGH_WAREHOUSE, THROUGH_WAREHOUSE_DISPLAY_NAME),
    (DIRECT_TO_BUYER, DIRECT_TO_BUYER_DISPLAY_NAME),
    (STOCK, 'Stock'),
)
OTHERS_CAN_CREATE_WITHOUT_ACCEPTANCE = 'others_can_create_without_acceptance'
WAREHOUSE_INVOICE_WEEKLY_FREQUENCY = 'weekly'
WAREHOUSE_INVOICE_MONTHLY_FREQUENCY = 'monthly'
WAREHOUSE_INVOICE_FREQUENCIES = (
    (WAREHOUSE_INVOICE_WEEKLY_FREQUENCY, 'Weekly'),
    (WAREHOUSE_INVOICE_MONTHLY_FREQUENCY, 'Monthly')
)


ABN_STATUS_TYPES = (
    ('active', 'Active'),
    ('cancelled', 'Cancelled'),
)
