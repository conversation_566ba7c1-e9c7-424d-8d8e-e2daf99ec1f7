# Generated by Django 2.1.9 on 2019-09-17 04:42

from django.db import migrations

def update_canola_varieties(apps, schema_editor):
    Load = apps.get_model('loads', 'Load')
    FreightOrder = apps.get_model('freights', 'FreightOrder')
    FreightContract = apps.get_model('freights', 'FreightContract')
    HomeStorage = apps.get_model('farms', 'HomeStorage')
    Contract = apps.get_model('contracts', 'Contract')

    updates = {515: 322, 516: 323, 517: 324, 518: 325}
    for old, new in updates.items():
        for klass in [Load, FreightOrder, FreightContract, HomeStorage, Contract]:
            klass.objects.filter(variety_id=old).update(variety_id=new)

class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0019_auto_20190509_0711'),
    ]

    operations = [
        migrations.RunPython(update_canola_varieties),
        migrations.RunSQL(
            'DELETE from commodity_varieties where id in (515, 516, 517, 518)'
        )
    ]
