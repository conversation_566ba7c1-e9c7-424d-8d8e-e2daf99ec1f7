# Generated by Django 5.1.8 on 2025-07-04 01:36

import logging
from django.db import migrations

from core.alerts.constants import (
    PICKUP_CONFIRMATION_ALERT_CODE,
    DELIVERY_CONFIRMATION_ALERT_CODE,
    SLOT_BOOKING_UPDATE_ALERT_CODE
)
from core.alerts.constants import RECEIVE_ALERT_ROLES

def create_employee_alert_data(apps, schema_editor):
    logger = logging.getLogger(__name__)
    logger.info("Starting data migration for employee alerts...")

    ReceiveAlert = apps.get_model('alerts', 'ReceiveAlert')
    Employee = apps.get_model('profiles', 'Employee')
    EmployeeAlert = apps.get_model('alerts', 'EmployeeAlert')

    BATCH_SIZE = 1000
    total_employees_fetched = 0

    default_config = {
        'is_active': True,
        'include_internal_sites': True,
        'include_external_sites': True,
    }

    receive_alerts = list(
        ReceiveAlert.objects.filter(name__in = [
            PICKUP_CONFIRMATION_ALERT_CODE,
            DELIVERY_CONFIRMATION_ALERT_CODE,
            SLOT_BOOKING_UPDATE_ALERT_CODE
        ]
    ))

    alerts_to_create = []

    employee_offset = 0
    while True:
        start = employee_offset
        end = employee_offset + BATCH_SIZE
        employee_ids = list(Employee.objects.values_list('id', flat=True)[start: end])
        if not employee_ids:
            break

        total_employees_fetched += len(employee_ids)
        logger.info(f"Fetched batch of {len(employee_ids)} employees (offset: {employee_offset}, total processed: {total_employees_fetched})")

        for employee_id in employee_ids:
            for receive_alert in receive_alerts:
                roles = RECEIVE_ALERT_ROLES.get(receive_alert.name)
                employee_receive_alert = EmployeeAlert(
                    employee_id=employee_id,
                    alert=receive_alert,
                    roles=roles,
                    **default_config
                )
                alerts_to_create.append(employee_receive_alert)

        employee_offset += BATCH_SIZE

    if alerts_to_create:
        logger.info(f"Creating {len(alerts_to_create)} employee alerts in bulk...")
        EmployeeAlert.objects.bulk_create(
            alerts_to_create,
            batch_size=1000,
            ignore_conflicts=True
        )
        logger.info(f"Successfully created {len(alerts_to_create)} employee alerts")

    logger.info(f"Data migration completed. Total employees processed: {total_employees_fetched}")

class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0034_receive_alert_data_migration'),
    ]

    operations = [
        migrations.RunPython(create_employee_alert_data, migrations.RunPython.noop),
    ]
