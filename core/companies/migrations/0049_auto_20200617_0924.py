# Generated by Django 2.2 on 2020-06-17 09:24

from django.db import migrations


def update_stocks_for_all_companies(apps, schema_editor):
    PlatformFeatures = apps.get_model('companies', 'PlatformFeatures')
    PlatformFeatures.objects.exclude(plan_type='logistics_lite').update(stocks=True)


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0048_auto_20200611_0311'),
    ]

    operations = [
        migrations.RunPython(update_stocks_for_all_companies)
    ]
