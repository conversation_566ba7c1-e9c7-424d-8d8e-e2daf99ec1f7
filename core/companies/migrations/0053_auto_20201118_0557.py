# Generated by Django 2.2.13 on 2020-11-18 05:57

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0052_auto_20201006_0431'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='report_recipients',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.EmailField(max_length=255), blank=True, null=True, size=None),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='report_recipients',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.EmailField(max_length=255), blank=True, null=True, size=None),
        ),
    ]
