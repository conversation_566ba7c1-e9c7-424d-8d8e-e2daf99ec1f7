# Generated by Django 2.1.5 on 2019-02-06 02:54

import core.common.models
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        ('trucks', '0006_auto_20190125_0452'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('company_sites', '0015_auto_20190128_1113'),
    ]

    operations = [
        migrations.CreateModel(
            name='FreightSlot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('type', models.CharField(choices=[('inload', 'Inload'), ('outload', 'Outload')], max_length=7)),
                ('start', models.DateTimeField()),
                ('end', models.DateTimeField()),
                ('booking_number', models.CharField(max_length=50)),
                ('delivery_order_number', models.CharField(blank=True, max_length=50, null=True)),
                ('status', models.CharField(choices=[('planned', 'Planned'), ('booked', 'Booked'), ('in_progress', 'In Progress'), ('delayed', 'Delayed'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='planned', max_length=50)),
                ('tonnage', core.common.models.RoundedDecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('mill_priority', models.BooleanField(default=False)),
                ('commodity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='commodities.Commodity')),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='company_sites_freightslot_related_created_by', related_query_name='company_sites_freightslots_created_by', to=settings.AUTH_USER_MODEL)),
                ('driver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('grade', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='commodities.Grade')),
                ('site', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='company_sites.CompanySite')),
                ('truck', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='trucks.Truck')),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='company_sites_freightslot_related_updated_by', related_query_name='company_sites_freightslots_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'freight_slots',
            },
            bases=(models.Model, core.common.models.InstanceCustomFuncMixin),
        ),
        migrations.CreateModel(
            name='HistoricalFreightSlot',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('type', models.CharField(choices=[('inload', 'Inload'), ('outload', 'Outload')], max_length=7)),
                ('start', models.DateTimeField()),
                ('end', models.DateTimeField()),
                ('booking_number', models.CharField(max_length=50)),
                ('delivery_order_number', models.CharField(blank=True, max_length=50, null=True)),
                ('status', models.CharField(choices=[('planned', 'Planned'), ('booked', 'Booked'), ('in_progress', 'In Progress'), ('delayed', 'Delayed'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='planned', max_length=50)),
                ('tonnage', core.common.models.RoundedDecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('mill_priority', models.BooleanField(default=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_date', models.DateTimeField()),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('commodity', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='commodities.Commodity')),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('driver', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('grade', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='commodities.Grade')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('site', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='company_sites.CompanySite')),
                ('truck', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='trucks.Truck')),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical freight slot',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
