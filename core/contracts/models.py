import math
import re
import sys
from copy import deepcopy
from datetime import datetime, timedelta, date, time

import django
import inflect
import inflection
from django.apps import apps
from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeign<PERSON><PERSON>, GenericRelation
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.fields import <PERSON>rray<PERSON>ield
from django.contrib.postgres.indexes import GinIndex
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator
from django.db import models
from django.db import transaction, IntegrityError
from django.db.models import Case, When, F, Prefetch, Sum
from django.db.models import Q
from django.db.models.expressions import RawSQL
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.functional import cached_property
from django_fsm import FSMField, transition, can_proceed
from memoize import memoize
from notifications.signals import notify
from pydash import get, compact, head

import core.common.constants
from core.commodities.constants import UNIT_ABBREVIATIONS
from core.common.constants import METER_CUBE, EPR_ITEM_DB_MODEL, CONSIGNOR_ROLE, CONSIGNEE_ROLE
from core.common.constants import (WHEAT_COMMODITY_ID, CONTRACT_MODEL, MEMOMIZE_TIMEOUT, TITLE_TRANSFER_MODEL,
                                   GRAIN_LEVY_ITEM_MODEL,
                                   FREIGHT_MOVEMENT_MODEL, EPR_ITEM_MODEL, CARRY_ITEM_MODEL, BALES, MODULES, MT, KG,
                                   LOAD_MODEL, SYSTEM_COMPANY_IDS, OUTLOAD, CURRENCIES, LITRE, INLOAD)
from core.common.exceptions import ErrorException
from core.common.models import (BaseModel, MasterDataMixin,
                                InstanceCustomFuncMixin, HierarchyObject, BaseCommunication, RoundedFloatField,
                                RawModel)
from core.common.templatetags.app_filters import format_price
from core.common.utils import (to_display_attr, to_query_filters,
                               to_fsm_field_filters,
                               deepgetattr, flatten, get_grade_name, remove_pagination_query_params,
                               get_grade_display_name, update_with_last_modified_time,
                               multiply_args, add_args, format_number, to_currency,
                               generate_identifier, get_current_user, get_content_type_id)
from core.companies.constants import DEFAULT_START_OF_WEEK, DEFAULT_END_OF_WEEK, OTHERS_CAN_CREATE_WITHOUT_ACCEPTANCE
from core.companies.models import Company
from core.contracts import constants
from core.contracts.constants import (CONTRACT_ACCEPTANCE_REQUEST_FOR_CONSIDERATION, MANUALLY_COMPLETEABLE_STATUSES,
                                      MOVEMENTS_NOT_DELIVERED, BUYER_REGISTERED, COMMODITY_CONTRACTS_SLACK_MESSAGE,
                                      CONSIGNOR_MANDATORY_PRICE_POINTS, CONSIGNOR_OPTIONAL_PRICE_POINTS,
                                      CONSIGNEE_MANDATORY_PRICE_POINTS, CONSIGNEE_OPTIONAL_PRICE_POINTS,
                                      CARRY_ITEM_DESCRIPTION, FREQUENCY_TO_UNIT_MAP,
                                      CONTRACT_COUNTER_PARTY_DISPLAY_NAME, STATUS_COMPLETED,
                                      TITLE_TRANSFER_APPROVED_BUYER_ERROR, TITLE_TRANSFER_AVAILABLE_TONNAGE_ERROR,
                                      TITLE_TRANSFER_BUYER_SELLER_NGR_CANNOT_BE_SAME, TITLE_TRANSFER_CREATOR_ERROR,
                                      TITLE_TRANSFER_SELLER_NGR_ERROR, COMMODITY_CONTRACT_INVOICE,
                                      CASHED_TT_MOBILE_NOTIFICATION_TITLE, CASHED_TT_MOBILE_NOTIFICATION_TEXT,
                                      TT_MOBILE_NOTIFICATION_TITLE, TT_MOBILE_NOTIFICATION_TEXT, DISTANCE_FOR_LEVY,
                                      PAYMENT_SCALE_FLAT, BROKER_TO_INVOICE_BUYER, SELLER_TO_INVOICE_BUYER,
                                      CONTRACT_TYPE_SPOT_ID, COMPLETED,
                                      CANNOT_VOID_EXTERNALLY_SYNCED_SITE_TITLE_TRANSFER,
                                      TITLE_TRANSFER_DUPLICATE_EXTERNAL_REFERENCE_ERROR, CONTRACT_TONNAGE_AMEND_AUDIT,
                                      CONTRACT_TYPE_BLENDED_ID, OPEN, COMPANY_TYPE_DOCUMENT_TYPE_MAPPING,
                                      CONTRACT_DOCUMENT_TYPE_ID, DELIVERY_ONUS_SELLER, DELIVERY_ONUS_BUYER,
                                      BROKER_NOTE_DOCUMENT_TYPE_ID, SALES_CONFIRMATION_DOCUMENT_TYPE_ID,
                                      PAYMENT_SCALE_CONFIG, PRICE_ADJUSTMENT_FACTOR, DEFAULT_ADMIX_BASE,
                                      DEFAULT_OIL_MIN_BASE, CONTRACT_TYPE_SPOT, NIL_BROKERAGES,
                                      BROKER_NOTE_DOCUMENT_TYPE, CONTRACT_DOCUMENT_TYPE)
from core.contracts.constants import (RECIPIENTS_SELF_KEY, RECIPIENTS_COUNTER_PARTY_KEY,
                                      RECIPIENTS_CONTACT_KEY, RECIPIENTS_ADMIN_KEY, REQUEST_NOTIFICATION_MESSAGE_MAP,
                                      RECIPIENTS_BROKER_KEY, PRICE_KEY, TONNAGE_KEY,
                                      FEE_PER_MT_BROKERAGE, PERCENT_OF_SALE_BROKERAGE, BROKERAGE_TYPE_SELLER,
                                      BROKERAGE_TYPE_BUYER, USER_CONVEYANCES,
                                      CONTRACT_ACCEPTANCE_REQUEST_TYPE_TITLE_TRANSFER,
                                      PROCESSED, CONTRACT_ACCEPTANCE_REQUEST_TYPE_VOID, FREQUENCY_TO_PERIOD_MAP,
                                      CONTRACT_DISPLAY_NAME, CANOLA_COMMODITY_ID, BUYER_RCTI_INVOICING,
                                      POOL_CONTRACT_TYPE_IDS,
                                      CONTRACT_ACCEPTANCE_REQUEST_TYPE_AMEND, PRICE_POINTS_DELIVERED_BUYER_ID,
                                      CONTRACT_ACCEPTANCE_REQUEST_TYPE_CONFIRM, PRICE_POINTS_EX_FARM_ID, INVOICED,
                                      RECIPIENTS_OFFICE_ADMIN_KEY, WARNING_CREATE_FREIGHT_ORDER_DRAFT,
                                      WARNING_CREATE_FREIGHT_CONTRACT_DRAFT,
                                      WARNING_CREATE_TITLE_TRANSFER_DRAFT, PLANNED, NULL_FREIGHT_STATUSES,
                                      PERIOD_TYPE_WEEKS,
                                      PERIOD_TYPE_MONTHS,
                                      PERIOD_TYPE_DAYS, PERMISSION_CHANGE_ERROR, PRICE_POINTS_FREE_ON_TRUCK_ID,
                                      PRICE_POINTS_FREE_IN_STORE_ID,
                                      PRICE_POINTS_DELIVERED_SITE_ID, PRICE_POINTS_DELIVERED_DCT_ID,
                                      AREA_CONTRACT_TYPE_ID,
                                      NOT_ASSIGNABLE_CONTRACT_STATUSES, CANNOT_CREATE_CHILD_REASON_STATUS,
                                      MOVEMENTS_MISSING_DETAILS,
                                      FREIGHT_CONTRACT_INCOMPLETE_STATUSES, DELIVERED, CONTRACT_NON_EDITABLE_STATUSES,
                                      CARRY_CURRENT_KEY, CARRY_MAX_KEY, ATTACHMENT, END_OF_DELIVERY, PRE_DELIVERY,
                                      POST_DELIVERY,
                                      SPREAD_CONTRACT_TYPES, MONTHLY, PRICE_POINTS_TRACK_ID,
                                      PRICE_POINTS_DELIVERED_MARKET_ZONE_ID, PERIOD_TYPE_DATE)
from core.contracts.interfaces import TitleTransferAudit
from core.contracts.signals import (
    contract_created, contract_rejected, contract_accepted, contract_void_requested,
    contract_void_request_accepted, contract_void_request_rejected,
    title_transfer_processed, contract_in_progress, title_transfer_amend,
    contract_amend_requested, contract_amend_rejected, contract_delivered,
    contract_completed, contract_delayed, contract_amend_accepted, contract_paid,
    title_transfer_void, contract_vendor_declaration_requested, contract_closeout,
    contract_sent
)
from core.countries.models import Country
from core.devices.constants import (NOTIFICATION_TYPE_CONTRACT, NOTIFICATION_TYPE_CONTRACT_ID,
                                    NOTIFICATION_TYPE_LOAD_CASHED_ID, NOTIFICATION_TYPE_TT_ID)
from core.devices.models import MobilePushNotification
from core.farms.models import Farm, Storage, Shrinkage, WarehouseFees
from core.freights.constants import (NULL_STATUSES, DRAFT_STATUS, REQUEST_RESOLVER, RECEIVE_INDEX, CONFIRM_REQUEST_TYPE,
                                     FREIGHT_DELIVERY, FREIGHT_PICKUP, INCOMPLETE_FREIGHT_ORDER_STATUSES,
                                     COMPLETED_STATUS, DELIVERED_STATUS, IN_PROGRESS_STATUS, PLANNED_STATUS,
                                     REJECTED_STATUS, VOID_STATUS, PAID_STATUS, LOAD_SELECTED_IN_LOAD_BY_LOAD_TRANSFER,
                                     CONFIRMED_STATUS, PICKUP_ORDER_TYPE_ID, MANUAL_CONTRACT_COMPLETE_BALANCED,
                                     ALREADY_CLOSED_ORDER, ALREADY_CLOSED_CONTRACT, CONTRACT_HAS_NO_DELIVERED_TONNAGE,
                                     OPEN_STATUS, CANNOT_CLOSE_OUT_CONTRACT_DUE_TO_MOVEMENT,
                                     CONTRACT_CLOSE_OUT_ACTION_NOT_PERMITTED,
                                     CONTRACT_CLOSE_OUT_NOT_PERMITTED_FOR_ALLOCATED_CONTRACT,
                                     CANNOT_CLOSE_OUT_CONTRACT_DUE_TO_PENDING_REQUEST_ON_MOVEMENT, PACK_ORDER_TYPE_ID)
from core.invoices.constants import (INVOICE_ACCEPTED_STATUSES, INVOICE_NULL_STATUSES, GRAIN_LEVY_TYPES,
                                     STATE_BASED_LEVIES)
from core.ngrs.models import Ngr
from core.notes.models import Note
from core.profiles.models import Employee
from core.services.external.aws import S3
from core.services.internal.pdf import HTMLToPDF
from core.services.internal.slack import Slack
from core.settings import WEB_URL
from core.timezones.utils import DateTimeUtil
from core.toggles.models import Toggle
from core.validation.validators import season_validator


class ContractDocumentType(BaseModel, MasterDataMixin):
    class Meta:
        app_label = 'contracts'
        db_table = 'contract_document_types'
        ordering = ['id']

    mandatory_props = BaseModel.mandatory_props + ['display_name']

    name = models.CharField(max_length=100, choices=constants.DOCUMENT_TYPES,)
    company_type = models.ForeignKey('companies.CompanyType', on_delete=models.CASCADE, null=True, blank=True)

    @property
    def display_name(self):
        return to_display_attr(constants.DOCUMENT_TYPES, self.name)

    @property
    def is_contract(self):
        return self.name.lower() == constants.CONTRACT_DOCUMENT_TYPE

    @classmethod
    def search_by_company_type(cls, company_type_id):
        document_type_ids = COMPANY_TYPE_DOCUMENT_TYPE_MAPPING.get(int(company_type_id), [])
        if not document_type_ids:
            return []

        queryset = cls.objects.filter(id__in=document_type_ids)
        return cls.qs2dict(queryset=queryset)

class PaymentTerm(BaseModel, MasterDataMixin):
    class Meta:
        app_label = 'contracts'
        db_table = 'payment_terms'
        ordering = ['id']

    name = models.CharField(
        max_length=100,
        choices=constants.PAYMENT_TERMS,
        null=False,
        blank=False,
    )
    start_from = models.CharField(
        max_length=20,
        choices=constants.PAYMENT_START_FROMS,
        null=False,
        blank=False,
    )
    period_type = models.CharField(
        max_length=10,
        choices=constants.PAYMENT_PERIODS,
        null=False,
        blank=False,
    )
    period = models.IntegerField(
        null=False,
        blank=False,
        validators=[MinValueValidator(0)],
    )

    def __estimated_due_date_days(self, start_date, start_of_week, end_of_week):
        due_date = None
        if 'Business Days' in self.name:  # pylint: disable=unsupported-membership-test
            start_day = start_date.weekday()
            weekdays = DateTimeUtil.get_week_days(start_of_week, end_of_week)
            if start_day or start_day == 0:
                days = DateTimeUtil.get_days_to_add(start_day, self.period, weekdays)
                due_date = start_date + timedelta(days=days)
        else:
            due_date = start_date + timedelta(days=self.period)
        return due_date

    def __estimated_due_date_weeks(self, start_date, end_of_week):
        due_date = None
        if start_date and end_of_week is not None:
            next_end_date = DateTimeUtil.next_end_day_from(start_date, end_of_week)
            due_date = next_end_date + timedelta(days=self.period) if next_end_date else None
        return due_date

    def __estimated_due_date_months(self, start_date):
        due_date = None
        if start_date:
            last_day = DateTimeUtil.get_last_day_of_month(start_date)
            due_date = date(start_date.year, start_date.month, last_day) + timedelta(days=self.period)
        return due_date

    def get_estimated_due_date_from(
            self, start_date, start_of_week=DEFAULT_START_OF_WEEK, end_of_week=DEFAULT_END_OF_WEEK,
            default_today_for_unknown=False):
        if not start_date:
            return None
        if self.period_type == PERIOD_TYPE_WEEKS:
            end_of_week = 0
            payment_due_date = self.__estimated_due_date_weeks(start_date, end_of_week)
        elif self.period_type == PERIOD_TYPE_MONTHS:
            payment_due_date = self.__estimated_due_date_months(start_date)
        elif self.period_type == PERIOD_TYPE_DAYS:
            payment_due_date = self.__estimated_due_date_days(start_date, start_of_week, end_of_week)
        elif self.period_type == PERIOD_TYPE_DATE:
            day = start_date.day
            if day <= self.period:
                payment_due_date = DateTimeUtil.get_last_date_of_month(start_date)
            else:
                payment_due_date = DateTimeUtil.get_next_month_date(start_date, self.period)
        else:
            payment_due_date = timezone.now() if default_today_for_unknown else None
        return payment_due_date


class PaymentScale(BaseModel, MasterDataMixin):
    class Meta:
        app_label = 'contracts'
        db_table = 'payment_scales'
        ordering = ['ordering_id']

    name = models.CharField(
        max_length=100,
        choices=constants.PAYMENT_SCALES,
        null=False,
        blank=False,
    )
    ordering_id = models.IntegerField(null=True, blank=True)


class Tolerance(BaseModel, MasterDataMixin):
    class Meta:
        app_label = 'contracts'
        db_table = 'tolerances'
        ordering = ['ordering_id']

    name = models.CharField(
        max_length=100,
        choices=constants.TOLERANCES,
        null=False,
        blank=False,
    )
    quantity = models.IntegerField(null=True, blank=True)
    percentage = RoundedFloatField(null=True, blank=True)
    operator = models.CharField(max_length=10, null=True, blank=True)
    ordering_id = models.IntegerField(null=True, blank=True)

    NIL_TOLERANCE_ID = 1

    @property
    def display_name(self):
        country = Country.get_requesting_country()
        if get(country, 'config.tolerance_unit'):
            return self.name.replace('MT', f" {get(country, 'config.tolerance_unit')}")
        return self.name

    def is_gt(self, other_tolerance, tonnage, commodity):
        self_range = self.get_range(tonnage, commodity)
        other_tolerance_range = other_tolerance.get_range(tonnage, commodity)

        return self_range[1] > other_tolerance_range[1]

    def get_range(self, tonnage, commodity, parent_tonnage=None):
        from_tonnage = to_tonnage = tonnage = float(tonnage)
        quantity = float(self.quantity)
        percentage = float(self.percentage)
        tolerance_unit = get(commodity, 'country.config.tolerance_unit')
        if parent_tonnage and quantity:
            quantity = (quantity/float(parent_tonnage)) * tonnage
        if tolerance_unit:
            tonnage = commodity.convert_to(tonnage, commodity.unit, tolerance_unit)

        percentage_tonnage = percentage * tonnage
        if self.operator == 'lt':
            tolerance_value = min([
                quantity,
                percentage_tonnage
            ])
            from_tonnage = tonnage - tolerance_value
            to_tonnage = tonnage + tolerance_value
        elif self.operator == '+/-':
            if quantity > 0:
                from_tonnage = tonnage - quantity
                to_tonnage = tonnage + quantity
            elif percentage > 0:
                from_tonnage = tonnage - percentage_tonnage
                to_tonnage = tonnage + percentage_tonnage
        else:
            from_tonnage = tonnage
            to_tonnage = tonnage

        if tolerance_unit:
            from_tonnage = commodity.convert_to(from_tonnage, tolerance_unit, commodity.unit)
            to_tonnage = commodity.convert_to(to_tonnage, tolerance_unit, commodity.unit)

        return [from_tonnage, to_tonnage]


class Checkpoint(BaseModel, MasterDataMixin):
    class Meta:
        app_label = 'contracts'
        db_table = 'checkpoints'

    name = models.CharField(
        max_length=100,
        choices=constants.DESTINATION_ORIGIN,
        null=False,
        blank=False,
    )


class Packaging(BaseModel):
    class Meta:
        app_label = 'contracts'
        db_table = 'packagings'
        ordering = ['id']

    name = models.CharField(
        max_length=100,
        choices=constants.PACKAGINGS,
        null=False,
        blank=False,
    )


class ContractConveyance(BaseModel, MasterDataMixin):
    class Meta:
        app_label = 'contracts'
        db_table = 'contract_conveyances'
        ordering = ['id']

    name = models.CharField(
        max_length=100,
        choices=constants.CONVEYANCES,
        null=False,
        blank=False,
    )


class ContractPricePoint(BaseModel, MasterDataMixin):
    class Meta:
        app_label = 'contracts'
        db_table = 'contract_price_points'
        ordering = ['id']

    mandatory_props = BaseModel.mandatory_props + ['display_name']

    name = models.CharField(
        max_length=100,
        choices=constants.PRICE_POINT_TYPES,
        null=False,
        blank=False,
    )

    @property
    def display_name(self):
        return to_display_attr(constants.PRICE_POINT_TYPES, self.name)

    @property
    def is_pickup_mandatory(self):
        return self.id in CONSIGNOR_MANDATORY_PRICE_POINTS

    @property
    def is_delivery_mandatory(self):
        return self.id in CONSIGNEE_MANDATORY_PRICE_POINTS

    @property
    def delivery_onus(self):
        return DELIVERY_ONUS_SELLER if self.is_delivery_mandatory else DELIVERY_ONUS_BUYER


class ContractType(BaseModel, MasterDataMixin):
    class Meta:
        app_label = 'contracts'
        db_table = 'contract_types'

    mandatory_props = BaseModel.mandatory_props + ['display_name']

    name = models.CharField(
        max_length=100,
        choices=constants.CONTRACT_TYPES,
        null=False,
        blank=False,
    )

    @property
    def display_name(self):
        country = Country.get_requesting_country()
        if self.name == CONTRACT_TYPE_SPOT and not get(country.config, 'contracts.spot'):
            return 'Cash Contract'
        return to_display_attr(constants.CONTRACT_TYPES, self.name)


class QuantityMixin(models.Model):
    quantity = RoundedFloatField(
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
    )

    class Meta:
        abstract = True

    @property
    def is_canola(self):
        return get(self, 'commodity_id') == CANOLA_COMMODITY_ID

    @property
    def inferred_tonnage(self):
        if self.is_strict_quantity_based_commodity:
            tonnage = self.actual_quantity
        else:
            tonnage = self.actual_tonnage

        return tonnage or 0.0

    @property
    def inferred_planned_tonnage(self):
        if self.is_strict_quantity_based_commodity:
            return self.quantity
        return get(self, 'tonnage') or get(self, 'planned_tonnage')

    @property
    def actual_quantity(self):
        return self.quantity

    @property
    def actual_tonnage(self):
        return get(self, 'tonnage') or get(self, 'planned_tonnage')

    @property
    def tonnage_display_value(self):
        display_value = None
        if self.actual_quantity:
            display_value = "{actual_quantity} {unit}".format(
                actual_quantity=self.actual_quantity, unit=self.quantity_unit)
        elif self.actual_tonnage:
            display_value = "{tonnage} {unit}".format(
                tonnage='{:.2f}'.format(self.actual_tonnage), unit=self.price_unit)

        if display_value and (BALES in display_value or MODULES in display_value):
            display_value += f' ({self.actual_tonnage} {MT})'

        return display_value

    @property
    def quantity_label_with_unit(self):
        return "{label} (in {unit})".format(label=self.quantity_label, unit=self.quantity_unit)

    @property
    def quantity_label(self):
        return self.commodity.quantity_label

    @property
    def quantity_unit(self):
        return get(self, 'commodity.unit') or MT

    @property
    def tonnage_unit(self):
        return get(self, 'commodity.tonnage_unit') or MT

    @property
    def price_unit(self):
        return get(self, 'commodity.price_unit') or MT

    @property
    def unit_conversions(self):
        return self.commodity.get_unit_conversions(self.actual_tonnage)

    @property
    def inferred_tonnage_unit(self):
        return get(self, 'commodity.price_unit') or MT

    @property
    def is_meter_cube_commodity(self):
        return bool(get(self, 'commodity.is_meter_cube'))

    @property
    def is_strict_quantity_based_commodity(self):
        return bool(get(self, 'commodity.is_strict_quantity_based'))

    def _is_hay_commodity(self):
        return bool(self, 'commodity.is_bales')

    @property
    def is_quantity_based_commodity(self):
        return self.commodity.is_quantity_based if self.commodity else False

    def quantity_with_unit(self):
        return str(self.actual_quantity) + " " + self.quantity_unit

    def tonnage_with_quantity(self):
        return str(self.actual_tonnage) + ", " + self.quantity_with_unit()

    @staticmethod
    def _tonnage_description(tonnage, unit, desc):
        tonnage = format_number(tonnage)
        label = "{tonnage}{unit}".format(tonnage=tonnage, unit=unit)
        desc = desc.lower()
        return {
            'type': desc.lower(),
            'value': tonnage,
            'label': label,
            'description': "{desc} - {label}".format(desc=desc, label=label)
        }

    @property
    def is_nil_tolerance(self):
        return get(self, 'tolerance_id') == Tolerance.NIL_TOLERANCE_ID

class ContractBaseModel(BaseModel, InstanceCustomFuncMixin):
    class Meta:
        abstract = True

    FILLABLES = [
        'status',
        'created_at'
    ]

    status = models.CharField(
        max_length=40,
        choices=constants.STATUSES,
        null=False,
        blank=False,
        default='planned',
    )

    @property
    def last_voided_by(self):
        last_voided_history = self.last_voided_history
        if last_voided_history:
            return last_voided_history.history_user or last_voided_history.updated_by or self.updated_by

    @property
    def last_voided_history(self):
        return self.history.order_by('-history_date').filter(status=VOID_STATUS).first()

    @classmethod
    def build(cls, params, identity=None, exclude_fillables=None):
        if identity:
            params.update({'role': identity})
        if getattr(
                cls, '_set_content_type_params', None
        ) and not isinstance(
            params.get('handler_type', None), ContentType
        ):
            params = cls._set_content_type_params(params)
        obj = cls(**params)
        if 'id' in params:
            old_obj = cls.objects.get(id=params['id'])
            fillables = old_obj.FILLABLES
            if exclude_fillables:
                fillables = set(fillables) - set(exclude_fillables)

            for field in fillables:
                if field not in params:
                    setattr(obj, field, getattr(old_obj, field, None))
        return obj

    def _save(self):
        self.raise_errors_if_unclean()
        self.save_only()

    def clean(self):
        mandatory_fields_method_name = '_template_mandatory_fields' if self.is_template() else '_mandatory_fields'
        for field in getattr(self, mandatory_fields_method_name, []):
            if getattr(self, field, None) in [None, '', {}]:
                error = {}
                error[field] = ['This field cannot be null.']
                self.errors.update(error)
            else:
                self.errors.pop(field, None)

    def raise_errors_if_unclean(self):
        self.full_clean_errors(update=True)
        if self.errors:
            raise ValidationError('')

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def has_acceptance_requests(self, all_requests=False):
        queryset = self.acceptance_requests.filter(resolved=False)
        if not all_requests:
            queryset = queryset.filter(
                type__in=CONTRACT_ACCEPTANCE_REQUEST_FOR_CONSIDERATION
            )
        return queryset.exists()

    @staticmethod
    def can_update_asset(user, updatedable_fields, field):
        return user and user.is_staff and field in updatedable_fields

    @staticmethod
    def get_estimated_payment_due_date(payment_term, start_date, party_company=None, default_today_for_unknown=False):
        if not payment_term or not start_date:
            return timezone.now() if default_today_for_unknown else None
        start_of_week = get(party_company, 'start_of_week')
        start_of_week = start_of_week if start_of_week is not None else DEFAULT_START_OF_WEEK
        end_of_week = get(party_company, 'end_of_week')
        end_of_week = end_of_week if end_of_week is not None else DEFAULT_END_OF_WEEK
        return payment_term.get_estimated_due_date_from(
            start_date, start_of_week, end_of_week, default_today_for_unknown)

    @staticmethod
    def get_estimated_payment_due_date_exact(payment_term, start_date, party_company=None):
        return ContractBaseModel.get_estimated_payment_due_date(payment_term, start_date, party_company, True)

    @property
    def estimated_payment_due_date(self):
        payment_term = get(self, 'commodity_contract.payment_term') or get(self, 'payment_term')
        return self.get_estimated_payment_due_date(
            payment_term,
            get(self, get(payment_term, 'start_from')),
            get(self, 'buyer.company')
        )

    def estimated_payment_due_date_exact(
            self,
            commodity_contract=None,
            default_today_for_unknown=True,
            freight_order=None
        ):
        commodity_contract = commodity_contract or (
            get(self, 'commodity_contract') if not freight_order else None
        )
        payment_term = (
            get(commodity_contract, 'payment_term') or
            get(freight_order, 'payment_term') or
            get(self, 'order.payment_term') or
            get(self, 'payment_term')
        )
        from core.freights.models import FreightContract
        if isinstance(self, FreightContract):
            reference_timezone = get(self, 'freight_delivery.consignee.handler._timezone.location')
        else:
            if not commodity_contract and isinstance(self, Contract):
                commodity_contract = self
            movement = commodity_contract.freight_contracts.exclude(
                status=VOID_STATUS
            ).first() if commodity_contract else None
            reference_timezone = get(movement, 'freight_delivery.consignee.handler._timezone.location')
            if not reference_timezone and commodity_contract:
                tt = commodity_contract.titletransfer_set.exclude(status='void').filter(is_active=True).first()
                reference_timezone = get(tt, 'storage.farm._timezone.location')

        start_date = get(self, get(payment_term, 'start_from')) or get(self, 'delivery_start_date')
        localized_start_date = DateTimeUtil.localize(start_date, reference_timezone)
        return self.get_estimated_payment_due_date(
            payment_term,
            localized_start_date,
            get(self, 'buyer.company'),
            default_today_for_unknown
        )

    def estimated_payment_due_date_exact_for_date(self, for_date, commodity_contract=None):
        commodity_contract = commodity_contract or get(self, 'commodity_contract')
        payment_term = get(commodity_contract, 'payment_term') or get(self, 'payment_term')
        return self.get_estimated_payment_due_date(
            payment_term,
            for_date,
            get(self, 'buyer.company'),
            True
        )

    def update_spread_details(self, data):
        """
        This method:
        depends on data['details']
        create if details are there but the spread is not
        updates if details are there and the spread exists
        deletes if details are not there and spread exists
        """
        if self.spread_id:
            if data:
                self.spread.details = data.get('details')  # pylint: disable=access-member-before-definition
                self.spread.save()  # pylint: disable=access-member-before-definition
            else:
                self.spread.delete()  # pylint: disable=access-member-before-definition
                self.spread = None
        elif get(data, 'details'):
            spread = Spread.objects.create(details=data.get('details'))
            spread.save()
            self.spread = spread


class PartyMixin(models.Model):
    class Meta:
        abstract = True

    company = models.ForeignKey(
        'companies.Company',
        on_delete=models.PROTECT,
    )
    contact = models.ForeignKey(
        'profiles.Employee',
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
    )
    ngr = models.ForeignKey(
        'ngrs.Ngr',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    def to_csv_row(self, _role=None):
        country = Country.get_requesting_country()
        abn_label = country.get_label('abn')
        bsb_label = country.get_label('bsb')
        _role = _role or self.role
        company = self.company
        ngr = self.ngr
        bank_account = ngr.bank_account_set.first() if ngr else None
        return {
            f'{_role} {abn_label}': get(company, 'abn'),
            f'{_role} Type': get(company, 'type.display_name'),
            f'{_role}': self.company_name,
            f'{_role} Address': get(company, 'address.name'),
            f'{_role} Phone': country.to_phone_format(get(company, 'mobile')),
            f'{_role} NGR': get(ngr, 'ngr_number'),
            f'{_role} {bsb_label}': get(bank_account, 'bsb_number'),
            f'{_role} Bank Name': get(bank_account, 'bank.name'),
            f'{_role} Account Name': get(bank_account, 'account_name'),
            f'{_role} Account No': get(bank_account, 'account_number'),
        }

    @property
    def establishment_id(self):
        return self.company_id

    @property
    def establishment_type(self):
        return 'company'

    @property
    def establishment_content_type(self):
        return 'company'

    @property
    def establishment(self):
        return self.company

    @property
    def company_name(self):
        return self.ngr.shareholders_name if self.ngr else get(self, 'company.name')

    @cached_property
    def display_name(self):
        return self.ngr.shareholders_name if self.ngr else get(self, 'company.display_name')

    @cached_property
    def contact_name(self):
        return get(self, 'contact.name')

    @cached_property
    def ngr_number(self):
        return get(self, 'ngr.ngr_number')

    @property
    def party_company_id(self):
        return self.company_id

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def get_company_admins(self):
        return self.company.get_company_admins() if self.company_id else []

    @cached_property
    def is_registered_or_managed_by_registered(self):
        return self.is_participating_in_transaction

    @cached_property
    def is_participating_in_transaction(self):
        return deepgetattr(
            self, 'represented_by.transaction_participation'
        ) or deepgetattr(
            self, 'company.transaction_participation'
        )

    @cached_property
    def is_shared_ngr(self):
        return self.ngr_id and get(self, 'ngr.is_shared_ngr')

    @cached_property
    def entity_name(self):
        if self.ngr_id and get(self, 'ngr.is_shared_ngr'):
            return self.ngr.shareholders_entity_name
        return get(self, 'company.entity_name')

    @property
    def is_grower(self):
        return get(self.company, 'is_grower')

    @property
    def company_ids(self):
        return compact({self.company_id, *(get(self, 'ngr.owner_company_ids') or [])})

    @property
    def company_abn(self):
        return self.ngr.shareholders_abn if self.ngr else get(self, 'company.abn')


class Party(ContractBaseModel, PartyMixin):  # pylint: disable=too-many-instance-attributes,too-many-public-methods
    class Meta:
        db_table = 'contract_parties'

    mandatory_props = BaseModel.mandatory_props

    FILLABLES = ContractBaseModel.FILLABLES + [
        'role',
        'company_id',
        'contact_id',
        'ngr_id',
        'represented_by',
        'represented_by_id',
    ]

    role = models.CharField(
        max_length=50,
        blank=False,
        null=False,
        choices=constants.PARTY_ROLES,
    )

    represented_by = models.ForeignKey(
        'companies.Company',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='party_represented_by'
    )

    @property
    def company_ids(self):
        return compact({self.represented_by_id,  *super().company_ids})

    def to_kwargs(self, include_role=True):
        kwargs = {
            'company_id': self.company_id,
            'ngr_id': self.ngr_id,
            'contact_id': self.contact_id,
        }
        if include_role:
            kwargs['role'] = self.role
        return kwargs

    def clone(self):
        return self.__class__(
            company_id=self.company_id, ngr_id=self.ngr_id, contact_id=self.contact_id, role=self.role)

    @property
    def _mandatory_fields(self):
        return []

    @property
    def _template_mandatory_fields(self):
        return []

    @classmethod
    def _set_content_type_params(cls, params): # delete this
        return params

    def get_primary_shareholder_company_ids(self):
        return self.ngr.primary_shareholder_company_ids if self.ngr_id else []

    def get_secondary_shareholder_company_ids(self):
        return self.ngr.secondary_shareholder_company_ids if self.ngr_id else []

Seller = Party
Buyer = Party


class ContractCommodityHandler(ContractBaseModel):
    class Meta:
        db_table = 'contract_commodity_handlers'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._sites = []

    FILLABLES = ContractBaseModel.FILLABLES + [
        'role',
        'handler_id',
        'handler',
        'position',
        'ld',
    ]

    mandatory_props = BaseModel.mandatory_props + ['handler_content_type']

    role = models.CharField(
        max_length=50,
        blank=False,
        null=False,
        choices=constants.HANDLERS,
    )
    handler = models.ForeignKey('farms.Farm', on_delete=models.CASCADE)
    contract = models.ForeignKey(
        'contracts.Contract',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='contractcommodityhandler_set'
    )
    position = models.PositiveSmallIntegerField(default=1)
    ld = models.FloatField(null=True, blank=True)

    @staticmethod
    def is_farm():
        return True

    @property
    def handler_content_type(self):
        return 'farm'

    @cached_property
    def name(self):
        return get(self, 'handler.name')

    @cached_property
    def display_name(self):
        return get(self, 'handler.display_name')

    @classmethod
    def _site_role(cls, role):
        _s_role = None

        if role == 'Consignor':
            _s_role = 'Pickup'
        elif role == 'Consignee':
            _s_role = 'Delivery'

        return _s_role

    @classmethod
    def build_with_save(cls, params, identity=None):
        for item in params:
            handler = cls.build(item, identity)
            handler.full_clean_errors(update=True)
            if handler.errors:
                raise ErrorException(error_dict={
                    'identity': identity,
                    'errors': handler.errors
                })
            handler._save()
        return params

    @classmethod
    def build(cls, params, identity=None):# pylint: disable=arguments-differ
        sites = params.pop('sites', [])
        obj = super().build(params, identity=identity)
        if not obj.ld:
            obj.ld = 0

        for site_params in sites:
            site_params.update({'status': params.get('status', 'planned')})
            obj._sites.append(Site.build(site_params, cls._site_role(identity)))
        return obj

    def clean(self):
        if self.ld and math.isnan(self.ld):
            self.ld = 0
        super().clean()

    def _save(self):
        super()._save()
        for site in self._sites:
            site.handler = self
            site.full_clean_errors(update=True)
            if site.errors:
                raise ErrorException(error_dict={
                    'identity': self.role,
                    'errors': site.errors
                }, args=site.errors)
            site._save()

    @classmethod
    def _set_content_type_params(cls, params):
        _engine = inflect.engine()
        _id = params.pop('handler_id', None)
        if _id:
            _obj = Farm.objects.filter(id=_id).first()
            if _obj:
                params['handler_id'] = _obj.id
        else:
            params['handler'] = None
        return params

    @cached_property
    def handler_company_id(self):
        return getattr(self.handler, 'company_id', None)

    def is_bhc(self):
        return get(self, 'handler.is_bhc')

    def get_handler_admins(self, farm_office_admins=False):
        admins = self.handler.company.get_company_admins()
        if farm_office_admins:
            admins += self.handler.office_admins()
        return admins

    @cached_property
    def sites(self):
        return self.qs2dict(
            queryset=self.site_set.all(),
            one_to_one_relations=['location__address']
        )

    @property
    def is_registered_or_managed_by_registered(self):
        return self.is_transaction_participator_or_managed_by_transaction_participator

    @property
    def is_transaction_participator_or_managed_by_transaction_participator(self):
        if self.contract:
            if self.contract.is_seller(self.handler):
                return self.contract.seller.is_participating_in_transaction
            elif self.contract.is_buyer(self.handler):
                return self.contract.buyer.is_participating_in_transaction
        else:
            return self.handler_id and self.handler.is_transaction_participator_or_managed_by_transaction_participator

    def is_managed_by_user(self, user):
        if self.contract:
            if self.contract.is_seller(self.handler):
                return self.contract.seller.company.is_managed_by_user(user)
            elif self.contract.is_buyer(self.handler):
                return self.contract.buyer.company.is_managed_by_user(user)
        else:
            return user and self.handler_id and self.is_farm() and self.handler.is_managed_by_user(user)

    @cached_property
    def site_name(self):
        EMPTY = ''
        name = None
        site = self.site_set.first()
        if site:
            name = site.location.name
        return name or EMPTY

    @cached_property
    def address(self):
        EMPTY = ''
        return deepgetattr(self, 'handler.address.address') or EMPTY

    @cached_property
    def site_address(self):
        EMPTY = ''
        address = None
        site = self.site_set.first()
        if site:
            address = deepgetattr(site, 'location.address.address')

        return address or self.address or EMPTY

    @cached_property
    def site_phone_number(self):
        return get(self, 'handler.mobile', '')

    @cached_property
    def has_sites(self):
        return self.site_set.exists()


class Site(ContractBaseModel):
    class Meta:
        db_table = 'sites'
        ordering = ['id']

    mandatory_props = BaseModel.mandatory_props + ['location_content_type']

    FILLABLES = ContractBaseModel.FILLABLES + [
        'role',
        'location_type',
        'location_id',
        'location',
        'handler_id',
    ]

    role = models.CharField(
        max_length=50,
        blank=False,
        null=False,
        choices=constants.SITE_ROLES,
    )
    limit = {"model__in": ("storage", "farmfield", "farm")}

    location_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        related_name='site_location_type',
        limit_choices_to=limit,
        null=False,
        blank=False,
    )
    location_id = models.PositiveIntegerField(
        null=False,
        blank=False,
    )
    location = GenericForeignKey('location_type', 'location_id')

    handler = models.ForeignKey(
        ContractCommodityHandler,
        null=False,
        blank=False,
        on_delete=models.CASCADE,
        related_name='site_set'
    )

    @property
    def location_content_type(self):
        return self.location_type.name.replace(" ", "")

    @classmethod
    def _set_content_type_params(cls, params):
        _engine = inflect.engine()
        _id = params.pop('location_id', None)
        _type = params.pop('location_type', None)
        if _id and _type:
            if _type in ['storage', 'farm']:
                _klass = apps.get_model('farms', 'Storage')
                _klass_manager = _klass.all
            elif _type == 'farmfield':
                _klass = apps.get_model('farm_fields', 'FarmField')
                _klass_manager = _klass.objects
            elif _type == 'companysite':
                _klass = apps.get_model('farms', 'Farm')
                _klass_manager = _klass.objects
            else:
                _klass = apps.get_model(
                    _engine.plural(_type),
                    inflection.camelize(_type)
                )
                _klass_manager = _klass.objects

            params['location'] = _klass_manager.filter(id=_id).first()
        return params

    @property
    def name_with_ld(self):
        result = get(self, 'location.name', '')
        ld = get(self, 'location.ld', '')
        if ld:
            result += ' (' + str(ld) + ')'

        return result


class Administration(ContractBaseModel):
    class Meta:
        db_table = 'contract_administrations'

    FILLABLES = ContractBaseModel.FILLABLES + [
        'invoicing',
        'brokered_by',
        'transfer_ownership_to_id',
    ]

    invoicing = models.CharField(
        max_length=50,
        choices=constants.INVOICINGS,
        null=True,
        blank=True,
        default='Broker to Invoice Buyer',
    )
    brokered_by = models.ForeignKey(
        'companies.Company',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='contract_brokered_by',
    )
    broker_contact = models.ForeignKey(
        'profiles.Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='contract_broker_contact',
    )
    transfer_ownership_to = models.ForeignKey(
        'profiles.Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='contract_ownership_to',
    )

    @classmethod
    def build(cls, params, _=None):# pylint: disable=arguments-differ
        return super().build(params)


class Spread(ContractBaseModel):
    class Meta:
        db_table = 'contract_spread_details'

    details = models.JSONField(null=False, blank=False)

    @classmethod
    def build(cls, params, _=None):  # pylint: disable=arguments-differ
        return super().build(params)

    @property
    def grade_ids(self):
        if not self.details:
            return []
        return compact([detail.get('id', None) for detail in self.details])  # pylint: disable=not-an-iterable

    def humanize(self):
        data = self.details or []
        data = sorted(data, key=lambda s: s.get('order') or 100)

        return ', '.join([self.__humanize(details) for details in data])

    def __humanize(self, details):
        if not details:
            return ''

        return details.get('name', '') + ' ' + details.get('price_variation', '') + str(details.get('value', ''))

    @property
    def blended_grades_label(self):
        grades_display = ''
        separator = ''
        for spread_detail in sorted(self.details, key=lambda spread: spread.get('percentage', 0), reverse=True):  # pylint: disable=not-an-iterable
            grades_display += f" {separator}{get(spread_detail, 'grade_name')} {get(spread_detail, 'percentage')}%"
            separator = '/ '
        return grades_display

    def get_blended_grade_price(self, grade_id):
        rate = 0
        for spread_detail in self.details:  # pylint: disable=not-an-iterable
            if get(spread_detail, 'grade_id') == grade_id:
                rate = get(spread_detail, 'price')
                break
        return rate

    def _save(self):
        if self.details:
            for detail in self.details:  # pylint: disable=not-an-iterable
                if 'price' in detail and not get(detail, 'price'):
                    detail['price'] = None
                if 'value' in detail and not get(detail, 'value'):
                    detail['value'] = None
        self.raise_errors_if_unclean()
        self.save_only()


class ContractArea(ContractBaseModel):
    class Meta:
        db_table = 'contract_area'

    mandatory_props = ['estimated_tonnage']

    area = RoundedFloatField(null=True, blank=True)
    estimated_yield = RoundedFloatField(null=True, blank=True)

    price_by = models.DateField(blank=True, null=True)

    @classmethod
    def build(cls, params, _=None):  # pylint: disable=arguments-differ
        return super().build(params)

    @property
    def estimated_tonnage(self):
        return self.get_estimated_tonnage(self.area, self.estimated_yield)

    @classmethod
    def get_estimated_tonnage(cls, area=0, estimated_yield=0):
        return float("%0.2f" % (float(area) * float(estimated_yield))) if isinstance(
            area, (int, float)
        ) and isinstance(estimated_yield, (int, float)) else 0


class SaleContract(models.Model):
    class Meta:
        unique_together = (('purchase', 'sale'), )

    purchase = models.ForeignKey('contracts.Contract', on_delete=models.CASCADE, related_name='salecontract_set')
    sale = models.ForeignKey('contracts.Contract', on_delete=models.CASCADE, related_name='purchasecontract_set')
    tonnage = RoundedFloatField()


class ThroughWarehouseAllocation(models.Model):
    class Meta:
        unique_together = (('purchase', 'sale'), )

    purchase = models.ForeignKey(
        'contracts.Contract', on_delete=models.CASCADE, related_name='throughwarehousesalecontract_set'
    )
    sale = models.ForeignKey(
        'contracts.Contract', on_delete=models.CASCADE, related_name='throughwarehousepurchasecontract_set'
    )
    tonnage = RoundedFloatField()


class StockAllocation(RawModel):

    class Meta:
        db_table = 'stock_allocations'

    contract = models.ForeignKey('contracts.Contract', on_delete=models.CASCADE, related_name='stock_allocations')
    commodity = models.ForeignKey('commodities.Commodity', on_delete=models.DO_NOTHING)
    variety = models.ForeignKey('commodities.Variety', on_delete=models.DO_NOTHING, null=True, blank=True)
    grade = models.ForeignKey('commodities.Grade', on_delete=models.DO_NOTHING, null=True, blank=True)
    ngr = models.ForeignKey('ngrs.Ngr', on_delete=models.DO_NOTHING)
    tonnage = RoundedFloatField()
    season = models.CharField(max_length=5, null=True, blank=True, validators=[season_validator])
    site = models.ForeignKey(Farm, on_delete=models.CASCADE)

    @property
    def site_display_name(self):
        return get(self.site, 'display_name')

    @property
    def is_quantity_based_commodity(self):
        return get(self.commodity, 'is_quantity_based')

    def get_freight_contracts_for(self, statuses):
        return self.contract.freight_contracts.filter(
            status__in=statuses, commodity_id=self.commodity_id,
            planned_grade_id=self.grade_id, customer__ngr_id=self.ngr_id,
            freight_pickup__consignor__handler_id=self.site_id, season=self.season, order_id__isnull=True
        )

    def get_freight_orders_for(self):
        return self.contract.freight_orders.filter(parent_order_id=None, is_auto_created=False).filter(
            commodity_id=self.commodity_id, planned_grade_id=self.grade_id, customer__ngr_id=self.ngr_id,
            freight_pickup__consignor__handler_id=self.site_id, season=self.season
        ).exclude(status__in=NULL_STATUSES)

    def planned_tonnage_of_allocated_stock_combination(self):
        contract = self.contract
        freight_movements = self.get_freight_contracts_for(contract.PLANNED_TO_DELIVERY_STATUSES)
        movements_tonnage = float(
            '%0.2f' % sum(float(freight.inferred_tonnage or 0) for freight in freight_movements)
        ) or 0.0
        orders_tonnage = float(
            '%0.2f' % sum(float(order.planned_tonnage or 0) for order in self.get_freight_orders_for())
        ) or 0.0
        return movements_tonnage + orders_tonnage

    def delivered_tonnage_of_allocated_stock_combination(self):
        contract = self.contract
        # not using linked movements because that should be under PC/SC and not stock allocation because
        # site needs to be same
        freight_movements = contract.freight_contracts.filter(
            status__in=contract.AFTER_DELIVERY_STATUSES,
            commodity_id=self.commodity_id,
            loads__grade_id=self.grade_id,
            loads__ngr_id=self.ngr_id,
            loads__farm_id=self.site_id,
            loads__storage_id__isnull=False,
            loads__season=self.season,
            loads__type=OUTLOAD
        )
        movements_tonnage = float(
            '%0.2f' % sum(float(freight.inferred_tonnage or 0) for freight in freight_movements)) or 0.0

        tonnage_attr = 'tonnage'
        if self.is_quantity_based_commodity:
            tonnage_attr = 'quantity'

        title_transfers_tonnage = self.contract.titletransfer_set.filter(
            is_active=True, status__in=[COMPLETED, INVOICED], commodity_id=self.commodity_id,
            grade_id=self.grade_id, season=self.season, seller__ngr_id=self.ngr_id, storage__farm_id=self.site_id
        ).aggregate(tonnage=models.Sum(tonnage_attr))['tonnage'] or 0

        return movements_tonnage + title_transfers_tonnage


class Contract(ContractBaseModel, QuantityMixin): # pylint: disable=too-many-instance-attributes,too-many-public-methods
    class Meta:
        db_table = 'contracts'
        unique_together = (('template_name', 'created_by'),)
        indexes = [
            models.Index(fields=['status']),
            GinIndex(fields=['viewer_company_ids']),
        ]

    mandatory_props = BaseModel.mandatory_props + [
        'status', 'reference_number', 'display_name'
    ]
    AMENDABLE_FIELDS = ['price', 'tonnage']
    WEB_SEARCHABLE_FIELDS = [
        'seller.company.display_name',
        'buyer.company.display_name',
        'administration.invoicing',
        'status_display_name',
        'commodity.display_name',
        'grade_name',
        'identifier',
        'contract_number',
        'season',
        'price',
        'price_point.display_name',
        'tonnage_display_value',
        'outstanding_tonnage',
        'created_at',
        'delivery_start_date',
    ]

    # fields
    private = models.BooleanField(default=False)
    identifier = models.CharField(max_length=14, null=True, blank=True, db_index=True,)
    delivery_onus = models.CharField(max_length=15, null=True, blank=True, choices=constants.ROLES,)
    carry_frequency = models.CharField(
        max_length=20, null=True, blank=True, default='Monthly', choices=constants.FREQUENCIES,)
    season = models.CharField(max_length=5, null=True, blank=True, validators=[season_validator])
    track = models.CharField(max_length=255, null=True, blank=True)
    template_name = models.CharField(max_length=200, null=True, blank=True,)
    contract_number = models.CharField(max_length=14, null=True, blank=True, db_index=True,)
    lot_number = models.CharField(max_length=200, null=True, blank=True,)
    buyer_internal_reference_number = models.CharField(max_length=255, null=True, blank=True, db_index=True)
    seller_internal_reference_number = models.CharField(max_length=255, null=True, blank=True, db_index=True)
    carry_start_date = models.DateField(blank=True, null=True)
    carry_end_date = models.DateField(blank=True, null=True)
    delivery_start_date = models.DateField(blank=True, null=True)
    delivery_end_date = models.DateField(blank=True, null=True)
    contract_date = models.DateTimeField(default=django.utils.timezone.now, null=False, db_index=True)
    special_conditions = models.TextField(null=True, blank=True,)
    general_conditions = models.TextField(null=True, blank=True,)
    carry_rate = RoundedFloatField(
        null=True, blank=True, validators=[MinValueValidator(0.0101)],)
    price = RoundedFloatField(null=True, blank=True)
    currency = models.CharField(choices=CURRENCIES, max_length=10, default='AU$')
    tonnage = RoundedFloatField(null=True, blank=True)
    status = FSMField(
        max_length=40, choices=constants.STATUSES, null=False, blank=False, default='planned', db_index=True,)
    levy = RoundedFloatField(
        null=True, blank=True, validators=[MinValueValidator(0)],
        default=0,
    )
    unaccounted_tonnage = RoundedFloatField(null=True, blank=True, default=0)
    delivered_tonnage = RoundedFloatField(null=True, blank=True, default=0)
    external_args = models.JSONField(null=True, blank=True)
    viewer_company_ids = ArrayField(models.IntegerField(), null=True, blank=True)
    sustainable_commodity = models.BooleanField(default=False)

    # relations
    seller = models.OneToOneField(
        Party, on_delete=models.SET_NULL, null=True, blank=True, related_name='contract_seller',)
    buyer = models.OneToOneField(
        Party, on_delete=models.SET_NULL, null=True, blank=True, related_name='contract_buyer',)
    show_seller_signature = models.BooleanField(default=False)
    show_buyer_signature = models.BooleanField(default=False)
    area = models.OneToOneField(
        ContractArea, on_delete=models.SET_NULL, null=True, blank=True, related_name='contract_area_details',)
    spread = models.OneToOneField(
        Spread, on_delete=models.SET_NULL, null=True, blank=True, related_name='contract_spread',)
    administration = models.OneToOneField(
        Administration, on_delete=models.SET_NULL, null=True, blank=True, related_name='contract_administration',)
    document_type = models.ForeignKey(
        ContractDocumentType, on_delete=models.SET_NULL, null=True, blank=True,)
    commodity = models.ForeignKey('commodities.Commodity', on_delete=models.DO_NOTHING, null=True, blank=True,)
    variety = models.ForeignKey('commodities.Variety', on_delete=models.DO_NOTHING, null=True, blank=True,)
    grade = models.ForeignKey('commodities.Grade', on_delete=models.DO_NOTHING, null=True, blank=True,)
    market_zone = models.ForeignKey('marketzones.Marketzone', on_delete=models.DO_NOTHING, null=True, blank=True,)
    region = models.ForeignKey('regions.Region', on_delete=models.DO_NOTHING, null=True, blank=True,)
    owner = models.ForeignKey('companies.Company', on_delete=models.SET_NULL, null=True, blank=True,)
    type = models.ForeignKey(ContractType, on_delete=models.DO_NOTHING, null=True, blank=True,)
    price_point = models.ForeignKey(ContractPricePoint, on_delete=models.DO_NOTHING, null=True, blank=True,)
    payment_scale = models.ForeignKey(PaymentScale, null=True, blank=True, on_delete=models.DO_NOTHING,)
    payment_term = models.ForeignKey(PaymentTerm, null=True, blank=True, on_delete=models.DO_NOTHING,)
    tolerance = models.ForeignKey(Tolerance, on_delete=models.DO_NOTHING, default=constants.TOLERANCE_5_OR_20_PERCENT,)
    conveyance = models.ForeignKey(ContractConveyance, null=True, blank=True, default=1, on_delete=models.DO_NOTHING,)
    inspection = models.ForeignKey(
        Checkpoint, null=True, blank=True, default=1, on_delete=models.DO_NOTHING, related_name='contract_inspection',)
    weight = models.ForeignKey(
        Checkpoint, null=True, blank=True, on_delete=models.DO_NOTHING, default=1, related_name='contract_weight',)
    packaging = models.ForeignKey(Packaging, null=True, blank=True, default=1, on_delete=models.DO_NOTHING)
    cash_price = models.ForeignKey('cash_board.CashPrices', null=True, blank=True, on_delete=models.SET_NULL)
    sale_contracts = models.ManyToManyField(
        "self", symmetrical=False, related_name='purchase_contracts', through='SaleContract')
    through_warehouse_allocations = models.ManyToManyField(
        "self", symmetrical=False, related_name='through_warehouse_contracts', through='ThroughWarehouseAllocation')

    # generic relations
    brokerage_set = GenericRelation(
        'contracts.Brokerage', related_query_name='contract',
        object_id_field='entity_id', content_type_field='entity_type'
    )
    invoice_set = GenericRelation(
        'invoices.Invoice', related_query_name='contract',
        object_id_field='raised_for_id', content_type_field='raised_for_type'
    )

    material_code = models.TextField(null=True, blank=True)  # for now just for Mauri CC upload
    note_set = GenericRelation(
        'notes.Note', related_query_name='contract',
        object_id_field='object_id', content_type_field='object_type'
    )
    blending_fee = models.FloatField(null=True, blank=True, default=0)
    contract_bid = models.ForeignKey('contract_bids.ContractBid', on_delete=models.SET_NULL,
                                     null=True, blank=True, related_name='contracts')
    use_contract_price_for_invoicing  = models.BooleanField(default=True)
    sales_order_id = models.TextField(null=True, blank=True) #for impex docs

    eligibility_dec = models.ForeignKey('vendor_decs.EligibilityDeclaration', on_delete=models.SET_NULL,
                                        null=True, blank=True, related_name='contract_eligibility_declaration')

    FILLABLES = [
        'seller',
        'buyer',
        'consignor',
        'consignee',
        'document_type_id',
        'commodity_id',
        'identifier',
        'owner_id',
        'type_id',
        'grade_id',
        'variety_id',
        'season',
        'price_point_id',
        'tonnage',
        'price',
        'delivery_onus',
        'payment_scale_id',
        'payment_term_id',
        'tolerance_id',
        'conveyance_id',
        'inspection_id',
        'weight_id',
        'packaging_id',
        'market_zone_id',
        'region_id',
        'delivery_start_date',
        'delivery_end_date',
        'carry_start_date',
        'carry_end_date',
        'carry_rate',
        'carry_frequency',
        'administration',
        'track',
        'general_conditions',
        'special_conditions',
        'spread',
        'brokerages',
        'template_name',
        'contract_number',
        'private',
        'levy',
        'crm_notes',
        'note',
        'consignees',
        'consignors',
        'communication',
        'area',
        'quantity',
        'amended',
        'current',
        'external_args',
        'contract_date',
        'lot_number',
        'id',
        'rate',
        'sub_total',
        'date',
        'payment_due_date',
        'item_type',
        'invoice',
        'gst',
        'item_id',
        'payment_date',
        'total',
        'description',
        'is_bhc',
        'standard_delivery_rate',
        'material_code',
        'load_item_id',
        'sustainable_commodity',
        'site_id',
        'ngr_id',
        'currency',
        'blending_fee',
        'chemical_applications',
        'is_blended',
        'freight_movement_id',
        'levy_type',
        'contract_bid_id',
        'close_out_reason',
        'use_contract_price_for_invoicing',
        'sales_order_id',
        'acceptance_required',
        'recipients',
        'subject',
        'show_seller_signature',
        'show_buyer_signature',
        'eligibility_dec_id',
        'contract_id',
        'party',
        'tonnage_unit',
        'quantity_label',
        'quantity_unit',
        'invoicing',
        'is_area_contract',
        'is_pool_contract',
        'delivery_site_address',
        'pickup_site_address',
        'pool_grades',
        'brokered_by_id',
        'created_by',
        'seller_signature',
        'buyer_signature'
    ] + ContractBaseModel.FILLABLES

    CUSTOM_FUNC_EXCLUSIONS = [
        'seller',
        'buyer',
    ]

    PLANNED_TO_DELIVERY_STATUSES = [
        'planned',
        'confirmation_pending',
        'confirmed',
        'open',
        'in_progress',
    ]

    AFTER_DELIVERY_STATUSES = [
        'delivered',
        'invoiced',
        'completed',
        'closed',
        'manual_contract_complete_balanced',
    ]

    CONTRACT_AFTER_DELIVERY_STATUSES = [
        'delivered',
        'completed',
        'invoiced',
        'paid'
        'closed',
    ]

    STATUS_CONFIRMED = ['confirmed']

    VOIDABLE_STATUES = [
        'planned',
        'confirmed',
        'open',
        'draft'
    ]

    VOID_REQUEST_STATUSES = [
        'confirmed',
        'open',
    ]

    AMENDABLE_STATUSES = [
        'planned',
        'confirmation_pending',
        'confirmed',
        'rejected',
        'open',
        'in_progress',
        'delivered',
        'delayed',
        'delayed_delivered',
        'completed',
        'invoiced',
        'paid'
    ]

    CONFIRMED_TO_DELIVERED_STATUSES = [
        'confirmed',
        'open',
        'in_progress',
        'delivered',
    ]

    ALLOWED_STATUSES_FOR_INVOICE_CREATION = [
        'confirmed',
        'open',
        'in_progress',
        'delivered',
        'completed',
        'invoiced',
        'paid'
    ]

    STATUS_FOR_POST_DELIVERY_INVOICE_CREATION = [
        'in_progress',
        'delivered',
        'completed',
        'invoiced',
        'paid'
    ]

    ALLOWED_STATUSES_FOR_CC_INVOICE_CREATION = [
        'in_progress',
        'delivered',
        'completed',
    ]

    TEMPLATE_OR_DRAFT_STATUSES = [
        'template',
        'draft',
    ]

    @cached_property
    def country_id(self):
        return self.commodity.country_id if self.commodity_id else self.created_by.company.country_id

    @cached_property
    def country_code(self):
        return self.country.code

    @cached_property
    def country(self):
        return Country.objects.filter(id=self.country_id).first()

    @property
    def seller_signature(self):
        seller_contact = self.created_by if self.is_created_by_seller else self.seller.contact
        if seller_contact:
            return seller_contact.signature_url
        return False

    @property
    def buyer_signature(self):
        buyer_contact = self.created_by if self.is_created_by_buyer else self.buyer.contact
        if buyer_contact:
            return buyer_contact.signature_url
        return False

    def add_sale_contract(self, sale_contract_id, tonnage):
        sale_contract = self.salecontract_set.filter(sale_id=sale_contract_id).first()
        if sale_contract:
            sale_contract.tonnage = tonnage
        else:
            sale_contract = SaleContract(purchase=self, sale_id=sale_contract_id, tonnage=tonnage)
        return sale_contract.save()

    def add_through_warehouse_allocation_contract(self, sale_contract_id, tonnage):
        through_warehouse_allocation = (
                self.throughwarehousesalecontract_set.filter(sale_id=sale_contract_id).first() or
                ThroughWarehouseAllocation(purchase=self, sale_id=sale_contract_id, tonnage=tonnage)
        )
        through_warehouse_allocation.tonnage = tonnage
        through_warehouse_allocation.save()

    def verbose_description(self, _):
        return "{number} - {commodity} - {grade} - {tonnage}{unit}".format(
            number=self.reference_number, commodity=self.commodity.display_name,
            grade=self.grade_display_name(), tonnage=self.inferred_planned_tonnage,
            unit=self.inferred_tonnage_unit
        )

    @property
    def web_href(self):
        return "#/contracts/{_id}/contract".format(_id=self.id)

    @property
    def is_cash_priced(self):
        return bool(self.cash_price_id)

    @property
    def is_sales_confirmation_or_broker_note(self):
        return get(self, 'document_type_id') in [BROKER_NOTE_DOCUMENT_TYPE_ID, SALES_CONFIRMATION_DOCUMENT_TYPE_ID]

    def note(self, user):
        company_id = get(user, 'company_id')
        note = self.note_set.filter(company_id=company_id).only('description', 'attachments').first()
        return note.to_dict(properties=['attachment_urls']) if note else None

    @property
    def has_valid_commodity_contract_invoice(self):
        return bool(self.__get_invoices().filter(status__in=INVOICE_ACCEPTED_STATUSES))

    @property
    def web_referrer_url(self):
        return f"{WEB_URL}/#/?referrerUrl=/contracts/{self.id}/contract"

    @property
    def grades_invoiced(self):
        grades = []
        invoices = self.__get_invoices().filter(status__in=INVOICE_ACCEPTED_STATUSES)
        for invoice in invoices:
            for invoice_item in invoice.invoiceitem_set.filter(
                    item_type__model__in=[FREIGHT_MOVEMENT_MODEL, TITLE_TRANSFER_MODEL]):
                entity = invoice_item.item
                if get(entity, 'is_freight_contract'):
                    grade_id = get(
                        entity.inload, 'grade_id'
                    ) if self.is_inspecting_at_delivery() else get(entity.outload, 'grade_id')
                else:
                    grade_id = entity.grade_id
                grades.append(grade_id)
        return sorted(set(compact(grades)))

    def get_orders_with_grade_different_from_base_grade(self):
        return self.freight_orders.exclude(status=VOID_STATUS).exclude(planned_grade_id=self.grade_id)

    def get_movements_with_grade_different_from_base_grade(self):
        movements = self.freight_contracts.exclude(status=VOID_STATUS)
        from core.loads.constants import ACTIVE_STATUSES
        return movements.exclude(
            loads__type=core.common.constants.INLOAD,
            loads__grade_id=self.grade_id,
            loads__status__in=ACTIVE_STATUSES
        ) if self.is_inspecting_at_delivery() else movements.exclude(
            loads__type=core.common.constants.OUTLOAD,
            loads__grade_id=self.grade_id, loads__status__in=ACTIVE_STATUSES
        )

    def get_title_transfers_with_grade_different_from_base_grade(self):
        return self.titletransfer_set.exclude(status=VOID_STATUS).exclude(grade_id=self.grade_id)

    @property
    def freight_orders_grade_other_than_contract_base_grade(self):
        return list(map(
            lambda order: {
                'identifier': order.identifier,
                'grade_id': order.planned_grade_id,
                'grade': order.planned_grade_name
            },
            self.get_orders_with_grade_different_from_base_grade()
        ))

    def get_movement_info(self, movement):
        load = movement.inload if self.is_inspecting_at_delivery() else movement.outload
        return {
            'identifier': movement.identifier,
            'grade_id': load.grade_id,
            'grade': load.grade_name
        }

    @property
    def all_grades(self):
        if spread_details := get(self.spread, 'details'):
            if spread_grades := [
                get(detail, 'name') or get(detail, 'grade_name')
                for detail in spread_details
                if get(detail, 'name') or get(detail, 'grade_name')
            ]:
                return spread_grades
        return [get(self.grade, 'name', '')]

    @property
    def all_grade_ids(self):
        if grade_ids := get(self.spread, 'grade_ids', []):
            return grade_ids
        return [self.grade_id]

    def close_out(self, user, close_out_reason):
        reasons = []
        contract = Contract.objects.filter(id=self.id).first()
        try:
            with transaction.atomic():
                for movement in contract.direct_freight_movements().filter(
                        status__in=[PLANNED_STATUS, CONFIRMED_STATUS]):
                    movement.create_void_requests(
                        user=user, request_reason=close_out_reason, communication_params={'acceptance_required':False}
                    )
                for movement in contract.direct_freight_movements().filter(status=DELIVERED_STATUS):
                    movement.completed(user)
                    movement.save()
                for order in contract.direct_freight_orders().filter(
                        status__in=[PLANNED_STATUS, CONFIRMED_STATUS, OPEN_STATUS]):
                    order.create_void_requests(
                        user=user, request_reason=close_out_reason, communication_params={'acceptance_required':False}
                    )
                for order in contract.direct_freight_orders().exclude(status=VOID_STATUS):
                    order_movements = order.direct_freight_movements().filter(status=DELIVERED_STATUS)
                    for movement in order_movements:
                        if not movement.cannot_mark_as_complete_reasons(user):
                            movement.completed(user)
                            movement.save()
                    reasons = order.close_out(user, close_out_reason)
                if not reasons:
                    contract.tonnage = contract.total_delivered_tonnage
                    contract.unaccounted_tonnage = contract.get_unaccounted_tonnage()
                    contract.updated_by = user
                    contract.save()
                    if contract.status != DELIVERED_STATUS:
                        contract.recalculate_status()
                    if contract.status != COMPLETED_STATUS:
                        contract.recalculate_status()
                    if contract.status != INVOICED:
                        contract.recalculate_status()
                contract.save()
                contract_closeout.send(
                    sender=Contract, instance=contract, close_out_reason=close_out_reason, created_by_id=user.id
                )
                return reasons
        except:  # pylint: disable=bare-except
            return reasons

    def cannot_close_out_reasons(self, user): # pylint: disable=too-many-branches
        close_out_reasons = {}
        # if allocated contract - not handled should not allow to close out for now
        total_delivered_tonnage = self.total_delivered_tonnage
        if not self.document_type.is_contract:
            close_out_reasons[CONTRACT_CLOSE_OUT_ACTION_NOT_PERMITTED] = None
        if self.is_allocated_contract:
            close_out_reasons[CONTRACT_CLOSE_OUT_NOT_PERMITTED_FOR_ALLOCATED_CONTRACT] = None
        if close_out_reasons.keys():
            return list(close_out_reasons.items())
        if not total_delivered_tonnage:
            close_out_reasons[CONTRACT_HAS_NO_DELIVERED_TONNAGE] = None
        if self.is_completed() and total_delivered_tonnage:
            close_out_reasons[ALREADY_CLOSED_CONTRACT] = None
        reasons = self.cannot_raise_amend_request_reasons(user)
        for reason in reasons:
            close_out_reasons[reason] = None
        for order in self.direct_freight_orders().exclude(status=VOID_STATUS):
            if reasons := order.cannot_close_out_reasons(user, is_close_out_contract=True):
                order_url = order.web_referrer_url
                for reason in reasons:
                    if reason in close_out_reasons:
                        close_out_reasons[reason].append((order.identifier, order_url))
                    else:
                        close_out_reasons[reason] = [(order.identifier, order_url)]
        for movement in self.direct_freight_movements():
            reason = CANNOT_CLOSE_OUT_CONTRACT_DUE_TO_MOVEMENT
            movement_url = movement.web_referrer_url
            if movement.acceptance_requests.filter(resolved=False).exists():
                movement_acceptance_pending_reason = CANNOT_CLOSE_OUT_CONTRACT_DUE_TO_PENDING_REQUEST_ON_MOVEMENT
                if movement_acceptance_pending_reason in close_out_reasons:
                    close_out_reasons[movement_acceptance_pending_reason].append((movement.identifier, movement_url))
                else:
                    close_out_reasons[movement_acceptance_pending_reason] = [(movement.identifier, movement_url)]
            if movement.is_in_progress():
                if reason in close_out_reasons:
                    close_out_reasons[reason].append((movement.identifier, movement_url))
                else:
                    close_out_reasons[reason] = [(movement.identifier, movement_url)]
        close_out_reasons.pop(ALREADY_CLOSED_ORDER, None)
        return list(close_out_reasons.items())


    def close_out_transition_confirmations(self, user):
        close_out_confirmations = []
        for order in self.direct_freight_orders().filter(
                status__in=[OPEN_STATUS, CONFIRMED_STATUS, DELIVERED_STATUS, IN_PROGRESS_STATUS]):
            updated_status = COMPLETED_STATUS if (order.status in
                                                  [DELIVERED_STATUS, IN_PROGRESS_STATUS]) else VOID_STATUS
            close_out_confirmations.append(
                (order.identifier, order.web_referrer_url,
                 order.order_status_display_name(user), updated_status, order.total_delivered_tonnage, order.id)
            )
        for movement in self.direct_freight_movements().filter(status__in=[CONFIRMED_STATUS, DELIVERED_STATUS]):
            updated_status = COMPLETED_STATUS if movement.is_delivered() else VOID_STATUS
            close_out_confirmations.append(
                (movement.identifier, movement.web_referrer_url,
                 movement.status, updated_status, movement.actual_tonnage, '')
            )
        return close_out_confirmations

    @property
    def freight_contracts_grade_other_than_contract_base_grade(self):
        movements = self.get_movements_with_grade_different_from_base_grade()
        return list(map(self.get_movement_info, movements))

    @property
    def title_transfers_grade_other_than_contract_base_grade(self):
        return list(map(
            lambda title_transfer: {
                'identifier': title_transfer.identifier,
                'grade_id': title_transfer.grade_id,
                'grade': title_transfer.grade_name
            },
            self.get_title_transfers_with_grade_different_from_base_grade()
        ))

    @property
    def is_independent_site_contract(self):
        return self.seller.company.is_selling_to_independent_site_buyer and self.buyer.company.is_independent_site_buyer

    @property
    def unplanned_tonnage(self):
        return float(self.inferred_tonnage) - float(self.accounted_tonnage)

    @property
    def is_sustainable_commodity_contract(self):
        return get(self, 'cash_price.sustainable_commodity') or self.sustainable_commodity

    def to_carry_item_for_dates(self, start_date, end_date, item_tonnage, identifier, item=None, extra_attributes=None):
        from core.freights.models import FreightContract
        if isinstance(item, FreightContract) and end_date:
            end_date = end_date.date()
        if end_date and self.carry_start_date and end_date >= self.carry_start_date and self.carry_rate:
            extra_attributes = extra_attributes or {}
            description = self.get_carry_item_description_for(identifier, start_date)
            carry_item_date = self.last_carry_calculation_date(start_date)
            sub_total = self.get_carry_sub_total_on_date(start_date, item_tonnage)
            gst = sub_total * self.country.gst_rate
            data = {
                "date": carry_item_date,
                "description": description,
                "gst": gst,
                "item_type": CARRY_ITEM_MODEL,
                "rate": self.carry_rate,
                "sub_total": sub_total,
                "tonnage": item_tonnage,
                "total": sub_total + gst,
                **extra_attributes
            }
            return data
        return None

    def get_carry_item_description_for(self, identifier, start_date, tt_identifier=''):
        delivered_at = start_date.strftime("%b %d %Y") if start_date else None
        return CARRY_ITEM_DESCRIPTION.format(
            frequency=self.carry_frequency,
            start_date=self.carry_start_date.strftime("%b %d %Y") if self.carry_start_date else None,
            num_periods=self.number_of_carry_periods(start_date),
            unit=FREQUENCY_TO_UNIT_MAP.get(self.carry_frequency),
            end_date=delivered_at,
            identifier=identifier,
            tt_identifier=tt_identifier
        )

    def get_carry_sub_total_on_date(self, start_date, tonnage=None):
        return (
                float(self.carry_rate or 0) * float(tonnage or self.actual_tonnage or 0) *
                float(self.number_of_carry_periods(start_date) or 0)
        )

    def vd_requested_by_company_name(self, vd_type):
        return self.acceptance_requests.filter(
            resolved=False, accepted=False, type=vd_type
        ).values_list('created_by__company__business_name').last()

    def vd_requested_on(self, vd_type):
        return self.acceptance_requests.filter(
            resolved=False, accepted=False, type=vd_type
        ).values_list('created_at').last()

    @classmethod
    def pending_request_for_grain_vendor_dec(cls, company_id):
        #fetch last record for request where company_id is in receivers
        id_list = ContractAcceptanceRequest.objects.filter(
            accepted=False, resolved=False, type='grain_vd', receivers__contains=[company_id]
        ).order_by('-created_at')

        contract_id_list = ContractAcceptanceRequest.objects.filter(
            id__in=id_list, receivers__contains=[company_id]).values_list('contract_id')

        queryset = cls.objects.exclude(
            status__in=['draft', 'template',
                        'rejected', 'void', 'load_rejected']
        ).filter(
            id__in=contract_id_list
        ).select_related(
            'commodity', 'grade', 'variety'
        )
        return queryset

    def tonnage_distribution(self, _):
        unit = self.inferred_tonnage_unit
        booked_tonnage = self.confirmed_tonnage
        outstanding_tonnage = self.outstanding_tonnage
        delivered_tonnage = self.total_delivered_tonnage

        if booked_tonnage:
            unplanned_tonnage = self.unplanned_tonnage
            outstanding_tonnage = float(outstanding_tonnage) - (float(unplanned_tonnage) + float(booked_tonnage))
            return [
                self._tonnage_description(unplanned_tonnage, unit, 'Unplanned'),
                self._tonnage_description(outstanding_tonnage, unit, 'Planned'),
                self._tonnage_description(booked_tonnage, unit, 'Booked'),
                self._tonnage_description(delivered_tonnage, unit, 'Delivered'),
            ]

        return [
            self._tonnage_description(outstanding_tonnage, unit, 'Unplanned'),
            self._tonnage_description(delivered_tonnage, unit, 'Delivered'),
        ]

    def hierarchy(self, user):
        return HierarchyObject(self, user, True).to_dict()

    def pack_orders_with_impex_docs(self, is_manual_creation=False):
        orders = self.freight_orders.filter(type_id=PACK_ORDER_TYPE_ID).exclude(status=VOID_STATUS)
        if not is_manual_creation:
            orders = orders.filter(freight_shipping__impex_shipment_docs_id__isnull=False)
        return orders


    def check_to_update_shipments(self, amended_data):
        shipment_fields = ['identifier', 'price_point_id', 'price', 'payment_term_id', 'tolerance_id', 'grade_id']
        has_pack_orders = self.pack_orders_with_impex_docs().exists()
        if has_pack_orders:
            return any([field in amended_data for field in shipment_fields]) # pylint: disable=use-a-generator
        return False


    def get_impex_partner_payload(self, partner_type, company, contact=None):
        return {
                "partner_type": partner_type,
                "impx_id": "",
                "code": company.abn if company.abn != '-' else company.name,
                "name": company.name,
                "street_address_1": get(company.address, 'address', ''),
                "street_address_2": "",
                "street_address_3": "",
                "street_address_4": "",
                "city": get(company.address, 'suburb', ''),
                "state": get(company.address, 'state.name', ''),
                "post_code": get(company.address, 'post_code', ''),
                "country_code": company.country_code,
                "country": company.country.name,
                "phone": get(contact, 'mobile', '') or get(company, 'mobile', ''),
                "email_address": get(contact, 'email', ''),
                "repersentative_name": get(contact, 'name', ''),
                "agent_number": "",
                "abn": company.abn,
                "acn": "",
                "tax_id": ""
        }

    def get_sales_order_payload(self, user=None, tz=None, order_id=None):
        buyer_ngr_primary_owner = self.buyer.ngr.bank_account_set.filter(is_primary=True).first() if self.buyer.ngr else '' # pylint: disable=line-too-long
        consignee = self.consignees.first()
        brokered_by = get(self.administration, 'brokered_by')
        payload = {
            "order_no": self.reference_number or self.identifier,
            "order_status": "new", "order_title": self.identifier,
            "output_type": "", "order_desc": self.display_name, "user_group": "",
            "order_category": "SOE", "order_category_desc": "Export Sales",
            "buyer_and_refrence_details": {
                "order_date": DateTimeUtil.localize_date(self.contract_date, tz, '%m/%d/%Y'),
                "our_reference": self.identifier,
                "po_number": self.identifier if self.document_type_id == BROKER_NOTE_DOCUMENT_TYPE_ID else "",
                "order_type": "", "customer_reference": "",
                "po_date": "",
                "requested_delivery_date": DateTimeUtil.localize_date(self.delivery_end_date, tz, '%m/%d/%Y'),
                "contract_start_date": DateTimeUtil.localize_date(self.delivery_start_date, tz, '%m/%d/%Y'),
                "contract_end_date": DateTimeUtil.localize_date(self.delivery_end_date, tz, '%m/%d/%Y'),
                "invoice_no": "", "invoice_date": "",  "sales_office": "", "sales_employee_code": "",
                "shipment_period": "", "cust_cont_person_code": "",  "reference_order": "", "sales_division": "",
                "sales_group": ""
            },
            "partner_code_validation": True,
            "partner": [
                self.get_impex_partner_payload("Exporter", self.seller.company, self.seller.contact),
                self.get_impex_partner_payload("Buyer", self.buyer.company, self.buyer.contact),
            ],
            "transport_and_other_details": {
                "loading_port": "",
                "port_of_loading_desc": "",
                "mode_of_transport": "",
                "final_destination": "", "final_destination_desc": "",
                "port_of_discharge_code": "", "port_of_discharge_desc": "",
                "country_of_origin_code": "", "country_of_origin_desc": "",
                "country_of_destination_code": "", "country_of_destination_desc": "",
                "estimated_departure_date": DateTimeUtil.localize_date(self.delivery_end_date, tz, '%m/%d/%Y'),
                "estimated_arrival_date": DateTimeUtil.localize_date(self.delivery_end_date, tz, '%m/%d/%Y'),
                "vessal_aircraft": "", "voyage_flight_no": "", "name_of_shiping_line": "",
                "line_operator_desc": "", "no_of_containers": "", "container_desc": ""
            },
            "product_details": {
                "product": [
                    {
                        "product_code": self.freight_orders.filter(id=order_id).first().get_product_code() if order_id
                        else self.grade_display_name(),
                        "product_desc": self.commodity.display_name,
                        "no_of_pack_unit": "", "pack_unit": "",
                        "base_quantity": self.tonnage,  "base_uom": user.unit,
                        "quantity": self.tonnage,  "selling_uom": user.unit,
                        "displayed_selling_price": self.price,
                        "item_value": self.tonnage * self.price,
                        "unit_net_wt": self.tonnage,
                        "total_net_wt": self.tonnage,
                        "net_uom": user.unit,
                        "unit_gross_wt": "", "total_gross_wt": "", "gross_uom": "",
                        "unit_volume": "", "total_volume": "", "volume_uom": "", "net_con_wt": "",
                        "gross_con_wt": "", "wt_con_uom": "", "no_of_packages": "", "brand": " ",
                        "col_preservation_type": "", "col_product_type": "",
                        "col_pack_type": "", "col_supplementary_code": "", "col_cut_code": "",
                        "col_product_source_state": "", "col_commodity_code_pra": "",
                        "shipping_marks": "",  "health_certificate_description": "",
                        "slaughter_start_date": "", "slaughter_end_date": "",
                        "production_date": DateTimeUtil.localize_date(self.delivery_start_date, tz, '%m/%d/%Y'),
                        "production_end_date": DateTimeUtil.localize_date(self.delivery_end_date, tz, '%m/%d/%Y'),
                        "storage_start_date": "",  "storage_end_date": "", "expiry_start_date": "",
                        "expiry_date": "", "ahecc_code": "", "establishment_specific_code": "",
                        "establishment_specific_description": "",
                        "registered_packing_establishment_code": "", "registered_packing_establishment_description": "",
                        "registered_storage_establishment_code": "", "registered_storage_establishment_description": "",
                        "registered_establishment_code": "", "registered_establishment_description": "",
                        "order_number": "", "order_type": "", "supplied_quantity": "", "outstanding_quantity": "",
                        "std_selling_price": "", "product_type": "",  "batch_number": "", "size": "",
                        "pack_size": "", "packed_net_weight": "", "packed_gross_weight": "",
                        "packed_volume": "", "total_packed_net_weight": "", "total_packed_gross_weight": "",
                        "total_packed_volume": "", "fob_value": "", "min_qty": "", "max_qty": "", "factor": "",
                        "discount_allowed": "", "discount_allowed_value": "", "specification": "",
                        "origin_criterion": "", "container_number": "",
                        "seal_number": "", "seal_no_2": "", "parent_product_id": "",
                        "level": "", "product_class": self.season, "chemical_lean_indicator": "",
                        "is_halal_indicator": "", "import_authority_code": "",
                        "product_category_id": "", "origin_country_code": self.country_code,
                        "product_details_1": self.commodity.display_name,
                        "product_details_2": self.grade_display_name(),
                        "product_details_3": "", "product_details_4": "", "product_details_5": "",
                        "label_3": "", "hs_code": "", "origin_country": self.country.name,
                        "udf_1": "",  "udf_2": "", "udf_3": "", "udf_4": "",
                        "udf_5": "", "udf_6": "", "hc_signed_date": "", "total_value": "", "total_fob_price": "",
                        "insurance": "", "freight": "", "fob_price_ctn": "", "supplier_code": "", "supplier_name": "",
                        "moisture": "", "vendor_other_product_code": "", "line_number": "",
                        "customer_product_code": "", "customer_product_description": "", "customer_ahecccode": ""
                    }
                ],
                "pricing_details": {
                    "freight_charges_in_base_currency": "", "insured_value_in_base_currency": "",
                    "is_default_consular_fee": False, "consular_fees_label": "", "is_consular_fee_percentage": False,
                    "consular_per_val": "", "consular_fees_value": "", "is_legal_fee": False,
                    "deposit_label": "", "is_deposit_fee_percentage": False,
                    "deposit_per_val": "", "deposit_value": "", "is_default_freight_charges": False,
                    "is_freight_charges_percentage": False, "freight_charges_label": "",
                    "freight_per_val": "", "freight_charges_value": "", "is_default_insurance_prem": False,
                    "insurance_premium_label": "", "is_insurance_prem_percentage": False, "insurance_per_val": "",
                    "insurance_premium_value": "", "is_other_pricing_1_percentage": False, "other_pricing_label_1": "",
                    "other_pricing_1_per_val": "", "other_pricing_1_value": "", "is_other_pricing_2_percentage": False,
                    "other_pricing_label_2": "", "other_pricing_2_per_val": "", "other_pricing_2_value": "",
                    "is_user_define_charges1": False, "is_user_define_charges2": False, "subtotal": ""
                },
                "is_auto_calculation": True
            },
            "other_information": {
                "brand": "", "preservation_type": "", "preservation_type_desc": "",
                "currency": self.currency, "exchange_rate": 0.0,
                "fec_no": "", "incoterms": self.price_point.display_name,
                "unit_price_info": user.unit, "forward_maturity_date": "", "general_product_description": ""
            },
            "terms_and_condition": {
                "document_list": "", "tolerance": get(self, 'tolerance.display_name'),
                "packing": "", "marks": "", "weight_quality_terms": "", "insurance": "", "header_text": "",
                "special_instruction": "", "proforma_invoice_text": "", "terms": ""
            },
            "internal_notes_details": {
                "internal_notes": []
            },
            "payment_and_other_details": {
                "currency": self.currency, "invoice_due_no_of_days": "", "invoice_due_status": "",
                "payment_type": "", "lc_due_dt_no_of_days": "", "lc_due_date": "",
                "incoterms": self.price_point.display_name, "delivery_terms": "",
                "insurance": "",
                "exporter_bank": {
                    "bank_name": "", "account_number": "", "bank_address_1": "", "bank_address_2": "",
                    "bank_address_3": "", "bank_address_4": "", "bsb_number": "", "swift_code": "", "detail": ""
                },
                "buyer_bank": {
                    "bank_name": get(buyer_ngr_primary_owner, 'bank.name', ''),
                    "account_number": get(buyer_ngr_primary_owner, 'account_number', ''),
                    "bank_address_1": '', "bank_address_2": '', "bank_address_3": '',
                    "bank_address_4": get(buyer_ngr_primary_owner, 'bank.country.name', ''),
                    "bsb_number": get(buyer_ngr_primary_owner, 'bsb_number', ''),
                    "swift_code": "", "detail": ""
                }
            },
            "signatory_and_other_details": {
                "signatory_company": "", "name_of_authorized_signatory": "", "place_of_issue": "", "date_issue": ""
            },
            "external_documents": [
                { "file_name": "", "content": "", "mime_type": "", "document_category": "" }
            ]
        }

        for note in self.note_set.filter(description__isnull=False):
            description = f"Note for {note.company.name}" if note.company.name else "General Note"
            payload['internal_notes_details']['internal_notes'].append({
                "notes_desc": f"{description} : {get(note, 'description', None)}"
            })
        if consignee:
            payload['partner'].append(self.get_impex_partner_payload("Consignee", consignee.handler.company))
        if brokered_by:
            payload['partner'].append(self.get_impex_partner_payload("Broker", brokered_by))

        return payload

    def recalculate_status(self, user=None):
        current_status = self.status
        self.transition_to_invoiced_or_paid_if_needed(user)
        if current_status == self.status:
            self.transition_to_status_if_needed(COMPLETED_STATUS)
        if current_status == self.status:
            self.transition_to_status_if_needed(DELIVERED_STATUS)
        if current_status == self.status:
            self.transition_to_status_if_needed(IN_PROGRESS_STATUS)
        if current_status == self.status:
            self.transition_to_confirmed_or_open_if_needed()

    def transition_to_invoiced_or_paid_if_needed(self, user=None):
        if can_proceed(self.invoiced):
            self.invoiced()
            if self.is_enough_to_mark_paid:
                self.paid(None, user, False)
            self.save()

    def transition_to_confirmed_or_open_if_needed(self):
        if self.should_be_confirmed_or_open():
            status_method = self.get_status_method_from_load_rejected()
            if can_proceed(status_method):
                status_method()
                self.save()

    def transition_to_status_if_needed(self, status):
        if not status:
            return
        status_method = getattr(self, status)
        if can_proceed(status_method):
            status_method()
            self.save()

    @property
    def direct_descendants(self):
        return [
            *self.direct_freight_orders().order_by('-freight_pickup__date_time'),
            *self.direct_freight_movements().order_by('-freight_pickup__date_time'),
            *self.titletransfer_set.all().order_by('-process_on')
        ]

    def clean(self):
        self.set_contract_number_uniqueness_error()
        if not self.price and self.price != 0:
            self.price = None
        super().clean()

    @staticmethod
    def facet_fields():
        return [
            'status',
            'season',
            'grade__name',
            'commodity__name',
            'price_point__name',
            'buyer__company__business_name',
            'seller__company__business_name',
        ]

    @classmethod
    def get_facets(cls, queryset, applied_facets=None):
        applied_facets = applied_facets or {}
        fields = cls.facet_fields()
        facets = {}
        for field in fields:
            applied_facet = get(applied_facets, field) or []
            for combo in queryset.values(field).annotate(count=models.Count('id')).values_list(field, 'count'):
                is_applied = False
                for value in applied_facet:
                    if combo[0] == value:
                        is_applied = True
                        break
                facets[field] = facets.get(field, []) + [[*combo, is_applied]]
        for field in fields:
            sorted_facets = sorted(
                [facet for facet in facets.get(field, []) if facet[1]], key=lambda x: x[1], reverse=True
            )
            if sorted_facets:
                facets[field] = sorted_facets
            else:
                facets.pop(field, None)
        return facets

    @classmethod
    def apply_facets(cls, queryset, facets):
        # facets = {'status': ['confirmed', 'in_progress']}
        for field, values in facets.items():
            queryset = queryset.filter(**{field + '__in': values})
        return queryset

    @classmethod
    def criteria_for_search(cls, search_str, user):
        search_criteria = (
            models.Q(identifier__icontains=search_str) |
            models.Q(contract_number__icontains=search_str) |
            models.Q(status__icontains=search_str) |
            models.Q(status__icontains=search_str.replace(' ', '_')) |
            models.Q(seller__company__business_name__icontains=search_str) |
            models.Q(buyer__company__business_name__icontains=search_str) |
            models.Q(administration__invoicing__icontains=search_str) |
            models.Q(price_point__name__icontains=search_str) |
            models.Q(price_point__name__icontains=search_str.replace(' ', '_')) |
            models.Q(commodity__name__icontains=search_str) |
            models.Q(grade__name__icontains=search_str) |
            models.Q(spread__details__contains=[{'grade_name': search_str}],
                     type_id=CONTRACT_TYPE_BLENDED_ID) |
            models.Q(season__icontains=search_str) |
            models.Q(type__name__icontains=search_str.replace(' ', '_')) |
            models.Q(delivery_onus__icontains=search_str) |
            models.Q(payment_term__name__icontains=search_str) |
            models.Q(tolerance__name__icontains=search_str) |
            models.Q(lot_number__icontains=search_str) |
            models.Q(
                models.Q(buyer_internal_reference_number__icontains=search_str) &
                models.Q(buyer__company_id=user.company_id)
            ) |
            models.Q(
                models.Q(seller_internal_reference_number__icontains=search_str) &
                models.Q(seller__company_id=user.company_id)
            )
        )
        handlers_criteria = (
                models.Q(contractcommodityhandler_set__handler__company__business_name__icontains=search_str) |
                models.Q(contractcommodityhandler_set__handler__name__icontains=search_str)
        )
        return search_criteria | handlers_criteria

    @classmethod
    def for_vendor_dec(cls, company_id, search_str=None):
        queryset = cls.objects.exclude(
            status__in=['draft', 'template', 'rejected', 'void']
        ).filter(
            Q(seller__company_id=company_id) |
            Q(seller__represented_by_id=company_id)
        ).select_related('commodity', 'grade', 'variety')

        if search_str:
            queryset = queryset.filter(
                Q(identifier__icontains=search_str) | Q(contract_number__icontains=search_str)
            )

        return queryset

    def can_user_view(self, user):
        return user.company_id in (self.viewer_company_ids or [])  # pylint: disable=unsupported-membership-test

    @property
    def should_show_spreads(self):
        return self.type_id in SPREAD_CONTRACT_TYPES or get(self, 'spread.details.0.gist')

    @property
    def applications(self):
        return self.chemical_applications.filter(is_active=True)

    def __has_any_uninvoiced_completed_freight_contract(self,):  # pylint: disable=unused-private-member
        from core.freights.models import FreightOrder
        my_pickup_order_ids = self.freight_orders.filter(
            freight_pickup__pickup_orders__isnull=False
        ).values_list('freight_pickup__pickup_orders__id', flat=True)

        order_ids_using_my_orders_as_delivery_order = FreightOrder.objects.filter(
            freight_delivery__delivery_orders__in=self.freight_orders.values_list('id', flat=True)
        ).values_list('id', flat=True)

        from core.freights.models import FreightContract
        linked_completed_movements = FreightContract.objects.filter(
            order_id__in=[*my_pickup_order_ids, *order_ids_using_my_orders_as_delivery_order],
            status__in=['completed', 'manual_contract_complete_balanced']
        )

        from core.invoices.models import InvoiceItem
        completed_movement_ids = [
            movement.id for movement in self.completed_freight_contracts
        ] if isinstance(
            self.completed_freight_contracts, list
        ) else self.completed_freight_contracts.values_list('id', flat=True)
        movement_ids = [
            *completed_movement_ids,
            *linked_completed_movements.values_list('id', flat=True)
        ]
        invoiced_movements = InvoiceItem.objects.filter(
            item_id__in=movement_ids,
            item_type__model=FREIGHT_MOVEMENT_MODEL,
            invoice__status__in=INVOICE_ACCEPTED_STATUSES,
            is_rejected=False,
            invoice__type=COMMODITY_CONTRACT_INVOICE,
            invoice__raised_for_id=self.id
        ).count()
        if self.is_blended:
            from core.loads.models import Load
            blended_invoice_items = InvoiceItem.objects.filter(
                item_id__in=Load.objects.filter(movement_id__in=movement_ids).values_list('id', flat=True),
                item_type__model=LOAD_MODEL,
                invoice__status__in=INVOICE_ACCEPTED_STATUSES,
                is_rejected=False,
                invoice__type=COMMODITY_CONTRACT_INVOICE,
                invoice__raised_for_id=self.id
            )
            invoiced_movements += len(compact(Load.objects.filter(
                id__in=blended_invoice_items.values_list('item_id', flat=True)
            ).values_list('movement_id', flat=True)))
        return invoiced_movements < len(movement_ids)

    def __has_any_uninvoiced_completed_title_transfer(self):  # pylint: disable=unused-private-member
        from core.invoices.models import InvoiceItem
        completed_transfer_ids = [
            title_transfer.id for title_transfer in self.completed_title_transfers
        ] if isinstance(
            self.completed_title_transfers, list
        ) else self.completed_title_transfers.values_list('id', flat=True)

        return InvoiceItem.objects.filter(
            item_id__in=completed_transfer_ids,
            item_type__model=TITLE_TRANSFER_MODEL,
            invoice__status__in=INVOICE_ACCEPTED_STATUSES,
            is_rejected=False,
            invoice__type=COMMODITY_CONTRACT_INVOICE,
        ).count() < len(completed_transfer_ids)

    def __has_any_brokerage_uninvoiced_completed_freight_contract(self, party):
        return any((
            not movement.is_brokerage_invoiced_for(
                party
            ) for movement in self.completed_freight_contracts
        ))

    def __has_any_brokerage_uninvoiced_completed_title_transfer(self, party):
        completed_title_transfers = get(
            self, 'completed_title_transfers'
        ) or self.titletransfer_set.filter(status='completed')
        return any((
            not tt.is_brokerage_invoiced_for(
                party
            ) for tt in completed_title_transfers
        ))

    @classmethod
    def get_invoice_pending_contracts_for_user(cls, user, filters=None):
        from core.freights.models import FreightContract

        filters = filters or {}

        contracts = cls.for_company_contract_queryset(
            user.company_id
        ).filter(
            status__in=cls.ALLOWED_STATUSES_FOR_INVOICE_CREATION
        ).exclude(
            excluded_contract__type=constants.COMMODITY_CONTRACT_INVOICE
        ).exclude(
            acceptance_requests__type__in=['void', 'amend'], acceptance_requests__resolved=False
        ).filter(
            models.Q(
                administration__invoicing=BUYER_RCTI_INVOICING,
                buyer__company_id=user.company_id
            ) | models.Q(
                seller__company_id=user.company_id
            )
        )
        title_transfer_content_type_id, movement_content_type_id = cls.content_type_ids_for(  # pylint: disable=unbalanced-tuple-unpacking
            model_names=['titletransfer', 'freightcontract'])
        from core.invoices.models import Invoice
        existing_invoices = Invoice.objects.filter(
            type='Commodity Contract', raised_for_id__in=list(contracts.values_list('id', flat=True)),
            status__in=INVOICE_ACCEPTED_STATUSES
        )
        invoiced_movement_ids = existing_invoices.filter(
            invoiceitem__item_type_id=movement_content_type_id).values_list('invoiceitem__item_id', flat=True)
        invoiced_title_transfer_ids = existing_invoices.filter(
            invoiceitem__item_type_id=title_transfer_content_type_id).values_list('invoiceitem__item_id', flat=True)

        contracts = contracts.prefetch_related(
            Prefetch(
                'freight_contracts',
                to_attr='completed_freight_contracts',
                queryset=FreightContract.objects.filter(
                    status__in=['completed', 'manual_contract_complete_balanced']).exclude(id__in=invoiced_movement_ids)
            )
        ).prefetch_related(
            Prefetch(
                'titletransfer_set',
                to_attr='completed_title_transfers',
                queryset=TitleTransfer.objects.filter(status='completed').exclude(id__in=invoiced_title_transfer_ids)
            )
        ).filter(**filters)

        filtered_contracts = []
        traversed = []
        for contract in contracts:
            if contract.id not in traversed:
                if contract.__has_any_uninvoiced_completed_freight_contract() or \
                contract.__has_any_uninvoiced_completed_title_transfer():
                    filtered_contracts.append(contract.id)
                traversed.append(contract.id)

        return cls.objects.filter(id__in=filtered_contracts)

    def __get_brokerage(self, party):
        if getattr(
                self, '_prefetched_objects_cache', None
        ) and 'brokerage_set' in self._prefetched_objects_cache:
            brokerages = list(filter(
                lambda b: b.type.lower() == party.lower(), self.brokerage_set.all()
            ))

            if brokerages:
                return brokerages[0]
        else:
            return self.brokerage_set.filter(type=party).first()

    def __has_not_brokerage_invoiced(self, brokerage_type):
        brokerage = self.__get_brokerage(brokerage_type)
        if not brokerage:
            return False
        party = getattr(self, brokerage_type.lower(), None)
        if brokerage and brokerage.is_post_delivery:
            return self.__has_any_brokerage_uninvoiced_completed_freight_contract(party) or \
                self.__has_any_brokerage_uninvoiced_completed_title_transfer(party)
        else:
            party = getattr(self, brokerage_type.lower(), None)
            return party and not self.is_brokerage_invoice_exists(party)

    def is_eligible_for_brokerage_invoice(self):
        return self.__has_not_brokerage_invoiced(
            BROKERAGE_TYPE_SELLER
        ) or self.__has_not_brokerage_invoiced(
            BROKERAGE_TYPE_BUYER
        )

    def is_contract_in_exclusion_list(self):
        brokerages = self.brokerage_set.all().values('type')
        if len(brokerages) == 2:
            return self.excluded_contract.filter(type=constants.SELLER_INVOICE).exists() and \
                self.excluded_contract.filter(type=constants.BUYER_INVOICE).exists()
        elif len(brokerages) == 1:
            if get(brokerages[0], 'type') == "Buyer":
                self.excluded_contract.filter(type=constants.BUYER_INVOICE).exists()
            else:
                return self.excluded_contract.filter(type=constants.SELLER_INVOICE).exists()
        return False

    @classmethod
    def get_brokerage_invoice_pending_contracts_for_user( # pylint: disable=too-many-locals
            cls, user, party_company_id='', include_contract_ids=None
    ):
        contract_content_type_id = BaseModel.content_type_ids_for(model_names=['contract'])[0]
        freight_contract_content_type_id = BaseModel.content_type_ids_for(model_names=['freightcontract'])[0]
        tt_content_type_id = BaseModel.content_type_ids_for(model_names=['titletransfer'])[0]

        # raw sql to optimise performance as there is no way to check directly if contract has pending brokerage invoice
        if party_company_id and party_company_id.isdigit():
            seller_condition = " and (cps.represented_by_id=" + str(user.company_id) + \
                               " or cps.company_id=" + party_company_id + ")"
            buyer_condition = " and (cpb.represented_by_id=" + str(user.company_id) + \
                              " or cpb.company_id=" + party_company_id + ")"
            brokerage_party_condition = "=" + party_company_id
        else:
            seller_condition = ""
            buyer_condition = ""
            brokerage_party_condition = " is not null"
        seller_party = " left outer join contract_parties cps on cc.seller_id=cps.id" + seller_condition
        buyer_party = " left outer join contract_parties cpb on cc.buyer_id=cpb.id" + buyer_condition
        brokerages = " join contract_brokerages ccb on ccb.entity_id=cc.id and ccb.entity_type_id=%s and ((ccb.type=%s and cpb.company_id" + brokerage_party_condition + ") or (ccb.type=%s and cps.company_id" + brokerage_party_condition + "))"  # pylint: disable=line-too-long
        excluded_contracts = " left outer join excluded_contract ec on cc.id = ec.contract_id and position(ccb.type in ec.type) > 0"  # pylint: disable=line-too-long
        freight_contract = " left outer join freight_contracts fm on cc.id=fm.commodity_contract_id and fm.status in %s"
        title_transfers = " left outer join title_transfers tt on tt.commodity_contract_id=cc.id and tt.status in %s"
        invoice_and_invoice_item_view = " left outer join (select ii.item_id, ii.invoice_id, ii.id, ii.item_type_id, ii.is_rejected, i.payer_id from invoice_items ii join invoices i on ii.invoice_id=i.id and i.type=%s) iiv"  # pylint: disable=line-too-long
        invoice_items_view_join_conditions = " on ((ccb.charged_on=%s and iiv.item_id=cc.id and iiv.item_type_id=%s) or (ccb.charged_on=%s and ((iiv.item_id=fm.id and iiv.item_type_id=%s) or (iiv.item_id=tt.id and iiv.item_type_id=%s)))) and iiv.is_rejected=false"  # pylint: disable=line-too-long
        invoice_parties = " left outer join invoice_parties ip on iiv.payer_id=ip.id and ((ip.company_id=cps.company_id and ccb.type=%s) or (ip.company_id=cpb.company_id and ccb.type=%s))"  # pylint: disable=line-too-long
        conditions = " where (cps.represented_by_id=%s or cpb.represented_by_id=%s) and ip.company_id is null and ec.type is null and cc.status in %s and (fm.id is not null or tt.id is not null or ccb.charged_on != %s)"  # pylint: disable=line-too-long

        query = "select distinct cc.id from contracts cc" + seller_party + buyer_party + brokerages + \
                excluded_contracts + freight_contract + title_transfers + invoice_and_invoice_item_view + \
                invoice_items_view_join_conditions + invoice_parties + conditions
        params = [contract_content_type_id, BROKERAGE_TYPE_BUYER, BROKERAGE_TYPE_SELLER,
                  ('completed', 'manual_contract_complete_balanced'), ('completed', 'invoiced'), 'Brokerage',
                  PRE_DELIVERY, contract_content_type_id, POST_DELIVERY, freight_contract_content_type_id,
                  tt_content_type_id, BROKERAGE_TYPE_SELLER, BROKERAGE_TYPE_BUYER, user.company_id, user.company_id,
                  ('confirmed', 'open', 'in_progress', 'delivered', 'completed', 'invoiced', 'paid'), POST_DELIVERY]

        contracts = Contract.objects.filter(
            models.Q(id__in=RawSQL(query, params)) | models.Q(id__in=include_contract_ids)).select_related(
            'commodity', 'variety', 'grade', 'administration',
            'seller__company', 'buyer__company', 'created_by'
        )

        return list(contracts)

    @classmethod
    def get_pending_acceptance_contracts_for_user(cls, user):
        contracts = cls.for_company_contract_queryset(
            user.company_id
        ).select_related(
            'commodity', 'variety', 'grade', 'price_point', 'created_by', 'document_type',
            'administration', 'seller__company', 'buyer__company', 'seller__represented_by', 'buyer__represented_by'
        ).prefetch_related(
            'acceptance_requests',
        ).filter(
            status='planned'
        )

        return [
            contract for contract in contracts if contract.confirmable(user)
        ]

    @classmethod
    def status_to_be_updated(cls, params=None):  # pylint: disable=too-many-branches
        contracts = Contract.objects.exclude(status__in=['template', 'planned', 'void', 'rejected']).order_by('-id')
        if params:
            contracts = contracts.filter(**params)

        contracts_to_be_updated = {
            'confirmed': [], 'open': [], 'in_progress': [], 'delivered': [], 'completed': [], 'invoiced': [], 'paid': []
        }
        for _cc in contracts:
            if _cc.status not in ['open', 'confirmed'] and round(_cc.total_delivered_tonnage, 2) == 0:
                if _cc.is_start_date_today():
                    contracts_to_be_updated['open'].append(_cc.id)
                else:
                    contracts_to_be_updated['confirmed'].append(_cc.id)
            if _cc.status != 'in_progress' and _cc.is_expected_tonnage_not_delivered():
                contracts_to_be_updated['in_progress'].append(_cc.id)
            if _cc.is_expected_tonnage_delivered():
                from core.invoices.models import Invoice
                invoiced_conditions = _cc.all_title_transfers_have_status(
                    ['invoiced']
                ) and _cc.all_freight_contracts_invoiced() and not _cc.any_pending_commodity_contract_invoice()
                paid_conditions = not Invoice.objects.exclude(status__in=['paid', 'rejected', 'void']).filter(
                    raised_for_id=_cc.id, raised_for_type_id=_cc.content_type_ids_for(model_names=['contract'])[0]
                )
                completed_conditions = _cc.all_freight_contracts_have_status(
                    ['completed', 'manual_contract_complete_balanced']
                ) and _cc.all_freight_orders_have_status('completed') and _cc.all_title_transfers_have_status(
                    ['completed', 'invoiced']
                )
                if completed_conditions:
                    if invoiced_conditions:
                        if paid_conditions:
                            contracts_to_be_updated['paid'].append(_cc.id) if _cc.status != 'paid' else None
                        else:
                            contracts_to_be_updated['invoiced'].append(_cc.id) if _cc.status != 'invoiced' else None
                    else:
                        contracts_to_be_updated['completed'].append(_cc.id) if _cc.status != 'completed' else None
                else:
                    contracts_to_be_updated['delivered'].append(_cc.id) if _cc.status != 'delivered' else None

        return contracts_to_be_updated

    @classmethod
    def get_pending_amend_acceptance_contracts_for_user(cls, user):
        contracts = cls.for_company_contract_queryset(
            user.company_id
        ).select_related(
            'commodity', 'variety', 'grade', 'price_point', 'created_by', 'document_type',
            'seller__company', 'buyer__company', 'seller__represented_by', 'buyer__represented_by'
        ).prefetch_related(
            'acceptance_requests',
        ).filter(
            status__in=cls.AMENDABLE_STATUSES
        )

        return [
            contract for contract in contracts if contract.amendable(user)
        ]

    @classmethod
    def get_pending_void_acceptance_contracts_for_user(cls, user):
        contracts = cls.for_company_contract_queryset(
            user.company_id
        ).select_related(
            'commodity', 'variety', 'grade', 'price_point', 'created_by', 'document_type',
            'seller__company', 'buyer__company', 'seller__represented_by', 'buyer__represented_by'
        ).prefetch_related(
            'acceptance_requests',
        ).filter(
            status__in=cls.VOID_REQUEST_STATUSES
        )

        return [
            contract for contract in contracts if contract.voidable(user)
        ]

    def is_contract_number_non_editable(self, user):
        return self.is_seller(user) and self.document_type.is_contract

    @property
    def is_contract_number_mandatory_for_confirmation(self):
        return False

    def was_created_in_status(self, status):
        if self.persisted and status and self.history.exists():
            return self.history.earliest().status == status

    @cached_property
    def seller_company_name(self):
        return get(self, 'seller.company_name')

    @cached_property
    def buyer_company_name(self):
        return get(self, 'buyer.company_name')

    @cached_property
    def seller_contact_name(self):
        return get(self, 'seller.contact_name')

    @cached_property
    def buyer_contact_name(self):
        return get(self, 'buyer.contact_name')

    @cached_property
    def seller_ngr_number(self):
        return get(self, 'seller.ngr.ngr_number')

    @cached_property
    def buyer_ngr_number(self):
        return get(self, 'buyer.ngr.ngr_number')

    @property
    def carry_current(self):
        return self.carry_current_for_tonnage(self.inferred_tonnage)

    def carry_current_for_tonnage(self, tonnage):
        """Assuming database and api_service timezone is same.
        :return:
        """
        today = timezone.now().date()
        carry_rate = self.carry_rate or 0
        carry_periods = self.number_of_carry_periods(today)
        return tonnage * carry_rate * carry_periods

    @property
    def carry_max(self):
        return self.carry_max_for_tonnage(self.inferred_tonnage)

    def carry_max_for_tonnage(self, tonnage):
        if not (self.carry_end_date and self.carry_start_date):
            return 0
        if self.carry_frequency == MONTHLY:
            carry_periods = DateTimeUtil.months_delta(self.carry_start_date, self.carry_end_date)
        else:
            difference_in_days = (self.carry_end_date - self.carry_start_date).days
            period = FREQUENCY_TO_PERIOD_MAP.get(self.carry_frequency) or 1
            carry_periods = math.ceil(difference_in_days / period)
        carry_rate = self.carry_rate or 0
        return tonnage * carry_rate * max(carry_periods, 0)

    @property
    def contract_current_value(self):
        return add_args(self.contract_value or 0, self.carry_current or 0)

    @property
    def amended_contract_current_value(self):
        carry_current = get(self.amended_details, CARRY_CURRENT_KEY, 0) \
            if CARRY_CURRENT_KEY in self.amended_details else self.carry_current
        return add_args(self.amended_contract_value or 0, carry_current or 0)

    @property
    def contract_value(self):
        return multiply_args(self.inferred_tonnage or 0, self.price or 0)

    @property
    def amended_contract_value(self):
        tonnage = get(
            self.amended_details, TONNAGE_KEY, 0
        ) if TONNAGE_KEY in self.amended_details else self.inferred_tonnage
        price = get(
            self.amended_details, PRICE_KEY, 0
        ) if PRICE_KEY in self.amended_details else self.price
        return multiply_args(tonnage or 0, price or 0)

    @property
    def contract_max_value(self):
        return add_args(self.contract_value or 0, self.carry_max or 0)

    @cached_property
    def progress_tonnage(self):
        return self._freight_contracts_tonnage_for([IN_PROGRESS_STATUS])

    def progress_tonnage_for_grade(self, grade_id):
        freight_contract_ids = self.all_freight_contracts().filter(
            status__in=[IN_PROGRESS_STATUS]
        ).values_list('id', flat=True)
        from core.loads.models import Load
        loads = Load.objects.filter(
            movement_id__in=freight_contract_ids, grade_id=grade_id, type=Load.OUTLOAD
        ).exclude(status__in=NULL_STATUSES)
        return float('%0.2f' % sum(float(load.estimated_net_weight or 0) for load in loads)) or 0.0

    def delivered_tonnage_for_grade(self, grade_id, freight_contract_ids=None):
        # Only for blended
        if not freight_contract_ids:
            freight_contract_ids = self.all_freight_contracts().filter(
                status__in=self.AFTER_DELIVERY_STATUSES
            ).values_list('id', flat=True)
        from core.loads.models import Load
        load_type = Load.INLOAD if self.is_inspecting_at_delivery() else Load.OUTLOAD
        loads = Load.objects.filter(movement_id__in=freight_contract_ids, grade_id=grade_id, type=load_type)
        return float('%0.2f' % sum(float(load.estimated_net_weight or 0) for load in loads)) or 0.0

    def invoiced_tonnage_for_grade(self, grade_id):
        return float(
            "%0.2f" % (self.all_invoiced_title_transfer_tonnage() + self.all_invoiced_movements_tonnage(grade_id))
        )

    @property
    def amended_contract_max_value(self):
        carry_max = self.carry_max
        if CARRY_MAX_KEY in self.amended_details:
            carry_max = float(get(self.amended_details, CARRY_MAX_KEY, 0))
        return add_args(self.amended_contract_value or 0, carry_max or 0)

    @property
    def estimated_payment_due_date_display(self):
        _date = self.estimated_payment_due_date_exact(default_today_for_unknown=False)
        if _date:
            return _date.strftime(Country.get_country_format('date'))

    @property
    def estimated_payment_due_date(self):
        if not self.buyer_id:
            return None
        return super().estimated_payment_due_date

    def get_amended_party_entity_name(self, amended_details, party):
        if party and amended_details and party in amended_details:
            party_details = get(amended_details, party)
            ngr = Ngr.objects.filter(id=get(party_details, 'ngr_id.id')).first()
            if get(ngr, 'is_shared_ngr'):
                amended_details[party]['is_shared_ngr'] = True
                amended_details[party]['entity_name'] = ' | '.join(ngr.share_holder_entity_names)
            else:
                amended_details[party]['is_shared_ngr'] = get(self, party + '.is_shared_ngr')
                amended_details[party]['entity_name'] = get(self, party + '.entity_name')

    @property
    def freight_customer(self):
        party = self.freight_customer_party_type
        if party:
            return getattr(self, party, None)

    @property
    def freight_customer_party_type(self):
        if self.is_seller_delivery_onus:
            return 'seller'
        if self.is_buyer_delivery_onus:
            return 'buyer'

    def is_freight_customer(self, user):
        party = self.freight_customer_party_type
        if party and user:
            return getattr(self, 'is_' + party)(user)

    @property
    def delivery_onus_company_id(self):
        if self.__is_party_delivery_onus('buyer'):
            return self.buyer.company_id
        elif self.__is_party_delivery_onus('seller'):
            return self.seller.company_id

    @property
    def is_seller_delivery_onus(self):
        return self.__is_party_delivery_onus('seller')

    @property
    def is_buyer_delivery_onus(self):
        return self.__is_party_delivery_onus('buyer')

    def __is_party_delivery_onus(self, party):
        return self.delivery_onus and party and self.delivery_onus.lower() == party.lower()

    @cached_property
    def pickup_site_name(self):
        EMPTY = ''
        name = None
        consignor = self.consignors.first()
        if consignor:
            name = consignor.site_name
        return name or EMPTY

    @cached_property
    def pickup_site_address(self):
        EMPTY = ''
        address = None
        consignor = self.consignors.first()
        if consignor:
            address = consignor.site_address
        return address or EMPTY

    @cached_property
    def delivery_site_address(self):
        EMPTY = ''
        address = None
        consignee = self.consignees.first()
        if consignee:
            address = consignee.site_address
        return address or EMPTY

    @property
    def seller_brokerage_invoiced(self):
        if self.persisted and self.excluded_contract.filter(type=constants.SELLER_INVOICE).exists():
            return True

        return self.brokerage_invoice_exists_for_party(BROKERAGE_TYPE_SELLER)

    def brokerage_invoice_exists_for_party(self, party):
        party_brokerage = self.__get_brokerage(party)
        if party_brokerage:
            contract_party = self.seller if party == BROKERAGE_TYPE_SELLER else self.buyer
            if party_brokerage.is_post_delivery:
                return self.is_completed() and self.all_freight_contracts_brokerage_invoiced(
                    contract_party
                ) and not self.__has_any_brokerage_uninvoiced_completed_title_transfer(contract_party)
            else:
                return self.is_brokerage_invoice_exists(contract_party)
        return False

    @property
    def has_raised_brokerage_invoice(self):
        return self.is_brokerage_invoice_exists(self.seller) or self.is_brokerage_invoice_exists(self.buyer)

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def is_brokerage_invoice_exists(self, party):
        contract_content_type_id = BaseModel.content_type_ids_for(
            model_names=['contract']
        )[0]
        from core.invoices.models import InvoiceItem
        invoice_ids = InvoiceItem.objects.filter(
            item_id=self.id, item_type_id=contract_content_type_id, is_rejected=False
        ).values_list('invoice_id', flat=True)
        from core.invoices.models import Invoice
        return Invoice.objects.filter(
            type='Brokerage',
            id__in=invoice_ids,
            payer__company_id=party.company_id,
        ).exclude(status__in=INVOICE_NULL_STATUSES).exists()

    @property
    def buyer_brokerage_invoiced(self):
        if self.excluded_contract.filter(type=constants.BUYER_INVOICE).exists():
            return True

        return self.brokerage_invoice_exists_for_party(BROKERAGE_TYPE_BUYER)

    @cached_property
    def rejection_reason(self):
        request = self.acceptance_requests.filter(
            type=CONTRACT_ACCEPTANCE_REQUEST_TYPE_CONFIRM,
            accepted=False,
            resolved=True,
        ).last()
        if request:
            return request.rejection_reason

    @cached_property
    def display_name(self):
        return CONTRACT_DISPLAY_NAME.format(
            reference_no=self.reference_number, commodity=self.commodity.display_name, grade=get_grade_name(self),
            tonnage=self.inferred_tonnage,
            unit=self.inferred_tonnage_unit
        )

    def display_name_with_counter_party(self, user):
        return CONTRACT_COUNTER_PARTY_DISPLAY_NAME.format(
            reference_no=self.reference_number,
            commodity=self.commodity.display_name,
            grade=get_grade_name(self),
            tonnage=self.inferred_tonnage,
            unit=self.inferred_tonnage_unit,
            party=self.counter_party_name(user)
        )

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def commodity_contract_invoice_display_name(self, user):
        party = self.counter_party(user)
        return constants.CC_INVOICE_DISPLAY_NAME.format(
            reference_number=self.reference_number,
            grade=get_grade_name(self),
            tonnage=self.inferred_tonnage,
            unit=self.inferred_tonnage_unit,
            party=get(party, 'company_name'),
            delivery_start_date=self.delivery_start_date.strftime(
                Country.get_country_format('date')) if self.delivery_start_date else ''
        )

    @cached_property
    def brokerage_invoice_display_name(self):
        return constants.BROKERAGE_INVOICE_DISPLAY_NAME.format(
            reference_number=self.reference_number,
            grade=get_grade_name(self),
            tonnage=self.inferred_tonnage,
            unit=self.inferred_tonnage_unit,
            delivery_start_date=self.delivery_start_date.strftime(
                Country.get_country_format('date')
            ) if self.delivery_start_date else ''
        )

    @property
    def site_type_by_price_point(self):
        if self.price_point_id in self.consignor_optional_price_points:
            return 'delivery'
        if self.price_point_id in self.consignor_mandatory_price_points:
            return 'pickup'

    @property
    def consignee_mandatory_price_points(self):
        return CONSIGNEE_MANDATORY_PRICE_POINTS

    @property
    def is_consignee_mandatory(self):
        return self.price_point_id in self.consignee_mandatory_price_points

    @property
    def is_consignor_mandatory(self):
        return self.price_point_id in self.consignor_mandatory_price_points

    @property
    def consignor_optional_price_points(self):
        return CONSIGNOR_OPTIONAL_PRICE_POINTS

    @property
    def consignor_mandatory_price_points(self):
        return CONSIGNOR_MANDATORY_PRICE_POINTS

    @property
    def is_unique_contract_number(self):
        return self.check_contract_number_unique(self.contract_number)

    @property
    def is_unique_numbers(self):
        return self.check_identifier_unique(self.identifier) and (
                not self.contract_number or self.check_contract_number_unique(self.contract_number)
        )

    @classmethod
    def check_contract_number_unique_for_user(cls, contract_number, user):
        if not contract_number:
            return True
        return Contract.objects.exclude(status='void').filter(
            contract_number__iexact=contract_number,
            viewer_company_ids__contains=[user.company_id]
        )

    def check_contract_number_unique(self, contract_number):
        if not contract_number:
            return True
        return not self.get_others_by_contract_number(
            contract_number
        ).exists()

    def get_others_by_contract_number(self, contract_number):
        exclusion_criteria = models.Q(status='void')
        if self.persisted:
            exclusion_criteria |= models.Q(id=self.id)
        return Contract.objects.exclude(
            exclusion_criteria
        ).filter(
            contract_number__iexact=contract_number,
            viewer_company_ids__overlap=self.get_viewer_company_ids_without_system()
        )

    def check_identifier_unique(self, identifier):
        if not identifier:
            return True
        return not self.get_others_by_identifier(identifier).exists()

    def get_others_by_identifier(self, identifier):
        exclusion_criteria = models.Q(status='void')
        if self.persisted:
            exclusion_criteria |= models.Q(id=self.id)
        return Contract.objects.exclude(
            exclusion_criteria
        ).filter(
            identifier__iexact=identifier,
            viewer_company_ids__overlap=self.get_viewer_company_ids_without_system()
        )

    @property
    def rejection_reason_display(self):
        request = self.acceptance_requests.filter(resolved=True, accepted=False).last()
        if request:
            return {'rejection_reason': request.rejection_reason, 'action': request.type.title()}
        return {}

    def is_sale_contract(self, user=None):
        if user:
            return self.is_seller(user) and self.purchasecontract_set.exists()
        else:
            return self.purchasecontract_set.exists()

    def is_purchase_contract(self, user=None):
        if user:
            return self.is_buyer(user) and self.salecontract_set.exists()
        else:
            return self.salecontract_set.exists()

    @property
    def is_allocated_contract(self):
        return self.is_purchase_contract() or self.is_sale_contract()

    def get_allocated_tonnage_against_purchase_contract(self, purchase_contract):
        """Tonnage allocated against from a purchase contract"""
        return float(get(self.purchasecontract_set.filter(purchase=purchase_contract), '0.tonnage') or 0)

    @staticmethod
    def get_allocated_planned_tonnage(purchase_contract, sale_contract):
        """All orders planned_tonnage created between sale and purchase contracts"""
        allocated_orders = Contract.get_allocated_orders(purchase_contract, sale_contract)
        return allocated_orders.aggregate(tonnage=Sum('planned_tonnage'))['tonnage'] or 0

    @staticmethod
    def get_allocated_orders(purchase_contract, sale_contract, get_ids=False):
        """All orders of purchase contracts linked to sale contract directly"""
        from core.freights.models import FreightOrder, FreightPickup
        sale_contract_orders = sale_contract.freight_orders.exclude(
            status__in=['rejected', 'void']).filter(parent_order__isnull=True)

        sale_contract_freight_pickups = FreightPickup.objects.filter(pickup_freight_order__in=sale_contract_orders)
        sale_contract_pickup_orders = sale_contract_freight_pickups.values_list('pickup_orders', flat=True)
        sale_contract_pickup_order_number = sale_contract_freight_pickups.values_list('order_number', flat=True)

        allocated_order_ids = FreightOrder.objects.filter(commodity_contract=purchase_contract).filter(
            Q(parent_order__isnull=True) | Q(parent_order__type_id=3)
        ).filter(
            Q(freight_delivery__delivery_orders__in=sale_contract_orders) |
            Q(freight_pickup__pickup_orders__in=sale_contract_orders) |
            Q(id__in=sale_contract_pickup_orders, identifier__in=sale_contract_pickup_order_number)
        ).values_list('id', flat=True)
        if get_ids:
            return allocated_order_ids

        return FreightOrder.objects.filter(id__in=allocated_order_ids)

    def get_contracted_tonnage_against_sale_contract(self, sale_contract):
        """Tonnage promised for a sale contract"""
        return float(get(self.salecontract_set.filter(sale=sale_contract), '0.tonnage') or 0)

    def allocated_tonnage_for_user(self, user):
        if self.is_seller(user) or get(user, 'company.is_system'):
            return self.total_allocated_tonnage
        if self.is_buyer(user):
            return self.total_contracted_tonnage

    def can_view_vendor_dec(self, user):
        if user.is_staff:
            return True
        if self.is_purchase_contract(user):
            return self.is_seller_delivery_onus or self.is_consignee_mandatory or self.is_buyer(user)
        if self.is_sale_contract(user):
            return self.is_buyer_delivery_onus or self.is_consignor_mandatory or self.is_seller(user)
        return True

    def counter_party_order_creation_restricted(self, user):
        if self.is_sale_contract(user):
            sm_settings = get(self.counter_party(user), 'company.sitemanagementsettings')
            return get(sm_settings, 'delivery_order_number_required')
        if self.is_purchase_contract(user):
            sm_settings = get(self.counter_party(user), 'company.sitemanagementsettings')
            return get(sm_settings, 'pickup_order_number_required')
        return False

    def get_grain_orders_against_sale_contract(self):
        from core.freights.models import FreightOrder
        from core.freights.constants import CALL_ON_GRAIN_TYPE_ID
        if self.is_sale_contract():
            return self.freight_orders.filter(type_id=CALL_ON_GRAIN_TYPE_ID)
        return FreightOrder.objects.none()

    def get_active_grain_orders_against_sale_contract(self):
        return self.get_grain_orders_against_sale_contract().exclude(status=VOID_STATUS).exclude(is_auto_created=True)

    def get_unfinished_grain_orders_against_sale_contract(self):
        return self.get_active_grain_orders_against_sale_contract().filter(unaccounted_tonnage__gt=0)

    @property
    def has_unfinished_grain_orders_against_sale_contract_with_tonnage(self):
        return self.get_unfinished_grain_orders_against_sale_contract().exists()

    def unallocated_tonnage_for_user(self, user):
        if self.is_seller(user) or get(user, 'company.is_system'):
            return self.unallocated_tonnage
        if self.is_buyer(user):
            return self.uncontracted_tonnage

    @property
    def uncontracted_tonnage(self):
        """For Purchase contract"""
        return round(float(self.tonnage) - float(self.total_contracted_tonnage), 2)

    @property
    def unallocated_tonnage(self):
        """For Sale contract"""
        return round(float(self.tonnage) - float(self.total_allocated_tonnage), 2)

    @cached_property
    def total_allocated_tonnage(self):
        """Tonnage allocated against from all purchase contracts"""
        return round(
            float(self.purchasecontract_set.aggregate(allocated_tonnage=Sum('tonnage'))['allocated_tonnage'] or 0) +
            float(self.stock_allocations.aggregate(allocated_tonnage=Sum('tonnage'))['allocated_tonnage'] or 0) +
            float(self.throughwarehousepurchasecontract_set.aggregate(
                allocated_tonnage=Sum('tonnage')
            )['allocated_tonnage'] or 0), 2
        )

    @property
    def total_through_warehouse_allocation_tonnage(self):
        return round(
            float(
                self.throughwarehousepurchasecontract_set.aggregate(
                    through_warehouse_tonnage=Sum('tonnage')
                )['through_warehouse_tonnage'] or 0), 2
        )

    @cached_property
    def total_contracted_tonnage(self):
        """Tonnage promised for all sale contracts"""
        return round(
            float(self.salecontract_set.aggregate(allocated_tonnage=Sum('tonnage'))['allocated_tonnage'] or 0) +
            float(self.throughwarehousesalecontract_set.aggregate(
                allocated_tonnage=Sum('tonnage')
            )['allocated_tonnage'] or 0), 2
        )

    @cached_property
    def request_reason_display(self):
        request = self.acceptance_requests.filter(type=CONTRACT_ACCEPTANCE_REQUEST_TYPE_VOID).last()
        if request:
            return {'request_reason': request.request_reason, 'action': request.type.title()}
        return {}

    @cached_property
    def description(self):
        return self.base_description + ', ' + self.price_point.display_name

    @property
    def base_description(self):
        description = self.commodity.display_name + ', ' + get_grade_name(self)
        if self.season and self.season != core.common.constants.SEASON_NA:
            description += ', ' + self.season
        return description

    @cached_property
    def invoice_item_description(self):
        return "{description}, {price}".format(
            description=self.base_description,
            price=format_price(self.price)
        )

    @cached_property
    def related_invoiced_items(self):  #  -- probably dead code
        contract_content_type_id = self.content_type_ids_for(model_names=[CONTRACT_MODEL])[0]
        invoice_item_class = apps.get_model('invoices', 'InvoiceItem')
        return BaseModel.qs2dict(invoice_item_class.objects.filter(
            item_id=self.id, item_type_id=contract_content_type_id, invoice__status__in=INVOICE_ACCEPTED_STATUSES,
            is_rejected=False, invoice__type='Brokerage'
        ), one_to_one_relations=['invoice__payer__company'])

    def number_of_carry_periods(self, to_date):
        if not (self.carry_end_date and self.carry_start_date) or not to_date:
            return 0
        carry_start_date = self.carry_start_date
        carry_end_date = self.carry_end_date
        carry_frequency = self.carry_frequency

        carry_start_date = carry_start_date if isinstance(carry_start_date, datetime) else \
            DateTimeUtil.convert_date_to_datetime(carry_start_date)
        carry_end_date = carry_end_date if isinstance(carry_end_date, datetime) else \
            DateTimeUtil.convert_date_to_datetime(carry_end_date)
        to_date = to_date if isinstance(to_date, datetime) else DateTimeUtil.convert_date_to_datetime(to_date)
        to_date = min(carry_end_date, to_date)

        if carry_frequency == MONTHLY:
            number_of_periods = DateTimeUtil.months_delta(carry_start_date, to_date)
        else:
            number_of_days = (to_date - carry_start_date).days + 1
            carry_days = FREQUENCY_TO_PERIOD_MAP[carry_frequency]

            number_of_periods = math.ceil(number_of_days / carry_days)

        return number_of_periods if number_of_periods >= 0 else 0

    def last_carry_calculation_date(self, to_date):
        carry_start_date = self.carry_start_date
        carry_end_date = self.carry_end_date
        carry_frequency = self.carry_frequency

        carry_start_date = carry_start_date if isinstance(carry_start_date, datetime) else \
            DateTimeUtil.convert_date_to_datetime(carry_start_date)
        carry_end_date = carry_end_date if isinstance(carry_end_date, datetime) else \
            DateTimeUtil.convert_date_to_datetime(carry_end_date)
        to_date = to_date if isinstance(to_date, datetime) else DateTimeUtil.convert_date_to_datetime(to_date)

        if carry_end_date <= to_date:
            last_calculation_date = carry_end_date
        else:
            carry_periods = self.number_of_carry_periods(to_date)
            carry_days = FREQUENCY_TO_PERIOD_MAP[carry_frequency]
            last_calculation_date = carry_start_date + timedelta(days=carry_days * carry_periods)

        return last_calculation_date.date().strftime(Country.get_country_format('date'))

    def has_buyer(self):
        return self._has_party('buyer')

    def has_seller(self):
        return self._has_party('seller')

    def _has_party(self, party):
        return getattr(self, party + '_id', False) and deepgetattr(self, party + '.company_id')

    def brokerages_info(self, user):
        brokerages = self.__brokerages(user)

        if len(brokerages) == 0:
            return {'payble_by': 'Nil'}
        if len(brokerages) == 1:
            return brokerages[0].info()
        if len(brokerages) == 2:
            return {
                **brokerages[0].info(),
                **brokerages[1].info(),
                'payble_by': 'Seller And Buyer'
            }

    def brokerages_for_user(self, user):
        brokerages = self.__brokerages(user)
        return self.qs2dict(queryset=brokerages) if brokerages else []

    def __brokerages(self, user):
        brokerages = None
        can_view_seller_brokerage = self.can_view_seller_brokerage(user)
        can_view_buyer_brokerage = self.can_view_buyer_brokerage(user)
        if can_view_seller_brokerage and can_view_buyer_brokerage:
            brokerages = self.brokerage_set.all()
        elif can_view_seller_brokerage:
            brokerage = self.__get_brokerage(BROKERAGE_TYPE_SELLER)
            brokerages = [brokerage] if brokerage else None
        elif can_view_buyer_brokerage:
            brokerage = self.__get_brokerage(BROKERAGE_TYPE_BUYER)
            brokerages = [brokerage] if brokerage else None
        return brokerages or []

    def is_creator(self, user):
        return user and (
            self.is_direct_creator_company_user(user) or self.is_broker(user)
        )

    def is_creator_party(self, user):
        external_system = get(self.external_args, 'system')
        external_system_party = get(self.external_args, 'party')
        if external_system and external_system_party:
            return self.is_buyer(user) if external_system_party == 'buyer' else self.is_seller(user)

        return self.is_creator(user) or (self.created_by.company.is_broker and
                                         (self.is_seller(user) and self.is_representing_seller(self.created_by)) or
                                         (self.is_buyer(user) and self.is_representing_buyer(self.created_by)))

    def can_view_seller_brokerage(self, user):
        return user and (self.is_creator(user) or self.is_seller(user) or user.is_staff)

    def can_view_buyer_brokerage(self, user):
        return user and (self.is_creator(user) or self.is_buyer(user) or user.is_staff)

    def confirmable(self, user):
        return user and self.is_planned_and_can_confirm_or_reject(user)

    def can_raise_void_request(self, user):
        return user and self.status in self.VOIDABLE_STATUES and self.is_party(user) and \
           not self.is_void_request_pending and (
               not self.is_planned() or self.is_creator_party(user)
           )

    def cannot_raise_void_request_reasons(self, user):
        reasons = []
        is_party = self.is_party(user)

        if self.status not in self.VOIDABLE_STATUES:
            reasons.append(constants.CONTRACT_STATUS_NOT_VOIDABLE)

        elif not is_party:
            reasons.append(constants.USER_NOT_AMONGST_THE_CONTRACT_PARTY)

        elif self.is_void_request_pending:
            reasons.append(constants.CONTRACT_HAS_PENDING_VOID_REQUEST)

        if self.is_amend_request_pending:
            reasons.append(constants.CONTRACT_HAS_PENDING_AMEND_REQUEST)

        if self.is_planned() and not self.is_creator_party(user):
            reasons.append(constants.USER_NOT_THE_CREATOR_OF_THE_CONTRACT)

        return reasons

    def can_raise_amend_request(self, user):
        if self.status_display_name(user) in constants.CONTRACT_EDIT_STATUSES:
            return self.can_edit(user)

        return user and self.status in self.AMENDABLE_STATUSES and self.is_party(user) and \
               not self.is_amend_request_pending

    def cannot_raise_amend_request_reasons(self, user):
        if get(user, 'is_superuser'):
            return []
        reasons = []
        status_display_name = self.status_display_name(user)
        is_creator_party = self.is_creator_party(user)
        is_party = self.is_party(user)
        if not is_creator_party and status_display_name == constants.STATUS_AWAITING_CONFIRMATION:
            reasons.append(constants.CONTRACT_HAS_PENDING_CONFIRMATION_REQUEST)

        if self.is_amend_request_pending:
            reasons.append(constants.CONTRACT_HAS_PENDING_AMEND_REQUEST)

        if self.is_void_request_pending:
            reasons.append(constants.CONTRACT_HAS_PENDING_VOID_REQUEST)

        creator_company = self.created_by.company
        if (
            user and user.company_id != creator_company.id and
            creator_company.only_creator_can_amend_contract
        ):
            reasons.append(constants.USER_NOT_THE_CREATOR_OF_THE_CONTRACT)

        if status_display_name in constants.CONTRACT_EDIT_STATUSES:
            if self.status in CONTRACT_NON_EDITABLE_STATUSES:
                reasons.append(constants.CONTRACT_STATUS_AMONG_NON_EDITABLE_STATUS)

            elif not is_creator_party:
                reasons.append(constants.USER_NOT_THE_CREATOR_OF_THE_CONTRACT)

        else:
            if self.status not in self.AMENDABLE_STATUSES:
                reasons.append(constants.CONTRACT_STATUS_NOT_AMONG_NON_AMENDABLE_STATUS)

            elif not is_party:
                reasons.append(constants.USER_NOT_AMONGST_THE_CONTRACT_PARTY)

        return list(set(reasons))

    def can_mark_paid(self, user):
        if self.status == INVOICED:
            return self.is_eligible_to_mark_paid(user)
        return False

    def is_eligible_to_mark_paid(self, user):
        if user:
            return False
        return self.is_buyer(user) or (not self.is_party_registered(self.buyer) and self.is_seller(user))

    def voidable(self, user):
        return user and self.can_user_void(user)

    def amendable(self, user):
        return user and self.can_user_amend(user)

    def is_highlighted(self, user):
        return self.confirmable(user) or self.amendable(user) or self.voidable(user)

    def party(self, user):
        party = None
        if user:
            if self.is_seller(user):
                party = 'seller'
            elif self.is_buyer(user):
                party = 'buyer'

        return party

    @cached_property
    def outstanding_tonnage(self):
        return round(float(self.inferred_tonnage) - self.total_delivered_tonnage, 2)

    @property
    def remaining_tonnage(self):
        return round(self.outstanding_tonnage - self.progress_tonnage, 2)

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def is_manually_completed(self):
        return self.freight_contracts.filter(status='manual_contract_complete_balanced').exists()

    @cached_property
    def maximum_tonnage(self):
        return float("%0.2f" % (self.expected_tonnage[1] - self.accounted_tonnage))

    @cached_property
    def minimum_tonnage(self):
        return float("%0.2f" % (self.expected_tonnage[0] - self.accounted_tonnage))

    @cached_property
    def minimum_allowed_tonnage(self):
        accounted_tonnage = self.accounted_tonnage
        return float(
            "%0.2f" % (self.tolerance.get_range(accounted_tonnage, self.commodity)[0]))

    @cached_property
    def invoiced_tonnage(self):
        return float("%0.2f" % (self.all_invoiced_title_transfer_tonnage() + self.all_invoiced_movements_tonnage()))

    @cached_property
    def is_enough_to_mark_paid(self):
        return self.is_invoiced() and not self.valid_invoices_excluding_paid().exists()

    def valid_invoices_excluding_paid(self):
        return self.invoice_set.filter(type='Commodity Contract').exclude(status__in=['paid', 'rejected', 'void'])

    def all_invoiced_movements_tonnage(self, grade_id=None):
        freight_contracts = self.all_freight_contracts().filter(status=COMPLETED_STATUS)
        load_type = 'inloads' if self.is_inspecting_at_delivery() else 'outloads'
        return float(
            '%0.2f' % sum(
                float(
                    (get(
                        get(freight, load_type).filter(grade_id=grade_id).first(), 'estimated_net_weight'
                    ) if grade_id else freight.inferred_tonnage) or 0
                ) for freight in freight_contracts if freight.is_commodity_contract_invoiced_for_contract(self.id)
            )
        ) or 0.0

    def all_invoiced_title_transfer_tonnage(self):
        tonnage_attr = 'tonnage'
        if self.is_strict_quantity_based_commodity:
            tonnage_attr = 'quantity'

        return self.titletransfer_set.filter(
            is_active=True,
            status__in=['invoiced']
        ).aggregate(
            tonnage=models.Sum(tonnage_attr)
        )['tonnage'] or 0

    @cached_property
    def accounted_tonnage(self):
        if self.unaccounted_tonnage is None:
            return 0

        return float("%0.2f" % (self.expected_tonnage[1] - self.unaccounted_tonnage))

    @property
    def direct_pack_orders(self):
        return self.freight_orders.filter(parent_order_id=None, type_id=PACK_ORDER_TYPE_ID).exclude(
            status__in=NULL_FREIGHT_STATUSES
        )

    @property
    def pack_orders_tonnage(self):
        tonnage_attr = 'planned_tonnage'
        if self.is_strict_quantity_based_commodity:
            tonnage_attr = 'quantity'
        return self.direct_pack_orders.aggregate(
            total_tonnage=models.Sum(tonnage_attr)
        )['total_tonnage'] or 0

    @property
    def unpacked_tonnage(self):
        return float("%0.2f" % (self.expected_tonnage[1] - self.pack_orders_tonnage))

    @property
    def does_seller_has_registered_broker(self):
        return self.party_has_registered_broker(self.seller)

    @property
    def consignees_names(self):
        return self.__handlers_names('Consignee')

    @property
    def consignors_names(self):
        return self.__handlers_names('Consignor')

    @property
    def delivery_sites_names(self):
        return self.__handlers_sites_names('Consignee')

    @property
    def pickup_sites_names(self):
        return self.__handlers_sites_names('Consignor')

    def __handlers_names(self, role):
        handlers = self.handlers(role=role).select_related('handler')
        return ', '.join([handler.display_name or '' for handler in handlers])

    def __handlers_sites_names(self, role):
        handlers = self.handlers(role=role).prefetch_related(
            'site_set__location'
        )
        sites_names = []
        for handler in handlers:
            for site in handler.site_set.all():
                sites_names.append(site.name_with_ld)

        return ', '.join(compact(sites_names))

    def handlers(self, role):
        return self.contractcommodityhandler_set.filter(role=role)

    @property
    def consignors(self):
        return self.handlers(core.common.constants.CONSIGNOR_ROLE)

    @property
    def consignees(self):
        return self.handlers(core.common.constants.CONSIGNEE_ROLE)

    @property
    def consignees_with_sites(self):
        handler_q_s = self.contractcommodityhandler_set.select_related(
            'handler'
        ).filter(
            role='Consignee'
        ).order_by('position')
        data = self.qs2dict(queryset=handler_q_s, one_to_one_relations=['handler__address'], )
        for consignee in data:
            if 'handler' in consignee:
                site = Farm.objects.get(id=consignee['handler']['id'])
                consignee['handler'] = {
                    **consignee['handler'],
                    'is_overdraft_allowed': site.is_overdraft_allowed,
                    'is_bhc': site.is_bhc
                }
        return data

    @property
    def consignors_with_sites(self):
        handler_q_s = self.consignors.select_related(
            'handler'
        ).order_by('position')
        data = self.qs2dict(queryset=handler_q_s, one_to_one_relations=['handler__address'], )
        for consignor in data:
            if 'handler' in consignor:
                site = Farm.objects.get(id=consignor['handler']['id'])
                consignor['handler'] = {
                    **consignor['handler'],
                    'is_overdraft_allowed': site.is_overdraft_allowed,
                    'is_bhc': site.is_bhc
                }
        return data

    @property
    def does_buyer_has_registered_broker(self):
        return self.party_has_registered_broker(self.buyer)

    def status_display_name(self, user=None):
        if self.persisted and user and self.is_planned():
            if (
                    self.is_amend_request_pending and self.can_user_amend(user)
            ) or self.can_confirm_or_reject(user):
                return constants.STATUS_ACTION_PENDING
            return constants.STATUS_AWAITING_CONFIRMATION

        return to_display_attr(constants.STATUSES, self.status)

    @staticmethod
    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def party_has_registered_broker(party):
        return get(party, 'represented_by.transaction_participation')

    def _has_confirmation_mandatory_attributes(self):
        return all(attr is not None for attr in [self.id])

    def _has_rejection_mandatory_attributes(self):
        return all(attr is not None for attr in [self.id])

    @property
    def buyer_company_id(self):
        return get(self.buyer, 'company_id')

    @property
    def seller_company_id(self):
        return get(self.seller, 'company_id')

    @property
    def epr_value(self):
        value = None
        tonnage = self.inferred_tonnage
        if self.variety_id and tonnage:
            epr = self.variety.epr
            if epr:
                value = float("%0.2f" % (epr['rate'] * tonnage))

        return value

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def is_buyer(self, user):
        return get(
            user, 'company_id'
        ) and (
            self.buyer_company_id == user.company_id or self.is_buyer_managed_by_user(user)
        )

    def is_buyer_rcti(self, user):
        if self.buyer_company_id == user.company_id:
            return True
        elif self.is_buyer_managed_by_user(user):
            if not self.is_seller_managed_by_user(user) and self.seller_company_id != user.company_id:
                return True
            else:
                return self.is_invoicing_buyer_rcti()
        return False

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def is_broker(self, user):
        return user.company_id == get(self, 'administration.brokered_by_id')

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def is_seller(self, user):
        return get(
            user, 'company_id'
        ) and (
            self.seller_company_id == user.company_id or self.is_seller_managed_by_user(user)
        )

    def is_consignor_not_bhc(self):
        return not self.consignors.filter(handler__mode__isnull=False).exists()

    def is_consignee_not_bhc(self):
        return not self.consignees.filter(handler__mode__isnull=False).exists()

    def is_consignee(self, user):
        return get(user, 'company_id') and self.consignees.filter(handler__company_id=user.company_id).exists()

    def is_consignor(self, user):
        return get(user, 'company_id') and self.consignors.filter(handler__company_id=user.company_id).exists()

    def is_consignor_registered(self):
        return self.consignors.filter(handler__company__transaction_participation=True)

    def is_consignee_registered(self):
        return self.consignees.filter(handler__company__transaction_participation=True)

    def is_invoicing_buyer_rcti(self):
        return self.administration.invoicing == BUYER_RCTI_INVOICING

    def is_invoicing_seller_to_buyer_or_broker_to_buyer(self):
        return self.administration.invoicing in [SELLER_TO_INVOICE_BUYER, BROKER_TO_INVOICE_BUYER]

    def user_has_delivery_onus(self, user):
        if self.is_both_parties_managed_by(user=user):
            return True

        user_party = self.user_party(user)
        return user_party and user_party.role.lower() == self.delivery_onus.lower()

    @staticmethod
    def can_create_cog_criteria(user):
        return models.Q(
            models.Q(models.Q(status=PLANNED_STATUS) & Contract.is_creator_criteria(user)) |
            models.Q(status__in=Contract.CONFIRMED_TO_DELIVERED_STATUSES, unaccounted_tonnage__gt=0,
                     contract_number__isnull=False)
        ) & models.Q(
            Contract.no_pending_requests_criteria()
        ) & models.Q(
            models.Q(Contract.seller_criteria(user) & ~models.Q(delivery_onus__iexact=DELIVERY_ONUS_SELLER)) |
            models.Q(Contract.buyer_criteria(user) & ~models.Q(delivery_onus__iexact=DELIVERY_ONUS_BUYER))
        )

    @staticmethod
    def can_create_pack_criteria(user, include_price_point=True):
        criteria = models.Q(
            models.Q(models.Q(status=PLANNED_STATUS) & Contract.is_creator_criteria(user)) |
            models.Q(status__in=Contract.CONFIRMED_TO_DELIVERED_STATUSES, unaccounted_tonnage__gt=0,
                     contract_number__isnull=False)
        ) & models.Q(
            Contract.no_pending_requests_criteria()
        ) & models.Q(
            models.Q(Contract.buyer_criteria(user) & ~models.Q(delivery_onus__iexact=DELIVERY_ONUS_BUYER)) |
            models.Q(Contract.seller_criteria(user) & ~models.Q(delivery_onus__iexact=DELIVERY_ONUS_SELLER))
        )
        if include_price_point:
            criteria &= models.Q(price_point_id__in=CONSIGNEE_MANDATORY_PRICE_POINTS)
        return criteria

    @staticmethod
    def seller_criteria(user):
        return models.Q(
            models.Q(seller__company_id=user.company_id) |
            models.Q(seller__represented_by_id=user.company_id)
        )

    @staticmethod
    def buyer_criteria(user):
        return models.Q(
            models.Q(buyer__company_id=user.company_id) |
            models.Q(buyer__represented_by_id=user.company_id)
        )

    @staticmethod
    def no_pending_requests_criteria():
        return ~models.Q(
            acceptance_requests__type__in=['void', 'amend'],
            acceptance_requests__resolved=False
        )

    @staticmethod
    def is_creator_criteria(user):
        return models.Q(
            models.Q(created_by_id=user.id) |
            models.Q(created_by__company_id=user.company_id) |
            models.Q(administration__brokered_by_id=user.company_id)
        )

    def user_has_conveyance(self, user):
        if self.is_both_parties_managed_by(user=user):
            return True

        user_party = self.user_party(user)
        return user_party and self.conveyance_id in USER_CONVEYANCES.get(user_party.role.lower())

    def is_seller_managed_by_user(self, user):
        return self.is_representing_seller(user)

    def is_buyer_managed_by_user(self, user):
        return self.is_representing_buyer(user)

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def is_direct_creator_company_user(self, user):
        return user and (user.id == self.created_by_id or self.created_by.company_id == user.company_id)

    def is_representing_seller(self, user):
        return self._is_representing_party(user, 'seller')

    def is_representing_buyer(self, user):
        return self._is_representing_party(user, 'buyer')

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def _is_representing_party(self, user, party):
        return get(user, 'company_id') and get(self, f'{party}.represented_by_id') == user.company_id

    def counter_party(self, user):
        counter_party = None
        if self.is_seller(user):
            counter_party = self.buyer
        elif self.is_buyer(user):
            counter_party = self.seller

        return counter_party

    def counter_party_name(self, user):
        return get(self.counter_party(user), 'company_name')

    def user_party(self, user):
        return self.buyer if self.is_buyer(user) else self.seller if self.is_seller(user) else None

    def get_relevant_notification_recipients(self, user):
        user_party_broker_admin = None
        counter_party_broker_admin = None
        counter_party = self.counter_party(user)
        user_party = self.user_party(user)
        counter_party_has_broker = self.party_has_registered_broker(counter_party)
        user_party_has_broker = self.party_has_registered_broker(user_party)
        counter_party_admin = counter_party.company.admin() if counter_party else None
        user_party_admin = user_party.company.admin() if user_party else None
        if counter_party_has_broker:
            counter_party_broker_admin = counter_party.represented_by.admin()
        if user_party_has_broker:
            user_party_broker_admin = user_party.represented_by.admin()

        broker_contact = self.broker_contact
        return {
            RECIPIENTS_SELF_KEY: {
                RECIPIENTS_CONTACT_KEY: user_party.contact if (
                    user_party and user_party.contact != user
                ) else None,
                RECIPIENTS_ADMIN_KEY: user_party_admin if (
                        user_party_has_broker and user_party_admin
                        and user_party_admin != user
                        and user_party_admin != user_party.contact
                ) else None,
                RECIPIENTS_BROKER_KEY: user_party_broker_admin if user_party_broker_admin != user else None,
                RECIPIENTS_OFFICE_ADMIN_KEY: broker_contact if broker_contact != user else None
            },
            RECIPIENTS_COUNTER_PARTY_KEY: {
                RECIPIENTS_CONTACT_KEY: counter_party.contact if (
                    counter_party and counter_party.contact
                ) else None,
                RECIPIENTS_ADMIN_KEY: counter_party_admin if (
                    counter_party_has_broker and counter_party_admin
                    and counter_party_admin != counter_party.contact
                ) else None,
                RECIPIENTS_BROKER_KEY: counter_party_broker_admin if counter_party_broker_admin != user else None,
            }
        }

    def send_notification_for_request(self, request_type, action, user, data=None):
        relevant_recipients = self.get_relevant_notification_recipients(user)
        func_args = {
            'sender': user,
            'verb': action,
            'action_object': self,
            'entity': 'contract',
            'app': 'contracts'
        }

        for party, party_values in relevant_recipients.items():
            recipients = list(party_values.values())
            filter_recipients = [el for el in list(set(flatten(recipients))) if el is not None]
            if request_type == CONTRACT_ACCEPTANCE_REQUEST_TYPE_TITLE_TRANSFER and \
                    action in (constants.REQUEST, PROCESSED):
                msg = REQUEST_NOTIFICATION_MESSAGE_MAP[request_type][action][party].format(
                    employee=user.name,
                    company=user.company.name,
                    contract_no=self.reference_number,
                    title_transfer_no=data['title_transfer_no'],
                    site_name=data['site_name'],
                    commodity=data['commodity'],
                    tonnage=data['tonnage'],
                    unit=self.inferred_tonnage_unit,
                )
                func_args.update({
                    'app': 'title_transfer',
                    'description': msg,
                    'recipient': filter_recipients
                })
            else:
                msg = REQUEST_NOTIFICATION_MESSAGE_MAP[request_type][action][party].format(
                    employee=user.name,
                    company=user.company.name,
                    contract_no=self.reference_number
                )
                func_args.update({
                    'description': msg,
                    'recipient': filter_recipients
                })
            notify.send(**func_args)
            args = {
                'notification_type': NOTIFICATION_TYPE_CONTRACT_ID,
                'message_txt': msg,
                'ios_msg_title': 'Title Transfer',
                'extra': {'ac_notification_id': self.id, 'ac_notification_type': 'contract'}
            }
            employee_ids = [o.id for o in filter_recipients]
            MobilePushNotification.notify_mobile_devices(employee_ids, args)

    @staticmethod
    def has_x_hours_passed(_datetime):
        return timezone.now() > _datetime + timedelta(hours=constants.ACCEPTANCE_PERIOD_IN_HOURS)

    def is_counter_party_registered(self, user):
        return self.is_counter_party_participating_in_transaction(user)

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def is_counter_party_participating_in_transaction(self, user):
        counter_party = self.counter_party(user)
        return deepgetattr(counter_party, 'is_participating_in_transaction')

    @staticmethod
    def is_party_registered(party):
        return party and party.is_registered_or_managed_by_registered

    def is_delivery_onus_registered(self):
        if self.delivery_onus.lower() == 'seller':
            return self.is_party_registered(self.seller)
        return self.is_party_registered(self.buyer)

    def can_user_confirm(self, user):
        if not self._has_confirmation_mandatory_attributes():
            return False

        return self.can_confirm_or_reject(user)

    def can_user_reject(self, user):
        if not self._has_rejection_mandatory_attributes():
            return False

        return self.can_confirm_or_reject(user)

    def can_user_accept_on_behalf(self, request, user):
        return request and self.is_creator_party(
            user
        ) and self.has_x_hours_passed(
            request.created_at
        ) and not self.is_counter_party_participating_in_transaction(
            user
        )

    def can_user_void(self, user):
        request = self.unresolved_void_request()
        result = self.status in self.VOID_REQUEST_STATUSES and request and (
            (self.party(request.created_by) != self.party(user) and self.is_party(user)) or
            self.is_both_parties_managed_by(user)
        )

        return result or self.can_user_accept_on_behalf(request, user)

    def can_user_amend(self, user):
        request = self.unresolved_amend_request()
        result = self.status in self.AMENDABLE_STATUSES and request and (
            (self.party(request.created_by) != self.party(user) and self.is_party(user)) or
            self.is_both_parties_managed_by(user)
        )
        if self.is_invoiced():
            result = result and self.__can_amend_in_invoiced_status()

        return result or self.can_user_accept_on_behalf(request, user)

    def __can_amend_in_invoiced_status(self):
        return not self.seller_brokerage_invoiced and not self.buyer_brokerage_invoiced and get(
            self, 'administration.brokered_by_id'
        )

    @property
    def is_void_request_pending(self):
        return self.unresolved_void_request() is not None

    @property
    def is_amend_request_pending(self):
        return self.unresolved_amend_request() is not None

    @property
    def has_been_amended(self):
        return self.acceptance_requests.filter(
            contract_id=self.id, resolved=True, accepted=True,
            type=constants.CONTRACT_ACCEPTANCE_REQUEST_TYPE_AMEND
        ).exists()

    def unresolved_title_transfer_request(self):
        return self._acceptance_request(request_type='title_transfer')

    def unresolved_void_request(self):
        return self._acceptance_request(request_type='void')

    def unresolved_confirm_request(self):
        return self._acceptance_request(request_type='confirm')

    def unresolved_amend_request(self):
        return self._acceptance_request(request_type='amend')

    def _acceptance_request(self, request_type, resolved=False):
        if getattr(self, '_prefetched_objects_cache', None) and \
           'acceptance_requests' in self._prefetched_objects_cache:
            return self._acceptance_request_from_prefetched_object_cache(request_type, resolved)
        else:
            return self._acceptance_request_from_db(request_type, resolved)

    def _acceptance_request_from_db(self, request_type, resolved=False):
        request = self.acceptance_requests.filter(
            contract_id=self.id,
            type=request_type,
            resolved=resolved,
        )
        return request.first()

    def _acceptance_request_from_prefetched_object_cache(self, request_type, resolved):
        request = None
        requests = list(filter(
            lambda r: r.contract_id == self.id and r.type == request_type and r.resolved == resolved,
            self.acceptance_requests.all()
        ))
        if requests:
            request = requests[0]

        return request

    def handle_title_transfer_request(self, args, process=True, is_cashed_out=False):
        title_transfer = self.create_title_transfer(args, process, True, True, is_cashed_out)
        return title_transfer

    def handle_notifications(self, identifier, tonnage, site_name, user):
        self.send_notification_for_request(
            request_type=CONTRACT_ACCEPTANCE_REQUEST_TYPE_TITLE_TRANSFER,
            action=constants.REQUEST, user=user, data={'title_transfer_no': identifier,
                                                       'site_name': site_name,
                                                       'commodity': self.commodity.display_name,
                                                       'tonnage': tonnage}
        )

    def __is_planned_and_can_apply_request(self, request, user):
        return self.is_planned() and self.is_creator_party(user) and not request.is_confirm

    def __can_apply_request_now(self, request, user):
        if request.is_confirm or request.is_amend or request.is_void:
            can_apply_now = not request.acceptance_required
        else:
            can_apply_now = self.is_both_parties_managed_by(
                user
            ) or self.can_user_accept_on_behalf(
                request, user
            ) or self.__is_planned_and_can_apply_request(
                request, user
            ) or request.should_auto_amend(
                user
            )

        if can_apply_now == request.acceptance_required:
            request.acceptance_required = not can_apply_now
            request.save()

        return can_apply_now

    def amend_orders(self, data):
        update = self.freight_orders.exclude(status__in=NULL_FREIGHT_STATUSES).update(
            **data, updated_at=timezone.now().date())
        return update

    def handle_request(
            self, user, request_type, args, request_function, request_reason=None, communication_params=None
    ):
        from core.jobs.models import Job
        communication_params = communication_params or {}
        acceptance_required = communication_params.pop('acceptance_required', True)

        request = ContractAcceptanceRequest.create({
            'type': request_type,
            'contract_id': self.id,
            'args': args,
            'request_reason': request_reason or '',
            'created_by_id': user.id,
            'updated_by_id': user.id,
            'acceptance_required': acceptance_required,
        })

        if request.errors:
            return request, False

        can_apply_now = self.__can_apply_request_now(request, user)

        season = get(args, 'amended.season')
        is_planned_and_can_apply_request = self.__is_planned_and_can_apply_request(request, user)
        should_notify = is_planned_and_can_apply_request or not can_apply_now
        request.create_requested_audit_record(user)
        if communication_params:
            AcceptanceRequestCommunication.create(
                communication_params,
                kwargs={
                    'request_id': request.id,
                    'created_by_id': user.id,
                    'updated_by_id': user.id,
                    'mail_status': bool(communication_params.get('subject'))
                }
            )
        if can_apply_now:
            if season and self.has_freight_orders and \
                    not self.has_freight_movements and not self.has_planned_title_transfers:
                self.amend_orders({'season': season})
            request_function(user=user, send_notifications=should_notify)
            self.save()
        elif request.is_amend and self.is_rejected():
            self.__handle_amend_request_for_rejected(user)

        if request.is_amend and bool(communication_params.get('subject')):
            contract_sent.send(
                sender=Contract,
                instance=self,
                created_by_id = get(user, 'id'),
                contract_acceptance_request=get(request, 'id'),
                state='amendment'
            )
            Job.schedule_job_for_task(
                'send_amended_contract_mail', params={'contract_id': self.id, 'user_id': user.id}
            )

        request.refresh_from_db()
        return request, should_notify

    def __handle_amend_request_for_rejected(self, user):
        if self.is_rejected() and user:
            self.status = 'planned'
            self.save()

    def is_party(self, user):
        return self.is_seller(user) or \
            self.is_buyer(user) or \
            self.is_direct_creator_company_user(user)

    def is_stranger(self, user):
        return self.viewer_company_ids is None or get(
            user, 'company_id') not in self.viewer_company_ids  # pylint: disable=unsupported-membership-test

    def is_seller_and_managed_by_creator(self, user):
        return self.is_seller(user) and self.is_seller_managed_by_user(self.created_by)

    def is_buyer_and_managed_by_creator(self, user):
        return self.is_buyer(user) and self.is_buyer_managed_by_user(self.created_by)

    def buyer_company_contract_restriction_exist(self):
        return get(self.buyer.company, 'purchase_contract_creation_restriction') != OTHERS_CAN_CREATE_WITHOUT_ACCEPTANCE

    def seller_company_contract_restriction_exist(self):
        return get(self.seller.company, 'sale_contract_creation_restriction') != OTHERS_CAN_CREATE_WITHOUT_ACCEPTANCE

    @memoize(timeout=MEMOMIZE_TIMEOUT)
    def can_confirm_or_reject(self, user): # pylint: disable=too-many-return-statements
        # pylint: disable=line-too-long,
        if self.unresolved_confirm_request():
            creator = self.created_by
            user_company_id = get(user, 'company_id')
            if self.is_creator_party(user) and not self.is_counter_party_participating_in_transaction(user):
                return self.has_x_hours_passed(self.created_at)
            if creator.company.is_broker and user_company_id == creator.company_id:
                return self.seller_company_contract_restriction_exist() and self.buyer_company_contract_restriction_exist()
            elif ((user_company_id == get(self.buyer, 'company_id') and self.buyer_company_contract_restriction_exist()) or
                  (user_company_id == get(self.seller, 'company_id') and self.seller_company_contract_restriction_exist())):
                return True
            elif self.is_buyer(creator):
                return self.is_seller(user) and not self.buyer_company_contract_restriction_exist()
            elif self.is_seller(creator):
                return self.is_buyer(user) and not self.seller_company_contract_restriction_exist()
            return False

    def is_start_date_today(self):
        return self.__has_checkpoint_date_arrived(self.delivery_start_date)

    @property
    def has_delivery_date_arrived(self):
        return self.__has_checkpoint_date_arrived(self.delivery_end_date)

    @staticmethod
    def __has_checkpoint_date_arrived(_date):
        if _date:
            _date = DateTimeUtil.get_date_from_string(_date, '%Y-%m-%d') if isinstance(_date, str) else _date
            return _date <= timezone.now().date()

    def should_be_confirmed_or_open(self):
        return self.total_delivered_tonnage <= 0 and not self.is_planned()

    def get_status_method_from_load_rejected(self):
        return getattr(self, 'open') if self.is_start_date_today() else getattr(self, 'confirm')

    def is_planned_and_can_confirm_or_reject(self, user):
        return self.is_planned() and self.can_confirm_or_reject(user)

    @property
    def direct_freight_contracts_tonnage(self):
        return float("%0.2f" % (
            self.planned_freight_contracts_tonnage() + \
            self.delivered_freight_contracts_tonnage()
        ))

    @cached_property
    def direct_freight_orders_tonnage(self):
        tonnage_attr = 'planned_tonnage'
        if self.is_strict_quantity_based_commodity:
            tonnage_attr = 'quantity'
        return self.freight_orders.filter(parent_order_id=None).exclude(
            status__in=['void', 'rejected', 'template']
        ).exclude(type_id=PACK_ORDER_TYPE_ID).aggregate(
            total_tonnage=models.Sum(tonnage_attr)
        )['total_tonnage'] or 0

    @property
    def all_freight_orders_tonnage(self):
        tonnage_attr = 'planned_tonnage'
        if self.is_strict_quantity_based_commodity:
            tonnage_attr = 'quantity'
        return self.freight_orders.filter(parent_order_id=None).exclude(type_id=PACK_ORDER_TYPE_ID).aggregate(
            total_tonnage=models.Sum(tonnage_attr)
        )['total_tonnage'] or 0

    @property
    def reference_number(self):
        return self.contract_number or self.identifier

    def is_expected_tonnage_not_delivered(self):
        return not self.is_expected_tonnage_delivered() and round(self.total_delivered_tonnage, 2) > 0

    def is_expected_tonnage_delivered(self):
        return round(self.expected_tonnage_range()[0], 2) <= round(self.total_delivered_tonnage, 2)

    def all_freight_contracts_have_status(self, status):
        status_set = set(
            self.all_freight_contracts().exclude(status__in=NULL_STATUSES).values_list('status', flat=True))
        return len(status_set) == 0 or status_set.issubset(set(status))

    def all_freight_orders_have_status(self, status):
        status_set = set(self.freight_orders.exclude(status__in=NULL_STATUSES).values_list('status', flat=True))
        return len(status_set) == 0 or (len(status_set) == 1 and status in status_set)

    def all_title_transfers_have_status(self, status):
        status_set = set(self.titletransfer_set.exclude(status__in=NULL_STATUSES).values_list('status', flat=True))
        return len(status_set) == 0 or status_set.issubset(set(status))

    def all_freight_contracts_invoiced(self):
        return all(map(lambda fc: fc.is_commodity_contract_invoiced_for_contract(self.id),
                       self.all_freight_contracts().exclude(status__in=NULL_STATUSES)))

    def all_freight_contracts_brokerage_invoiced(self, user):
        return all(map(lambda fc: fc.is_brokerage_invoiced(user), self.all_freight_contracts().exclude(
            status__in=NULL_STATUSES
        )))

    def any_incomplete_freight_contracts(self):
        return self.all_freight_contracts().filter(status__in=FREIGHT_CONTRACT_INCOMPLETE_STATUSES).exists()

    def freight_contracts_cannot_mark_as_complete_reasons(self, _=None):
        freight_contracts = self.all_freight_contracts().filter(status__in=['delivered']).exclude(
            status__in=NULL_STATUSES)
        identifiers = [f.identifier for f in freight_contracts if f.cannot_mark_as_complete_reasons()]
        return [MOVEMENTS_MISSING_DETAILS.format(identifiers=', '.join(identifiers))] if identifiers else []

    def cannot_complete_manually_reasons(self, user):# pylint: disable=too-many-return-statements
        if not self.status in MANUALLY_COMPLETEABLE_STATUSES:
            return [CANNOT_CREATE_CHILD_REASON_STATUS]
        if self.any_incomplete_freight_contracts():
            return [MOVEMENTS_NOT_DELIVERED]
        if self.is_seller(user) and \
           self.is_buyer_participating_in_transaction and \
           not self.was_created_in_status('delivered') and not self.is_buyer(user):
            return [BUYER_REGISTERED]
        if self.unresolved_amend_request():
            return [constants.PENDING_UNRESOLVED_REQUEST.format(
                type='Amend',
            )]
        elif self.unresolved_confirm_request():
            return [constants.PENDING_UNRESOLVED_REQUEST.format(
                type='Confirmation',
            )]
        elif self.unresolved_title_transfer_request():
            return [constants.PENDING_UNRESOLVED_REQUEST.format(
                type='Title Transfer',
            )]
        elif self.unresolved_void_request():
            return [constants.PENDING_UNRESOLVED_REQUEST.format(
                type='Void',
            )]

        return self.freight_contracts_cannot_mark_as_complete_reasons(user)

    @property
    def has_planned_title_transfers(self):
        return self.titletransfer_set.filter(is_active=True, status='planned').exists()

    def mark_all_planned_title_transfer_as_completed(self, process_on):
        title_transfers = self.titletransfer_set.filter(is_active=True, status='planned')
        for title_transfer in title_transfers:
            title_transfer.process_on = process_on
            title_transfer.completed()
            title_transfer.save()

    def mark_all_delivered_freight_contracts_as_completed(self, user, request_origin='Web', feature=None):
        freight_contracts = self.all_freight_contracts().filter(status=DELIVERED)
        for freight_contract in freight_contracts:
            freight_contract.completed(user, request_origin, feature)
            freight_contract.save()

    def mark_incomplete_freight_orders_as_completed(self):
        freight_orders = self.freight_orders.filter(status__in=INCOMPLETE_FREIGHT_ORDER_STATUSES)
        for freight_order in freight_orders:
            freight_order.mark_incomplete_child_orders_as_completed()
            freight_order.status = COMPLETED_STATUS
            freight_order.save()

    def can_contract_be_marked_as_completed(self):
        return (
                       self.is_delivered() or self.is_completed() or self.is_invoiced()
               ) and self.is_expected_tonnage_delivered() and \
               self.all_freight_contracts_have_status(['completed', 'manual_contract_complete_balanced']) and \
               self.all_title_transfers_have_status(['completed', 'invoiced']) and \
               not (self.is_invoiced() and self.can_mark_invoiced())

    def can_create_commodity_contract_invoice(self, user, counter_party_subscriber_check=True):
        if self.is_void_request_pending or self.is_amend_request_pending:
            return False
        if user and self.status in self.ALLOWED_STATUSES_FOR_INVOICE_CREATION:
            if self.is_invoicing_buyer_rcti():
                return self.is_buyer(user) or (
                        counter_party_subscriber_check and not self.is_buyer_participating_in_transaction)
            else:
                return self.is_seller(user) or (
                        counter_party_subscriber_check and not self.is_seller_participating_in_transaction
                )
        return False

    def cannot_create_commodity_contract_invoice_reasons(self, user):
        reasons = []
        is_invoicing_buyer_rcti = self.is_invoicing_buyer_rcti()
        is_buyer, is_seller = self.is_buyer(user), self.is_seller(user)

        if self.is_void_request_pending:
            reasons.append(constants.CONTRACT_HAS_PENDING_VOID_REQUEST)

        if self.is_amend_request_pending:
            reasons.append(constants.CONTRACT_HAS_PENDING_AMEND_REQUEST)

        if self.status not in self.ALLOWED_STATUSES_FOR_INVOICE_CREATION:
            reasons.append(constants.CONTRACT_STATUS_NOT_AMONGST_INVOICEABLE_STATUS)

        elif is_invoicing_buyer_rcti and not is_buyer and self.is_buyer_participating_in_transaction:
            reasons.append(constants.INVOICING_IS_BUYER_RCTI_AND_USER_NOT_BUYER)

        elif not is_invoicing_buyer_rcti and not is_seller and self.is_seller_participating_in_transaction:
            reasons.append(constants.INVOICING_IS_SELLER_TO_INVOICE_BUYER_AND_USER_NOT_THE_SELLER)

        if not self.price:
            reasons.append(constants.PRICE_MISSING)

        return reasons

    def can_create_brokerage_invoice(self, user):
        if self.is_void_request_pending or self.is_amend_request_pending:
            return False
        result = bool(user and self.is_broker(user) and self.status in self.ALLOWED_STATUSES_FOR_INVOICE_CREATION)
        if not result:
            return result

        brokerages = self.__brokerages(user)
        if not brokerages:
            return False
        if all((b.is_end_of_delivery for b in brokerages)):
            return self.has_delivery_date_arrived

        return True

    def __is_brokerage_invoiced(self, user):
        brokerages_for_user = self.brokerages_for_user(user)
        seller_brokerage_invoiced = self.seller_brokerage_invoiced
        buyer_brokerage_invoiced = self.buyer_brokerage_invoiced
        return len(brokerages_for_user) == 1 and (seller_brokerage_invoiced or buyer_brokerage_invoiced) or \
                (seller_brokerage_invoiced and buyer_brokerage_invoiced)

    def cannot_create_brokerage_invoice_reasons(self, user):
        reasons = []
        is_broker = self.is_broker(user)
        if not is_broker:
            reasons.append(constants.USER_NOT_THE_BROKER)

        elif self.status not in self.ALLOWED_STATUSES_FOR_INVOICE_CREATION:
            reasons.append(constants.CONTRACT_STATUS_NOT_AMONGST_INVOICEABLE_STATUS)

        elif len(self.__brokerages(user)) == 0:
            reasons.append(constants.CONTRACT_DOES_NOT_HAS_BROKERAGES)

        elif self.__is_brokerage_invoiced(user):
            reasons.append(constants.BROKERAGE_INVOICE_ALREADY_CREATED)

        return reasons

    def can_create_title_transfer(self, user):
        if not self.__can_create_child_absent_contract_number():
            return False
        if user and self.is_buyer(user) \
                and self._is_in_tonnage_acceptable_state(user):
            return True
        return user and (self.is_seller(user) or self.is_buyer(user)) and self._is_in_tonnage_acceptable_state(user)

    def cannot_create_title_transfer_reasons(self, user):
        reasons, warnings = self._cannot_create_child_reasons_common(
            user, WARNING_CREATE_TITLE_TRANSFER_DRAFT)
        if not self.__can_create_child_absent_contract_number():
            reasons.append(
                constants.CANNOT_CREATE_FO_FM_REASON_CONTRACT_NUMBER)

        return reasons, warnings

    def contract_invoicable_disabled_options(self):
        contract_invoice = False
        buyer_invoice = False
        seller_invoice = False
        is_status_unconfirmed = self.status in [PLANNED_STATUS, REJECTED_STATUS, VOID_STATUS]

        if self.status in [PLANNED_STATUS, REJECTED_STATUS, VOID_STATUS, PAID_STATUS] or self.excluded_contract.filter(
                type=constants.COMMODITY_CONTRACT_INVOICE).exists():
            contract_invoice = True

        if is_status_unconfirmed or self.excluded_contract.filter(
                type=constants.BUYER_INVOICE).exists():
            buyer_invoice = True

        if is_status_unconfirmed or self.excluded_contract.filter(type=constants.SELLER_INVOICE).exists():
            seller_invoice = True

        if not buyer_invoice or not seller_invoice:
            brokerages = self.brokerage_set.all()
            if len(brokerages) == 1:
                payble_by = get(brokerages[0].info(), 'payble_by')
                if payble_by == "Seller":
                    buyer_invoice = True
                if payble_by == "Buyer":
                    seller_invoice = True
            elif len(brokerages) == 0:
                buyer_invoice = True
                seller_invoice = True

        return {
            "contract_invoice": contract_invoice,
            "buyer_invoice": buyer_invoice,
            "seller_invoice": seller_invoice
        }

    def __can_create_child_absent_contract_number(self):
        return self.is_planned() or self.contract_number

    def can_create_freight_contract(self, user):
        if not user or not self.__can_create_child_absent_contract_number() \
           or not self.delivery_onus or not self.conveyance:
            return False
        return self._is_in_tonnage_acceptable_state(user)

    def cannot_create_freight_contract_reasons(self, user):
        reasons, warnings = self._cannot_create_child_reasons_common(user, WARNING_CREATE_FREIGHT_CONTRACT_DRAFT)

        if not self.__can_create_child_absent_contract_number():
            reasons.append(constants.CANNOT_CREATE_FO_FM_REASON_CONTRACT_NUMBER)
        if not self.user_has_delivery_onus(user):
            if self.is_planned():
                reasons.append(constants.CANNOT_CREATE_FREIGHT_ORDER_REASON_DEL_ONUS)

        return reasons, warnings

    def is_user_freight_customer(self, user):
        party = self.freight_customer_party_type

        if party == 'seller':
            return self.is_seller(user)
        if party == 'buyer':
            return self.is_buyer(user)

    def can_show_create_freight_order_option(self, user):
        return self.is_user_freight_customer(user)

    def can_create_freight_order(self, user=None): #  # pylint: disable=unused-argument
        return True

    def can_create_call_on_grain_order(self, user):
        return user and \
            self.__can_create_child_absent_contract_number() and \
            self._is_in_tonnage_acceptable_state(
                user
            ) and not self.user_has_delivery_onus(
                user
            )

    def is_allowed_every_commodity_transaction(self, user):
        return self.is_creator(user) and self.is_planned()

    def cannot_create_freight_order_reasons(self, user):
        reasons, warnings = self._cannot_create_child_reasons_common(user, WARNING_CREATE_FREIGHT_ORDER_DRAFT)
        if not self.__can_create_child_absent_contract_number():
            reasons.append(constants.CANNOT_CREATE_FO_FM_REASON_CONTRACT_NUMBER)

        return reasons, warnings

    def cannot_create_vendor_dec_reasons(self, user):  # pylint: disable=unused-argument
        reasons = []
        if self.status in ['void', 'rejected', 'draft']:
            reasons.append(core.common.constants.CANNOT_CREATE_COMMODITY_VENDOR_DEC_STATUS_REASON)

        return reasons

    def cannot_request_vendor_dec_reasons(self, user):
        reasons = []
        if user.company_id in [
                get(self, 'seller.company_id'), get(self, 'seller.represented_by_id')
        ]:
            reasons.append(core.common.constants.CANNOT_REQUEST_COMMODITY_VENDOR_DEC_SELLER_REASON)
        if self.status in ['void', 'rejected', 'draft']:
            reasons.append(core.common.constants.CANNOT_CREATE_COMMODITY_VENDOR_DEC_STATUS_REASON)

        return reasons

    def cannot_create_cog_reasons(self, user):
        reasons, warnings = self._cannot_create_child_reasons_common(user, WARNING_CREATE_FREIGHT_ORDER_DRAFT)
        if not self.__can_create_child_absent_contract_number():
            reasons.append(constants.CANNOT_CREATE_FO_FM_REASON_CONTRACT_NUMBER)

        if self.user_has_delivery_onus(user) and self.is_counter_party_registered(user):
            reasons.append(constants.CANNOT_CREATE_COG_REASON_DEL_ONUS)

        return reasons, warnings

    def cannot_create_pack_order_reasons(self, user):
        reasons, warnings = self._cannot_create_child_reasons_common(user, WARNING_CREATE_FREIGHT_ORDER_DRAFT, True)
        return reasons, warnings

    def _cannot_create_child_reasons_common(self, user, warning_reason, is_pack=False):
        reasons = []
        warnings = []

        if not self.is_ongoing() and self.is_allowed_every_commodity_transaction(user) and self.is_planned():
            warnings.append(warning_reason)

        if (
            self.status and
            self.status.lower() in ['draft', 'planned', 'void', 'rejected', 'confirmation_pending'] and
            not self.is_allowed_every_commodity_transaction(user)
        ):
            display_status = self.status_display_name(user)
            reasons.append(f'Contract is in {display_status} status')
        if self.is_amend_request_pending:
            reasons.append(constants.CANNOT_CREATE_CHILD_REASON_PENDING_AMEND)
        if self.is_void_request_pending:
            reasons.append(constants.CANNOT_CREATE_CHILD_REASON_PENDING_VOID)
        if not is_pack and not self.can_account_more_tonnage():
            reasons.append(constants.CANNOT_CREATE_CHILD_REASON_TONNAGE)
        elif is_pack and not self.can_account_more_pack_tonnage():
            reasons.append(constants.CANNOT_CREATE_PACK_ORDER_REASON_TONNAGE)

        return reasons, warnings

    def _is_in_tonnage_acceptable_state(self, user=None):
        return bool((self.status and self.is_freight_movement_creation_allowed() and not (
            self.is_void_request_pending or self.is_amend_request_pending
        ) and self.can_account_more_tonnage()) or (user and self.is_allowed_every_commodity_transaction(user)))

    def can_account_more_tonnage(self):
        return bool(self.unaccounted_tonnage and self.unaccounted_tonnage > 0)

    def can_account_more_pack_tonnage(self):
        unpacked_tonnage = self.unpacked_tonnage
        return bool(unpacked_tonnage and unpacked_tonnage > 0)

    def is_ongoing(self):
        return self.status.lower() in self.CONFIRMED_TO_DELIVERED_STATUSES

    def is_freight_movement_creation_allowed(self):
        return self.status.lower() not in ['draft', 'planned', 'void', 'rejected', 'confirmation_pending']

    def expected_tonnage_range(self, amended_tonnage=None):
        tonnage = amended_tonnage if amended_tonnage else self.inferred_tonnage
        return self.tolerance.get_range(tonnage, self.commodity)

    def linked_order_freight_contracts(self):
        from core.freights.models import FreightContract, FreightOrder
        orders = FreightOrder.objects.filter(type_id__lt=PICKUP_ORDER_TYPE_ID)

        delivery_order_ids = orders.filter(
            freight_delivery__delivery_orders__commodity_contract_id=self.id
        ).values_list('id', flat=True)
        pickup_order_ids = orders.filter(
            delivery_siblings__pickup_freight_order__commodity_contract_id=self.id
        ).values_list('id', flat=True)
        return FreightContract.objects.filter(order_id__in=[*delivery_order_ids, *pickup_order_ids])

    @property
    def direct_movement_ids(self):
        return self.freight_contracts.values_list('id', flat=True)

    @property
    def linked_order_movement_ids(self):
        return self.linked_order_freight_contracts().values_list('id', flat=True)

    def all_freight_contracts(self):
        movement_ids = list(self.direct_movement_ids)
        movement_ids += list(self.linked_order_movement_ids)
        from core.freights.models import FreightContract
        return FreightContract.objects.filter(id__in=movement_ids).exclude(type_id=PACK_ORDER_TYPE_ID)

    def direct_freight_orders(self):
        return self.freight_orders.filter(parent_order_id=None).exclude(type_id=PACK_ORDER_TYPE_ID)

    def confirmed_freight_contracts_tonnage(self):
        return self._freight_contracts_tonnage_for(self.STATUS_CONFIRMED)

    def delivered_freight_contracts_tonnage(self):
        return self._freight_contracts_tonnage_for(self.AFTER_DELIVERY_STATUSES)

    def planned_freight_contracts_tonnage(self):
        return self._freight_contracts_tonnage_for(self.PLANNED_TO_DELIVERY_STATUSES)

    def _freight_contracts_tonnage_for(self, statuses):
        status_criteria = {'status__in': statuses} if len(statuses) > 1 else {'status': statuses[0]}
        direct_freight_contracts = self.all_freight_contracts().filter(**status_criteria)

        return float(
            '%0.2f' % sum(float(freight.inferred_tonnage or 0) for freight in direct_freight_contracts)) or 0.0

    def planned_freight_orders_tonnage(self):
        return self._freight_orders_tonnage_for(self.PLANNED_TO_DELIVERY_STATUSES)

    def _freight_orders_tonnage_for(self, statuses):
        direct_freight_orders = self.direct_freight_orders()
        freight_orders = direct_freight_orders.filter(
            status__in=statuses
        )
        if not freight_orders:
            return float('%0.2f' % 0)
        return float('%0.2f' % sum(float(order.inferred_tonnage or 0) for order in freight_orders))

    def delivered_freight_orders_tonnage(self):
        return self._freight_orders_tonnage_for(self.AFTER_DELIVERY_STATUSES)

    @property
    def total_delivered_tonnage(self):
        return float(self.delivered_tonnage or 0) or 0

    def get_delivered_tonnage(self):
        return float(self.delivered_freight_contracts_tonnage()) + float(self.title_transfered_tonnage)

    def set_delivered_tonnage(self):
        Contract.objects.filter(id=self.id).update(delivered_tonnage=self.get_delivered_tonnage())

    def set_unaccounted_tonnage(self):
        Contract.objects.filter(id=self.id).update(unaccounted_tonnage=self.get_unaccounted_tonnage())

    @property
    def undelivered_tonnage(self):
        return float(self.inferred_tonnage) - float(self.total_delivered_tonnage)

    @property
    def title_transfered_tonnage(self):
        tonnage_attr = 'tonnage'
        if self.is_strict_quantity_based_commodity:
            tonnage_attr = 'quantity'

        return self.titletransfer_set.filter(
            is_active=True,
            status__in=['completed', 'invoiced']
        ).aggregate(
            tonnage=models.Sum(tonnage_attr)
        )['tonnage'] or 0

    @cached_property
    def all_title_transfered_tonnage(self):
        tonnage_attr = 'tonnage'
        if self.is_strict_quantity_based_commodity:
            tonnage_attr = 'quantity'

        return self.titletransfer_set.exclude(status='void').filter(
            is_active=True
        ).aggregate(
            tonnage=models.Sum(tonnage_attr)
        )['tonnage'] or 0

    def direct_freight_movements(self):
        from core.freights.models import FreightContract
        queryset = FreightContract.objects
        return queryset.filter(
            commodity_contract_id=self.id, order_id=None
        ).exclude(type_id=PACK_ORDER_TYPE_ID) if self.id else queryset.none()

    @property
    def all_movements_tonnage(self):
        freight_contracts = self.all_freight_contracts().exclude(status__in=NULL_STATUSES)

        return float('%0.2f' % sum(float(freight.inferred_tonnage or 0) for freight in freight_contracts)) or 0.0

    @property
    def direct_freight_movements_tonnage(self):
        freight_contracts = self.direct_freight_movements().exclude(status__in=NULL_STATUSES)

        if not freight_contracts:
            return float('%0.2f' % 0)

        return float('%0.2f' % sum(float(freight.inferred_tonnage or 0) for freight in freight_contracts))

    @property
    def all_freight_movements_tonnage(self):
        freight_contracts = self.direct_freight_movements()

        if not freight_contracts:
            return float('%0.2f' % 0)

        return float('%0.2f' % sum(float(freight.inferred_tonnage or 0) for freight in freight_contracts))

    confirmed_tonnage = property(confirmed_freight_contracts_tonnage)
    expected_tonnage = property(expected_tonnage_range)
    planned_tonnage = property(planned_freight_contracts_tonnage)

    @property
    def delivered_freight_orders_delivered_tonnage(self):
        orders = self.freight_orders.filter(parent_order_id=None).exclude(
            status__in=['void', 'rejected']
        ).exclude(type_id=PACK_ORDER_TYPE_ID)

        if not orders.exists():
            return float('%0.2f' % 0)

        from core.freights.models import FreightOrder
        return FreightOrder.delivered_movements_tonnage(list(orders.values_list('id', flat=True)))

    def is_inspecting_at_delivery(self):
        return self.inspection_id == constants.CHECKPOINT_DELIVERY_ID

    def is_weighing_at_delivery(self):
        return self.weight_id == constants.CHECKPOINT_DELIVERY_ID

    def is_weighing_at_origin(self):
        return self.weight_id == constants.CHECKPOINT_ORIGIN_ID

    @transition(
        field=status,
        source=['planned', 'in_progress', 'delivered', 'completed'],
        target='confirmed',
        permission=can_user_confirm
    )
    # pylint: disable=too-many-locals
    def confirm(self, user=None, send_notifications=True):
        if not user:
            return
        with transaction.atomic():
            request = self.unresolved_confirm_request()
            if request:
                request.mark_accepted(user=user)

            if send_notifications:
                contract_accepted.send(sender=Contract, instance=self, created_by_id=user.id,
                                       contract_created_by=self.created_by_id,
                                       buyer=self.buyer.company.display_name)

    @transition(
        field=status,
        source='planned',
        target='rejected',
        permission=can_user_reject
    )
    # pylint: disable=too-many-locals
    def reject(self, user, rejection_reason):
        with transaction.atomic():
            request = self.unresolved_confirm_request()
            if request:
                request.rejection_reason = rejection_reason
                request.mark_resolved(user=user)
            from core.freights.models import FreightContract
            FreightContract.reject_linked_movements(self)
            self.reject_linked_freight_orders()
            self.titletransfer_set.update(is_active=False, status='rejected')
            contract_rejected.send(sender=Contract, instance=self, created_by_id=user.id)

    @transition(
        field=status,
        source=['confirmed', 'in_progress', 'delivered', 'completed'],
        target='open',
        conditions=[is_start_date_today]
    )
    def open(self):
        pass

    @transition(
        field=status,
        source=['confirmed', 'open', 'delivered', 'completed'],
        target='in_progress',
        conditions=[is_expected_tonnage_not_delivered]
    )
    def in_progress(self):
        contract_in_progress.send(sender=Contract, instance=self)

    def can_be_marked_as_delivered(self):
        return self.is_expected_tonnage_delivered() and not(
                self.is_completed() and self.can_contract_be_marked_as_completed())

    @transition(
        field=status,
        source=['confirmed', 'open', 'in_progress', 'completed'],
        target='delivered',
        conditions=[can_be_marked_as_delivered]
    )
    def delivered(self):
        contract_delivered.send(sender=Contract, instance=self)

    @transition(
        field=status,
        source=['delivered', 'invoiced'],
        target='completed',
        conditions=[can_contract_be_marked_as_completed]
    )
    def completed(self):
        if not self.cash_price_id:
            contract_completed.send(sender=Contract, instance=self)

    def can_mark_invoiced(self):
        return self.invoiceable() and not self.any_pending_commodity_contract_invoice() and \
               self.is_expected_tonnage_delivered()

    def invoiceable(self):
        return (self.is_completed() or self.is_invoiced() or self.is_paid()) and self.all_title_transfers_have_status(
            ['invoiced']
        ) and self.all_freight_contracts_invoiced()

    def any_pending_commodity_contract_invoice(self):
        return self.__get_invoices().filter(status__in=['generated'])

    def __get_invoices(self, invoice_type='Commodity Contract'):
        from core.invoices.models import Invoice

        return Invoice.objects.filter(
            type=invoice_type, raised_for_id=self.id,
            raised_for_type__model='contract'
        )

    @transition(field='status', source=['completed', 'paid'], target='invoiced', conditions=[can_mark_invoiced])
    def invoiced(self):
        self.invoice_set.filter(
            status=DRAFT_STATUS, invoiceitem__item_type__model__in=[EPR_ITEM_DB_MODEL, GRAIN_LEVY_ITEM_MODEL]
        ).exclude(invoiceitem__item_type__model__in=[FREIGHT_MOVEMENT_MODEL, TITLE_TRANSFER_MODEL, LOAD_MODEL]).delete()

    @transition(field='status', source='invoiced', target='paid')
    def paid(self, data, user, update_invoices=False):
        if user:
            contract_paid.send(sender=Contract, instance=self, created_by_id=user.id)
            self.updated_by = user
        if update_invoices:
            invoice_class = apps.get_model('invoices', 'Invoice')
            invoice_class.mark_paid_for_contract(self.id, data, user)

    @transition(
        field=status,
        source=['open', 'in_progress'],
        target='delayed',
    )
    def delayed(self):
        contract_delayed.send(sender=Contract, instance=self)

    @transition(
        field=status,
        source=VOIDABLE_STATUES,
        target='void',
        permission=can_user_void
    )
    def void(self, user, send_notifications=True): # pylint: disable=unused-argument
        from core.freights.models import FreightContract
        request = self.unresolved_void_request()
        with transaction.atomic():
            if request:
                request.mark_accepted(user=user)
                contract_void_request_accepted.send(sender=Contract, instance=self, created_by_id=user.id)
            FreightContract.void_linked_movements(commodity_contract=self, user=user)
            self.void_linked_freight_orders()
            self.sale_contracts.clear()
            self.purchase_contracts.clear()
            self.titletransfer_set.update(is_active=False, status='void')

    def update_object_details(self, obj, data):
        for key, value in data.items():
            setattr(obj, key, value)
            obj.save()

    def add_party(self, data, party='Consignee'):
        self.handlers(role=party).delete()
        for item in data:
            item.update({
                'contract_id': self.id,
                'status': self.status,
                'created_by_id': self.created_by_id,
                'updated_by_id': self.updated_by_id,
            })
        ContractCommodityHandler.build_with_save(data, identity=party)

    def change_status_based_on_delivery_start_date(self):
        if self.status in [
                constants.CONFIRMED, constants.REJECTED, constants.OPEN
        ]:
            if self.is_start_date_today():
                self.status = constants.OPEN
            else:
                self.status = constants.CONFIRMED

    def change_status_based_on_tonnage_delivered(self):
        if self.is_expected_tonnage_delivered():
            if self.status == constants.IN_PROGRESS:
                self.status = constants.DELIVERED
            if self.status == constants.DELIVERED and can_proceed(self.completed):
                self.status = constants.COMPLETED
        else:
            if self.status in [constants.COMPLETED, constants.DELIVERED, constants.INVOICED, PAID_STATUS]:
                self.status = constants.IN_PROGRESS

    def __amend_brokerages(self, value, request):
        is_broker = request.created_by.company.is_broker
        if not value:
            if is_broker:
                self.brokerage_set.all().delete()
            else:
                party = BROKERAGE_TYPE_SELLER if self.is_seller(
                    request.created_by
                ) else BROKERAGE_TYPE_BUYER
                self.brokerage_set.filter(type=party).delete()
        if len(value) == 1 and get(value, '0.type') and is_broker:
            self.brokerage_set.exclude(type=value[0]['type']).delete()
        for params in value:
            brokerage = self.brokerage_set.filter(type=params['type'])
            brokerages_to_create = []
            if brokerage:
                if request.get_brokerage_diff(params['type']):
                    brokerage.update(**params)
            else:
                brokerages_to_create.append(params)
            if brokerages_to_create:
                Brokerage.build_with_save(
                    brokerages_to_create,
                    entity_object=self,
                    status=self.status
                )

    def update_title_transfer_object_details(self, obj, data):
        contact_id = data.get('contact_id', None)
        ngr_id = data.get('ngr_id', None)
        if contact_id:
            Party.objects.filter(
                id__in=TitleTransfer.objects.filter(
                    commodity_contract_id=self.id
                ).values_list(obj, flat=True)).update(contact_id=contact_id)
        if ngr_id:
            Party.objects.filter(
                id__in=TitleTransfer.objects.filter(
                    commodity_contract_id=self.id
                ).values_list(obj, flat=True)).update(ngr_id=ngr_id)

    def update_note_data(self, note_params):
        if note_params and 'description' in note_params:
            company_id = note_params['company_id']
            note = self.note_set.filter(company_id=company_id).first()
            if not note:
                Note.persist({**note_params, 'object_id': self.id,
                              'object_type_klass': Contract,
                              'created_by_id': self.created_by.id})
                return
            note.description = note_params['description']
            if 'attachments' in note_params:
                attachments = note.attachments or []
                files = note_params.pop('attachments', None)
                if note.id and files:
                    for _file in compact(files):
                        if 'base64' in _file:
                            _name = Note.FILE_PATH + str(note.id) + '/' + _file['name']
                            attachments.append({'name': _file['name'],
                                                'url': S3.upload_base64(_file['base64'], _name, False)})
                note.attachments = attachments
            note.save()

    def __apply_amend(self, request, user):  # pylint: disable=too-many-branches,too-many-statements
        if get(request.args, 'amended'):
            args = deepcopy(request.args['amended'])
        else:
            args = deepcopy(request.args)
        old_consignor_id = None
        for key, value in args.items():
            if key == 'spread':
                self.update_spread_details(value)
            elif key == 'carry_rate' and value == '':
                self.carry_rate = None
                self.carry_start_date = None
                self.carry_end_date = None
            elif key == 'seller':
                self.update_object_details(obj=self.seller, data=value)
                self.update_title_transfer_object_details(obj='seller_id', data=value)
            elif key == 'buyer':
                self.update_object_details(obj=self.buyer, data=value)
                self.update_title_transfer_object_details(obj='buyer_id', data=value)
            elif key == 'administration':
                self.update_object_details(obj=self.administration, data=value)
            elif key == 'area':
                self.update_object_details(obj=self.area, data=value)
            elif key == 'crm_notes':
                note = value
                note['object_id'] = self.id
                note['object_type_klass'] = Contract
                note['created_by_id'] = self.created_by.id
                note['company_id'] = user.company_id
                self.update_note_data(note)
            elif key == 'consignees':
                self.add_party(value, 'Consignee')
            elif key == 'consignors':
                self.add_party(value, 'Consignor')
            elif key == 'brokerages':
                self.__amend_brokerages(value, request)
            elif key == 'track' and self.should_update_track():
                self.titletransfer_set.update(is_active=False, status='void')
                unaccounted_tonnage = self.get_unaccounted_tonnage()
                setattr(self, 'unaccounted_tonnage', unaccounted_tonnage)
                setattr(self, key, value)
            elif key == 'chemical_applications':
                self.chemical_applications.all().delete()
                ChemicalApplication.persist_many(value, contract_id=self.id)
            else:
                if key in ['grade_id', 'variety_id', 'season']:
                    self.update_value_in_related_transactions(key, value)
                if key == 'tonnage':
                    unaccounted_tonnage = self.get_unaccounted_tonnage(value)
                    setattr(self, 'unaccounted_tonnage', unaccounted_tonnage)
                if key == 'carry_rate':
                    self.trigger_contract_load_cost_job()
                setattr(self, key, value)
                if key == 'tolerance_id':
                    all_orders = self.direct_freight_orders()
                    all_orders.update(tolerance_id=value)
                    for order in all_orders:
                        order.set_unaccounted_tonnage()
        self.updated_by_id = user.id

        return old_consignor_id

    def consignee_site_location_id(self):
        return get(self.consignees_with_sites, '0.sites.0.location.id')

    def consignor_site_location_id(self):
        return get(self.consignors_with_sites, '0.sites.0.location.id')

    def should_update_consignor(self):
        return self.price_point_id in [
            PRICE_POINTS_EX_FARM_ID, PRICE_POINTS_FREE_ON_TRUCK_ID
        ] and not self.is_consignor_not_bhc()

    def should_update_consignee(self):
        return self.price_point_id in [
            PRICE_POINTS_FREE_IN_STORE_ID,
            PRICE_POINTS_DELIVERED_DCT_ID, PRICE_POINTS_DELIVERED_SITE_ID,
        ] and not self.is_consignee_not_bhc()

    def should_update_track(self):
        return self.unresolved_confirm_request()

    @transaction.atomic
    def amend(self, user, send_notifications=True):
        request = self.acceptance_requests.filter(
            resolved=False,
            type='amend',
            contract_id=self.id
        ).order_by('-id').first()
        if not request:
            return

        old_consignor_id = self.__apply_amend(request, user)
        if not self.errors:
            if get(request.args, 'amended.season') and self.has_freight_orders and \
                    not self.has_freight_movements and not self.has_planned_title_transfers:
                self.amend_orders(
                    {'season': get(request.args, 'amended.season')})
            request.mark_accepted()

            setattr(self, 'unaccounted_tonnage', self.get_unaccounted_tonnage())
            setattr(self, 'delivered_tonnage', self.get_delivered_tonnage())

            self.change_status_based_on_delivery_start_date()
            self.change_status_based_on_tonnage_delivered()

            self.save()
            if can_proceed(self.completed) and not self.can_mark_invoiced():
                self.completed()
                self.save()
            if can_proceed(self.invoiced):
                self.invoiced()
                if not self.valid_invoices_excluding_paid().exists():
                    self.paid({}, user, False)
                self.save()
            ContractCommodityHandler.objects.filter(id=old_consignor_id).delete()
            if send_notifications:
                contract_amend_accepted.send(
                    sender=Contract,
                    instance=self,
                    created_by_id=user.id,
                    contract_acceptance_request=request.id
                )
            if self.is_planned():
                self.void_draft_transactions(user)
            self.add_farm_parties_to_farm_directory()
            self.add_parties_to_companies_directory()
            self.trigger_contract_load_cost_job()
            from core.jobs.models import Job
            Job.schedule_job_for_task(
                'recreate_draft_invoices_for_contract',
                params={'contract_id': self.id}
            )

    def trigger_contract_load_cost_job(self):
        if Toggle.get('LOAD_COST_TOGGLE'):
            from core.jobs.models import Job
            Job.schedule_job_for_task('contract_load_cost_job', self.id)

    def process_contract_load_costs(self):
        for contract in self.all_freight_contracts().exclude(status__in=NULL_STATUSES):
            contract.update_costs()

        for title_transfer in self.titletransfer_set.exclude(status__in=NULL_STATUSES):
            title_transfer.update_costs()

    def void_draft_transactions(self, user):
        if not self.can_create_freight_contract(user) or not self.can_create_freight_order(user):
            reason = PERMISSION_CHANGE_ERROR.format(identifier=self.identifier)
            from core.freights.models import FreightContract
            FreightContract.void_linked_movements(self, reason, user)
            self.void_linked_freight_orders(reason)
            self.titletransfer_set.update(is_active=False, status='void')

    def title_transfer(self, _=None, __=None):
        from core.jobs.models import Job
        request = self.acceptance_requests.get(
            resolved=False,
            type='title_transfer',
            contract_id=self.id
        )
        with transaction.atomic():
            title_transfer = TitleTransfer.build(request.args)
            if self.is_planned():
                title_transfer.status = DRAFT_STATUS
            if not title_transfer.errors:
                title_transfer.save()
                title_transfer.audit(action='create', user=title_transfer.created_by,
                                     acceptance_request_id=get(request, 'id'))
                if title_transfer.id and not title_transfer.is_draft():
                    request.mark_accepted()
                    if title_transfer.is_in_past:
                        transaction.on_commit(
                            lambda: Job.schedule_job_for_task('process_title_transfer', title_transfer.id)
                        )
            return title_transfer

    def create_title_transfer(self, params, process=True, create_audit=False, notification=False, is_cashed_out=False):
        communication_params = params.pop('communication', {})
        if self.is_planned():
            params['status'] = 'draft'
        note = params.pop('note', None)

        title_transfer = TitleTransfer.build({**params, 'commodity_contract_id': self.id})
        if not title_transfer.errors:
            title_transfer.save()
            title_transfer.update_note_data(note)
            params = {}
            if communication_params or self.is_spot_contract:
                communication_params.pop('acceptance_required', True)
                acceptance_request = TitleTransferAcceptanceRequest.create({
                    'type': CONFIRM_REQUEST_TYPE,
                    'title_transfer_id': title_transfer.id,
                    'created_by_id': self.created_by.id,
                    'updated_by_id': self.created_by.id,
                    'resolved': True
                })
                TitleTransferCommunication.create(
                    communication_params,
                    kwargs={
                        'title_transfer_id': title_transfer.id,
                        'request_id': acceptance_request.id,
                        'type': 'create'
                    }
                )
                params = {'acceptance_request_id': acceptance_request.id}
            if create_audit:
                title_transfer.audit(action='create', user=title_transfer.created_by, **params)
            if notification and title_transfer.persisted and not title_transfer.is_draft() and not is_cashed_out:
                self.handle_notifications(
                    title_transfer.identifier, title_transfer.tonnage, title_transfer.site_display_name,
                    title_transfer.created_by
                )
            can_send_creation_email = (
                self.is_spot_contract or bool(get(communication_params, 'subject'))
            )
            if process and not title_transfer.external_system:
                title_transfer.queue_mail_and_process(can_send_creation_email)
        return title_transfer

    def void_linked_freight_orders(self, reason=None):
        freight_orders = self.freight_orders.filter(level=0).exclude(
            status__in=['void', 'rejected']
        )
        with transaction.atomic():
            for freight_order in freight_orders:
                freight_order.create_void_request(reason)
                freight_order.void()
                freight_order.save()

    def reject_linked_freight_orders(self):
        freight_orders = self.freight_orders.filter(level=0)
        with transaction.atomic():
            for freight_order in freight_orders:
                freight_order.create_reject_request()
            update_with_last_modified_time(
                freight_orders, status='rejected'
            )

    @property
    def _template_mandatory_fields(self):
        return ['template_name']

    @property
    def _mandatory_fields(self):
        mandatory_fields = [
            'seller', 'buyer', 'document_type', 'commodity', 'owner',
            'type', 'price_point', 'identifier', 'season', 'tonnage', 'price', 'delivery_onus',
            'payment_scale', 'payment_term', 'tolerance', 'delivery_start_date',
            'delivery_end_date', 'administration',
        ]
        if not self.is_pool_contract:
            mandatory_fields.append('grade')
        else:
            mandatory_fields.remove('price')
        if self.type_id == AREA_CONTRACT_TYPE_ID:
            mandatory_fields.remove('price')
        return mandatory_fields

    @property
    def is_pool_contract(self):
        return self.type_id in POOL_CONTRACT_TYPE_IDS

    @property
    def is_area_contract(self):
        return self.type_id == AREA_CONTRACT_TYPE_ID

    @property
    def is_blended(self):
        return self.type_id == CONTRACT_TYPE_BLENDED_ID

    @property
    def blended_grades_label(self):
        labels = []
        tonnage = self.inferred_tonnage
        for spread in sorted(get(self.spread, 'details', []), key=lambda spread_detail: spread_detail.get('quantity', 0), reverse=True):  # pylint: disable=not-an-iterable, line-too-long
            grade_name = get(spread, 'grade_name')
            quantity = get(spread, 'quantity')
            if grade_name and quantity:
                percentage = round((quantity/tonnage) * 100, 2)
                if str(percentage).endswith('.0'):
                    percentage = str(percentage).replace('.0', '')
                labels.append(f'{grade_name} {percentage}%')
        return ' / '.join(labels)

    @property
    def price_label(self):
        if self.is_pool_contract:
            return

        return f"Price (per {self.inferred_tonnage_unit})"

    @property
    def tonnage_label(self):
        _tonnage = self.country.get_label('tonnage')
        return f'Maximum {_tonnage}' if self.is_area_contract else _tonnage

    @property
    def grade_label(self):
        return 'Base Grade' if self.is_area_contract else 'Grade'

    @property
    def site_ld_label(self):
        return self.__ld_label(self.price_point_id)

    def __ld_label(self, price_point_id):
        return 'Price Differential' if price_point_id == PRICE_POINTS_DELIVERED_BUYER_ID else 'LD'

    @property
    def amended_site_ld_label(self):
        amended_price_point_id = get(self, 'amended_details.price_point_id')
        if amended_price_point_id:
            return self.__ld_label(amended_price_point_id)

        return self.__ld_label(self.price_point_id)

    @property
    def pool_grades(self):
        if not self.commodity_id:
            return ''
        params = {'variety_id': self.variety_id, } if self.variety_id else None
        if self.commodity_id == WHEAT_COMMODITY_ID and self.variety_id and self.market_zone_id:
            params.update({'zone': self.market_zone.wheat_classification_zone, })
        elif params:
            params.pop('variety_id')

        return ", ".join(list(self.commodity.grades(params=params or None).values_list('name', flat=True)))

    @classmethod
    def contacts_for(cls, companies=None, farms=None, only_company_admin=False):
        if not companies and not farms:
            return []
        criteria = None
        if companies:
            criteria = models.Q(company_id__in=companies)
        if farms:
            if criteria is None:
                criteria = models.Q(farms__id__in=farms)
            else:
                criteria |= models.Q(farms__id__in=farms)

        queryset = Employee.objects.filter(id__in=Employee.objects.filter(criteria).values_list('id', flat=True))
        if only_company_admin:
            queryset = queryset.filter(type_id=core.common.constants.COMPANY_ADMIN_TYPE_ID)
        else:
            queryset = queryset.exclude(type_id=core.common.constants.OBSERVER_TYPE_ID)

        return queryset.order_by('first_name', 'last_name')

    @classmethod
    def farm_parties(cls, company):
        if company.is_broker:
            farms = company.managed_farms
        else:
            farms = company.directory_companies_farms(include_self=True)

        return farms.select_related('farmacceptancerequest', 'company', 'created_by').order_by('name')

    @classmethod
    def parties_minimal(cls, user, user_properties=None):
        user_properties = user_properties or []
        company = user.company
        farm_parties = cls.farm_parties(company=company)
        farm_company_ids = set(farm_parties.values_list('company_id', flat=True))
        company_ids = set(company.directory_companies_ids + [company.id])
        company_parties = Company.objects.filter(
            id__in=(company_ids - farm_company_ids)
        ).order_by('business_name')

        if 'brokerages_for_user' in user_properties and user:
            company_parties = company_parties.prefetch_related(
                Prefetch(
                    'brokerages_set',
                    to_attr='user_brokerages',
                    queryset=Brokerage.objects.filter(broker_company_id=user.company_id)
                )
            )

        return {
            'farms': farm_parties,
            'companies': company_parties
        }

    @classmethod
    def parties(cls, creator_company):
        added_companies_ids = creator_company.get_companies(return_ids=True)
        added_companies_ids.append(creator_company.id)
        if creator_company.is_broker:
            farm_directory = creator_company.farm_directory_set.select_related(
                'farm', 'farm__address',
                'farm__company__platformfeatures', 'farm__company__type',
                'farm__farmacceptancerequest', 'farm__created_by',
            ).filter(
                models.Q(
                    models.Q(
                        farm__farmacceptancerequest__isnull=True
                    ) | models.Q(
                        farm__farmacceptancerequest__accepted=True,
                        farm__farmacceptancerequest__resolved=True,
                    )
                )
            ).order_by('farm__name')
            farms = [d.farm for d in farm_directory]
        else:
            farms = Farm.objects.select_related(
                'address', 'company__type', 'farmacceptancerequest',
                'company__platformfeatures'
            ).filter(
                models.Q(
                    company_id__in=added_companies_ids
                ) & models.Q(
                    models.Q(farmacceptancerequest__isnull=True) | models.Q(
                        farmacceptancerequest__accepted=True,
                        farmacceptancerequest__resolved=True,
                    )
                )
            ).order_by('name')

        farm_company_ids = [f.company_id for f in farms]
        companies_ids = [
            _id for _id in added_companies_ids if _id not in farm_company_ids
        ]
        other_companies = Company.objects.select_related(
            'type', 'address', 'platformfeatures'
        ).filter(id__in=companies_ids).order_by('business_name')

        return {
            'farms': farms,
            'companies': other_companies
        }

    def process_draft_transactions(self):
        self.process_draft_freight_orders()
        self.process_draft_freight_contracts()
        self.process_draft_title_transfers()

    def process_draft_title_transfers(self):
        from core.jobs.models import Job
        draft_title_transfers = self.titletransfer_set.filter(status=DRAFT_STATUS)
        requests = self.acceptance_requests.filter(
            resolved=False,
            type='title_transfer',
            contract_id=self.id
        )
        for request in requests:
            request.mark_accepted()
        for tt in draft_title_transfers:
            tt.status = PLANNED
            tt.save()
            if tt.is_in_past:
                Job.schedule_job_for_task('process_title_transfer', tt.id)
            Job.schedule_job_for_task(
                'send_title_transfer_created_mail', params={'id': tt.id}
            )

            self.handle_notifications(tt.identifier, tt.inferred_tonnage, tt.bhc_site, tt.created_by)

    def process_draft_freight_contracts(self):
        draft_freight_contracts = self.freight_contracts.filter(status=DRAFT_STATUS)
        from core.freights.models import FreightContractAcceptanceRequest
        for fm in draft_freight_contracts:
            creator = fm.created_by
            if (fm.is_customer(creator) and fm.is_freight_provider(creator)) or fm.is_self:
                fm.status = 'confirmed'
                fm.save()
            else:
                fm.move_to_planned_status()

            fm.trigger_freights_job(
                'send_freight_contract_created_mail', {'movement_id': fm.id, 'scheduled': True}, on_commit=True
            )

            fm.handle_notifications()
            receiver = fm.get_receiver_for_acceptance_request(user=creator)
            request = None
            if receiver:
                fm.send_notifications(REQUEST_RESOLVER[receiver][RECEIVE_INDEX], fm.created_by)
                request = FreightContractAcceptanceRequest.create({
                    'type': CONFIRM_REQUEST_TYPE,
                    'receiver': receiver,
                    'contract_id': fm.id,
                    'created_by_id': fm.created_by.id,
                    'updated_by_id': fm.created_by.id
                })

            fm.audit(
                action='create',
                user=fm.created_by,
                acceptance_request_id=request.id if request else None
            )
            if fm.status in ['confirmed', 'delivered']:
                fm.audit(action='accept')

    def process_draft_freight_orders(self):
        draft_freight_orders = self.freight_orders.filter(status=DRAFT_STATUS)
        for fo in draft_freight_orders:
            creator = fo.created_by
            if fo.is_call_on_grain or fo.is_self or (
                    fo.is_customer(creator) and fo.is_freight_provider(creator)
            ):
                fo.status = 'confirmed'
                fo.save()
                request = None
            else:
                fo.move_to_planned_status()
                request = fo.create_confirm_request(creator)

            fo.audit(
                action='create',
                user=fo.created_by,
                acceptance_request_id=request.id if request else None
            )
            if fo.status in ['confirmed', 'open']:
                fo.audit(action='accept')

            fo.trigger_freights_job(
                'send_freight_order_created_mail', {'freight_order_id': fo.id, 'scheduled': True}, on_commit=True
            )

    def generate_pdf(self, party):
        html_for_pdf = render_to_string(
            'preview.html',
            {
                'contract': self,
                'act': False,
                'urlprefix': core.common.constants.STATIC_URL_PREFIX,
                'grade_name': get_grade_name(self),
                'party': party,
                'footer': True,
            }
        )
        pdf = HTMLToPDF.from_string(html_for_pdf, False)
        S3.upload(self.pdf_path(party=party), pdf)

    def pdf_path(self, party):
        return 'contracts/' + str(self.id) + '/' + party + '/' + self.pdf_name()

    def pdf_name(self):
        if self.document_type_id == CONTRACT_DOCUMENT_TYPE_ID:
            name = self.reference_number
        else:
            name = self.identifier
        return name + '.pdf'

    def get_party_for_pdf(self, user):
        party = None
        if user:
            if self.is_creator(user) or user.is_staff:
                party = 'creator'
            elif self.is_seller(user):
                party = 'seller'
            elif self.is_buyer(user):
                party = 'buyer'

        return party

    def get_party_for_pdf_for_amended_email(self, user):
        party = None
        if user:
            broker_by_id = get(self, 'amended_details.administration.brokered_by_id')
            is_broker = user.company_id == broker_by_id if broker_by_id else self.is_broker(user)
            if self.is_direct_creator_company_user(user) or is_broker:
                party = 'creator'
            elif self.is_seller(user):
                party = 'seller'
            elif self.is_buyer(user):
                party = 'buyer'

        return party

    def pdf_url(self, user):
        return self.get_pdf_url_for(self.get_party_for_pdf(user))

    def get_pdf_url_for(self, party):
        url = None
        try:
            url = S3.url_for(self.pdf_path(party))
        except:  # pylint: disable=bare-except
            pass
        return url

    def seller_pdf_url(self, user):
        if user and (user.is_staff or self.can_view_seller_brokerage(user)):
            return self.get_pdf_url_for('seller')

    def buyer_pdf_url(self, user):
        if user and (user.is_staff or self.can_view_buyer_brokerage(user)):
            return self.get_pdf_url_for('buyer')

    @property
    def name(self):
        return self.template_name or self.reference_number

    def to_class(name):
        if name.lower() == 'area':
            return ContractArea
        return getattr(sys.modules[__name__], name.capitalize())

    @staticmethod
    def _121_rels(): # pylint: disable=no-method-argument
        return {
            'seller': None,
            'buyer': None,
            'administration': None,
            'spread': None,
            'area': None,
        }

    @classmethod
    def _build_121_deps(cls, params, rels):
        _rels = rels.copy()
        for rel in _rels:
            if rel in params:
                rel_params = params.pop(rel, {})
                if isinstance(rel_params, dict):
                    rel_params.update({'status': params.get('status', 'planned')})
                    rels[rel] = cls.to_class(rel).build(rel_params, rel.capitalize())
            else:
                rels.pop(rel)
        return rels

    def get_unaccounted_tonnage(self, amend_tonnage=None):
        return float(
            "%0.2f" % (
                    self.expected_tonnage_range(amend_tonnage)[1] - (
                        self.direct_freight_movements_tonnage +
                        self.direct_freight_orders_tonnage +
                        self.all_title_transfered_tonnage
                    )
            )
        )

    @property
    def is_spot_contract(self):
        return self.type_id == CONTRACT_TYPE_SPOT_ID

    def allow_attaching_party_signature(self, user):
        if self.is_seller(user):
            self.show_seller_signature = True
        elif self.is_buyer(user):
            self.show_buyer_signature = True
        self.save()

    @property
    def is_created_by_seller(self):
        return self.is_seller(self.created_by)

    @property
    def is_created_by_buyer(self):
        return self.is_buyer(self.created_by)

    @classmethod
    def persist(cls, params, tz=None, can_send_create_email=False, spot_contract_from_title_transfer=False): # pylint: disable=too-many-branches, too-many-statements, too-many-locals
        from core.jobs.models import Job
        from core.vendor_decs.models import EligibilityDeclaration
        communication_params = params.pop('communication', {})
        brokerages_params = params.pop('brokerages', [{}, {}])
        chemical_applications = params.pop('chemical_applications', [])
        rels = cls._121_rels()
        note = params.pop('crm_notes', {})
        consignee_params = params.pop('consignees', None)
        consignor_params = params.pop('consignors', None)
        contract = Contract(**{k: v for k, v in params.items() if k not in rels})
        original_contract = None
        if 'id' in params:
            original_contract = Contract.objects.get(id=params['id'])
            for field in original_contract._meta.fields:
                if field.name not in params:
                    setattr(contract, field.name, getattr(original_contract, field.name, None))
        try:
            with transaction.atomic():
                if not original_contract:
                    contract.full_clean_errors()

                cls._build_121_deps(params, rels)
                for rel, obj in rels.items():
                    if obj:
                        obj.full_clean()
                        obj._save()
                        setattr(contract, rel, obj)

                is_contract_template = contract.status == 'template'
                if not contract.persisted and deepgetattr(contract, 'document_type.name') == 'contract':
                    contract.contract_number = contract.identifier
                if (not is_contract_template and contract.buyer and
                        contract.buyer.company.is_contract_internal_reference_on):
                    reference_number = contract.buyer.company.generate_internal_reference_number_for(
                        NOTIFICATION_TYPE_CONTRACT)
                    contract.buyer_internal_reference_number = reference_number
                if (not is_contract_template and contract.seller and
                        contract.seller.company.is_contract_internal_reference_on):
                    reference_number = contract.seller.company.generate_internal_reference_number_for(
                        NOTIFICATION_TYPE_CONTRACT)
                    contract.seller_internal_reference_number = reference_number
                contract.raise_errors_if_unclean()
                contract.unaccounted_tonnage = contract.expected_tonnage[1]
                contract.save_only()
                contract.brokerage_set.all().delete()
                for role, party_params in {'Consignor': consignor_params, 'Consignee': consignee_params}.items():
                    if party_params:
                        contract.handlers(role=role).delete()
                        [item.update({
                            'contract_id': contract.id,
                            'status': contract.status,
                            'created_by_id': contract.created_by_id,
                            'updated_by_id': contract.updated_by_id,
                        }) for item in party_params]
                        ContractCommodityHandler.build_with_save(party_params, identity=role)

                Brokerage.build_with_save(
                    brokerages_params,
                    entity_object=contract,
                    status=contract.status
                )
                ChemicalApplication.persist_many(chemical_applications, contract_id=contract.id)
                description = note.get('description', '').strip()
                if description or len(note.get('attachments', []) or []) > 0:
                    description = description if description else ATTACHMENT
                    note['object_id'] = contract.id
                    note['object_type_klass'] = Contract
                    note['created_by_id'] = contract.created_by.id
                    Note.persist({**note, 'description': description})

                contract.add_farm_parties_to_farm_directory()
                contract.add_parties_to_companies_directory()
                if contract.persisted:
                    if contract.is_created_by_seller and contract.seller_signature:
                        contract.show_seller_signature = True
                    elif contract.is_created_by_buyer and contract.buyer_signature:
                        contract.show_buyer_signature = True
                    contract.save()
                    EligibilityDeclaration.attach_declaration(contract)

                if not contract.is_delivered():
                    contract.handle_request(
                        user=contract.created_by,
                        request_type=CONTRACT_ACCEPTANCE_REQUEST_TYPE_CONFIRM,
                        args={},
                        request_function=contract.confirm,
                        communication_params=communication_params,
                    )
                if contract.status not in ['draft', 'template']:
                    request = contract.acceptance_request_for_confirmation()
                    contract_created.send(
                        sender=Contract,
                        instance=contract
                    )
                    if (
                        bool(communication_params.get('subject'))
                        or can_send_create_email
                    ):
                        contract_sent.send(
                            sender=Contract,
                            instance=contract,
                            created_by_id = get(contract.created_by, 'id'),
                            contract_acceptance_request=get(request, 'id'),
                            state='creation'
                        )
                        transaction.on_commit(
                            lambda: Job.schedule_job_for_task(
                                job_type='send_contract_created_mail',
                                params=contract.id
                            )
                        )

                if not contract.is_template():
                    contract.change_status_based_on_delivery_start_date()
                    contract.save_only()
                if contract.id and contract.is_spot_contract and not spot_contract_from_title_transfer:
                    TitleTransfer.create_from_contract_and_process(contract, tz)
                if contract.created_by.company.has_impex_docs_connection_for_manual_creation:
                    Job.schedule_job_for_task('get_sales_order_information_from_impex_docs', contract.id)
        except (IntegrityError, ValidationError, ErrorException) as ex:
            if isinstance(ex, ErrorException):
                setattr(contract, 'id', None)
                if ex.error_dict.get('identity') == 'Consignee':
                    setattr(contract, 'consignees_errors', [{'errors': ex.error_dict.get('errors')}])
                if ex.error_dict.get('identity') == 'Consignor':
                    setattr(contract, 'consignors_errors', [{'errors': ex.error_dict.get('errors')}])
            for rel, obj in rels.items():
                if obj:
                    obj.id = None
                    obj.full_clean_errors()
                    setattr(contract, rel, obj)
            if not contract.id:
                contract.full_clean_errors()
            exception_arg = get(ex, 'args[0]', '')
            if 'duplicate' in exception_arg and (
                    'contract_unique_identifier_viewer_ids' in exception_arg or
                    'contract_uniq_contract_number_viewer_ids' in exception_arg
            ):
                contract.errors = contract.errors or {}
                contract.errors['contract_number'] = ['Contract with this Contract number already exists']
        return contract

    def log_unique_party(self):
        buyer_company = self.buyer.company
        seller_company = self.seller.company
        slack_message = COMMODITY_CONTRACTS_SLACK_MESSAGE.format(
            env=settings.ENV.upper(),
            ref=self.reference_number,
            buyer=buyer_company.name,
            seller=seller_company.name
        )
        uniq = False
        if buyer_company.can_upload_contract_csv:
            uniq = not Contract.for_company(
                buyer_company.id, {}).exclude(id=self.id).filter(seller__company_id=seller_company.id).exists()
        elif seller_company.can_upload_contract_csv:
            uniq = not Contract.for_company(
                seller_company.id, {}).exclude(id=self.id).filter(buyer__company_id=buyer_company.id).exists()

        if uniq:
            Slack(Slack.COMMODITY_CONTRACTS_CHANNEL).log(slack_message)

    def set_contract_number_uniqueness_error(self):
        error = not self.is_unique_numbers

        if error:
            self.errors = self.errors or {}
            self.errors['contract_number'] = ['Contract with this Contract number already exists']

    def add_farm_parties_to_farm_directory(self):
        if self.status not in ['template', 'draft']:
            farm_party_ids = set(compact(list(self.contractcommodityhandler_set.values_list('handler_id', flat=True))))  #pylint: disable=line-too-long

            brokers = {
                broker for broker in [
                    get(self, 'seller.represented_by'), get(
                        self, 'buyer.represented_by')
                ] if broker
            }

            for broker in brokers:
                broker.add_farms_to_directory(farm_party_ids)

            creator_company = self.created_by.company
            if creator_company.is_broker:
                creator_company.add_farms_to_directory(farm_party_ids)

    def add_parties_to_companies_directory(self, sync=True):
        if not sync:
            from core.jobs.models import Job
            Job.schedule_job_for_task('add_parties_to_directories', {'contract_ids': [self.id]}, True)
            return

        creator_company_id = self.created_by.company_id
        seller_company_id = get(self, 'seller.party_company_id')
        buyer_company_id = get(self, 'buyer.party_company_id')
        seller_broker_company_id = get(self, 'seller.represented_by_id')
        buyer_broker_company_id = get(self, 'buyer.represented_by_id')
        brokered_by_id = get(self.administration, 'brokered_by_id')

        ids = [creator_company_id, seller_company_id, buyer_company_id,
               seller_broker_company_id, buyer_broker_company_id, brokered_by_id]
        ids += list(self.contractcommodityhandler_set.values_list('handler__company_id', flat=True))

        unique_ids = list(set(compact(ids)))

        for party_id in unique_ids:
            Company.add_companies_to_directory(party_id, unique_ids)

    def is_both_parties_managed_by(self, user=None):
        user = user or self.created_by
        return self.status not in self.TEMPLATE_OR_DRAFT_STATUSES and \
            self.is_seller(user) and \
            self.is_buyer(user)

    def new_contract_notification_msg(self, sender, action_verb='sent you a'):
        message = '<b>' + sender.name + '</b> from <b>' + sender.company.name + \
            '</b> has ' + action_verb + ' ' + \
            self.document_type.display_name + ' (<b>' + self.identifier + '</b> | ' + \
            self.commodity.display_name + ' | '
        message += self.pool_grades + ' | ' if self.is_pool_contract else self.grade.name + ' | '
        message += str(self.inferred_tonnage) + '{unit})'.format(unit=self.inferred_tonnage_unit)
        return message

    def party_contact(self, party, amend=False):
        contact = None
        if amend:
            contact = self.__amend_party_contact(party)

        if not contact and party == 'seller':
            contact = deepgetattr(self, 'seller.contact')
        elif not contact and party == 'buyer':
            contact = deepgetattr(self, 'buyer.contact')
        elif not contact and party == 'broker':
            contact = self.broker_contact

        return contact

    @property
    def broker_contact(self):
        return get(self, 'administration.broker_contact')

    @property
    def last_email_action(self):
        pending_request = self.acceptance_requests.filter().last()

        has_last_pending_email = bool(
            pending_request and
            not get(pending_request, 'communication.mail_status')
        )
        return {
            'has_pending_email': has_last_pending_email,
            'action_type': pending_request.type.capitalize() if pending_request and pending_request.type else None
        }

    def __amend_party_contact(self, party):
        contact = None
        if party == 'seller':
            contact = get(self, 'amended_details.seller.contact_id')
        elif party == 'buyer':
            contact = get(self, 'amended_details.buyer.contact_id')
        elif party == 'broker':
            contact = get(self, 'amended_details.administration.broker_contact_id')

        return contact

    def amended_mail_subject(self):
        return self.__request_mail_subject(self.last_amend_request(), True)

    def created_mail_subject(self):
        return self.__request_mail_subject(self.acceptance_request_for_confirmation(), False)

    def __request_mail_subject(self, request, is_amend):
        return deepgetattr(
            request, 'communication.subject'
        ) or self.get_default_email_subject(get(request, 'created_by'), is_amend)

    def get_default_email_subject(self, user, is_amend):
        identifier = self.identifier.upper()
        subject = constants.DEFAULT_AMENDED_MAIL_SUBJECT.format(identifier=identifier) if is_amend else \
            constants.DEFAULT_MAIL_SUBJECT.format(identifier=identifier)
        if user:
            amending_text = "[Amendment] " if is_amend else ""
            space = ' '
            ac = space + 'a/c' + space
            hash_tag = '#'
            representing_text = ''
            if self.is_seller_managed_by_user(user):
                representing_text = ac + get(self, 'seller.company.display_name', '')
            if self.is_buyer_managed_by_user(user):
                buyer = get(self, 'buyer.company.display_name', '')
                representing_text = representing_text + space + '&' + space + buyer if representing_text else ac + \
                                                                                                                buyer
            subject = amending_text + user.company.name + space + self.document_type.display_name
            if user.company.is_broker:
                subject += representing_text
            subject += space + hash_tag + identifier
        return subject

    def created_mail_body_header(self):
        return self.__request_mail_body(self.acceptance_request_for_confirmation())

    def amended_mail_body_header(self):
        return self.__request_mail_body(self.last_amend_request())

    def __request_mail_body(self, request):
        body = deepgetattr(request, 'communication.body')

        if body:
            body = body.replace("\n", "<br />")

        return body

    def acceptance_request_for_confirmation(self):
        return self.acceptance_requests.filter(
            type=CONTRACT_ACCEPTANCE_REQUEST_TYPE_CONFIRM
        ).select_related('communication').first()

    def request_communication_dict(self, request_id):
        request = self.acceptance_requests.filter(id=request_id).select_related('communication').first()
        communication = get(request, 'communication')
        if communication:
            return communication.to_dict()

    def last_amend_request(self):
        return self.acceptance_requests.filter(
            type=CONTRACT_ACCEPTANCE_REQUEST_TYPE_AMEND
        ).select_related('communication').last()

    def last_void_request(self):
        return self.acceptance_requests.filter(
            type=CONTRACT_ACCEPTANCE_REQUEST_TYPE_VOID
        ).select_related('communication').last()

    def _request_actor_parties(self, request):
        if (request.is_confirm or request.is_amend) and not request.acceptance_required:
            return []

        party = request.created_by if request else self.created_by
        if self.is_both_parties_managed_by(party):
            return []

        parties = []
        broker_contact = self.broker_contact
        if self.is_buyer(party):
            parties.append('seller')
            if broker_contact and self.is_seller(broker_contact):
                parties.append('broker')
        elif self.is_seller(party):
            parties.append('buyer')
            if broker_contact and self.is_buyer(broker_contact):
                parties.append('broker')

        return parties

    def get_communication(self, action_type):
        request = self.acceptance_requests.filter(
            type=action_type
        ).select_related('communication').first()
        return getattr(request, 'communication', None)

    def get_recipients(self, request=None):
        request = request or self.acceptance_request_for_confirmation()
        communication = getattr(request, 'communication', None)
        if communication:
            recipients = communication.recipients or {}
            actor_parties = self._request_actor_parties(request)
            readonly_recipients = {k: v for k, v in recipients.items() if k not in actor_parties}
            for party in readonly_recipients.copy().keys():
                if self.is_creator(
                        self.party_contact(party)
                ) and 'creator' not in readonly_recipients:
                    readonly_recipients['creator'] = readonly_recipients.pop(party)

            actor_recipients = {k: v for k, v in recipients.items() if k in actor_parties}

            return [readonly_recipients, actor_recipients]
        else:
            return [None, None]

    def get_amend_recipients(self):
        request = self.last_amend_request()
        if request:
            return self.get_recipients(request)
        else:
            return [None, None]

    def get_void_recipients_for_mail(self):
        request = self.last_void_request()
        if request:
            return self.get_recipients(request)
        else:
            return [None, None]

    def show_amended_brokerage(self, party):
        show_amended_brokerage = False
        if not party:
            return show_amended_brokerage
        amended_details = self.amended_details
        brokerages = get(amended_details, 'brokerages', [])
        amended_administration = get(amended_details, 'administration')
        administration = get(self, 'administration')
        if amended_details and (brokerages or amended_administration):
            if amended_administration and (
                    get(administration, 'brokered_by_id') != get(amended_administration, 'brokered_by_id.id') or
                    get(administration, 'broker_contact_id') != get(amended_administration, 'broker_contact_id.id')
            ):
                show_amended_brokerage = True
            for brokerage in brokerages:
                brokerage_type = get(brokerage, 'type')
                if party in ['seller', 'creator'] and brokerage_type == 'Seller':
                    show_amended_brokerage = True
                if party in ['buyer', 'creator'] and brokerage_type == 'Buyer':
                    show_amended_brokerage = True
        return show_amended_brokerage

    @classmethod
    def templates(cls, user, limit=None):
        _template_query_set = Contract.objects.filter(
            status='template',
            created_by__company_id=user.company_id,
            is_active=True
        ).only(
            'id', 'identifier', 'contract_number', 'template_name', 'contract_date'
        ).order_by(
            '-contract_date', '-id'
        ).annotate(
            order=models.Value(2, output_field=models.IntegerField())
        )
        _last_x_contract_query_set = Contract.objects.filter(
            created_by_id=user.id,
            is_active=True
        ).only(
            'id', 'identifier', 'contract_number', 'template_name', 'contract_date'
        ).order_by(
            '-contract_date', '-id'
        ).exclude(status='template').annotate(
            order=models.Value(1, output_field=models.IntegerField())
        )

        if limit:
            _last_x_contract_query_set = _last_x_contract_query_set[:limit]

        return _template_query_set.union(_last_x_contract_query_set).order_by('order', '-contract_date', '-id')

    @classmethod
    def for_company(cls, company_id, query_params=None):
        return cls._for(company_id, query_params)

    @classmethod
    def for_company_contract_ids(
            cls, company_id
    ):
        return cls.for_company_contract_queryset(company_id).values_list('id', flat=True)

    @classmethod
    def for_company_contract_queryset(cls, company_id):
        return cls.objects.exclude(status='template').filter(viewer_company_ids__contains=[company_id])

    @classmethod
    def filter_by(cls, params):
        company_id = params.pop('company_id')[0]
        _queryset = cls.objects.exclude(status__in=['template', 'draft']).filter(**to_query_filters(dict(params)))
        return _queryset.filter(
            models.Q(
                delivery_onus='Buyer',
                buyer__company_id=company_id,
            ) | models.Q(
                delivery_onus='Seller',
                seller__company_id=company_id,
            )
        ).only(
            'identifier', 'season', 'price', 'tonnage', 'quantity',
            'delivery_start_date', 'delivery_end_date',
            'commodity__id', 'commodity__name', 'status',
            'variety_id', 'grade_id', 'administration',
            'seller', 'buyer', 'delivery_onus',
            'price_point__name', 'grade__name', 'spread', 'weight_id', 'conveyance', 'tolerance',
        )

    @classmethod
    def for_assigning_to_freight(cls, freight, company_id):
        params = cls.for_assigning_to_movement_params(freight)
        customer_company_id = params.pop('customer_company_id', None)
        consignee_handler_id = params.pop('consignee_handler_id')
        consignor_handler_id = params.pop('consignor_handler_id')
        queryset = cls.objects.exclude(status__in=NOT_ASSIGNABLE_CONTRACT_STATUSES).filter(**params)
        if not freight.is_order_entity and freight.is_commodity_contract_invoiced:
            queryset = queryset.filter(id=freight.commodity_contract_id)
        else:
            if freight.commodity_contract_id and not get(freight, 'order_id'):
                queryset = queryset.exclude(id=freight.commodity_contract_id)

            if freight.commodity_contract_id:
                queryset = queryset.filter(
                    models.Q(
                        buyer__company_id__in=[freight.commodity_contract.buyer.company_id,
                                               freight.commodity_contract.seller.company_id]
                    ) | models.Q(
                        seller__company_id__in=[freight.commodity_contract.buyer.company_id,
                                                freight.commodity_contract.seller.company_id]
                    ))
                if freight.created_by.company_id in [freight.commodity_contract.buyer.company_id,
                                                     freight.commodity_contract.seller.company_id]:
                    queryset = queryset.filter(
                        models.Q(
                            buyer__company_id=freight.created_by.company_id,
                        ) | models.Q(
                            seller__company_id=freight.created_by.company_id
                        ))
                elif freight.created_by.company_id == freight.commodity_contract.buyer.represented_by_id:
                    queryset = queryset.filter(
                        models.Q(
                            buyer__company_id=freight.commodity_contract.buyer.company_id,
                            buyer__represented_by_id=freight.commodity_contract.buyer.represented_by_id
                        ) | models.Q(
                            seller__company_id=freight.commodity_contract.buyer.company_id,
                            seller__represented_by_id=freight.commodity_contract.buyer.represented_by_id
                        ))
                elif freight.created_by.company_id == freight.commodity_contract.seller.represented_by_id:
                    queryset = queryset.filter(
                        models.Q(
                            buyer__company_id=freight.commodity_contract.seller.company_id,
                            buyer__represented_by_id=freight.commodity_contract.seller.represented_by_id
                        ) | models.Q(
                            seller__company_id=freight.commodity_contract.seller.company_id,
                            seller__represented_by_id=freight.commodity_contract.seller.represented_by_id
                        ))
            elif customer_company_id:
                queryset = queryset.filter(
                    models.Q(
                        buyer__company_id__in=[customer_company_id, freight.created_by.company_id],
                    ) | models.Q(
                        seller__company_id__in=[customer_company_id, freight.created_by.company_id]
                    ))
            if consignor_handler_id:
                queryset = queryset.filter(
                    models.Q(
                        price_point_id__in=CONSIGNOR_MANDATORY_PRICE_POINTS,
                        contractcommodityhandler_set__handler_id=consignor_handler_id,
                        contractcommodityhandler_set__role=CONSIGNOR_ROLE,
                    ) | models.Q(
                        price_point_id__in=CONSIGNOR_OPTIONAL_PRICE_POINTS
                    ))
            if consignee_handler_id:
                queryset = queryset.filter(
                    models.Q(
                        price_point_id__in=CONSIGNEE_MANDATORY_PRICE_POINTS,
                        contractcommodityhandler_set__handler_id=consignee_handler_id,
                        contractcommodityhandler_set__role=CONSIGNEE_ROLE,
                    ) | models.Q(
                        price_point_id__in=CONSIGNEE_OPTIONAL_PRICE_POINTS
                    )
                )
            queryset = queryset.filter(
                unaccounted_tonnage__gte=freight.inferred_tonnage
            )
            if company_id:
                queryset = queryset.filter(viewer_company_ids__contains=[company_id])

        return cls.objects.filter(id__in=queryset.values_list('id', flat=True))

    @classmethod
    def filter_by_company(cls, company_id, queryset):
        if company_id:
            queryset = queryset.filter(
                models.Q(
                    owner_id=company_id
                ) | models.Q(
                    seller__company_id=company_id
                ) | models.Q(
                    seller__represented_by_id=company_id,
                ) | models.Q(
                    buyer__company_id=company_id
                ) | models.Q(
                    buyer__represented_by_id=company_id,
                )
            )
        return queryset

    @classmethod
    def for_assigning_to_movement_params(cls, freight):
        base_params = {
            'commodity_id': freight.commodity_id,
            'customer_company_id': None,
            'consignee_handler_id': None,
            'consignor_handler_id': None,
        }

        if freight.customer:
            base_params['customer_company_id'] = freight.customer.company_id
        if freight.freight_delivery_id:
            inload = freight.inload if freight.is_freight_contract else None
            if get(inload, 'storage_id'):
                base_params['consignee_handler_id'] = inload.storage.farm
            elif freight.freight_delivery.consignee:
                base_params['consignee_handler_id'] = freight.freight_delivery.consignee.handler_id
        if freight.freight_pickup_id:
            outload = freight.outload if freight.is_freight_contract else None
            if get(outload, 'storage_id'):
                base_params['consignor_handler_id'] = outload.storage.farm
            elif freight.freight_pickup.consignor:
                base_params['consignor_handler_id'] = freight.freight_pickup.consignor.handler_id

        return base_params

    @classmethod
    def _apply_separate_status_filters_if_needed(cls, queryset, params):
        if 'status' in params and isinstance(params['status'], list):
            if len(params['status']) == 1:
                params['status'] = params['status'][0]
            else:
                queryset = queryset.filter(to_fsm_field_filters('status', params.pop('status')))

        return queryset, params

    def set_viewer_company_ids(self):
        Contract.objects.filter(id=self.id).update(viewer_company_ids=self.get_viewer_company_ids())

    def get_viewer_company_ids(self):
        return compact({
            self.owner_id,
            *(get(self, 'seller.company_ids') or []),
            *(get(self, 'buyer.company_ids') or []),
            get(self, 'created_by.company_id'),
            get(self, 'administration.brokered_by_id'),
            *SYSTEM_COMPANY_IDS
        })

    def get_viewer_company_ids_without_system(self):
        return [company_id for company_id in self.get_viewer_company_ids() if company_id not in SYSTEM_COMPANY_IDS]

    @classmethod
    def for_company_only_queryset(cls, company_id):
        return cls.objects.exclude(status='template').filter(viewer_company_ids__contains=[company_id])

    @classmethod
    # pylint: disable=too-many-arguments
    def _for(cls, company_id, query_params=None):
        queryset = cls.for_company_only_queryset(company_id)

        if query_params:
            seller_company_id = query_params.pop('seller_company_id', None)
            buyer_company_id = query_params.pop('buyer_company_id', None)
            params = dict(query_params)
            remove_pagination_query_params(params)
            queryset, params = cls._apply_separate_status_filters_if_needed(queryset, params)

            start_date = params.pop('start_date', None)
            end_date = params.pop('end_date', None)
            ids = params.pop('id__in', None)
            from_date = params.pop('from_date', None)
            to_date = params.pop('to_date', None)
            params.pop('with_tonnage_distribution', None)
            params.pop('only_tonnage_distribution', None)
            if ids and all(ids):
                ids = ids[0].split(',')
                queryset = queryset.filter(id__in=[int(_id) for _id in ids])
            if start_date and all(start_date):
                queryset = queryset.filter(delivery_start_date__gte=datetime.strptime(head(start_date), "%Y-%m-%d"))
            if end_date and all(end_date):
                queryset = queryset.filter(delivery_end_date__lte=datetime.strptime(head(end_date), "%Y-%m-%d"))
            if from_date:
                from_date = DateTimeUtil.get_datetime_from_epoch(from_date[0])
            if to_date:
                to_date = DateTimeUtil.get_datetime_from_epoch(to_date[0])

            if from_date:
                queryset = queryset.filter(delivery_end_date__gte=from_date)
            if to_date:
                queryset = queryset.filter(delivery_start_date__lte=to_date)

            queryset = queryset.filter(**to_query_filters(params))
            if seller_company_id and buyer_company_id:
                queryset = queryset.filter(
                    seller__company_id__in=seller_company_id, buyer__company_id__in=buyer_company_id
                )

        queryset = queryset.select_related(
            'commodity', 'grade', 'conveyance', 'administration', 'seller', 'buyer', 'created_by'
        ).prefetch_related('acceptance_requests')

        return queryset

    @classmethod
    def for_farm(cls, farm_id, user, query_params=None):
        queryset = cls.objects.exclude(status='template')
        if query_params:
            queryset = queryset.filter(**to_query_filters(query_params))

        queryset = queryset.filter(contractcommodityhandler_set__handler_id__in=[farm_id])
        if user.company.is_broker:
            queryset = queryset.filter(
                models.Q(buyer__represented_by_id=user.company_id) |
                models.Q(seller__represented_by_id=user.company_id)
            )
        queryset = cls.objects.filter(id__in=queryset.values_list('id', flat=True))
        return queryset.select_related(
            'commodity', 'price_point', 'grade', 'seller', 'buyer').prefetch_related('freight_orders',)

    @property
    def is_buyer_registered_or_managed_by_registered(self):
        return deepgetattr(self, 'buyer.is_registered_or_managed_by_registered')

    @property
    def is_buyer_participating_in_transaction(self):
        return deepgetattr(self, 'buyer.is_participating_in_transaction')

    @property
    def is_seller_participating_in_transaction(self):
        return deepgetattr(self, 'seller.is_participating_in_transaction')

    @property
    def is_seller_registered_or_managed_by_registered(self):
        return deepgetattr(self, 'seller.is_registered_or_managed_by_registered')

    def can_edit(self, user=None):
        return user and self.status not in CONTRACT_NON_EDITABLE_STATUSES and self.is_creator_party(user) and (
            (
                self.has_x_hours_passed(
                    self.created_at
                ) and not self.is_counter_party_participating_in_transaction(user)
            ) or self.status_display_name(user) in constants.CONTRACT_EDIT_STATUSES
        ) and not self.is_amend_request_pending

    def edit_or_amend_label(self, user):
        if self.can_edit(user) and self.status in ['planned', 'confirmation_pending', 'rejected']:
            return 'Edit'
        if self.can_raise_amend_request(user) and self.status not in ['planned', 'confirmation_pending', 'rejected']:
            return 'Amend'
        return None

    def get_model_class(self, content_type):
        engine = inflect.engine()
        klass_name = inflection.camelize(content_type)
        if content_type == 'storage':
            klass = apps.get_model('farms', 'Storage')
        elif content_type == 'farmfield':
            klass = apps.get_model('farm_fields', 'FarmField')
        elif content_type == 'companysite':
            klass = apps.get_model('farms', 'Farm')
        else:
            klass = apps.get_model(engine.plural(content_type), klass_name)
        return klass

    def _get_object(self, model, value):
        obj = model.objects.filter(id=value).first()
        return obj.to_dict() if obj else None

    @property
    def amend_request_details(self):
        return self.acceptance_requests.filter(
            contract_id=self.id,
            resolved=False,
            type=CONTRACT_ACCEPTANCE_REQUEST_TYPE_AMEND
        ).first()

    def _get_area_amended_details(self, value):
        if value:
            area = get(value, 'area')
            estimated_yield = get(value, 'estimated_yield')
            if area and estimated_yield:
                value['estimated_tonnage'] = ContractArea.get_estimated_tonnage(float(area), float(estimated_yield))
        return value

    def amended_details_for_acceptance_request(self, contract_acceptance_request_id=None, send_last=False): # pylint: disable=line-too-long,too-many-locals,too-many-branches
        details = self.amendment_request_args(contract_acceptance_request_id)

        if not details and not contract_acceptance_request_id and send_last:
            details = self.acceptance_requests.filter(
                contract_id=self.id, type=CONTRACT_ACCEPTANCE_REQUEST_TYPE_AMEND
            ).order_by('-created_at')[0:1].values_list('args', flat=True)

        if not details:
            return None

        data = {}

        if 'amended' in get(details, '0').keys():
            amended = details[0]['amended']
            data['amended'] = self.amended_current_details(amended)
            if 'current' in get(details, '0').keys():
                current = details[0]['current']
                data['current'] = self.amended_current_details(current)
        else:
            data['amended'] = self.amended_current_details(details[0])

        return data

    def amended_current_details(self, details): # pylint: disable=line-too-long,too-many-locals,too-many-branches
        data = {}
        for field, value in details.items(): # pylint: disable=too-many-nested-blocks
            if field == 'area':
                data[field] = self._get_area_amended_details(value)
                continue
            if field in ['spread', 'brokerages', 'crm_notes', 'history_id']: # pylint: disable=no-else-continue
                data[field] = value
                continue
            elif field in ['seller', 'buyer', 'administration']:
                related_model = self._meta.get_field(field).related_model
                data[field] = {}
                for f, v in value.items():
                    f_m = related_model._meta.get_field(f).related_model
                    data[field][f] = self._get_object(f_m, v) if f_m and v else v
                if field in ['seller', 'buyer']:
                    self.get_amended_party_entity_name(data, field)
                continue
            elif field in ['consignors', 'consignees'] and value:
                data[field] = value
                for key, c_val in enumerate(value):
                    handler_id = c_val['handler_id']
                    if handler_id:
                        klass = self.get_model_class('companysite')
                        data[field][key]['handler'] = self._get_object(klass, handler_id)
                    if c_val.get('sites'):
                        for site_key, site_value in enumerate(c_val['sites']):
                            location_id = site_value['location_id']
                            location_type = get(site_value, 'location_content_type') or get(site_value, 'location_type')
                            if location_id and location_type:
                                site_klass = self.get_model_class(location_type)
                                data[field][key]['sites'][site_key]['location'] = self._get_object(site_klass, location_id) # pylint: disable=line-too-long
                continue
            elif field in ['consignors', 'consignees'] and value == []:
                data[field] = value
                continue
            else:
                field_model = self._meta.get_field(field).related_model
                data[field] = self._get_object(
                    field_model, value) if field_model and value and isinstance(value, (int, str)) else value
        return data

    def last_amended_details_for_acceptance_request(
            self, contract_acceptance_request_id=None, send_last=True
    ):
        return self.amended_details_for_acceptance_request(contract_acceptance_request_id, send_last)

    amended_details = property(amended_details_for_acceptance_request)
    last_amended_details = property(last_amended_details_for_acceptance_request)

    @property
    def amendment_details_minimal(self): #  pylint: disable=too-many-branches
        from core.locations.models import Location
        details = self.amended_details

        if details and details.keys():
            details = details['amended']

        if not details:
            return

        def get_address_details(address_id):
            if not address_id:
                return
            location = Location.objects.filter(id=address_id).first()
            if location:
                return {
                    'id': address_id,
                    'name': location.name,
                    'address': location.address,
                    'longitude': location.longitude,
                    'latitude': location.latitude,
                    'location_type': location.location_type
                }

        def get_handler_details(handler_data):
            __data = {
                'handler': {
                    'name': get(handler_data, 'handler.display_name'),
                    'id': get(handler_data, 'handler_id'),
                    'entity': get(handler_data, 'handler.entity'),
                    'position': get(handler_data, 'position') or 1,
                }
            }
            if 'sites' in handler_data:
                __data['sites'] = [
                    {
                        'location_id': site.get('location_id', None),
                        'location_content_type': site.get('location_type', None),
                        'ld': float(get(site, 'ld')) if site.get('ld', None) is not None else 0,
                        'location': {
                            'name': get(site, 'location.name'),
                            'entity': get(site, 'location.entity'),
                            'address': get_address_details(get(site, 'location.address_id'))
                        }
                    } for site in handler_data.get('sites', []) if site
                ]
            return __data

        data = {
            'site_ld_label': self.amended_site_ld_label,
            'contract_current_value': self.amended_contract_current_value,
            'contract_value': self.amended_contract_value,
            'contract_max_value': self.amended_contract_max_value,
        }
        for key, value in details.items():
            if key in [
                    'levy', 'price', 'track', 'tonnage', 'carry_max', 'carry_rate',
                    'carry_end_date', 'carry_frequency', 'carry_start_date', 'delivery_end_date',
                    'general_conditions', 'special_conditions', 'delivery_start_date',
                    'brokerages', 'delivery_onus', 'carry_current', 'contract_number', 'quantity',
            ]:
                if key in [
                        'levy', 'price', 'tonnage', 'carry_max', 'carry_rate', 'carry_current', 'quantity'
                ]:
                    data[key] = float(value)
                elif key == 'brokerages' and value:
                    for brokerage in value:
                        brokerage['rate'] = float(brokerage['rate'])
                    data[key] = value
                else:
                    data[key] = value
            elif key in ['buyer', 'seller']:
                data[key] = data.get(key, {})
                if 'ngr_id' in value:
                    data[key]['ngr_id'] = get(value, 'ngr_id.id')
                if 'contact_id' in value:
                    data[key]['contact_name'] = get(value, 'contact_id.name')
            elif key == 'spread':
                data[key] = get(value, 'details')
            elif key == 'consignors':
                data[key] = [
                    get_handler_details(
                        consignor
                    ) for consignor in value if consignor
                ]
            elif key == 'consignees':
                data['consignees'] = [
                    get_handler_details(
                        consignee
                    ) for consignee in value if consignee
                ]
            elif key in ['region_id', 'market_zone_id']:
                data[key] = get(value, 'id')
            elif key in [
                    'weight_id', 'packaging_id', 'tolerance_id', 'conveyance_id', 'inspection_id',
                    'price_point_id', 'payment_term_id',
            ]:
                new_key = key.replace('_id', '')
                if new_key == 'price_point':
                    data[new_key] = {'id': get(value, 'id'), 'display_name': get(value, 'display_name')}
                else:
                    data[new_key] = {'id': get(value, 'id'), 'name': get(value, 'name')}
            elif key == 'administration':
                if 'invoicing' in value:
                    data['contract_invoicing'] = value['invoicing']
                if 'brokered_by_id' in value:
                    data['broker_company'] = get(value, 'brokered_by_id.name')
                if 'broker_contact_id' in value:
                    data['brokered_by'] = get(value, 'broker_contact_id.name')

        return data

    def amendment_request_args(self, contract_acceptance_request_id=None):
        if contract_acceptance_request_id:
            args = {'id': contract_acceptance_request_id}
        else:
            args = {'contract_id': self.id, 'type': CONTRACT_ACCEPTANCE_REQUEST_TYPE_AMEND}
        return self.acceptance_requests.filter(**args).order_by('-created_at').values_list('args', flat=True)

    @property
    def has_freight_orders(self):
        return self.freight_orders.filter(parent_order_id=None).exclude(status__in=NULL_FREIGHT_STATUSES).exists()

    @property
    def has_freight_movements(self):
        return self.all_freight_contracts().exclude(status__in=NULL_FREIGHT_STATUSES).exists()

    @property
    def has_title_transfers(self):
        return self.titletransfer_set.filter(is_active=True).exclude(status__in=NULL_FREIGHT_STATUSES).exists()

    @property
    def has_raised_invoice(self):
        contract_content_type_id = self.content_type_ids_for(model_names=[CONTRACT_MODEL])[0]
        return apps.get_model('invoices', 'Invoice').objects.filter(
            raised_for_id=self.id,
            raised_for_type_id=contract_content_type_id,
            status='generated'
        ).exists()

    def grade_display_name(self):
        return get_grade_name(self)

    @cached_property
    def commodity_name(self):
        return get(self, 'commodity.display_name')

    @cached_property
    def variety_name(self):
        return get(self, 'variety.name')

    grade_name = property(grade_display_name)

    def amended_grade_display_name(self):
        amended_details = self.amended_details
        return get_grade_display_name(
            get(amended_details, 'commodity_id.id', self.commodity_id),
            get(amended_details, 'variety_id.id', self.variety_id),
            get(amended_details, 'grade_id.id', self.grade_id),
            get(amended_details, 'season', self.season),
        )

    def update_value_in_related_transactions(self, key, value):
        from core.freights.models import FreightContract
        if key == 'grade_id':
            key = 'planned_grade_id'
            self.titletransfer_set.update(grade_id=value)
        params = {key: value}
        FreightContract.update_linked_movements(self, params, False)
        self.freight_orders.filter().update(**params)

    def update_freight_details(self, key, value):
        from core.freights.models import FreightContract
        draft_freight_orders = self.freight_orders.filter(status=DRAFT_STATUS)
        freight_key = FREIGHT_DELIVERY if key == 'consignee' else FREIGHT_PICKUP
        params = deepcopy(value)
        FreightContract.update_linked_movements(self, {freight_key: {key: params}})
        for fo in draft_freight_orders:
            params_copy = deepcopy(params)
            fo.amend({freight_key: {key: params_copy}})

    def all_vendor_decs(self):
        from core.vendor_decs.models import CommodityVendorDec
        from core.vendor_decs.constants import GRAIN_COMMODITY_TYPE
        return CommodityVendorDec.by_contract_id(self.id, GRAIN_COMMODITY_TYPE)

    def get_internal_reference_number(self, user):
        if self.seller and user.company_id == self.seller.company_id:
            return self.seller_internal_reference_number
        if self.buyer and user.company_id == self.buyer.company_id:
            return self.buyer_internal_reference_number
        return None

    def get_formatted_audit_for_csv(self, instance_id, instance_type_id, date_format, tz=None):
        from core.audit_history.models import AuditHistory
        audits = AuditHistory.objects.filter(
            instance_id=instance_id, instance_type_id=instance_type_id).order_by('created_at')

        def get_action(description):
            action_variations = {
                'Created': ['create', 'created'],
                'Accepted Amendment': ['accepted the amendment'],
                'Rejected Amendment': ['rejected the amendment'],
                'Accepted Void': ['accepted the void'],
                'Rejected Void': ['rejected the void'],
                'Amended': ['amend', 'requested an amendment', 'edited', 'updated'],
                'Voided': ['void', 'voided'],
                'Accepted': ['accept', 'accepted'],
                'Rejected': ['reject', 'rejected'],
                'Close out': ['closed out'],
                'Paid': ['paid'],
                'Marked Complete': ['completed']
            }
            for action, variations in action_variations.items():
                if any(variation in description for variation in variations):
                    return action
            return ''

        if audits:
            formatted_audits = []
            for audit in audits:
                description = audit.description.lower()
                link_pattern = r'<a\s+href="[^"]+"[^>]*>.*?</a>'
                entities = ['title transfer', 'freight order', 'freight movement']
                if (any(entity in description for entity in entities) or
                        ('contract bid' in description and bool(re.search(link_pattern, description)))):
                    continue
                audit_action = get_action(description)
                if audit_action:
                    formatted_audits.append(f'{DateTimeUtil.localize_date(audit.created_at, tz, date_format)} -'
                                            f' {audit.created_by.name} ({audit_action})')
            return ' | '.join(formatted_audits)
        return ''

    def to_csv_row(self, user, tz, custom_csv_headers, country, blended_grade_detail=None):  # pylint: disable=too-many-locals, too-many-statements
        date_format = country.get_format('date')
        brokerages_info = self.brokerages_info(user)
        estimated_payment_due_date = self.estimated_payment_due_date_exact(default_today_for_unknown=False)
        if estimated_payment_due_date:
            estimated_payment_due_date = estimated_payment_due_date.strftime(date_format)

        carry_start_date = DateTimeUtil.localize_date(
            self.carry_start_date, tz, date_format
        ) if self.carry_start_date else None
        carry_end_date = DateTimeUtil.localize_date(
            self.carry_end_date, tz, date_format) if self.carry_end_date else None
        vendor_decs = set(self.all_vendor_decs().values_list('identifier', flat=True))
        user_company = user.company
        has_contract_allocation = user_company.contract_allocations
        has_order_booking = get(user_company, 'sitemanagementsettings.order_booking')
        allocated_tonnage = None
        unallocated_tonnage = None
        if has_contract_allocation:
            unallocated_tonnage = self.unallocated_tonnage_for_user(user)
            if unallocated_tonnage:
                unallocated_tonnage = round(unallocated_tonnage, 2)
            allocated_tonnage = self.allocated_tonnage_for_user(user)
            if allocated_tonnage:
                allocated_tonnage = round(allocated_tonnage, 2)

        from core.common.utils import format_csv_string as _format
        unit = user.unit
        display_unit = f' ({unit})' if country.display_unit_in_brackets_for_csv else ''
        tonnage_label = country.get_label('tonnage')

        audit_formatted = self.get_formatted_audit_for_csv(self.id, get_content_type_id(CONTRACT_MODEL),
                                                           country.get_format('datetime'), tz)
        grade_name = get(blended_grade_detail, 'grade_name') or _format(self.grade_name)
        price = get(blended_grade_detail, 'price') or self.price
        contract_tonnage = get(blended_grade_detail, 'quantity') or self.actual_tonnage
        confirmed_tonnage = self.confirmed_tonnage
        progress_tonnage = self.progress_tonnage
        unplanned_tonnage = self.unplanned_tonnage
        delivered_tonnage = self.total_delivered_tonnage
        outstanding_tonnage = self.outstanding_tonnage
        remaining_tonnage = self.remaining_tonnage
        contract_value = self.contract_value
        contract_current_value = self.contract_current_value
        contract_max_value = self.contract_max_value
        invoiced_tonnage = self.invoiced_tonnage
        order_planned_tonnage = self.direct_freight_orders_tonnage
        orders_delivered_tonnage = self.delivered_freight_orders_delivered_tonnage
        if blended_grade_detail:
            grade_id = get(blended_grade_detail, 'grade_id')
            grade_quantity = get(blended_grade_detail, 'quantity')
            grade_percentage = (grade_quantity / self.tonnage) * 100
            confirmed_tonnage = round((confirmed_tonnage * grade_percentage) / 100, 2)
            progress_tonnage = self.progress_tonnage_for_grade(grade_id)
            unplanned_tonnage = round((unplanned_tonnage * grade_percentage) / 100, 2)
            delivered_tonnage = self.delivered_tonnage_for_grade(grade_id)
            invoiced_tonnage = self.invoiced_tonnage_for_grade(grade_id)
            inferred_tonnage = round((self.inferred_tonnage * grade_percentage) / 100, 2)
            outstanding_tonnage = inferred_tonnage - delivered_tonnage
            remaining_tonnage = outstanding_tonnage - progress_tonnage
            contract_value = multiply_args(inferred_tonnage or 0, price or 0)
            contract_current_value = add_args(
                contract_value or 0, self.carry_current_for_tonnage(inferred_tonnage) or 0
            )
            contract_max_value = add_args(contract_value or 0, self.carry_max_for_tonnage(inferred_tonnage) or 0)
            order_planned_tonnage = round((order_planned_tonnage * grade_percentage) / 100, 2)
            if allocated_tonnage:
                allocated_tonnage = round((allocated_tonnage * grade_percentage) / 100, 2)
            if unallocated_tonnage:
                unallocated_tonnage = round((unallocated_tonnage * grade_percentage) / 100, 2)
            direct_freight_orders = self.direct_freight_orders().exclude(status__in=['void', 'rejected'])
            if direct_freight_orders.exists():
                from core.freights.models import FreightOrder
                movements = FreightOrder.get_movements_for(
                    direct_freight_orders.values_list('id', flat=True),
                    include={'status__in': frozenset(['delivered', 'completed'])}
                )
                orders_delivered_tonnage = self.delivered_tonnage_for_grade(
                    grade_id, movements.values_list('id', flat=True)
                )


        # pylint: disable=line-too-long
        columns_mapping = {
            "Broker Note No / Sales Confirmation No": self.identifier,
            "Contract No": self.contract_number,
            "Internal Reference Number": _format(self.get_internal_reference_number(user)),
            "Status": _format(self.status_display_name(user)),
            "Seller": _format(self.seller_company_name),
            "Seller Contact": _format(self.seller_contact_name),
            "Seller Address": _format(get(self, 'seller.company.address.address')),
            "Seller Phone": country.to_phone_format(get(self, 'seller.contact.mobile')),
            "Seller Email": get(self, 'seller.contact.email'),
            "Seller NGR": _format(self.seller_ngr_number),
            "Pickup Site": _format(self.consignors_names),
            "Pickup Storage": _format(self.pickup_sites_names),
            "Pickup Site Address": _format(self.pickup_site_address),
            "Buyer": _format(self.buyer_company_name),
            "Buyer Contact": _format(self.buyer_contact_name),
            "Buyer Address": _format(get(self, 'buyer.company.address.address')),
            "Buyer Phone": country.to_phone_format(get(self, 'buyer.contact.mobile')),
            "Buyer Email": get(self, 'buyer.contact.email'),
            "Buyer NGR": _format(self.buyer_ngr_number),
            "Delivery Site": _format(self.consignees_names),
            "Delivery Storage": _format(self.delivery_sites_names),
            "Delivery Site Address": _format(self.delivery_site_address),
            "Delivery Start Date": DateTimeUtil.localize_date(self.delivery_start_date, tz, date_format),
            "Delivery End Date": DateTimeUtil.localize_date(self.delivery_end_date, tz, date_format),
            "Conveyance": _format(get(self, 'conveyance.name')),
            "Delivery Onus": _format(self.delivery_onus),
            "Contract Type": _format(get(self, 'type.display_name')),
            "Commodity": _format(get(self, 'commodity.display_name')),
            "Variety": _format(get(self, 'variety.name')),
            "Grade": grade_name,
            "Spreads": _format(self.spread.humanize()) if self.spread_id else None,
            "Season": self.season,
            "Price ($)": price,
            "Price Point": _format(get(self, 'price_point.display_name')),
            "Track": _format(self.track),
            f"{tonnage_label}{display_unit}": round(self.commodity.convert_to(contract_tonnage, self.commodity.unit, unit), 5), # pylint: disable=line-too-long
            "Quantity ({})".format(BALES): self.actual_quantity if self.commodity.is_bales else 'NA',
            "Quantity ({})".format(MODULES): self.actual_quantity if self.commodity.is_modules else 'NA',
            "Quantity ({})".format(METER_CUBE): self.actual_quantity if self.commodity.is_meter_cube else 'NA',
            "Quantity ({})".format(KG): self.actual_quantity if self.commodity.is_kg else 'NA',
            "Quantity ({})".format(LITRE): self.actual_quantity if self.commodity.is_litre else 'NA',
            f"Allocated {tonnage_label}{display_unit}":
                round(self.commodity.convert_to(allocated_tonnage, self.commodity.unit, unit), 5) if allocated_tonnage else None,
            f"Unallocated {tonnage_label}{display_unit}":
                round(self.commodity.convert_to(unallocated_tonnage, self.commodity.unit, unit), 5) if unallocated_tonnage else None,
            f"Outstanding {tonnage_label}{display_unit}":
                round(self.commodity.convert_to(outstanding_tonnage, self.commodity.unit, unit), 5) if outstanding_tonnage else None,
            f"Booked {tonnage_label}{display_unit}":
                round(self.commodity.convert_to(confirmed_tonnage, self.commodity.unit, unit), 5)
                if (has_order_booking and confirmed_tonnage) else None,
            f"In Progress {tonnage_label}{display_unit}":
                round(self.commodity.convert_to(progress_tonnage, self.commodity.unit, unit), 5) if progress_tonnage else 0.0,
            f"Unplanned {tonnage_label}{display_unit}":
                round(self.commodity.convert_to(max([unplanned_tonnage, 0]), self.commodity.unit, unit), 5) if unplanned_tonnage else None,
            f"Delivered {tonnage_label}{display_unit}":
                round(self.commodity.convert_to(delivered_tonnage, self.commodity.unit, unit), 5) if delivered_tonnage else 0.0,
            f"Invoiced {tonnage_label}{display_unit}":
                round(self.commodity.convert_to(invoiced_tonnage, self.commodity.unit, unit), 5) if invoiced_tonnage else 0.0,
            f"Remaining {tonnage_label}{display_unit}":
                round(self.commodity.convert_to(remaining_tonnage, self.commodity.unit, unit), 5) if remaining_tonnage else None,
            "Tolerance": _format(get(self, 'tolerance.name')),
            "Packaging": _format(get(self, 'packaging.name')),
            "Inspection": _format(get(self, 'inspection.name')),
            "Weight": _format(get(self, 'weight.name')),
            "Market Zone": _format(get(self, 'market_zone.name')),
            "Region": _format(get(self, 'region.name')),
            "Payment Scale": _format(get(self, 'payment_scale.name')),
            "Payment Terms": _format(get(self, 'payment_term.name')),
            "Estimated Payment Due Date": estimated_payment_due_date,
            "Carry Rate": self.carry_rate,
            "Carry Frequency": self.carry_frequency,
            "Carry Start Date": carry_start_date,
            "Carry End Date": carry_end_date,
            "Current Carry Value ($)": self.carry_current,
            "Max Carry Value ($)": self.carry_max,
            "Contract Value ($)": contract_value,
            "Current Contract Value ($)": contract_current_value,
            "Max Contract Value ($)": contract_max_value,
            "EPR Value ($)": self.epr_value,
            "Central Levy ($)": self.levy,
            "Brokered By": _format(get(self, 'administration.brokered_by.name')),
            "Broker Contact": _format(get(self.broker_contact, 'name')),
            "Brokerage Payable By": _format(brokerages_info.get('payble_by', None)),
            "Seller Brokerage Charged At": brokerages_info.get('seller_brokerage_charged_on', None),
            "Seller Brokerage Fee Type": brokerages_info.get('seller_brokerage_fee_type', None),
            "Seller Brokerage Rate": brokerages_info.get('seller_brokerage_rate', None),
            "Seller Brokerage Total Fee ($)": brokerages_info.get('seller_brokerage_total_fee', None),
            "Buyer Brokerage Charged At": brokerages_info.get('buyer_brokerage_charged_on', None),
            "Buyer Brokerage Fee Type": brokerages_info.get('buyer_brokerage_fee_type', None),
            "Buyer Brokerage Rate": brokerages_info.get('buyer_brokerage_rate', None),
            "Buyer Brokerage Total Fee ($)": brokerages_info.get('buyer_brokerage_total_fee', None),
            "Invoicing": _format(get(self, 'administration.invoicing')),
            "General Conditions": _format(self.general_conditions),
            "Special Conditions": _format(self.special_conditions),
            "Contract Date": DateTimeUtil.localize_date(self.contract_date, tz, date_format),
            "Created On": DateTimeUtil.localize_date(self.created_at, tz, date_format),
            "Created By Company": _format(self.created_by.company.name),
            "Created By Contact": _format(self.created_by.name),
            "Contract Delivered{}".format(display_unit):
                round(self.commodity.convert_to(delivered_tonnage, self.commodity.unit, unit), 5) if delivered_tonnage else None,
            "Contract Outstanding{}".format(display_unit):
                round(self.commodity.convert_to(outstanding_tonnage, self.commodity.unit, unit), 5) if outstanding_tonnage else None,
            "Order Planned{}".format(display_unit):
                round(self.commodity.convert_to(order_planned_tonnage, self.commodity.unit, unit), 5) if order_planned_tonnage else 0.0,
            "Order Delivered{}".format(display_unit):
                round(self.commodity.convert_to(
                    orders_delivered_tonnage, self.commodity.unit, unit), 5) if orders_delivered_tonnage else 0.0,
            "Last modified date": DateTimeUtil.localize_date(self.updated_at, tz, date_format),
            "Commodity Vendor Dec": 'Yes' if len(vendor_decs) else 'No',
            "Commodity Vendor Dec No.": ' '.join(vendor_decs),
            "Sustainable Commodity Contract": 'Yes' if self.sustainable_commodity else 'No',
            "Currency": self.currency,
            f"{tonnage_label} Unit": unit,
            "Lot No.": self.lot_number,
            "Audit Log (DD/MM/YYYY hh:mm A - User Name (Action))": audit_formatted
        }
        if custom_csv_headers:
            return [columns_mapping.get(column) for column in custom_csv_headers]
        else:
            return list(columns_mapping.values())

    @staticmethod
    def immediate_parent():
        return None

    def update_asset(self, asset_id, field, user):
        fields = [
            'seller_company', 'seller_contact', 'seller_ngr',
            'buyer_company', 'buyer_contact', 'buyer_ngr',
            'consignor_handler', 'pickup_storage', 'pickup_field',
            'consignee_handler', 'delivery_storage', 'delivery_field',
        ]
        can_update = self.can_update_asset(user, fields, field)

        if not can_update:
            return

        func = get(self, '_update_' + field)
        if func:
            func(asset_id)

    def _update_seller_company(self, company_id):
        self._update_party_asset(self.seller, 'company_id', company_id)
        self.set_viewer_company_ids()
        for fo in self.freight_orders.iterator():
            fo.set_viewer_company_ids()
        for fm in self.freight_contracts.iterator():
            fm.set_viewer_company_ids()
        for tt in self.titletransfer_set.iterator():
            tt.set_viewer_company_ids()

    def _update_seller_contact(self, contact_id):
        self._update_party_asset(self.seller, 'contact_id', contact_id)

    def _update_seller_ngr(self, ngr_id):
        self._update_party_asset(self.seller, 'ngr_id', ngr_id)

    def _update_buyer_company(self, company_id):
        self._update_party_asset(self.buyer, 'company_id', company_id)
        self.set_viewer_company_ids()
        for fo in self.freight_orders.iterator():
            fo.set_viewer_company_ids()
        for fm in self.freight_contracts.iterator():
            fm.set_viewer_company_ids()
        for tt in self.titletransfer_set.iterator():
            tt.set_viewer_company_ids()

    def _update_buyer_contact(self, contact_id):
        self._update_party_asset(self.buyer, 'contact_id', contact_id)

    def _update_buyer_ngr(self, ngr_id):
        self._update_party_asset(self.buyer, 'ngr_id', ngr_id)

    def _update_pickup_storage(self, location_id):
        self._update_pickup_location(Storage, location_id)

    def _update_pickup_field(self, location_id):
        from core.farm_fields.models import FarmField
        self._update_pickup_location(FarmField, location_id)

    def _update_delivery_storage(self, location_id):
        self._update_delivery_location(Storage, location_id)

    def _update_delivery_field(self, location_id):
        from core.farm_fields.models import FarmField
        self._update_delivery_location(FarmField, location_id)

    @staticmethod
    def _update_party_asset(party, field, asset_id):
        setattr(party, field, asset_id)
        party.save()

    def create_vendor_dec_request(self, user, communication_params, vendor_dec_type='grain_vd'):
        request_reason = 'create vendor dec'
        acceptance_required = communication_params.pop('acceptance_required')
        recipients = communication_params.get('recipients', {})
        parties_company_ids = communication_params.pop('parties_company_ids', {})
        receivers = []
        if parties_company_ids:
            receivers = list(parties_company_ids.values())
        if recipients:
            for key in recipients.keys():
                if key == 'seller':
                    receivers.append(self.seller.company_id)
                if key == 'broker':
                    receivers.append(self.seller.represented_by.company_id)
        request = ContractAcceptanceRequest.create(
            {'type': vendor_dec_type,
             'contract_id': self.id,
             'created_by_id': user.id,
             'updated_by_id': user.id,
             'args': {},
             'receivers': compact(flatten(receivers)),
             'resolved': False,
             'accepted': False,
             'request_reason': request_reason,
             'acceptance_required': acceptance_required})

        if communication_params:
            AcceptanceRequestCommunication.create(
                communication_params,
                kwargs={'request_id': request.id, 'created_by_id': user.id,
                        'updated_by_id': user.id, })

    def get_levy_total(self, levy, item, levy_item=None, include_levy_adjustments=True): # pylint: disable=too-many-locals
        from ..services.external.google import GoogleMaps
        is_delivery_site_bhc = False
        levy_value = float(levy['value'] or 0)
        is_blended_load_item = get(item, 'is_blended')
        if item['item_type'] == FREIGHT_MOVEMENT_MODEL or is_blended_load_item:
            is_delivery_site_bhc = get(item['item'].inload, 'is_bhc_site', False)
        price_point_id = get(self, 'price_point_id')
        item_tonnage = float(item['tonnage'] or 0)
        item_sub_total = float(item['sub_total'] or 0)
        # pylint: disable=line-too-long
        if levy['multiplier'] == 'tonnage':
            value = round(float(levy_value * item_tonnage), 2)
            return max(value, 0.0), value, None, 0

        if (
                price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS and
                ((item['item_type'] in [TITLE_TRANSFER_MODEL, LOAD_MODEL] and not is_blended_load_item) or
                 is_delivery_site_bhc)
        ) or (
                price_point_id in [PRICE_POINTS_TRACK_ID, PRICE_POINTS_DELIVERED_MARKET_ZONE_ID,
                                   PRICE_POINTS_FREE_IN_STORE_ID]
        ):
            standard_value = round(levy_value * item_sub_total, 2)
            adjusted_value = round(float(levy_value * DISTANCE_FOR_LEVY * item_tonnage), 2)
            if include_levy_adjustments:
                return max(round(standard_value - adjusted_value, 2), 0.0), standard_value, None, adjusted_value
            return round(standard_value, 2), standard_value, None, adjusted_value

        if ((item['item_type'] not in [TITLE_TRANSFER_MODEL, LOAD_MODEL] or is_blended_load_item) and
                not is_delivery_site_bhc and price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS):
            movement = item['item']
            total_distance = movement.estimated_distance or 0
            if not total_distance:
                outload = movement.outload
                inload = movement.inload
                if outload and inload:
                    params = {
                        'origin_latitude': get(outload, 'site_address.latitude'),
                        'origin_longitude': get(outload, 'site_address.longitude'),
                        'destination_latitude': get(inload, 'site_address.latitude'),
                        'destination_longitude': get(inload, 'site_address.longitude'),
                    }
                    departure_time = outload.date_time
                    if departure_time:
                        params['departure_time'] = departure_time
                    response = GoogleMaps.get_distance(params, get(movement, 'country_code'))
                    if response and response.get('status') == 'OK':
                        total_distance = round(
                            float(response['distance']['value']/1000 if response['distance'] else 0), 2)
            standard_value = round(levy_value * item_sub_total, 2)
            from core.contracts.constants import FREIGHT_DELIVERY_CHARGES
            standard_delivery_rate = (get(levy_item, 'adjustments.standard_delivery_rate')
                                      or get(item, 'standard_delivery_rate', FREIGHT_DELIVERY_CHARGES))
            adjusted_value = round(
                float(levy_value * standard_delivery_rate * (float(total_distance) * item_tonnage)), 2
            )
            if include_levy_adjustments:
                return max(round(standard_value - adjusted_value, 2), 0.0), standard_value, total_distance, adjusted_value
            return round(standard_value, 2), standard_value, total_distance, adjusted_value

        if price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS:
            value = round(float(levy_value * item_sub_total), 2)
            return max(value, 0.0), value, None, 0

    def get_grain_levies(self, item, only_total=False, get_all_fields=False, levy_item=None, state=None, user=None, tz=None):  # pylint: disable=too-many-locals,too-many-branches, too-many-statements, line-too-long
        # assume item['item'] is either FM or TT
        from core.freights.constants import GRAIN_LEVY_DESCRIPTION
        total = 0
        display_rate = 0
        adjusted_price = 0
        total_distance = None
        standard_value = 0
        levy_type = get(item, 'levy_type')
        commodity = self.commodity
        levy = {}
        if commodity:
            levy = commodity.levy(levy_type)
        is_blended_load_item = get(item, 'is_blended')
        if levy_type in STATE_BASED_LEVIES:
            levy = get(levy, state)
        levy_val = get(levy, 'value')
        include_levy_adjustments = get(user, 'company.include_levy_adjustments') if user else True
        if levy_val and levy_val != 0:
            levy_total, standard_value, total_distance, adjusted_price = self.get_levy_total(
                levy, item, levy_item, include_levy_adjustments
            )
            if levy['multiplier'] == 'tonnage':
                total = -1 * levy_total
                display_rate = f"{self.currency} {str(levy['value'])} / MT"
            else:
                total = -1 * levy_total
                display_rate = str(round(levy['value'] * 100, 5)) + '%'
        if only_total:
            return total
        if is_blended_load_item:
            identifier = get(item, 'identifier')
            tt_identifier = ''
        elif item['item_type'] == LOAD_MODEL:
            related_load = get(item['item'], 'related_canola_inload')
            _, identifier = related_load.get_load_type_with_identifier()
            tt_identifier = '(' + get(item['item'], 'title_transfer.identifier', '') + ')'
        else:
            identifier = item['item'].identifier
            tt_identifier = ''
        description = GRAIN_LEVY_DESCRIPTION % (
            to_display_attr(GRAIN_LEVY_TYPES, levy_type).format(commodity_name=get(self, 'commodity.display_name')),
            get(self, 'commodity.display_name'), self.currency,
            str(round(float(item['sub_total'] or 0), 2)), identifier, tt_identifier
        )
        is_delivery_site_bhc = False
        if item['item_type'] == FREIGHT_MOVEMENT_MODEL or is_blended_load_item:
            is_delivery_site_bhc = get(item['item'].inload, 'is_bhc_site', False)
        result = {}
        reference_timezone = (tz or get(item, 'item.freight_delivery.consignee.handler._timezone.location') or
                              get(item, 'item.storage.farm._timezone.location'))
        item_date = (
            DateTimeUtil.localize(get(item, 'item.date_time'), reference_timezone) or date.today()
        ).strftime(Country.get_country_format('date'))
        if not item_date:
            item_date = (
                DateTimeUtil.localize(get(levy_item, 'item.date'), reference_timezone) or date.today()
            ).strftime(Country.get_country_format('date'))
        if get_all_fields:
            result.update({
                'is_bhc': is_delivery_site_bhc,
                'total_distance': total_distance,
                'contract_value': item['sub_total'],
                'price_point_id': self.price_point_id,
                'item_for_type': item['item_type'],
                'adjusted_price': adjusted_price if include_levy_adjustments else 0,
                'adjusted_price_value': adjusted_price,
                'multiplier': get(levy, 'multiplier')
            })
        result.update({
            'description': get(levy_item, 'item.description') or description.strip(),
            'tonnage': get(levy_item, 'tonnage') or item['tonnage'],
            'rate': get(levy_item, 'item.rate') or display_rate,
            'sub_total': get(levy_item, 'total') or total,
            'gst': 0,
            'total': get(levy_item, 'total') or total,
            'date': item_date,
            'item_type': GRAIN_LEVY_ITEM_MODEL,
            'levy_adjustment_included': include_levy_adjustments,
            'is_custom_adjustment': get(levy_item, 'adjustments.is_custom_adjustment'),
            'actual_price': get(levy_item, 'adjustments.actual_price'),
            'standard_value': standard_value
        })
        if is_blended_load_item:
            result.update({'load_id': item['item_id']})
        elif item['item_type'] == TITLE_TRANSFER_MODEL:
            result.update({'title_transfer_id': item['item_id']})
        elif item['item_type'] == LOAD_MODEL:
            result.update({'load_id': item['item_id']})
            result.update({'title_transfer_id': get(item['item'], 'title_transfer_id')})
        else:
            from core.contracts.constants import FREIGHT_DELIVERY_CHARGES
            standard_delivery_rate = (get(levy_item, 'adjustments.standard_delivery_rate') or
                                      get(item, 'standard_delivery_rate', FREIGHT_DELIVERY_CHARGES))
            result.update({'freight_movement_id': item['item_id'],
                           'standard_delivery_rate': standard_delivery_rate})
        return result

    def get_eprs(self, item, user=None, only_total=False, epr_item=None, tz=None):  # pylint: disable=too-many-locals
        from core.freights.constants import EPR_DESCRIPTION
        variety = get(self, 'variety')
        is_blended_load_item = get(item, 'is_blended')
        identifier = ''
        if item['item_type'] == FREIGHT_MOVEMENT_MODEL:
            movement = item.get('item')
            variety = get(movement.invoice_details(user), 'variety')
            identifier = get(movement, 'identifier')
        if item['item_type'] == TITLE_TRANSFER_MODEL:
            tt = item['item']
            variety = get(tt, 'variety')
            identifier = get(tt, 'identifier')
        if item['item_type'] == LOAD_MODEL and not is_blended_load_item:
            load = item['item']
            variety = get(load, 'variety')
            related_load = load.related_canola_inload
            _, identifier = related_load.get_load_type_with_identifier()
            identifier += f" ({get(load, 'title_transfer.identifier', '')})"
        description = EPR_DESCRIPTION % (identifier, get(self, 'commodity.display_name'),
                                         get(variety, 'name', 'N/A') or 'N/A')
        if is_blended_load_item:
            from core.loads.models import Load
            load = Load.objects.filter(id=item['item_id']).first()
            variety = get(load, 'variety')
            identifier = get(item, 'identifier')
            description = (f"End Point Royalty (EPR) ({identifier} | {get(self, 'commodity.display_name')} |"
                           f" {get(load, 'grade.name')} | {get(variety, 'name', 'N/A') or 'N/A'})")
        rate = get(variety, 'epr.rate', 0)

        subtotal = round(float(rate) * float(item['tonnage']), 2)
        gst_rate = self.country.gst_rate
        total = (-1 * + round((subtotal * float(1 + gst_rate)), 2)) if gst_rate else (-1 * + round(subtotal, 2))
        if only_total:
            return total
        reference_timezone = (tz or get(item, 'item.freight_delivery.consignee.handler._timezone.location') or
                              get(item, 'item.storage.farm._timezone.location'))
        item_date = (
            DateTimeUtil.localize(get(item, 'item.date_time'), reference_timezone) or date.today()
        ).strftime(Country.get_country_format('date'))
        if not item_date:
            item_date = (
                DateTimeUtil.localize(get(epr_item, 'item.date'), reference_timezone) or date.today()
            ).strftime(Country.get_country_format('date'))
        sub_total = get(epr_item, 'sub_total') or -1 * subtotal
        epr = {
            'description': get(epr_item, 'item.description') or description,
            'tonnage': get(epr_item, 'tonnage') or item['tonnage'],
            'rate': get(epr_item, 'item.rate') or to_currency(round(rate, 2), True, self.currency),
            'sub_total': round(sub_total, 2),
            'item_type': EPR_ITEM_MODEL,
            'gst': get(epr_item, 'gst') or -1 * + round((subtotal * gst_rate), 2),
            'total': get(epr_item, 'total') or total,
            'date': item_date,
        }
        if is_blended_load_item:
            epr['load_id'] = item['item_id']
        elif item['item_type'] == TITLE_TRANSFER_MODEL:
            epr['title_transfer_id'] = item['item_id']
        elif item['item_type'] == LOAD_MODEL:
            epr['load_id'] = item['item_id']
            epr['title_transfer_id'] = get(item['item'], 'title_transfer_id')
        else:
            epr['freight_movement_id'] = item['item_id']

        return epr

    def get_ld_from_contract(self, consignee_handler_id, consignor_handler_id=0):
        consignees = self.consignees_with_sites
        consignors = self.consignors_with_sites
        consignee_handler = list(filter(lambda obj: obj['handler_id'] == consignee_handler_id, consignees))
        consignor_handler = list(filter(lambda obj: obj['handler_id'] == consignor_handler_id, consignors))
        consignee_ld = get(consignee_handler, '0.ld', None)
        consignor_ld = get(consignor_handler, '0.ld', None)
        if not consignee_ld:
            return consignor_ld
        if not consignor_ld:
            return consignee_ld
        return consignee_ld + consignor_ld

    def get_default_ld(self, freight, item_type):
        ld = None
        if PRICE_POINTS_TRACK_ID == get(self, 'price_point_id'):
            if item_type == TITLE_TRANSFER_MODEL:
                tracks = get(freight, 'storage.farm.tracks')
                if tracks:
                    ld = get(tracks, self.track)
                if not ld:
                    ld = get(freight, 'storage.farm.ld')
            if item_type == FREIGHT_MOVEMENT_MODEL:
                handler = get(freight.inload, 'handler')
                tracks = get(handler, 'tracks')
                if tracks:
                    ld = get(tracks, self.track)
                if not ld:
                    ld = get(handler, 'ld')
        if ld:
            ld = -1 * ld
        return ld

    def get_final_contract_price(self, farm_id, item, item_type, load_grade_id):
        if item_type == FREIGHT_MOVEMENT_MODEL and item:
            inload = item.inload
            outload = item.outload
            consignee_id = get(inload, 'farm_id', 0)
            consignor_id = get(outload, 'farm_id', 0)
        else:
            consignor_id = 0
            consignee_id = farm_id
        location_differential = self.get_ld_from_contract(consignee_id, consignor_id)
        if not location_differential:
            location_differential = self.get_default_ld(item, item_type) or 0
        rate = self.get_price_for_others(load_grade_id, location_differential)
        if self.is_canola:
            rate = self.get_price_for_canola(
                rate, float(get(self, 'specs.impu') or 0), float(get(self, 'specs.coil') or 0)
            )
        return rate

    def get_spread_grade_price(self, grade_id):
        price_diff = 0
        if self.spread:
            grade = list(filter(lambda obj: obj.get('id') == grade_id, self.spread.details))
            if grade:
                grade = grade[0]
                grade_value = float(grade['value']) if grade['value'] else 0
                price_diff = grade_value if grade['price_variation'] == '+' else -1 * grade_value
        return price_diff or 0

    def get_price_for_others(self, grade_id, location_differential):
        price_diff = self.get_spread_grade_price(grade_id)
        if location_differential:
            price_diff += location_differential
        return round(float(self.price or 0) + float(price_diff or 0), 2)

    def calculate_price(self, rate, impu, coil, admix_base, oil_min_base):
        impu = float(impu or 0)
        coil = float(coil or 0)
        admix_adjustment = float(self.get_admixture_adjustment(rate, impu, admix_base))
        oil_adjustment = self.get_oil_adjustment(rate, impu, coil, admix_base, oil_min_base)
        return round(float(rate + admix_adjustment + oil_adjustment), 2)

    def get_weighted_average(self, loads, load_ids, property_name):
        percentage = 0
        total_weight = 0
        for load in loads:
            tonnage = get(load_ids, str(load.id), 0)
            property_val = get(load, 'specs.{}'.format(property_name), 0)
            percentage += (tonnage * property_val)
            total_weight += tonnage
        return round(float(percentage / total_weight), 2)

    def get_admixture_adjustment(self, base_price, impu, admix_base=DEFAULT_ADMIX_BASE, rounded=True):
        if admix_base != 0 and float(impu or 0) <= admix_base:
            return 0
        admix_adjustment = base_price * (admix_base - float(impu or 0)) * 0.01
        return round(admix_adjustment, 2) if rounded else admix_adjustment

    def get_oil_adjustment(
        self, base_price, impu, coil, admix_base=DEFAULT_ADMIX_BASE, oil_min_base=DEFAULT_OIL_MIN_BASE
    ):
        post_admix_adjusted_price = self.get_admixture_adjustment(base_price, impu, admix_base, False)
        oil_adjustment = float(
            (float(post_admix_adjusted_price) + base_price) *
            (float(coil or 0) - oil_min_base) * PRICE_ADJUSTMENT_FACTOR
        )
        return round(oil_adjustment, 2)

    def get_price_for_canola(self, rate, impu, coil):
        impu = float(impu or 0)
        coil = float(coil or 0)
        payment_scale = self.payment_scale_id
        config = PAYMENT_SCALE_CONFIG.get(payment_scale, PAYMENT_SCALE_CONFIG[PAYMENT_SCALE_FLAT])

        if (impu is not None and coil is not None and payment_scale != PAYMENT_SCALE_FLAT):
            oil_content_for_pricing = coil
            if config['oil_cap'] and coil >= config['oil_cap']:
                oil_content_for_pricing = config['oil_cap']

            return self.calculate_price(
                rate, impu, oil_content_for_pricing, config['admix_base'], config['oil_min_base']
            )
        return rate

    def get_canola_loads(self, title_transfers_items):
        canola_loads = []
        for title_transfers_item in title_transfers_items:
            item = get(title_transfers_item, 'item')
            location_differential = self.get_ld_from_contract(get(item, 'storage.farm_id'))
            if not location_differential:
                location_differential = self.get_default_ld(item, TITLE_TRANSFER_MODEL) or 0
            rate = self.get_price_for_others(item.grade_id, location_differential)
            if self.is_canola:
                if item.canola_loads:
                    for load in item.canola_loads:
                        load_specs = load.specs
                        tonnage_from_load = get(item.canola_load_ids, str(load.id))
                        canola_load_price_per_mt = self.get_price_for_canola(
                            rate, get(load_specs, 'impu'), get(load_specs, 'coil')
                        )
                        load_subtotal = +round(
                            (tonnage_from_load * round(canola_load_price_per_mt, 2)), 2
                        )
                        load_invoice_details = {
                            'id': item.id,
                            'titleTransfer': item.identifier,
                            'commodity': 'Canola',
                            'grade': get(load, 'grade_name'),
                            'season': get(load, 'season'),
                            'load_type': get(load.get_load_type_with_identifier(), '0', ''),
                            'load_identifier': get(load.get_load_type_with_identifier(), '1', ''),
                            'coil': str(get(load.specs, 'coil')) + '%',
                            'impu': str(get(load.specs, 'impu')) + '%',
                            'tonnage': str(tonnage_from_load) + 'MT',
                            'subtotal': f'{self.currency} {str(load_subtotal)}',
                        }
                        canola_loads.append(load_invoice_details)

        return canola_loads

    def create_draft_invoices_for_contract(self):
        from core.invoices.models import Invoice
        items = []
        items.extend([fc for fc in self.freight_contracts.filter(
            status=COMPLETED_STATUS
        ) if not fc.is_commodity_contract_invoiced_for_contract(self.id)])
        items.extend([tt for tt in self.titletransfer_set.filter(
            status=STATUS_COMPLETED
        ) if not tt.is_commodity_contract_invoiced])
        for item in items:
            item_type = TITLE_TRANSFER_MODEL if isinstance(item, TitleTransfer) else FREIGHT_MOVEMENT_MODEL
            Invoice.create_or_update_draft_invoice(item, item_type, self.created_by_id)

    def get_levy_data(self, data, user, get_all_fields, state=None, tz=None):
        result = self.get_grain_levies(data, get_all_fields=get_all_fields, state=state, user=user, tz=tz)
        result.update({'invoice': True, 'gst': 'N/A'})
        result['invoice'] = result['total'] != 0 and get(user, 'company.default_levy_epr')
        return result

    def get_epr_data(self, data, user, tz=None):
        result = self.get_eprs(data, user, tz=tz)
        result.update({'invoice': True})
        result['invoice'] = result['total'] != 0 and get(user, 'company.default_levy_epr')
        return result

    def get_levy_or_epr_data(self, data, user, item_type, get_all_fields=False, tz=None):  # pylint: disable=too-many-locals,too-many-branches, too-many-statements
        item = None
        result = {}
        item_id = get(data, 'item_id')
        data_item_type = get(data, 'item_type')
        is_blended_load = get(data, 'is_blended')
        from core.invoices.models import InvoiceItem
        invoice_item = InvoiceItem.objects.filter(
            item_id=item_id, item_type__model=data_item_type, invoice__raised_for_id=self.id,
            invoice__type=COMMODITY_CONTRACT_INVOICE, is_rejected=False
        ).exclude(invoice__status='draft').first()
        pickup_site = None
        delivery_site = None
        if data_item_type == FREIGHT_MOVEMENT_MODEL or is_blended_load:
            from core.freights.models import FreightContract
            movement_id = get(data, 'freight_movement_id') if is_blended_load else item_id
            item = FreightContract.objects.filter(id=movement_id).first()
            if get(item, 'status') not in [COMPLETED_STATUS, MANUAL_CONTRACT_COMPLETE_BALANCED]:
                return []
            pickup_site = get(item, 'outload.farm')
            delivery_site = get(item, 'inload.farm')
            result['freight_movement_identifier'] = get(item, 'identifier')
        elif data_item_type == TITLE_TRANSFER_MODEL:
            item = TitleTransfer.objects.filter(id=item_id).first()
            if get(item, 'status') not in [COMPLETED_STATUS, INVOICED]:
                return []
            pickup_site = get(item, 'storage.farm')
            result['title_transfer_identifier'] = get(item, 'identifier')
        elif data_item_type == LOAD_MODEL:
            from core.loads.models import Load
            item = Load.objects.filter(id=item_id).first()
            pickup_site = get(item, 'storage.farm')
            related_load = item.related_canola_inload
            _, identifier = related_load.get_load_type_with_identifier()
            result['load_identifier'] = identifier
        if item:
            data['item'] = item
        if item_type == GRAIN_LEVY_ITEM_MODEL:
            grain_levies = []
            commodity = self.commodity
            pickup_state = get(pickup_site, 'address.state.short_code')
            delivery_state = get(delivery_site, 'address.state.short_code')
            if delivery_site and not delivery_state:
                delivery_site.update_state()
                delivery_site.refresh_from_db()
                delivery_state = get(delivery_site, 'address.state.short_code')
            if pickup_site and not pickup_state:
                pickup_site.update_state()
                pickup_site.refresh_from_db()
                pickup_state = get(pickup_site, 'address.state.short_code')
            state = get(pickup_site, 'address.state.short_code')
            country_levies = get(commodity, 'country.config.levy_types')
            levies = []
            if country_levies:
                levies = (get(country_levies, pickup_state) or get(country_levies, delivery_state) or
                          get(country_levies, 'default'))
            for levy_type in levies:
                grain_levy = {**result, 'levy_type': levy_type}
                data.update({'levy_type': levy_type})
                if commodity:
                    levy = commodity.levy(levy_type)
                    if levy_type in STATE_BASED_LEVIES:
                        levy = get(levy, state)
                        if not levy:
                            continue
                if invoice_item:
                    levy_item = InvoiceItem.get_item_from_items(
                        invoice_item, get(invoice_item, 'invoice.grain_levies'), levy_type
                    )
                    if levy_item:
                        is_custom_adjustment = get(levy_item, 'adjustments.is_custom_adjustment')
                        grain_levy.update(self.get_grain_levies(
                            data, get_all_fields=get_all_fields, levy_item=levy_item if is_custom_adjustment else None,
                            state=state, user=user, tz=tz
                        ))
                        grain_levy.update({'invoice': True, 'gst': 'N/A'})
                    else:
                        grain_levy.update(self.get_levy_data(data, user, get_all_fields, state=state, tz=tz))
                        grain_levy.update({'invoice': False, 'gst': 'N/A'})
                else:
                    grain_levy.update(self.get_levy_data(data, user, get_all_fields, state=state, tz=tz))
                grain_levies.append({**grain_levy})
            return grain_levies
        elif item_type == EPR_ITEM_MODEL:
            if invoice_item:
                epr_item = InvoiceItem.get_item_from_items(invoice_item, get(invoice_item, 'invoice.eprs'))
                if epr_item:
                    result.update(self.get_eprs(data, user, epr_item=epr_item, tz=tz))
                    result.update({'invoice': True})
                else:
                    result.update(self.get_epr_data(data, user, tz=tz))
                    result.update({'invoice': False})
            else:
                result.update(self.get_epr_data(data, user, tz=tz))
        return result

    @property
    def is_price_point_track(self):
        return self.price_point_id == PRICE_POINTS_TRACK_ID

    @staticmethod
    def apply_receival_fees_on_price(price_per_mt, receival_fees):
        return round(float(price_per_mt) + float(receival_fees), 2)

    @staticmethod
    def apply_shrinkage_on_price(price_per_mt, shrinkage):
        return round(float(price_per_mt)/(1-(float(shrinkage)/100)), 2)

    def apply_shrinkage_and_receival_on_price(self, price_per_mt, receival_fees, shrinkage):
        if receival_fees:
            price_per_mt = self.apply_receival_fees_on_price(price_per_mt, receival_fees)
        if shrinkage:
            price_per_mt = self.apply_shrinkage_on_price(price_per_mt, shrinkage)
        return price_per_mt

    def get_adjustment_details(
            self, item_type, grade_id, location_differential, price_per_mt, shrinkage, receival_fees, is_canola,
            is_load_by_load, impu, coil, price_distribution=None
    ): # pylint: disable=too-many-locals
        adjustment_details = {
            'contract_price': get(price_distribution, 'contract') or self.price,
            'spread_price': get(price_distribution, 'spread') or self.get_spread_grade_price(grade_id),
            'location_differential': get(price_distribution, 'ld') or location_differential,
            'total_price_per_mt': get(price_distribution, 'total') or price_per_mt
        }
        if get(price_distribution, 'additional_cost'):
            adjustment_details['additional_cost'] = get(price_distribution, 'additional_cost')
        if self.is_price_point_track:
            adjustment_details['shrinkage'] = shrinkage or 0
            adjustment_details['receival_fees'] = receival_fees or 0
        if is_canola:
            is_flat = self.payment_scale_id == PAYMENT_SCALE_FLAT
            if is_flat:
                adjustment_details['admixture_adjustment'] = 0.0
                adjustment_details['oil_adjustment'] = 0.0
            else:
                config = PAYMENT_SCALE_CONFIG.get(
                    self.payment_scale_id, PAYMENT_SCALE_CONFIG[PAYMENT_SCALE_FLAT]
                )
                oil_content_for_pricing = coil
                if (self.payment_scale_id != PAYMENT_SCALE_FLAT and
                    config['oil_cap'] and coil >= config['oil_cap']):
                    oil_content_for_pricing = config['oil_cap']

                adjustment_details['admixture_adjustment'] = round(
                    float(self.get_admixture_adjustment(price_per_mt, impu, config['admix_base'])), 2
                )
                adjustment_details['oil_adjustment'] = self.get_oil_adjustment(
                    price_per_mt, impu, oil_content_for_pricing, config['admix_base'], config['oil_min_base']
                )
                adjustment_details['total_price_per_mt'] += round(float(adjustment_details['admixture_adjustment']) +
                                                             float(adjustment_details['oil_adjustment']), 2)
                if is_load_by_load:
                    adjustment_details['is_load_by_load'] = True

        adjustment_details['item_type'] = item_type
        return adjustment_details

    def upsert_stock_allocations(self, stock_allocations):
        for stock_allocation in stock_allocations:
            tonnage = stock_allocation.pop('tonnage', None)
            contract_stock_allocation = StockAllocation.objects.filter(
                contract_id=self.id, commodity_id=get(stock_allocation, 'commodity_id'),
                grade_id=get(stock_allocation, 'grade_id'), ngr_id=get(stock_allocation, 'ngr_id'),
                site_id=get(stock_allocation, 'site_id'), season=get(stock_allocation, 'season')
            ).first() or StockAllocation(contract_id=self.id, **stock_allocation, tonnage=0)
            contract_stock_allocation.tonnage += tonnage or 0
            contract_stock_allocation.save()

    def _get_sale_and_purchase_contract(self, contract, user):
        if self.seller.company_id == user.company_id:
            return self, contract
        elif self.buyer.company_id == user.company_id:
            return contract, self

    def get_through_warehouse_allocation_tonnage_for_contract(self, contract, user):
        sale_contract, purchase_contract = self._get_sale_and_purchase_contract(contract, user)
        return get(
            ThroughWarehouseAllocation.objects.filter(
                sale_id=sale_contract.id, purchase_id=purchase_contract.id
            ).first(),
            'tonnage'
        )

    def get_through_warehouse_allocation_delivered_tonnage_for_contract(self, contract, user):
        sale_contract, purchase_contract = self._get_sale_and_purchase_contract(contract, user)
        purchase_contract_orders = purchase_contract.freight_orders.exclude(
            status__in=['rejected', 'void']).filter(parent_order__isnull=True)
        if sale_contract.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS:
            consignor_handler_ids = sale_contract.consignors.values_list('handler_id', flat=True)
            purchase_contract_orders.filter(Q(freight_delivery__consignee__handler_id__in=consignor_handler_ids) |
                                            Q(freight_delivery__consignee__handler_id__isnull=True))
        from core.freights.models import FreightOrder
        return FreightOrder.delivered_movements_tonnage(list(purchase_contract_orders.values_list('id', flat=True)))

    def get_through_warehouse_allocation_planned_tonnage_for_contract(self, contract, user):
        sale_contract, purchase_contract = self._get_sale_and_purchase_contract(contract, user)
        purchase_contract_orders = purchase_contract.freight_orders.exclude(
            status__in=['rejected', 'void']).filter(parent_order__isnull=True)
        if sale_contract.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS:
            consignor_handler_ids = sale_contract.consignors.values_list('handler_id', flat=True)
            purchase_contract_orders.filter(Q(freight_delivery__consignee__handler_id__in=consignor_handler_ids) |
                                            Q(freight_delivery__consignee__handler_id__isnull=True))
        return float('%0.2f' % (purchase_contract_orders.aggregate(tonnage=Sum('planned_tonnage'))['tonnage'] or 0))

    def update_tonnages_and_status(self):
        self.set_unaccounted_tonnage()
        self.set_delivered_tonnage()
        self.refresh_from_db()

        if can_proceed(self.open):
            self.open()
            self.save()

        if can_proceed(self.in_progress):
            self.in_progress()
            self.save()

        if can_proceed(self.delivered):
            self.delivered()
            self.save()

        if can_proceed(self.completed):
            self.completed()
            self.save()

    @classmethod
    def all_contracts_by_country_id(cls, country_id, is_staff, search_str):
        queryset = cls.objects.filter(commodity__country_id=country_id)
        return cls._search(queryset, ['identifier', 'contract_number'], is_staff, search_str)

    @property
    def total_tonnage_with_tolerance(self):
        return self.expected_tonnage_range()[1]

    def can_create_direct_to_buyer_allocation(self, counter_contract, counter_contract_type):
        if counter_contract:
            consignor_handler_ids = counter_contract.consignors.values_list('handler_id', flat=True)
            if counter_contract.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS and consignor_handler_ids.exists():
                if self.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS:
                    return self.consignors.filter(handler_id__in=consignor_handler_ids).exists()
                if self.price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS:
                    return not self.consignees.filter(handler_id__in=consignor_handler_ids).exists()
            consignee_handler_ids = counter_contract.consignees.values_list('handler_id', flat=True)
            if counter_contract.price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS and consignee_handler_ids.exists():
                if self.price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS:
                    return self.consignees.filter(handler_id__in=consignee_handler_ids).exists()
                if counter_contract_type != 'sale_contract' and self.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS:
                    return not self.consignors.filter(handler_id__in=consignee_handler_ids).exists()
        return True

    def can_create_through_warehouse_allocation(self, counter_contract, counter_contract_type):  # pylint: disable=too-many-return-statements, too-many-branches
        if counter_contract:
            consignor_handler_ids = counter_contract.consignors.values_list('handler_id', flat=True)
            consignee_handler_ids = counter_contract.consignees.values_list('handler_id', flat=True)
            if counter_contract_type == 'sale_contract':
                if (counter_contract.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS and
                        consignor_handler_ids.exists()):
                    if self.price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS:
                        return self.consignees.filter(handler_id__in=consignor_handler_ids).exists()
                    if self.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS:
                        return not self.consignors.filter(handler_id__in=consignor_handler_ids).exists()
                if (counter_contract.price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS and
                        consignee_handler_ids.exists()):
                    if self.price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS:
                        return not self.consignees.filter(handler_id__in=consignee_handler_ids).exists()
            else:
                if (counter_contract.price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS and
                        consignee_handler_ids.exists()):
                    if self.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS:
                        return self.consignors.filter(handler_id__in=consignee_handler_ids).exists()
                    if self.price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS:
                        return not self.consignees.filter(handler_id__in=consignee_handler_ids).exists()
                if (counter_contract.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS and
                        consignor_handler_ids.exists()):
                    if self.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS:
                        return not self.consignors.filter(handler_id__in=consignor_handler_ids).exists()
        return True

    @staticmethod
    def matching_consignor_criteria(handler_ids):
        return models.Q(
            models.Q(price_point_id__in=CONSIGNOR_MANDATORY_PRICE_POINTS,
                     contractcommodityhandler_set__handler_id__in=handler_ids,
                     contractcommodityhandler_set__role=CONSIGNOR_ROLE) |
            models.Q(price_point_id__in=CONSIGNOR_OPTIONAL_PRICE_POINTS)
        )

    @staticmethod
    def matching_consignee_criteria(handler_ids):
        return models.Q(
            models.Q(price_point_id__in=CONSIGNEE_MANDATORY_PRICE_POINTS,
                     contractcommodityhandler_set__handler_id__in=handler_ids,
                     contractcommodityhandler_set__role=CONSIGNEE_ROLE) |
            models.Q(price_point_id__in=CONSIGNEE_OPTIONAL_PRICE_POINTS)
        )

    def direct_to_buyer_allocation_contracts_criteria(self):
        consignor_handler_ids = self.consignors.values_list('handler_id', flat=True)
        if self.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS and consignor_handler_ids.exists():
            return self.matching_consignor_criteria(consignor_handler_ids)
        consignee_handler_ids = self.consignees.values_list('handler_id', flat=True)
        if self.price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS and consignee_handler_ids.exists():
            return self.matching_consignee_criteria(consignee_handler_ids)
        return ~models.Q(pk__in=[])

    def through_warehouse_allocation_consignor_criteria(self):
        consignee_handler_ids = self.consignees.values_list('handler_id', flat=True)
        if self.price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS and consignee_handler_ids.exists():
            return self.matching_consignor_criteria(consignee_handler_ids)
        return ~models.Q(pk__in=[])

    def through_warehouse_allocation_consignee_criteria(self):
        consignor_handler_ids = self.consignors.values_list('handler_id', flat=True)
        if self.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS and consignor_handler_ids.exists():
            return self.matching_consignee_criteria(consignor_handler_ids)
        return ~models.Q(pk__in=[])

    def existing_purchase_allocation_criteria(self):
        return (models.Q(purchasecontract_set__purchase__id=self.id) |
                models.Q(throughwarehousepurchasecontract_set__purchase_id=self.id))

    def existing_sale_allocation_criteria(self):
        return (models.Q(salecontract_set__sale__id=self.id) |
                models.Q(throughwarehousesalecontract_set__sale_id=self.id))

    def send_create_email(self, request, acceptance_request):
        contract_sent.send(
            sender=Contract,
            instance=self,
            created_by_id = get(request.user, 'id'),
            contract_acceptance_request=acceptance_request.id,
            state='creation'
        )
        from core.jobs.models import Job
        params = {
            'id': self.id,
            'communication_id': get(acceptance_request, 'communication.id')
        }
        Job.schedule_job_for_task(
            job_type='send_contract_created_mail',
            params=params
        )

    def send_amend_email(self, request, acceptance_request):
        contract_sent.send(
            sender=Contract,
            instance=self,
            created_by_id = get(request.user, 'id'),
            contract_acceptance_request=acceptance_request.id,
            state='amendment'
        )
        from core.jobs.models import Job
        params={
            'contract_id': self.id,
            'user_id': request.user.id,
            'communication_id': get(acceptance_request, 'communication.id')
        }
        Job.schedule_job_for_task(
            'send_amended_contract_mail',
            params
        )

    def send_void_email(self, request, acceptance_request):
        contract_sent.send(
            sender=Contract,
            instance=self,
            created_by_id = get(request.user, 'id'),
            contract_acceptance_request=get(acceptance_request, 'id'),
            state='void'
        )
        reason = get(acceptance_request, 'request_reason')
        params = {
            "object_id": self.id,
            "raised_for": "contract",
            "acted_by": request.user.id,
            "subject": get(request.data, 'subject'),
            "acceptance_required": get(request.data, 'acceptanceRequired'),
            "request_reason": reason,
            "communication_id": get(acceptance_request, 'communication.id'),
        }
        from core.jobs.models import Job
        Job.schedule_job_for_task(
            'send_void_mail',
            params
        )

class Brokerage(ContractBaseModel):
    class Meta:
        db_table = 'contract_brokerages'
        ordering = ['id']

    FILLABLES = ContractBaseModel.FILLABLES + [
        'contract_id',
        'type',
        'fee_type',
        'rate',
        'total_fee',
        'broker_company_id',
        'frequency',
    ]

    limit = {"model__in": ("company", "contract")}
    entity_type = models.ForeignKey(
        ContentType,
        limit_choices_to=limit,
        on_delete=models.CASCADE
    )
    entity_id = models.PositiveIntegerField()
    entity_object = GenericForeignKey('entity_type', 'entity_id')

    type = models.CharField(
        max_length=10,
        null=False,
        blank=False,
        choices=constants.ROLES
    )
    fee_type = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        choices=constants.BROKERAGE_FEE_TYPES
    )
    charged_at = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        choices=constants.CHARGED_AT_PERIODS
    )
    charged_on = models.CharField(
        max_length=50,
        null=False,
        blank=False,
        choices=constants.CHARGED_ON,
        default='Pre-Delivery'
    )
    subscription_start_date = models.DateField(
        null=True,
        blank=True
    )
    frequency = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        choices=constants.BROKERAGE_FREQUENCIES
    )
    rate = RoundedFloatField(null=True, blank=True, default=1)
    total_fee = RoundedFloatField(null=True, blank=True)
    broker_company = models.ForeignKey(
        'companies.Company',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
    )

    invoice_set = GenericRelation(
        'invoices.Invoice', related_query_name='brokerage',
        object_id_field='raised_for_id', content_type_field='raised_for_type'
    )

    @property
    def is_not_post_delivery(self):
        return self.charged_on in [PRE_DELIVERY, END_OF_DELIVERY]

    @property
    def is_post_delivery(self):
        return self.charged_on == POST_DELIVERY

    @property
    def is_end_of_delivery(self):
        return self.charged_on == END_OF_DELIVERY

    @staticmethod
    def get_company_brokerages(company_id, broker_company_id=None):
        return Brokerage._filter_by_entity(company_id, Company, broker_company_id)

    @staticmethod
    def get_contract_brokerages(contract_id):
        contract_id = contract_id if isinstance(contract_id, list) else [contract_id]
        return Brokerage._filter_by_entity(contract_id, Contract)

    @staticmethod
    def _filter_by_entity(entity_id, entity_klass, broker_company_id=None):
        filters = {
            'entity_id__in': entity_id if isinstance(entity_id, list) else [entity_id],
            'entity_type': ContentType.objects.get_for_model(entity_klass),
        }
        if broker_company_id:
            filters.update({'broker_company_id': broker_company_id})

        return Brokerage.objects.filter(**filters)

    @classmethod
    def build_with_save(cls, params, entity_object=None, status=None):
        def _save(_params):
            obj = None
            if _params:
                _params.update(
                    {
                        'entity_object': entity_object,
                        'status': status,
                        'created_by_id': entity_object.created_by_id,
                        'updated_by_id': entity_object.updated_by_id,
                    }
                )
                obj = cls(**_params)
                obj.type = obj.type or 'Seller'
                obj._save()
            return obj

        return list(map(_save, params))

    @property
    def _mandatory_fields(self):
        return ['fee_type']

    @classmethod
    def update_brokerage_on_change(cls, contract_id, args):
        amended_price = float(args.get(PRICE_KEY, 0))
        amended_tonnage = float(args.get(TONNAGE_KEY, 0))
        new_contract_value = amended_price * amended_tonnage

        return Brokerage.get_contract_brokerages(contract_id=contract_id).update(
            total_fee=Case(
                When(fee_type=FEE_PER_MT_BROKERAGE, then=amended_tonnage * F('rate')),
                When(fee_type=PERCENT_OF_SALE_BROKERAGE, then=(new_contract_value * F('rate'))/100),
                default=F('total_fee')
            )
        )

    def info(self):
        role = self.type.lower()
        return {
            'payble_by': self.type,
            role + '_brokerage_charged_on': self.charged_on,
            role + '_brokerage_fee_type': self.fee_type,
            role + '_brokerage_rate': self.rate,
            role + '_brokerage_total_fee': self.total_fee,
        }


class AcceptanceRequest(BaseModel):
    class Meta:
        abstract = True

    resolved = models.BooleanField(default=False)
    accepted = models.BooleanField(default=False)
    args = models.JSONField(null=True, blank=True)
    type = models.CharField(
        max_length=100, null=False, blank=False, choices=constants.CONTRACT_ACCEPTANCE_REQUEST_TYPES)
    rejection_reason = models.TextField(null=True, blank=True)
    request_reason = models.TextField(null=True, blank=True)
    acceptance_required = models.BooleanField(default=True, null=True, blank=True)

    @property
    def is_confirm(self):
        return self.type == 'confirm'

    @property
    def is_void(self):
        return self.type == 'void'

    @property
    def is_amend(self):
        return self.type == 'amend'

    def mark_accepted(self, user=None):
        self.mark_resolved(accepted=True, user=user)

    def mark_resolved(self, accepted=False, user=None): # pylint: disable=unused-argument
        self.accepted = accepted
        self.resolved = True
        if user:
            self.updated_by = user
        self.save()


class ContractAcceptanceRequest(AcceptanceRequest): # pylint: disable=too-many-instance-attributes
    class Meta:
        db_table = 'contract_acceptance_requests'

    contract = models.ForeignKey(
        Contract,
        on_delete=models.CASCADE,
        null=False,
        blank=False,
        related_name='acceptance_requests'
    )
    receivers = ArrayField(models.IntegerField(), null=True, blank=True)

    def sibling(self, _type):
        return ContractAcceptanceRequest.objects.filter(
            contract_id=self.contract_id, type=_type
        ).last()

    def mark_resolved(self, accepted=False, user=None):
        if not accepted:
            if self.is_void:
                contract_void_request_rejected.send(
                    sender=Contract, instance=self.contract, created_by_id=user.id,
                    rejection_reason=self.rejection_reason
                )
            elif self.is_amend:
                contract_amend_rejected.send(
                    sender=Contract, instance=self.contract, created_by_id=user.id,
                    rejection_reason=self.rejection_reason, contract_acceptance_request=self.id
                )
                confirm_request = self.sibling('confirm')
                if confirm_request and not confirm_request.accepted:
                    self.contract.status = 'rejected'
                    self.contract.save()

        super().mark_resolved(accepted=accepted, user=user)

    def create_requested_audit_record(self, user, action=None, movement_identifier=''):
        if user and self.is_void:
            contract_void_requested.send(
                sender=Contract, instance=self.contract, created_by_id=user.id
            )
        if action in ['tonnage_amend_by_inload', 'tonnage_amend_by_outload']:
            message = CONTRACT_TONNAGE_AMEND_AUDIT.get(action).format(
                    tonnage_label=self.contract.country.get_label('tonnage'), movement_identifier=movement_identifier
                )
            contract_amend_requested.send(
                sender=Contract,
                instance=self.contract,
                created_by_id=user.id,
                amend_request_text=message,
                contract_acceptance_request=self.id
            )
        elif user and self.is_amend:
            amend_request_text = constants.REQUESTED_AN_AMENDMENT_FOR
            if self.contract.is_planned():
                amend_request_text = constants.EDITED
            elif self.contract.is_rejected():
                amend_request_text = constants.EDITED_AND_RE_SUBMITTED
            contract_amend_requested.send(
                sender=Contract,
                instance=self.contract,
                created_by_id=user.id,
                amend_request_text=amend_request_text,
                contract_acceptance_request=self.id
            )
        elif action == 'vendor_dec':
            contract_vendor_declaration_requested.send(
                sender=Contract,
                instance=self.contract,
                created_by_id=user.id,
                amend_request_text=constants.REQUESTED_VENDOR_DECLARATION,
                contract_acceptance_request=self.id
            )


    def get_brokerage_diff(self, party):
        args = self.args
        # pylint: disable=consider-iterating-dictionary
        if 'amended' in args.keys():
            args = args['amended']
        new_dict = list(filter(lambda x: x['type'] == party, args['brokerages']))
        new_dict = new_dict[0] if new_dict else {}
        brokerages = self.contract.brokerage_set.filter(type=party)
        if brokerages:
            brokerage = brokerages.first()
            brokerage.__dict__.update(new_dict)
            return brokerage.get_dirty_fields()

        return new_dict

    def is_created_by_seller(self):
        return self.contract.is_seller(self.created_by)

    def is_created_by_buyer(self):
        return self.contract.is_buyer(self.created_by)

    def should_auto_amend(self, user=None):
        args = self.args
        # pylint: disable=consider-iterating-dictionary
        if args and 'amended' in args.keys():
            args = args['amended']
        if self.is_amend and args:
            if 'buyer' in args and not args['buyer']: #  pylint: disable=unsupported-membership-test
                args.pop('buyer')
            if 'seller' in args and not args['seller']: #  pylint: disable=unsupported-membership-test
                args.pop('seller')
            if 'consignors' in args and len(args) == 1: #  pylint: disable=unsupported-membership-test
                return self.contract.price_point_id in CONSIGNEE_MANDATORY_PRICE_POINTS and self.is_created_by_seller()
            if 'consignees' in args and len(args) == 1: # pylint: disable=unsupported-membership-test
                return self.contract.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS and self.is_created_by_buyer()

            if list(args.keys()) == ['brokerages']:  # pylint: disable=unsupported-membership-test
                if self.contract.is_seller(user) and self.contract.is_buyer(user):
                    return True
                elif self.contract.is_seller(user):
                    return not self.get_brokerage_diff(BROKERAGE_TYPE_BUYER)
                elif self.contract.is_buyer(user):
                    return not self.get_brokerage_diff(BROKERAGE_TYPE_SELLER)
        return False


class AcceptanceRequestCommunication(BaseCommunication):
    class Meta:
        db_table = 'contract_acceptance_request_communications'

    request = models.OneToOneField(
        'contracts.ContractAcceptanceRequest',
        on_delete=models.CASCADE,
        related_name='communication',
    )


class TitleTransfer(BaseModel, QuantityMixin, InstanceCustomFuncMixin):  # pylint: disable=too-many-public-methods,too-many-instance-attributes
    class Meta:
        db_table = 'title_transfers'
        ordering = ['-updated_at']
        constraints = [
            models.UniqueConstraint(
                fields=['identifier'], condition=(~models.Q(status='void')), name='title_transfer_identifier_uniq')
        ]
        indexes = [
            models.Index(fields=['status']),
            GinIndex(fields=['viewer_company_ids']),
        ]

    invoice_item_type = TITLE_TRANSFER_MODEL

    process_on = models.DateField()
    tonnage = RoundedFloatField(default=0.0)
    commodity_contract = models.ForeignKey(
        Contract,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    seller = models.OneToOneField(
        Party,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='title_transfer_seller',
    )
    buyer = models.OneToOneField(
        Party,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='title_transfer_buyer',
    )
    storage = models.ForeignKey(
        'farms.Storage',
        on_delete=models.SET_NULL,
        null=True, blank=True
    )
    bhc_site = models.ForeignKey(
        'farms.Farm',
        on_delete=models.SET_NULL,
        null=True, blank=True,
    )
    variety = models.ForeignKey(
        'commodities.Variety',
        on_delete=models.DO_NOTHING,
        null=True, blank=True,
    )
    grade = models.ForeignKey(
        'commodities.Grade',
        on_delete=models.DO_NOTHING
    )
    identifier = models.CharField(
        max_length=14,
        db_index=True
    )
    reference_number = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    external_reference_number = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    status = FSMField(
        max_length=40,
        choices=constants.STATUSES,
        null=False,
        blank=False,
        default='planned',
    )
    rejection_reason = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    coil = RoundedFloatField(null=True, blank=True)
    impu = RoundedFloatField(null=True, blank=True)
    season = models.CharField(max_length=5, null=True, blank=True, validators=[season_validator])
    commodity = models.ForeignKey('commodities.Commodity', on_delete=models.DO_NOTHING, null=True, blank=True)
    canola_load_ids = models.JSONField(null=True, blank=True)
    canola_outload_composition = GenericRelation(
        'loads.OutloadComposition', object_id_field='entity_for_id', content_type_field='entity_for_type')
    extras = models.JSONField(null=True, blank=True, default=dict)
    throughput = models.BooleanField(default=False)
    external_system = models.CharField(max_length=255, null=True, blank=True)
    viewer_company_ids = ArrayField(models.IntegerField(), null=True, blank=True)

    note_set = GenericRelation(
        'notes.Note', related_query_name='title_transfer',
        object_id_field='object_id', content_type_field='object_type'
    )
    audit_history_set = GenericRelation(
        'audit_history.AuditHistory', related_query_name='title_transfer', object_id_field='instance_id',
        content_type_field='instance_type'
    )

    FILLABLES = [
        'process_on',
        'tonnage',
        'commodity_contract_id',
        'bhc_site_id',
        'grade_id',
        'identifier',
        'reference_number',
        'coil',
        'impu',
        'quantity',
        'variety_id',
        'storage_id',
        'rejection_reason',
        'communication',
        'title_transfer',
        'commodity_id',
        'season',
        'external_reference_number',
        'seller',
        'buyer',
        'handler_id',
        'reference_number',
        'canola_load_ids',
        'extras',
        'external_system',
        'loads',
        'external_reference',
        'reason',
        'commodity',
        'grade',
        'variety',
        'status',
        'external_contract_number',
        'note',
        'contract_id',
        'throughput',
        'contract_price',
        'payment_term_id',
        'payment_scale_id'
    ]

    @classmethod
    def filter_by_load_id(cls, load_id):
        return cls.objects.exclude(status__in=['void', 'rejected']).filter(extras__load_id=load_id)

    def get_matching_contract_from_external_reference_number(self):
        if self.external_reference_number:
            queryset = Contract.objects.filter(
                seller__company_id=self.seller.company_id,
                buyer__company_id=self.buyer.company_id,
                commodity_id=self.commodity_id,
                status__in=[CONFIRMED_STATUS, IN_PROGRESS_STATUS, OPEN, DELIVERED_STATUS, COMPLETED_STATUS],
                season=self.season,
                unaccounted_tonnage__gte=self.tonnage
            ).filter(
                models.Q(contract_number__iexact=self.external_reference_number) |
                models.Q(identifier__iexact=self.external_reference_number)
            )
            return queryset.filter(
                grade_id=self.grade_id
            ).first() or queryset.filter(spread__details__contains=[{'id': self.grade_id}]).first()

    @cached_property
    def country_id(self):
        return self.commodity.country_id if self.commodity_id else self.created_by.company.country_id

    @cached_property
    def country_code(self):
        return self.country.code

    @cached_property
    def country(self):
        return Country.objects.filter(id=self.country_id).first()

    @cached_property
    def model_display_name(self):
        return constants.TITLE_TRANSFER_DISPLAY_NAME

    @property
    def is_cash_priced(self):
        return get(self, 'commodity_contract.is_cash_priced', False)

    @property
    def is_grade_matching_with_contract(self):
        return (get(self, 'commodity_contract.is_blended') or
                (get(self, 'grade_id') in get(self, 'commodity_contract.all_grade_ids')))

    @property
    def cash_price_external_reference(self):
        return get(self, 'commodity_contract.cash_price.external_reference', None)

    @property
    def cash_price_price(self):
        return get(self, 'commodity_contract.cash_price.price', None)

    @property
    def cash_price_id(self):
        return get(self, 'commodity_contract.cash_price_id', None)

    @property
    def date_time(self):  # needed so that FM and TT are at the same level (for example in invoice)
        return self.process_on

    @property
    def status_display_name(self):
        return to_display_attr(constants.STATUSES, self.status)

    def audit(self, action, user=None, **kwargs):
        TitleTransferAudit(action, self, user, **kwargs).send()

    def amended_details_for_acceptance_request(self, acceptance_request_id):
        return self.get_amended_details(acceptance_request_id=acceptance_request_id)

    def get_amended_details(self, acceptance_request_id=None):
        if acceptance_request_id:
            filters = {'id': acceptance_request_id}
        else:
            filters = {'resolved': False, 'type': CONTRACT_ACCEPTANCE_REQUEST_TYPE_AMEND}
        return self.acceptance_requests.filter(**filters).values_list(
            'args', flat=True
        ).first()

    def request_communication_dict(self, request_id):
        request = self.acceptance_requests.filter(id=request_id).select_related('communication').first()
        communication = get(request, 'communication')
        if communication:
            return communication.to_dict()

    def estimated_payment_due_date(self):
        if self.commodity_contract_id:
            return ContractBaseModel.get_estimated_payment_due_date(
                self.commodity_contract.payment_term, self.process_on, self.buyer.company)

    def estimated_payment_due_date_exact(self):
        if self.commodity_contract_id:
            return ContractBaseModel.get_estimated_payment_due_date_exact(
                self.commodity_contract.payment_term, self.process_on, self.buyer.company)

    def get_canola_load_ids(self):
        if isinstance(self.canola_load_ids, dict):
            return list(self.canola_load_ids.keys())

        return []

    @property
    def has_canola_loads(self):
        return bool(self.get_canola_load_ids())

    def get_tonnage_from_canola_loads(self):
        return sum((self.canola_load_ids or {}).values())

    @property
    def inloads(self):
        return self.load_set.filter(type=INLOAD)

    @property
    def inload(self):
        return self.inloads.first()

    @property
    def outloads(self):
        return self.load_set.filter(type=OUTLOAD)

    @property
    def outload(self):
        return self.outloads.first()

    @property
    def canola_loads(self):
        from core.loads.models import Load
        canola_load_ids = self.get_canola_load_ids()
        if canola_load_ids:
            return Load.objects.filter(id__in=canola_load_ids)
        return Load.objects.none()

    def verbose_description(self, _):
        return "{number} - {status} - {tonnage}{unit}".format(
            number=self.identifier, tonnage=self.inferred_tonnage,
            unit=self.inferred_tonnage_unit,
            status=self.status.title()
        )

    @property
    def web_href(self):
        return "#/contracts/{_id}/title-transfers".format(_id=self.commodity_contract_id)

    def tonnage_distribution(self, _):
        return [
            self._tonnage_description(
                self.inferred_tonnage, self.inferred_tonnage_unit,
                self.status
            )
        ]

    @property
    def direct_descendants(self):
        return []

    @property
    def commodity_contract_consignee_handler_id(self):
        return get(self, 'commodity_contract.consignee.handler_id')

    @property
    def buyer_company_id(self):
        return get(self, 'buyer.company_id')

    @property
    def seller_company_id(self):
        return get(self, 'seller.company_id')

    def set_viewer_company_ids(self):
        self.__class__.objects.filter(id=self.id).update(viewer_company_ids=self.get_viewer_company_ids())

    def get_viewer_company_ids(self):
        return compact({
            *(get(self, 'commodity_contract.viewer_company_ids', []) or []),
            get(self, 'created_by.company_id'),
            *(get(self, 'seller.company_ids') or []),
            *(get(self, 'buyer.company_ids') or []),
            get(self, 'storage.farm.company_id'),
            *SYSTEM_COMPANY_IDS
        })

    @classmethod
    def filter_by_company(cls, company_id):
        return cls.objects.filter(viewer_company_ids__contains=[company_id])

    @property
    def title_transfer_type(self):
        if get(self, 'commodity_contract.cash_price_id'):
            return 'Cash'
        elif self.commodity_contract:
            return 'Contract'
        else:
            return 'Independent'

    @classmethod
    def filter_by_company_for_csv(cls, company_id):
        return cls.filter_by_company(company_id).select_related(
            'commodity_contract__seller__company',
            'commodity_contract__seller__contact',
            'commodity_contract__seller__ngr',
            'commodity_contract__buyer__company',
            'commodity_contract__buyer__contact',
            'commodity_contract__buyer__ngr',
            'commodity_contract__commodity',
            'commodity_contract__variety',
            'grade',
            'storage__farm',
            'buyer',
            'seller',
        ).order_by('created_at')

    def to_csv_row(self, user, tz, canola_load_by_load=False, load=None, headers=None, country=None):  # pylint: disable=too-many-locals
        seller_type = 'Trader' if not get(self, 'seller.company.is_grower') else 'Grower'
        buyer_type = 'Trader' if not get(self, 'buyer.company.is_grower') else 'Grower'
        tonnage = self.inferred_tonnage
        unit = user.unit if user else country.display_unit
        date_format = country.get_format('date')
        abn_label = country.get_label('abn')
        tonnage_label = country.get_label('tonnage')
        if canola_load_by_load:
            tonnage = get(self.canola_load_ids, str(load.id))
            tonnage_label = f"Load {tonnage_label}"
        sustainable = get(self, 'commodity_contract.cash_price.sustainable_commodity')
        csv_row = {
            "Title Transfer Number": self.identifier,
            "Type": self.title_transfer_type,
            "Status": self.status,
            "Contract Number": get(self, 'commodity_contract.contract_number')
                               or get(self, 'external_reference_number'),
            "Broker Note No / Sales Confirmation No": get(self, 'commodity_contract.identifier'),
            "Seller Type": seller_type,
            f"Seller {abn_label}": self.seller.company_abn,
            "Seller": get(self, 'seller.company_name'),
            "Seller Contact": get(self, 'seller.contact.name'),
            "Seller NGR": get(self, 'seller.ngr.ngr_number') or get(self, 'commodity_contract.seller.ngr.ngr_number'),
            "Buyer Type": buyer_type,
            f"Buyer {abn_label}": self.buyer.company_abn,
            "Buyer": get(self, 'buyer.company_name'),
            "Buyer Contact": get(self, 'buyer.contact.name'),
            "Buyer NGR": get(self, 'buyer.ngr.ngr_number') or get(self, 'commodity_contract.buyer.ngr.ngr_number'),
            "Transfer Site Company": get(self, 'storage.farm.company.display_name'),
            "Transfer Site": get(self, 'storage.farm.name'),
            "Commodity": get(self, 'commodity.display_name'),
            "Sustainable": sustainable if sustainable else '',
            "Variety": get(self, 'variety.name'),
            "Grade": self.grade_name,
            "Season": get(self, 'season'),
            tonnage_label: round(self.commodity.convert_to(tonnage, self.commodity.unit, unit), 5),
            f"Quantity ({BALES})": self.actual_quantity if self.commodity.is_bales else 'NA',
            f"Quantity ({MODULES})": self.actual_quantity if self.commodity.is_modules else 'NA',
            f"Quantity ({METER_CUBE})": self.actual_quantity if self.commodity.is_meter_cube else 'NA',
            f"Quantity ({KG})": self.actual_quantity if self.commodity.is_kg else 'NA',
            f"Price(per {country.display_unit})": float(
                "%0.2f" % get(self, 'commodity_contract.cash_price.price')
            ) if get(self, 'commodity_contract.cash_price') else None,
            "Transfer Date": DateTimeUtil.localize_date(self.process_on, tz, date_format),
            "Reference Number": self.reference_number,
            "Created on": DateTimeUtil.localize_date(self.created_at, tz, date_format),
            "Last modified date": DateTimeUtil.localize_date(self.updated_at, tz, date_format)
        }
        if not canola_load_by_load:
            csv_row.update({
                "COIL (%)": self.coil,
                "IMPU (% by weight)": self.impu,
                "Created By": get(self, 'created_by.name'),
                "Created By Company": get(self, 'created_by.company.name'),
            })
        else:
            load_type, load_identifier = load.get_load_type_with_identifier()
            csv_row.update({
                'Load Type': load_type,
                'Identifier': load_identifier,
                "MOGR (%)": get(load, 'specs.mogr'),
                "PRGR (%)": get(load, 'specs.prgr'),
                "COIL (%)": get(load, 'specs.coil'),
                "TWT (kg/hl)": get(load, 'specs.twt'),
                "IMPU (% by weight)": get(load, 'specs.impu'),
                "BRSH (%)": get(load, 'specs.brsh'),
            })
        csv_row.update({f'{tonnage_label} Unit': unit})
        if headers:
            return [csv_row.get(column) for column in headers]
        else:
            return list(csv_row.values())

    def generate_pdf(self):
        canola_loads=self.canola_loads.select_related('variety', 'grade')
        for load in canola_loads:
            load.load_type, load.identifier = load.get_load_type_with_identifier()
            load.transfer_tonnage = get(self.canola_load_ids, str(load.id))
        commodity_display_name = get(self, 'commodity.display_name')
        if get(self, 'is_sustainable_commodity_contract'):
            commodity_display_name += ' (Sustainable)'
        html_for_pdf = render_to_string(
            'title_transfer_preview.html',
            {
                'title_transfer': self,
                'loads': canola_loads,
                'act': False,
                'create_msg': False,
                'void_msg': False,
                'commodity_display_name': commodity_display_name
            }
        )
        return HTMLToPDF.from_string(html_for_pdf, False)

    @classmethod
    def build_party(cls, params, role):
        if params:
            abn = params.pop('abn', None)
            ngr_number = params.pop('ngr_number', None)
            user = get_current_user()
            if abn and not params.get('company_id'):
                company = Company.get_or_create_by_abn(abn, user)
                params['company_id'] = company.id if company else None
            if ngr_number and not params.get('ngr_id'):
                ngr = Ngr.objects.filter(ngr_number__iexact=ngr_number.strip()).first()
                if not ngr and params.get('company_id'):
                    ngr = Ngr.create_by_number(ngr_number, params['company_id'], user=user)
                params['ngr_id'] = ngr.id if ngr else None

            return Party(role=role, **params)

    def create_spot_contract_from_title_transfer(self, params):
        user = self.created_by
        tz = user.company.country.timezone
        price_point_id = PRICE_POINTS_DELIVERED_SITE_ID
        site_type = "consignees"
        document_type_id = CONTRACT_DOCUMENT_TYPE_ID
        administration = {'invoicing': BUYER_RCTI_INVOICING}
        contract_identifier = generate_identifier(CONTRACT_DOCUMENT_TYPE)
        represented_by_id = None
        if user.company.is_broker:
            document_type_id = BROKER_NOTE_DOCUMENT_TYPE_ID
            administration = {'invoicing': BUYER_RCTI_INVOICING, 'brokered_by': user.company, 'broker_contact': user}
            contract_identifier = generate_identifier(BROKER_NOTE_DOCUMENT_TYPE)
            represented_by_id = user.company_id
        if not params['identifier']:
            params['identifier'] = contract_identifier
        with transaction.atomic():
            today = DateTimeUtil.localize(timezone.now(), tz).date()
            params.update({
                "document_type_id": document_type_id,  # contract
                "type_id": CONTRACT_TYPE_SPOT_ID,  # spot contract / cash contract
                "tonnage": self.tonnage,
                "price_point_id": price_point_id,
                "delivery_onus": "Seller",
                "owner_id": Country.get_root_user_id(),
                "commodity_id": self.commodity_id,
                "grade_id": self.grade_id,
                "season": self.season,
                "delivery_start_date": today,
                "delivery_end_date": today,
                "administration": administration,
                site_type: [
                    {
                        "handler_id": self.storage.farm_id,
                        "position": 1,
                        "ld": "0.00",
                    }
                ],
                "seller": {
                    "company_id": self.seller.company_id,
                    "contact_id": self.seller.contact_id,
                    "ngr_id": self.seller.ngr_id,
                    "represented_by_id": represented_by_id
                },
                "buyer": {
                    "company_id": self.buyer.company_id,
                    "contact_id": self.buyer.contact_id,
                    "ngr_id": self.buyer.ngr_id,
                },
                "communication": {"acceptance_required": False},
                "tolerance_id": 1,  # Nil Tolerance
                "brokerages": NIL_BROKERAGES,
            })
            contract = Contract.persist(params, can_send_create_email=True, spot_contract_from_title_transfer=True)
            if contract.errors:
                return {'errors': contract.errors}
            return contract

    @classmethod
    def criteria_for_search(cls, search_str):
        return (
                models.Q(identifier__icontains=search_str) |
                models.Q(external_reference_number__icontains=search_str) |
                models.Q(reference_number__icontains=search_str) |
                models.Q(status__icontains=search_str) |
                models.Q(status__icontains=search_str.replace(' ', '_')) |
                models.Q(storage__farm__name__icontains=search_str) |
                models.Q(storage__farm__company__business_name__icontains=search_str) |
                models.Q(seller__company__business_name__icontains=search_str) |
                models.Q(buyer__company__business_name__icontains=search_str) |
                models.Q(commodity__name__icontains=search_str) |
                models.Q(grade__name__icontains=search_str) |
                models.Q(season__icontains=search_str) |
                models.Q(commodity_contract__identifier__icontains=search_str)
        )

    def update_note_data(self, note_params):
        if note_params and 'description' in note_params:
            company_id = note_params['company_id']
            note = self.note_set.filter(company_id=company_id).first()
            if not note:
                Note.persist({**note_params, 'object_id': self.id,
                              'object_type_klass': TitleTransfer,
                              'created_by_id': self.created_by.id})
                return
            note.description = note_params['description']
            if 'attachments' in note_params:
                files = note_params.pop('attachments', None)
                attachments = [file for file in files if 'base64' not in file]
                if note.id:
                    for _file in files:
                        if 'base64' in _file:
                            _name = Note.FILE_PATH + str(note.id) + '/' + _file['name']
                            attachments.append({'name': _file['name'],
                                                'url': S3.upload_base64(_file['base64'], _name, False)})
                note.attachments = attachments
            note.save()

    def check_identifier_uniqueness_error(self):
        exclusion_criteria = models.Q(status__in=['void'])
        if self.persisted:
            exclusion_criteria |= models.Q(id=self.id)
        return self.__class__.objects.exclude(exclusion_criteria).filter(
            identifier__iexact=self.identifier
        ).exists() and not self.persisted

    def __set_identifier_uniqueness_error(self):
        error = self.check_identifier_uniqueness_error()
        if error:
            self.errors = {}
            self.errors["identifier"] = ['Title Transfer with this identifier already exists']

    def validate_unique(self, *args, **kwargs):
        self.__set_identifier_uniqueness_error()
        super().validate_unique(*args, **kwargs)

    @classmethod
    def build(cls, params):
        seller = cls.build_party(params.pop('seller', None), 'Seller')
        buyer = cls.build_party(params.pop('buyer', None), 'Buyer')
        handler_id = params.pop('handler_id', None)
        storage_id = Storage.objects.filter(
            is_gate=True, farm_id=handler_id
        ).values_list('id', flat=True).first()
        if storage_id:
            params['storage_id'] = storage_id
        title_transfer = cls(**params)
        title_transfer.grade_id = title_transfer.grade_id
        seller.save()
        buyer.save()
        title_transfer.seller = seller
        title_transfer.buyer = buyer
        load_id = get(title_transfer, 'extras.load_id')
        if not title_transfer.external_system:
            title_transfer.external_system = (get(title_transfer, 'site.externally_sync_source') or '').lower() or None
            title_transfer.extras = title_transfer.extras or {}
            title_transfer.extras['source'] = 'agrichain'

        if title_transfer.is_canola_load_by_load_transfer:
            if not title_transfer.canola_load_ids and load_id:
                from core.loads.models import Load
                canola_load_ids = {str(load_id): title_transfer.tonnage}
                load = Load.objects.filter(id=load_id).first()
                if load and load.split_siblings.exists():
                    canola_load_ids[str(load_id)] = load.shrunk_tonnage
                    for sibling in load.split_siblings:
                        canola_load_ids[str(sibling.id)] = sibling.shrunk_tonnage
                title_transfer.canola_load_ids = canola_load_ids
            if not title_transfer.canola_load_ids:
                title_transfer.errors = {'canola_load_ids': ['Canola loads are required']}
                if seller.id:
                    seller.delete()
                if buyer.id:
                    buyer.delete()

        return title_transfer

    def get_reasons_for_non_assignable_contract(self, contract):
        reasons = []
        unmatched_fields = []
        if contract.status in ['confirmation_pending', 'void']:
            reasons.append('Contract status should be confirmed.')
        if self.commodity_id != contract.commodity_id:
            unmatched_fields.append('Commodity')
        if self.grade_id != contract.grade_id:
            unmatched_fields.append('Grade')
        if self.season != contract.season:
            unmatched_fields.append('Season')
        sites = []
        if contract.price_point_id in CONSIGNOR_MANDATORY_PRICE_POINTS:
            sites = contract.consignors.values_list('handler_id', flat=True)
        else:
            sites = contract.consignees.values_list('handler_id', flat=True)
        if contract.price_point_id != PRICE_POINTS_TRACK_ID and get(self, 'storage.farm_id') not in sites:
            unmatched_fields.append('Site')
        if get(contract, 'seller.company_id') != get(self, 'seller.company_id'):
            unmatched_fields.append('Seller')
        if get(contract, 'buyer.company_id') != get(self, 'buyer.company_id'):
            unmatched_fields.append('Buyer')
        if len(unmatched_fields) != 0:
            reasons.append(
                f'{", ".join(unmatched_fields)} in the existing title transfer does not match with the given contract.')
        elif contract.unaccounted_tonnage < self.tonnage:
            reasons.append('Not enough tonnage remaining on contract.')

        return reasons

    @property
    def is_load_by_load_transfer_on(self):
        return get(self, 'site.sitemanagementsettings.load_by_load_transfer')

    @property
    def is_canola_load_by_load_transfer(self):
        return self.is_canola and self.is_grower_seller and self.is_load_by_load_transfer_on

    @property
    def is_grower_seller(self):
        return self.get_seller().is_grower

    def post_create(self, user, communication_params=None, process_external=False, create_audit=False):
        if not self.persisted:
            return

        if communication_params and 'acceptance_required' in communication_params:
            communication_params.pop('acceptance_required')

        params = {}
        acceptance_request = None
        if communication_params:
            acceptance_request = TitleTransferAcceptanceRequest.create({
                'type': CONFIRM_REQUEST_TYPE,
                'title_transfer_id': self.id,
                'created_by_id': self.created_by.id,
                'updated_by_id': self.created_by.id,
                'resolved': True
            })
            TitleTransferCommunication.create(
                communication_params, kwargs={
                    'title_transfer_id': self.id,
                    'request_id': acceptance_request.id,
                    'type': 'create',
                    'mail_status': bool(communication_params.get('subject'))
                }
            )
            params = {'acceptance_request_id': acceptance_request.id}

        if not self.is_draft():
            if self.external_system and self.external_reference_number:
                contract = self.get_matching_contract_from_external_reference_number()
                if contract:
                    self.commodity_contract = contract
                    self.external_reference_number = None
                    self.save()
                    self.commodity_contract.update_tonnages_and_status()
            if create_audit:
                self.audit(action='create', user=user, **params)
            from core.jobs.models import Job
            if self.is_in_past and (not self.external_system and process_external):
                Job.schedule_job_for_task('process_title_transfer', self.id)

            if bool(communication_params and communication_params.get('subject')):
                params = {'action_type': 'confirm'}
                if acceptance_request:
                    params['acceptance_request_id'] = acceptance_request.id
                self.audit('mail', user, **params)
                Job.schedule_job_for_task('send_title_transfer_created_mail', params={'id': self.id})

            self.send_notification(user)
            self.send_mobile_push_notification()

    def post_void(self, user):
        if self.commodity_contract_id:
            contract = self.commodity_contract
            contract.unaccounted_tonnage = contract.get_unaccounted_tonnage()
            contract.delivered_tonnage = contract.get_delivered_tonnage()
            contract.save()
            if contract.cash_price:
                contract.open() if not contract.is_open() else None
                contract.void(user)
                contract.save()
            self.revert_parent_status()
            from core.jobs.models import Job
            job_params = {
                    'item_id': get(self, 'id'),
                    'item_type': core.common.constants.TITLE_TRANSFER_MODEL,
                    'user_id': get(user, 'id')
                }
            if not Job.objects.filter(status='pending', type='create_update_or_delete_draft_invoices',
                                      params=job_params).exists():
                Job.schedule_job_for_task(
                    'create_update_or_delete_draft_invoices',
                    params=job_params
                )

    def send_notification_to_company_admins(self, user, trigger_mail=False, cash_price=None):
        from core.jobs.models import Job
        if self.site and self.site.stocks_management:
            site_company = self.site.company
            if cash_price:
                subject = f'{self.seller.company.name} Title Transfer #{self.identifier} ({self.tonnage} MT) against Cash Price ({self.site.name} | {cash_price.track} | {self.commodity.display_name} | {self.grade.name} | {self.season})'  # pylint: disable=line-too-long
                if self.seller.company_id != user.company_id:
                    subject = f'{user.company.name} A/C {subject}'
            else:
                subject = f'{user.company.name} Title Transfer'
                if user.company.type_id == core.common.constants.BROKER_TYPE_ID and get(self, 'commodity_contract'):
                    representing_seller = get(self.commodity_contract, 'seller.represented_by_id') == user.company_id
                    representing_company = self.seller.company.name if representing_seller else self.buyer.company.name
                    subject += f' A/C {representing_company}'
                subject += f' #{self.identifier}'
            communication_params = {
                'recipients': {
                    'buyer': compact(self.buyer.company.get_company_admins().exclude(
                        is_active=False).values_list('email', flat=True)),
                    'seller': compact(self.seller.company.get_company_admins().exclude(
                        is_active=False).values_list('email', flat=True)),
                    'transfer_site': compact(site_company.get_company_admins().exclude(
                        is_active=False).values_list('email', flat=True))
                    if site_company.id not in [self.buyer.company_id, self.seller.company_id] else [],
                },
                'subject': subject,
                'body': '',
            }
            TitleTransferCommunication.create(
                communication_params, kwargs={
                    'title_transfer_id': self.id, 'type': 'create'}
            )
            if trigger_mail:
                Job.schedule_job_for_task('send_title_transfer_created_mail', params={'id': self.id})

    def is_brokerage_invoiced_for(self, party):
        from core.invoices.models import Invoice
        invoice_item_class = apps.get_model('invoices', 'InvoiceItem')
        return invoice_item_class.objects.filter(
            item_id=self.id,
            item_type_id=self.content_type_id,
            is_rejected=False,
            invoice__type='Brokerage',
            invoice__status__in=Invoice.AFTER_SUBMITTED_STATUSES,
            invoice__payer__company_id=party.company_id,
        ).exists()

    @classmethod
    def is_title_transfer_valid(cls, user, data, external_reference=None):  # pylint: disable=too-many-locals
        from core.loads.models import Load
        seller_ngr_id = get(data, 'seller.ngr_id')
        handler_id = get(data, 'handler_id')
        tonnage = get(data, 'tonnage')
        buyer = get(data, 'buyer')
        seller = get(data, 'seller')
        buyer_ngr_id = get(data, 'buyer.ngr_id')
        storage_id = get(data, 'storage_id')
        errors = []
        farm = None
        seller_ngr = Ngr.objects.filter(id=seller_ngr_id).first()

        if handler_id:
            farm = Farm.objects.filter(pk=handler_id).first()
        elif storage_id:
            farm = get(Storage.objects.filter(id=storage_id).first(), 'farm')

        if not farm:
            errors.append(TITLE_TRANSFER_CREATOR_ERROR)
            return {'errors': errors}

        if not cls.is_valid_title_transfer_creator(user, farm, buyer, seller, seller_ngr):
            errors.append(TITLE_TRANSFER_CREATOR_ERROR)
            return {'errors': errors}

        if buyer_ngr_id == seller_ngr_id:
            errors.append(TITLE_TRANSFER_BUYER_SELLER_NGR_CANNOT_BE_SAME)
            return {'errors': errors}

        if seller_ngr and seller_ngr.is_shared_ngr and cls.is_ngr_primary(
                seller_ngr, get(seller, 'company_id'), False
        ) and not cls.is_ngr_primary(seller_ngr, get(buyer, 'company_id'), True):
            errors.append(TITLE_TRANSFER_SELLER_NGR_ERROR)

        buyer_company = Company.objects.filter(id=get(buyer, 'company_id')).first()
        if farm and farm.company_id != get(buyer, 'company_id') and farm.company.enable_transfer_to_approved_buyers and not get(buyer_company, 'is_grower'): # pylint: disable=line-too-long
            is_approved_buyer = farm.company.approved_buyers.filter(id=get(buyer, 'company_id')).exists()
            if not is_approved_buyer:
                errors.append(TITLE_TRANSFER_APPROVED_BUYER_ERROR)

        if farm and farm.company and not farm.company.overdraft_transfer_allowed:
            available_tonnage = Load.get_available_tonnage(
                handler_id, get(data, 'commodity_id'),
                get(data, 'grade_id'), get(data, 'season'),
                seller_ngr_id
            )
            if tonnage and available_tonnage < round(float(tonnage), 2):
                errors.append(TITLE_TRANSFER_AVAILABLE_TONNAGE_ERROR)
        if external_reference and TitleTransfer.objects.filter(
                reference_number=external_reference).exclude(status=VOID_STATUS).exists():
            errors.append(TITLE_TRANSFER_DUPLICATE_EXTERNAL_REFERENCE_ERROR % external_reference)

        if errors:
            return {'errors': errors}

    @classmethod
    def is_valid_title_transfer_creator(cls, user, farm, buyer, seller, seller_ngr):  # pylint: disable=too-many-locals
        current_user_company = user.company_id
        if not user.company.is_broker:
            is_creator_seller = current_user_company == get(seller, 'company_id')
            is_creator_buyer = current_user_company == get(buyer, 'company_id')
            is_creator_site_company = current_user_company == farm.company_id
            seller_company = Company.objects.filter(id=get(seller, 'company_id')).first()
            is_seller_and_site_non_subscriber = (not get(seller_company, 'transaction_participation') and
                                                 not get(farm, 'company.transaction_participation'))
            buyer_company_and_primary_owner_in_seller_ngr = (is_creator_buyer and
                                                             current_user_company in
                                                             seller_ngr.primary_owner_company_ids)
            return not (not (is_creator_seller or is_creator_site_company or
                        buyer_company_and_primary_owner_in_seller_ngr) and not is_seller_and_site_non_subscriber)
        else:
            seller_broker = get(seller, 'company_id') in user.company.own_and_managed_farm_company_ids
            site_broker = farm.company_id in user.company.own_and_managed_farm_company_ids
            representing_seller = get(seller, 'represented_by_id') == current_user_company
            return seller_broker or site_broker or representing_seller

    @classmethod
    def is_ngr_primary(cls, ngr, company_id, primary=False):
        return ngr.bank_account_set.filter(
                company_id=company_id, is_primary=primary
            ).exists()

    def update_costs(self, payload=None):
        from core.loads.models import LoadCost
        commodity_contract = get(self, 'commodity_contract')
        for load in compact([*self.outloads, *self.inloads]):
            LoadCost.upsert(load, payload, [commodity_contract], self, TITLE_TRANSFER_MODEL)

    def recalculate_cost(self):
        for load in compact([*self.outloads, *self.inloads]):
            load.queue_load_cost_job()

    @cached_property
    def content_type_id(self):
        return self.content_type_ids_for(model_names=['titletransfer'])[0]

    @property
    def is_commodity_contract_invoiced(self):
        return self.__is_invoiced('Commodity Contract')

    def __is_invoiced(self, invoice_type):
        invoice_item_class = apps.get_model('invoices', 'InvoiceItem')
        return invoice_item_class.objects.filter(
            item_id=self.id,
            item_type_id=self.content_type_id,
            invoice__status__in=INVOICE_ACCEPTED_STATUSES,
            is_rejected=False,
            invoice__type=invoice_type,
        ).exists()

    @property
    def is_in_past(self):
        return self.process_on <= DateTimeUtil.localize(timezone.now(), self.country.timezone).date()

    @property
    def is_today(self):
        now = timezone.now()
        return self.process_on in [DateTimeUtil.localize(now, self.country.timezone).date(), now.date()]

    @property
    def grade_name(self):
        return get_grade_display_name(
            self.commodity_id, self.variety_id, self.grade_id,
            self.season,
        )

    @property
    def variety_name(self):
        return get(self, 'variety.name')

    @property
    def site_display_name(self):
        return get(self, 'site.display_name')

    @property
    def is_stocks_management(self):
        return get(self, 'site.stocks_management')

    @property
    def site(self):
        return get(self, 'storage.farm')

    @property
    def farm(self):
        return self.site

    @property
    def description(self):
        contract = self.commodity_contract
        oil_admix = ""
        if (contract.is_canola and get(contract, 'payment_scale.name') != 'Flat'
                and not self.canola_loads and (self.coil or self.impu)):
            if self.coil and self.impu:
                oil_admix = ", {coil}% Oil & {admix}% Admix".format(
                    coil=self.coil, admix=self.impu)
            elif self.coil:
                oil_admix = ", {coil}% Oil".format(coil=self.coil)
            elif self.impu:
                oil_admix = ", {impu}% Oil".format(impu=self.impu)

        grade = '(' + contract.pool_grades + ')' if contract.is_pool_contract else self.grade_name
        quantity = ", {quantity}".format(
            quantity=self.tonnage_display_value
        ) if self.is_quantity_based_commodity and not self.is_strict_quantity_based_commodity else ''
        description = f"{self.identifier}, {contract.commodity.display_name}, {grade}{quantity}"
        if contract.season and contract.season != core.common.constants.SEASON_NA:
            description += f", {contract.season}"
        reference_number = ", Ref. No: {reference_number}".format(
            reference_number=self.reference_number) if self.reference_number else ''
        return "{description}, {price_point}, {site}{oil_admix}{reference_number}".format(
            description=description,
            price_point=contract.price_point.display_name,
            site=self.site_display_name,
            oil_admix=oil_admix,
            reference_number=reference_number
        )

    def can_user_view(self, user):
        return self.commodity_contract.is_creator(user) or not (self.commodity_contract.unresolved_confirm_request()
                                                                or self.commodity_contract.rejection_reason)

    @transaction.atomic
    def amend(self, data, user=None):
        from core.loads.models import Load
        new_buyer_data = data.pop('buyer', None)
        draft_invoices = False
        warehouse_invoices = False
        if 'variety_id' in data:
            new_variety_id = data.pop('variety_id')
            is_variety_changed = self.variety_id != new_variety_id  # pylint: disable=access-member-before-definition
            if is_variety_changed:
                self.variety_id = new_variety_id
                self.save()
                for load in self.load_set.filter():
                    load.variety_id = self.variety_id
                    load.updated_by = user or load.updated_by
                    load.save()
                draft_invoices = True
        if 'note' in data:
            self.update_note_data(data.pop('note'))

        if 'process_on' in data and (user and user.is_superuser):
            process_on = datetime.strptime(get(data, 'process_on'), "%Y-%m-%d")
            if process_on != self.process_on:
                self.process_on = process_on
                self.save()
                for load in self.load_set.filter():
                    load.date_time = process_on
                    load.updated_by = user or load.updated_by
                    load.save()

        if new_buyer_data:
            buyer = self.buyer
            for field in new_buyer_data:
                setattr(buyer, field, new_buyer_data[field])
            buyer.save()
            if new_buyer_data.get('ngr_id'):
                self.load_set.filter(type=Load.INLOAD).update(ngr_id=new_buyer_data.get('ngr_id'))
                warehouse_invoices = True
        title_transfer_amend.send(sender=TitleTransfer, instance=self, user=user or self.updated_by)
        self.post_process_triggers(draft_invoices, warehouse_invoices)

    @property
    def related_invoiced_items(self):
        title_transfer_type_id = self.content_type_ids_for(model_names=['titletransfer'])[0]
        invoice_item_class = apps.get_model('invoices', 'InvoiceItem')
        return self.qs2dict(invoice_item_class.objects.filter(
            item_id=self.id, item_type_id=title_transfer_type_id, invoice__status__in=INVOICE_ACCEPTED_STATUSES,
            is_rejected=False, invoice__type='Brokerage'
        ), one_to_one_relations=['invoice__payer__company'])

    @transition(field='status', source=['planned', 'invoiced', 'draft', 'completed', 'in_progress'], target='completed')
    def completed(self):
        pass

    def transition_parent_status(self, status):
        parent = self.commodity_contract
        status_method = getattr(parent, status)
        if can_proceed(status_method):
            status_method()
            parent.save()

    @transition(field='status', source=['completed', 'invoiced'], target='invoiced')
    def invoiced(self):
        pass

    def get_seller(self):
        return self.seller or get(self.commodity_contract, 'seller')

    def get_buyer(self):
        return self.buyer or get(self.commodity_contract, 'buyer')

    def process_load_by_load(self):
        if self.canola_load_ids:
            from core.loads.models import Load
            storage = self.storage
            seller = self.get_seller()
            buyer = self.get_buyer()
            seller_ngr_id = get(seller, 'ngr_id')
            buyer_ngr_id = get(buyer, 'ngr_id')
            for load_id, tonnage in self.canola_load_ids.items():
                load = Load.objects.filter(id=load_id).first()
                if load:
                    load_payload = {
                        'commodity_id': load.commodity_id,
                        'variety_id': load.variety_id,
                        'grade_id': load.grade_id,
                        'season': load.season,
                        'date_time': timezone.now() if self.is_today else self.process_on,
                        'source': 'title_transfer',
                        'docket_number': self.reference_number,
                        'specs': load.specs,
                        'title_transfer_id': self.id,
                        'estimated_net_weight': tonnage,
                        'created_by_id': self.updated_by_id,
                        'updated_by_id': self.updated_by_id,
                    }
                    storage.create_outload({**load_payload, 'ngr_id': seller_ngr_id, 'extras': {'canola_load_ids': {load.id: tonnage}}, 'throughput': self.throughput,}, True, self.created_by)  # pylint: disable=line-too-long
                    storage.create_inload({**load_payload, 'ngr_id': buyer_ngr_id, 'extras': {'canola_load_ids': {load.id: tonnage}}}, True, self.created_by)  # pylint: disable=line-too-long
                    if load.external_reference:
                        total_allocated_tonnage = load.total_allocated_title_transfer_tonnage
                        if total_allocated_tonnage < load.net_weight:
                            load.estimated_net_weight = total_allocated_tonnage
                            load.tare_weight = None
                            load.gross_weight = None
                            load.save()

    def process_as_single_load(self):
        storage = self.storage
        if storage:
            coil = float(self.coil) if self.coil else self.coil
            impu = float(self.impu) if self.impu else self.impu
            tonnage = self.inferred_tonnage
            load_payload = {
                'commodity_id': self.commodity_id,
                'variety_id': self.variety_id,
                'grade_id': self.grade_id,
                'season': self.season,
                'date_time': timezone.now() if self.is_today else self.process_on,
                'source': 'title_transfer',
                'docket_number': self.reference_number,
                'specs': {
                    'coil': coil,
                    'impu': impu
                },
                'title_transfer_id': self.id,
                'estimated_net_weight': tonnage,
                'created_by_id': self.updated_by_id,
                'updated_by_id': self.updated_by_id,
            }
            if get(self.commodity, 'is_strict_quantity_based'):
                load_payload.update({'quantity': tonnage})
            storage.create_outload({**load_payload, 'ngr_id': get(self.get_seller(), 'ngr_id'), 'throughput': self.throughput}, True, self.created_by) # pylint: disable=line-too-long
            storage.create_inload({**load_payload, 'ngr_id': get(self.get_buyer(), 'ngr_id')}, True, self.created_by)

    @transaction.atomic
    def process(self):
        if self.is_completed() or self.is_invoiced() or self.is_void():
            return
        if self.canola_load_ids:
            self.process_load_by_load()
        else:
            self.process_as_single_load()
        tonnage = self.inferred_tonnage

        self.completed()

        contract = self.commodity_contract
        self.save()
        self.audit(action='processed', user=self.created_by)
        site_display_name = self.site_display_name

        draft_invoices = False

        if contract:
            title_transfer_processed.send(
                sender=Contract,
                title_transfer=self,
                instance=contract,
                site_name=site_display_name,
                title_transfer_no=self.identifier,
                tonnage=tonnage,
                commodity_name=self.commodity.display_name,
                status_text='completed',
                contract_id=contract.id,
                acceptance_request_id=get(self.acceptance_requests.filter(type=CONFIRM_REQUEST_TYPE).last(), 'id', None)
            )
            contract.set_unaccounted_tonnage()
            contract.set_delivered_tonnage()

            contract.refresh_from_db()

            if contract.status in ['confirmed', 'open'] and can_proceed(contract.in_progress):
                contract.in_progress()
                contract.save()

            if can_proceed(contract.delivered):
                contract.delivered()
                contract.save()

            if can_proceed(contract.completed):
                contract.completed()
                contract.save()

            contract.send_notification_for_request(
                request_type=CONTRACT_ACCEPTANCE_REQUEST_TYPE_TITLE_TRANSFER,
                action=PROCESSED, user=self.created_by, data={'title_transfer_no': self.identifier,
                                                            'site_name': site_display_name,
                                                            'commodity': self.commodity.display_name,
                                                            'tonnage': self.inferred_tonnage}
            )
            draft_invoices = True

        transaction.on_commit(lambda: self.post_process_triggers(draft_invoices, True))

    def post_process_triggers(self, draft_invoices=True, warehouse_invoices=True):
        from ..jobs.models import Job
        if draft_invoices:
            job_params = {
                    'item_id': get(self, 'id'),
                    'item_type': TITLE_TRANSFER_MODEL,
                    'user_id': self.updated_by_id,
                }
            if not Job.objects.filter(status='pending', type='create_update_or_delete_draft_invoices',
                                      params=job_params).exists():
                Job.schedule_job_for_task(
                    'create_update_or_delete_draft_invoices',
                    params=job_params
                )
        if warehouse_invoices:
            if self.canola_load_ids:
                from core.loads.models import Load
                loads = Load.objects.filter(id__in=list(self.canola_load_ids.keys()))
            else:
                loads = self.load_set.filter()
            for load in loads:
                load.update_related_draft_warehouse_invoices()

    def cannot_void_title_transfer_reasons(self, user):
        reasons = []
        if self.is_invoiced():
            reasons.append(constants.CANNOT_VOID_INVOICED_TITLE_TRANSFER)
        elif self.is_void():
            reasons.append(constants.CANNOT_VOID_VOIDED_TITLE_TRANSFER)
        elif self.is_rejected():
            reasons.append(constants.CANNOT_VOID_REJECTED_TITLE_TRANSFER)
        elif get(self, 'commodity_contract.cash_price') and user.company_id != get(self, 'storage.farm.company_id'):
            reasons.append('Only Site Users can void cash Sales. Please contact the site to void this transaction.')
        elif get(self, 'storage.farm.stocks_management') and user.company_id != get(self, 'storage.farm.company_id'):
            reasons.append('Only Site Users can void this title transfer. '
                           'Please contact the site to void this transaction.')
        if get(self, 'site.externally_sync_source') and not self.is_planned():
            reasons.append(CANNOT_VOID_EXTERNALLY_SYNCED_SITE_TITLE_TRANSFER)

        from core.loads.models import OutloadComposition
        for load in self.inloads:
            ocs = OutloadComposition.objects.filter(inload_id=load.id)
            for oc in ocs:
                if get(oc.entity_for, 'canola_load_ids'):
                    reasons.append(LOAD_SELECTED_IN_LOAD_BY_LOAD_TRANSFER)

        return reasons

    def void_message(self, sender):
        return "<b>{}</b> has been voided by <b>{}</b> from company <b>{}</b>".format(
            self.identifier, sender.name, sender.company.business_name
        )

    def void_loads(self):
        combos = []

        for load in self.load_set.all():
            load.status = 'void'
            load.save()
            load.update_related_models_on_void()
            combo = {'storage': load.storage, **load.storage_level_combo}
            combos.append(combo) if combo not in combos else None
        for combo in combos:
            storage = combo.pop('storage')
            loads = storage.load_set.filter(**combo).filter(type='outload').exclude(status='void')
            load = loads.order_by('date_time', 'id').first()
            storage.recreate_storage_levels_from_loads(load.storage_level_combo if load else combo)

    @transition(
        field=status,
        source=['completed', 'planned', 'draft'],
        target='void'
    )
    def void(self, user, reason, communication_params=False, void_loads=True):
        if not communication_params:
            communication_params = {}
        from core.jobs.models import Job
        with transaction.atomic():
            acceptance_request = None
            if communication_params:
                communication_params.pop('acceptance_required', True)
                acceptance_request = TitleTransferAcceptanceRequest.create({
                    'type': VOID_STATUS,
                    'title_transfer_id': self.id,
                    'created_by_id': user.id,
                    'updated_by_id': user.id,
                    'rejection_reason': reason,
                    'resolved': True
                })
                TitleTransferCommunication.create(
                    communication_params, kwargs={
                        'title_transfer_id': self.id,
                        'request_id': acceptance_request.id,
                        'type': 'void',
                        'mail_status': bool(communication_params.get('subject'))
                    }
                )

            self.rejection_reason = reason
            self.void_loads() if void_loads else None

            optional_args = {}
            if get(acceptance_request, 'id'):
                optional_args['is_mail_log'] = False

            title_transfer_void.send(
                sender=TitleTransfer, instance=self, created_by_id=user.id,
                identifier=self.identifier, tonnage=self.tonnage, site_name=self.site_display_name,
                contract_id=self.commodity_contract_id, acceptance_request_id=get(acceptance_request, 'id'),
                **optional_args
            )
            self.updated_by = user
            if bool(communication_params.get('subject')):
                params = {'action_type': 'void'}
                if acceptance_request:
                    params['acceptance_request_id'] = acceptance_request.id
                self.audit('mail', user, **params)
                transaction.on_commit(
                    lambda: Job.schedule_job_for_task(
                        'send_title_transfer_void_mail', params={'id': self.id, 'reason': reason})
                )

    def revert_parent_status(self):
        if not self.commodity_contract.is_planned():
            self.transition_parent_status('confirm')
            self.transition_parent_status('open')
            self.transition_parent_status('in_progress')
            self.transition_parent_status('delivered')
            self.transition_parent_status('completed')

    def created_mail_subject(self, request_type):
        return self.__request_mail_subject(self.title_transfer_communication.filter(
            resolved=False, title_transfer_id=self.id, type=request_type).first(), request_type)

    def __request_mail_subject(self, communication, request_type):
        return deepgetattr(
            communication, 'subject'
        ) or self.get_default_email_subject(self.created_by, request_type)

    def creator_is_broker(self):
        if self.commodity_contract_id:
            if self.commodity_contract.seller.represented_by_id == self.created_by.company_id:
                return self.commodity_contract.seller.company.name, self.commodity_contract.seller.represented_by.name # pylint: disable=line-too-long
            elif self.commodity_contract.buyer.represented_by_id == self.created_by.company_id:
                return self.commodity_contract.buyer.company.name, self.commodity_contract.buyer.represented_by.name # pylint: disable=line-too-long
            else:
                return False, False
        else:
            return False, False

    def get_default_email_subject(self, user, request_type):
        identifier = self.identifier.upper()
        company, broker_company = self.creator_is_broker()
        if request_type == 'create':
            if broker_company:
                subject = constants.BROKER_CREATED_TITLE_TRANSFER.format(
                    broker_company=broker_company, company=company, identifier=identifier)
            else:
                subject = constants.DEFAULT_TITILE_TRANSFER_MAIL_SUBJECT.format(
                    company=user.company.name, identifier=identifier)
        else:
            if broker_company:
                subject = constants.BROKER_VOID_TITLE_TRANSFER.format(
                    broker_company=broker_company, company=company, identifier=identifier)
            else:
                subject = constants.VOID_TITILE_TRANSFER_MAIL_SUBJECT.format(
                    company=user.company.name, identifier=identifier)
        return subject

    def get_communication(self, action_type):
        request = self.acceptance_requests.filter(
            type=action_type
        ).select_related('communication').last()
        return get(request, 'communication')

    def get_recipients(self, request_type):
        recipient_communication = self.title_transfer_communication.filter(
            resolved=False, title_transfer_id=self.id, type=request_type)
        for comm in recipient_communication:
            recipients = comm.recipients.values() if isinstance(comm.recipients, dict) else []
            return compact(recipients)

    @classmethod
    def get_title_transfers(cls, contract_id, company_id, raw=False, title_transfer_id=None):
        filters = {'viewer_company_ids__contains': [company_id]}

        if title_transfer_id:
            filters['id'] = title_transfer_id
        if contract_id:
            filters['commodity_contract_id'] = contract_id

        queryset = cls.objects.filter(**filters)

        return queryset if raw else queryset.select_related(
            'storage__farm__company', 'grade', 'variety', 'commodity',
            'buyer__company', 'seller__company'
        )

    def queue_mail_and_process(self, can_send_creation_email = True):
        from core.jobs.models import Job
        if not self.is_draft() and self.persisted:
            if self.is_in_past:
                Job.schedule_job_for_task('process_title_transfer', self.id)
            if can_send_creation_email:
                params = {'action_type': 'confirm'}
                pending_request = self.acceptance_requests.filter().last()
                if pending_request:
                    params['acceptance_request_id'] = pending_request.id
                self.audit('mail', self.updated_by, **params)
                Job.schedule_job_for_task('send_title_transfer_created_mail', params={'id': self.id})

    def to_carry_item(self, contract):
        return contract.to_carry_item_for_dates(
            self.process_on,
            self.process_on,
            self.tonnage,
            self.identifier,
            self,
            {'title_transfer_id': self.id}
        )

    def get_invoice_for_commodity_contract(self, commodity_contract_id):
        if commodity_contract_id:
            invoice_item_class = apps.get_model('invoices', 'InvoiceItem')
            invoice_items = invoice_item_class.objects.filter(
                item_id=self.id,
                item_type__model=TITLE_TRANSFER_MODEL,
                invoice__status__in=INVOICE_ACCEPTED_STATUSES,
                is_rejected=False,
                invoice__type=COMMODITY_CONTRACT_INVOICE,
                invoice__raised_for_id=commodity_contract_id
            )
            if not invoice_items and self.canola_loads:
                load_ids = list(self.outloads.values_list('id', flat=True))
                invoice_items = invoice_item_class.objects.filter(
                    item_id__in=load_ids,
                    item_type__model=LOAD_MODEL,
                    invoice__status__in=INVOICE_ACCEPTED_STATUSES,
                    is_rejected=False,
                    invoice__type=COMMODITY_CONTRACT_INVOICE,
                    invoice__raised_for_id=commodity_contract_id
                )
            return get(invoice_items.first(), 'invoice')

    def send_mobile_push_notification(self, cash_price=None):
        seller_company = get(self.seller, 'company')
        buyer_company = get(self.buyer, 'company')
        seller_employee_ids = seller_company.get_company_admins().values_list('id', flat=True)
        buyer_employee_ids = buyer_company.get_company_admins().values_list('id', flat=True)
        if get(self.seller, 'contact_id'):
            seller_employee_ids = [*list(seller_employee_ids), self.seller.contact_id]
        if get(self.buyer, 'contact_id'):
            buyer_employee_ids = [*list(buyer_employee_ids), self.buyer.contact_id]
        employee_ids = [*list(seller_employee_ids), *list(buyer_employee_ids)]
        site = self.site_display_name
        site_company = get(self.storage, 'farm.company.name')
        if cash_price:
            title = CASHED_TT_MOBILE_NOTIFICATION_TITLE % (site_company, site)
            message_text = CASHED_TT_MOBILE_NOTIFICATION_TEXT % (
                self.identifier, site_company, site, get(self.grade, 'name'),
                self.tonnage, get(cash_price, 'price')
            )
            extra = {'ac_notification_id': self.id, 'ac_notification_type': 'loadCashed'}
        else:
            title = TT_MOBILE_NOTIFICATION_TITLE % (site_company, site)
            message_text = TT_MOBILE_NOTIFICATION_TEXT % (
                self.identifier, site_company, site, get(self.grade, 'name'), self.tonnage
            )
            extra = {'ac_notification_id': self.id, 'ac_notification_type': 'title_transfer_created'}
        args = {
            'notification_type': NOTIFICATION_TYPE_LOAD_CASHED_ID if cash_price else NOTIFICATION_TYPE_TT_ID,
            'message_txt': message_text,
            'ios_msg_title': title,
            'extra': extra
        }
        MobilePushNotification.notify_mobile_devices(list(employee_ids), args)

    #this is used only for mobile
    def send_notification(self, user, cash_price=None):
        site = self.site_display_name
        site_company = get(self.storage, 'farm.company.name')
        if cash_price:
            title = CASHED_TT_MOBILE_NOTIFICATION_TITLE % (site_company, site)
            message_text = CASHED_TT_MOBILE_NOTIFICATION_TEXT % (
                self.identifier, site_company, site, get(self.grade, 'name'), self.tonnage, get(cash_price, 'price'))
        else:
            title = TT_MOBILE_NOTIFICATION_TITLE % (site_company, site)
            message_text = TT_MOBILE_NOTIFICATION_TEXT % (
                self.identifier, site_company, site, get(self.grade, 'name'), self.tonnage)
        recipients = Employee.objects.filter(
            company_id__in=[self.seller.company_id, self.buyer.company_id]
        ).exclude(
            type_id=core.common.constants.DRIVER_TYPE_ID
        )
        func_args = {
            'sender': user,
            'verb': 'Delivered',
            'action_object': self,
            'entity': 'titletransfer',
            'app': 'title_transfer',
            'isMobileOnly': True,
            'description': message_text,
            'recipient': recipients,
            'ios_msg_title': title,
        }
        notify.send(**func_args)

    def is_buyer_delivery_site(self, storage, buyer):
        buyer_company_id = get(buyer, 'company_id')
        transfer_site_company_id = get(storage, 'farm.company_id')
        return buyer_company_id == transfer_site_company_id

    def get_shrinkage(self):
        storage = self.storage
        seller = self.seller
        transfer_date = (DateTimeUtil.timezone_datetime_to_utc(datetime.combine(self.process_on, time.min)) if
                         self.process_on else timezone.now())
        shrinkage = Shrinkage.get_shrinkage_for(
            site_id=get(storage, 'farm_id'),
            commodity_id=self.commodity_id,
            for_company_id=get(seller, 'company_id'),
            start_date__lte=transfer_date,
            end_date__gte=transfer_date
        )
        return get(shrinkage, 'percentage', 0)

    def get_receival_fees(self):
        storage = self.storage
        seller = self.seller
        receival_fee = WarehouseFees.get_fee_for(
            site=get(storage, 'farm'),
            commodity_id=self.commodity_id,
            warehouse_type='Inload Fees',
            party_company_id=get(seller, 'company_id'),
            season=self.season,
            tenure=self.get_tenure_for_title_transfer(),
            grade_id=self.grade_id
        )
        return get(receival_fee, 'per_unit', 0)

    @classmethod
    def create_from_contract_and_process(cls, contract, tz):
        title_transfer_params = {
            "identifier": generate_identifier('title_transfer'),
            "commodity_id": contract.commodity_id,
            "grade_id": contract.grade_id,
            "season": contract.season,
            "seller": contract.seller.to_kwargs(False),
            "buyer": contract.buyer.to_kwargs(False),
            "process_on": DateTimeUtil.localize(timezone.now(), tz).date(),
            "tonnage": contract.tonnage,
            "variety_id": contract.variety_id,
            "created_by_id": contract.updated_by_id,
            "updated_by_id": contract.updated_by_id,
            "communication": {'acceptance_required': False},
            "handler_id": get(contract, 'consignees[0].handler_id') or get(contract, 'consignors[0].handler_id')
        }
        return contract.handle_title_transfer_request(args=title_transfer_params, process=True)

    def recreate_commodity_contract_draft_invoices(self, old_contract_id):
        if self.is_completed() and old_contract_id != self.commodity_contract_id:
            if old_contract_id:
                from core.invoices.models import Invoice
                Invoice.delete_existing_draft(self, TITLE_TRANSFER_MODEL, old_contract_id)
            if self.commodity_contract_id:
                self.save()
                from core.jobs.models import Job
                job_params = {
                        'item_id': self.id,
                        'item_type': TITLE_TRANSFER_MODEL,
                        'user_id': self.updated_by_id,
                    }
                if not Job.objects.filter(status='pending', type='create_update_or_delete_draft_invoices',
                                          params=job_params).exists():
                    Job.schedule_job_for_task(
                        'create_update_or_delete_draft_invoices',
                        params=job_params
                    )

    def change_contract(self, to_contract_id, user, data):
        old_contract_id = self.commodity_contract_id  # pylint: disable=access-member-before-definition
        old_contract_identifier = get(self.commodity_contract, 'identifier')
        contract_ids_to_update = compact([old_contract_id, to_contract_id])

        self.commodity_contract_id = to_contract_id
        self.updated_by = user
        self.external_reference_number = get(data, 'external_reference_number')
        self.save()
        self.audit('assign', user=user, old_contract_identifier=old_contract_identifier)

        for contract in Contract.objects.filter(id__in=contract_ids_to_update):
            contract.update_tonnages_and_status()
        self.recreate_commodity_contract_draft_invoices(old_contract_id)

    def get_tenure_for_title_transfer(self):
        warehouse_company = get(self.storage, 'farm.company')
        if warehouse_company:
            return warehouse_company.get_tenure_for_warehouse_invoice(
                DateTimeUtil.convert_date_to_datetime(self.process_on).replace(tzinfo=None) if self.process_on else None
            )

    def send_create_email(self):
        from core.jobs.models import Job
        params = {
            'id': self.id,
        }
        Job.schedule_job_for_task(
            'send_title_transfer_created_mail',
            params=params
        )

    def send_void_email(self, reason):
        from core.jobs.models import Job
        params={
            'id': self.id,
            'reason': reason
        }
        Job.schedule_job_for_task(
            'send_title_transfer_void_mail',
            params=params
        )

    @property
    def last_email_action(self):
        pending_request = self.acceptance_requests.filter().last()

        has_last_pending_email = bool(
            pending_request and
            not get(pending_request, 'communication.mail_status')
        )
        return {
            'has_pending_email': has_last_pending_email,
            'action_type': pending_request.type.capitalize() if pending_request and pending_request.type else None
        }



class TitleTransferAcceptanceRequest(AcceptanceRequest):
    class Meta:
        db_table = 'title_transfer_acceptance_requests'

    title_transfer = models.ForeignKey(
        TitleTransfer,
        on_delete=models.CASCADE,
        null=False,
        blank=False,
        related_name='acceptance_requests'
    )

class TitleTransferCommunication(BaseCommunication):
    class Meta:
        db_table = 'title_transfer_communications'
    type = models.CharField(
        max_length=10,
        null=False,
        blank=False,
        choices=constants.TITLE_TRANSFER_COMMUNICATION_TYPES)

    title_transfer = models.ForeignKey(
        TitleTransfer,
        on_delete=models.CASCADE,
        related_name='title_transfer_communication')

    request = models.OneToOneField(
        'contracts.TitleTransferAcceptanceRequest',
        on_delete=models.CASCADE,
        related_name='communication',
        null=True,
        blank=True
    )


class ExcludedContracts(BaseModel):
    class Meta:
        db_table = 'excluded_contract'

    contract = models.ForeignKey(
        Contract,
        on_delete=models.CASCADE,
        related_name='excluded_contract'
    )
    type = models.CharField(
        max_length=255,
        null=False,
        blank=False,
    )


class ChemicalApplication(BaseModel):
    class Meta:
        db_table = 'chemical_applications'

    contract = models.ForeignKey(
        Contract, on_delete=models.CASCADE, related_name='chemical_applications',
        null=True, blank=True)
    order = models.ForeignKey(
        'freights.FreightOrder', on_delete=models.CASCADE, related_name='chemical_applications',
        null=True, blank=True)
    movement = models.ForeignKey(
        'freights.FreightContract', on_delete=models.CASCADE, related_name='chemical_applications',
        null=True, blank=True)
    load = models.ForeignKey(
        'loads.Load', on_delete=models.CASCADE, related_name='chemical_applications',
        null=True, blank=True)
    commodity = models.ForeignKey('commodities.Commodity', on_delete=models.DO_NOTHING)
    grade = models.ForeignKey('commodities.Grade', on_delete=models.DO_NOTHING, null=True, blank=True)
    price = models.FloatField(default=0, null=True, blank=True)
    fee = models.FloatField(default=0, null=True, blank=True)
    application_fee = models.FloatField(default=0, null=True, blank=True)

    FILLABLES = [
        'contract_id',
        'order_id',
        'movement_id',
        'load_id',
        'price',
        'fee',
        'created_by_id',
        'updated_by_id',
        'created_at',
        'updated_at',
        'commodity_id',
        'application_fee',
        'grade_id'
    ]

    @property
    def summary(self):
        transaction_entity = self.transaction_entity
        summary = get(self.commodity, 'display_name')
        if isinstance(transaction_entity, Contract):
            summary += f' - {transaction_entity.currency}{self.price}/{self.price_unit}'
            summary += f' - {transaction_entity.currency}{self.fee}/{self.transaction_entity.commodity.price_unit}'
        else:
            if self.application_fee:
                commodity_unit_abbr = get(UNIT_ABBREVIATIONS, self.price_unit)
                summary += (f" {self.application_fee} {commodity_unit_abbr}/"
                            f"{self.transaction_entity.commodity.price_unit}")
            if self.grade:
                summary += f" on {self.grade.name}"

        return summary

    @property
    def transaction_entity(self):
        return self.contract or self.order or self.movement

    @property
    def price_unit(self):
        return self.commodity.price_unit if self.commodity_id else None

    @property
    def price_display(self):
        return f"{self.transaction_entity.currency}{self.price}/{self.price_unit}" if self.price else ''

    @property
    def mixing_fee_display(self):
        transaction_entity = self.transaction_entity
        return f"{transaction_entity.currency}{self.fee}/{transaction_entity.commodity.price_unit}" if self.fee else ''

    def clean(self):
        if not self.contract_id and not self.order_id and not self.movement_id and not self.load_id:
            raise ValidationError('At least one of contract, order, movement or load is required')
        self.price = self.price or 0
        self.fee = self.fee or 0
        super().clean()

    @classmethod
    def persist_many(cls, params, **kwargs):
        params = params if isinstance(params, list) else [params]
        return [cls.persist(data, **kwargs) for data in compact(params)]

    @classmethod
    def persist(cls, params, **kwargs):
        application = cls(**params, **kwargs)
        application.save()
        return application
