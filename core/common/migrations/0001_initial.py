# Generated by Django 2.1.11 on 2020-02-07 10:56

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PDFQueue',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.IntegerField()),
                ('object_type', models.CharField(choices=[('contract', 'contract'), ('movement', 'movement'), ('order', 'order'), ('invoice', 'invoice')], max_length=255)),
            ],
            options={
                'db_table': 'pdf_queue',
            },
        ),
        migrations.AlterUniqueTogether(
            name='pdfqueue',
            unique_together={('object_id', 'object_type')},
        ),
    ]
