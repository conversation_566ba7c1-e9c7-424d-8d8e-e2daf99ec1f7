# Generated by Django 3.2.10 on 2022-01-17 04:57

from django.db import migrations
from bulk_update.helper import bulk_update


def add_season_to_site_management_settings_default_fields(apps, schema_editor):
    SiteManagementSettings = apps.get_model("company_sites", "SiteManagementSettings")
    settings = SiteManagementSettings.objects.all()
    for setting in settings:
        setting.fields.append({"show": False, "defaultValue": None, "mandatory": False, "label": "Season (Optional)", "name": "Season", "customisable": True, "id": "season"})
    bulk_update(settings, update_fields=['fields'], batch_size=500)


class Migration(migrations.Migration):
    dependencies = [
        ('company_sites', '0109_auto_20220113_1026'),
    ]

    operations = [
        migrations.RunPython(add_season_to_site_management_settings_default_fields)
    ]
