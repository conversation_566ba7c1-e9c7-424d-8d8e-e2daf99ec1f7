# Generated by Django 4.1.13 on 2024-01-02 09:58

from django.db import migrations
from core.countries.constants import AUSTRALIA_COUNTRY_ID, CANADA_COUNTRY_ID, NZ_COUNTRY_ID, USA_COUNTRY_ID


def update_currency_for_contract_based_on_country(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    Contract = apps.get_model('contracts', 'Contract')
    default_currency = {
        USA_COUNTRY_ID: 'US$',
        CANADA_COUNTRY_ID: 'CA$',
        NZ_COUNTRY_ID: 'NZ$',
    }
    for company in Company.objects.exclude(country_id=AUSTRALIA_COUNTRY_ID):
        Contract.objects.filter(created_by__company_id=
                                company.id).update(currency=default_currency.get(company.country_id))


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0196_contract_currency_historicalcontract_currency'),
    ]

    operations = [
        migrations.RunPython(update_currency_for_contract_based_on_country)
    ]
