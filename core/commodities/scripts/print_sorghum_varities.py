import csv
import json
import sys

csvfile = open(sys.argv[-1], "r")
fieldnames = ("Variety",)
reader = csv.DictReader(csvfile, fieldnames)
res = []
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _json = {
            "fields": {
                "created_at": "2018-04-02T08:00:00+00:00",
            },
        }
        _json["pk"] = i
        _json["model"] = "commodities.sorghumvariety"
        _json["fields"]["name"] = _dict["Variety"]
        res.append(_json)

print(json.dumps(res))
