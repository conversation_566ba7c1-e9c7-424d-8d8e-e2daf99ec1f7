# Generated by Django 4.0.3 on 2022-05-30 07:22

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('banks', '0012_alter_historicalbank_options_bank_bsb_number_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='bank',
            name='bsb_number',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=15), blank=True, db_index=True, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='bank',
            name='code',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.Char<PERSON>ield(max_length=10), db_index=True, size=None),
        ),
        migrations.AlterField(
            model_name='historicalbank',
            name='bsb_number',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.Char<PERSON><PERSON>(max_length=15), blank=True, db_index=True, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='historicalbank',
            name='code',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=10), db_index=True, size=None),
        ),
    ]
