# pylint: disable-all
import csv
import json as core_json
import unittest

from django.conf import settings
from django.core.management import call_command
from django.test.testcases import TestCase

import core.common.constants
from core.company_sites.models import CompanySite
from core.locations.models import Location
from core.marketzones.models import Marketzone
from core.regions.models import Region
from .models import Company


class BHCSeedTest(TestCase):
    @unittest.skip("Run for getting BHC company and locations seed")
    def test_seed(self):
        if settings.RUN_TEST_FIXTURES:
            call_command("loaddata", "core/fixtures/core_entities.yaml")
            call_command("loaddata", "core/fixtures/states.yaml")
            call_command("loaddata", "core/fixtures/marketzones.yaml")
            call_command("loaddata", "core/fixtures/regions.yaml")

        id = 10001
        l_id = 10001
        for _file in [
                'BHC.csv',
        ]:
            csvfile = open('/code/core/companies/fixtures/' + _file, "r")
            fieldnames = ("ABN","SiteName","SiteAddress","SiteLatitude","SiteLongitude","Phone","MarketZone","Region","State","Port","NTP","BHC","Mode","LD",)
            reader = csv.DictReader(csvfile, fieldnames)
            res = []
            data = list(reader)
            data = [dict(d) for d in data]
            data.pop(0)
            dict_res = {}

            for x in data:
                dict_res[x['ABN']] = dict_res.get(x['ABN'], None)
                if not dict_res[x['ABN']]:
                    x['Port'] = [x['Port']]
                    dict_res[x['ABN']] = [x]
                else:
                    existing_sites = list(filter(
                        lambda y: y['SiteName'] == x['SiteName'], dict_res[x['ABN']]
                    ))
                    if existing_sites:
                        existing_site = existing_sites[0]
                        existing_site['Port'].append(x['Port'])
                    else:
                        x['Port'] = [x['Port']]
                        dict_res[x['ABN']].append(x)

            for abn, sites in dict_res.items():
                for site in sites:
                    operator = site.get('BHC').strip()
                    lat = site.get("SiteLatitude", '').strip()
                    lng = site.get("SiteLongitude", '').strip()
                    address = site.get("SiteAddress", '').strip()
                    phone = site.get('Phone', '0444444444').strip()
                    market_zone_name = site.get('MarketZone', '').strip()

                    if market_zone_name == 'VIC Western Districts':
                        market_zone_name = 'VIC Western District'
                    if market_zone_name == 'SA Eyre Peninsula':
                        market_zone_name = 'SA Eyre Penisula'
                    if market_zone_name == 'SA Mount Gambier':
                        market_zone_name = 'SA Mount Gambia'
                    if market_zone_name == 'VIC Melbourne':
                        market_zone_name = 'VIC Melbourne Zone'

                    region_name = site.get('Region', '').strip()
                    site_name = site.get('SiteName', '').strip()
                    mode = site.get('Mode', '').strip()
                    ld = float(site.get('LD'))
                    tracks = site.get('Port', [])
                    market_zone = None
                    region = None
                    if abn:
                        company = Company.objects.filter(abn=abn).first()
                        if not company:
                            company_location = Location(
                                id=id,
                                name=operator,
                                address=address,
                                latitude=lat,
                                longitude=lng,
                                location_type='company'
                            )
                            company_location.save()
                            if not company_location.id:
                                print("Company location is invalid. Errors: ", company_location.errors)
                            company = Company(
                                id=id,
                                business_name=operator,
                                abn=abn,
                                address_id=company_location.id,
                                mobile=phone or '0444444444',
                                type_id=4,
                            )
                            company.save()

                            id+=1
                            if company.errors:
                                print("Company errors: ", company.errors)

                        if market_zone_name:
                            market_zone = Marketzone.objects.filter(name=market_zone_name).first()
                            if not market_zone:
                                print("Couldn't find MarketZone: ", market_zone_name)
                        if region_name:
                            region = Region.objects.filter(name=region_name).first()
                            if not region:
                                print("Couldn't find Region: ", region_name)

                        bhc_site_address = Location(
                            id=l_id,
                            name=site_name,
                            address=address or site_name,
                            latitude=lat,
                            longitude=lng,
                            location_type='companysite'
                        )
                        bhc_site_address.save()
                        if not bhc_site_address.id:
                            print("BHC site address is invalid. Errors: ", bhc_site_address.errors)

                        company = Company.objects.filter(abn=abn).first()
                        company_site = CompanySite(
                            id=l_id,
                            name=site_name,
                            tracks=tracks,
                            address_id=bhc_site_address.id,
                            marketzone=market_zone,
                            region=region,
                            company=company,
                            phone=phone,
                            ld=ld,
                            mode=mode,
                        )

                        company_site.save()
                        if company_site.errors:
                            print("BHCSite Errors: ", company_site.errors)
                        else:
                            print("BHCSiteId: "+str(company_site.id)+" CompanyId: "+str(company.id))
                            l_id+=1

        company_res = []
        company_locations_res = []
        for company in Company.objects.filter(type_id=4).all():
            json = {
                "model": "companies.company",
                "pk": company.id,
                "fields": {
                    "created_at": "2019-01-24T08:00:00+00:00",
                    "updated_at": "2019-01-24T08:00:00+00:00",
                    "business_name":company.business_name,
                    "entity_name":company.entity_name,
                    "abn":company.abn,
                    "type_id": 4,
                    "address_id": company.address_id,
                    "mobile": company.mobile,
                    "owner_company_id": core.common.constants.AU_SYSTEM_COMPANY_ID
                },
            }
            l_json = {
                "model": "locations.location",
                "pk": company.address_id,
                "fields": {
                    "created_at": "2018-01-24T08:00:00+00:00",
                    "updated_at": "2018-01-24T08:00:00+00:00",
                    "name": company.business_name,
                    "address": company.address.address,
                    "latitude": company.address.latitude,
                    "longitude": company.address.longitude,
                    "location_type": 'company',
                },
            }
            company_res.append(json)
            company_locations_res.append(l_json)
        bhc_site_res = []
        bhc_site_address_res = []
        for bhc_site in CompanySite.objects.filter(ld__isnull=False):
            json = {
                "model": "companysites.companysite",
                "pk": bhc_site.id,
                "fields": {
                    "created_at": "2018-01-24T08:00:00+00:00",
                    "updated_at": "2018-01-24T08:00:00+00:00",
                    "name": bhc_site.name,
                    "tracks": bhc_site.tracks,
                    "mode": bhc_site.mode,
                    "ld": bhc_site.ld,
                    "address_id": bhc_site.address_id,
                    "area": bhc_site.area,
                    "marketzone_id": bhc_site.marketzone_id,
                    "region_id": bhc_site.region_id,
                    "company_id": bhc_site.company_id,
                    "phone": bhc_site.phone,
                },
            }
            address_json = {
                "model": "locations.location",
                "pk": bhc_site.address_id,
                "fields": {
                    "created_at": "2018-01-24T08:00:00+00:00",
                    "updated_at": "2018-01-24T08:00:00+00:00",
                    "name": bhc_site.address.name,
                    "address": bhc_site.address.address,
                    "latitude": bhc_site.address.latitude,
                    "longitude": bhc_site.address.longitude,
                    "location_type": 'companysite',
                },
            }
            bhc_site_res.append(json)
            bhc_site_address_res.append(address_json)
        print(core_json.dumps(company_res))
        print("*********************************************")
        print(core_json.dumps(company_locations_res))
        print("*********************************************")
        print(core_json.dumps(bhc_site_res))
        print("*********************************************")
        print(core_json.dumps(bhc_site_address_res))

