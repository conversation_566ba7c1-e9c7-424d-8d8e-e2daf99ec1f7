# Generated by Django 4.1.13 on 2024-05-20 05:32

from django.db import migrations


def migrate_specs_to_array(apps, schema_editor):
    Commodity = apps.get_model('commodities', 'Commodity')
    Grade = apps.get_model('commodities', 'Grade')
    for commodity in Commodity.objects.filter():
        commodity.specs_array = commodity.specs or []
        commodity.save(update_fields=['specs_array'])

    for grade in Grade.objects.filter():
        grade.specs_array = grade.specs or []
        grade.save(update_fields=['specs_array'])


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0052_commodity_specs_array_grade_specs_array_and_more'),
    ]

    operations = [
        migrations.RunPython(migrate_specs_to_array),
    ]
