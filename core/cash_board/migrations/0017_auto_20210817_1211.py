# Generated by Django 2.2.20 on 2021-08-17 12:11

import core.cash_board.models
from django.db import migrations
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('cash_board', '0016_auto_20210817_1128'),
    ]

    operations = [
        migrations.AlterModelManagers(
            name='cashprices',
            managers=[
                ('active', core.cash_board.models.ActiveCashPricesManager()),
                ('archived', core.cash_board.models.ArchivedCashPricesManager()),
                ('objects', core.cash_board.models.DefaultCompanyManager()),
            ],
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='status',
            field=django_fsm.FSMField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('completed', 'Completed'), ('partial', 'Partially Fulfilled')], db_index=True, default='active', max_length=40),
        ),
        migrations.AlterField(
            model_name='historicalcashprices',
            name='status',
            field=django_fsm.FSMField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('completed', 'Completed'), ('partial', 'Partially Fulfilled')], db_index=True, default='active', max_length=40),
        ),
    ]
