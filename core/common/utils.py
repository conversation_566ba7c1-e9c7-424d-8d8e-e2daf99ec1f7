import base64
import csv
import gc
import os
import random
import re
import string
import subprocess
import tempfile
import time as core_time
import uuid
from builtins import float
from datetime import datetime, date, time
from email.mime.image import MIMEImage
from functools import reduce
from threading import local
from urllib import parse
from zipfile import ZipFile
from types import SimpleNamespace

import inflect
import inflection
import requests
from django.apps import apps
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.contrib.humanize.templatetags.humanize import intcomma
from django.contrib.staticfiles import finders
from django.core.mail import EmailMessage
from django.db.models import DateField, DateTimeField, Q
from django.db.models import Max
from django.http import QueryDict
from django.utils import timezone
from pydash import compact, get, flatten as pydash_flatten

# import core.common.constants
import core.countries.models
from core.settings import CAMELIZE_FIELD_EXCEPTIONS, EXCLUDED_KEYS_FOR_CASE_TOGGLE
from core.commodities.constants import UNKNOWN

thread_locals = local()


def is_qa():
    """
    Returns boolean specifying if the current environment is qa or not
    """
    return settings.ENV == core.common.constants.QA_ENVIRONMENT


def is_staging():
    """
    Returns boolean specifying if the current environment is staging or not
    """
    return settings.ENV == core.common.constants.STAGING_ENVIRONMENT


def is_production():
    """
    Returns boolean specifying if the current environment is production or not
    """
    return settings.ENV == core.common.constants.PRODUCTION_ENVIRONMENT


def is_dev_or_ci():
    """
    Returns boolean specifying if the current environment is production or not
    """
    return not settings.ENV or settings.ENV in ['ci', 'dev']


def get_business_name_filter(business_name):
    business_name = business_name[0] if business_name and isinstance(business_name, list) else business_name
    return {'business_name__iexact': business_name } if business_name else {}

def convert_image_to_mime_type(image_name=None, mime_name=None):
    """
    Converts any image from agrument if any to the Mime type else convert AgriChain Logo
    :return: Mime type image
    """
    if image_name and mime_name:
        with open(finders.find(image_name), 'rb') as _file:
            image_data = _file.read()
        image = MIMEImage(image_data)
        image.add_header('Content-ID', mime_name)
    else:
        with open(finders.find('agrichain-logo.png'), 'rb') as _file:
            image_data = _file.read()
        image = MIMEImage(image_data)
        image.add_header('Content-ID', '<logo>')
    return image


def get_model_class_from_name(name):
    """
    Returns class of the mentioned model name by querying the django-content-type table
    :param name:
    :return: Class
    """
    return ContentType.objects.get(model=name.lower()).model_class()


def remove_duplicate_elements_by_key(elements, key):
    """
    Returns a List of dictionaries after removing those dictionaries that have same value for a key
    ex:
    Input:  elements=[{'a': 100, 'b': 200}, {'a': 2, 'b': 500}, {'a': 100, 'b': 300}], key='a'
    Output: [{'a': 100, 'b': 200}, {'a': 2, 'b': 500}]
    :param elements:
    :param key:
    :return: list of dictionaries
    """
    occured_elements = []
    unique_elements = []
    for element in elements:
        if element[key] not in occured_elements:
            unique_elements.append(element)
            occured_elements.append(element[key])
    return unique_elements

def chunked_queryset_iterator(queryset, chunk_size=None):
    """
    Returns the iterator on the given queryset.
    The given queryset doesn't load all the rows in the memory at once. It loads the rows of the given chunk size
    at once
    default chunk size = 50
    :param queryset:
    :param chunk_size:
    """
    current_id = 0
    largest_id = queryset.aggregate(max_id=Max('id'))['max_id'] or 0
    chunk_size = chunk_size or core.common.constants.CHUNK_SIZE

    queryset = queryset.order_by('id')
    while current_id < largest_id:
        for row in queryset.filter(pk__gt=current_id)[:chunk_size]:
            current_id = row.pk
            yield row
        gc.collect()


def update_with_last_modified_time(queryset, **kwargs):
    """
    This function adds any auto_now field to the update call because QuerySet.update() doesn't do it
    :param queryset:
    :param kwargs:
    :return:
    """
    model_fields = queryset.model._meta.get_fields()
    fields_and_value_map = {}
    for field in model_fields:
        try:
            auto_now = getattr(field, 'auto_now')
        except AttributeError:
            auto_now = False

        if auto_now:
            if isinstance(field, DateField):
                fields_and_value_map[field.name] = date.today()
            elif isinstance(field, DateTimeField):
                fields_and_value_map[field.name] = timezone.now()

    fields_and_value_map.update(kwargs)
    return queryset.update(**fields_and_value_map)


def set_current_user(func):
    """
    This function sets the current user in the thread
    example: lambda self: user
    :param func: lambda func with user as self
    """
    setattr(thread_locals, core.common.constants.CURRENT_USER, func.__get__(func, local))  # pylint: disable=unnecessary-dunder-call


def get_current_user():
    """
    This function get the current user that is already set in the thread
    :return: current user if set in the thread else None
    """
    current_user = getattr(thread_locals, core.common.constants.CURRENT_USER, None)
    if callable(current_user):
        current_user = current_user()  # pylint: disable=not-callable

    return current_user


def set_request_url(func):
    setattr(thread_locals, core.common.constants.REQUEST_URL, func.__get__(func, local))  # pylint: disable=unnecessary-dunder-call


def set_request_tz(func):
    setattr(thread_locals, core.common.constants.REQUEST_TZ, func.__get__(func, local))  # pylint: disable=unnecessary-dunder-call


def set_request_country(func):
    setattr(thread_locals, core.common.constants.REQUEST_COUNTRY, func.__get__(func, local))  # pylint: disable=unnecessary-dunder-call


def set_request_unit(func):
    setattr(thread_locals, core.common.constants.REQUEST_UNIT, func.__get__(func, local))  # pylint: disable=unnecessary-dunder-call


def get_request_url():
    request_url = getattr(thread_locals, core.common.constants.REQUEST_URL, None)
    if callable(request_url):
        request_url = request_url()  # pylint: disable=not-callable

    return request_url


def get_request_tz():
    request_tz = getattr(thread_locals, core.common.constants.REQUEST_TZ, None)
    if callable(request_tz):
        request_tz = request_tz()  # pylint: disable=not-callable

    return request_tz


def get_request_unit():
    request_unit = getattr(thread_locals, core.common.constants.REQUEST_UNIT, None)
    if callable(request_unit):
        request_unit = request_unit()  # pylint: disable=not-callable

    return request_unit or get(get_current_user(), 'unit')


def get_request_country_code():
    request_country = getattr(thread_locals, core.common.constants.REQUEST_COUNTRY, None)
    if callable(request_country):
        request_country = request_country()  # pylint: disable=not-callable

    return request_country


def get_request_country_id(country_code=None):
    return core.countries.models.Country.to_id_by_code(country_code or get_request_country_code())


def exclude_country_hidden_fields_in_csv(country, headers):
    csv_headers = []
    hidden_fields = country.config.get('hidden_fields', [])
    show_target_moisture = country.config.get('show_target_moisture', False)
    if not show_target_moisture:
        hidden_fields.append('net_weight_with_shrinkage')
    units = country.config.get('units', [])
    for key in headers:
        key_field = key.lower().replace(" ", "_")
        if (
                (core.common.constants.BALES in key or
                 core.common.constants.MODULES in key or
                 core.common.constants.METER_CUBE in key or
                 core.common.constants.KG in key or core.common.constants.LITRE in key) and (
                core.common.constants.BALES not in units or
                core.common.constants.MODULES not in units or
                core.common.constants.METER_CUBE not in units or
                core.common.constants.KG not in units or core.common.constants.LITRE not in units
        )):
            continue
        if not any(hidden_field in key_field for hidden_field in hidden_fields):
            csv_headers.append(key)
    return csv_headers


def clean_string(key):
    if key:
        text = re.sub(r'\s+', ' ', key.strip())
        return re.sub(r'\s*/\s*', '/', text)
    return key


def camelize(data):
    """
    This function get the dictionary or a list of dictionaries and convert the keys to camelCase
    ex:
    Input --> {
        foo_bar: 100,
        bar_foo: {
            foo_foo: 10,
            bar_bar: 20
        }
    }
    Output --> {
        fooBar: 100,
        barFoo: {
            fooFoo: 10,
            barBar: 20
        }
    }
    :param data:
    :return: dictionary or list of dictionaries having camelCase Keys
    """
    def _camelize(data_dict):
        if not isinstance(data_dict, (dict, QueryDict)):
            return data_dict

        toggled_dict = {}
        for key, value in data_dict.items():
            if isinstance(value, dict) and key not in EXCLUDED_KEYS_FOR_CASE_TOGGLE:
                data_dict[key] = _camelize(value)
            elif isinstance(value, list) and bool(value) and isinstance(value[0], dict):
                data_dict[key] = list(map(_camelize, value))
            if key not in CAMELIZE_FIELD_EXCEPTIONS:
                toggled_dict[inflection.camelize(key, False)] = data_dict[key]
            else:
                toggled_dict[key] = data_dict[key]

        return toggled_dict

    if isinstance(data, list):
        return list(map(_camelize, data))

    return _camelize(data)


def snakeize(data):
    """
    This function get the dictionary or a list of dictionaries and convert the keys to snake_case
    ex:
    Input --> {
        fooBar: 100,
        barFoo: {
            fooFoo: 10,
            barBar: 20
        }
    }
    Output --> {
        foo_bar: 100,
        bar_foo: {
            foo_foo: 10,
            bar_bar: 20
        }
    }
    :param data:
    :return: dictionary or list of dictionaries having snake_case Keys
    """
    def _snakeize(data_dict):
        toggled_dict = {}

        for key, value in data_dict.items():
            if isinstance(value, dict):
                data_dict[key] = lowercase(value) if key in EXCLUDED_KEYS_FOR_CASE_TOGGLE else _snakeize(value)
            elif isinstance(value, list) and all(_v.__class__.__name__ not in ['list', 'dict'] for _v in value):
                data_dict[key] = value
            elif isinstance(value, list):
                data_dict[key] = snakeize(value)
            toggled_dict[inflection.underscore(key)] = data_dict[key]

        return toggled_dict

    if isinstance(data, list):
        return list(map(_snakeize, data))

    return _snakeize(data)


def _camelize(value, uppercase_first_letter=False):
    """
    This function converts a string to camelCase
    :param value: string to be converted
    :return: camelCase string
    """
    return inflection.camelize(value, uppercase_first_letter=uppercase_first_letter) if value else value


def lowercase(data_dict):
    """
    This function get the dictionary convert the keys to lowercase
    ex:
    Input --> {
        fooBar: 100,
        barFoo: 200
    }
    Output --> {
        foobar: 100,
        barfoo: 100
    }
    :param data:
    :return: dictionary having lowercase Keys
    """
    return {k.lower(): v for k, v in data_dict.items()}


def replace_nullable_int_with_null(data, fields):
    """
    This function replaces the given fields in the data with None, if the fields have null values
    :param data:
    :param fields:
    :return: Data with None as values where needed
    """
    for field in fields:
        if field in data and not data[field]:
            data[field] = None
    return data


def to_int_list(_list):
    new_list = []
    for item in _list:
        try:
            new_list.append(int(item))
        except:  # pylint: disable=bare-except
            continue
    return new_list


def invert_dict(data_dict):
    """
    This functions returns a dict having keys as values and values as keys
    ex:
    Input --> {test: 100, foo: 200, bar: 300}
    Output --> {100: test, 200: foo, 300: bar}
    :param data_dict:
    :return:
    """
    return {value: key for key, value in data_dict.items()}


def to_display_attr(options, value):
    """
    This function returns the display name of a choice field from the choices and given field
    :param options:
    :param value:
    :return: Display Name of the Field
    """
    values = [option[1] for option in options if option[0] == value]
    return values[0] if values else None


def to_query_filters(request_params):
    """
    This function converts the given request params to appropriate filter arguments for Django queryset
    :param request_params:
    :return: dictionary containing appropriate filter args
    """
    if request_params in [[], '', {}, None]:
        return request_params

    filter_params = {}

    for key, value in request_params.items():
        if '__gt' in key or '__lt' in key:
            if isinstance(value, list):
                filter_params[key] = value[0]
            else:
                filter_params[key] = value
        elif '__isnull' in key:
            filter_params[key] = bool(value)
        elif isinstance(value, list):
            if len(value) == 1 and isinstance(value[0], str) and ',' in value[0]:
                value = value[0].split(',')
            filter_params[key + '__in'] = value
        else:
            filter_params[key] = value

    return filter_params


def to_fsm_field_filters(key, values):
    """
    This function makes the Q filter arguments from the given key and value to be used for Finite State Machine
    :param key:
    :param values:
    :return:
    """
    if values in [[], '', {}, None]:
        return None

    or_condition = Q(_connector=Q.OR)

    for value in values:
        or_condition = or_condition.add(Q(**{key: value}), Q.OR)

    return or_condition


def deepgetattr(obj, attr):
    """
    This function gets the nested values of attr from the given obj
    :param obj:
    :param attr:
    :return: The nested value from the obj if it exists, None otherwise
    """
    try:
        return reduce(getattr, attr.split('.'), obj)
    except:  # pylint: disable=bare-except
        return None


def reduce_to(attr, items, initial_value=0, default_value=0):
    """
    This function adds given attr from all the items with given initial value.
    :param attr:
    :param items:
    :param initial_value:
    :param default_value: Value to be used if the attr is not found in some item
    :return: Returns the sum of all the given attr of the items
    """
    return reduce(
        lambda carry, item: carry + (getattr(item, attr) if hasattr(item, attr) else item.get(attr, default_value)),
        items, initial_value
    )


def generate_random_password(length=None):
    """
    This function generates a random password of the given length
    :param length: Default: 8
    :return:
    """
    length = length or core.common.constants.DEFAULT_PASSWORD_LENGTH
    password = ''.join(random.SystemRandom().choices(string.ascii_letters + string.digits, k=length-4))
    password = 'Aa' + password + '$7'
    return password


def flatten(data_list, unique=False):
    """
    This function returns a flatten list from the given nested list
    ex:
    Input --> [1, 2, [3, 4], 5, [6]]
    Output --> [1, 2, 3, 4, 5, 6]
    :param data_list:
    :param unique:
    :return: Flattened list from the nested list
    """
    result = []
    for data in data_list:
        result.extend(flatten(data)) if isinstance(data, list) else result.append(data)  # pylint: disable=expression-not-assigned
    return list(set(result)) if unique else result


def get_unique_object_list(object_list):
    """
    This function returns list of objects that are unique on the basis of the id
    ex:
    Input -> [{id: 1, name: "foo"}, {id: 2, name: "bar"}, {id: 1, nme: "foo"}]
    Output --> [{id: 1, name: "foo"}, {id: 2, name: "bar"}]
    :param object_list:
    :return:
    """
    id_to_obj_dict = {obj.id: obj for obj in object_list}
    return id_to_obj_dict.values()


def get_unique_list_of_objects(object_list):
    object_list = compact(object_list)
    if not object_list:
        return object_list
    unique_elements = set()
    all_keys = object_list[0].keys()
    for item in object_list:
        # Combine all keys as a tuple to identify uniqueness
        unique_key = tuple(item[key] for key in all_keys)
        unique_elements.add(unique_key)

    return [dict(zip(all_keys, key)) for key in unique_elements]


def get_grade_name(obj, all_grades=False):
    """
    This function returns the grade name for the object based on it's variety
    :param obj, all_grades:
    :return:
    """
    if get(obj, 'is_blended'):
        return get(obj, 'blended_grades_label')

    spread_details = get(obj, 'spread.details', [])
    if spread_details and all_grades and obj.type_id not in [1, 9]:
        grade_names = [
            get(detail, 'name') or get(detail, 'grade_name')
            for detail in spread_details
            if get(detail, 'name') or get(detail, 'grade_name')
        ]
        if grade_names:
            return ', '.join(grade_names)

    grade = getattr(obj, 'grade', None) or getattr(obj, 'planned_grade', None)
    grade_name = get(grade, 'name')

    if grade_name in ['BAR1', 'BAR2', 'BAR3'] and obj.season in ['16/17', '17/18', '18/19']:
        grade_name = grade_name.replace('BAR', 'F')

    return '-' if grade_name == UNKNOWN else grade_name


# todo: need to merge with get_grade_name
def get_grade_display_name(commodity_id, variety_id, grade_id, season=None, legacy=False):
    """
    This function returns the grade name representation based on commodity and variety
    """
    from core.commodities.models import Grade
    grade = Grade.objects.filter(id=grade_id).first()
    grade_name = None
    if legacy and grade and commodity_id == core.common.constants.BARLEY_COMMODITY_ID and variety_id:
        grade_name = grade.name.lower()
        variety_grade_name_key_repr = grade_name[:-1] + '_' + grade_name[-1]
        from core.commodities.models import Variety
        variety = Variety.objects.filter(id=variety_id).first()
        grade_name = variety and variety.details.get(variety_grade_name_key_repr, grade.name)
    elif grade:
        grade_name = grade.name

    if grade_name in ['BAR1', 'BAR2', 'BAR3'] and season in ['16/17', '17/18', '18/19']:
        grade_name = grade_name.replace('BAR', 'F')

    return grade_name


def assign_key_contact(employees, key_contact_id):
    """
    This function marks true for field key_contact if that employee is the given key contact
    :param employees:
    :param key_contact_id:
    :return: Returns updated list of employees
    """
    for employee in employees:
        employee['key_contact'] = employee['id'] == key_contact_id

    return employees


def get_base64_content(url, datatype=None):
    """
    This function returns base64 content from the given url
    :param url:
    :param datatype:
    :return: base64 content
    """
    try:
        content = base64.b64encode(requests.get(url, timeout=60).content)
    except:  # pylint: disable=bare-except
        content = None
    else:
        datatype = datatype or core.common.constants.DEFAULT_BASE64_DATATYPE
        datatype += "base64,"
        content = datatype + content.decode('utf-8')

    return content


def get_headers_from_meta_request(meta_request):
    """
    This function returns headers in standard format from request.META
    example:
    Input --> {'HTTP_XYZ': 'test_header1', 'HTTP_ABC_DEF': 'test_header2', 'TEST': 'test_meta'}
    Output --> {'Xyz': 'test_header1', 'Abc-Def': 'test_header2'}
    :param meta_request:
    :return: standard headers
    """
    return {
        key.replace('HTTP_', '').title().replace('_', '-'): value for key, value in meta_request.items()
        if key.startswith('HTTP')
    }


def get_essential_headers_from_meta_request(meta_request):
    """
    This function returns headers in standard format from request.META
    example:
    Input --> {'HTTP_XYZ': 'test_header1', 'HTTP_ABC_DEF': 'test_header2', 'TEST': 'test_meta'}
    Output --> {'Xyz': 'test_header1', 'Abc-Def': 'test_header2'}
    :param meta_request:
    :return: standard headers
    """
    return get_headers_from_meta_request(
        {k: v for k, v in meta_request.items() if k not in [
            'HTTP_AUTHORIZATION', 'HTTP_COOKIE', 'HTTP_ACCEPT_ENCODING', 'HTTP_HOST', 'HTTP_CONNECTION',
            'HTTP_ACCEPT_LANGUAGE', 'HTTP_ACCEPT', 'HTTP_CACHE_CONTROL', 'HTTP_X_CSRFTOKEN',
            'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED_PROTO', 'HTTP_X_FORWARDED_HOST', 'HTTP_X_FORWARDED_PORT',
        ]}
    )


def abstract_class_without_fields(cls, *exclude):
    """
    This function removes unwanted fields from abstract base classes.
    Usage:
        class NotUpdatableModel(without(BaseModel, 'updated_at', 'updated_by')):
            pass
    """
    if cls._meta.abstract:
        remove_fields = [f for f in cls._meta.local_fields if f.name in exclude]
        for f in remove_fields:
            cls._meta.local_fields.remove(f)
        return cls
    else:
        raise Exception("Not an abstract model")


def remove_pagination_query_params(query_param_dict):
    """
    This function removes pagination query params from dict. This can be used in methods like: for_company, for_farm,
    etc.
    :param query_param_dict:
    """
    if isinstance(query_param_dict, dict):
        query_param_dict.pop(core.common.constants.PAGE_QUERY_PARAM, None)
        query_param_dict.pop(core.common.constants.PAGE_SIZE_QUERY_PARAM, None)


def get_content_type_id(model):
    """
    This function returns the content type id for the given model if that model exists, returns None otherwise
    :param model:
    :return: content type id
    """
    model = model.lower().replace(' ', '')
    return ContentType.objects.filter(model=model).values_list('id', flat=True).first()


def get_full_function_name(func):
    """
    Returns the name of the function with app name prefixed
    :param func:
    :return: string: full name of the given function
    """
    return func.__module__ + '.' + func.__name__


def convert_to_primitive_in_json(json):
    """
    This function makes changes in the incoming json and replaces datetime, date, time, float objects to string
    :param json:
    """
    if isinstance(json, dict):
        for key, value in json.items():
            if isinstance(value, (datetime, date, time)):
                json[key] = str(value)
            elif isinstance(value, dict):
                convert_to_primitive_in_json(value)
    elif isinstance(json, list):
        for obj in json:
            convert_to_primitive_in_json(obj)


def get_changeset(current, last_version):
    """
    This function returns diff of last_version
    :param current:dict
    :param last_version:dict
    :return: dict: diff of two versions with key, value
    """
    changeset = {}

    if current and last_version:
        changeset = {k: v for k, v in current.items() if v != last_version.get(k, None)}

    return changeset

def send_mail_with_pdf_attachment(
        html, recipients, subject, pdf_name, pdf, sender, alert_recipients=None
):
    if recipients := compact(recipients):
        cc_recipients = [sender.email]
        if alert_recipients:
            cc_recipients.extend(alert_recipients)
        mail = EmailMessage(
            subject=subject,
            body=html,
            from_email=core.common.constants.FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME,
            to=recipients,
            reply_to=[sender.email or core.common.constants.SUPPORT_EMAIL],
            cc=compact(cc_recipients),
        )
        mail.content_subtype = "html"
        mail.attach(pdf_name, pdf, 'application/pdf')
        mail.attach(convert_image_to_mime_type())
        return mail.send()


def get_filename_with_timestamp(prefix='', extension='csv'):
    return prefix + str(core_time.time()).replace('.', '') + '.' + extension


def get_attachment_header_value(filename):
    return 'attachment; filename="{filename}"'.format(filename=filename)


def multiply_args(*args):
    return float("%0.2f" % (reduce((lambda x, y: float(x) * float(y)), args)))


def add_args(*args):
    return float("%0.2f" % (reduce((lambda x, y: float(x) + float(y)), args)))


def calculate_font_size(name, width, min_font=12, max_font=28, padding=0.1):
    """
    Calculate a font size that fits `name` inside a rectangle of width x height.
    `padding` (0-1) reserves some space for margins.
    """
    name_length = len(name) if name else 1

    effective_width = width * (1 - padding * 2)
    font_size = effective_width / (0.35 * name_length)

    font_size = max(min_font, min(max_font, font_size))
    return int(font_size)


def generate_identifier(_type, requesting_country=None):  # pylint: disable=too-many-branches
    now = datetime.today().utcnow()
    generated_uuid = str(uuid.uuid4())[0:7]
    _date = now.strftime('%y%m%d')
    prefix = ''

    if _type == 'sales_confirmation':
        prefix = core.common.constants.SALES_CONFIRMATION_PREFIX
    elif _type == 'broker_note':
        prefix = core.common.constants.BROKER_NOTE_PREFIX
    elif _type == 'contract':
        prefix = core.common.constants.CONTRACT_PREFIX
    elif _type == 'order':
        prefix = core.common.constants.FREIGHT_ORDER_PREFIX
    elif _type == 'pickup_order':
        prefix = core.common.constants.PICKUP_ORDER_PREFIX
    elif _type == 'delivery_order':
        prefix = core.common.constants.DELIVERY_ORDER_PREFIX
    elif _type == 'grain_order':
        prefix = core.common.constants.GRAIN_ORDER_PREFIX
    elif _type == 'freight_movement':
        prefix = core.common.constants.FREIGHT_MOVEMENT_PREFIX
    elif _type == 'title_transfer':
        prefix = core.common.constants.TITLE_TRANSFER_PREFIX
    elif _type == 'invoice':
        if identifier_length := get(requesting_country, 'config.invoicing.identifier_length'):
            generated_uuid = str(uuid.uuid4())[0:identifier_length - 7]
        prefix = core.common.constants.INVOICE_PREFIX
    elif _type == 'load':
        prefix = core.common.constants.LOAD_PREFIX
    elif _type == 'regrade_load':
        prefix = core.common.constants.REGRADE_LOAD_PREFIX
    elif _type == 'silo_to_silo_transfer_load':
        prefix = core.common.constants.SILO_TO_SILO_TRANSFER_PREFIX
    elif _type == 'stock_swap':
        prefix = core.common.constants.STOCK_SWAP_PREFIX

    identifier = prefix + _date + generated_uuid
    identifier = identifier.upper()
    return identifier


def to_filter_clause(params, prefix=None):
    if prefix:
        prefix += '__'
    else:
        prefix = ''
    __params = {}

    for k, v in params.items():
        if v:
            __params[prefix + k] = v
        else:
            __params[prefix + k + '__isnull'] = True

    return __params


def format_number(n):
    return float('%0.2f' % n)


def to_fixed_2_digit(n):  # returns str
    _n = n
    if isinstance(n, str):
        dehumanize = ',' in n
        if dehumanize:
            _n = _n.replace(',', '')
        _n = float(_n)
    return '{:20,.2f}'.format(_n).replace(' ', '')


def to_currency(n, round_off=True, currency=None):
    currency = currency or core.countries.models.Country.get_requesting_country().currency
    if not n:
        return f'{currency} 0.00'
    prefix = currency
    _n = n
    if isinstance(n, str):
        _n = float(n.replace(',', ''))
    if _n < 0:
        _n = abs(_n)
        prefix = f'-{currency}'
    if round_off:
        _n = to_fixed_2_digit(intcomma(_n)) if _n else '0.00'
    return f'{prefix} {_n}'


def to_csv_currency(n, round_off=True):
    if not n:
        return '$0.00'
    prefix = '$'
    _n = n
    if isinstance(n, str):
        _n = float(n.replace(',', ''))
    if _n < 0:
        _n = abs(_n)
        prefix = '-$'
    if round_off:
        _n = to_fixed_2_digit(intcomma(_n)) if _n else '0.00'
    return f'{prefix}{_n}'


def is_super_admin(company_id):
    try:
        return company_id and int(company_id) in core.common.constants.SYSTEM_COMPANY_IDS
    except:  # pylint: disable=bare-except
        return False


def get_stock_owners(loads):
    # may include owners who used to have stock but now its empty
    from core.companies.models import Company
    result = {}
    company_ids = set(compact(pydash_flatten(loads.values_list('ngr__owner_company_ids', flat=True))))
    company_klass = Company
    company_klass.assign_system_manager()
    company_combos = Company.objects.filter(id__in=company_ids).values('id', 'business_name')
    for combo in company_combos:
        company_id = combo['id']
        name = core.common.constants.UNKNOWN if is_super_admin(company_id) else combo['business_name']
        result[f"{company_id}"] = {'commodities': {}, 'info': {'id': f"{company_id}", 'name': name}}
    return result


def to_commodities_groups(loads):  # pylint: disable=too-many-locals
    result = {'all': {'info': {}, 'commodities': {}}}
    commodity_ids = set(loads.values_list('commodity_id', flat=True))
    for commodity_id in commodity_ids:
        if str(commodity_id) not in result['all']['commodities']:
            from core.commodities.models import Commodity
            commodity = Commodity.objects.get(id=commodity_id)
            commodity_info = {'id': commodity.id, 'name': commodity.name, 'display_name': commodity.display_name}
            result['all']['commodities'][str(commodity_id)] = {'info': commodity_info, 'tonnage': None}
    return result


def to_commodities_grouped_by_storage(loads):
    from core.farms.models import Storage
    result = {}
    for combo in list(set(list(loads.values_list('storage_id', 'commodity_id')))):
        storage_id = str(combo[0])
        commodity_id = combo[1]
        if storage_id not in result:
            storage = Storage.objects.filter(id=storage_id).first()
            result[storage_id] = {'commodities': set(), 'info': {'id': storage_id, 'name': storage.name}}
        result[storage_id]['commodities'].add(commodity_id)
    return result


def is_hay_commodity(commodity_id):
    from core.commodities.models import Commodity
    return Commodity.is_bales_commodity(commodity_id)


def is_meter_cube_commodity(commodity_id):
    from core.commodities.models import Commodity
    return Commodity.is_meter_cube_commodity(commodity_id)


def get_klass(entity):
    _engine = inflect.engine()

    if entity == 'freightorder':
        _klass = apps.get_model('freights', 'FreightOrder')
    elif entity == 'freightcontract':
        _klass = apps.get_model('freights', 'FreightContract')
    elif entity == 'title_transfer':
        _klass = apps.get_model('contracts', 'TitleTransfer')
    else:
        _klass_name = inflection.camelize(entity)
        _klass = apps.get_model(_engine.plural(entity), _klass_name)

    return _klass


def is_continuous_list_of_numbers(number_list):
    if not number_list:
        return False
    try:
        return sorted(number_list) == list(range(min(number_list), max(number_list) + 1))
    except:  # pylint: disable=bare-except
        return False


def smart_join(data_list, separator, last_separator):  # [foo, bar, tao], ", ", "&" --> "foo, bar & tao"
    if not data_list:
        return
    try:
        if len(data_list) == 1:
            return data_list[0]
        if len(data_list) == 2:
            return " {} ".format(last_separator).join(data_list)
        if not last_separator:
            return separator.join(data_list)  # fallback
        return "{} {} {}".format(separator.join(data_list[:-1]), last_separator, data_list[-1])
    except:  # pylint: disable=bare-except
        return separator.join(data_list)


def convert_tuples_to_dict(tuples, invert=False):
    result = {}
    for tup in tuples:
        if invert:
            result[tup[1]] = tup[0]
        else:
            result[tup[0]] = tup[1]

    return result


def validate_csv(content):  # iterates each line of csv to validate if it can be parsed
    reader = csv.DictReader(content.decode('utf-8-sig', errors='replace'))
    for _ in enumerate(reader):
        pass

def is_valid_number(number):
    try:
        float(number)
        return True
    except ValueError:
        return False

def strip_special(format_str):
    if format_str:
        format_str = re.sub('[^A-Za-z0-9]+', '', format_str)
        return format_str.lower()


def unzip_files(file_location, location_to_save):
    with ZipFile(file_location, 'r') as zip_file:
        zip_file.extractall(location_to_save)


def decrypt_password(password):
    SALT = "$2a$10$"

    if SALT not in password:
        return password

    chars = password.split(SALT)
    return ''.join([chr(int(char)) for char in chars])


def remove_elements_from_list(original_list, remove_list):
    """
    This function removes elements a list from another list
    and preserve the order of elements
    """
    return list(filter(lambda i: i not in remove_list, original_list))


def decode_string(_str, plus=True):
    return parse.unquote_plus(_str) if plus else parse.unquote(_str)


def str_to_array_request_param(params, attr, to_int=False):
    vals = params.get(attr, '').split(',')
    result = []
    for val in vals:
        if val:
            val = val.strip()
            if to_int:
                val = int(val)
            result.append(val)
    return list(set(result))


def chunks(lst, n):
    """Yield successive n-sized chunks from lst."""
    for i in range(0, len(lst), n):
        yield lst[i:i + n]


def to_number(_str):
    return float(re.sub(r'[//a-zA-Z/%/$\s]', '', str(_str))) if _str else 0


def format_csv_string(val):
    if isinstance(val, str):
        return val.replace(',', '').replace('\n', ' ').replace(
            '\r', ' ').replace('"', "'").replace('""', "'")
    return val


def to_usa_phone_format(value):
    if not value:
        return value

    val = value.replace(')', '').replace('(', '').replace(' ', '').replace('-', '')
    return f'({val[:3]}) {val[3:6]}-{val[6:10]}'


def to_nz_bank_account_format(bank_account):
    account_number = get(bank_account, 'account_number')
    if not account_number:
        return account_number or ''

    return f"{account_number[:7]}-{account_number[-3:]}"


def to_nz_branch_number_format(branch_number):
    if not branch_number:
        return branch_number or ''

    return f"{branch_number[:2]}-{branch_number[2:]}"


def api_version():
    version_file_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), '..', 'VERSION.txt')
    return open(version_file_path, mode='r', encoding='UTF-8').read().split('\n')[0]


def save_download_locally(download, buff=None, file_path=None):  # pragma: no cover
    """
    This function is used to save a download locally for development/testing purposes.
    Physical location is /core/contracts/static/
    Name of the file will be same as download.name (contracts_16958027164263468.csv)
    """
    if download.name and settings.ENV in ['dev', 'ci']:
        if not file_path and not buff:
            return
        path = os.path.join(os.path.abspath(os.path.dirname(__file__)), '..', 'contracts', 'static', download.name)
        if file_path:
            subprocess.call(f'mv {file_path} {path}', shell=True)
        else:
            with open(path, 'wb') as file:
                file.write(buff.getvalue())
                file.close()

def get_valid_float_from_string(val):
    if isinstance(val, str):
        parts = val.split('.')
        val = parts[0] + '.' + ''.join(parts[1:])
        return float(val)
    return val


def cd_temp():
    cwd = os.getcwd()
    tmpdir = tempfile.mkdtemp()
    os.chdir(tmpdir)
    return cwd


def dict_to_obj(d, convert=True, exclude_keys=None):
    exclude_keys = exclude_keys or []

    if isinstance(d, dict):
        for k, v in d.items():
            if isinstance(v, (dict, list)):
                should_convert = convert and k not in exclude_keys
                d[k] = dict_to_obj(v, convert=should_convert, exclude_keys=exclude_keys)

        if convert:
            return SimpleNamespace(**d)
        else:
            return d

    elif isinstance(d, list):
        return [dict_to_obj(i, convert=convert, exclude_keys=exclude_keys)
                if isinstance(i, (dict, list)) else i for i in d]
    else:
        return d
