# Generated by Django 4.1.13 on 2024-05-12 06:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0165_auto_20240509_0158'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalxeromapping',
            name='item_type',
            field=models.CharField(blank=True, choices=[('title_transfers', 'Title Transfers'), ('movements', 'Freight Movements'), ('carry', 'Carry Charges'), ('custom', 'Custom Items'), ('epr', 'EPR'), ('levy', 'Grain Levy'), ('inload_fees', 'Inload Fees'), ('outload_fees', 'Outload Fees'), ('storage_fees', 'Storage Fees'), ('transfer_fees', 'Transfer Fees'), ('stock_swap', 'Stock Swap'), ('regrade_reseason', 'Regrade Reseason'), ('chemical_applications', 'Chemical Applications')], max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='xeromapping',
            name='item_type',
            field=models.CharField(blank=True, choices=[('title_transfers', 'Title Transfers'), ('movements', 'Freight Movements'), ('carry', 'Carry Charges'), ('custom', 'Custom Items'), ('epr', 'EPR'), ('levy', 'Grain Levy'), ('inload_fees', 'Inload Fees'), ('outload_fees', 'Outload Fees'), ('storage_fees', 'Storage Fees'), ('transfer_fees', 'Transfer Fees'), ('stock_swap', 'Stock Swap'), ('regrade_reseason', 'Regrade Reseason'), ('chemical_applications', 'Chemical Applications')], max_length=250, null=True),
        ),
    ]
