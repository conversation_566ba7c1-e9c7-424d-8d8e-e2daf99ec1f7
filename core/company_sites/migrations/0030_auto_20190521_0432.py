# Generated by Django 2.1.7 on 2019-05-21 04:32

from django.db import migrations
from core.company_sites.constants import DEFAULT_SLOT_STATUSES

def populate_company_site_management_settings(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    SiteManagementSettings = apps.get_model('company_sites', 'SiteManagementSettings')
    settings = []
    for company in Company.objects.all():
        settings.append(SiteManagementSettings(company=company, colors=DEFAULT_SLOT_STATUSES))

    SiteManagementSettings.objects.bulk_create(settings)


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0029_auto_20190520_1659'),
    ]

    operations = [
        migrations.RunPython(populate_company_site_management_settings)
    ]
