# Generated by Django 4.0.4 on 2022-07-06 09:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0030_alter_historicalcommodity_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('company_sites', '0114_freightslot__last_pushed_at_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='freightslot',
            name='commodity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='commodities.commodity'),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='driver',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='grade',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='commodities.grade'),
        ),
    ]
