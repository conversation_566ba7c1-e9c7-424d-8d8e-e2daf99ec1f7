# Generated by Django 4.1.13 on 2024-06-05 07:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0168_historicalxeromapping_site_xeromapping_site'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalxeromapping',
            name='item_type',
            field=models.CharField(blank=True, choices=[('title_transfers', 'Title Transfers'), ('movements', 'Freight Movements'), ('carry', 'Carry Charges'), ('custom', 'Custom Items'), ('epr', 'EPR'), ('levy', 'Grain Levy'), ('inload_fees', 'Inload Fees'), ('outload_fees', 'Outload Fees'), ('storage_fees', 'Storage Fees'), ('transfer_fees', 'Transfer Fees'), ('stock_swap', 'Stock Swap'), ('regrade_reseason', 'Regrade Reseason'), ('chemical_applications', 'Chemical Applications'), ('blending_fees', 'Blending Fees')], max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='xeromapping',
            name='item_type',
            field=models.CharField(blank=True, choices=[('title_transfers', 'Title Transfers'), ('movements', 'Freight Movements'), ('carry', 'Carry Charges'), ('custom', 'Custom Items'), ('epr', 'EPR'), ('levy', 'Grain Levy'), ('inload_fees', 'Inload Fees'), ('outload_fees', 'Outload Fees'), ('storage_fees', 'Storage Fees'), ('transfer_fees', 'Transfer Fees'), ('stock_swap', 'Stock Swap'), ('regrade_reseason', 'Regrade Reseason'), ('chemical_applications', 'Chemical Applications'), ('blending_fees', 'Blending Fees')], max_length=250, null=True),
        ),
    ]
