# Generated by Django 5.1.8 on 2025-06-04 06:17

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0217_company_show_throughput_load_and_more'),
        ('ngrs', '0052_auto_20250325_0338'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalxeromapping',
            name='ngr',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='ngrs.ngr'),
        ),
        migrations.AddField(
            model_name='xeromapping',
            name='ngr',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ngrs.ngr'),
        ),
    ]
