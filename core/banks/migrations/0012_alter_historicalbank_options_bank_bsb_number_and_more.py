# Generated by Django 4.0.3 on 2022-05-30 04:57

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('banks', '0011_alter_historicalbank_options_alter_bank_created_by_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='historicalbank',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical bank'},
        ),
        migrations.AddField(
            model_name='bank',
            name='bsb_number',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=15), blank=True, null=True, size=None),
        ),
        migrations.AddField(
            model_name='bank',
            name='head_branch',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='historicalbank',
            name='bsb_number',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=15), blank=True, null=True, size=None),
        ),
        migrations.AddField(
            model_name='historicalbank',
            name='head_branch',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='bank',
            name='code',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=10), size=None),
        ),
        migrations.AlterField(
            model_name='historicalbank',
            name='code',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=10), size=None),
        ),
        migrations.AlterField(
            model_name='historicalbank',
            name='history_date',
            field=models.DateTimeField(),
        ),
    ]
