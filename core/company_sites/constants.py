# pylint: disable=line-too-long

# WEB_PUSH_NOTIFICATIONS_ACTIONS
SLOT_ASSIGNED = 'Slot Assigned'
SLOTS_ASSIGNED = 'Slots Assigned'
SLOT_BOOKED = 'Slot Booked'
SLOTS_BOOKED = 'Slots Booked'
BOOKING_CANCELLED = 'Booking Cancelled'
SLOT_UPDATED = 'Slot Updated'
COMMENT_ADDED = 'Comment Added'
SM_BROADCAST_MESSAGE_TITLE = "Message from {company_name}"


NEW_PLANNED_SINGLE_SLOT_WITH_FP = 'NEW_PLANNED_SINGLE_SLOT_WITH_FP'
BOOKED_SINGLE_SLOT_WITH_FP = 'BOOKED_SINGLE_SLOT_WITH_FP'
NEW_PLANNED_RECURRING_SLOTS_WITH_FP = 'NEW_PLANNED_RECURRING_SLOTS_WITH_FP'
NEW_BOOKED_RECURRING_SLOTS_WITH_FP = 'NEW_BOOKED_RECURRING_SLOTS_WITH_FP'
CANCELLED_BOOKING_BY_SITE_MANAGER = 'CANCELLED_BOOKING_BY_SITE_MANAGER'
CHANGED_STATUS = 'CHANGED_STATUS'
UPDATED_SLOT_BY_SITE_MANAGER = 'UPDATED_SLOT_BY_SITE_MANAGER'
BOOKED_SLOT = 'BOOKED_SLOT'
CANCELLED_BOOKING_BY_BOOKIE = 'CANCELLED_BOOKING_BY_BOOKIE'
UPDATED_SLOT_BY_BOOKIE = 'UPDATED_SLOT_BY_BOOKIE'
NEW_COMMENT = 'NEW_COMMENT'
NEW_COMMENT_BY_BOOKIE = 'NEW_COMMENT_BY_BOOKIE'

NEW_PLANNED_SINGLE_SLOT_WITH_FP_APP = "<b>{user}</b> from <b>{company}</b> has assigned you a slot for <b>{site_name}</b> site on <b>start_date</b> at <b>start_time</b>"
BOOKED_SINGLE_SLOT_WITH_FP_APP = "<b>{user}</b> from <b>{company}</b> has booked a slot for you for <b>{site_name}</b> site on <b>start_date</b> at <b>start_time</b>"
NEW_PLANNED_RECURRING_SLOTS_WITH_FP_APP = "<b>{user}</b> from <b>{company}</b> has assigned multiple slots for <b>{site_name}</b> site from <b>start_date start_time</b> to <b>end_date end_time</b>"
NEW_BOOKED_RECURRING_SLOTS_WITH_FP_APP = "<b>{user}</b> from <b>{company}</b> has booked multiple slots for <b>{site_name}</b> site from <b>start_date start_time</b> to <b>end_date end_time</b>"
CANCELLED_BOOKING_BY_SITE_MANAGER_APP = "<b>{user}</b> from <b>{company}</b> has cancelled your booking for <b>{site_name}</b> site on <b>start_date</b> at <b>start_time</b>"
CHANGED_STATUS_APP = "<b>{user}</b> from <b>{company}</b> has marked your slot for <b>{site_name}</b> site on <b>start_date</b> at <b>start_time</b> as <b>{status}</b>"
UPDATED_SLOT_BY_SITE_MANAGER_APP = "<b>{user}</b> from <b>{company}</b> has updated details of your slot for <b>{site_name}</b> site on <b>start_date</b> at <b>start_time</b>"
BOOKED_SLOT_APP = "<b>{user}</b> from <b>{company}</b> has booked the slot for <b>{site_name}</b> site on <b>start_date</b> at <b>start_time</b>"
CANCELLED_BOOKING_BY_BOOKIE_APP = "<b>{user}</b> from <b>{company}</b> has cancelled their booking for the slot for <b>{site_name}</b> site on <b>start_date</b> at <b>start_time</b>"
UPDATED_SLOT_BY_BOOKIE_APP = "<b>{user}</b> from <b>{company}</b> has updated their booking details for the slot for <b>{site_name}</b> site on <b>start_date</b> at <b>start_time</b>"
NEW_COMMENT_APP = "<b>{user}</b> from <b>{company}</b> has added a comment against your slot for <b>{site_name}</b> site on <b>start_date</b> at <b>start_time</b>"
NEW_COMMENT_BY_BOOKIE_APP = "<b>{user}</b> from <b>{company}</b> has added a comment against the slot of <b>{site_name}</b> site on <b>start_date</b> at <b>start_time</b>"
SM_BROADCAST_IN_APP_NOTIFICATION_MESSAGE = 'Message from {company_name}: <b>{message_text}</b>'
SM_BROADCAST_PUSH_NOTIFICATION_MESSAGE = 'Message from {company_name} "{plain_text}"'
SM_BROADCAST_WEB_PUSH_NOTIFICATION_MESSAGE = '{plain_text}'

WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT = "<b>start_date</b> | <b>start_time</b>"

PUSH_NOTIFICATION_DICTIONARY = {
    NEW_PLANNED_SINGLE_SLOT_WITH_FP: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': NEW_PLANNED_SINGLE_SLOT_WITH_FP_APP, 'action': SLOT_ASSIGNED},
    BOOKED_SINGLE_SLOT_WITH_FP: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': BOOKED_SINGLE_SLOT_WITH_FP_APP, 'action': SLOT_BOOKED},
    NEW_PLANNED_RECURRING_SLOTS_WITH_FP: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': NEW_PLANNED_RECURRING_SLOTS_WITH_FP_APP, 'action': SLOTS_ASSIGNED},
    NEW_BOOKED_RECURRING_SLOTS_WITH_FP: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': NEW_BOOKED_RECURRING_SLOTS_WITH_FP_APP, 'action': SLOTS_BOOKED},
    CANCELLED_BOOKING_BY_SITE_MANAGER: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': CANCELLED_BOOKING_BY_SITE_MANAGER_APP, 'action': BOOKING_CANCELLED},
    CHANGED_STATUS: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': CHANGED_STATUS_APP, 'action': SLOT_UPDATED},
    UPDATED_SLOT_BY_SITE_MANAGER: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': UPDATED_SLOT_BY_SITE_MANAGER_APP, 'action': SLOT_UPDATED},
    BOOKED_SLOT: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': BOOKED_SLOT_APP, 'action': SLOT_BOOKED},
    CANCELLED_BOOKING_BY_BOOKIE: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': CANCELLED_BOOKING_BY_BOOKIE_APP, 'action': BOOKING_CANCELLED},
    UPDATED_SLOT_BY_BOOKIE: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': UPDATED_SLOT_BY_BOOKIE_APP, 'action': SLOT_UPDATED},
    NEW_COMMENT: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': NEW_COMMENT_APP, 'action': COMMENT_ADDED},
    NEW_COMMENT_BY_BOOKIE: {'web': WEB_PUSH_NOTIFICATION_FORMAT_CONSTANT, 'app': NEW_COMMENT_BY_BOOKIE_APP, 'action': COMMENT_ADDED},
}

SLOT_PLANNED_COLOR = "#4AB9D8"
SLOT_BOOKED_COLOR = "#F7C718"
SLOT_IN_PROGRESS_COLOR = "#FF8500"
SLOT_COMPLETED_COLOR = "#3BB820"
SLOT_DELAYED_COLOR = "#D41BD4"
SLOT_CANCELLED_COLOR = "#CB0000"
SLOT_REJECTED_COLOR = "#000000"
SLOT_RESTRICTED_COLOR = "#9d9d9d"

DEFAULT_SLOT_STATUSES = [
    {
        'id': 'planned',
        'name': 'Planned',
        'label': 'Planned',
        'color': SLOT_PLANNED_COLOR,
    },
    {
        'id': 'booked',
        'name': 'Booked',
        'label': 'Booked',
        'color': SLOT_BOOKED_COLOR,
    },
    {
        'id': 'in_progress',
        'name': 'In Progress',
        'label': 'In Progress',
        'color': SLOT_IN_PROGRESS_COLOR,
    },
    {
        'id': 'completed',
        'name': 'Completed',
        'label': 'Completed',
        'color': SLOT_COMPLETED_COLOR,
    },
    {
        'id': 'delayed',
        'name': 'Delayed',
        'label': 'Delayed',
        'color': SLOT_DELAYED_COLOR,
    },
    {
        'id': 'cancelled',
        'name': 'Cancelled',
        'label': 'Incomplete',
        'color': SLOT_CANCELLED_COLOR,
    },
    {
        'id': 'rejected',
        'name': 'Rejected',
        'label': 'Rejected',
        'color': SLOT_REJECTED_COLOR,
    },
    {
        'id': 'restricted',
        'name': 'Restricted',
        'label': 'Restricted',
        'color': SLOT_RESTRICTED_COLOR,
    },
]

STATUSES = (
    ('planned', 'Planned'),
    ('booked', 'Booked'),
    ('in_progress', 'In Progress'),
    ('delayed', 'Delayed'),
    ('completed', 'Completed'),
    ('cancelled', 'Cancelled'),
    ('rejected', 'Rejected'),
    ('restricted', 'Restricted'),
)

SLOT_VIEWS = (
    ('day', 'Daily'),
    ('week', 'Weekly'),
    ('month', 'Monthly'),
)

CUSTOMER_FIELD_SETTINGS = {
    "show": True,
    "defaultValue": None,
    "mandatory": False,
    "label": "Customer",
    "name": "Customer",
    "customisable": True,
    "id": "customer"
}

DEFAULT_SLOT_FIELDS = [
    {
        "show": True,
        "defaultValue": None,
        "mandatory": True,
        "label": "Site",
        "name": "Site",
        "customisable": False,
        "id": "siteId"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Type (Optional)",
        "name": "Type",
        "customisable": True,
        "id": "type"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": True,
        "label": "Status",
        "name": "Status",
        "customisable": False,
        "id": "status"
    },
    {
        "show": False,
        "defaultValue": None,
        "mandatory": False,
        "label": "Priority (Optional)",
        "name": "Priority",
        "customisable": True,
        "id": "priority"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": True,
        "label": "Date",
        "name": "Date",
        "customisable": False,
        "id": "startDate"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": True,
        "label": "Start Time",
        "name": "Start Time",
        "customisable": False,
        "id": "startTime"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": True,
        "label": "End Time",
        "name": "End Time",
        "customisable": False,
        "id": "endTime"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Commodity (Optional)",
        "name": "Commodity",
        "customisable": True,
        "id": "commodityId"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Grade (Optional)",
        "name": "Grade",
        "customisable": True,
        "id": "gradeId"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Tonnage (Optional)",
        "name": "Tonnage",
        "customisable": True,
        "id": "tonnage"
    },
    {
        "show": False,
        "defaultValue": None,
        "mandatory": False,
        "label": "Season (Optional)",
        "name": "Season",
        "customisable": True,
        "id": "season"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Customer (Optional)",
        "name": "Customer",
        "customisable": True,
        "id": "customer"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Order No (Optional)",
        "name": "Order No",
        "customisable": True,
        "id": "deliveryOrderNumber"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Booking No (Optional)",
        "name": "Booking No",
        "customisable": True,
        "id": "bookingNumber"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": True,
        "label": "Freight Provider",
        "name": "Freight Provider",
        "customisable": False,
        "id": "freightProviderId"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Sub Freight Provider",
        "name": "Sub Freight Provider",
        "customisable": True,
        "id": "subFreightProviderId"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Truck (Optional)",
        "name": "Truck",
        "customisable": True,
        "id": "truckId"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Mass Limit Code",
        "name": "Mass Limit Code",
        "customisable": True,
        "id": "massLimitCode"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Driver (Optional)",
        "name": "Driver",
        "customisable": True,
        "id": "driverId"
    },
    {
        "show": False,
        "defaultValue": None,
        "mandatory": False,
        "label": "Pits (Optional)",
        "name": "Pits",
        "customisable": True,
        "id": "pits"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Trailer 1",
        "name": "Trailer 1",
        "customisable": True,
        "id": "trailer_1"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Trailer 2",
        "name": "Trailer 2",
        "customisable": True,
        "id": "trailer_2"
    },
    {
        "show": True,
        "defaultValue": None,
        "mandatory": False,
        "label": "Trailer 3",
        "name": "Trailer 3",
        "customisable": True,
        "id": "trailer_3"
    }
]

MARK_SLOT_URGENT_HOURS_FROM_START = 24
FLEET_TRUCK_NOTICE_HOURS_FROM_START = 4

PITS = [
    {'id': 'road', 'name': 'Road'},
    {'id': 'rail', 'name': 'Rail'},
    {'id': 'bulk', 'name': 'Bulk'},
    {'id': 'liquid', 'name': 'Liquid'},
    {'id': 'additive', 'name': 'Additive'},
    {'id': 'flour', 'name': 'Flour'},
    {'id': 'animal_nutrition', 'name': 'Animal Nutrition'},
    {'id': 'meal', 'name': 'Meal'},
]

SLOT_OLD_VERSION = 'Slot Details have been updated by someone else. Please close the slot and try again'
SLOT_SETTING_OLD_VERSION = 'Site Settings have been updated by someone else. Please refresh the page and try again'
REVERT_TO_BOOKED_STATUSES = ['completed', 'rejected', 'cancelled', 'delayed']
COMMON_COMPARISON_CSV_REPORT_HEADERS = [
    'No of Booked Slots',
    'No of Completed Slots',
    'No of Incomplete Slots / No Shows',
    'No of Cancelled Slots',
    '% Completed Slots',
    '% Incomplete Slots / No Shows',
    '% Cancelled Slots',
    'Avg Movement Time',
    'Max Movement Time',
    'Min Movement Time',
    'Peak Time'
]
SITE_COMPARISON_CSV_REPORT_HEADERS = [
    'Sites',
    *COMMON_COMPARISON_CSV_REPORT_HEADERS,
    'Truck Average Time On Site',
    'Min Time Spent On Site',
    'Max Time Spent On Site',
    'Truck Average Waiting Time'
]
CARRIER_COMPARISON_CSV_REPORT_HEADERS = [
    'Carriers',
    *COMMON_COMPARISON_CSV_REPORT_HEADERS
]

FLEET_TRUCK_MAIL_SUBJECT = '[AgriChain] Please update slot bookings with correct details to avoid any delays ' \
                           'to your truck.'

CHECKPOINT_ORDER_CREATION_PERMISSIONS = (
    ('site_only', 'Site Only'),
    ('grain_owner_and_site_acceptance_from_site', 'Grain Owner and Site (Acceptance from Site)'),
    ('grain_owner_and_site', 'Grain owner and Site (Site Acceptance Not Required)'),
)

EXTERNAL_SFTP_SLOT_PUSH_CSV_HEADERS = [
    "Vendor No",
    "Type",
    "Movement No",
    "Order No",
    "Contract No",
    "Commodity",
    "Material Code",
    "Slot Date Time",
    "Booked Tonnage",
    "Site",
    "Truck",
    "Driver",
    "Grade",
    "Status"
]

TRUCK_COMPANY_NOT_ALLOWED = 'The truck company is not allowed to book this slot. Please talk to Site/Company Admin.'
SLOT_OLD_VERSION_FROM_MOVEMENT = 'This slot is updated by someone else, please select a different slot.'

MAXIMUM_SLOTS = 1000
BOOKED_STATUS='booked'
