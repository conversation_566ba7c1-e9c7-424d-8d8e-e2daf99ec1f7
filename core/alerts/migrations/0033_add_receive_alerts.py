# Generated by Django 5.1.8 on 2025-06-16 08:48

from django.db import migrations

def add_receive_alerts(apps, schema_editor):
    ReceiveAlert = apps.get_model('alerts', 'ReceiveAlert')
    ALERTS = [
        'slot_booking_alert',
        'pickup_information',
        'delivery_information'
    ]
    for alert_name in ALERTS:
        ReceiveAlert.objects.create(name=alert_name, channel='email')

class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0032_create_receive_alert_models'),
    ]

    operations = [
        migrations.RunPython(
            add_receive_alerts,
            lambda x,y: None
        ),
    ]
