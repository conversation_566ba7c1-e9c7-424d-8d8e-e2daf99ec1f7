# Generated by Django 2.1.2 on 2018-10-30 13:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0007_auto_20181030_1225'),
    ]

    operations = [
        migrations.AlterField(
            model_name='commodity',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name='epr',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name='grade',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalcommodity',
            name='updated_at',
            field=models.DateTimeField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name='historicalepr',
            name='updated_at',
            field=models.DateTimeField(blank=True, editable=False, null=True),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name='historicalgrade',
            name='updated_at',
            field=models.DateTimeField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name='historicalvariety',
            name='updated_at',
            field=models.DateTimeField(blank=True, editable=False, null=True),
        ),
        migrations.AlterField(
            model_name='variety',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
    ]
