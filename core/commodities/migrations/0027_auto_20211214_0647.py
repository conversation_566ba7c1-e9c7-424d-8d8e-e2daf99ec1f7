# Generated by Django 3.2.6 on 2021-12-14 06:47

from django.db import migrations


def convert_fertiliser_varieties_to_grade(apps, schema_editor):
    Load = apps.get_model('loads', 'Load')
    Contract = apps.get_model('contracts', 'Contract')
    TitleTransfer = apps.get_model('contracts', 'TitleTransfer')
    FreightContract = apps.get_model('freights', 'FreightContract')
    FreightOrder = apps.get_model('freights', 'FreightOrder')
    Storage = apps.get_model('farms', 'Storage')
    CommodityVendorDec = apps.get_model('vendor_decs', 'CommodityVendorDec')
    variety_grade_map = {
        905: 279, 906: 280, 907: 281, 908: 282, 909: 283, 910: 284, 911: 285, 1681: 286, 1682: 287, 1683: 288
    }
    for klass in [
        Load, Contract, TitleTransfer, Storage, CommodityVendorDec
    ]:
        for variety_id, grade_id in variety_grade_map.items():
            klass.objects.filter(variety_id=variety_id).update(variety_id=None, grade_id=grade_id)

    for klass in [
        FreightContract, FreightOrder
    ]:
        for variety_id, grade_id in variety_grade_map.items():
            klass.objects.filter(variety_id=variety_id).update(variety_id=None, planned_grade_id=grade_id)


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0026_auto_20211129_1336'),
    ]

    operations = [
        migrations.RunPython(convert_fertiliser_varieties_to_grade)
    ]
