import csv
import json

res = []
count = 563
csvfile = open('./OatsVarieties.csv', "r")
fieldnames = ("Variety","Classification",)
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _json = {
            "pk": count,
            "model": "commodities.variety",
            "fields": {
                "name": _dict["Variety"],
                "details": {"type": _dict["Classification"]},
                "commodity_id": 8,
                "created_at": "2018-11-19T08:00:00+00:00",
                "updated_at": "2018-11-19T08:00:00+00:00",
                "created_by": 1,
                "updated_by": 1,
            },
        }
        count += 1
        res.append(_json)

print(len(res))
print(json.dumps(res))
