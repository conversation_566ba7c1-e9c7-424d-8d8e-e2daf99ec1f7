from rest_framework.generics import ListAP<PERSON>View

from core.audit_history.constants import (CONTRACT_MODEL, ORDER_MODEL, MOVEMENT_MODEL, INVOICE_MODEL, VENDOR_DEC,
                                          CASH_PRICE)
from core.audit_history.models import AuditHistory
from core.audit_history.serializers import AuditHistorySerializer
from core.common.utils import get_content_type_id
from core.contracts.models import Contract
from core.freights.models import FreightOrder, FreightContract


class ContractAuditHistoryView(ListAPIView):
    serializer_class = AuditHistorySerializer

    def get_queryset(self):
        contract_content_type_id = get_content_type_id(CONTRACT_MODEL)
        contract_id = self.kwargs.get('contract_id')
        contract = Contract.objects.filter(id=contract_id).first()
        user = self.request.user
        if contract and not contract.can_view_vendor_dec(user):
            return None

        manager = AuditHistory.objects
        ids = []
        ids += list(manager.filter(order__commodity_contract=contract_id).values_list('id', flat=True))
        ids += list(manager.filter(title_transfer__commodity_contract=contract_id).values_list('id', flat=True))
        ids += list(manager.filter(movement__commodity_contract=contract_id).values_list('id', flat=True))
        ids += list(manager.filter(vendor_dec__contract_id=contract_id).values_list('id', flat=True))
        ids += list(manager.filter(instance_id=contract_id, instance_type_id=contract_content_type_id).values_list(
            'id', flat=True))
        ids += list(manager.filter(
            invoice__raised_for_id=contract_id, invoice__raised_for_type_id=contract_content_type_id).values_list(
            'id', flat=True))

        return AuditHistory.objects.filter(id__in=ids).select_related('created_by', 'created_by__company')

    def get_serializer_context(self):
        return {"contract_id": self.kwargs.get('contract_id')}


class OrderAuditHistoryView(ListAPIView):
    serializer_class = AuditHistorySerializer

    def get_queryset(self):
        content_type_id = get_content_type_id(ORDER_MODEL)
        order_id = self.kwargs.get('order_id')
        order = FreightOrder.objects.filter(id=order_id).first()
        user = self.request.user
        if order and not order.can_view_vendor_dec(user):
            return None

        manager = AuditHistory.objects
        ids = []
        ids += list(manager.filter(order=order_id).values_list('id', flat=True))
        ids += list(manager.filter(order__parent_order=order_id).values_list('id', flat=True))
        ids += list(manager.filter(movement__order=order_id).values_list('id', flat=True))
        ids += list(manager.filter(vendor_dec__order_id=order_id).values_list('id', flat=True))
        ids += list(manager.filter(instance_id=order_id, instance_type_id=content_type_id).values_list('id', flat=True))
        ids += list(manager.filter(
            invoice__raised_for_id=order_id, invoice__raised_for_type_id=content_type_id).values_list('id', flat=True))
        pickup_order_id = order.get_checkpoint_order_id_for_audit_history('freight_pickup')
        delivery_order_id = order.get_checkpoint_order_id_for_audit_history('freight_delivery')
        if pickup_order_id:
            ids += list(manager.filter(order=pickup_order_id).values_list('id', flat=True))
        if delivery_order_id:
            ids += list(manager.filter(order=delivery_order_id).values_list('id', flat=True))

        return AuditHistory.objects.filter(id__in=ids).select_related('created_by', 'created_by__company')

    def get_serializer_context(self):
        return {"order_id": self.kwargs.get('order_id')}


class MovementAuditHistoryView(ListAPIView):
    serializer_class = AuditHistorySerializer

    def get_queryset(self):
        content_type_id = get_content_type_id(MOVEMENT_MODEL)
        movement_id = self.kwargs.get('movement_id')
        movement = FreightContract.objects.filter(id=movement_id).first()
        user = self.request.user
        if movement and not movement.can_view_vendor_dec(user):
            return None

        manager = AuditHistory.objects
        ids = []
        ids += list(manager.filter(
            instance_id=movement_id, instance_type_id=content_type_id).values_list('id', flat=True))
        ids += list(manager.filter(vendor_dec__movement_id=movement_id).values_list('id', flat=True))

        return AuditHistory.objects.filter(id__in=ids).select_related('created_by', 'created_by__company')

    def get_serializer_context(self):
        return {"movement_id": self.kwargs.get('movement_id')}


class InvoiceAuditHistoryView(ListAPIView):
    serializer_class = AuditHistorySerializer

    def get_queryset(self):
        content_type_id = get_content_type_id(INVOICE_MODEL)
        invoice_id = self.kwargs.get('invoice_id')
        return AuditHistory.objects.select_related('created_by', 'created_by__company').filter(
            instance_id=invoice_id, instance_type_id=content_type_id
        )

    def get_serializer_context(self):
        return {"invoice_id": self.kwargs.get('invoice_id')}


class VendorDecAuditHistoryView(ListAPIView):
    serializer_class = AuditHistorySerializer

    def get_queryset(self):
        content_type_id = get_content_type_id(VENDOR_DEC)
        vendor_dec_id = self.kwargs.get('vendor_dec_id')
        return AuditHistory.objects.select_related('created_by', 'created_by__company').filter(
            instance_id=vendor_dec_id, instance_type_id=content_type_id
        )

    def get_serializer_context(self):
        return {"vendor_dec_id": self.kwargs.get('vendor_dec_id')}


class CashPriceAuditHistoryView(ListAPIView):
    serializer_class = AuditHistorySerializer

    def get_queryset(self):
        content_type_id = get_content_type_id(CASH_PRICE)
        cash_price_id = self.kwargs.get('cash_price_id')
        return AuditHistory.objects.select_related('created_by', 'created_by__company').filter(
            instance_id=cash_price_id, instance_type_id=content_type_id
        )

    def get_serializer_context(self):
        return {"cash_price_id": self.kwargs.get('cash_price_id')}


class TitleTransferAuditHistoryView(ListAPIView):
    serializer_class = AuditHistorySerializer

    def get_queryset(self):
        content_type_id = get_content_type_id('titletransfer')
        title_transfer_id = self.kwargs.get('title_transfer_id')
        return AuditHistory.objects.select_related('created_by', 'created_by__company').filter(
            instance_id=title_transfer_id, instance_type_id=content_type_id
        )

    def get_serializer_context(self):
        return {"title_transfer_id": self.kwargs.get('title_transfer_id')}
