# Generated by Django 2.1.7 on 2019-04-01 09:31

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0031_auto_20190305_0657'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='bhcsite',
            name='mobile',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 1, 02, 03, 04, 07 or 08')]),
        ),
        migrations.Alter<PERSON>ield(
            model_name='bhcsite',
            name='phone',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 1, 02, 03, 04, 07 or 08')]),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='company',
            name='fax',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|07|08)[0-9]+$', 'Must start with 1, 02, 03, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='company',
            name='mobile',
            field=models.CharField(max_length=10, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 1, 02, 03, 04, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='historicalbhcsite',
            name='mobile',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 1, 02, 03, 04, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='historicalbhcsite',
            name='phone',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 1, 02, 03, 04, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='fax',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|07|08)[0-9]+$', 'Must start with 1, 02, 03, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='mobile',
            field=models.CharField(max_length=10, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 1, 02, 03, 04, 07 or 08')]),
        ),
    ]
