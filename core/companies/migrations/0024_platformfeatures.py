# Generated by Django 2.1.5 on 2019-02-14 04:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('companies', '0023_auto_20190128_1113'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlatformFeatures',
            fields=[
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('company', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to='companies.Company')),
                ('companies', models.BooleanField(default=False)),
                ('farms', models.BooleanField(default=False)),
                ('stocks', models.BooleanField(default=False)),
                ('contracts', models.BooleanField(default=False)),
                ('orders', models.BooleanField(default=False)),
                ('movements', models.BooleanField(default=False)),
                ('invoices', models.BooleanField(default=False)),
                ('site_management', models.BooleanField(default=False)),
                ('site_bookings', models.BooleanField(default=False)),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_platformfeatures_related_created_by', related_query_name='companies_platformfeaturess_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_platformfeatures_related_updated_by', related_query_name='companies_platformfeaturess_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'platform_features',
                'ordering': ['company_id'],
            },
        ),
    ]
