# Generated by Django 4.1.10 on 2023-11-03 06:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0192_merge_20231013_0950'),
    ]

    operations = [
        migrations.AlterField(
            model_name='contract',
            name='tolerance',
            field=models.ForeignKey(default=16, on_delete=django.db.models.deletion.DO_NOTHING, to='contracts.tolerance'),
        ),
        migrations.AlterField(
            model_name='historicalcontract',
            name='tolerance',
            field=models.ForeignKey(blank=True, db_constraint=False, default=16, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='contracts.tolerance'),
        ),
        migrations.AlterField(
            model_name='historicaltolerance',
            name='name',
            field=models.CharField(choices=[('Nil Tolerance', 'Nil Tolerance'), ('5% or 12mt (whichever is lesser)', '5% or 12mt (whichever is lesser)'), ('2% or 2mt (whichever is lesser)', '2% or 2mt (whichever is lesser)'), ('+/- 10%', '+/- 10%'), ('+/- 5%', '+/- 5%'), ('+/- 10mt', '+/- 10mt'), ('+/- 5mt', '+/- 5mt'), ('+/- 15mt', '+/- 15mt'), ('+/- 50mt', '+/- 50mt'), ('+/- 3mt', '+/- 3mt'), ('+/- 40mt', '+/- 40mt'), ('+/- 13mt', '+/- 13mt'), ('+/- 20mt', '+/- 20mt'), ('2.5% or 13mt (whichever is lesser)', '2.5% or 13mt (whichever is lesser)'), ('+/- 25mt', '+/- 25mt'), ('5% or 20mt (whichever is lesser)', '5% or 20mt (whichever is lesser)')], max_length=100),
        ),
        migrations.AlterField(
            model_name='tolerance',
            name='name',
            field=models.CharField(choices=[('Nil Tolerance', 'Nil Tolerance'), ('5% or 12mt (whichever is lesser)', '5% or 12mt (whichever is lesser)'), ('2% or 2mt (whichever is lesser)', '2% or 2mt (whichever is lesser)'), ('+/- 10%', '+/- 10%'), ('+/- 5%', '+/- 5%'), ('+/- 10mt', '+/- 10mt'), ('+/- 5mt', '+/- 5mt'), ('+/- 15mt', '+/- 15mt'), ('+/- 50mt', '+/- 50mt'), ('+/- 3mt', '+/- 3mt'), ('+/- 40mt', '+/- 40mt'), ('+/- 13mt', '+/- 13mt'), ('+/- 20mt', '+/- 20mt'), ('2.5% or 13mt (whichever is lesser)', '2.5% or 13mt (whichever is lesser)'), ('+/- 25mt', '+/- 25mt'), ('5% or 20mt (whichever is lesser)', '5% or 20mt (whichever is lesser)')], max_length=100),
        ),
    ]
