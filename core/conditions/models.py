from django.db import models

from core.common.models import BaseModel

class SpecialCondition<PERSON>anager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(type='special')

class GeneralConditionManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(type='general')

class DefaultConditionManager(models.Manager):
    def get_querset(self):
        return super().get_queryset()

class Condition(BaseModel):
    class Meta:
        db_table = "conditions"
        ordering = ['name']

    MODULES = (
        ('contract', 'contract'),
        ('freight', 'freight'),
        ('invoice', 'invoice'),
    )

    TYPES = (
        ('general', 'General'),
        ('special', 'Special'),
    )

    special_objects = SpecialConditionManager()
    general_objects = GeneralConditionManager()
    objects = DefaultConditionManager()

    name = models.CharField(max_length=100)
    details = models.TextField(null=False, blank=False)
    module = models.Char<PERSON>ield(max_length=20, choices=MODULES)
    type = models.CharField(max_length=20, choices=TYPES)
    company = models.ForeignKey('companies.Company', on_delete=models.CASCADE)
    default = models.BooleanField(default=False)
    FILLABLES = [
        'name',
        'details',
        'type',
        'company_id',
        'module',
        'default'
    ]
    