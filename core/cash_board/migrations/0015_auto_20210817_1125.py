# Generated by Django 2.2.20 on 2021-08-17 11:25

import core.cash_board.models
from django.db import migrations
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('cash_board', '0014_auto_20210816_1845'),
    ]

    operations = [
        migrations.AlterModelManagers(
            name='cashprices',
            managers=[
                ('active', core.cash_board.models.ActiveCashPricesManager()),
                ('archived', core.cash_board.models.ArchivedCashPricesManager()),
            ],
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='status',
            field=django_fsm.FSMField(choices=[('completed', 'Completed'), ('inactive', 'Inactive'), ('active', 'Active'), ('partial', 'Partially Fulfilled')], db_index=True, default='active', max_length=40),
        ),
        migrations.AlterField(
            model_name='historicalcashprices',
            name='status',
            field=django_fsm.FSMField(choices=[('completed', 'Completed'), ('inactive', 'Inactive'), ('active', 'Active'), ('partial', 'Partially Fulfilled')], db_index=True, default='active', max_length=40),
        ),
    ]
