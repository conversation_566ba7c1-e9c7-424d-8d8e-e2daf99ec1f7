# pylint: disable=useless-super-delegation
# pylint: disable=consider-iterating-dictionary
# pylint: disable=too-many-instance-attributes

import datetime
from time import time
from types import SimpleNamespace
from unittest.mock import ANY

import factory
import pydash
from django.conf import settings
from django.core import mail
from django.core.exceptions import ValidationError
from django.contrib.auth.hashers import make_password
from django.db import transaction
from django.test import tag
from django.urls import reverse
from django.utils import timezone
from inflection import camelize
from mock import patch, Mock, call, PropertyMock, MagicMock
from pydash import get
from rest_framework import status
from rest_framework.authtoken.models import Token

from core.alerts.constants import STOCK_AUTO_UPDATE_ALERT_CODE
from core.alerts.models import Alert
from core.common.models import MergedEntity
from core.stocks.models import Stock
from core.company_sites.models import SiteManagementSettings

from core.banks.models import Bank
from core.common.constants import (GROWER_TYPE_ID, LOGISTICS_TYPE_ID, OBSERVER_TYPE_ID, SYSTEM_COMPANY_IDS,
                                   TRADER_TYPE_ID, DRIVER_TYPE_ID, BHC_TYPE_ID, SYSTEM_TYPE_ID, COMPANY_ADMIN_TYPE_ID,
                                   FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME)
from core.commodities.models import Commodity
from core.cash_board.models import CashPrices
from core.contract_bids.models import ContractBid
from core.states.models import State
from core.marketzones.models import Marketzone
from core.regions.models import Region
from core.countries.constants import AUSTRALIA_COUNTRY_ID, CANADA_COUNTRY_ID, NZ_COUNTRY_ID, AUSTRALIA_COUNTRY_CODE, \
    USA_COUNTRY_CODE, USA_COUNTRY_ID, CANADA_COUNTRY_CODE, NZ_COUNTRY_CODE
from core.common.tests import ACTestCase, AuthSetup
from core.companies.constants import FEES, WAREHOUSE_INVOICE_MONTHLY_FREQUENCY, WAREHOUSE_INVOICE_WEEKLY_FREQUENCY, \
    SITES, NGR, STORAGES, EMPLOYEES
from core.companies.constants import LOGISTICS_LITE_PLAN, PREMIUM_PLAN, get_csv_headers
from core.companies.mock_data import (
    COMPANY_VALID_MOCK_DATA,
    COMPANY_GROWER_VALID_MOCK_DATA,
    COMPANY_INVALID_MOCK_DATA,
)
from core.companies.models import (
    Company,
    AddedCompany,
    CompanyType,
    SystemCompanyManager,
    NonSystemCompanyManager,
    DefaultCompanyManager,
    PlatformFeatures,
    XeroMapping, CompanyGroup, ApplicationRate,
    SSOSetting, XeroConnection, ImpexDocsConnection)
from core.companies.tests.factories import CompanyFactory, ExternalPortalFactory, CompanyGroupFactory
from core.company_sites.mock_data import (
    COMPANY_SITE_VALID_MOCK_DATA,
)
from core.company_sites.models import CompanySite
from core.company_sites.tests.factories import CompanySiteFactory
from core.contracts.models import TitleTransfer
from core.contracts.tests.factories import (
    PartyFactory, ContractFactory, TitleTransferFactory, ContractCommodityHandlerFactory
)
from core.farms.constants import STORAGE_TYPE_CONTAINER
from core.farms.mock_data import (
    FARM_VALID_MOCK_DATA,
    FARM_VALID_MOCK_DATA_2,
    FARM_VALID_MOCK_DATA_3,
    FARM_VALID_MOCK_DATA_4,
    HOME_STORAGE_VALID_MOCK_DATA
)
from core.farms.models import Farm, Storage, Shrinkage
from core.farms.tests.factories import FarmFactory, StorageFactory
from core.freights.tests.factories import (
    FreightOrderFactory, FreightMovementFactory, FreightPickupFactory, FreightDeliveryFactory
)
from core.loads.models import Load
from core.loads.tests.factories import LoadFactory
from core.locations.mock_data import (
    LOCATION_FARM_MOCK_DATA,
    LOCATION_FARM_MOCK_DATA_2,
    LOCATION_FARM_MOCK_DATA_3,
    LOCATION_FARM_MOCK_DATA_4,
    COMPANY_LOCATION_VALID_MOCK_DATA,
)
from core.locations.models import Location
from core.locations.tests.factories import AddressFactory
from core.ngrs.mock_data import (
    NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA,
    NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA2,
    NGR_WITH_BANK_ACCOUNT_INVALID_MOCK_DATA,
    NGR_WITH_BANK_ACCOUNT_INVALID_MOCK_DATA2,
    NGR_WITHOUT_BANK_ACCOUNT_INVALID_MOCK_DATA,
)
from core.ngrs.models import Ngr, BankAccount, NgrPortalCredential
from core.ngrs.tests.factories import NgrFactory
from core.profiles.mock_data import (
    EMPLOYEE_VALID_MOCK_DATA,
    EMPLOYEE_INVALID_MOCK_DATA,
)
from core.profiles.models import Employee, EmployeeViewFilters
from core.profiles.tests.factories import EmployeeFactory
from core.trailers.mock_data import (
    TRAILER_VALID_MOCK_DATA_1,
    TRAILER_VALID_MOCK_DATA_2,
    TRAILER_VALID_MOCK_DATA_3,
)
from core.trucks.constants import FLEET_REGO
from core.trucks.factories import TruckFactory
from core.trucks.mock_data import (
    TRUCK_VALID_MOCK_DATA_1,
    TRUCK_VALID_MOCK_DATA_2,
)
from core.trucks.models import Truck
from core.countries.models import Country
from core.locations.constants import AUSTRALIA

def _get_company_url(company_id: int):
    return reverse('core.companies:company-detail', kwargs={'company_id': company_id})


def get_company_employees_url(company_id: int):
    return reverse(
        'core.companies:employee-company',
        kwargs={'company_id': company_id}
    )


def get_company_employee_url(company_id: int, employee_id: int):
    return reverse(
        'core.companies:employee-company-detail',
        kwargs={'company_id': company_id, 'employee_id': employee_id}
    )


def _get_company_companies_url(company_id: int):
    return reverse(
        'core.companies:company-company',
        kwargs={'company_id': company_id}
    )


def get_company_farms_url(company_id: int):
    return reverse(
        'core.companies:farm-company',
        kwargs={'company_id': company_id}
    )


def _get_company_trucks_url(company_id: int):
    return reverse(
        'core.companies:truck-company',
        kwargs={'company_id': company_id}
    )


def _get_company_trailers_url(company_id: int):
    return reverse(
        'core.companies:trailer-company',
        kwargs={'company_id': company_id}
    )

def _get_company_unassigned_trailers_url(company_id: int):
    return reverse(
        'core.companies:trailer-unassigned-company',
        kwargs={'company_id': company_id}
    )


def _get_company_truck_url(company_id: int, truck_id: int):
    return reverse(
        'core.companies:truck-company-detail',
        kwargs={'company_id': company_id, 'truck_id': truck_id}
    )

def _get_company_ngr_url(company_id: int, ngr_id: int):
    return reverse(
        'core.companies:ngr-company-detail',
        kwargs={'company_id': company_id, 'ngr_id': ngr_id}
    )


def _get_company_location_url(company_id: int, location_id: int):
    return reverse(
        'core.companies:location-company-detail',
        kwargs={'company_id': company_id, 'location_id': location_id}
    )

def _get_company_site_url(company_id: int, company_site_id: int):
    return reverse(
        'core.companies:company_site-company-detail',
        kwargs={'company_id': company_id, 'company_site_id': company_site_id}
    )

# pylint: disable=too-many-public-methods


@tag('model')
class EstablishmentMixinTest(ACTestCase):
    @patch('core.companies.models.Company.company_key_contacts_set')
    def test_key_contact_name_for_user(self, company_key_contacts_set_mock):
        company = Company()
        self.assertIsNone(company.key_contact_name_for_user(user=None))
        self.assertIsNone(company.key_contact_name_for_user(user=Employee(is_staff=True)))

        select_related_mock = Mock()
        filter_mock = Mock()
        select_related_mock.filter = Mock(return_value=filter_mock)
        key_contact_mock = Mock()
        key_contact_mock.employee.name = 'key-contact'
        filter_mock.first = Mock(return_value=key_contact_mock)
        company_key_contacts_set_mock.select_related = Mock(return_value=select_related_mock)
        user_mock = Mock(company_id=1, is_staff=False)

        self.assertEqual(company.key_contact_name_for_user(user_mock), 'key-contact')
        company_key_contacts_set_mock.select_related.assert_called_once_with('employee')
        select_related_mock.filter.assert_called_once_with(
            requester_company_id=1
        )
        filter_mock.first.assert_called_once()

    @patch('core.companies.models.Company.company_key_contacts_set')
    def test_key_contact_mobile_for_user(self, company_key_contacts_set_mock):
        company = Company()
        self.assertIsNone(company.key_contact_mobile_for_user(user=None))
        self.assertIsNone(company.key_contact_mobile_for_user(user=Employee(is_staff=True)))

        select_related_mock = Mock()
        filter_mock = Mock()
        select_related_mock.filter = Mock(return_value=filter_mock)
        key_contact_mock = Mock()
        key_contact_mock.employee.mobile = 'mobile-number'
        filter_mock.first = Mock(return_value=key_contact_mock)
        company_key_contacts_set_mock.select_related = Mock(return_value=select_related_mock)
        user_mock = Mock(company_id=1, is_staff=False)

        self.assertEqual(company.key_contact_mobile_for_user(user_mock), 'mobile-number')
        company_key_contacts_set_mock.select_related.assert_called_once_with('employee')
        select_related_mock.filter.assert_called_once_with(
            requester_company_id=1
        )
        filter_mock.first.assert_called_once()


@tag('model')
class PlatformFeaturesTest(ACTestCase):
    @patch('core.companies.models.PlatformFeatures.save')
    def test_persist_grower(self, save_mock):
        save_mock.return_value = Mock()

        company = Company(id=100, type_id=1)

        platform_features = PlatformFeatures.persist(company_id=company.id, company=company)

        self.assertTrue(platform_features.is_companies_enabled())
        self.assertFalse(platform_features.is_farms_enabled())
        self.assertTrue(platform_features.is_stocks_enabled())
        self.assertTrue(platform_features.is_contracts_enabled())
        self.assertTrue(platform_features.is_orders_enabled())
        self.assertTrue(platform_features.is_movements_enabled())
        self.assertTrue(platform_features.is_invoices_enabled())
        self.assertTrue(platform_features.is_site_bookings_enabled())
        self.assertTrue(platform_features.is_site_management_enabled())

    @patch('core.companies.models.PlatformFeatures.save')
    def test_persist_broker(self, save_mock):
        save_mock.return_value = Mock()

        company = Company(id=100, type_id=2)

        platform_features = PlatformFeatures.persist(company_id=company.id, company=company)

        self.assertTrue(platform_features.is_companies_enabled())
        self.assertFalse(platform_features.is_farms_enabled())
        self.assertTrue(platform_features.is_stocks_enabled())
        self.assertTrue(platform_features.is_contracts_enabled())
        self.assertTrue(platform_features.is_orders_enabled())
        self.assertTrue(platform_features.is_movements_enabled())
        self.assertTrue(platform_features.is_invoices_enabled())
        self.assertTrue(platform_features.is_site_bookings_enabled())
        self.assertTrue(platform_features.is_site_management_enabled())

    @patch('core.companies.models.PlatformFeatures.save')
    def test_persist_logistics(self, save_mock):
        save_mock.return_value = Mock()

        company = Company(id=100, type_id=3)

        platform_features = PlatformFeatures.persist(company_id=company.id, company=company)

        self.assertTrue(platform_features.is_companies_enabled())
        self.assertTrue(platform_features.is_contracts_enabled())
        self.assertTrue(platform_features.is_orders_enabled())
        self.assertTrue(platform_features.is_movements_enabled())
        self.assertTrue(platform_features.is_site_bookings_enabled())
        self.assertTrue(platform_features.is_site_management_enabled())
        self.assertTrue(platform_features.is_invoices_enabled())
        self.assertTrue(platform_features.is_stocks_enabled())
        self.assertFalse(platform_features.is_farms_enabled())

    @patch('core.companies.models.PlatformFeatures.save')
    def test_persist_bhc(self, save_mock):
        save_mock.return_value = Mock()

        company = Company(id=100, type_id=4)

        platform_features = PlatformFeatures.persist(company_id=company.id, company=company)

        self.assertTrue(platform_features.is_companies_enabled())
        self.assertTrue(platform_features.is_contracts_enabled())
        self.assertTrue(platform_features.is_orders_enabled())
        self.assertTrue(platform_features.is_movements_enabled())
        self.assertTrue(platform_features.is_site_bookings_enabled())
        self.assertTrue(platform_features.is_site_management_enabled())
        self.assertTrue(platform_features.is_invoices_enabled())
        self.assertTrue(platform_features.is_stocks_enabled())
        self.assertFalse(platform_features.is_farms_enabled())

    @patch('core.companies.models.PlatformFeatures.save')
    def test_persist_trader(self, save_mock):
        save_mock.return_value = Mock()

        company = Company(id=100, type_id=5)

        platform_features = PlatformFeatures.persist(company_id=company.id, company=company)

        self.assertTrue(platform_features.is_companies_enabled())
        self.assertTrue(platform_features.is_contracts_enabled())
        self.assertTrue(platform_features.is_orders_enabled())
        self.assertTrue(platform_features.is_movements_enabled())
        self.assertTrue(platform_features.is_site_bookings_enabled())
        self.assertTrue(platform_features.is_site_management_enabled())
        self.assertTrue(platform_features.is_invoices_enabled())
        self.assertTrue(platform_features.is_stocks_enabled())
        self.assertFalse(platform_features.is_farms_enabled())

    @patch('core.companies.models.PlatformFeatures.save')
    def test_persist_system(self, save_mock):
        save_mock.return_value = Mock()

        company = Company(id=100, type_id=6)

        platform_features = PlatformFeatures.persist(company_id=company.id, company=company)

        self.assertTrue(platform_features.is_companies_enabled())
        self.assertTrue(platform_features.is_contracts_enabled())
        self.assertTrue(platform_features.is_orders_enabled())
        self.assertTrue(platform_features.is_movements_enabled())
        self.assertTrue(platform_features.is_site_bookings_enabled())
        self.assertTrue(platform_features.is_site_management_enabled())
        self.assertTrue(platform_features.is_invoices_enabled())
        self.assertFalse(platform_features.is_farms_enabled())
        self.assertTrue(platform_features.is_stocks_enabled())

    @patch('core.companies.models.PlatformFeatures.save')
    def test_persist_specific(self, save_mock):
        save_mock.return_value = Mock()

        company = Company(id=100, type_id=3)

        platform_features = PlatformFeatures.persist(
            company_id=company.id,
            company=company,
            features=['site_bookings'],
        )

        self.assertFalse(platform_features.is_companies_enabled())
        self.assertFalse(platform_features.is_contracts_enabled())
        self.assertFalse(platform_features.is_orders_enabled())
        self.assertFalse(platform_features.is_movements_enabled())
        self.assertTrue(platform_features.is_site_bookings_enabled())
        self.assertFalse(platform_features.is_site_management_enabled())
        self.assertFalse(platform_features.is_invoices_enabled())
        self.assertFalse(platform_features.is_farms_enabled())
        self.assertFalse(platform_features.is_stocks_enabled())

    def test_build_specific(self):
        platform_features = PlatformFeatures()

        platform_features.build_specific(['site_bookings'])

        self.assertTrue(platform_features.is_site_bookings_enabled())
        self.assertFalse(platform_features.is_companies_enabled())
        self.assertFalse(platform_features.is_contracts_enabled())
        self.assertFalse(platform_features.is_orders_enabled())
        self.assertFalse(platform_features.is_movements_enabled())
        self.assertFalse(platform_features.is_site_management_enabled())
        self.assertFalse(platform_features.is_invoices_enabled())
        self.assertFalse(platform_features.is_farms_enabled())
        self.assertFalse(platform_features.is_stocks_enabled())

    def test_build_for_type_logistic(self):
        platform_features = PlatformFeatures()
        platform_features.build_for_type(LOGISTICS_LITE_PLAN)
        self.assertFalse(platform_features.is_invoices_enabled())
        self.assertFalse(platform_features.is_farms_enabled())
        self.assertFalse(platform_features.is_stocks_enabled())
        self.assertTrue(platform_features.is_movements_enabled())

        self.assertEqual(platform_features.plan_type, LOGISTICS_LITE_PLAN)


    def test_build_for_type_premium(self):
        platform_features = PlatformFeatures()
        platform_features.build_for_type(PREMIUM_PLAN)
        self.assertTrue(platform_features.is_companies_enabled())
        self.assertTrue(platform_features.is_site_bookings_enabled())
        self.assertTrue(platform_features.is_invoices_enabled())
        self.assertTrue(platform_features.is_stocks_enabled())
        self.assertTrue(platform_features.is_movements_enabled())

        self.assertEqual(platform_features.plan_type, PREMIUM_PLAN)

    def test_build_for_type_company_change_farm_true(self):
        platform_features = PlatformFeatures(farms=False)
        platform_features._enable_broker_grower_common = Mock()
        platform_features.build_for_type(PREMIUM_PLAN, GROWER_TYPE_ID)
        platform_features._enable_broker_grower_common.assert_called_once()
        self.assertEqual(platform_features.plan_type, PREMIUM_PLAN)

    def test_build_for_type_company_change_farm_false(self):
        platform_features = PlatformFeatures()
        platform_features.build_logistics = Mock()
        platform_features.build_for_type(PREMIUM_PLAN, LOGISTICS_TYPE_ID)
        platform_features.build_logistics.assert_called_once()
        self.assertEqual(platform_features.plan_type, PREMIUM_PLAN)



    def test_persist_with_exception(self):
        platform_features = PlatformFeatures()

        with self.assertRaises(Company.DoesNotExist):
            platform_features.persist(company_id=100, raise_exception=True)

@tag('model')
class CompanyTest(ACTestCase):
    def setUp(self):
        super().setUp()
        self.company_valid = CompanyFactory()
        self.company_invalid = Company(abn="***********")
        self.employee = Employee(
            **EMPLOYEE_VALID_MOCK_DATA,
            company=self.company_valid
        )
        self.employee.full_clean()
        self.grower_company_valid = CompanyFactory(type_id=1, owner_company_id=self.company_valid.id)
        self.grower_employee = EmployeeFactory(company=self.grower_company_valid, type_id=4)
        self.grower_admin = EmployeeFactory(company=self.grower_company_valid, type_id=1)
        self.location = AddressFactory()

    def test_get_tagged_ngr_id(self):
        company = CompanyFactory()
        ngr = NgrFactory(company=company, tags=['payable'])
        self.assertEqual(company.get_tagged_ngr_id('payable'), ngr.id)
        self.assertIsNone(company.get_tagged_ngr_id('test'))

    def test_transaction_config_uniqueness(self):
        company = CompanyFactory()

        self.assertTrue(company.persisted)

        company.transaction_code = 'BAR'
        company.transaction_reference_number_configs = {'contract': {'prefix': 'C', 'length': 6, 'start_from': 1}}
        company.save()

        self.assertEqual(company.errors, {})

        company2 = CompanyFactory.build(
            transaction_code='BAR',
            transaction_reference_number_configs={'contract': {'prefix': 'C', 'length': 6, 'start_from': 1}}
        )
        company2.save()
        self.assertFalse(company2.persisted)
        self.assertEqual(company2.errors['transaction_code'], ['Transaction Code + prefix combination is duplicate.'])

        company2.errors = {}
        company2.transaction_code = 'BA'
        company2.transaction_reference_number_configs['contract']['prefix'] = 'RC'
        company2.save()
        self.assertFalse(company2.persisted)
        self.assertEqual(company2.errors['transaction_code'], ['Transaction Code + prefix combination is duplicate.'])

    def test_make_transaction_participator(self):
        company = Company()
        company.save = Mock()
        self.assertFalse(company.transaction_participation)

        company.owner_company_id = 1
        company.make_transaction_participator()
        self.assertFalse(company.transaction_participation)
        company.save.assert_not_called()

        company.owner_company_id = None
        company.make_transaction_participator()
        self.assertTrue(company.transaction_participation)
        company.save.assert_called_once()

    def test_get_overview(self):
        company = CompanyFactory()
        overview = company.get_overview()
        self.assertEqual(
            overview, {
            'highlights': ANY,
            'id': company.id,
            'abn': company.abn,
            'entity_name': company.entity_name,
            'business_name': company.business_name,
        })

    @patch('core.services.external.sftp.SFTP')
    def test_get_sftp_service(self, mock_sftp):
        sftp_data = {
            'hostname': 'test_host',
            'location': 'test_location',
            'username': 'test_user',
            'password': 'test_password',
        }
        company = CompanyFactory(_sftp_configuration=sftp_data)

        mock_connection_instance = mock_sftp.return_value
        result = company.get_sftp_service()

        mock_sftp.assert_called_once_with(
            hostname='test_host',
            username='test_user',
            password='test_password'
        )
        self.assertEqual(result, mock_connection_instance)

        company = CompanyFactory()
        self.assertIsNone(company.get_sftp_service())

    def test_sftp_location_path(self):
        sftp_data = {
            'hostname': 'test_host',
            'location': 'test_location',
            'username': 'test_user',
            'password': 'test_password',
        }
        company = CompanyFactory(_sftp_configuration=sftp_data)
        self.assertEqual(company.sftp_location_path, 'test_location')

    @patch('core.services.external.sftp.SFTP')
    def test_push_to_company_SFTP(self, mock_sftp):
        sftp_data = {
            'hostname': 'test_host',
            'location': 'test_location',
            'username': 'test_user',
            'password': 'test_password',
        }
        company = CompanyFactory(_sftp_configuration=sftp_data)

        mock_connection_instance = mock_sftp.return_value
        result = company.get_sftp_service()

        mock_sftp.assert_called_once_with(
            hostname='test_host',
            username='test_user',
            password='test_password'
        )
        self.assertEqual(result, mock_connection_instance)
        file_mock = Mock()
        file_mock.name = 'file_test'
        company.push_to_company_SFTP(file_mock)
        mock_connection_instance.put_file.assert_called_once_with(file_mock.name, company.sftp_location_path)

    def test_is_ca(self):
        company = Company(country_id=CANADA_COUNTRY_ID)
        self.assertTrue(company.is_ca)
        self.assertFalse(company.is_nz)

    def test_is_nz(self):
        company = Company(country_id=NZ_COUNTRY_ID)
        self.assertTrue(company.is_nz)
        self.assertFalse(company.is_ca)

    def test_country_code_for_aus(self):
        company = Company(country_id=AUSTRALIA_COUNTRY_ID)
        self.assertEqual(company.country_code, AUSTRALIA_COUNTRY_CODE)

    def test_country_code_for_us(self):
        company = Company(country_id=USA_COUNTRY_ID)
        self.assertEqual(company.country_code, USA_COUNTRY_CODE)

    def test_country_code_for_ca(self):
        company = Company(country_id=CANADA_COUNTRY_ID)
        self.assertEqual(company.country_code, CANADA_COUNTRY_CODE)

    def test_country_code_for_nz(self):
        company = Company(country_id=NZ_COUNTRY_ID)
        self.assertEqual(company.country_code, NZ_COUNTRY_CODE)

    @patch('core.companies.models.Company._get_impex_docs_token')
    def test_verify_impex_docs_connection(self, mock_connection):
        company = CompanyFactory(impex_docs_enabled=False)
        self.assertEqual(company.verify_impex_docs_connection, False)

        company = CompanyFactory(impex_docs_enabled=True)
        mock_connection.return_value = 'impexdocsconnection'
        self.assertEqual(company.verify_impex_docs_connection, True)
        mock_connection.assert_called_once()

    @patch('core.companies.models.Company.impex_docs_connection', new_callable=PropertyMock)
    def test_get_impex_docs_token(self, mock_impex_docs_connection):
        company = CompanyFactory()
        mock_connection = Mock()
        mock_token_data = Mock()
        mock_connection.refresh.return_value = mock_token_data
        mock_impex_docs_connection.return_value = mock_connection

        result = company._get_impex_docs_token()

        mock_connection.refresh.assert_called_once()
        self.assertEqual(result, mock_token_data)

    @patch('core.companies.models.Company.verify_impex_docs_connection', new_callable=PropertyMock)
    @patch('core.companies.models.Company.impex_docs_connection', new_callable=PropertyMock)
    def test_has_impex_docs_connection(self, mock_impex_docs_connection, mock_verify_impex_docs_connection):
        company = CompanyFactory(impex_docs_enabled=False)
        self.assertEqual(company.has_impex_docs_connection, False)

        company = CompanyFactory(impex_docs_enabled=True)
        mock_verify_impex_docs_connection.return_value = True
        mock_connection = Mock()
        mock_connection.is_expired = False
        mock_impex_docs_connection.return_value = mock_connection
        self.assertTrue(company.has_impex_docs_connection)

    @patch('core.companies.models.Company.get_impex_docs_params')
    @patch('core.companies.models.Company.impex_docs_connection', new_callable=PropertyMock)
    def test_get_impex_docs_service(self, mock_impex_docs_connection, mock_get_impex_docs_params):
        company = CompanyFactory()
        mock_connection = Mock()
        mock_connection.client_id = 'test_client_id'
        mock_connection.client_secret = 'test_client_secret'
        mock_impex_docs_connection.return_value = mock_connection
        impex_docs_instance = company.get_impex_docs_service()
        self.assertEqual(impex_docs_instance.client_id, mock_connection.client_id)
        self.assertEqual(impex_docs_instance.client_secret, mock_connection.client_secret)

        result = {
            'token': 'test_token',
            'client_id': 'test_client_id',
            'client_secret': 'test_client_secret',
            'user_id': 'test_user_id',
            'user_email': 'test_user_email'
        }
        mock_get_impex_docs_params.return_value = result
        impex_docs_instance_with_validate = company.get_impex_docs_service(validate=True)
        self.assertEqual(impex_docs_instance_with_validate.token, result['token'])
        self.assertEqual(impex_docs_instance_with_validate.client_id, result['client_id'])
        self.assertEqual(impex_docs_instance_with_validate.client_secret, result['client_secret'])
        self.assertEqual(impex_docs_instance_with_validate.user_id, result['user_id'])
        self.assertEqual(impex_docs_instance_with_validate.user_email, result['user_email'])

    @patch('core.companies.models.Company.impex_docs_connection', new_callable=PropertyMock)
    @patch('core.companies.models.Company._get_impex_docs_token')
    @patch('core.companies.models.Company.has_impex_docs_connection', new_callable=PropertyMock)
    def test_get_impex_docs_params(self, mock_has_impex_docs_connection, mock_get_token, mock_impex_docs_connection):
        company = CompanyFactory()

        mock_connection = Mock()
        mock_connection.client_id = 'test_client_id'
        mock_connection.client_secret = 'test_client_secret'
        mock_connection.user_id = 'test_user_id'
        mock_connection.user_email = 'test_user_email'

        mock_impex_docs_connection.return_value = mock_connection
        mock_has_impex_docs_connection.return_value = True
        mock_get_token.return_value = 'test_token'

        expected_result = {
            'token': 'test_token', 'client_id': 'test_client_id', 'client_secret': 'test_client_secret',
            'user_id': 'test_user_id', 'user_email': 'test_user_email'
        }
        result = company.get_impex_docs_params()
        self.assertEqual(result, expected_result)
        mock_get_token.assert_called_once()

        mock_has_impex_docs_connection.return_value = False
        result = company.get_impex_docs_params()
        self.assertEqual(result, False)

    @patch('core.companies.models.Company.verify_xero_connection', new_callable=PropertyMock)
    @patch('core.companies.models.Company.xero_connection', new_callable=PropertyMock)
    def test_is_valid_xero_connection(self, mock_xero_connection, mock_verify_xero_connection):
        company = CompanyFactory(xero_enabled=False)
        self.assertFalse(company.is_valid_xero_connection)

        company = CompanyFactory(xero_enabled=True, xero_client_id='test_xero', xero_client_secret='test_xero_secret')
        mock_verify_xero_connection.return_value = True
        mock_connection = Mock()
        mock_connection.is_expired = False
        mock_xero_connection.return_value = mock_connection
        self.assertTrue(company.is_valid_xero_connection)

    @patch('core.companies.models.Company._get_xero_token')
    def test_verify_xero_connection(self, mock_xero_token):
        company = CompanyFactory(xero_enabled=False)
        self.assertFalse(company.verify_xero_connection)

        company = CompanyFactory(xero_enabled=True, xero_client_id='test_xero', xero_client_secret='test_xero_secret')
        mock_xero_token.return_value = 'xero_token'
        self.assertTrue(company.verify_xero_connection)
        mock_xero_token.assert_called_once()

    @patch('core.companies.models.Company._get_xero_token')
    @patch('core.companies.models.Company.is_valid_xero_connection', new_callable=PropertyMock)
    def test_get_xero_params(self, mock_impex_docs_connection, mock_get_token):
        company = CompanyFactory(
            xero_client_id='xero_client', xero_client_secret='xero_secret', xero_tenant_id='xero_tenant'
        )

        mock_impex_docs_connection.return_value = True
        mock_get_token.return_value = 'test_token'

        expected_result = {
            'token': 'test_token',
            'client_id': company.xero_client_id,
            'client_secret': company.xero_client_secret,
            'tenant_id': company.xero_tenant_id
        }
        result = company.get_xero_params()
        self.assertEqual(result, expected_result)
        mock_get_token.assert_called_once()

        mock_impex_docs_connection.return_value = False
        result = company.get_xero_params()
        self.assertEqual(result, False)

    @patch('core.companies.models.Company.xero_connection', new_callable=PropertyMock)
    def test_get_xero_token(self, mock_xero_connection):
        company = CompanyFactory()
        mock_connection = Mock()
        mock_token_data = Mock()
        mock_connection.refresh.return_value = mock_token_data
        mock_xero_connection.return_value = mock_connection

        result = company._get_xero_token()

        mock_connection.refresh.assert_called_once()
        self.assertEqual(result, mock_token_data)

    @patch('core.companies.models.Company.get_external_booking_params')
    @patch('core.companies.models.Company.external_booking_connection', new_callable=PropertyMock)
    def test_get_external_booking_service(self, mock_external_booking_connection, mock_get_external_booking_params):
        company = CompanyFactory()
        mock_connection = Mock()
        mock_connection.encoded_client_credentials = 'test_client_id'
        mock_connection.refresh_token = 'test_refresh_token'
        mock_connection.access_token = 'test_access_token'
        mock_connection.base_url = 'base_url'
        mock_connection.url_paths = {
            'token':'url_path',
            'booking': 'booking_id', 'update_booking': 'update', 'cancel_booking': 'cancel',
            'expire_booking': 'expire', 'truck_vendor_dec': 'vendor_dec'
        }
        mock_external_booking_connection.return_value = mock_connection
        external_docs_instance = company.get_external_booking_service()
        self.assertEqual(external_docs_instance.client_credentials, mock_connection.encoded_client_credentials)
        self.assertEqual(external_docs_instance.access_token, mock_connection.access_token)
        self.assertEqual(external_docs_instance.refresh_token, mock_connection.refresh_token)
        self.assertEqual(external_docs_instance.api_url, mock_connection.base_url)

        result = {
            'client_credentials': mock_connection.encoded_client_credentials,
            'refresh_token': mock_connection.refresh_token,
            'access_token': mock_connection.access_token,
            'base_url': mock_connection.base_url,
            'url_paths': mock_connection.url_paths,
        }
        mock_get_external_booking_params.return_value = result
        external_docs_instance_with_validate = company.get_external_booking_service(validate=True)
        self.assertEqual(external_docs_instance_with_validate.client_credentials,
                         mock_connection.encoded_client_credentials)
        self.assertEqual(external_docs_instance_with_validate.access_token, mock_connection.access_token)
        self.assertEqual(external_docs_instance_with_validate.refresh_token, mock_connection.refresh_token)
        self.assertEqual(external_docs_instance_with_validate.api_url, mock_connection.base_url)


    @patch('core.companies.models.Company.external_booking_connection', new_callable=PropertyMock)
    def test_get_external_booking_params(self, mock_external_booking_connection):
        company = CompanyFactory()

        mock_connection = Mock()
        mock_connection.encoded_client_credentials = 'test_client_id'
        mock_connection.base_url = 'test_base_url'
        mock_connection.url_paths = 'test_url_paths'

        mock_token = Mock()
        mock_token.refresh_token = 'test_refresh_token'
        mock_token.access_token = 'test_access_token'
        mock_connection.refresh_tokens.return_value = mock_token

        mock_external_booking_connection.return_value = mock_connection

        expected_result = {
            'client_credentials': mock_connection.encoded_client_credentials,
            'refresh_token': mock_token.refresh_token,
            'access_token': mock_token.access_token,
            'base_url': mock_connection.base_url,
            'url_paths': mock_connection.url_paths,
        }
        result = company.get_external_booking_params()
        self.assertEqual(result, expected_result)

        mock_external_booking_connection.return_value = False
        result = company.get_external_booking_params()
        self.assertEqual(result, False)

    def test_to_admin_stocks_upload_csv_row_sites(self):
        site = FarmFactory()
        market_zone_name = get(site, 'market_zone.name')
        region_name = get(site, 'region.name')
        expected_row = [site.company.name, site.company_id, site.name, site.id,
                        site.address.address, market_zone_name, region_name]
        self.assertEqual(Company.to_admin_stocks_upload_csv_row(SITES, site), expected_row)

    def test_to_admin_stocks_upload_csv_row_ngr(self):
        item = NgrFactory()
        expected_row = [
            get(item, 'company_name'),
            get(item, 'company_id'),
            get(item, 'ngr_number'),
            get(item, 'id')
        ]
        self.assertEqual(Company.to_admin_stocks_upload_csv_row(NGR, item), expected_row)

    def test_to_admin_stocks_upload_csv_row_storages(self):
        item = StorageFactory()
        expected_row = [
            get(item, 'operator_name'),
            get(item, 'operator_id'),
            get(item, 'farm_name'),
            get(item, 'farm_id'),
            get(item, 'name'),
            get(item, 'id'),
            get(item, 'type_name'),
            get(item, 'size'),
        ]
        self.assertEqual(Company.to_admin_stocks_upload_csv_row(STORAGES, item), expected_row)

    def test_to_admin_stocks_upload_csv_row_employees(self):
        item = EmployeeFactory()
        expected_row = [
            get(item, 'company.name'),
            get(item, 'company_id'),
            get(item, 'name'),
            get(item, 'id'),
            get(item, 'type.display_name'),
            get(item, 'title'),
            get(item, 'mobile'),
            get(item, 'email'),
        ]
        self.assertEqual(Company.to_admin_stocks_upload_csv_row(EMPLOYEES, item), expected_row)

    def test_to_admin_stocks_upload_csv_row_unknown_data_type(self):
        item = {'company_name': 'Unknown'}
        self.assertEqual(Company.to_admin_stocks_upload_csv_row('UNKNOWN', item), [])

    def test_sync_ngr_from_portal(self):
        from core.jobs.models import Job
        company = CompanyFactory()
        company.sync_ngr_from_portal('123')
        params = {'company_id': company.id, 'ngr_number': '123'}
        jobs = Job.objects.filter(type='sync_ngr_from_portal', params=params)
        self.assertTrue(jobs.exists())

    @patch('core.loads.models.Load.get_pending_warehouse_invoice_items')
    def test_get_warehouse_invoice_pending_loads_for_outload(self, mock_get_pending):
        company = CompanyFactory(warehouse_charged_at=Load.OUTLOAD, warehouse_inload_charged_from=None)
        ngr = NgrFactory(company=company)
        commodity_id = 1
        start_date = "2024-01-01"
        end_date = "2024-01-31"

        # Mock return values for Load methods
        mock_get_pending.return_value = (["outload1"], ["outload1"], ["throughput_out1"], ["throughput_in1"])

        result = company.get_warehouse_invoice_pending_loads(
            start_date, end_date, ngr.id, ngr.company_id, commodity_id
        )
        self.assertEqual(result, (['outload1'], ['outload1'], ['throughput_out1'], ['throughput_in1']))
        mock_get_pending.assert_called_once_with(
            company, ngr.company_id, start_date, end_date, ngr.id, commodity_id, None
        )

    @patch('core.loads.models.Load.get_pending_warehouse_invoice_items')
    def test_get_warehouse_invoice_pending_loads_for_inload(self, mock_get_pending):
        company = CompanyFactory(warehouse_charged_at=Load.INLOAD, warehouse_inload_charged_from=None)
        ngr = NgrFactory(company=company)
        commodity_id = 1
        start_date = "2024-01-01"
        end_date = "2024-01-31"
        mock_get_pending.return_value = (["outload1"], ["inload1"], ["throughput_out1"], ["throughput_in1"])

        result = company.get_warehouse_invoice_pending_loads(
            start_date, end_date, ngr.id, ngr.company_id, commodity_id
        )
        self.assertEqual(
            result, (['outload1'], ['inload1'], ['throughput_out1'], ['throughput_in1'])
        )
        mock_get_pending.assert_has_calls([
            call(company, ngr.company_id, start_date, end_date, ngr.id, commodity_id, None)
        ])

    @patch('core.loads.models.Load.get_pending_warehouse_invoice_items')
    def test_get_warehouse_invoice_pending_loads_for_transfers(self, mock_get_pending):
        company = CompanyFactory(warehouse_charged_at=Load.INLOAD, warehouse_inload_charged_from=None)
        ngr = NgrFactory(company=company)
        commodity_id = 1
        start_date = "2024-01-01"
        end_date = "2024-01-31"
        mock_get_pending.return_value = (["outload2"], ["inload2"], ["throughput_out2"], ["throughput_in2"])

        company.warehouse_charged_at = 'outload/transfer'
        company.save()
        result = company.get_warehouse_invoice_pending_loads(
            start_date, end_date, ngr.id, ngr.company_id, commodity_id
        )
        self.assertEqual(
            result, (['outload2'], ['inload2'],
                     ['throughput_out2'], ['throughput_in2'])
        )
        mock_get_pending.assert_has_calls([
            call(company, ngr.company_id, start_date, end_date, ngr.id, commodity_id, None)
        ])

    def test_make_mobile_participator(self):
        company = Company()
        company.save = Mock()
        self.assertFalse(company.mobile_participation)

        company.owner_company_id = 1
        company.make_mobile_participator()
        self.assertFalse(company.mobile_participation)
        company.save.assert_not_called()

        company.owner_company_id = None
        company.make_mobile_participator()
        self.assertTrue(company.mobile_participation)
        company.save.assert_called_once()

    def test_company_name(self):
        self.assertEqual(
            Company(business_name='foobar').company_name,
            Company(business_name='foobar').name,
        )

    def test_is_grower(self):
        self.assertTrue(Company(type_id=1).is_grower)
        self.assertFalse(Company(type_id=2).is_grower)
        for i in [2, 3, 4, 5, 6]:
            self.assertFalse(Company(type_id=i).is_grower)

    def test_is_broker(self):
        self.assertTrue(Company(type_id=2).is_broker)
        for i in [1, 3, 4, 5, 6]:
            self.assertFalse(Company(type_id=i).is_broker)

    def test_is_logistics(self):
        self.assertTrue(Company(type_id=3).is_logistics)
        for i in [1, 2, 4, 5, 6]:
            self.assertFalse(Company(type_id=i).is_logistics)

    def test_is_trader(self):
        self.assertTrue(Company(type_id=5).is_trader)
        for i in [1, 2, 3, 4, 6]:
            self.assertFalse(Company(type_id=i).is_trader)

    def test_is_system(self):
        self.assertTrue(Company(type_id=6).is_system)
        for i in [1, 2, 3, 4, 5]:
            self.assertFalse(Company(type_id=i).is_system)

    def test_is_agrichain(self):
        self.assertTrue(Company(type_id=6, abn='***********').is_agrichain)
        for i in [1, 2, 3, 4, 5]:
            self.assertFalse(Company(type_id=i, abn='***********').is_agrichain)

    @patch('core.common.constants')
    def test_is_allowed_as_buyer_for_pool_contract(self, constant_mock):
        self.assertFalse(Company(id=1).is_allowed_as_buyer_for_pool_contract)
        constant_mock.ABNS_FOR_POOL_CONTRACTS = ['1212121212']
        self.assertTrue(
            Company(id=1, abn='1212121212').is_allowed_as_buyer_for_pool_contract
        )

    def test_is_registered_grower(self):
        self.assertTrue(Company(type_id=1, owner_company_id=None).is_registered_grower)
        self.assertFalse(Company(type_id=1, owner_company_id=1).is_registered_grower)
        for i in [2, 3, 4, 5, 6]:
            self.assertFalse(Company(type_id=i, owner_company_id=None).is_registered_grower)

    def test_is_unregistered_grower(self):
        self.assertTrue(Company(type_id=1, owner_company_id=1).is_unregistered_grower)
        self.assertFalse(Company(type_id=1, owner_company_id=None).is_unregistered_grower)
        for i in [2, 3, 4, 5, 6]:
            self.assertFalse(Company(type_id=i, owner_company_id=1).is_unregistered_grower)

    def test_is_broker_or_logistics_or_trader(self):
        for i in [2, 3, 5]:
            self.assertTrue(Company(type_id=i).is_broker_or_logistics_or_trader)
        for i in [1, 4, 6]:
            self.assertFalse(Company(type_id=i).is_broker_or_logistics_or_trader)

    def test_is_registered_broker_or_logistics_or_trader(self):
        for i in [2, 3, 5]:
            self.assertTrue(
                Company(
                    type_id=i, owner_company_id=None
                ).is_registered_broker_or_logistics_or_trader
            )
        for i in [2, 3, 5]:
            self.assertFalse(
                Company(
                    type_id=i, owner_company_id=1
                ).is_registered_broker_or_logistics_or_trader
            )
        for i in [1, 4, 6]:
            self.assertFalse(
                Company(
                    type_id=i, owner_company_id=None
                ).is_registered_broker_or_logistics_or_trader
            )
        for i in [1, 4, 6]:
            self.assertFalse(
                Company(
                    type_id=i, owner_company_id=1
                ).is_registered_broker_or_logistics_or_trader
            )

    def test_is_unregistered_broker_or_logistics_or_trader(self):
        for i in [2, 3, 5]:
            self.assertTrue(
                Company(
                    type_id=i, owner_company_id=1
                ).is_unregistered_broker_or_logistics_or_trader
            )
        for i in [2, 3, 5]:
            self.assertFalse(
                Company(
                    type_id=i, owner_company_id=None
                ).is_unregistered_broker_or_logistics_or_trader
            )
        for i in [1, 4, 6]:
            self.assertFalse(
                Company(
                    type_id=i, owner_company_id=1
                ).is_unregistered_broker_or_logistics_or_trader
            )
        for i in [1, 4, 6]:
            self.assertFalse(
                Company(
                    type_id=i, owner_company_id=None
                ).is_unregistered_broker_or_logistics_or_trader
            )

    def test_system_company_manager(self):
        self.assertTrue(
            isinstance(Company.moderators, SystemCompanyManager)
        )
        self.assertEqual(
            Company.moderators.count(),
            4
        )
        self.assertEqual(
            sorted(list(Company.moderators.values_list('id', flat=True))),
            sorted([1, -1, -2, -3])
        )

    def test_non_system_company_manager(self):
        Company.assign_non_system_manager()
        self.assertTrue(
            isinstance(Company.objects, NonSystemCompanyManager)
        )

    def test_default_company_manager(self):
        Company.assign_system_manager()
        self.assertTrue(
            isinstance(Company.objects, DefaultCompanyManager)
        )
        Company.assign_non_system_manager()

    def test_is_registered(self):
        self.assertFalse(
            Company(owner_company_id=self.company_valid.id).is_registered
        )
        self.assertTrue(
            Company(owner_company_id=None).is_registered
        )

    def test_is_registered_or_managed_by_registered(self):
        self.assertFalse(
            Company(owner_company_id=self.company_valid.id).is_registered_or_managed_by_registered
        )
        self.assertTrue(
            Company(owner_company_id=None).is_registered_or_managed_by_registered
        )

    def test_is_transaction_participator_or_managed_by_transaction_participator(self):
        self.assertFalse(
            Company(transaction_participation=False).is_transaction_participator_or_managed_by_transaction_participator
        )
        self.assertTrue(
            Company(transaction_participation=True).is_transaction_participator_or_managed_by_transaction_participator
        )

    def test_is_registered_text(self):
        self.assertEqual(
            Company(owner_company_id=self.company_valid.id).is_registered_text,
            'No'
        )
        self.assertEqual(
            Company(owner_company_id=None).is_registered_text,
            'Yes'
        )

    def test_name(self):
        self.assertEqual(
            Company(business_name='foo', entity_name='foobar').name,
            'foo'
        )

    def test_create_invalid_address_fail(self):
        data = {
            'abn': '***********',
            'business_name': 'Foo Corporation',
            'address': {
                'latitude': 123.45,
                'longitude': 54.321},
            'mobile': '0234567891',
            'website': 'ag.ri',
            'type_id': 2,
        }
        company = Company.create_with_location(data)
        self.assertIsNotNone(company.errors)
        self.assertIn('address', company.errors)

    def test_update_success(self):
        data = {
            'abn': '***********',
            'business_name': 'Bar Corporation Updated',
            'address': {
                'address': 'Bar Pur',
                'latitude': 111.22,
                'longitude': 44.22},
            'mobile': '0234567891',
            'type_id': 2,
        }
        company = CompanyFactory()
        _company_updated = Company.update_with_location(company.id, data)
        self.assertEqual(company.errors, {})
        self.assertEqual(_company_updated.business_name, 'Bar Corporation Updated')

    @patch('core.services.internal.postgres.PostgresQL')
    def test_internal_reference_number(self, postgres_service_mock):
        postgres_service_mock.create_seq = Mock()
        postgres_service_mock.update_seq = Mock()
        postgres_service_mock.drop_seq = Mock()
        postgres_service_mock.last_value = Mock(return_value=1)
        company = CompanyFactory()
        company.transaction_code = "MNC"
        company.transaction_reference_number_configs = {}
        company.save()

        self.assertEqual(company.is_transaction_code_exists, True)
        self.assertEqual(company.is_contract_internal_reference_on, False)

        company.transaction_reference_number_configs = {'contract': {'prefix': "", 'start_from': 5, 'length': 6}}
        company.save()

        self.assertEqual(company.is_transaction_code_exists, True)
        self.assertEqual(company.is_contract_internal_reference_on, True)

    @patch('core.services.external.abn.ABN')
    def test_save_with_abn_lookup(self, abn_service_mock):
        abn_service_mock.has_constructor = False
        abn_lookup_mock = SimpleNamespace(entity_name='foobar')
        abn_service_mock.get_details = Mock(return_value=abn_lookup_mock)

        params = factory.build(dict, FACTORY_CLASS=CompanyFactory, **{'abn': '***********'})
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        params.pop('entity_name')
        company = Company.get_or_create(params)

        self.assertIsNotNone(company.id)
        self.assertEqual(company.entity_name, 'foobar')
        abn_service_mock.get_details.assert_called_with(params['abn'])

    @patch('core.services.external.abn.ABN')
    def test_save_with_abn_error(self, abn_service_mock):
        abn_service_mock.has_constructor = False
        abn_lookup_mock = SimpleNamespace(errors={'abn': ['Failed coz I wanted it to!']})
        abn_service_mock.get_details = Mock(return_value=abn_lookup_mock)

        params = factory.build(dict, FACTORY_CLASS=CompanyFactory, **{'abn': '***********'})
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        params.pop('entity_name')
        company = Company.get_or_create(params)

        self.assertIsNone(company.id)
        self.assertFalse(company.entity_name)
        self.assertEqual(
            company.errors,
            {
                'abn': ['Failed coz I wanted it to!'],
                'entity_name': ['This field cannot be blank.'],
            }
        )
        abn_service_mock.get_details.assert_called_once_with(params['abn'])

    def test_required_fields(self):
        with self.assertRaises(ValidationError) as ex:
            self.company_invalid.full_clean()
        self.assertEqual(
            ex.exception.message_dict['address'],
            ['This field cannot be null.']
        )
        for _attr in ['entity_name', 'mobile']:
            self.assertEqual(
                ex.exception.message_dict[_attr],
                ['This field cannot be blank.']
            )

    def test_employees_exist(self):
        self.assertFalse(self.company_valid.employees_exist)

        self.employee.save()

        self.assertTrue(self.company_valid.employees_exist)

    @patch('core.companies.models.Company.employee_set')
    def test_get_company_admins(self, employee_set_mock):
        employee_set_mock.filter = Mock(return_value='admins')

        self.assertEqual(Company(id=1).get_company_admins(), 'admins')
        employee_set_mock.filter.assert_called_once_with(type_id=1, is_active=True)

    def test_get(self):
        params = {
            'id': self.company_valid.id
        }

        self.assertEqual(Company.get_or_create(params), self.company_valid)

    @patch('core.services.external.abn.ABN')
    def test_companies(self, abn_service_mock):
        abn_service_mock.has_constructor = False
        abn_lookup_mock = SimpleNamespace(entity_name='foobar')
        abn_service_mock.get_details = Mock(return_value=abn_lookup_mock)
        self.assertEqual(len(self.company_valid.companies), 0)

        company1 = CompanyFactory(owner_company_id=self.company_valid.id)
        self.company_valid.add_company_to_directory(company1.id)

        CompanyFactory(abn='***********', owner_company_id=123)

        self.assertGreaterEqual(len(self.company_valid.companies), 1)
        self.assertEqual(self.company_valid.companies[0].id, company1.id)

    def test_farms_for_broker(self):
        self.assertEqual(len(self.grower_company_valid.farms), 0)
        self.farm = Farm(
            name='Farm 1',
            address=self.location,
            market_zone_id=1,
            company_id=self.grower_company_valid.id,
        )
        self.farm.save()

        self.assertEqual(len(self.grower_company_valid.farms), 1)
        self.assertEqual(self.grower_company_valid.farms[0].id, self.farm.id)

    def test_farms_for_grower(self):
        grower = CompanyFactory(type_id=1)

        self.assertEqual(len(grower.farms), 0)

        farm = FarmFactory(company=grower)

        self.assertEqual(len(grower.farms), 1)
        self.assertEqual(grower.farms[0].id, farm.id)

    def test_farms_for_others(self):
        company = CompanyFactory(type_id=3)

        self.assertEqual(len(company.farms), 0)

        FarmFactory(company=company)

        self.assertEqual(len(company.farms), 0)

    @patch('core.companies.models.apps.get_model')
    def test_is_farm_creator(self, get_model_mock):
        farm_klass_mock = Mock()
        manager_mock = Mock()
        filter_mock = Mock()
        filter_mock.exists = Mock(return_value=True)
        manager_mock.filter = Mock(return_value=filter_mock)
        farm_klass_mock.objects = manager_mock
        get_model_mock.return_value = farm_klass_mock

        self.assertTrue(Company(id=100).is_farm_creator(200))

        get_model_mock.assert_called_once_with('farms', 'Farm')
        manager_mock.filter.assert_called_once_with(id=200, created_by__company_id=100)
        filter_mock.exists.assert_called_once()

    def test_is_managing_farm(self):
        self.broker_company = CompanyFactory(type_id=2)
        farm = FarmFactory(broker_company=self.broker_company, company=self.grower_company_valid)
        farm.create_broker_raised_grower_accepted_request()
        self.assertTrue(self.broker_company.is_managing_farm(farm.id))
        self.assertFalse(self.grower_company_valid.is_managing_farm(farm.id))

    def test_delete_with_no_employees(self):
        self.assertTrue(self.company_valid.delete())
        self.assertEqual(Company.objects.count(), 1)

    def test_delete_with_employees(self):
        self.employee.save()

        self.assertFalse(self.company_valid.delete())
        self.assertEqual(Company.objects.count(), 2)

    def test_upsert_farm(self):
        _farm = self.company_valid.upsert_farm(
            {
                'name': 'Farm 1',
                'address': {
                    'name': 'Farm 1',
                    'latitude': 123.45,
                    'longitude': 54.321,
                    'address': 'farm 1 address, australia',
                    'location_type': 'farm'
                },
                'brokerages': [{
                    'fee_type': 'Fee Per MT',
                    'rate': 1,
                    'total_fee': 10,
                }],
                'market_zone_id': 1,
            },
            current_user=self.employee
        )

        self.assertIsNotNone(_farm.id)
        self.assertEqual(self.company_valid.farm_set.count(), 1)
        self.assertEqual(self.company_valid.farm_set.get(), _farm)

        _farm2 = self.grower_company_valid.upsert_farm(
            {
                'name': 'Farm 2',
                'address': {
                    'name': 'Farm 2',
                    'latitude': 123.45,
                    'longitude': 54.321,
                    'address': 'farm 1 address, australia',
                    'location_type': 'farm'
                },
                'market_zone_id': 1,
            },
            current_user=self.employee
        )

        self.assertIsNotNone(_farm2.id)
        self.assertEqual(self.grower_company_valid.farm_set.count(), 1)
        self.assertEqual(self.grower_company_valid.farm_set.get(), _farm2)

    def test_upsert_farm_notifications(self):
        _farm1 = self.grower_company_valid.upsert_farm(
            {
                'name': 'Farm 1',
                'address': {
                    'name': 'Farm 1',
                    'latitude': 123.45,
                    'longitude': 54.321,
                    'address': 'farm 1 address, australia',
                    'location_type': 'farm'
                },
                'market_zone_id': 1,
                'broker_company_id': self.company_valid.id
            },
            self.grower_employee
        )

        self.assertIsNotNone(_farm1.id)

        _farm2 = self.grower_company_valid.upsert_farm(
            {
                'name': 'Farm 2',
                'address': {
                    'name': 'Farm 2',
                    'latitude': 123.45,
                    'longitude': 54.321,
                    'address': 'farm 1 address, australia',
                    'location_type': 'farm'
                },
                'market_zone_id': 1,
                'broker_company_id': self.company_valid.id
            },
            self.employee
        )

        self.assertIsNotNone(_farm2.id)

    def test_upsert_farm_invalid_address(self):
        farm = self.company_valid.upsert_farm(
            {
                'name': 'Farm 1',
                'address': {'latitude': None},
                'market_zone_id': 1,
            },
            self.employee
        )
        self.assertIsNone(farm.id)
        self.assertIn('latitude', farm.errors)
        self.assertIn('longitude', farm.errors)

    @patch('core.services.external.abn.ABN')
    def test_register_success(self, abn_service_mock):
        abn_service_mock.has_constructor = False
        abn_lookup_mock = SimpleNamespace(entity_name='foobar')
        abn_service_mock.get_details = Mock(return_value=abn_lookup_mock)

        params = factory.build(dict, FACTORY_CLASS=CompanyFactory, **{'abn': '***********'})
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)

        company = Company.register(params)
        self.assertIsNotNone(company.id)
        self.assertIsNotNone(company.main_farm)
        self.assertEqual(company.main_farm.name, company.name)

    @patch('core.services.external.abn.ABN')
    def test_grower_register_success(self, abn_service_mock):
        abn_service_mock.has_constructor = False
        abn_lookup_mock = SimpleNamespace(entity_name='foobar')
        abn_service_mock.get_details = Mock(return_value=abn_lookup_mock)

        params = factory.build(dict, FACTORY_CLASS=CompanyFactory, **{'abn': '***********'})
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        company = Company.register(params)
        self.assertIsNotNone(company.id)
        self.assertIsNotNone(company.main_farm)
        self.assertEqual(company.main_farm.name, company.name)

    @patch('core.companies.models.Company.add_farm_to_directory')
    @patch('core.services.external.abn.ABN')
    def test_unregistered_grower_register(self, abn_service_mock, add_farm_to_directory_mock):
        abn_service_mock.has_constructor = False
        abn_lookup_mock = SimpleNamespace(entity_name='foobar')
        abn_service_mock.get_details = Mock(return_value=abn_lookup_mock)

        params = factory.build(
            dict, FACTORY_CLASS=CompanyFactory, **{'abn': '***********', 'owner_company_id': self.company_valid.id})
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)

        company = Company.register(params)

        self.assertIsNotNone(company.id)
        self.assertIsNotNone(company.main_farm)
        self.assertEqual(company.main_farm.name, company.name)
        add_farm_to_directory_mock.assert_called_once_with(company.main_farm.id)

    @patch('core.services.external.abn.ABN')
    def test_register_failure(self, abn_service_mock):
        abn_service_mock.has_constructor = False
        abn_lookup_mock = SimpleNamespace(entity_name=None)
        abn_service_mock.get_details = Mock(return_value=abn_lookup_mock)

        params = factory.build(dict, FACTORY_CLASS=CompanyFactory, **{'abn': '***********'})
        params.pop('address')
        company = Company.register(params)

        self.assertTrue('address' in company.errors)

    def test_add_companies_to_directory(self):
        added_company_count = AddedCompany.objects.count()
        subject_company = CompanyFactory()
        object_company = CompanyFactory()
        Company.add_companies_to_directory(subject_company.id, [object_company.id])

        self.assertEqual(AddedCompany.objects.count(), added_company_count + 1)

    def test_delete_companies_from_directory(self):
        subject_company = CompanyFactory()
        object_company = CompanyFactory()
        Company.add_companies_to_directory(subject_company.id, [object_company.id])

        added_company_count = AddedCompany.objects.count()

        Company.delete_companies_from_directory(subject_company.id, [object_company.id])

        self.assertEqual(AddedCompany.objects.count(), added_company_count - 1)

    def test_add_company_to_super_directory(self):
        added_company_count = AddedCompany.objects.count()
        object_company = CompanyFactory()
        Company.add_company_to_super_directory(object_company.id)
        self.assertEqual(AddedCompany.objects.count(), added_company_count + 1)

    @patch('core.jobs.models.Job.schedule_job_for_task')
    def test_mark_registered(self, task_mock):
        company = CompanyFactory(owner_company_id=1, transaction_participation=False)
        latest_admin = Employee(id=1, first_name='foo', last_name='bar', username='username', email='<EMAIL>')
        company.get_company_admins = Mock(
            return_value=Mock(order_by=Mock(return_value=Mock(first=Mock(return_value=latest_admin)))))

        self.assertFalse(company.is_registered)

        company.mark_registered()

        self.assertTrue(company.is_registered)
        self.assertFalse(company.transaction_participation)
        task_mock.assert_called_once_with(
            'send_email_for_activation',
            params={
                'full_name': 'foo bar',
                'first_name': 'foo',
                'email': '<EMAIL>',
                'username': 'username'
            }
        )

    def test_company_id(self):
        self.assertEqual(Company(id=1).company_id, 1)
        self.assertIsNone(Company().company_id)

    @patch('core.companies.models.Company.create_platform_features')
    @patch('core.common.models.BaseModel.create_with_location')
    @patch('core.services.external.aws.S3')
    def test_create_with_location_with_logo(
            self, s3_mock, base_create_with_location_mock,
            create_platform_features_mock,
    ):
        company = Company(id=1, logo_path=None)
        company.save = Mock()
        base_create_with_location_mock.return_value = company
        s3_mock.upload_base64 = Mock(return_value='logo-path')

        Company.create_with_location(params={'logo': {'base64': 'content', 'name': 'logo-name'}})

        self.assertEqual(company.logo_path, 'logo-path')
        base_create_with_location_mock.assert_called()
        s3_mock.upload_base64.assert_called_once_with(
            'content',
            'companies/logos/1/logo-name',
            False,
            True,
        )
        company.save.assert_called()
        create_platform_features_mock.assert_called_once()

    @patch('core.companies.models.Company.create_platform_features')
    @patch('core.companies.models.apps')
    @patch('core.common.models.BaseModel.create_with_location')
    def test_create_with_location_with_brokerages(
            self, base_create_with_location_mock,
            apps_mock, create_platform_features_mock,
    ):
        brokerage_klass_mock = Mock()
        brokerage_klass_mock.build_with_save = Mock()
        apps_mock.get_model = Mock(return_value=brokerage_klass_mock)
        company = Company(id=1, logo_path=None)
        company.save = Mock()
        base_create_with_location_mock.return_value = company

        Company.create_with_location(
            params={
                'brokerages': [{'foo': 'bar'}],
                'platform_features': ['foo', 'bar']
            }
        )

        base_create_with_location_mock.assert_called()
        apps_mock.get_model.assert_called_once_with('contracts', 'Brokerage')
        brokerage_klass_mock.build_with_save.assert_called_once_with(
            params=[{'foo': 'bar'}],
            entity_object=company,
            status='planned'
        )
        create_platform_features_mock.assert_called_once_with(features=['foo', 'bar'], plan_type='premium')

    @patch('core.companies.models.Company.brokerages_set')
    @patch('core.companies.models.apps')
    @patch('core.common.models.BaseModel.update_with_location')
    def test_update_with_location_with_brokerages(
            self,
            base_update_with_location_mock,
            apps_mock,
            brokerages_set_mock
    ):
        user_mock = Mock(company_id=1)
        all_brokerages_mock = Mock()
        all_brokerages_mock.delete = Mock()
        brokerages_set_mock.filter = Mock(return_value=all_brokerages_mock)
        brokerage_klass_mock = Mock()
        brokerage_klass_mock.build_with_save = Mock()
        apps_mock.get_model = Mock(return_value=brokerage_klass_mock)
        base_update_with_location_mock.return_value = self.company_valid

        Company.update_with_location(
            instance_id=self.company_valid.id,
            params={'brokerages': [{'foo': 'bar', 'broker_company_id': 1}]},
            user=user_mock,
        )

        base_update_with_location_mock.assert_called()
        apps_mock.get_model.assert_called_once_with('contracts', 'Brokerage')
        brokerage_klass_mock.build_with_save.assert_called_once_with(
            params=[{'foo': 'bar', 'broker_company_id': 1}],
            entity_object=self.company_valid,
            status='planned'
        )
        brokerages_set_mock.filter.assert_called_once_with(
            broker_company_id=1
        )
        all_brokerages_mock.delete.assert_called_once()

    @patch('core.common.models.BaseModel.update_with_location')
    @patch('core.services.external.aws.S3')
    def test_update_with_location_with_logo(self, s3_mock, base_update_with_location_mock):
        self.company_valid.logo_path = 'foobar.png'
        self.company_valid.save()
        base_update_with_location_mock.return_value = self.company_valid
        s3_mock.upload_base64 = Mock(return_value='logo-path')
        s3_mock.remove = Mock()

        Company.update_with_location(
            instance_id=self.company_valid.id,
            params={'logo': {'base64': 'content', 'name': 'logo-name'}}
        )

        updated_company = Company.objects.get(id=self.company_valid.id)
        self.assertEqual(updated_company.logo_path, 'logo-path')
        base_update_with_location_mock.assert_called()
        s3_mock.remove.assert_called_once_with('foobar.png')
        s3_mock.upload_base64.assert_called_once_with(
            'content',
            'companies/logos/' + str(self.company_valid.id) + '/logo-name',
            False,
            True,
        )

    @patch('core.services.external.aws.S3')
    def test_logo_url(self, s3_mock):
        s3_mock.public_url_for.return_value = 'http://s3.com/path/to/object'

        company = Company(id=1, logo_path=None)
        self.assertIsNone(company.logo_url)
        s3_mock.public_url_for.assert_not_called()

        company = Company(id=1, logo_path='object-key')
        self.assertEqual(company.logo_url, 'http://s3.com/path/to/object')
        s3_mock.public_url_for.assert_called_once_with('object-key')

    @patch('core.companies.models.Company.brokerages_set')
    def test_brokerages_for_user(self, brokerages_set_mock):
        user_mock = Mock(company_id=20)
        brokerages_set_mock.filter = Mock(return_value='brokerages-queryset')
        company = Company(id=10)
        company.qs2dict = Mock(return_value='brokerages-dict')

        self.assertEqual(company.brokerages_for_user(user_mock), 'brokerages-dict')

        company.qs2dict.assert_called_once_with('brokerages-queryset')
        brokerages_set_mock.filter.assert_called_once_with(broker_company_id=20)

        self.assertEqual(company.brokerages_for_user(None), 'brokerages-dict')

    @patch('core.companies.models.Company.farm_set')
    def test_is_independent_farmer(self, farm_set_mock):
        for type_id in [2, 3, 4, 5, 6]:
            self.assertFalse(Company(type_id=type_id).is_independent_farmer)

        company = Company(type_id=1)
        farm_set_mock.count = Mock(return_value=0)

        self.assertFalse(company.is_independent_farmer)

        farm_set_mock.count = Mock(return_value=2)
        self.assertFalse(company.is_independent_farmer)

        farm_set_mock.count = Mock(return_value=1)
        self.assertTrue(company.is_independent_farmer)

    @patch('core.companies.models.Company.farm_set')
    def test_independent_farm_id(self, farm_set_mock):
        farm_set_mock.all = Mock(return_value=[Mock(id=100)])

        self.assertIsNone(Company(type_id=2).independent_farm_id)
        self.assertEqual(Company(type_id=1).independent_farm_id, 100)

        farm_set_mock.all = Mock(return_value=[Mock(id=100), Mock(id=101)])
        self.assertIsNone(Company(type_id=1).independent_farm_id)

        farm_set_mock.all.assert_called()

    @patch('core.companies.models.Company.farm_set')
    def test_independent_farm(self, farm_set_mock):
        farm_mock = Mock(id=100)
        farm_mock_1 = Mock(id=101)
        farm_set_mock.all = Mock(return_value=[farm_mock])

        self.assertIsNone(Company(type_id=2).independent_farm)
        self.assertEqual(Company(type_id=1).independent_farm, farm_mock)

        farm_set_mock.all = Mock(return_value=[farm_mock, farm_mock_1])
        self.assertIsNone(Company(type_id=1).independent_farm)

        farm_set_mock.all.assert_called()

    @patch('core.companies.models.PlatformFeatures')
    def test_create_platform_features(self, platform_features_klass_mock):
        platform_features_klass_mock.persist = Mock(return_value='features')

        self.assertEqual(
            Company(id=1).create_platform_features(
                features=['foo', 'bar']
            ),
            'features'
        )
        platform_features_klass_mock.persist.assert_called_once_with(
            company_id=1,
            raise_exception=True,
            features=['foo', 'bar']
        )

    @patch('core.companies.models.apps.get_model')
    @patch('core.companies.models.AddedCompany')
    @patch('core.companies.models.Company.is_in_farm_directory')
    @patch('core.companies.models.FarmDirectory')
    def test_add_farm_to_directory(
            self,
            farm_directory_klass_mock,
            is_in_farm_directory_mock,
            added_company_klass_mock,
            get_model_mock
    ):
        farm_klass_mock = Mock()
        company = Company(id=100, type_id=1)
        farm_klass_mock.objects = Mock(get=Mock(return_value=Farm(id=2, company=company)))
        get_model_mock.return_value = farm_klass_mock
        added_company_klass_mock.objects = Mock(get_or_create=Mock())
        farm_directory_klass_mock.create = Mock(return_value='farm-directory-instance')
        is_in_farm_directory_mock.return_value = False

        self.assertEqual(
            Company(id=6, type_id=2).add_farm_to_directory(farm_id=2),
            'farm-directory-instance'
        )
        get_model_mock.assert_called_once_with('farms', 'Farm')
        farm_klass_mock.objects.get.assert_called_once_with(id=2)
        added_company_klass_mock.objects.get_or_create.assert_called_once_with(
            subject_company_id=6,
            object_company_id=100,
        )
        farm_directory_klass_mock.create.assert_called_once_with({'farm_id': 2, 'company_id': 6})

    @patch('core.companies.models.apps.get_model')
    def test_get_directory_farms(self, get_model_mock):
        farm_klass_mock = Mock()
        farm_klass_mock.objects = Mock(filter=Mock(return_value='queryset'))
        get_model_mock.return_value = farm_klass_mock

        self.assertEqual(Company(id=100).get_directory_farms(), 'queryset')

        get_model_mock.assert_called_once_with('farms', 'Farm')
        farm_klass_mock.objects.filter.assert_called_once_with(farmdirectory__company_id=100)

    @patch('core.companies.models.Company.farm_directory_set')
    def test_is_in_farm_directory(self, farm_directory_set_mock):
        filter_mock = Mock(exists=Mock(return_value=True))
        farm_directory_set_mock.filter = Mock(return_value=filter_mock)

        self.assertTrue(Company().is_in_farm_directory(farm_id=11))

        farm_directory_set_mock.filter.assert_called_once_with(farm_id=11)
        filter_mock.exists.assert_called_once()

    @patch('core.companies.models.Company.trailer_set')
    def test_unassigned_trailers(self, trailer_set_mock):
        expected_trailers = ['trailer1', 'trailer2']
        trailer_set_mock.filter = Mock(return_value=expected_trailers)

        self.assertEqual(
            Company().unassigned_trailers(),
            expected_trailers
        )
        trailer_set_mock.filter.assert_called_once_with(truck_id__isnull=True)

    def test_can_register(self):
        user_mock = Mock(company=Company(type_id=6))
        unreg_company = Company(owner_company_id=2)
        unreg_company.has_company_admin = Mock(return_value=True)

        self.assertTrue(unreg_company.can_register(user_mock))

        unreg_company.has_company_admin.assert_called_once()

    @patch('core.companies.models.Company.employee_set')
    def test_has_company_admin(self, employee_set_mock):
        filter_mock = Mock()
        filter_mock.exists = Mock(return_value=True)
        employee_set_mock.filter = Mock(return_value=filter_mock)

        self.assertTrue(Company().has_company_admin())
        employee_set_mock.filter.assert_called_once_with(type_id=1, is_active=True)
        filter_mock.exists.assert_called_once()

    @patch('core.companies.models.Company.employee_set')
    def test_admin(self, employee_set_mock):
        filter_mock = Mock()
        filter_mock.first = Mock(return_value=True)
        employee_set_mock.filter = Mock(return_value=filter_mock)

        self.assertTrue(Company().admin())
        employee_set_mock.filter.assert_called_once_with(type_id=1, is_active=True)
        filter_mock.first.assert_called_once()

    def test_cannot_create_employee_reasons(self):
        company = Company(id=20)
        company.transaction_participation = True
        self.assertEqual(
            company.cannot_create_employee_reasons(None), ['The selected party is registered on the system']
        )
        system_user = Employee.objects.get(id=1)
        self.assertEqual(company.cannot_create_employee_reasons(system_user), [])

        user_mock = Mock()
        user_mock.company_id = 30
        user_mock.company = Mock(is_system=False)
        self.assertEqual(
            company.cannot_create_employee_reasons(user_mock), ['The selected party is a subscriber on the system']
        )
        company.transaction_participation = False
        company.owner_company_id = 1
        self.assertEqual(company.cannot_create_employee_reasons(user_mock), [])

        company.owner_company_id = None
        company.transaction_participation = True
        user_mock.company_id = 20
        for type_id in [1, 2]:
            user_mock.type_id = type_id
            self.assertEqual(company.cannot_create_employee_reasons(user_mock), [])

        for type_id in [3, 4, 5]:
            user_mock.type_id = type_id
            self.assertEqual(
                company.cannot_create_employee_reasons(user_mock),
                ['Only Company Admin, Office Admin can create']
            )

    def test_cannot_create_ngr_reasons(self):
        company = Company(id=20)
        company.transaction_participation = True
        self.assertEqual(
            company.cannot_create_ngr_reasons(None), ['The selected party is registered on the system']
        )
        system_user = Employee.objects.get(id=1)
        self.assertEqual(company.cannot_create_ngr_reasons(system_user), [])

        user_mock = Mock()
        user_mock.company_id = 30
        user_mock.company = Mock(is_system=False)
        self.assertEqual(
            company.cannot_create_ngr_reasons(user_mock), ['The selected party is a subscriber on the system']
        )
        company.owner_company_id = 1
        company.transaction_participation = False
        self.assertEqual(company.cannot_create_ngr_reasons(user_mock), [])

        company.owner_company_id = None
        company.transaction_participation = True
        user_mock.company_id = 20
        for type_id in [1, 2, 3]:
            user_mock.type_id = type_id
            self.assertEqual(company.cannot_create_ngr_reasons(user_mock), [])

        for type_id in [4, 5]:
            user_mock.type_id = type_id
            self.assertEqual(
                company.cannot_create_ngr_reasons(user_mock),
                ['Only Company Admin, Office Admin, Office Employee can create']
            )

    def test_is_managed_by_user(self):
        self.assertFalse(Company(id=100).is_managed_by_user(Mock(company_id=10)))
        self.assertTrue(Company(id=100).is_managed_by_user(Mock(company_id=100)))

    @patch('core.companies.models.Company.objects')
    def test_bhc_companies(self, manager_mock):
        expected_bhcs = ['bhcs']
        manager_mock.filter = Mock(return_value=expected_bhcs)

        self.assertEqual(Company.bhc_companies(), expected_bhcs)
        manager_mock.filter.assert_called_once_with(type_id=4)

    def test_directory_companies(self):
        subject_company = CompanyFactory(id=100)
        CompanyFactory(id=101)  # obj company1
        CompanyFactory(id=102)  # obj company2

        directory_companies = subject_company.directory_companies(subject_company.id)
        self.assertEqual(directory_companies.count(), 0)

        subject_company.add_companies_to_directory(100, [101, 102])
        directory_companies = subject_company.directory_companies(subject_company.id)

        self.assertEqual(directory_companies.count(), 2)
        self.assertEqual(
            sorted(list(directory_companies.values_list('id', flat=True))),
            sorted([101, 102])
        )

        universal_obj_company = CompanyFactory(id=103)
        universal_obj_company.mark_default_directory_company()

        directory_companies = subject_company.directory_companies(subject_company.id)

        self.assertEqual(AddedCompany.objects.count(), 3)

        self.assertEqual(directory_companies.count(), 3)
        self.assertEqual(
            sorted(list(directory_companies.values_list('id', flat=True))),
            sorted([101, 102, 103])
        )

        subject_company.add_company_to_directory(103)

        directory_companies = subject_company.directory_companies(subject_company.id)

        self.assertEqual(AddedCompany.objects.count(), 4)
        self.assertEqual(directory_companies.count(), 3)
        self.assertEqual(
            sorted(list(directory_companies.values_list('id', flat=True))),
            sorted([101, 102, 103])
        )

    @patch('core.farms.models.Farm.accepted_farms')
    @patch('core.companies.models.Company.directory_farm_ids', new_callable=PropertyMock)
    def test_managed_farms(self, directory_farm_ids_mock, accepted_farms_mock):
        directory_farm_ids_mock.return_value = [1, 2]
        accepted_farms_mock.return_value = 'foo-bar'
        self.assertEqual(Company().managed_farms, 'foo-bar')
        accepted_farms_mock.assert_called_once_with({'id__in': [1, 2]})

    @patch('core.farms.models.Farm.accepted_farms')
    @patch('core.companies.models.Company.directory_companies_ids', new_callable=PropertyMock)
    def test_directory_companies_farms(self, directory_companies_ids_mock, accepted_farms_mock):
        directory_companies_ids_mock.return_value = [1, 2]
        accepted_farms_mock.return_value = 'foo-bar'
        self.assertEqual(Company().directory_companies_farms(), 'foo-bar')
        accepted_farms_mock.assert_called_once_with({'company_id__in': [1, 2]})

        directory_companies_ids_mock.return_value = [4]
        self.assertEqual(Company(id=5).directory_companies_farms(include_self=True), 'foo-bar')
        self.assertEqual(accepted_farms_mock.call_count, 2)
        self.assertEqual(
            accepted_farms_mock.call_args_list,
            [call({'company_id__in': [1, 2]}), call({'company_id__in': [1, 4, 5]})]
        )

    @patch('core.companies.models.Company.farm_directory_set')
    def test_farm_directory_set(self, farm_directory_set_mock):
        farm_directory_set_mock.values_list = Mock(return_value=[1, 2])
        self.assertEqual(Company().directory_farm_ids, [1, 2])
        farm_directory_set_mock.values_list.assert_called_once_with('farm_id', flat=True)

    def test_directory_companies_ids(self):
        subject_company = CompanyFactory(id=100)
        CompanyFactory(id=101)
        CompanyFactory(id=102)

        subject_company.add_companies_to_directory(100, [101, 102])

        self.assertEqual(AddedCompany.objects.count(), 2)
        self.assertEqual(sorted(subject_company.directory_companies_ids), [101, 102])


    def test_is_mobile_flow_logistics(self):
        self.assertTrue(Company(mobile_flow='logistics').is_mobile_flow_logistics)
        self.assertFalse(Company(mobile_flow=None).is_mobile_flow_logistics)
        self.assertFalse(Company(mobile_flow='foobar').is_mobile_flow_logistics)

    def test_is_mobile_flow_storage(self):
        self.assertTrue(Company(mobile_flow='storage').is_mobile_flow_storage)
        self.assertFalse(Company(mobile_flow=None).is_mobile_flow_storage)
        self.assertFalse(Company(mobile_flow='foobar').is_mobile_flow_storage)

    def test_reset_mobile_flow(self):
        for type_id in [2, 3]:
            company = Company(type_id=type_id, mobile_flow=None)
            company.save = Mock()
            company.reset_mobile_flow()
            self.assertTrue(company.is_mobile_flow_logistics)
            company.save.assert_called_once()

        for type_id in [1, 4, 5]:
            company = Company(type_id=type_id, mobile_flow=None)
            company.save = Mock()
            company.reset_mobile_flow()
            self.assertTrue(company.is_mobile_flow_storage)
            company.save.assert_called_once()

    def test_to_csv_row(self):
        company = CompanyFactory()
        user = Employee(id=1, is_staff=False, company=company)
        headers = get_csv_headers(self.company_valid.country)
        if self.company_valid.created_by.is_staff:
            headers += ['Subscriber', 'Mobile Subscriber', 'ID']

        self.assertEqual(
            self.company_valid.to_csv_row(self.company_valid.created_by, headers),
            [
                self.company_valid.business_name,
                self.company_valid.entity_name,
                self.company_valid.abn,
                self.company_valid.type.name,
                True,
                ANY,
                ANY,
                None,
                None,
                None,
                '21 DEOW of Delivery',
                ANY,
                False,
                False,
                self.company_valid.id
            ]
        )
        headers = get_csv_headers(user.company.country)
        self.assertEqual(
            self.company_valid.to_csv_row(user, headers),
            [
                self.company_valid.business_name,
                self.company_valid.entity_name,
                self.company_valid.abn,
                self.company_valid.type.name,
                True,
                ANY,
                ANY,
                None,
                None,
                None,
                '21 DEOW of Delivery',
                ANY,
            ]
        )

    def test_is_default_directory_company(self):
        company1 = CompanyFactory(id=100)
        company2 = CompanyFactory(id=101)

        self.assertFalse(company1.is_default_directory_company)

        company2.add_company_to_directory(company1.id)
        self.assertFalse(company1.is_default_directory_company)
        self.assertEqual(AddedCompany.objects.count(), 1)

        AddedCompany.objects.all().delete()
        self.assertEqual(AddedCompany.objects.count(), 0)

        company1.mark_default_directory_company()
        self.assertTrue(company1.is_default_directory_company)
        self.assertEqual(AddedCompany.objects.count(), 1)

        company1.unmark_default_directory_company()
        self.assertFalse(company1.is_default_directory_company)
        self.assertEqual(AddedCompany.objects.count(), 0)

    def test_my_stocked_farms(self):
        company = CompanyFactory()
        ngr = NgrFactory(company=company, ngr_type='single')
        farm1 = FarmFactory(company=company)
        farm2 = FarmFactory(company=company)
        silo = StorageFactory(type='silo', farm=farm1)
        container = StorageFactory(type=STORAGE_TYPE_CONTAINER, farm=farm2)

        LoadFactory(storage=silo, ngr=ngr)
        LoadFactory(storage=silo, status='void', ngr=ngr)
        LoadFactory(storage=container, ngr=ngr)
        LoadFactory(storage=container, status='void', ngr=ngr)

        stocked_farms = company.my_stocked_farms()
        self.assertEqual(stocked_farms.count(), 1)
        self.assertEqual(stocked_farms.first().id, farm1.id)

        stocked_farms = company.my_stocked_farms(only_containers=True)
        self.assertEqual(stocked_farms.count(), 1)
        self.assertEqual(stocked_farms.first().id, farm2.id)

    def test_is_bulk_invoicing_applicable_for_tenure(self):
        company = Company(bulk_invoicing_from=datetime.date(2020, 2, 1))
        self.assertTrue(company.is_bulk_invoicing_applicable_for_tenure(datetime.datetime(2020, 2, 2)))
        self.assertTrue(company.is_bulk_invoicing_applicable_for_tenure(datetime.datetime(2020, 2, 3)))
        self.assertTrue(company.is_bulk_invoicing_applicable_for_tenure(datetime.datetime(2021, 2, 1)))
        self.assertTrue(company.is_bulk_invoicing_applicable_for_tenure(datetime.datetime(2021, 3, 1)))
        self.assertTrue(company.is_bulk_invoicing_applicable_for_tenure(datetime.datetime(2021, 1, 1)))
        self.assertTrue(company.is_bulk_invoicing_applicable_for_tenure(datetime.datetime(2020, 2, 1)))
        self.assertFalse(company.is_bulk_invoicing_applicable_for_tenure(datetime.datetime(2020, 1, 31)))
        self.assertFalse(company.is_bulk_invoicing_applicable_for_tenure(datetime.datetime(2020, 1, 1)))

    def test_get_tenure_dates_for_warehouse_invoice(self):
        company_with_monthly_invoice = CompanyFactory(warehouse_invoice_frequency=WAREHOUSE_INVOICE_MONTHLY_FREQUENCY)
        self.assertEqual(
            company_with_monthly_invoice.get_tenure_dates_for_warehouse_invoice(datetime.datetime(2024, 7, 8)),
            (datetime.datetime(2024, 7, 1, 0, 0), datetime.datetime(2024, 7, 31, 0, 0))
        )

        company_with_monthly_invoice = CompanyFactory(
            warehouse_invoice_frequency=WAREHOUSE_INVOICE_WEEKLY_FREQUENCY, warehouse_invoice_start_of_week=1
        )
        self.assertEqual(
            company_with_monthly_invoice.get_tenure_dates_for_warehouse_invoice(datetime.datetime(2024, 7, 8)),
            (datetime.datetime(2024, 7, 8, 0, 0), datetime.datetime(2024, 7, 14, 0, 0))
        )

    def test_get_tenure_for_warehouse_invoice(self):
        company_with_monthly_invoice = CompanyFactory(warehouse_invoice_frequency=WAREHOUSE_INVOICE_MONTHLY_FREQUENCY)
        self.assertEqual(
            company_with_monthly_invoice.get_tenure_for_warehouse_invoice(datetime.datetime(2024, 7, 8)),
            "01/07/2024 - 31/07/2024"
        )

        company_with_monthly_invoice = CompanyFactory(
            warehouse_invoice_frequency=WAREHOUSE_INVOICE_WEEKLY_FREQUENCY, warehouse_invoice_start_of_week=1
        )
        self.assertEqual(
            company_with_monthly_invoice.get_tenure_for_warehouse_invoice(datetime.datetime(2024, 7, 8)),
            "08/07/2024 - 14/07/2024"
        )

    def test_replace_viewer_company_id(self):
        new_company = CompanyFactory()

        contract = ContractFactory()
        order = FreightOrderFactory()
        movement = FreightMovementFactory()
        title_transfer = TitleTransferFactory()

        contract.refresh_from_db()
        order.refresh_from_db()
        movement.refresh_from_db()
        title_transfer.refresh_from_db()

        self.assertTrue(new_company.id not in contract.viewer_company_ids)
        self.assertTrue(new_company.id not in movement.viewer_company_ids)
        self.assertTrue(new_company.id not in order.viewer_company_ids)
        self.assertTrue(new_company.id not in title_transfer.viewer_company_ids)

        Company.replace_viewer_company_id(1, new_company.id)

        contract.refresh_from_db()
        order.refresh_from_db()
        movement.refresh_from_db()
        title_transfer.refresh_from_db()

        self.assertTrue(new_company.id in contract.viewer_company_ids)
        self.assertTrue(new_company.id in movement.viewer_company_ids)
        self.assertTrue(new_company.id in order.viewer_company_ids)
        self.assertTrue(new_company.id in title_transfer.viewer_company_ids)

    @patch('core.countries.models.Country.get_requesting_country')
    def test_create_by_abn_no_abn_service(self, mock_get_requesting_country):
        mock_country = MagicMock()
        type(mock_country).get_abn_service_class = PropertyMock(return_value=None)
        mock_get_requesting_country.return_value = mock_country

        result = Company.create_by_abn('***********', self.employee)
        self.assertIsNone(result)
        mock_get_requesting_country.assert_called_once()

    @patch('core.services.external.abn.ABN.get_details')
    def test_create_by_abn_cancelled(self, mock_get_details):
        mock_get_details.return_value = {
            'errors': {'abn': ['Cancelled ABN']}
        }

        result = Company.create_by_abn('***********', self.employee)
        self.assertIsInstance(result, Company)
        self.assertEqual(result.abn, '***********')
        self.assertEqual(result.errors, {'abn': ['The party has Cancelled ABN ***********']})
        mock_get_details.assert_called_once_with('***********')

    @patch('core.companies.models.Company.add_company_to_super_directory')
    @patch('django.apps.apps.get_model')
    @patch('core.services.external.abn.ABN.get_details')
    def test_create_by_abn_success(self, mock_get_details, mock_get_model, mock_add_to_super_directory):
        mock_details = SimpleNamespace(
            business_name='Test Business',
            entity_name='Test Entity',
            address='123 Test St, Test City'
        )
        mock_get_details.return_value = mock_details

        mock_farm = MagicMock()
        mock_farm.persisted = True
        mock_farm.id = 1

        mock_farm_model = MagicMock()
        mock_farm_model.create_main_farm.return_value = mock_farm
        mock_ngr_model = MagicMock()
        mock_get_model.side_effect = lambda app, model: mock_farm_model if model == 'Farm' else mock_ngr_model

        mock_add_to_super_directory.return_value = None

        with patch('core.companies.models.Company.create_with_location') as mock_create:
            mock_company = MagicMock()
            mock_company.persisted = True
            mock_create.return_value = mock_company

            result = Company.create_by_abn('***********', self.employee)
            default_lat, default_lng = Country.get_default_lat_lng()

            self.assertEqual(result, mock_company)
            mock_create.assert_called_once_with({
                'business_name': 'Test Business',
                'entity_name': 'Test Entity',
                'abn': '***********',
                'address': {
                    'name': '123 Test St, Test City',
                    'address': '123 Test St, Test City',
                    'latitude': default_lat,
                    'longitude': default_lng,
                },
                'type_id': GROWER_TYPE_ID,
                'mobile': '0444444444',
                'owner_company_id': self.employee.company_id,
                'created_by': self.employee,
                'updated_by': self.employee
            })
            Company.add_company_to_super_directory.assert_called_once()

    @patch('core.services.external.abn.ABN.get_details')
    def test_create_by_abn_unknown(self, mock_get_details):
        mock_get_details.return_value = {
            'errors': {'abn': ['Unknown ABN']}
        }

        with patch('core.companies.models.Company.create_with_location') as mock_create:

            mock_company = MagicMock()
            mock_company.persisted = False
            mock_create.return_value = mock_company

            result = Company.create_by_abn('***********', self.employee)

            self.assertEqual(result, mock_company)
            default_lat, default_lng = Country.get_default_lat_lng()
            mock_create.assert_called_once_with({
                'business_name': 'Unknown Company',
                'entity_name': 'Unknown Company',
                'abn': '***********',
                'address': {
                    'name': AUSTRALIA,
                    'address': AUSTRALIA,
                    'latitude': default_lat,
                    'longitude': default_lng,
                },
                'type_id': GROWER_TYPE_ID,
                'mobile': '0444444444',
                'owner_company_id': self.employee.company_id,
                'created_by': self.employee,
                'updated_by': self.employee
            })

    @patch('core.services.external.abn.ABN.get_details')
    def test_create_by_abn_custom_params(self, mock_get_details):
        mock_details = SimpleNamespace(
            business_name='Test Business',
            entity_name='Test Entity',
            address='123 Test St, Test City'
        )
        mock_get_details.return_value = mock_details

        with patch('core.companies.models.Company.create_with_location') as mock_create:

            mock_company = MagicMock()
            mock_company.persisted = False
            mock_create.return_value = mock_company

            result = Company.create_by_abn(
                '***********',
                self.employee,
                type_id=999,
                business_name='Custom Business',
                address_name='Custom Address',
                latitude=10.0,
                longitude=20.0,
                mobile='1234567890'
            )

            self.assertEqual(result, mock_company)
            mock_create.assert_called_once_with({
                'business_name': 'Custom Business',
                'entity_name': 'Test Entity',
                'abn': '***********',
                'address': {
                    'name': 'Custom Address',
                    'address': 'Custom Address',
                    'latitude': 10.0,
                    'longitude': 20.0,
                },
                'type_id': 999,
                'mobile': '1234567890',
                'owner_company_id': self.employee.company_id,
                'created_by': self.employee,
                'updated_by': self.employee
            })

@tag('model')
class AddedCompanyTest(ACTestCase):
    def setUp(self):
        super().setUp()
        self.company_1 = CompanyFactory()
        self.company_2 = CompanyFactory()

    def test_unique_constraint(self):
        data = {
            'subject_company_id': self.company_1.id,
            'object_company_id': self.company_2.id,
        }

        AddedCompany.create(data)
        model = AddedCompany.create(data).to_dict()

        self.assertIsNone(model.get('id', None))
        self.assertIsNotNone(model.get('errors', None))
        self.assertIsNotNone(model['errors'].get('__all__', None))
        self.assertTrue(
            'Added company with this Subject company and Object company already exists.' in\
            model['errors']['__all__']
        )


@tag('view')
class CompaniesViewTest(AuthSetup):
    def setUp(self):
        self.companies_url = reverse('core.companies:company')
        super().setUp()

    def test_get(self):
        response = self.client.get(
            self.companies_url,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertIsNotNone(response.data[0]['id'])

    def test_get_companies_with_trucks_empty(self):
        _query_string = '?truck__id__gt=0&one_to_many_relations=truck'
        response = self.client.get(
            self.companies_url + _query_string,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_get_companies_with_trucks_not_empty(self):
        _truck = self.company.truck_set.create(**TRUCK_VALID_MOCK_DATA_1)
        _query_string = '?truck__id__gt=0&one_to_many_relations=truck'
        response = self.client.get(
            self.companies_url + _query_string,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        trucks = [truck for truck in response.data[0]['trucks'] if truck['rego'] != FLEET_REGO]
        self.assertEqual(len(trucks), 1)
        self.assertEqual(trucks[0]['id'], _truck.id)

        _truck.delete()

    def test_get_companies_with_trucks_company_not_empty(self):
        _truck = self.company.truck_set.create(**TRUCK_VALID_MOCK_DATA_1)
        _query_string = '?truck__id__gt=0&one_to_many_relations=truck&' \
                        'one_to_many_fields_with_relations=truck__farm,truck__company'
        response = self.client.get(
            self.companies_url + _query_string,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        trucks = [truck for truck in response.data[0]['trucks'] if truck['rego'] != FLEET_REGO]
        self.assertEqual(len(trucks), 1)
        self.assertEqual(trucks[0]['id'], _truck.id)

    def test_get_with_exclude(self):
        response = self.client.get(
            self.companies_url + '?exclude=' + str(self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_get_with_assets(self):
        response = self.client.get(
            self.companies_url + '?assets=employee',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)

        response = self.client.get(
            self.companies_url + '?assets=employee,truck',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)

    def test_post_success_new_company(self):
        params = factory.build(dict, FACTORY_CLASS=CompanyFactory)
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        response = self.client.post(
            self.companies_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        company_companies_ids = self.employee.company.get_companies(return_ids=True)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIsNotNone(response.data['id'])
        self.assertEqual(response.data['createdById'], self.employee.id)
        self.assertEqual(response.data['updatedById'], self.employee.id)
        self.assertEqual(response.data['ownerCompanyId'], self.employee.company_id)
        self.assertTrue(len(company_companies_ids), 1)
        self.assertTrue(response.data['id'] in company_companies_ids)
        self.assertTrue(
            Farm.objects.filter(
                company_id=response.data['id'], name=response.data['name']
            ).exists()
        )

    def test_post_duplicate_name_failure(self):
        response = self.client.post(
            self.companies_url,
            COMPANY_VALID_MOCK_DATA,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['errors'],
                         {'businessName': ['Company with this business name already exists']})

    def test_post_success_new_grower_company(self):
        response = self.client.post(
            self.companies_url,
            COMPANY_GROWER_VALID_MOCK_DATA,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        company_companies_ids = self.employee.company.get_companies(return_ids=True)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIsNotNone(response.data['id'])
        self.assertEqual(response.data['typeId'], 1)
        self.assertEqual(response.data['createdById'], self.employee.id)
        self.assertEqual(response.data['updatedById'], self.employee.id)
        self.assertEqual(response.data['ownerCompanyId'], self.employee.company_id)
        self.assertTrue(len(company_companies_ids), 1)
        self.assertTrue(response.data['id'] in company_companies_ids)
        self.assertTrue(
            Farm.objects.filter(
                company_id=response.data['id'], name=response.data['name']
            ).exists()
        )

    def test_post_success_new_company_no_type_id(self):
        params = factory.build(dict, FACTORY_CLASS=CompanyFactory)
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        params.pop('type_id', None)

        response = self.client.post(
            self.companies_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        company_companies_ids = self.employee.company.get_companies(return_ids=True)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIsNotNone(response.data['id'])
        self.assertEqual(response.data['createdById'], self.employee.id)
        self.assertEqual(response.data['updatedById'], self.employee.id)
        self.assertEqual(response.data['ownerCompanyId'], self.employee.company_id)
        self.assertEqual(response.data['typeId'], 5)
        self.assertTrue(len(company_companies_ids), 1)
        self.assertTrue(response.data['id'] in company_companies_ids)

    def test_post_success_existing_company(self):
        params = factory.build(dict, FACTORY_CLASS=CompanyFactory)
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        response = self.client.post(
            self.companies_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        company_companies_ids = self.employee.company.get_companies(return_ids=True)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIsNotNone(response.data['id'])
        self.assertTrue(len(company_companies_ids), 1)
        self.assertTrue(response.data['id'] in company_companies_ids)

        response2 = self.client.post(
            self.companies_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(response2.status_code, status.HTTP_200_OK)  # get or create
        self.assertEqual(response2.data['id'], response.data['id'])

    def test_post_failure(self):
        response = self.client.post(
            self.companies_url,
            COMPANY_INVALID_MOCK_DATA,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(
            response.status_code,
            status.HTTP_400_BAD_REQUEST
        )
        self.assertIsNone(response.data['id'])
        self.assertEqual(
            response.data['errors']['abn'],
            ['Ensure this value has at least 11 characters (it has 5).']
        )

    def test_200_company_from_ngr_number(self):
        company_1 = CompanyFactory()
        ngr = NgrFactory(company=company_1)
        response = self.client.get(
            self.companies_url + f'?ngr_number={ngr.ngr_number}',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], company_1.id)

        ngr_1 = NgrFactory()
        ngr_1.company_id = 1
        ngr_1.save()
        response = self.client.get(
            self.companies_url + f'?ngr_number={ngr_1.ngr_number}',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['companies']), 0)
        self.assertEqual(response.data['systemNgrId'], ngr_1.id)


@tag('view')
class CompanyImpexDocsProductMasterViewTest(AuthSetup):

    @patch('core.services.external.impex_docs.requests.get')
    def test_get_impex_product_master(self, mock_get):
        client_id = 'test_client_id'
        client_secret = 'test_client_secret'
        impex_docs_token = 'test_token'
        company = CompanyFactory()
        company.impex_docs_enabled = True
        company.save()
        employee = EmployeeFactory(company=company)
        connection = ImpexDocsConnection(company=company, client_id=client_id, client_secret=client_secret,
                                         access_token=impex_docs_token,
                                         expires_in=timezone.now() + timezone.timedelta(days=1))
        connection.save()

        mock_response = Mock(status_code=200)
        mock_response.json.return_value = {
            "continuityToken": None,
            "data": [{
                "product_id": 189333,
                "product_code": "wheat-aph2",
                "product_description": "wheat-aph2-Ajana",
                "other_product_code": "",
                "mfg_part_number": "",
                "product_class": "SEASON 24/25",
                "ahecc_code": "",
                "ahecc_uom": "",
                "net_weight": 0.0,
                "gross_weight": 0.0,
                "volume": 0.0,
                "net_weight_uom": "",
                "gross_weight_uom": "",
                "volume_uom": "",
                "size": "",
                "specification": "",
                "label_3": "",
                "hs_code": "",
                "origin_country_code": "",
                "origin_criterion": "",
                "sales_uom": "",
                "purchase_uom": "",
                "base_uom": "",
                "packaging_uom": "",
                "product_type": "",
                "product_type_desc": "",
                "pack_type": "",
                "pack_type_desc": "",
                "preservation_type_code": "",
                "preservation_type_desc": "",
                "supplementary_type_code": "",
                "supplementary_type_desc": "",
                "cut_code": "",
                "cut_code_desc": "",
                "product_source_state_code": "",
                "product_source_state_desc": "",
                "commodity_code_pra": "",
                "commodity_code_desc_pra": "",
                "health_cer_desc_alter_desc": "",
                "parent_product_id": "",
                "level": 0,
                "is_active_for_facility": True,
                "product_category": "",
                "chemical_lean_indicator": "",
                "halal_indicator": False,
                "import_authority_code": "",
                "shelf_life": "",
                "shelf_life_uom": "",
                "country_of_origin": "",
                "duty_drawback": "",
                "international_article_number": "",
                "brand": "",
                "registered_establishment_code": "",
                "registered_establishment_description": "",
                "product_details_1": "",
                "product_details_2": "",
                "product_details_3": "",
                "product_details_4": "",
                "product_details_5": ""
            }]
        }
        mock_get.return_value = mock_response
        response = self.client.get(
            f'/companies/{company.id}/impex-docs/product-master/?status=active',
            HTTP_AUTHORIZATION='Token ' + employee.refresh_token(),
            format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['products']), 1)
        self.assertEqual(response.data['products'],
                         [{'productId': 189333, 'name': 'wheat-aph2', 'productDescription': 'wheat-aph2-Ajana'}])


@tag('view')
class CompanyImpexDocsProductMappingsViewTest(AuthSetup):

    def setUp(self):
        super().setUp()
        self.client_id = 'test_client_id'
        self.client_secret = 'test_client_secret'
        self.impex_docs_token = 'test_token'
        self.company = CompanyFactory()
        self.company.impex_docs_enabled = True
        self.company.save()
        self.employee = EmployeeFactory(company=self.company)
        self.connection = ImpexDocsConnection(
            company=self.company, client_id=self.client_id, client_secret=self.client_secret,
            access_token=self.impex_docs_token, expires_in=timezone.now() + timezone.timedelta(days=1))
        self.connection.save()

    def test_post_impex_product_mappings(self):
        response = self.client.post(
            f'/companies/{self.company.id}/impex-docs/mappings/products/',
            {
                'impexDocsMappings': [
                    {'commodityId': 1, 'gradeId': 1, 'productCode': 'wheat-aph1', 'varietyId': 470},
                    {'commodityId': 1, 'gradeId': 3, 'productCode': 'wheat-aph1', 'varietyId': 470}
                ]
            },
            HTTP_AUTHORIZATION='Token ' + self.employee.refresh_token(),
            format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        mapping_1 = self.company.impex_product_mappings.filter(grade_id=1).first()
        mapping_2 = self.company.impex_product_mappings.filter(grade_id=3).first()
        self.assertEqual(
            response.data,
            [{'id': mapping_1.id, 'companyId': self.company.id, 'gradeId': 1, 'commodityId': 1, 'varietyId': 470,
              'commodity': 'Wheat', 'grade': 'APH1', 'variety': 'Adagio', 'productCode': 'wheat-aph1'},
             {'id': mapping_2.id, 'companyId': self.company.id, 'gradeId': 3, 'commodityId': 1, 'varietyId': 470,
              'commodity': 'Wheat', 'grade': 'APH2', 'variety': 'Adagio', 'productCode': 'wheat-aph1'}]
        )

        response = self.client.post(
            f'/companies/{self.company.id}/impex-docs/mappings/products/',
            {
                'impexDocsMappings': [
                    {'commodityId': 1, 'gradeId': 1, 'productCode': 'wheat-aph2', 'varietyId': None},
                    {'commodityId': 1, 'gradeId': 1, 'productCode': 'wheat-aph2', 'varietyId': 470},
                    {'commodityId': 1, 'gradeId': 1, 'productCode': 'wheat-aph2', 'varietyId': 469},
                    {'commodityId': 1, 'gradeId': None, 'productCode': 'wheat-aph2', 'varietyId': None}
                ]
            },
            HTTP_AUTHORIZATION='Token ' + self.employee.refresh_token(),
            format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        mapping_3 = self.company.impex_product_mappings.filter(grade_id=1, variety__isnull=True).first()
        mapping_4 = self.company.impex_product_mappings.filter(grade_id=1, variety_id=469).first()
        mapping_5 = self.company.impex_product_mappings.filter(
            grade__isnull=True, variety_id__isnull=True, commodity_id=1).first()
        self.assertEqual(
            response.data,
            [{'id': mapping_3.id, 'companyId': self.company.id, 'gradeId': 1, 'commodityId': 1, 'varietyId': None,
              'commodity': 'Wheat', 'grade': 'APH1', 'variety': None, 'productCode': 'wheat-aph2'},
             {'commodityId': 1, 'gradeId': 1, 'productCode': 'wheat-aph2', 'varietyId': 470,
              'errors': {'_All_': ['This combination already exists']}},
             {'id': mapping_4.id, 'companyId': self.company.id, 'gradeId': 1, 'commodityId': 1, 'varietyId': 469,
              'commodity': 'Wheat', 'grade': 'APH1', 'variety': 'RGT Accroc', 'productCode': 'wheat-aph2'},
             {'id': mapping_5.id, 'companyId': self.company.id, 'gradeId': None, 'commodityId': 1, 'varietyId': None,
              'commodity': 'Wheat',  'grade': None, 'variety': None, 'productCode': 'wheat-aph2'}]
        )

    def test_get_impex_product_mappings(self):
        self.client.post(
            f'/companies/{self.company.id}/impex-docs/mappings/products/',
            {
                'impexDocsMappings': [
                    {'commodityId': 1, 'gradeId': 1, 'productCode': 'wheat-aph1', 'varietyId': 470},
                    {'commodityId': 1, 'gradeId': 3, 'productCode': 'wheat-aph1', 'varietyId': 470},
                    {'commodityId': 1, 'gradeId': 1, 'productCode': 'wheat-aph2', 'varietyId': None},
                    {'commodityId': 1, 'gradeId': 1, 'productCode': 'wheat-aph2', 'varietyId': 470},
                    {'commodityId': 1, 'gradeId': None, 'productCode': 'wheat-aph2', 'varietyId': 469},
                    {'commodityId': 1, 'gradeId': None, 'productCode': 'wheat-aph3', 'varietyId': None}
                ]
            },
            HTTP_AUTHORIZATION='Token ' + self.employee.refresh_token(),
            format='json')
        self.assertEqual(self.company.impex_product_mappings.count(), 5)

        mapping_1 = self.company.impex_product_mappings.filter(grade_id=1, variety_id=470).first()
        mapping_2 = self.company.impex_product_mappings.filter(grade_id=3, variety_id=470).first()
        mapping_3 = self.company.impex_product_mappings.filter(grade_id=1, variety__isnull=True).first()
        mapping_4 = self.company.impex_product_mappings.filter(grade_id__isnull=True, variety_id=469).first()
        mapping_5 = self.company.impex_product_mappings.filter(
            grade__isnull=True, variety_id__isnull=True, commodity_id=1).first()

        response = self.client.get(
            f'/companies/{self.company.id}/impex-docs/mappings/products/',
            HTTP_AUTHORIZATION='Token ' + self.employee.refresh_token(),
            format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 5)
        self.assertEqual(
            response.data,
            [{'id': mapping_1.id, 'companyId': self.company.id, 'gradeId': 1, 'commodityId': 1, 'varietyId': 470,
              'commodity': 'Wheat', 'grade': 'APH1', 'variety': 'Adagio', 'productCode': 'wheat-aph1'},
             {'id': mapping_2.id, 'companyId': self.company.id, 'gradeId': 3, 'commodityId': 1, 'varietyId': 470,
              'commodity': 'Wheat', 'grade': 'APH2', 'variety': 'Adagio', 'productCode': 'wheat-aph1'},
             {'id': mapping_3.id, 'companyId': self.company.id, 'gradeId': 1, 'commodityId': 1, 'varietyId': None,
              'commodity': 'Wheat', 'grade': 'APH1', 'variety': None, 'productCode': 'wheat-aph2'},
             {'id': mapping_4.id, 'companyId': self.company.id, 'gradeId': None, 'commodityId': 1, 'varietyId': 469,
              'commodity': 'Wheat', 'grade': None, 'variety': 'RGT Accroc', 'productCode': 'wheat-aph2'},
             {'id': mapping_5.id, 'companyId': self.company.id, 'gradeId': None, 'commodityId': 1, 'varietyId': None,
              'commodity': 'Wheat', 'grade': None, 'variety': None, 'productCode': 'wheat-aph3'}]
        )

        order = FreightOrderFactory(customer__company=self.company, commodity_id=1,
                                    planned_grade_id=1, variety_id=470)
        order_1 = FreightOrderFactory(customer__company=self.company, commodity_id=1,
                                      planned_grade_id=1, variety_id=None)
        order_2 = FreightOrderFactory(customer__company=self.company, commodity_id=1,
                                      planned_grade_id=None, variety_id=None)
        self.assertEqual(order.get_product_code(), 'wheat-aph1')
        self.assertEqual(order_1.get_product_code(), 'wheat-aph2')
        self.assertEqual(order_2.get_product_code(), 'wheat-aph3')


@tag('view')
class CompanyImpexDocsProductMappingViewTest(AuthSetup):

    def setUp(self):
        super().setUp()
        self.company_1 = CompanyFactory()
        self.company_1.impex_docs_enabled = True
        self.company_1.save()
        self.employee_1 = EmployeeFactory(company=self.company_1)
        self.client.post(
            f'/companies/{self.company_1.id}/impex-docs/mappings/products/',
            {
                'impexDocsMappings': [
                    {'commodityId': 1, 'gradeId': 1, 'productCode': 'wheat-aph1', 'varietyId': 470},
                    {'commodityId': 1, 'gradeId': 3, 'productCode': 'wheat-aph1', 'varietyId': 470},
                    {'commodityId': 1, 'gradeId': 1, 'productCode': 'wheat-aph2', 'varietyId': None},
                    {'commodityId': 1, 'gradeId': 1, 'productCode': 'wheat-aph2', 'varietyId': 470},
                    {'commodityId': 1, 'gradeId': None, 'productCode': 'wheat-aph2', 'varietyId': 469},
                    {'commodityId': 1, 'gradeId': None, 'productCode': 'wheat-aph3', 'varietyId': None}
                ]
            },
            HTTP_AUTHORIZATION='Token ' + self.employee_1.refresh_token(),
            format='json')

    def test_put_impex_product_mapping(self):
        mapping_1 = self.company_1.impex_product_mappings.filter(commodity_id=1, grade_id=1, variety_id=470).first()
        response = self.client.put(
            path=f'/companies/{self.company_1.id}/impex-docs/mappings/{mapping_1.id}/',
            data={'commodityId': 1, 'gradeId': 2, 'productCode': 'wheat-aph4', 'varietyId': 470},
            HTTP_AUTHORIZATION='Token ' + self.employee_1.refresh_token(),
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mapping_1.refresh_from_db()
        self.assertEqual(mapping_1.grade_id, 2)
        mapping_2 = self.company_1.impex_product_mappings.filter(commodity_id=1, variety_id=469).first()
        response = self.client.put(
            path=f'/companies/{self.company_1.id}/impex-docs/mappings/{mapping_2.id}/',
            data={'commodityId': 1, 'gradeId': None, 'productCode': 'wheat-aph2', 'varietyId': None},
            HTTP_AUTHORIZATION='Token ' + self.employee_1.refresh_token(),
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data, {'error': 'Mapping already exists'})

        response = self.client.put(
            path=f'/companies/{self.company_1.id}/impex-docs/mappings/{mapping_2.id}/',
            data={'commodityId': 1, 'gradeId': 4, 'productCode': 'wheat-aph2', 'varietyId': None},
            HTTP_AUTHORIZATION='Token ' + self.employee_1.refresh_token(),
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mapping_2.refresh_from_db()
        self.assertEqual(mapping_2.grade_id, 4)
        self.assertEqual(mapping_2.variety_id, None)

    def test_delete_impex_product_mapping(self):
        mapping_count = self.company_1.impex_product_mappings.count()
        mapping_1 = self.company_1.impex_product_mappings.filter(commodity_id=1, grade_id=1, variety_id=470).first()
        response = self.client.delete(
            path=f'/companies/{self.company_1.id}/impex-docs/mappings/{mapping_1.id}/',
            HTTP_AUTHORIZATION='Token ' + self.employee_1.refresh_token(),
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(self.company_1.impex_product_mappings.count(), mapping_count - 1)


@tag('view')
class CompaniesDirectoryViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.directory_url = reverse('core.companies:company-directory')

    def add_company_to_directory(self):
        params = factory.build(dict, FACTORY_CLASS=CompanyFactory, **{'owner_company_id': self.company.id})
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        self.other_company = Company.get_or_create(params)
        self.company.add_company_to_directory(self.other_company.id)

    def test_get_no_company(self):
        res = self.client.get(self.directory_url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')
        self.assertEqual(res.status_code, 200)
        self.assertEqual(res.data, [])

    def test_get(self):
        self.add_company_to_directory()
        res = self.client.get(self.directory_url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')
        self.assertEqual(res.status_code, 200)
        self.assertEqual(len(res.data), 1)
        self.assertEqual(res.data[0]['name'], self.other_company.business_name)

@tag('view')
class ManagedCompaniesViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.directory_url = reverse('core.companies:company-managed-company')

    def test_get_success(self):
        res = self.client.get(self.directory_url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')
        self.assertEqual(res.status_code, 200)
        self.assertEqual(res.data, [{
            "name": self.company.business_name, "id": self.company.id,
            'transactionParticipation': self.company.transaction_participation, 'typeId': self.company.type_id
        }])


@tag('view')
class CompanyViewTest(AuthSetup):
    def test_get(self):
        response = self.client.get(
            _get_company_url(0),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        response = self.client.get(
            _get_company_url(self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.get('id'), self.company.id)
        _keys = (_k for _k in COMPANY_VALID_MOCK_DATA.keys() if _k != 'address')
        for _key in _keys:
            self.assertEqual(
                response.data.get(camelize(_key, False)),
                getattr(self.company, _key)
            )

    def test_put_404(self):
        data = {
            "businessName": "foo"
        }
        response = self.client.put(
            _get_company_url(0),
            data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_put_ok(self):
        data = {
            "businessName": "foo bar",
            "address": {
                "name": "australia, 1",
                "address": "Sydney Olympic Park NSW 2127, Australia",
                "latitude": -23.7001391,
                "longitude": 133.8810526
            },
        }
        response = self.client.put(
            _get_company_url(self.company.id),
            data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data.get('businessName'), data['businessName'])
        self.assertEqual(response.data['updatedById'], self.employee.id)
        _response_address = response.data.get('address')
        for key in ['name', 'address', 'latitude', 'longitude']:
            self.assertEqual(data['address'][key], _response_address[key])

    @patch('os.environ.get', Mock(return_value='ci'))
    def test_put_with_change_in_business_type(self):  # pylint: disable=too-many-locals
        from core.jobs.models import Job
        company = CompanyFactory(type_id=BHC_TYPE_ID, business_name='test bhc company')
        storage_company = CompanyFactory()
        storage_company_employee = EmployeeFactory(company=storage_company)

        farm = FarmFactory(company=storage_company, stocks_management=True)
        storage = StorageFactory(farm=farm, type='silo')
        stock_auto_update_alert = Alert.persist({
            'name': STOCK_AUTO_UPDATE_ALERT_CODE,
            'channel': 'email',
            'company_id': storage_company.id,
            'recipients': [
                {
                    'party': 'own_company',
                    'employee_roles': ['company_admin'],
                    'employees': [storage_company_employee.id]
                }
            ]
        })
        self.assertTrue(stock_auto_update_alert.persisted)
        shrinkage_data = [{
            'site_id': farm.id,
            'commodity_id': 1,
            'for_company_id': company.id,
            'percentage': 0.7,
            'start_date': '2020-01-01',
            'end_date': '2044-12-31'
        }]
        Shrinkage.bulk_create(shrinkage_data)

        ngr = NgrFactory(company=company)
        ngr.owner_company_ids = [company.id]
        ngr.save()

        load = LoadFactory(ngr=ngr, commodity_id=1, grade_id=4, season='23/24', storage=storage,
            farm=farm, type='inload', estimated_net_weight=100)

        stock = Stock(ngr_id=load.ngr_id, commodity_id=load.commodity_id, grade_id=load.grade_id,
            season=load.season, storage_id=load.storage_id, tonnage=100, variety_ids=[1])
        stock.save()
        staff_user = EmployeeFactory(is_staff=True)
        data = {
            "typeId": GROWER_TYPE_ID,
        }
        response = self.client.put(
            _get_company_url(company.id),
            data,
            HTTP_AUTHORIZATION='Token ' + staff_user.refresh_token(),
            format='json'
        )
        jobs = Job.objects.filter(
            type='update_shrinkage_with_updated_business_type',
            params={'company_id': company.id, 'companies_with_affecting_stocks': [storage_company.id]}
        )
        self.assertTrue(jobs.exists())
        company.refresh_from_db()
        mail.outbox.clear()
        if job := jobs.first():
            job.run_now()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(company.type_id, GROWER_TYPE_ID)
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, f"AgriChain | Auto Update to Stock owned by {company.name} at your sites")
        self.assertEqual(sent_mail.from_email, FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME)
        self.assertEqual(sent_mail.to, [storage_company_employee.email])
        self.assertIn(f"We would like to inform you that stocks of <strong>{company.name}</strong>"
                      f" have been updated because of change in Shrink applied on loads. This is due to the fact "
                      f"that the company type has changed from Non Grower to Grower.", sent_mail.body)

    def test_put_with_plan_type(self):
        self.company.create_platform_features()
        self.assertEqual(self.company.platformfeatures.plan_type, 'premium')

        staff_user = Employee.objects.filter(is_staff=True).first()
        data = {
            "plan_type": "logistics_lite",
        }

        response = self.client.put(
            _get_company_url(self.company.id),
            data,
            HTTP_AUTHORIZATION='Token ' + staff_user.refresh_token(),
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(pydash.get(response.data, 'platformfeatures.planType'), 'logistics_lite')

    def test_put_with_log_off_time(self):
        staff_user = Employee.objects.filter(is_staff=True).first()
        data = {
            "idle_period_to_logoff": '0.1',
        }
        response = self.client.put(
            _get_company_url(self.company.id),
            data,
            HTTP_AUTHORIZATION='Token ' + staff_user.refresh_token(),
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(pydash.get(response.data, 'idlePeriodToLogoff'), datetime.timedelta(seconds=360))

    def test_put_400(self):
        response = self.client.put(
            _get_company_url(self.company.id),
            COMPANY_INVALID_MOCK_DATA,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data['errors']['abn'],
            ['Ensure this value has at least 11 characters (it has 5).']
        )

    def test_put_duplicate_name_failure(self):
        company = CompanyFactory(type=CompanyType.objects.get(pk=5))
        data = {
            "businessName": company.business_name,
            "address": {
                "name": "australia, 1",
                "address": "Sydney Olympic Park NSW 2127, Australia",
                "latitude": -23.7001391,
                "longitude": 133.8810526
            },
        }
        response = self.client.put(
            _get_company_url(self.company.id),
            data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['errors'], {'businessName': ['Company with this business name already exists']})


@tag('view')
class CompanyEmployeesViewTest(AuthSetup):
    def test_get(self):
        response = self.client.get(
            get_company_employees_url(65420),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIsNone(response.data)

        response = self.client.get(
            get_company_employees_url(self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        for _key in (
                k for k in factory.build(
            dict, FACTORY_CLASS=EmployeeFactory) if k not in ['password', 'company', 'type']
        ):
            self.assertEqual(
                response.data[0].get(camelize(_key, False)),
                getattr(self.employee, _key)
            )
        self.assertFalse(response.data[0]['keyContact'])

        employee = EmployeeFactory(company=self.company, is_active=False)

        self.assertTrue(employee.persisted)
        self.assertFalse(employee.is_active)
        self.assertEqual(employee.company_id, self.company.id)

        response = self.client.get(
            get_company_employees_url(self.company.id) + '?archived',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    @patch('core.companies.views.web_views.KeyContact')
    def test_get_with_key_contact(self, key_contact_klass_mock):
        random_user = EmployeeFactory()
        key_contact_klass_mock.get_for_company = Mock(return_value=self.employee.id)

        response = self.client.get(
            get_company_employees_url(self.company.id),
            HTTP_AUTHORIZATION='Token ' + random_user.refresh_token(),
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertTrue(response.data[0]['keyContact'])
        key_contact_klass_mock.get_for_company.assert_called_once_with(
            requester_company_id=random_user.company_id,
            company_id=self.company.id
        )

    def test_post_success(self):
        params = factory.build(dict, FACTORY_CLASS=CompanyFactory)
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        company = Company.get_or_create(params)
        employee = EmployeeFactory(company=company)
        url = get_company_employees_url(company.id)
        response = self.client.post(
            url,
            EMPLOYEE_VALID_MOCK_DATA,
            HTTP_AUTHORIZATION='Token ' + employee.refresh_token(),
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['email'], '<EMAIL>')
        self.assertEqual(response.data['createdById'], employee.id)
        self.assertIsNotNone(response.data['id'])
        self.assertFalse(response.data['keyContact'])

    def test_post_invalid_company(self):
        _invalid_company_id = 999
        url = get_company_employees_url(_invalid_company_id)
        response = self.client.post(
            url,
            EMPLOYEE_VALID_MOCK_DATA,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_post_invalid_data(self):
        params = factory.build(dict, FACTORY_CLASS=CompanyFactory)
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        company = Company.get_or_create(params)
        url = get_company_employees_url(company.id)
        response = self.client.post(
            url,
            EMPLOYEE_INVALID_MOCK_DATA,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        employee = EmployeeFactory(company=company)
        response = self.client.post(
            url,
            EMPLOYEE_INVALID_MOCK_DATA,
            HTTP_AUTHORIZATION='Token ' + employee.refresh_token(),
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_post_name_duplicate_failure(self):
        url = get_company_employees_url(self.company.id)
        EmployeeFactory(
            first_name=EMPLOYEE_VALID_MOCK_DATA['first_name'], last_name=EMPLOYEE_VALID_MOCK_DATA['last_name'],
            company=self.company
        )
        response = self.client.post(
            url,
            EMPLOYEE_VALID_MOCK_DATA,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['errors'], {
            'firstName': ['Employee with this first name already exists in this company'],
            'lastName': ['Employee with this last name already exists in this company']}
        )

    def test_post_failure_observer_type_employees(self):
        params = factory.build(dict, FACTORY_CLASS=CompanyFactory)
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        company = Company.get_or_create(params)
        url = get_company_employees_url(company.id)
        employee_data = {**EMPLOYEE_VALID_MOCK_DATA, 'type_id': OBSERVER_TYPE_ID}
        response = self.client.post(
            url,
            employee_data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.data['errors']['typeId'],
            ["Observer type employees can't be created by users"]
        )


@tag('view')
class CompanyEmployeeViewTest(AuthSetup):
    def test_get(self):
        response = self.client.get(
            get_company_employee_url(42, 42),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIsNone(response.data)

        response = self.client.get(
            get_company_employee_url(self.company.id, self.employee.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.get('id'), self.employee.id)
        for _key in (k for k in EMPLOYEE_VALID_MOCK_DATA.keys() if k != 'password'):
            self.assertEqual(
                response.data.get(camelize(_key, False)),
                getattr(self.employee, _key)
            )

    def test_get_with_relations(self):
        response = self.client.get(
            get_company_employee_url(
                self.company.id, self.employee.id
            ) + '?many_to_one_relations=company',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(response.data['company'])
        self.assertEqual(response.data.get('id'), self.employee.id)
        for _key in (k for k in EMPLOYEE_VALID_MOCK_DATA.keys() if k != 'password'):
            self.assertEqual(
                response.data.get(camelize(_key, False)),
                getattr(self.employee, _key)
            )

    def test_put_ok(self):
        employee = EmployeeFactory(company=self.company, type_id=2)
        with transaction.atomic():
            location = AddressFactory()
            farm = FarmFactory(address=location, company=self.company)
            data = factory.build(dict, FACTORY_CLASS=EmployeeFactory, **{
                "username": employee.username,
                "type_id": 1,
                "first_name": "alpha",
                "last_name": "beta",
                "mobile": "0434567890",
            })
            data.pop('email', None)
            data.pop('type', None)
            data.pop('company', None)
            response = self.client.put(
                get_company_employee_url(self.company.id, employee.id),
                data,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(response.data['updatedById'], self.employee.id)
            self.assertFalse(response.data['keyContact'])

            data['type_id'] = 2
            data.pop('password', None)
            response = self.client.put(
                get_company_employee_url(self.company.id, employee.id),
                data,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            data['type_id'] = 4
            data['farm_ids'] = [farm.id]
            response = self.client.put(
                get_company_employee_url(self.company.id, employee.id),
                data,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            data['farm_ids'] = []
            response = self.client.put(
                get_company_employee_url(self.company.id, employee.id),
                data,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)

            data['farm_ids'] = [farm.id]
            response = self.client.put(
                get_company_employee_url(self.company.id, employee.id),
                data,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            linked_farms = response.data['linkedFarmNames']
            linked_farms_count = len(linked_farms.strip().split(',')) if linked_farms.strip() else 0
            self.assertGreater(linked_farms_count, 0)

            data['type_id'] = DRIVER_TYPE_ID
            response = self.client.put(
                get_company_employee_url(self.company.id, employee.id),
                data,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            linked_farms = response.data['linkedFarmNames']
            linked_farms_count = len(linked_farms.strip().split(',')) if linked_farms.strip() else 0
            self.assertEqual(linked_farms_count, 0)

    def test_put_200_for_email_update_by_company_admin(self):
        employee = EmployeeFactory(company=self.company, type_id=2, email="<EMAIL>")
        company_admin = EmployeeFactory(
            company=self.company, type_id=COMPANY_ADMIN_TYPE_ID,password="Company1$")
        company_admin.password = make_password(company_admin.password)
        company_admin.save()
        with transaction.atomic():
            data = factory.build(dict, FACTORY_CLASS=EmployeeFactory, **{
                "type_id": 1,
                "first_name": "alpha",
                "last_name": "beta",
                "mobile": "0434567890",
                "email": "<EMAIL>",
                "transaction_password": "Company1$"
            })
            data.pop('type', None)
            data.pop('company', None)
            data.pop('password', None)
            self.assertIsNotNone(data.get('transaction_password'))
            response = self.client.put(
                get_company_employee_url(self.company.id, employee.id),
                data,
                HTTP_AUTHORIZATION='Token ' + company_admin.refresh_token(),
                format='json'
            )
            employee.refresh_from_db()
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(employee.email, "<EMAIL>")

    def test_put_200_for_email_update(self):
        employee = EmployeeFactory(company=self.company, type_id=2, email="<EMAIL>")
        employee.password = make_password(employee.password)
        employee.save()
        with transaction.atomic():
            data = factory.build(dict, FACTORY_CLASS=EmployeeFactory, **{
                "type_id": 1,
                "first_name": "alpha",
                "last_name": "beta",
                "mobile": "0434567890",
                "email": "<EMAIL>",
                "transaction_password": "Password1$"
            })
            data.pop('type', None)
            data.pop('company', None)
            data.pop('password', None)
            self.assertIsNotNone(data.get('transaction_password'))
            response = self.client.put(
                get_company_employee_url(self.company.id, employee.id),
                data,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            employee.refresh_from_db()
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            self.assertEqual(employee.email, "<EMAIL>")

    def test_put_400_for_email_update(self):
        employee = EmployeeFactory(company=self.company, type_id=2, email="<EMAIL>")
        with transaction.atomic():
            data = factory.build(dict, FACTORY_CLASS=EmployeeFactory, **{
                "type_id": 1,
                "first_name": "alpha",
                "last_name": "beta",
                "mobile": "0434567890",
                "email": "<EMAIL>",
                "transaction_password": "fakePassword"
            })
            data.pop('type', None)
            data.pop('company', None)
            response = self.client.put(
                get_company_employee_url(self.company.id, employee.id),
                data,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            employee.refresh_from_db()
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(response.data, {'errors': {'transactionPassword': ['Bad Credentials']}})
            self.assertTrue(employee.email, "<EMAIL>")

    def test_put_403_for_email_update(self):
        employee = EmployeeFactory(company=self.company, type_id=2, email="<EMAIL>")
        with transaction.atomic():
            data = factory.build(dict, FACTORY_CLASS=EmployeeFactory, **{
                "type_id": 1,
                "first_name": "alpha",
                "last_name": "beta",
                "mobile": "0434567890",
                "email": "<EMAIL>",
            })
            data.pop('type', None)
            data.pop('company', None)
            response = self.client.put(
                get_company_employee_url(self.company.id, employee.id),
                data,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            employee.refresh_from_db()
            self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
            self.assertTrue(employee.email, "<EMAIL>")

    def test_put_name_duplicate_failure(self):
        employee = EmployeeFactory(company=self.company)
        employee1 = EmployeeFactory(company=self.company)
        data = {
            "first_name": employee1.first_name,
            "last_name": employee1.last_name,
        }
        with transaction.atomic():
            response = self.client.put(
                get_company_employee_url(self.company.id, employee.id),
                data,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(response.data['errors'], {
                'firstName': ['Employee with this first name already exists in this company'],
                'lastName': ['Employee with this last name already exists in this company']
            })

    def test_put_check_atleast_one_company_admin(self):
        data = {"type_id": 4}

        response = self.client.put(
            get_company_employee_url(self.company.id, self.employee.id),
            data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN) # cant change own role

        superuser = Employee.objects.get(id=1)

        response = self.client.put(
            get_company_employee_url(self.company.id, self.employee.id),
            data,
            HTTP_AUTHORIZATION='Token ' + superuser.refresh_token(),
            format='json'
        )

        self.assertIsNotNone(response.data['errors'])
        self.assertEqual(response.data['errors']['typeId'], ['Atleast one company admin needed'])

    def test_put_check_reset_to_same_password(self):

        employee = EmployeeFactory(company=self.company, type_id=2)
        employee.password = make_password(employee.password)
        employee.save()

        data = {
            "type_id": 1,
            "first_name": "alpha",
            "last_name": "beta",
            "mobile": "0434567890",
            "password": 'Password1$'
        }
        response = self.client.put(
            get_company_employee_url(self.company.id, employee.id),
            data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIsNotNone(response.data['errors'])
        self.assertEqual(response.data['errors']['password'], ['New password cannot be same as old password'])

    def test_put_404(self):
        data = {
            "email": "<EMAIL>",
            "first_name": "Foo",
            "last_name": "Bar",
            "mobile": "0434567890",
        }
        response = self.client.put(
            get_company_employee_url(234234, self.employee.id),
            data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


@tag('view')
class CompanyCompaniesViewTest(AuthSetup):
    def add_company_to_directory(self):
        params = factory.build(dict, FACTORY_CLASS=CompanyFactory, **{'owner_company_id': self.company.id})
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        self.other_company = Company.get_or_create(params)
        self.company.add_company_to_directory(self.other_company.id)

    def test_get_no_companies(self):
        response = self.client.get(
            _get_company_companies_url(self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, [])

    def test_get_has_companies(self):
        self.add_company_to_directory()

        response = self.client.get(
            _get_company_companies_url(self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], self.other_company.id)

    def test_get_has_companies_include_parent_company(self):
        self.add_company_to_directory()

        response = self.client.get(
            _get_company_companies_url(self.company.id) + '?include_parent_company=true',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

        _companies_ids = [company['id'] for company in response.data]
        self.assertTrue(self.other_company.id in _companies_ids)
        self.assertTrue(self.company.id in _companies_ids)

    def test_get_has_companies_assets(self):
        self.add_company_to_directory()

        response = self.client.get(
            _get_company_companies_url(self.company.id) + '?assets=employee',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_get_has_companies_assets_include_parent_company(self):
        self.add_company_to_directory()
        Truck.create({
            **TRUCK_VALID_MOCK_DATA_1,
            'company_id': self.other_company.id,
        })
        Truck.create({
            **TRUCK_VALID_MOCK_DATA_2,
            'company_id': self.other_company.id,
        })

        response = self.client.get(
            _get_company_companies_url(self.company.id) +\
            '?include_parent_company=true' +\
            '&assets=employee',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], self.company.id)

        response = self.client.get(
            _get_company_companies_url(self.company.id) +\
            '?include_parent_company=true' +\
            '&assets=truck',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

        response = self.client.get(
            _get_company_companies_url(self.company.id) +\
            '?include_parent_company=true' +\
            '&assets=employee,truck',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_get_has_companies_that_have_trucks_include_relation(self):
        self.add_company_to_directory()
        _truck = self.other_company.truck_set.create(**TRUCK_VALID_MOCK_DATA_1)
        response = self.client.get(
            _get_company_companies_url(self.company.id) +\
            '?truck__id__gt=0'+\
            '&one_to_many_relations=truck',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0].get('id', None), self.other_company.id)
        self.assertIsNotNone(response.data[0].get('trucks', None))
        trucks = [truck for truck in response.data[0]['trucks'] if truck['rego'] != FLEET_REGO]
        self.assertEqual(len(trucks), 1)
        self.assertEqual(trucks[0].get('id', None), _truck.id)

        _truck.delete()

    def test_get_has_companies_404(self):
        response = self.client.get(
            _get_company_companies_url('786'),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_companies_for_system_users(self):
        original_manager = Company.objects
        employee = Employee.objects.filter(email='<EMAIL>').first()
        _token = Token.objects.filter(user_id=employee.id).first()
        response = self.client.get(
            _get_company_companies_url(employee.company_id),
            HTTP_AUTHORIZATION='Token ' + _token.key,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(
            sorted([response.data[0]['id'], response.data[1]['id']]),
            sorted([employee.company_id, self.company.id])
        )
        Company.objects = original_manager


@tag('view')
class CompanyFarmsViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.location = AddressFactory()
        self.grower_company = CompanyFactory(type_id=1)
        self.logistics_company = CompanyFactory(type_id=3)

    def create_farm(self):
        self.farm = FarmFactory(
            name='Farm 1',
            address_id=self.location.id,
            company_id=self.grower_company.id,
            broker_company_id=self.company.id
        )
        self.farm.create_broker_raised_grower_accepted_request()

    def test_get_grower_company_farms_empty(self):
        response = self.client.get(
            get_company_farms_url(self.grower_company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, [])

    def test_get_grower_company_farms_not_empty(self):
        self.create_farm()

        response = self.client.get(
            get_company_farms_url(self.grower_company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], self.farm.id)
        self.assertIsNotNone(response.data[0]['isAssociated'])

    def test_get_grower_company_farms_with_or_without_unnaccepted(self):
        _common_farm_data = {
            'market_zone_id': 1,
            'company_id': self.grower_company.id,
        }
        _accepted_farm = Farm.create({
            **FARM_VALID_MOCK_DATA_2,
            **_common_farm_data,
            'address_id': Location.create(LOCATION_FARM_MOCK_DATA_2).id,
            'broker_company_id': self.company.id,
        })
        _unaccepted_farm = Farm.create({
            **FARM_VALID_MOCK_DATA_3,
            **_common_farm_data,
            'address_id': Location.create(LOCATION_FARM_MOCK_DATA_3).id,
            'broker_company_id': self.company.id,
        })
        _unaccepted_farm.create_broker_raised_grower_pending_request()
        _no_broker_farm = Farm.create({
            **FARM_VALID_MOCK_DATA_4,
            **_common_farm_data,
            'address_id': Location.create(LOCATION_FARM_MOCK_DATA_4).id,
        })

        response = self.client.get(
            get_company_farms_url(self.grower_company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        _farms_ids = [_item['id'] for _item in response.data]
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)
        self.assertTrue(_accepted_farm.id in _farms_ids)
        self.assertTrue(_unaccepted_farm.id in _farms_ids)
        self.assertTrue(_no_broker_farm.id in _farms_ids)

        response = self.client.get(
            get_company_farms_url(self.grower_company.id) + '?include_unaccepted=true',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        _farms_ids = [_item['id'] for _item in response.data]
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)
        self.assertTrue(_accepted_farm.id in _farms_ids)
        self.assertTrue(_unaccepted_farm.id in _farms_ids)
        self.assertTrue(_no_broker_farm.id in _farms_ids)

        response = self.client.get(
            get_company_farms_url(self.grower_company.id) + '?include_unaccepted=false',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        _farms_ids = [_item['id'] for _item in response.data]
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        self.assertTrue(_accepted_farm.id in _farms_ids)
        self.assertTrue(_no_broker_farm.id in _farms_ids)

        _accepted_farm.delete()
        _unaccepted_farm.delete()
        _no_broker_farm.delete()

    def test_get_broker_company_farms_with_or_without_unaccepted(self):
        _common_farm_data = {
            'market_zone_id': 1,
            'company_id': self.grower_company.id,
            'broker_company_id': self.company.id,
        }
        _accepted_farm = Farm.create({
            **FARM_VALID_MOCK_DATA_2,
            **_common_farm_data,
            'address_id': Location.create(LOCATION_FARM_MOCK_DATA_2).id,
        })
        _accepted_farm.create_broker_raised_grower_accepted_request()
        _unaccepted_farm = Farm.create({
            **FARM_VALID_MOCK_DATA_3,
            **_common_farm_data,
            'address_id': Location.create(LOCATION_FARM_MOCK_DATA_3).id,
        })
        _unaccepted_farm.create_broker_raised_grower_pending_request()
        self.company.add_farm_to_directory(_accepted_farm.id)
        self.company.add_farm_to_directory(_unaccepted_farm.id)

        response = self.client.get(
            get_company_farms_url(self.company.id) + '?include_unaccepted=true',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        _farms_ids = [_item['id'] for _item in response.data]
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        self.assertTrue(_accepted_farm.id in _farms_ids)
        self.assertTrue(_unaccepted_farm.id in _farms_ids)

        response = self.client.get(
            get_company_farms_url(self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        _farms_ids = [_item['id'] for _item in response.data]
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertTrue(_accepted_farm.id in _farms_ids)

        _accepted_farm.delete()
        _unaccepted_farm.delete()

    def test_get_logistics_company_farms_empty(self):
        response = self.client.get(
            get_company_farms_url(self.logistics_company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, [])

    def test_get_logistics_company_farms_with_relations_not_empty(self):
        self.create_farm()
        self.logistics_company.add_company_to_directory(self.grower_company.id)

        _storage = Storage.create_with_location({
            **HOME_STORAGE_VALID_MOCK_DATA,
            'farm_id': self.farm.id,
        })
        _unaccepted_farm = Farm.create({
            **FARM_VALID_MOCK_DATA_3,
            'address_id': Location.create(LOCATION_FARM_MOCK_DATA_3).id,
            'market_zone_id': 1,
            'company_id': self.grower_company.id,
            'broker_company_id': self.company.id,
        })
        _unaccepted_farm.create_broker_raised_grower_pending_request()

        response = self.client.get(
            get_company_farms_url(self.logistics_company.id) +\
            '?relations=*storage__address',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        _storage.address.delete()
        _storage.delete()
        _unaccepted_farm.delete()

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], self.farm.id)

        self.assertIsNotNone(response.data[0].get('storages', None))
        self.assertEqual(len(response.data[0]['storages']), 2)
        self.assertIsNotNone(response.data[0]['storages'][0].get('address', None))

    def test_other_company_farms_that_cant_have_farms(self):
        trading_company = CompanyFactory(type_id=5)

        response = self.client.get(
            get_company_farms_url(trading_company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, [])

    def test_get_farms_for_system_users(self):
        self.create_farm()
        company1 = CompanyFactory(abn='***********')
        location1 = Location.create({
            'name': 'Farm 1',
            'latitude': 123.45,
            'longitude': 54.321,
            'address': 'farm 1 address, australia',
            'location_type': 'farm'
        })
        Farm.create({
            'name': 'farm 3',
            'address_id': location1.id,
            'company_id': company1.id
        })
        company2 = CompanyFactory(abn='***********')
        location2 = Location.create({
            'name': 'Farm 2',
            'latitude': 123.45,
            'longitude': 54.321,
            'address': 'farm 2 address, australia',
            'location_type': 'farm'
        })
        Farm.create({
            'name': 'farm 3',
            'address_id': location2.id,
            'company_id': company2.id
        })
        employee = Employee.objects.filter(email='<EMAIL>').first()
        token = Token.objects.filter(user_id=employee.id).first()
        response = self.client.get(
            get_company_farms_url(employee.company_id),
            HTTP_AUTHORIZATION='Token ' + token.key,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_get_farms_404(self):
        response = self.client.get(
            get_company_farms_url('786'),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 404)

    def test_post_farms_404(self):
        response = self.client.post(
            get_company_farms_url('786'),
            {
                'name': 'Farm 1',
                'address': {
                    'name': 'Farm 1',
                    'latitude': 123.45,
                    'longitude': 54.321,
                    'address': 'farm 1 address, australia',
                    'location_type': 'farm'
                },
                'market_zone_id': 1,
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 404)

    def test_post_success(self):
        employee = EmployeeFactory(company=self.company)
        response = self.client.post(
            get_company_farms_url(self.company.id),
            {
                'name': 'Farm 1',
                'address': {
                    'name': 'Farm 1',
                    'latitude': 123.45,
                    'longitude': 54.321,
                    'address': 'farm 1 address, australia',
                    'location_type': 'farm'
                },
                'market_zone_id': 1,
            },
            HTTP_AUTHORIZATION='Token ' + employee.refresh_token(),
            format='json'
        )

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data['createdById'], employee.id)
        self.assertEqual(response.data['updatedById'], employee.id)
        self.assertIsNotNone(response.data['isAssociated'])

    def test_post_add_to_directory_success(self):
        self.create_farm()
        self.assertFalse(self.employee.company.is_in_farm_directory(self.farm.id))
        response = self.client.post(
            get_company_farms_url(self.farm.company_id),
            {
                'name': 'Farm 1',
                'address': {
                    'name': 'Farm 1',
                    'latitude': 123.45,
                    'longitude': 54.321,
                    'address': 'farm 1 address, australia',
                    'location_type': 'farm'
                },
                'market_zone_id': 1,
                'existing_id': self.farm.id,
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 201)
        self.assertTrue(self.employee.company.is_in_farm_directory(self.farm.id))
        self.assertEqual(response.data['id'], self.farm.id)
        self.assertIsNotNone(response.data['isAssociated'])
        self.assertIsNotNone(response.data['isInAddedCompanies'])
        self.assertIsNotNone(response.data['isPendingRequest'])
        self.assertIsNotNone(response.data['isHighlighted'])
        self.assertIsNotNone(response.data['isManagedByUser'])
        self.assertIsNotNone(response.data['isInUserDirectory'])

    def test_post_farms_400(self):
        response = self.client.post(
            get_company_farms_url(self.company.id),
            {
                'address': {
                    'name': 'Farm 1',
                    'latitude': 123.45,
                    'longitude': 54.321,
                    'address': 'farm 1 address, australia',
                    'location_type': 'farm'
                },
                'market_zone_id': 1,
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.data['errors'],
            {
                'name': ['This field cannot be blank.']
            }
        )

    def test_post_duplicate_name_failure(self):
        self.client.post(
            get_company_farms_url(self.company.id),
            {
                'name': 'Farm Duplicate Test',
                'address': {
                    'name': 'Farm 1',
                    'latitude': 123.45,
                    'longitude': 54.321,
                    'address': 'farm 1 address, australia',
                    'location_type': 'farm'
                },
                'market_zone_id': 1,
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        response = self.client.post(
            get_company_farms_url(self.company.id),
            {
                'name': 'Farm duplicate test',
                'address': {
                    'name': 'Farm 1',
                    'latitude': 123.45,
                    'longitude': 54.321,
                    'address': 'farm 1 address, australia',
                    'location_type': 'farm'
                },
                'market_zone_id': 1,
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data['errors'], {'name': ['Farm with this name already exists in this company']})


@tag('view')
class CompanyTrucksWebViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory(business_name='Trucking Company')
        TruckFactory.create_batch(10, company_id=self.company.id)
        self.assertTrue(Truck.objects.filter(company_id=self.company.id).count() >= 10)

    def test_get_200(self):
        response = self.client.get(
            f'/companies/{self.company.id}/trucks/web/',
            HTTP_AUTHORIZATION=f'Token {self.token}',
            format='json'
        )
        trucks = Truck.objects.filter(company_id=self.company.id).exclude(rego=FLEET_REGO)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), len(trucks))

        response = self.client.get(
            f'/companies/{self.company.id}/trucks/web/?page_size=5',
            HTTP_AUTHORIZATION=f'Token {self.token}',
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 5)

    def test_get_search_200(self):
        truck = TruckFactory(rego='X1T123', company_id=self.company.id)

        response = self.client.get(
            f'/companies/{self.company.id}/trucks/web/?search=X1',
            HTTP_AUTHORIZATION=f'Token {self.token}',
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['id'], truck.id)

        response = self.client.get(
            f'/companies/{self.company.id}/trucks/web/?search=',
            HTTP_AUTHORIZATION=f'Token {self.token}',
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 10)


@tag('view')
class CompanyTrucksViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.truck = self.company.truck_set.create(**TRUCK_VALID_MOCK_DATA_1)

    def test_get_company_trucks_ok(self):
        response = self.client.get(
            _get_company_trucks_url(self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        trucks = [truck for truck in response.data if truck['rego'] != FLEET_REGO]
        self.assertEqual(len(trucks), 1)
        self.assertEqual(trucks[0]['id'], self.truck.id)
        self.assertEqual(trucks[0]['companyId'], self.truck.company_id)
        self.assertIsNotNone(trucks[0]['totalWeights'])


@tag('view')
class CompanyTruckViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.truck = self.company.truck_set.create(**TRUCK_VALID_MOCK_DATA_1)

    def test_get_company_truck_ok(self):
        response = self.client.get(
            _get_company_truck_url(self.company.id, self.truck.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], self.truck.id)
        self.assertEqual(response.data['companyId'], self.truck.company_id)
        self.assertIsNotNone(response.data['totalWeights'])


@tag('view')
class CompanyTrailersViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.truck = self.company.truck_set.create(**TRUCK_VALID_MOCK_DATA_1)
        self.trailer1 = self.company.trailer_set.create(**TRAILER_VALID_MOCK_DATA_1, truck=self.truck)
        self.trailer2 = self.company.trailer_set.create(**TRAILER_VALID_MOCK_DATA_2)

    def test_get_200(self):
        response = self.client.get(
            _get_company_trailers_url(self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(
            sorted([d['id'] for d in response.data]),
            sorted([self.trailer1.id, self.trailer2.id])
        )

    def test_get_404(self):
        response = self.client.get(
            _get_company_trailers_url(76420),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


@tag('view')
class CompanyUnassignedTrailersViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.truck = self.company.truck_set.create(**TRUCK_VALID_MOCK_DATA_1)
        self.trailer1 = self.company.trailer_set.create(**TRAILER_VALID_MOCK_DATA_1, truck=self.truck)
        self.trailer2 = self.company.trailer_set.create(**TRAILER_VALID_MOCK_DATA_2)
        self.trailer3 = self.company.trailer_set.create(**TRAILER_VALID_MOCK_DATA_3)

    def test_get_200(self):
        response = self.client.get(
            _get_company_unassigned_trailers_url(self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(
            sorted([d['id'] for d in response.data]),
            sorted([self.trailer2.id, self.trailer3.id])
        )

    def test_get_404(self):
        response = self.client.get(
            _get_company_unassigned_trailers_url(76420),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


@tag('view')
class BhcSitesViewTest(AuthSetup):
    def test_get_bhc_locations(self):
        company = CompanyFactory(type_id=4)
        bhc_site = FarmFactory(company=company, market_zone_id=1, mode='Rail')

        response = self.client.get(
            reverse('core.companies:bhc-company-locations'),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], bhc_site.id)
        self.assertEqual(response['Cache-Control'], 'private, max-age=86400')


@tag('model')
class CompanyTypeTest(ACTestCase):
    def test_fields(self):
        _company_type = CompanyType()
        _company_type.save()
        self.assertEqual(
            _company_type.errors,
            {
                'name': ['This field cannot be blank.']
            }
        )

        _company_type.name = 'foo'
        _company_type.save()
        self.assertEqual(
            _company_type.errors,
            {
                'name': ["Value 'foo' is not a valid choice."]
            }
        )

        _types = dict(CompanyType.TYPES)
        for _expected_type in ['grower', 'broker', 'logistics', 'bhc', 'trader']:
            self.assertTrue(_expected_type in _types)

    def test_str(self):
        self.assertEqual(str(CompanyType(name='broker')), 'Broker')
        self.assertEqual(str(CompanyType(name='grower')), 'Grower')
        self.assertEqual(str(CompanyType(name='logistics')), 'Logistics')
        self.assertEqual(str(CompanyType(name='bhc')), 'BHC')
        self.assertEqual(str(CompanyType(name='trader')), 'Trader')

    def test_display_name(self):
        self.assertEqual(
            CompanyType(name='broker').display_name,
            'Broker'
        )
        self.assertEqual(
            CompanyType(name='grower').display_name,
            'Grower'
        )
        self.assertEqual(
            CompanyType(name='logistics').display_name,
            'Logistics'
        )
        self.assertEqual(
            CompanyType(name='bhc').display_name,
            'BHC'
        )
        self.assertEqual(
            CompanyType(name='trader').display_name,
            'Trader'
        )
        self.assertIsNone(
            CompanyType(name='foobar').display_name
        )


@tag('view')
class CompanyNgrsViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory()
        self.location = AddressFactory()
        self.farm = FarmFactory(address=self.location, company=self.company)
        self.company_ngrs_url = reverse(
            'core.companies:ngr-company',
            kwargs={'company_id': self.company.id}
        )

    def test_post_created(self):
        params = {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA}
        params['bank_accounts'][0]['entity_for'] = ''
        response = self.client.post(
            self.company_ngrs_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Ngr.objects.count(), 2)
        self.assertEqual(BankAccount.objects.count(), 1)
        _ngr_mock_data = {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA}
        _bank_account_mock_data = _ngr_mock_data.pop('bank_accounts')[0]
        _bank_account_mock_data.pop('ngr_id', False)
        for _key in _ngr_mock_data.keys():
            self.assertEqual(
                response.data.get(camelize(_key, False)),
                params.get(_key)
            )

        _response_banks_data = response.data.get(camelize('bank_accounts', False))
        for _key in _bank_account_mock_data.keys():
            if _key != 'entity_for':
                if _key == 'bank_id':
                    _bank_account_key = _bank_account_mock_data.get('bank')
                else:
                    _bank_account_key = _bank_account_mock_data.get(_key)
                self.assertEqual(
                    _response_banks_data[0].get(camelize(_key, False)),
                    _bank_account_key
                )

    def test_post_no_bank_data(self):
        params = {**NGR_WITHOUT_BANK_ACCOUNT_INVALID_MOCK_DATA}
        response = self.client.post(
            self.company_ngrs_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Ngr.objects.count(), 2)
        self.assertEqual(BankAccount.objects.count(), 0)

    def test_post_with_empty_bank_data(self):
        params = {**NGR_WITH_BANK_ACCOUNT_INVALID_MOCK_DATA}
        response = self.client.post(
            self.company_ngrs_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Ngr.objects.count(), 2)
        self.assertEqual(BankAccount.objects.count(), 0)

    def test_post_invalid_bank_data(self):
        params = {**NGR_WITH_BANK_ACCOUNT_INVALID_MOCK_DATA2}
        response = self.client.post(
            self.company_ngrs_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(Ngr.objects.count(), 1)
        self.assertEqual(BankAccount.objects.count(), 0)
        self.assertIsNotNone(response.data['errors'])
        self.assertIn(camelize('bank_accounts', False), response.data['errors'])

    def test_post_invalid_company_id(self):
        params = {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA}
        params['bank_accounts'][0]['entity_for'] = ''
        _ngrs_url_with_invalid_company_id = reverse(
            'core.companies:ngr-company',
            kwargs={'company_id': 222}
        )
        response = self.client.post(
            _ngrs_url_with_invalid_company_id,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIsNone(response.data)

    def test_get_success(self):
        params = {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA}
        _ngr = Ngr.create_with_banks(self.company.id, params)
        response = self.client.get(
            self.company_ngrs_url,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        _ngr_mock_data = {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA}
        _bank_account_mock_data = _ngr_mock_data.pop('bank_accounts')[0]
        _bank_account_mock_data.pop('ngr_id', False)
        for _key in _ngr_mock_data.keys():
            self.assertEqual(
                response.data[0].get(camelize(_key, False)),
                params.get(_key)
            )

        _response_banks_data = response.data[0].get(camelize('bank_accounts', False))
        for _key in _bank_account_mock_data.keys():
            if _key != 'entity_for':
                if _key == 'bank_id':
                    _bank_account_key = _bank_account_mock_data.get('bank')
                else:
                    _bank_account_key = _bank_account_mock_data.get(_key)
                self.assertEqual(
                    _response_banks_data[0].get(camelize(_key, False)),
                    _bank_account_key
                )


@tag('view')
class CompanyFarmNgrsViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        params = factory.build(dict, FACTORY_CLASS=CompanyFactory)
        params['address'] = factory.build(dict, FACTORY_CLASS=AddressFactory)
        self.company = Company.get_or_create(params)
        self.location = Location.create({**LOCATION_FARM_MOCK_DATA})
        self.farm = Farm.create(FARM_VALID_MOCK_DATA, kwargs={
            'address_id': self.location.id,
            'company_id': self.company.id,
        })
        self.company_farm_ngrs_url = reverse(
            'core.companies:ngr-company-farm',
            kwargs={'company_id': self.company.id}
        )

    def test_get_success(self):
        params = {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA}
        _ngr = Ngr.create_with_banks(self.company.id, params)
        response = self.client.get(
            self.company_farm_ngrs_url,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        _ngr_mock_data = {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA}
        _bank_account_mock_data = _ngr_mock_data.pop('bank_accounts')[0]
        _bank_account_mock_data.pop('ngr_id', False)
        for _key in _ngr_mock_data.keys():
            self.assertEqual(
                response.data[0].get(camelize(_key, False)),
                params.get(_key)
            )

        _response_banks_data = response.data[0].get(camelize('bank_accounts', False))
        for _key in _bank_account_mock_data.keys():
            if _key != 'entity_for':
                if _key == 'bank_id':
                    _bank_account_key = _bank_account_mock_data.get('bank')
                else:
                    _bank_account_key = _bank_account_mock_data.get(_key)
                self.assertEqual(
                    _response_banks_data[0].get(camelize(_key, False)),
                    _bank_account_key
                )


@tag('view')
class CompanyNgrViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory()
        self.location = Location.create({**LOCATION_FARM_MOCK_DATA})
        self.farm = Farm.create(FARM_VALID_MOCK_DATA, kwargs={
            'address_id': self.location.id,
            'company_id': self.company.id,
        })
        self.ngr = Ngr.create_with_banks(
            self.company.id,
            {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA}
        )

    def test_get_ok(self):
        response = self.client.get(
            _get_company_ngr_url(self.company.id, self.ngr.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.get('id'), self.ngr.id)
        _ngr_mock_data = {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA}
        _bank_account_mock_data = _ngr_mock_data.pop('bank_accounts')[0]
        _bank_account_mock_data.pop('ngr_id', False)

        for _key in _ngr_mock_data.keys():
            self.assertEqual(
                response.data.get(camelize(_key, False)),
                _ngr_mock_data.get(_key)
            )

        _response_banks_data = response.data.get(camelize('bank_accounts', False))
        for _key in _bank_account_mock_data.keys():
            if _key != 'entity_for':
                if _key == 'bank_id':
                    _bank_account_key = _bank_account_mock_data.get('bank')
                else:
                    _bank_account_key = _bank_account_mock_data.get(_key)
                self.assertEqual(
                    _response_banks_data[0].get(camelize(_key, False)),
                    _bank_account_key
                )

    def test_get_not_found(self):
        response = self.client.get(
            _get_company_ngr_url(self.company.id, 99),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_put_success(self):
        params = {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA2}
        response = self.client.put(
            _get_company_ngr_url(self.company.id, self.ngr.id),
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(Ngr.objects.count(), 2)
        self.assertEqual(BankAccount.objects.count(), 1)
        _ngr_mock_data = {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA2}
        _bank_account_mock_data = _ngr_mock_data.pop('bank_accounts')[0]
        _bank_account_mock_data.pop('ngr_id', False)
        _response_banks_data = response.data.get(camelize('bank_accounts', False))
        for _key in _bank_account_mock_data.keys():
            if _key == 'bank_id':
                _bank_account_key = _bank_account_mock_data.get('bank')
            else:
                _bank_account_key = _bank_account_mock_data.get(_key)
            self.assertEqual(
                _response_banks_data[0].get(camelize(_key, False)),
                _bank_account_key
            )

    def test_put_invalid_company_id(self):
        params = {**NGR_WITH_BANK_ACCOUNT_VALID_MOCK_DATA}
        params['bank_accounts'][0]['entity_for'] = ''
        response = self.client.put(
            _get_company_ngr_url(99, self.ngr.id),
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_put_invalid_bank_data(self):
        params = {**NGR_WITH_BANK_ACCOUNT_INVALID_MOCK_DATA2}
        response = self.client.put(
            _get_company_ngr_url(self.company.id, self.ngr.id),
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(Ngr.objects.count(), 2)
        self.assertEqual(BankAccount.objects.count(), 1)
        self.assertIsNotNone(response.data['errors'])
        self.assertIn(camelize('bank_accounts', False), response.data['errors'])


@tag('view')
class CompanyLocationsViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company_locations_url = reverse(
            'core.companies:location-company',
            kwargs={'company_id': self.company.id}
        )

    def test_post_bad_request(self):
        params = {
            'marketzone_id': 1,
            'region_id': 1,
        }
        response = self.client.post(
            self.company_locations_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIsNotNone(response.data['errors'])
        for _key in ['name', 'latitude', 'longitude']:
            self.assertTrue(camelize(_key, False) in response.data['errors'])

    def test_post_invalid_company_id(self):
        params = {
            **COMPANY_LOCATION_VALID_MOCK_DATA,
            'marketzone_id': 1,
            'region_id': 1,
        }
        _locations_url_with_invalid_company_id = reverse(
            'core.companies:location-company',
            kwargs={'company_id': 9999}
        )
        response = self.client.post(
            _locations_url_with_invalid_company_id,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIsNone(response.data)

    def test_post_created(self):
        params = {
            **COMPANY_LOCATION_VALID_MOCK_DATA,
            'marketzone_id': 1,
            'region_id': 1,
        }
        response = self.client.post(
            self.company_locations_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        for _key in COMPANY_LOCATION_VALID_MOCK_DATA.keys():
            self.assertEqual(
                response.data.get(camelize(_key, False)),
                params.get(_key)
            )

    def test_get_success(self):
        _location = Location.create(COMPANY_LOCATION_VALID_MOCK_DATA, kwargs={
            'owner_id': self.company.id,
            'marketzone_id': 1,
            'region_id': 1,
        })
        response = self.client.get(
            self.company_locations_url,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertTrue(response.data[0]['name'], 'Abc company location')


@tag('view')
class CompanyLocationViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company_location = Location.create(COMPANY_LOCATION_VALID_MOCK_DATA, kwargs={
            'owner_id': self.company.id,
        })

    def test_get_not_found(self):
        response = self.client.get(
            _get_company_location_url(self.company.id, 9999),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIsNone(response.data)

    def test_get_ok(self):
        response = self.client.get(
            _get_company_location_url(self.company.id, self.company_location.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.get('id'), self.company_location.id)
        for _key in COMPANY_LOCATION_VALID_MOCK_DATA.keys():
            self.assertEqual(
                response.data.get(camelize(_key, False)),
                getattr(self.company_location, _key)
            )

    def test_put_not_found(self):
        response = self.client.put(
            _get_company_location_url(self.company.id, 9999),
            {'name': 'XYZ company location'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIsNone(response.data)

    def test_put_bad_request(self):
        data = {
            'locationType': 'random'
        }
        response = self.client.put(
            _get_company_location_url(self.company.id, self.company_location.id),
            data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        for _key in data.keys():
            self.assertTrue(_key in response.data['errors'])

    def test_put_ok(self):
        data = {**COMPANY_LOCATION_VALID_MOCK_DATA}
        response = self.client.put(
            _get_company_location_url(self.company.id, self.company_location.id),
            data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        for _key in COMPANY_LOCATION_VALID_MOCK_DATA.keys():
            self.assertEqual(
                response.data.get(camelize(_key, False)),
                data.get(_key)
            )


@tag('view')
class CompanySitesViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company_sites_url = reverse(
            'core.companies:company_site-company',
            kwargs={'company_id': self.company.id}
        )

    def test_post_bad_request(self):
        params = {
            'market_zone_id': 1,
            'region_id': 1,
        }
        response = self.client.post(
            self.company_sites_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIsNotNone(response.data['errors'])
        for _key in ['name']:
            self.assertTrue(_key in response.data['errors'])

    def test_post_invalid_company_id(self):
        params = {
            **COMPANY_SITE_VALID_MOCK_DATA,
            'market_zone_id': 1,
            'region_id': 1,
        }
        _compay_sites_url_with_invalid_company_id = reverse(
            'core.companies:company_site-company',
            kwargs={'company_id': 9999}
        )
        response = self.client.post(
            _compay_sites_url_with_invalid_company_id,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIsNone(response.data)

    def test_post_created(self):
        params = {
            **COMPANY_SITE_VALID_MOCK_DATA,
            'market_zone_id': 1,
            'region_id': 1,
        }
        response = self.client.post(
            self.company_sites_url,
            params,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        for _key in ['name', 'phone']:
            self.assertEqual(
                response.data.get(_key),
                params.get(_key)
            )

    def test_get_success(self):
        _company_site = CompanySite.create_with_location(COMPANY_SITE_VALID_MOCK_DATA, kwargs={
            'company_id': self.company.id,
            'market_zone_id': 1,
            'region_id': 1,
        })
        response = self.client.get(
            self.company_sites_url,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

    def test_post_name_duplicate_failure(self):
        params = {
            **COMPANY_SITE_VALID_MOCK_DATA,
            'market_zone_id': 1,
            'region_id': 1,
        }
        with transaction.atomic():
            self.client.post(
                self.company_sites_url,
                params,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            response = self.client.post(
                self.company_sites_url,
                params,
                HTTP_AUTHORIZATION='Token ' + self.token,
                format='json'
            )
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertEqual(response.data['errors'],
                            {'name': ['Site with this name already exists in this company']})


@tag('view')
class CompanySiteViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company_site = CompanySite.create_with_location(COMPANY_SITE_VALID_MOCK_DATA, kwargs={
            'company_id': self.company.id,
            'market_zone_id': 1,
            'region_id': 1,
        })

    def test_get_not_found(self):
        response = self.client.get(
            _get_company_site_url(self.company.id, 9999),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIsNone(response.data)

    def test_get_ok(self):
        response = self.client.get(
            _get_company_site_url(self.company.id, self.company_site.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(response.data)
        self.assertEqual(response.data.get('id'), self.company_site.id)
        for _key in ['name', 'mobile']:
            self.assertEqual(
                response.data.get(_key),
                getattr(self.company_site, _key)
            )

    def test_put_not_found(self):
        response = self.client.put(
            _get_company_site_url(self.company.id, 9999),
            {'name': 'Site 1'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIsNone(response.data)

    def test_put_bad_request(self):
        data = {
            'mobile': 'random'
        }
        response = self.client.put(
            _get_company_site_url(self.company.id, self.company_site.id),
            data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        for _key in data.keys():
            self.assertTrue(_key in response.data['errors'])

    def test_put_ok(self):
        data = {
            **COMPANY_SITE_VALID_MOCK_DATA,
            'market_zoneId': 1,
            'regionId': 1,
        }
        response = self.client.put(
            _get_company_site_url(self.company.id, self.company_site.id),
            data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        for _key in ['name', 'phone', 'email']:
            self.assertEqual(
                response.data.get(_key),
                data.get(_key)
            )


@tag('view')
class CompanyTypesViewTest(AuthSetup):
    def test_get(self):
        response = self.client.get(
            '/companies/types/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 5)
        for item in response.data:
            self.assertEqual(
                list(item.keys()),
                ['id', 'name', 'entity', 'isActive', 'displayName']
            )


@tag('view')
class GroupCompaniesViewTest(AuthSetup):

    def setUp(self):
        super().setUp()
        self.sub_company_1 = CompanyFactory()
        self.group_1 = CompanyGroupFactory(owner_company=self.company)
        self.group_2 = CompanyGroupFactory(owner_company=self.company)
        self.group_3 = CompanyGroupFactory(owner_company=self.company)
        self.sub_company_1.groups.add(self.group_1)
        self.sub_company_2 = CompanyFactory()
        self.sub_company_2.groups.add(self.group_1)
        self.sub_company_3 = CompanyFactory()
        self.sub_company_3.groups.add(self.group_2)
        self.sub_company_4 = CompanyFactory()

    def test_get(self):
        Company.add_companies_to_directory(self.company.id,
                                           [self.sub_company_1.id, self.sub_company_2.id, self.sub_company_3.id])

        url = '/companies/' + str(self.company.id) + '/groups/' + str(self.group_1.id) + '/companies/'

        response = self.client.get(
            path=url,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 2)

        url = '/companies/' + str(self.company.id) + '/groups/' + str(self.group_2.id) + '/companies/'
        response = self.client.get(
            path=url,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['id'], self.sub_company_3.id)

        url = '/companies/' + str(self.company.id) + '/groups/' + str(self.group_3.id) + '/companies/'
        response = self.client.get(
            path=url,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 0)


@tag('view')
class CompanyGroupsViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.group_1 = CompanyGroupFactory(owner_company=self.company)
        self.group_2 = CompanyGroupFactory(owner_company=self.company)
        self.group_3 = CompanyGroupFactory(owner_company=self.company)

        self.sub_company_1 = CompanyFactory()
        self.sub_company_1.groups.add(self.group_1)
        self.sub_company_2 = CompanyFactory()
        self.sub_company_2.groups.add(self.group_1)
        self.sub_company_3 = CompanyFactory()
        self.sub_company_3.groups.add(self.group_2)
        self.sub_company_4 = CompanyFactory()
        self.sub_company_4.groups.add(self.group_2)
        self.sub_company_5 = CompanyFactory()
        self.group_4 = CompanyGroupFactory(owner_company=self.sub_company_4)

    def test_get_200(self):
        url = '/companies/' + str(self.company.id) + '/groups/'
        response = self.client.get(
            path=url,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
        group_ids = [group['id'] for group in response.data]
        self.assertIn(self.group_1.id, group_ids)
        self.assertIn(self.group_2.id, group_ids)
        self.assertIn(self.group_3.id, group_ids)

    def test_post_remove_companies_from_group(self):
        Company.add_companies_to_directory(self.company.id,
                                           [self.sub_company_1.id, self.sub_company_2.id,
                                            self.sub_company_3.id, self.sub_company_4.id, self.sub_company_5.id])

        url = '/companies/' + str(self.company.id) + '/groups/?remove_companies=true'
        removing_companies_from_group = \
            {'company_ids': [self.sub_company_1.id, self.sub_company_3.id]}
        response = self.client.post(
            path=url,
            data=removing_companies_from_group,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 204)
        self.assertFalse(self.sub_company_1.groups.filter(owner_company=self.company).exists())
        self.assertFalse(self.sub_company_3.groups.filter(owner_company=self.company).exists())

    def test_post_rename_groups(self):
        Company.add_companies_to_directory(self.company.id,
                                           [self.sub_company_1.id, self.sub_company_2.id,
                                            self.sub_company_3.id, self.sub_company_4.id, self.sub_company_5.id])
        url = '/companies/' + str(self.company.id) + '/groups/'
        renaming_group_data = {'groupName': 'Test', 'groupType': FEES, 'groupId': self.group_1.id}
        response = self.client.post(
            path=url,
            data=renaming_group_data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 204)
        self.assertEqual(self.sub_company_1.groups.filter(owner_company=self.company).first().name, 'Test')
        self.assertEqual(self.sub_company_2.groups.filter(owner_company=self.company).first().name, 'Test')

        renaming_group_data = {'groupName': 'Test', 'groupType': FEES, 'groupId': self.group_3.id}
        response = self.client.post(
            path=url,
            data=renaming_group_data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 204)

        renaming_group_data = {'groupName': 'Test', 'groupType': FEES, 'groupId': self.group_4.id}
        response = self.client.post(
            path=url,
            data=renaming_group_data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 204)

    def test_post_create_group(self):
        Company.add_companies_to_directory(self.company.id,
                                           [self.sub_company_1.id, self.sub_company_2.id,
                                            self.sub_company_3.id, self.sub_company_4.id, self.sub_company_5.id])
        url = '/companies/' + str(self.company.id) + '/groups/'
        new_group_data = {'groupName': 'Group-4', 'groupType': FEES, 'companyIds': None}
        self.assertEqual(CompanyGroup.objects.filter(owner_company_id=self.company.id).count(), 3)
        response = self.client.post(
            path=url,
            data=new_group_data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 204)
        self.assertEqual(CompanyGroup.objects.filter(owner_company_id=self.company.id).count(), 4)

    def test_post_add_to_existing_group(self):
        Company.add_companies_to_directory(self.company.id,
                                           [self.sub_company_1.id, self.sub_company_2.id,
                                            self.sub_company_3.id, self.sub_company_4.id, self.sub_company_5.id])
        url = '/companies/' + str(self.company.id) + '/groups/'
        new_group_data = {'groupName': self.group_1.name, 'groupType': FEES,
                          'companyIds': [self.sub_company_3.id, self.sub_company_5.id]}
        self.assertEqual(self.sub_company_3.groups.filter(owner_company_id=self.company.id).first().id,
                         self.group_2.id)
        self.assertFalse(self.sub_company_5.groups.filter(owner_company_id=self.company.id).exists())
        response = self.client.post(
            path=url,
            data=new_group_data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(self.sub_company_1.groups.filter(owner_company_id=self.company.id).first().name,
                         self.group_1.name)

        self.assertEqual(self.sub_company_5.groups.filter(owner_company_id=self.company.id).first().name,
                         self.group_1.name)

    def test_post_add_companies_to_new_group(self):
        Company.add_companies_to_directory(self.company.id,
                                           [self.sub_company_1.id, self.sub_company_2.id,
                                            self.sub_company_3.id, self.sub_company_4.id, self.sub_company_5.id])
        url = '/companies/' + str(self.company.id) + '/groups/'
        new_group_data = {'groupName': 'Sample', 'groupType': FEES,
                          'companyIds': [self.sub_company_1.id, self.sub_company_5.id]}
        self.assertEqual(self.sub_company_1.groups.filter(owner_company_id=self.company.id).first().id,
                         self.group_1.id)
        self.assertFalse(self.sub_company_5.groups.filter(owner_company_id=self.company.id).exists())
        response = self.client.post(
            path=url,
            data=new_group_data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        new_group = CompanyGroup.objects.filter(owner_company_id=self.company.id, name='Sample', type=FEES).first()
        self.assertEqual(self.sub_company_1.groups.filter(owner_company_id=self.company.id).first().name,
                         new_group.name)

        self.assertEqual(self.sub_company_5.groups.filter(owner_company_id=self.company.id).first().name,
                         new_group.name)


@tag('view')
class CompanyRegisterViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.system = Company.moderators.first()
        self.staff_user = Employee.objects.filter(is_staff=True).first()
        self.admin_token = self.staff_user.refresh_token()

    def test_put_404(self):
        response = self.client.put(
            '/companies/432/register/',
            {},
            HTTP_AUTHORIZATION='Token ' + self.admin_token,
            format='json'
        )

        self.assertEqual(response.status_code, 404)

    @patch('core.companies.views.web_views.Company.get_company_admins')
    @patch('core.companies.views.web_views.Company.mark_registered')
    def test_put_200_only_email(self, mark_registered_mock, get_company_admins_mock):
        admin_mock = Mock()
        admin_mock.schedule_mark_registered_mail = Mock()
        get_company_admins_mock.return_value = [admin_mock]

        response = self.client.put(
            '/companies/' + str(self.company.id) + '/register/',
            {'mail': True},
            HTTP_AUTHORIZATION='Token ' + self.admin_token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)

        mark_registered_mock.assert_not_called()
        get_company_admins_mock.assert_called_once()

    @patch('core.companies.views.web_views.Company.get_company_admins')
    @patch('core.companies.views.web_views.Company.mark_registered')
    def test_put_200(self, mark_registered_mock, get_company_admins_mock):
        admin_mock = Mock()
        admin_mock.schedule_mark_registered_mail = Mock()
        get_company_admins_mock.return_value = [admin_mock]

        response = self.client.put(
            '/companies/' + str(self.company.id) + '/register/',
            {'register': True, 'mail': True},
            HTTP_AUTHORIZATION='Token ' + self.admin_token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)

        mark_registered_mock.assert_called_once()
        get_company_admins_mock.assert_called_once()


@tag('view')
class CompanyPurgeViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.system = Company.moderators.first()
        self.super_user = Employee.objects.filter(is_superuser=True).first()
        self.admin_token = self.super_user.refresh_token()

    @patch('core.companies.views.web_views.Company')
    def test_delete_422(self, company_klass_mock):
        delete_mock = Mock()
        delete_mock.side_effect = Exception('foo')
        manager_mock = Mock()
        manager_mock.filter = Mock(return_value=Mock(delete=delete_mock))
        company_klass_mock.objects = manager_mock

        response = self.client.delete(
            '/companies/123/purge/',
            HTTP_AUTHORIZATION='Token ' + self.admin_token,
            format='json'
        )

        self.assertEqual(
            response.data,
            {
                'alert': "Couldn't delete. Please contact AgriChain Back Office Support Team."
            }
        )

        self.assertEqual(response.status_code, 422)
        manager_mock.filter.assert_called_once_with(id=123)
        manager_mock.filter().delete.assert_called_once()

    @patch('core.companies.views.web_views.Company')
    def test_delete_202(self, company_klass_mock):
        manager_mock = Mock()
        manager_mock.filter = Mock(return_value=Mock(delete=Mock(return_value=True)))
        company_klass_mock.objects = manager_mock

        response = self.client.delete(
            '/companies/123/purge/',
            HTTP_AUTHORIZATION='Token ' + self.admin_token,
            format='json'
        )

        self.assertEqual(response.status_code, 202)
        manager_mock.filter.assert_called_once_with(id=123)
        manager_mock.filter().delete.assert_called_once()


@tag('view')
class CompanyNgrsMinimalisticViewTest(AuthSetup):
    def test_get_200_ok(self):
        company = Company(id=1)
        ngr1 = NgrFactory(company=company, ngr_number='UNKNOWN_COMPANY')
        ngr1.company_id = company.id
        ngr1.ngr_type = 'single'
        ngr1.save()

        url = reverse('core.companies:company-ngrs', kwargs={'company_id': company.id})

        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.data,
            [
                {
                    'companyId': company.id,
                    'id': ngr1.id,
                    'isVerified': False,
                    'ngrNumber': ngr1.ngr_number,
                    'ngrType': ngr1.ngr_type,
                    'shareHolderEntityNames': ngr1.share_holder_entity_names,
                    'secondaryShareholderCompanyIds': [],
                    'primaryShareholderCompanyIds': [company.id],
                    'shareholdersAbn': ngr1.shareholders_abn,
                    'shareholdersEntityName': ngr1.shareholders_entity_name,
                    'shareholdersName': ngr1.shareholders_name,
                    'sustainableDeclarations': []
                },
            ]
        )

        company_2 = CompanyFactory()
        ngr_2 = NgrFactory(company=company_2)

        url = reverse('core.companies:company-ngrs', kwargs={'company_id': company_2.id})

        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.data,
            [
                {
                    'companyId': company_2.id,
                    'id': ngr_2.id,
                    'isVerified': False,
                    'ngrNumber': ngr_2.ngr_number,
                    'ngrType': ngr_2.ngr_type,
                    'shareHolderEntityNames': ngr_2.share_holder_entity_names,
                    'secondaryShareholderCompanyIds': [],
                    'primaryShareholderCompanyIds': [company_2.id],
                    'shareholdersAbn': ngr_2.shareholders_abn,
                    'shareholdersEntityName': ngr_2.shareholders_entity_name,
                    'shareholdersName': ngr_2.shareholders_name,
                    'sustainableDeclarations': []
                },
            ]
        )


@tag('view')
class CompaniesMobileViewTest(AuthSetup):

    def test_get(self):
        company = CompanyFactory(type=CompanyType.objects.get(pk=5))
        site1 = CompanySiteFactory(company=company)
        site2 = CompanySiteFactory(company=company)
        site3 = CompanySiteFactory(company=company)

        url = reverse('core.companies:company-companies_mobiles') + "?type_id=5"
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token)

        self.assertEqual(len(response.data), 1)
        response_sites = response.data[0].pop('sites')
        self.assertEqual(
            sorted(response_sites, key=lambda site: site['id']),
            sorted([
                {
                    'id': site1.id,
                    'companyId': company.id,
                    'name': site1.name,
                    'phone': site1.phone,
                    'isBhc': site1.is_bhc,
                    'isActive': site1.is_active,
                    'entity': site1.entity,
                    'address': {
                        'id': site1.address.id,
                        'name': site1.address.name,
                        'address': site1.address.address,
                        'latitude': site1.address.latitude,
                        'longitude': site1.address.longitude,
                        'locationType': site1.address.location_type,
                        'entity': site1.address.entity,
                        'isActive': site1.address.is_active,
                    },
                },
                {
                    'id': site2.id,
                    'companyId': company.id,
                    'name': site2.name,
                    'phone': site2.phone,
                    'isBhc': site2.is_bhc,
                    'isActive': site2.is_active,
                    'entity': site2.entity,
                    'address': {
                        'id': site2.address.id,
                        'name': site2.address.name,
                        'address': site2.address.address,
                        'latitude': site2.address.latitude,
                        'longitude': site2.address.longitude,
                        'locationType': site2.address.location_type,
                        'entity': site2.address.entity,
                        'isActive': site2.address.is_active,
                    },
                },
                {
                    'id': site3.id,
                    'companyId': company.id,
                    'name': site3.name,
                    'phone': site3.phone,
                    'isBhc': site3.is_bhc,
                    'isActive': site3.is_active,
                    'entity': site3.entity,
                    'address': {
                        'id': site3.address.id,
                        'name': site3.address.name,
                        'address': site3.address.address,
                        'latitude': site3.address.latitude,
                        'longitude': site3.address.longitude,
                        'locationType': site3.address.location_type,
                        'entity': site3.address.entity,
                        'isActive': site3.address.is_active,
                    },
                }
            ], key=lambda site: site['id'])
        )
        self.assertEqual(
            response.data,
            [{
                'id': company.id,
                'abn': company.abn,
                'name': company.name,
                'typeId': company.type_id,
                'entityName': company.entity_name,
                'businessName': company.business_name,
                'mobile': company.mobile,
                'website': company.website,
                'entity': 'company',
                'isActive': company.is_active,
                'address': {
                    'id': company.address.id,
                    'name': company.address.name,
                    'address': company.address.address,
                    'latitude': company.address.latitude,
                    'longitude': company.address.longitude,
                    'locationType': company.address.location_type,
                    'entity': company.address.entity,
                    'isActive': company.address.is_active,
                },
            }]
        )


@tag('view')
class CompanyAdminViewTest(AuthSetup):
    def test_get_404(self):
        url = reverse('core.companies:company-admins', kwargs={'company_id': 9999})
        response = self.client.get(path=url)
        self.assertEqual(response.status_code, 404)

    def test_get_success(self):
        url = reverse('core.companies:company-admins', kwargs={'company_id': self.company.id})
        response = self.client.get(path=url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {'emails': ['<EMAIL>']})


@tag('view')
class ClaimCompanyViewTest(AuthSetup):
    def test_post_404(self):
        url = reverse('core.companies:employee-claim_company', kwargs={'company_id': 9999})
        data = {
            'first_name': 'first',
            'last_name': 'last',
            'mobile': '0432323212',
            'email': '<EMAIL>',
            'username': '<EMAIL>',
            'claim_details': 'Test Claim'
        }
        response = self.client.post(path=url, data=data, format='json')
        self.assertEqual(response.status_code, 404)

    @patch('core.companies.views.web_views.Job')
    @patch('core.companies.views.web_views.Slack.log')
    def test_post_success_registered(self, mock_log_to_slack, mock_job):
        company = CompanyFactory()
        mock_job.schedule_job_for_task = Mock()
        data = {
            'first_name': 'first',
            'last_name': 'last',
            'mobile': '0432323212',
            'email': '<EMAIL>',
            'username': '<EMAIL>',
            'claim_details': 'Test Claim'
        }
        url = reverse('core.companies:employee-claim_company', kwargs={'company_id': company.id})
        response = self.client.post(path=url, data=data, format='json')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {'success': True})
        mock_log_to_slack.assert_called_once_with(
            'A new registered company claim request has been received with the following details:\n'
            '*Environment*: {1}\n'
            '*Claimant Name*: first last\n'
            '*Claimant Mobile*: 0432323212\n'
            '*Claimant Email*: <EMAIL>\n'
            '*Claimant Username*: <EMAIL>\n'
            '*Claimed Company ABN*: {0.abn}\n'
            '*Claimed Company*: {0.business_name}\n'
            '*Company Claim Details*: Test Claim\n'.format(company, settings.ENV)
        )
        mock_job.schedule_job_for_task.assert_called_once_with(
            'send_email_for_claim_company',
            params={
                'params': {
                    'status': 'registered',
                    'env': settings.ENV,
                    'name': 'first last',
                    'mobile': '0432323212',
                    'email': '<EMAIL>',
                    'username': '<EMAIL>',
                    'abn': company.abn,
                    'business_name': company.business_name,
                    'claim_details': 'Test Claim'
                }
            }
        )

    @patch('core.companies.views.web_views.Job')
    @patch('core.companies.views.web_views.Employee')
    @patch('core.companies.views.web_views.Slack.log')
    def test_post_success_unregistered(self, mock_log_to_slack, mock_employee, mock_job):
        company = CompanyFactory()
        company.owner_company_id = 1
        company.save()

        mock_employee.add_to_company = Mock(return_value=Employee(id=2))
        mock_job.schedule_job_for_task = Mock()
        data = {
            'first_name': 'first',
            'last_name': 'last',
            'mobile': '0432323212',
            'email': '<EMAIL>',
            'username': '<EMAIL>',
            'claim_details': 'Test Claim'
        }
        url = reverse('core.companies:employee-claim_company', kwargs={'company_id': company.id})
        response = self.client.post(path=url, data=data, format='json')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {'success': True})
        mock_log_to_slack.assert_called_once_with(
            'A new unregistered company claim request has been received with the following details:\n'
            '*Environment*: {1}\n'
            '*Claimant Name*: first last\n'
            '*Claimant Mobile*: 0432323212\n'
            '*Claimant Email*: <EMAIL>\n'
            '*Claimant Username*: <EMAIL>\n'
            '*Claimed Company ABN*: {0.abn}\n'
            '*Claimed Company*: {0.business_name}\n'
            '*Company Claim Details*: Test Claim\n'.format(company, settings.ENV)
        )
        mock_job.schedule_job_for_task.assert_called_once_with(
            'send_email_for_claim_company',
            params={
                'params': {
                    'status': 'unregistered',
                    'env': settings.ENV,
                    'name': 'first last',
                    'mobile': '0432323212',
                    'email': '<EMAIL>',
                    'username': '<EMAIL>',
                    'abn': company.abn,
                    'business_name': company.business_name,
                    'claim_details': 'Test Claim'
                }
            }
        )
        mock_employee.add_to_company.assert_called_once_with(company, {
            'first_name': 'first',
            'last_name': 'last',
            'mobile': '0432323212',
            'email': '<EMAIL>',
            'username': '<EMAIL>',
        })

    @patch('core.companies.views.web_views.Job')
    @patch('core.companies.views.web_views.Employee')
    @patch('core.companies.views.web_views.Slack.log')
    def test_post_fail_unregistered(self, mock_log_to_slack, mock_employee, mock_job):
        company = CompanyFactory()
        company.owner_company_id = 1
        company.save()

        mock_employee.add_to_company = Mock(return_value=Employee())
        mock_job.schedule_job_for_task = Mock()
        data = {
            'first_name': 'first',
            'last_name': 'last',
            'mobile': '0432323212',
            'email': '<EMAIL>',
            'username': '<EMAIL>',
            'claim_details': 'Test Claim'
        }
        url = reverse('core.companies:employee-claim_company', kwargs={'company_id': company.id})
        response = self.client.post(path=url, data=data, format='json')

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'success': False})
        mock_log_to_slack.assert_not_called()
        mock_job.schedule_job_for_task.assert_not_called()
        mock_employee.add_to_company.assert_called_once_with(company, {
            'first_name': 'first',
            'last_name': 'last',
            'mobile': '0432323212',
            'email': '<EMAIL>',
            'username': '<EMAIL>',
        })


@tag('view')
class CompaniesNamesViewTest(AuthSetup):
    def test_get_list_ok(self):
        url = reverse('core.companies:company-names')
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], COMPANY_VALID_MOCK_DATA['business_name'])


@tag('view')
class CompanyCanCreateEmployeeViewTest(AuthSetup):
    def test_get_404(self):
        response = self.client.get(
            '/companies/99999/employees/new/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(response.status_code, 404)

    @patch('core.companies.views.web_views.Company')
    def test_get_200(self, mock_company_class):
        company_mock = Mock()
        company_mock.cannot_create_employee_reasons = Mock(return_value=['reason1'])
        manager_mock = Mock()
        manager_mock.get = Mock(return_value=company_mock)
        mock_company_class.objects = manager_mock
        response = self.client.get(
            '/companies/99999/employees/new/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.data,
            {
                'result': False,
                'reasons': ['reason1']
            }
        )

        manager_mock.get.assert_called_once_with(id=99999)
        company_mock.cannot_create_employee_reasons.assert_called_once_with(self.employee)


@tag('view')
class CompanyCanCreateNgrViewTest(AuthSetup):
    def test_get_404(self):
        response = self.client.get(
            '/companies/99999/ngrs/new/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(response.status_code, 404)

    @patch('core.companies.views.web_views.Company')
    def test_get_200(self, mock_company_class):
        company_mock = Mock()
        company_mock.cannot_create_ngr_reasons = Mock(return_value=['reason1'])
        manager_mock = Mock()
        manager_mock.get = Mock(return_value=company_mock)
        mock_company_class.objects = manager_mock
        response = self.client.get(
            '/companies/99999/ngrs/new/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.data,
            {
                'result': False,
                'reasons': ['reason1']
            }
        )

        manager_mock.get.assert_called_once_with(id=99999)
        company_mock.cannot_create_ngr_reasons.assert_called_once_with(self.employee)


@tag('view')
class UnregisteredCompanyEmployeesViewTest(AuthSetup):
    def test_post_404(self):
        response = self.client.post('/companies/unregistered/9999/employees/', data={'password': 'test'}, format='json')
        self.assertEqual(response.status_code, 404)

    @patch('core.companies.views.web_views.Slack.log')
    def test_post_success(self, mock_log_to_slack):
        company = CompanyFactory()
        company.owner_company_id = self.company.id
        company.save()

        self.assertFalse(company.is_registered)

        response = self.client.post(
            '/companies/unregistered/{}/employees/'.format(company.id),
            data={
                'type_id': 1, 'first_name': 'Test', 'last_name': 'Last', 'password': 'Password1$',
                'mobile': '0412121212', 'email': '<EMAIL>', 'username': '<EMAIL>',
            }, format='json'
        )

        self.assertEqual(response.status_code, 201)
        self.assertIsNotNone(response.data['id'])
        self.assertEqual(response.data['farmIds'], [])

        company.refresh_from_db()
        self.assertTrue(company.is_registered)
        self.assertTrue(mock_log_to_slack.called)

    @patch('core.companies.views.web_views.Slack.log')
    def test_post_success_single_farm(self, mock_log_to_slack):
        company = CompanyFactory()
        company.owner_company_id = self.company.id
        company.type_id = 1
        company.save()

        farm = FarmFactory(company=company)

        self.assertFalse(company.is_registered)

        response = self.client.post(
            '/companies/unregistered/{}/employees/'.format(company.id),
            data={
                'type_id': 1, 'first_name': 'Test', 'last_name': 'Last', 'password': 'Password1$',
                'mobile': '0412121212', 'email': '<EMAIL>', 'username': '<EMAIL>'
            }, format='json'
        )

        self.assertEqual(response.status_code, 201)
        self.assertIsNotNone(response.data['id'])
        self.assertEqual(response.data['farmIds'], [farm.id])

        company.refresh_from_db()
        self.assertTrue(company.is_registered)
        self.assertTrue(mock_log_to_slack.called)

    @patch('core.companies.views.web_views.Slack.log')
    def test_post_name_duplicate_failure(self, mock_log_to_slack):
        company = CompanyFactory()
        company.owner_company_id = self.company.id
        company.type_id = 1
        company.save()

        FarmFactory(company=company)
        employee = EmployeeFactory(company=company)

        self.assertFalse(company.is_registered)
        response = self.client.post(
            '/companies/unregistered/{}/employees/'.format(company.id),
            data={
                'type_id': 1, 'first_name': employee.first_name, 'last_name': employee.last_name,
                'password': 'Password1$', 'mobile': '0412121212', 'email': '<EMAIL>',
                'username': '<EMAIL>'
            }, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data['errors'], {
            'firstName': ['Employee with this first name already exists in this company'],
            'lastName': ['Employee with this last name already exists in this company']
        })

        company.refresh_from_db()
        self.assertFalse(company.is_registered)
        self.assertFalse(mock_log_to_slack.called)


@tag('view')
class UnregisteredCompanyEmployeeViewTest(AuthSetup):
    def test_post_404(self):
        response = self.client.put(
            '/companies/unregistered/9999/employees/9999/', data={'password': 'test'}, format='json'
        )
        self.assertEqual(response.status_code, 404)

    @patch('core.companies.views.web_views.Slack.log')
    def test_put_success(self, mock_log_to_slack):
        company = CompanyFactory()
        company.owner_company_id = self.company.id
        company.save()
        employee = EmployeeFactory(company=company)

        self.assertFalse(company.is_registered)

        response = self.client.put(
            '/companies/unregistered/{}/employees/{}/'.format(company.id, employee.id),
            data={
                'type_id': 1, 'first_name': 'Test', 'last_name': 'Last', 'password': 'Password1$',
            }, format='json'
        )

        self.assertEqual(response.status_code, 201)
        self.assertIsNotNone(response.data['id'])
        self.assertEqual(response.data['farmIds'], [])

        company.refresh_from_db()
        employee.refresh_from_db()

        self.assertTrue(employee.type_id, 1)
        self.assertTrue(employee.first_name, 'Test')
        self.assertTrue(employee.last_name, 'Last')
        self.assertTrue(company.is_registered)
        self.assertTrue(mock_log_to_slack.called)

    def test_put_success_single_farm(self):
        company = CompanyFactory()
        company.owner_company_id = self.company.id
        company.type_id = 1
        company.save()
        employee = EmployeeFactory(company=company)

        farm = FarmFactory(company=company)

        self.assertFalse(company.is_registered)

        response = self.client.put(
            '/companies/unregistered/{}/employees/{}/'.format(company.id, employee.id),
            data={
                'type_id': 1, 'first_name': 'Test', 'last_name': 'Last',
            }, format='json'
        )

        self.assertEqual(response.status_code, 201)
        self.assertIsNotNone(response.data['id'])
        self.assertEqual(response.data['farmIds'], [farm.id])

        company.refresh_from_db()
        employee.refresh_from_db()

        self.assertTrue(employee.type_id, 1)
        self.assertTrue(employee.first_name, 'Test')
        self.assertTrue(employee.last_name, 'Last')
        self.assertTrue(employee.farm_ids, [farm.id])
        self.assertTrue(company.is_registered)


@tag('view')
class CompanyMergeViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.merger_company = CompanyFactory()
        self.merge_to_company = CompanyFactory()
        self.merger_company_location = Location.create({**LOCATION_FARM_MOCK_DATA})
        self.merger_company_farm = Farm.create(FARM_VALID_MOCK_DATA, kwargs={
            'address_id': self.merger_company_location.id,
            'company_id': self.merger_company.id,
        })
        self.merge_to_company_location = Location.create({**LOCATION_FARM_MOCK_DATA_2})
        self.merge_to_company_farm = Farm.create(FARM_VALID_MOCK_DATA_2, kwargs={
            'address_id': self.merge_to_company_location.id,
            'company_id': self.merge_to_company.id,
        })
        self.admin_token = Employee.objects.get(id=1).refresh_token()

    def test_post_success(self):
        response = self.client.post(
            '/companies/{}/merge/?purge_company_id={}'.format(self.merge_to_company.id, self.merger_company.id),
            data={
                "farms": {"transfer":[self.merger_company_farm.id],"merge":[]},
            },
            HTTP_AUTHORIZATION='Token ' + self.admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {"success": True})
        self.assertFalse(Company.objects.filter(pk=self.merger_company.id).exists())
        self.merger_company_farm.refresh_from_db()
        self.assertEqual(self.merger_company_farm.company_id, self.merge_to_company.id)
        self.assertEqual(
            MergedEntity.get_to_id_by_from_id(self.merger_company.id, self.merger_company.__class__.__name__),
            self.merge_to_company.id
        )


@tag('view')
class CompanySiteLoadsSearchViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.farm = FarmFactory(name='foo-bar', company=self.company)
        self.storage = StorageFactory(name='foo', type='silo', size=100, farm=self.farm)
        self.trader_company = CompanyFactory(business_name='Trader Company', type_id=5)
        self.grower_company = CompanyFactory(business_name='Grower Company', type_id=1)
        self.trader_ngr = NgrFactory(ngr_number='NGR', company=self.trader_company, ngr_type='single')
        self.grower_ngr = NgrFactory(ngr_number='GROWER_NGR', company=self.grower_company, ngr_type='single')
        self.seller = PartyFactory(role='Seller', company=self.grower_company)
        self.buyer = PartyFactory(role='Buyer', company=self.trader_company)
        self.title_transfer = TitleTransfer(id=1, buyer=self.buyer,
                                            seller=self.seller, process_on=timezone.now(),
                                            grade_id=1, identifier='T220405CFFB197')
        self.title_transfer.save()
        self.title_transfer_outload = LoadFactory(
            storage=self.storage, commodity_id=2,
            grade_id=1, variety_id=1, ngr_id=self.trader_ngr.id, type=Load.OUTLOAD,
            estimated_net_weight=50, gross_weight=100, tare_weight=50, season='23/24',
            title_transfer_id=self.title_transfer.id
        )
        self.title_transfer_inload = LoadFactory(
            storage=self.storage, commodity_id=2,
            grade_id=1, variety_id=1, ngr_id=self.grower_ngr.id, type=Load.INLOAD,
            estimated_net_weight=50, gross_weight=100, tare_weight=50, season='23/24',
            title_transfer_id=self.title_transfer.id
        )

    def test_get(self):
        response = self.client.get(
            '/companies/{}/company_sites/{}/loads/search/?search=M22'.format(self.company.id, self.farm.id),
            HTTP_AUTHORIZATION='Token ' + self.employee.token_key,
            format='json',
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'], [])
        self.assertEqual(response.data['count'], 0)
        self.assertEqual(response.data['pageSize'], 10)
        self.assertEqual(response.data['page'], 0)

        response = self.client.get(
            '/companies/{}/company_sites/{}/loads/search/?search=T22'.format(self.company.id, self.farm.id),
            HTTP_AUTHORIZATION='Token ' + self.employee.token_key,
            format='json',
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual([item['titleTransferNumber'] for item in response.data['results']],\
            ['T220405CFFB197', 'T220405CFFB197'])
        self.assertEqual(response.data['count'], 2)
        self.assertEqual(response.data['pageSize'], 10)
        self.assertEqual(response.data['page'], 0)

        response = self.client.get(
            '/companies/{}/company_sites/{}/loads/search/?search=NGR'.format(self.company.id, self.farm.id),
            HTTP_AUTHORIZATION='Token ' + self.employee.token_key,
            format='json',
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(sorted([item['ngr'] for item in response.data['results']]), sorted(['GROWER_NGR', 'NGR']))
        self.assertEqual(response.data['count'], 2)
        self.assertEqual(response.data['pageSize'], 10)
        self.assertEqual(response.data['page'], 0)


@tag('view')
class CompanyNGRCredentialsViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.ngr = NgrFactory(company=self.company, ngr_type='single')

    def test_get_404(self):
        response = self.client.get(
            f'/companies/{self.company.id}/ngrs/credentials/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)

    def test_get_200(self):
        NgrPortalCredential.create_credentials(
            self.company.id, {"username": "testUsername", "password": "testPassword"})

        response = self.client.get(
            f'/companies/{self.company.id}/ngrs/credentials/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['username'], 'testUsername')
        self.assertEqual(response.data['password'], 'testPassword')

    def test_put_200(self):
        # creates if doesn't exists
        response = self.client.put(
            f'/companies/{self.company.id}/ngrs/credentials/',
            {"username": "TestUsername1", "password": "Test Password 1"},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['username'], "TestUsername1")
        self.assertEqual(response.data['password'], "Test Password 1")

        response = self.client.put(
            f'/companies/{self.company.id}/ngrs/credentials/',
            {"username": "TestUsername2", "password": "TestPassword2"},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['username'], "TestUsername2")
        self.assertEqual(response.data['password'], "TestPassword2")

    def test_delete_204(self):
        NgrPortalCredential.create_credentials(
            self.company.id, {"username": "testUsername", "password": "testPassword"}
        )
        response = self.client.delete(
            f'/companies/{self.company.id}/ngrs/credentials/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 204)
        self.assertFalse(NgrPortalCredential.objects.filter(company_id=self.company.id).exists())

    def test_ngr_create_bank_payload(self):
        payees = [
            {
                "payee_id": ********,
                "payee_split": "100.00",
                "bsb_id": "012003",
                "bank_account_number": "12222",
                "bank_account_name": "search",
                "bsb_bank": "ANZ",
                "abn": "***********",
            }
        ]
        payee = payees[0]
        bank_code = pydash.get(payee, 'bsb_bank')

        bank = Bank.find_branch(bank_code, pydash.get(payee, 'bsb_id'))
        payload = self.ngr.create_bank_payload(self.company, payee, bank, payees, self.company)
        bank = Bank.find_by_bsb_number('012003').first()

        expected_payload = {
            'account_name': 'search',
            'account_number': '12222',
            'bank_id': bank.id,
            'bsb_number': '012003',
            'company_id': self.company.id,
            'is_primary': True,
            'shareholder_percent': '100.00'
        }
        self.assertEqual(payload, expected_payload)

        bank = Bank.find_by_bsb_number('012004').first()
        bank_account_params = {
            "entity_for_id": self.ngr.id,
            "entity_for_type": 'ngr',
            "company_id": self.company.id,
            "bank_id": bank.id,
            'bsb_number': '012004',
            'account_name': 'search2',
            'account_number': '121212',
            'is_primary': True,
            'shareholder_percent': '100.00'
        }
        _params = Ngr._set_content_type_params(bank_account_params)
        bank_account = BankAccount(**_params)
        bank_account.save_only()

        payload = self.ngr.create_bank_payload(self.company, payee, None, payees, self.company)

        expected_payload = {
            'shareholder_percent': '100.00',
            'bsb_number': '012003',
            'account_number': '12222',
            'account_name': 'search',
            'company_id': self.company.id,
            'bank_id': bank.id,
            'is_primary': True
        }

        self.assertEqual(payload, expected_payload)

    def test_is_current_company_abn_exists(self):
        payees = [
            {
                "payee_id": ********,
                "payee_split": "100.00",
                "bsb_id": "012003",
                "bank_account_number": "*********",
                "bank_account_name": "search",
                "bsb_bank": "ANZ",
                "abn": "***********",
            }
        ]

        is_abn_exists = Ngr.is_current_company_abn_exists(self.company, payees)

        self.assertFalse(is_abn_exists)

        payees[0]['abn'] = self.company.abn
        is_abn_exists = Ngr.is_current_company_abn_exists(self.company, payees)

        self.assertTrue(is_abn_exists)

    def test_get_unmatched_abns(self):
        payees = [
            {
                "payee_id": ********,
                "payee_split": "100.00",
                "bsb_id": "012003",
                "bank_account_number": "*********",
                "bank_account_name": "search",
                "bsb_bank": "ANZ",
                "abn": "***********",
            }
        ]
        result = Ngr.get_unmatched_abns(self.company, payees)

        self.assertEqual(len(result), 1)

        payees[0]['abn'] = self.company.abn
        result = Ngr.get_unmatched_abns(self.company, payees)

        self.assertEqual(len(result), 0)


@tag('model')
class XeroMappingTest(ACTestCase):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory()

    def test_uniq(self):
        mapping = XeroMapping(company=self.company, commodity_id=1, xero_account='200', transaction_type='deductions')
        mapping.full_clean()
        mapping.save()

        self.assertTrue(mapping.persisted)

        with self.assertRaises(ValidationError) as ex:
            mapping = XeroMapping(
                company=self.company, commodity_id=1, xero_account='200', transaction_type='deductions')
            mapping.full_clean()
        self.assertEqual(ex.exception.message_dict, {'__all__': ['This combination already exists']})

        mapping2 = XeroMapping(company=self.company, commodity_id=2, xero_account='200', transaction_type='deductions')
        mapping2.full_clean()
        mapping2.save()

        self.assertTrue(mapping2.persisted)

        with self.assertRaises(ValidationError) as ex:
            mapping2.commodity_id = 1
            mapping2.full_clean()
        self.assertEqual(ex.exception.message_dict, {'__all__': ['This combination already exists']})

    def test_mandatory(self):
        mapping = XeroMapping(company=self.company, commodity_id=1, xero_account='200', transaction_type='deductions')
        mapping.full_clean()
        mapping.save()

        self.assertTrue(mapping.persisted)
        with self.assertRaises(ValidationError) as ex:
            mapping = XeroMapping(
                company=self.company, xero_account='200', transaction_type='deductions')
            mapping.full_clean()
        self.assertEqual(
            ex.exception.message_dict,
            {'__all__': ['One of the commodity, grade, season and item_type needs to be present']}
        )

    def test_best_match(self):
        mapping1 = XeroMapping(
            company=self.company, commodity_id=1, xero_account='200', transaction_type='sale_contract')
        mapping1.save()
        mapping2 = XeroMapping(
            company=self.company, commodity_id=1, xero_account='200', item_type='movements',
            transaction_type='sale_contract')
        mapping2.save()
        mapping3 = XeroMapping(
            company=self.company, commodity_id=1, xero_account='200', season='19/20',
            item_type='movements', transaction_type='sale_contract')
        mapping3.save()
        mapping4 = XeroMapping(
            company=self.company, commodity_id=1, grade_id=1, xero_account='200', season='19/20',
            item_type='movements', transaction_type='sale_contract')
        mapping4.save()
        mapping5 = XeroMapping(
            company=self.company, commodity_id=1, grade_id=1, xero_account='200',
            season='19/20', transaction_type='sale_contract')
        mapping5.save()
        mapping6 = XeroMapping(
            company=self.company, item_type='movements', xero_account='200', transaction_type='sale_contract')
        mapping6.save()

        self.assertEqual(self.company.xero_mappings.count(), 6)

        best_match = XeroMapping.get_best_match(
            mappings=self.company.xero_mappings, commodity_id=2, grade_id=1, season='19/20', site_id=None,
            item_type='movements', ngr_id=None
        )
        self.assertEqual(best_match.id, mapping6.id)

        best_match = XeroMapping.get_best_match(
            mappings=self.company.xero_mappings, commodity_id=2, grade_id=1, season='22/23', site_id=None,
            item_type='movements', ngr_id=None
        )
        self.assertEqual(best_match.id, mapping6.id)

        best_match = XeroMapping.get_best_match(
            mappings=self.company.xero_mappings, commodity_id=2, grade_id=1, season='19/20', site_id=None,
            item_type='title_transfers', ngr_id=None
        )
        self.assertEqual(best_match, None)

        best_match = XeroMapping.get_best_match(
            mappings=self.company.xero_mappings, commodity_id=1, grade_id=1, season='19/20', site_id=None,
            item_type='movements', ngr_id=None
        )
        self.assertEqual(best_match.id, mapping4.id)

        best_match = XeroMapping.get_best_match(
            mappings=self.company.xero_mappings, commodity_id=1, grade_id=2, season="19/20", site_id=None,
            item_type='movements', ngr_id=None
        )
        self.assertEqual(best_match.id, mapping3.id)

        best_match = XeroMapping.get_best_match(
            mappings=self.company.xero_mappings, commodity_id=1, grade_id=1, season="21/22", site_id=None,
            item_type='movements', ngr_id=None
        )
        self.assertEqual(best_match.id, mapping2.id)


@tag('view')
class CompanyExternalPortalsViewTest(AuthSetup):
    def test_get_200(self):
        response = self.client.get(
            f'/companies/{self.company.id}/external/portals/',
            HTTP_AUTHORIZATION=f'Token {self.token}'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, [])

        portal = ExternalPortalFactory(company=self.company)

        response = self.client.get(
            f'/companies/{self.company.id}/external/portals/',
            HTTP_AUTHORIZATION=f'Token {self.token}'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], portal.id)

    def test_post_201(self):
        payload = factory.build(dict, FACTORY_CLASS=ExternalPortalFactory)
        payload.pop('company', None)
        payload.pop('company_id', None)

        response = self.client.post(
            f'/companies/{self.company.id}/external/portals/',
            payload,
            HTTP_AUTHORIZATION=f'Token {self.token}',
            format='json'
        )

        self.assertEqual(response.status_code, 201)
        self.assertIsNotNone(response.data['id'])
        self.assertEqual(self.company.external_portals.count(), 1)


@tag('view')
class CompanyExternalPortalViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.portal = ExternalPortalFactory(company=self.company)

    def test_get(self):
        response = self.client.get(
            f'/companies/{self.portal.company_id}/external/portals/{self.portal.id}/',
            HTTP_AUTHORIZATION=f'Token {self.token}'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['id'], self.portal.id)

@tag('view')
class AddCompaniesToDirectoryViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company_1 = CompanyFactory()
        self.company_2 = CompanyFactory()

    def test_post_200(self):
        response = self.client.post(
            f'/companies/{self.company.id}/add-to-directory/',
            data={'company_ids': [self.company_1.id, self.company_2.id]},
            HTTP_AUTHORIZATION=f'Token {self.token}',
            format='json',
        )

        self.assertEqual(response.status_code, 200)
        directory_companies = Company.directory_companies_only(self.company.id)
        self.assertTrue(self.company_1 in directory_companies)
        self.assertTrue(self.company_2 in directory_companies)

        company_3 = CompanyFactory()
        company_4 = CompanyFactory()
        self.assertFalse(Ngr.objects.filter(ngr_number='********').exists())
        response_1 = self.client.post(
            f'/companies/{self.company.id}/add-to-directory/',
            data={'abns': [company_3.abn, company_4.abn],
                  'ngr_number': "********",
                  'ngr_details': [
                        {
                            'bsb_number': '',
                            'account_name': '',
                            'account_number': '',
                            'payee_split': 60.00,
                            'is_primary': True,
                            'abn': company_3.abn
                        },
                        {
                            'bsb_number': '',
                            'account_name': '',
                            'account_number': '',
                            'payee_split': 40.00,
                            'is_primary': False,
                            'abn': company_4.abn
                        }
                    ]
                  },
            HTTP_AUTHORIZATION=f'Token {self.token}',
            format='json',
        )
        directory_companies = Company.directory_companies_only(self.company.id)
        created_ngr = Ngr.objects.filter(ngr_number='********').first()
        self.assertEqual(response_1.status_code, 200)

        self.assertTrue(company_3 in directory_companies)
        self.assertTrue(company_4 in directory_companies)
        self.assertTrue(created_ngr.ngr_type, 'shared')

    def test_post_404(self):
        response = self.client.post(
            '/companies/9999/add_to_directory/',
            data={'company_ids': [self.company_1.id, self.company_2.id]},
            HTTP_AUTHORIZATION=f'Token {self.token}',
            format='json'
        )

        self.assertEqual(response.status_code, 404)


@tag('view')
class CompanyApplicationRatesViewTest(AuthSetup):
    def setUp(self):
        self.application_company = CompanyFactory()
        self.employee1 = EmployeeFactory(company=self.application_company)

    def test_post_201(self):
        response = self.client.post(
            f'/companies/{self.application_company.id}/application-rates/',
            data=[{'commodity_id': 237, 'rate': '1.2'}],
            HTTP_AUTHORIZATION=f'Token {self.employee1.refresh_token()}',
            format='json'
        )

        self.assertEqual(response.status_code, 201)

    def test_post_400(self):
        application_rate = ApplicationRate(company_id=self.application_company.id, commodity_id=237, rate=1.5)
        application_rate.save()
        response = self.client.post(
            f'/companies/{self.application_company.id}/application-rates/',
            data=[{'commodity_id': 237, 'rate': '1.5'}],
            HTTP_AUTHORIZATION=f'Token {self.employee1.refresh_token()}',
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'errors': 'Application Rate already exists'})

    def test_get_200(self):
        application_rate = ApplicationRate(company_id=self.application_company.id, commodity_id=237, rate=1.5)
        application_rate.save()
        response = self.client.get(
            f'/companies/{self.application_company.id}/application-rates/',
            HTTP_AUTHORIZATION=f'Token {self.employee1.refresh_token()}',
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], application_rate.id)

    def test_get_404(self):
        response = self.client.get(
            '/companies/9999/application-rates/',
            HTTP_AUTHORIZATION=f'Token {self.employee1.refresh_token()}',
            format='json'
        )
        self.assertEqual(response.status_code, 404)


@tag('view')
class CompanyApplicationRateViewTest(AuthSetup):
    def test_delete_204(self):
        application_rate = ApplicationRate(company_id=self.company.id, commodity_id=237, rate=1.5)
        application_rate.save()
        response = self.client.delete(
            f'/companies/application-rates/{application_rate.id}/',
            HTTP_AUTHORIZATION=f'Token {self.token}',
            format='json'
        )
        self.assertEqual(response.status_code, 204)
        application_rate.refresh_from_db()
        self.assertEqual(application_rate.is_active, False)

    def test_delete_404(self):
        response = self.client.delete(
            '/companies/application-rates/9999/',
            HTTP_AUTHORIZATION=f'Token {self.token}',
            format='json'
        )
        self.assertEqual(response.status_code, 404)

@tag('view')
class CompanyEmployeeNamesViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory()
        self.employee = EmployeeFactory(
            company=self.company,
            email='<EMAIL>',
            first_name='John',
            last_name='Doe'
        )
        self.employee2 = EmployeeFactory(
            company=self.company,
            email='<EMAIL>',
            first_name='Jane',
            last_name='Smith'
        )
        self.observer_employee = EmployeeFactory(
            company=self.company,
            email='<EMAIL>',
            first_name='Jane',
            last_name='Observer',
            type_id=OBSERVER_TYPE_ID
        )
        self.url = f'/companies/{self.company.id}/employees/names/'

    def test_post_without_email(self):
        response = self.client.post(
            self.url,
            data={},
            format='json'
        )
        self.assertEqual(response.status_code, 400)

        response = self.client.post(
            self.url,
            data={"email": ""},
            format='json'
        )
        self.assertEqual(response.status_code, 400)

    def test_post_with_invalid_company_id(self):
        response = self.client.post(
            '/companies/9999/employees/names/',
            data={'email': '<EMAIL>'},
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, [])

    def test_post_success_single_employee(self):
        response = self.client.post(
            self.url,
            data={'email': '<EMAIL>'},
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(response.data[0]['firstName'], 'John')
        self.assertEqual(response.data[0]['lastName'], 'Doe')

    def test_post_success_multiple_employees(self):
        response = self.client.post(
            self.url,
            data={'email': '<EMAIL>'},
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)  # excluding observer
        self.assertEqual(response.data[0]['firstName'], 'John')
        self.assertEqual(response.data[0]['lastName'], 'Doe')
        self.assertEqual(response.data[1]['firstName'], 'Jane')
        self.assertEqual(response.data[1]['lastName'], 'Smith')

    def test_post_no_matching_employees(self):
        response = self.client.post(
            self.url,
            data={'email': '<EMAIL>'},
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, [])

@tag('view')
class CompanyLookupByABNOrNGRViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.test_abn = '***********'
        self.test_abn2 = '***********'
        self.test_abn3 = '***********'
        self.test_abn4 = '***********'
        self.test_ngr = '1234567'
        self.test_ngr2 = '9002333'
        self.invalid_identifier = '999999'

        self.company = CompanyFactory(
            abn=self.test_abn,
            business_name='Test Company'
        )
        self.company2 = CompanyFactory(
            abn=self.test_abn2,
            business_name='Another Company'
        )
        self.company3 = CompanyFactory(
            abn=self.test_abn3,
            business_name='NGR Company'
        )
        self.company4 = CompanyFactory(
            abn='***********',
            business_name='Extra NGR Company'
        )

        self.ngr = NgrFactory(company=self.company3, ngr_number=self.test_ngr)
        self.ngr2 = NgrFactory(company=self.company4, ngr_number=self.test_ngr2)

    def test_get_without_identifier(self):
        response = self.client.get('/companies/lookup/')
        self.assertEqual(response.status_code, 404)

    def test_get_with_invalid_identifier(self):
        response = self.client.get(f'/companies/lookup/{self.invalid_identifier}/')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 0)

    def test_get_by_abn_success(self):
        response = self.client.get(f'/companies/lookup/{self.test_abn}/')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['businessName'], 'Test Company')

    def test_get_by_ngr_success(self):
        response = self.client.get(f'/companies/lookup/{self.test_ngr}/')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['businessName'], 'NGR Company')

    @patch('core.ngrs.models.Ngr.get_owner_company_ids')
    def test_get_by_ngr_with_multiple_companies(self, mock_get_owner_company_ids):

        mock_get_owner_company_ids.return_value = [self.company3.id, self.company4.id]

        response = self.client.get(f'/companies/lookup/{self.test_ngr2}/')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(response.data[0]['businessName'], 'Extra NGR Company')
        self.assertEqual(response.data[1]['businessName'], 'NGR Company')
        self.assertTrue(mock_get_owner_company_ids.called)

@tag('view')
class CompanyHistoryViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.system_company_id = SYSTEM_COMPANY_IDS[0]
        self.system_company = Company.moderators.filter(id=self.system_company_id).first()
        if not self.system_company:
            self.system_company = CompanyFactory(
                id=self.system_company_id
            )
        self.regular_company = CompanyFactory()

        self.admin_user = EmployeeFactory(
            company=self.company,
            is_staff=True
        )
        self.regular_user = EmployeeFactory(
            company=self.company,
            is_staff=False
        )

        self.client.force_authenticate(user=self.admin_user)

        self.system_company.business_name = "System Company"
        self.system_company.save()

        self.regular_company.business_name = "Regular Company"
        self.regular_company.save()

    def test_get_system_company_history(self):
        response = self.client.get(f'/companies/{self.system_company.id}/history/')

        self.assertEqual(response.status_code, 200)
        self.assertTrue(self.system_company.id in SYSTEM_COMPANY_IDS)
        response_data = response.json()
        self.assertEqual(response_data[0]['snapshot']['id'], self.system_company.id)

    def test_get_regular_company_history(self):
        response = self.client.get(f'/companies/{self.regular_company.id}/history/')

        self.assertEqual(response.status_code, 200)
        self.assertFalse(self.regular_company.id in SYSTEM_COMPANY_IDS)
        response_data = response.json()
        self.assertEqual(response_data[0]['snapshot']['id'], self.regular_company.id)

    def test_get_non_existent_company_history(self):
        response = self.client.get('/companies/99999/history/')
        self.assertEqual(response.status_code, 404)

    def test_get_history_without_admin_permission(self):
        self.client.force_authenticate(user=self.regular_user)
        response = self.client.get(f'/companies/{self.regular_company.id}/history/')
        self.assertEqual(response.status_code, 403)

@tag('view')
class CompanySSOSettingViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory(
            business_name='Test Company'
        )
        self.sso_setting = SSOSetting.objects.create(
            company=self.company,
            is_active=True,
            agrichain_auth_enabled=True,
            extras={
                'web': {'key': 'web_value'},
                'mobile': {'key': 'mobile_value'}
            }
        )

        self.inactive_company = CompanyFactory(
            business_name='Inactive Company'
        )
        self.inactive_sso = SSOSetting.objects.create(
            company=self.inactive_company,
            is_active=False,
            agrichain_auth_enabled=True,
            extras={}
        )

        self.company_without_sso = CompanyFactory(
            business_name='No SSO Company'
        )

    def test_get_active_sso_setting(self):
        response = self.client.get(f'/companies/{self.company.id}/sso-setting/web/')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['companyName'], 'Test Company')
        self.assertEqual(response.data['companyId'], self.company.id)
        self.assertEqual(response.data['agrichainAuthEnabled'], True)
        self.assertEqual(response.data['loginUri'], f'/companies/{self.company.id}/sso/azure/login')
        self.assertEqual(response.data['extras'], {'key': 'web_value'})

    def test_get_sso_setting_with_platform(self):
        response = self.client.get(f'/companies/{self.company.id}/sso-setting/mobile/')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['extras'], {'key': 'mobile_value'})

    def test_get_inactive_sso_setting(self):
        response = self.client.get(f'/companies/{self.inactive_company.id}/sso-setting/web/')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['companyName'], '')
        self.assertEqual(response.data['agrichainAuthEnabled'], False)


    def test_get_non_existent_sso_setting(self):
        response = self.client.get(f'/companies/{self.company_without_sso.id}/sso-setting/web/')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['companyName'], '')
        self.assertEqual(response.data['agrichainAuthEnabled'], False)


    def test_get_non_existent_company(self):
        response = self.client.get('/companies/99999000/sso-setting/web/')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['companyName'], '')
        self.assertEqual(response.data['agrichainAuthEnabled'], False)

    def test_get_sso_setting_with_invalid_platform(self):
        response = self.client.get(f'/companies/{self.company.id}/sso-setting/invalid/')

        self.assertEqual(response.status_code, 200)
        self.assertIsNone(response.data['extras'])

@tag('view')
class CompanyCompaniesWebViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory(
            business_name='Company'
        )

        self.staff_user = EmployeeFactory(
            company=self.company,
            is_staff=True
        )
        self.regular_user = EmployeeFactory(
            company=self.company,
            is_staff=False
        )

        self.system_company_id = SYSTEM_COMPANY_IDS[0]
        self.system_company = Company.moderators.filter(id=self.system_company_id).first()
        if not self.system_company:
            self.system_company = CompanyFactory(
                id=self.system_company_id
            )

        self.company1 = CompanyFactory(
            business_name='Test Company 1',
            country_id=self.staff_user.company.country_id
        )
        self.company2 = CompanyFactory(
            business_name='Test Company 2',
            country_id=self.staff_user.company.country_id
        )
        self.company3 = CompanyFactory(
            business_name='Test Company 3',
            country_id=self.staff_user.company.country_id
        )

        self.company.add_company_to_directory(self.company1.id)
        self.company.add_company_to_directory(self.company2.id)
        self.company.add_company_to_directory(self.company3.id)

        self.group = CompanyGroup.objects.create(
            owner_company_id=self.company.id,
            name='Test Group'
        )
        self.group.companies.add(self.company1)

        self.employee_filters = EmployeeViewFilters.objects.create(
            employee=self.regular_user, filters={
                "company_filters": {
                    'group__in': self.group.id
                }
            }
        )
        self.url = f'/companies/{self.company.id}/companies/web/'

    def test_get_companies_as_staff(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, 200)
        records = response.data['results']
        self.assertEqual(len(records), 5)

    def test_get_companies_as_regular_user(self):
        self.client.force_authenticate(user=self.regular_user)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, 200)
        records = response.data['results']
        self.assertEqual(len(records), 3)
        self.assertTrue(all(
            record['id'] in [self.company1.id, self.company2.id, self.company3.id] for record in records)
        )

    def test_get_companies_with_groups_filter(self):
        self.client.force_authenticate(user=self.regular_user)
        response = self.client.get(f'{self.url}?is_groups=true')

        self.assertEqual(response.status_code, 200)
        records = response.data['results']
        self.assertEqual(len(records), 1)
        self.assertEqual(records[0]['name'], 'Test Company 1')

    def test_get_ungrouped_companies(self):
        self.client.force_authenticate(user=self.regular_user)
        self.employee_filters.filters = {}
        self.employee_filters.save()
        response = self.client.get(f'{self.url}?is_groups=true&ungrouped=true')

        self.assertEqual(response.status_code, 200)
        records = response.data['results']
        self.assertEqual(len(records), 2)
        self.assertEqual(records[0]['name'], 'Test Company 2')
        self.assertEqual(records[1]['name'], 'Test Company 3')

    def test_system_companies_excluded(self):
        self.client.force_authenticate(user=self.staff_user)
        self.company.add_company_to_directory(self.system_company.id)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, 200)
        records = response.data['results']
        company_ids = [record['id'] for record in records]
        self.assertNotIn(self.system_company.id, company_ids)

@tag('view')
class CompanyCompaniesSearchViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory(
            business_name='Company'
        )
        self.staff_user = EmployeeFactory(
            company=self.company,
            is_staff=True
        )
        self.regular_user = EmployeeFactory(
            company=self.company,
            is_staff=False
        )

        self.company1 = CompanyFactory(
            business_name='Alpha Company',
            entity_name='Alpha Entity',
            abn='***********',
            mobile='0411111111',
            website='www.alpha.com',
            country_id=self.staff_user.company.country_id
        )
        self.company2 = CompanyFactory(
            business_name='Beta Company',
            entity_name='Beta Entity',
            abn='***********',
            mobile='0422222222',
            website='www.beta.com',
            country_id=self.staff_user.company.country_id
        )

        self.company3 = CompanyFactory(
            business_name='Test Company 3',
            country_id=self.staff_user.company.country_id
        )

        self.company.add_company_to_directory(self.company1.id)
        self.company.add_company_to_directory(self.company2.id)
        self.company.add_company_to_directory(self.company3.id)

        self.group = CompanyGroup.objects.create(
            owner_company_id=self.company.id,
            name='Test Group'
        )
        self.group.companies.add(self.company1)

        self.employee_filters = EmployeeViewFilters.objects.create(
            employee=self.regular_user, filters={
                "company_filters": {
                    'group__in': self.group.id
                }
            }
        )

        self.url = f'/companies/{self.company.id}/companies/web/search/'

    def test_search_by_business_name(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.url}?search=Alpha')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Company')

    def test_search_by_entity_name(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.url}?search=Beta Entity')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Beta Company')

    def test_search_by_abn(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.url}?search=***********')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Company')

    def test_search_by_mobile(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.url}?search=0422222222')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Beta Company')

    def test_search_by_website(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.url}?search=beta.com')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Beta Company')

    def test_order_by_name_asc(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.url}?order_by=name&order=asc')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Company')
        self.assertEqual(response.data['results'][1]['name'], 'Beta Company')
        self.assertEqual(response.data['results'][2]['name'], 'Company')
        self.assertEqual(response.data['results'][3]['name'], 'Foo Corporation')
        self.assertEqual(response.data['results'][4]['name'], 'Test Company 3')

    def test_order_by_name_desc(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.url}?order_by=name&order=desc')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Test Company 3')
        self.assertEqual(response.data['results'][1]['name'], 'Foo Corporation')
        self.assertEqual(response.data['results'][2]['name'], 'Company')
        self.assertEqual(response.data['results'][3]['name'], 'Beta Company')
        self.assertEqual(response.data['results'][4]['name'], 'Alpha Company')

    def test_search_with_groups_filter(self):
        self.client.force_authenticate(user=self.regular_user)
        response = self.client.get(f'{self.url}?search=Alpha&is_groups=True')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Company')

    def test_search_ungrouped_companies(self):
        self.client.force_authenticate(user=self.regular_user)
        response = self.client.get(f'{self.url}?ungrouped=true&is_groups=True&search=Beta')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Beta Company')

    def test_search_as_regular_user(self):
        self.client.force_authenticate(user=self.regular_user)
        response = self.client.get(f'{self.url}?search=Alpha')

        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data['results']) >= 0)

@tag('view')
class CompanyFarmsViewsTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory()
        self.staff_user = EmployeeFactory(
            company=self.company,
            is_staff=True
        )
        self.regular_user = EmployeeFactory(
            company=self.company,
            is_staff=False
        )

        self.system_company = CompanyFactory(
            business_name='System Company',
            type_id=SYSTEM_COMPANY_IDS[0]
        )

        PlatformFeatures.persist(company_id=self.company.id, company=self.company)
        PlatformFeatures.persist(company_id=self.system_company.id, company=self.system_company)

        self.regular_company = CompanyFactory(
            business_name='Regular Company'
        )
        self.regular_company2 = CompanyFactory(
            business_name="Regular Company 2"
        )

        self.farm1 = FarmFactory(
            name='Alpha Farm',
            company=self.regular_company,
            is_active=True,
            mobile='0411111111'
        )
        self.farm2 = FarmFactory(
            name='Beta Farm',
            company=self.regular_company,
            is_active=False,
            mobile='0422222222'
        )
        self.system_farm = FarmFactory(
            name='System Farm',
            company=self.system_company,
            is_active=True
        )

        self.web_url = f'/companies/{self.regular_company.id}/farms/web/'
        self.search_url = f'/companies/{self.regular_company.id}/farms/web/search/'

@tag('view')
class CompanyFarmsWebViewTest(CompanyFarmsViewsTest):
    def test_get_farms_as_staff(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(self.web_url)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['count'], 2)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Farm')
        self.assertEqual(response.data['results'][1]['name'], 'Beta Farm')

    def test_get_system_company_farms_as_staff(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'/companies/{self.system_company.id}/farms/')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'System Farm')

    def test_get_farms_as_regular_user(self):
        self.client.force_authenticate(user=self.regular_user)
        response = self.client.get(self.web_url)

        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data['results']), 2)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Farm')
        self.assertEqual(response.data['results'][1]['name'], 'Beta Farm')

    def test_get_farms_with_unaccepted(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.web_url}?include_unaccepted=true')

        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data['results']) > 0)

    def test_get_active_farms_only(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.web_url}?is_active=true')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Farm')

    def test_get_non_existent_company(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get('/companies/99999/farms/web/')

        self.assertEqual(response.status_code, 404)

    def test_global_farms_enabled(self):
        self.client.force_authenticate(user=self.staff_user)

        self.company.platformfeatures.farms = True
        self.company.platformfeatures.save()

        response = self.client.get(f'/companies/{self.company.id}/farms/web/?is_global=true')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 5)

    def test_global_farms_disabled(self):
        self.client.force_authenticate(user=self.staff_user)

        self.system_company.platformfeatures.farms = False
        self.system_company.platformfeatures.save()

        response = self.client.get(f'/companies/{self.system_company.id}/farms/web/?is_global=true')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)

@tag('view')
class CompanyFarmsSearchViewTest(CompanyFarmsViewsTest):
    def test_search_by_name(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.search_url}?search=Alpha')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Farm')

    def test_search_by_mobile(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.search_url}?search=0411111111')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Farm')

    def test_search_with_order_by_name_asc(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.search_url}?order_by=name&order=asc')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Farm')
        self.assertEqual(response.data['results'][1]['name'], 'Beta Farm')

    def test_search_with_order_by_name_desc(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.search_url}?order_by=name&order=desc')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Beta Farm')
        self.assertEqual(response.data['results'][1]['name'], 'Alpha Farm')

    def test_search_with_is_active(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.search_url}?is_active=true')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Farm')

    def test_search_by_company_name(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.search_url}?search=Regular Company')

        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data['results']) > 0)

    def test_search_with_include_unaccepted(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get(f'{self.search_url}?include_unaccepted=true')

        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data['results']) > 0)

    def test_search_non_existent_company(self):
        self.client.force_authenticate(user=self.staff_user)
        response = self.client.get('/companies/99999/farms/search/')

        self.assertEqual(response.status_code, 404)

    def test_search_global_farms_enabled(self):
        self.client.force_authenticate(user=self.staff_user)

        self.company.platformfeatures.farms = True
        self.company.platformfeatures.save()

        response = self.client.get(f'/companies/{self.company.id}/farms/web/search/?is_global=true')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 5)

    def test_search_global_farms_disabled(self):
        self.client.force_authenticate(user=self.staff_user)

        self.system_company.platformfeatures.farms = False
        self.system_company.platformfeatures.save()

        response = self.client.get(f'/companies/{self.system_company.id}/farms/web/search/?is_global=true')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)

@tag('view')
class CompanySiteManagementSettingsViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        self.client.force_authenticate(user=self.employee)

        self.company = CompanyFactory()
        self.farm1 = FarmFactory(
            company=self.company,
            allow_inload_slot_order_booking=False,
            allow_outload_slot_order_booking=False
        )
        self.farm2 = FarmFactory(
            company=self.company,
            allow_inload_slot_order_booking=False,
            allow_outload_slot_order_booking=False
        )

        self.settings = SiteManagementSettings(
            company=self.company,
            minimum_tonnage=0.1
        )
        self.settings.save()

    def test_get_existing_settings(self):
        self.settings.minimum_tonnage = 0.5
        self.settings.order_booking = True
        self.settings.save()

        response = self.client.get(f'/companies/{self.company.id}/site-management/settings/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['minimumTonnage'], 0.5)
        self.assertEqual(response.data['orderBooking'], True)

    def test_get_non_existing_company(self):
        response = self.client.get('/companies/99999/site-management/settings/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_update_settings(self):
        update_data = {
            'minimum_tonnage': 0.5,
            'order_booking': True,
            'left_border_color_by_pits': True,
            'trailer_booking_quantity': 5,
            'pickup_order_number_required': True,
            'activity_log': True,
            'inload_sms': True,
            'load_by_load_transfer': True,
            'customer_optional': True,
            'delivery_order_number_required': True,
            'statuses': ['status1', 'status2'],
            'fields': ['field1', 'field2'],
            'title_order': ['title1', 'title2'],
            'tooltip_order': ['tooltip1', 'tooltip2'],
            'pits': ['pit1', 'pit2']
        }

        response = self.client.put(
            f'/companies/{self.company.id}/site-management/settings/',
            update_data,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.settings.refresh_from_db()

        def snake_to_camel(snake_str):
            components = snake_str.split('_')
            return components[0] + ''.join(x.title() for x in components[1:])

        for key, value in update_data.items():
            self.assertEqual(response.data[snake_to_camel(key)], value)

        self.farm1.refresh_from_db()
        self.farm2.refresh_from_db()
        self.assertTrue(self.farm1.allow_inload_slot_order_booking)
        self.assertTrue(self.farm1.allow_outload_slot_order_booking)
        self.assertTrue(self.farm2.allow_inload_slot_order_booking)
        self.assertTrue(self.farm2.allow_outload_slot_order_booking)

    def test_partial_update(self):
        update_data = {
            'minimum_tonnage': 0.5
        }

        response = self.client.put(
            f'/companies/{self.company.id}/site-management/settings/',
            update_data,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.settings.refresh_from_db()
        self.assertEqual(self.settings.minimum_tonnage, 0.5)
        self.assertEqual(self.settings.order_booking, False)

    def test_update_with_empty_values(self):
        self.settings.minimum_tonnage = 0.5
        self.settings.trailer_booking_quantity = 5
        self.settings.save()

        update_data = {
            'minimum_tonnage': None,
            'trailer_booking_quantity': None
        }

        response = self.client.put(
            f'/companies/{self.company.id}/site-management/settings/',
            update_data,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.settings.refresh_from_db()
        self.assertEqual(self.settings.minimum_tonnage, 0.1)
        self.assertIsNone(self.settings.trailer_booking_quantity)


@tag('view')
class CompanyNGRNumberValidateViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.company = CompanyFactory()
        self.ngr_number = "1234567"
        self.url = f'/companies/{self.company.id}/ngrs/{self.ngr_number}/validate/'
        self.client.force_authenticate(self.employee)

    @patch('core.services.external.ngr.NGRLookUp.fetch')
    def test_successful_ngr_lookup(self, mock_fetch):
        mock_response = {
            'gps_latitude': 12,
            'gps_longitude': 24,
            'payees': [{
                'abn': '***********',
                'ngr_number': self.ngr_number,
                'business_name': 'Test Farm',
                'bank_account_name': 'Test',
                'status': 'ACTIVE',
                'bank_id': 2,
                'entity_name': 'Test Trading LTD',
                'payee_type': '',
                "bank_account_number": "123456",
                "bsb_id": "034221",
                "bsb_bank": "WBC",
                "payee_split": "100.00",
                "payee_status": "active",
            }]
        }
        mock_fetch.return_value = mock_response

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('ngrDetails', response.data)
        self.assertEqual(len(response.data['ngrDetails']), 1)
        self.assertEqual(response.data['ngrDetails'][0]['businessName'], 'Test Farm')

        mock_fetch.assert_called_once_with(self.company.id, self.ngr_number, True)

    @patch('core.services.external.ngr.NGRLookUp.fetch')
    def test_ngr_lookup_with_error(self, mock_fetch):
        error_response = {
            'error': 'Invalid NGR number',
            'code': 'INVALID_NGR'
        }
        mock_fetch.return_value = error_response

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['error'], 'Invalid NGR number')
        self.assertEqual(response.data['code'], 'INVALID_NGR')

    @patch('core.services.external.ngr.NGRLookUp.fetch')
    def test_ngr_lookup_with_multiple_results(self, mock_fetch):
        CompanyFactory(abn='***********')
        CompanyFactory(abn='***********')
        mock_response = {'payees': [
            {
                'abn': '***********',
                'ngr_number': self.ngr_number,
                'business_name': 'Test Farm 1',
                'bank_account_name': 'Test',
                'status': 'ACTIVE',
                'bank_id': 2,
                'entity_name': 'Test Trading 1',
                'payee_type': '',
                "bank_account_number": "123456",
                "bsb_id": "034221",
                "bsb_bank": "WBC",
                "payee_split": "100.00",
                "payee_status": "active",
            },
            {
                'abn': '***********',
                'ngr_number': self.ngr_number,
                'business_name': 'Test Farm 2',
                'bank_account_name': 'Test',
                'status': 'ACTIVE',
                'bank_id': 2,
                'entity_name': 'Test Trading 2',
                'payee_type': '',
                "bank_account_number": "123457",
                "bsb_id": "034221",
                "bsb_bank": "WBC",
                "payee_split": "100.00",
                "payee_status": "active",
            }
        ]}
        mock_fetch.return_value = mock_response

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('ngrDetails', response.data)
        self.assertEqual(len(response.data['ngrDetails']), 2)
        self.assertEqual(response.data['ngrDetails'][0]['businessName'], 'Test Farm 1')
        self.assertEqual(response.data['ngrDetails'][1]['businessName'], 'Test Farm 2')

    @patch('core.services.external.ngr.NGRLookUp.fetch')
    def test_ngr_lookup_with_empty_response(self, mock_fetch):
        mock_fetch.return_value = []

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('ngrDetails', response.data)
        self.assertEqual(len(response.data['ngrDetails']), 0)

    @patch('core.services.external.ngr.NGRLookUp.fetch')
    def test_ngr_lookup_with_none_response(self, mock_fetch):
        mock_fetch.return_value = None

        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('ngrDetails', response.data)
        self.assertEqual(len(response.data['ngrDetails']), 0)

    def test_invalid_company_id(self):
        url = f'/companies/99999/ngrs/{self.ngr_number}/validate/'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['error'], 'NGR Portal Credentials not found')

@tag('view')
class AllCompaniesMinimalisticViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.country_id = AUSTRALIA_COUNTRY_ID

        self.company1 = CompanyFactory(
            business_name='Alpha Company',
            country_id=self.country_id,
            type_id=GROWER_TYPE_ID,
            transaction_participation=True
        )

        self.company2 = CompanyFactory(
            business_name='Beta Corp',
            country_id=self.country_id,
            owner_company_id=CompanyFactory().id,
            type_id=TRADER_TYPE_ID,
            transaction_participation=False
        )

        self.client.force_authenticate(self.employee)

    @patch('core.common.utils.get_request_country_id')
    def test_get_all_companies(self, mock_country):
        mock_country.return_value = self.country_id
        response = self.client.get('/companies/minimal/all/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 5)
        self.assertIn('name', response.data[0])

    @patch('core.common.utils.get_request_country_id')
    def test_search_companies(self, mock_country):
        mock_country.return_value = self.country_id
        response = self.client.get('/companies/minimal/all/?search=Alpha')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'Alpha Company')

    @patch('core.common.utils.get_request_country_id')
    def test_no_match_search(self, mock_country):
        mock_country.return_value = self.country_id
        response = self.client.get('/companies/minimal/all/?search=NonExistent')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

@tag('view')
class AllRegisteredCompaniesMinimalisticViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.country_id = AUSTRALIA_COUNTRY_ID

        self.registered_company = CompanyFactory(
            business_name='Alpha Company',
            country_id=self.country_id,
            owner_company_id=None,
            type_id=GROWER_TYPE_ID,
            transaction_participation=True
        )

        self.owned_company = CompanyFactory(
            business_name='Beta Corp',
            country_id=self.country_id,
            owner_company_id=CompanyFactory().id,
            type_id=TRADER_TYPE_ID,
            transaction_participation=False
        )

        self.client.force_authenticate(self.employee)

    @patch('core.common.utils.get_request_country_id')
    def test_get_registered_companies(self, mock_country):
        mock_country.return_value = self.country_id
        response = self.client.get('/companies/minimal/registered/')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)
        self.assertTrue(self.registered_company.id in [data['id'] for data in response.data])

    @patch('core.common.utils.get_request_country_id')
    def test_search_registered_companies(self, mock_country):
        mock_country.return_value = self.country_id
        response = self.client.get('/companies/minimal/registered/?search=Alpha')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'Alpha Company')

    @patch('core.common.utils.get_request_country_id')
    def test_no_match_search_registered(self, mock_country):
        mock_country.return_value = self.country_id
        response = self.client.get('/companies/minimal/registered/?search=NonExistent')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

class TestCompanyOwnershipStocks(AuthSetup):
    def setUp(self):
        super().setUp()
        self.url = '/companies/stocks/mobile/'

        self.farm = FarmFactory(company=self.company, stocks_management=True)
        self.farm2 = FarmFactory(company=self.company, stocks_management=True)
        self.storage = StorageFactory(farm=self.farm, type='silo')
        self.container = StorageFactory(farm=self.farm2, type='container')

        self.ngr = NgrFactory(company=self.company)
        self.ngr.owner_company_ids = [self.company.id]
        self.ngr.save()

        self.load = LoadFactory(
            ngr=self.ngr,
            commodity_id=1,
            grade_id=4,
            season='23/24',
            storage=self.storage,
            farm=self.farm,
            type='inload',
            status='planned',
            estimated_net_weight=100
        )

        self.stock = Stock(
            ngr_id=self.load.ngr.id,
            commodity_id=self.load.commodity_id,
            grade_id=self.load.grade_id,
            season=self.load.season,
            storage_id=self.load.storage_id,
            tonnage=100,
            variety_ids=[1]
        )
        self.stock.save()

        self.container_load = LoadFactory(
            ngr=self.ngr,
            commodity_id=2,
            grade_id=5,
            season='23/24',
            storage=self.container,
            farm=self.farm2,
            type='inload',
            status='planned',
            estimated_net_weight=250
        )

        self.container_stock = Stock(
            ngr_id=self.container_load.ngr.id,
            commodity_id=self.container_load.commodity_id,
            grade_id=self.container_load.grade_id,
            season=self.container_load.season,
            storage_id=self.container_load.storage_id,
            tonnage=250,
            variety_ids=[2]
        )
        self.container_stock.save()

    def test_get_stocks_basic(self):
        self.company.enable_my_stocks_on_mobile = True
        self.company.save()

        self.client.force_authenticate(user=self.employee)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        stock_data = response.data[0]
        expected_identifier = f"{self.farm.id}.{self.stock.commodity_id}.{self.stock.grade_id}"
        expected_identifier += f".{self.stock.season.replace('/', '')}.{self.stock.ngr_id}"
        self.assertEqual(stock_data['identifier'], expected_identifier)
        self.assertTrue(stock_data['myStocks'])
        self.assertFalse(stock_data['myStocksAtOtherSites'])
        self.assertEqual(stock_data['tonnage'], 100)

    def test_get_third_party_stocks(self):
        farm_owner_company = CompanyFactory()

        third_party_farm = FarmFactory(company=farm_owner_company, stocks_management=True)
        third_party_storage = StorageFactory(farm=third_party_farm, type='silo')

        third_party_ngr = NgrFactory(company=self.employee.company)
        third_party_ngr.owner_company_ids = [self.employee.company.id]
        third_party_ngr.save()

        third_party_load = LoadFactory(
            ngr=third_party_ngr,
            commodity_id=3,
            grade_id=6,
            season='23/24',
            storage=third_party_storage,
            farm=third_party_farm,
            type='inload',
            status='planned',
            estimated_net_weight=200
        )

        third_party_stock = Stock(
            ngr_id=third_party_load.ngr.id,
            commodity_id=third_party_load.commodity_id,
            grade_id=third_party_load.grade_id,
            season=third_party_load.season,
            storage_id=third_party_load.storage_id,
            tonnage=200,
            variety_ids=[3]
        )
        third_party_stock.save()
        self.employee.enable_my_stocks_on_mobile = False
        self.employee.save()

        self.client.force_authenticate(user=self.employee)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        stock_data = response.data[0]
        self.assertTrue(stock_data['myStocksAtOtherSites'])
        self.assertEqual(stock_data['farmId'], third_party_farm.id)
        self.assertEqual(stock_data['tonnage'], 200)

    def test_get_stocks_only_containers(self):
        self.company.enable_my_stocks_on_mobile = True
        self.company.transaction_participation = True
        self.company.save()
        self.farm2.stocks_management = True
        self.farm2.save()

        self.client.force_authenticate(user=self.employee)
        response = self.client.get(f'{self.url}?only_containers=true')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        stock_data = response.data[0]
        self.assertEqual(stock_data['commodityId'], self.container_stock.commodity_id)
        self.assertEqual(stock_data['tonnage'], 250)

    def test_get_stocks_with_from_parameter(self):
        self.company.enable_my_stocks_on_mobile = True
        self.company.transaction_participation = True
        self.company.save()

        self.client.force_authenticate(user=self.employee)

        past_epoch = (int(time()) - 3600) * 1000
        response = self.client.get(f'{self.url}?from={past_epoch}')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)

        future_epoch = (int(time()) + 3600) * 1000
        response = self.client.get(f'{self.url}?from={future_epoch}')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

@tag('view')
class CompanyXeroFixturesViewTest(AuthSetup):
    def setUp(self):
        super().setUp()

        self.company = CompanyFactory()

        self.xero_connection = XeroConnection.objects.create(
            company=self.company,
            refresh_token="test_refresh_token",
            expires_in="2090-01-02T12:00:00Z"
        )

        self.client.force_authenticate(self.employee)

    @patch('core.companies.models.XeroConnection.get_accounts_and_items')
    def test_get_fixtures_as_staff(self, mock_get):
        self.employee.is_staff = True
        self.employee.save()

        mock_get.return_value = {
            'accounts': ['account1', 'account2'],
            'items': ['item1', 'item2']
        }

        response = self.client.get(f'/companies/{self.company.id}/xero/connections/fixtures/')

        self.assertEqual(response.status_code, 200)
        self.assertIn('accounts', response.data)
        self.assertIn('items', response.data)

    @patch('core.companies.models.XeroConnection.get_accounts_and_items')
    def test_get_fixtures_as_company_user(self, mock_get):
        self.employee.is_staff = False
        self.employee.company_id = self.company.id
        self.employee.save()

        mock_get.return_value = {
            'accounts': ['account1', 'account2'],
            'items': ['item1', 'item2']
        }

        response = self.client.get(f'/companies/{self.company.id}/xero/connections/fixtures/')

        self.assertEqual(response.status_code, 200)
        self.assertIn('accounts', response.data)
        self.assertIn('items', response.data)

    def test_get_fixtures_wrong_company(self):
        other_company = CompanyFactory()
        self.employee.is_staff = False
        self.employee.company_id = other_company.id
        self.employee.save()

        response = self.client.get(f'/companies/{self.company.id}/xero/connections/fixtures/')

        self.assertEqual(response.status_code, 403)

    def test_get_fixtures_no_connection(self):
        self.employee.is_staff = True
        self.employee.save()

        self.xero_connection.delete()

        response = self.client.get(f'/companies/{self.company.id}/xero/connections/fixtures/')

        self.assertEqual(response.status_code, 404)

    @patch('core.companies.models.XeroConnection.get_accounts_and_items')
    def test_get_fixtures_api_error(self, mock_get):
        self.employee.is_staff = True
        self.employee.save()

        mock_get.side_effect = Exception("API Error")

        response = self.client.get(f'/companies/{self.company.id}/xero/connections/fixtures/')

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data['error'], "API Error")

    def test_get_fixtures_company_not_found(self):
        self.employee.is_staff = True
        self.employee.save()

        response = self.client.get('/companies/99999/xero/connections/fixtures/')

        self.assertEqual(response.status_code, 404)

@tag('view')
class CompanyXeroExchangeCodeViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)

        self.company = CompanyFactory()

        XeroConnection.objects.create(
            company=self.company,
            refresh_token="test_refresh_token",
            expires_in="2090-01-02T12:00:00Z"
        )

        self.exchange_code_payload = {
            'code': 'test_auth_code',
            'redirect_uri': 'https://example.com/callback'
        }

    @patch('core.companies.models.XeroConnection.exchange_code')
    def test_exchange_code_as_staff(self, mock_exchange):
        self.employee.is_staff = True
        self.employee.save()

        mock_exchange.return_value = {
            'access_token': 'test_access_token',
            'refresh_token': 'test_refresh_token',
            'expires_in': "2090-01-02T12:00:00Z"
        }

        response = self.client.post(
            f'/companies/{self.company.id}/xero/exchange-code/',
            self.exchange_code_payload,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertIn('accessToken', response.data)
        mock_exchange.assert_called_once_with(
            self.company, {'code': self.exchange_code_payload['code'],
                           'redirect_uri': self.exchange_code_payload['redirect_uri']}
        )

    @patch('core.companies.models.Company.set_xero_tenant_id')
    @patch('core.companies.models.XeroConnection.exchange_code')
    def test_exchange_code_as_company_user(self, mock_exchange, mock_set_tenant):
        self.employee.company_id = self.company.id
        self.employee.save()

        mock_exchange.return_value = {
            'access_token': 'test_access_token',
            'refresh_token': 'test_refresh_token',
            'expires_in': "2090-01-02T12:00:00Z"
        }

        response = self.client.post(
            f'/companies/{self.company.id}/xero/exchange-code/',
            self.exchange_code_payload,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertIn('accessToken', response.data)
        mock_exchange.assert_called_once_with(
            self.company,
            {'code': self.exchange_code_payload['code'],
             'redirect_uri': self.exchange_code_payload['redirect_uri']}
        )
        mock_set_tenant.assert_called_once()

    def test_exchange_code_missing_code(self):
        self.employee.is_staff = True
        self.employee.save()

        payload = {'redirect_uri': self.exchange_code_payload['redirect_uri']}

        response = self.client.post(
            f'/companies/{self.company.id}/xero/exchange-code/',
            payload,
            format='json'
        )

        self.assertEqual(response.status_code, 400)

    def test_exchange_code_missing_redirect_uri(self):
        self.employee.is_staff = True
        self.employee.save()

        payload = {'code': self.exchange_code_payload['code']}

        response = self.client.post(
            f'/companies/{self.company.id}/xero/exchange-code/',
            payload,
            format='json'
        )

        self.assertEqual(response.status_code, 400)

    def test_exchange_code_wrong_company(self):
        other_company = CompanyFactory()
        self.employee.company_id = other_company.id
        self.employee.save()

        response = self.client.post(
            f'/companies/{self.company.id}/xero/exchange-code/',
            self.exchange_code_payload,
            format='json'
        )

        self.assertEqual(response.status_code, 403)

    def test_exchange_code_company_not_found(self):
        self.employee.is_staff = True
        self.employee.save()

        response = self.client.post(
            '/companies/99999/xero/exchange-code/',
            self.exchange_code_payload,
            format='json'
        )

        self.assertEqual(response.status_code, 404)

@tag('view')
class CompanyXeroMappingsViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)
        self.company = CompanyFactory()

        self.commodity = Commodity.objects.get(name='wheat')

        self.existing_mapping = XeroMapping.objects.create(
            company=self.company,
            commodity=self.commodity,
            xero_account='200',
            transaction_type='deductions'
        )

        self.existing_mapping2 = XeroMapping.objects.create(
            company=self.company,
            commodity=self.commodity,
            xero_account='201',
            transaction_type='warehouse'
        )

    def test_get_mappings(self):
        self.employee.is_staff = True
        self.employee.save()

        response = self.client.get(f'/companies/{self.company.id}/xero/mappings/')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(response.data[0]['id'], self.existing_mapping.id)
        self.assertEqual(response.data[1]['id'], self.existing_mapping2.id)

    def test_get_mappings_filtered(self):
        self.employee.is_staff = True
        self.employee.save()

        response = self.client.get(
            f'/companies/{self.company.id}/xero/mappings/',
            {'transaction_type': 'deductions', 'commodity_id': str(self.commodity.id)}
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], self.existing_mapping.id)

    @patch('core.companies.models.XeroTrackingCategory.upsert')
    @patch('core.companies.models.XeroMapping.upsert')
    def test_create_mapping_success(self, mock_mapping_upsert, mock_tracking_upsert):
        self.employee.is_staff = True
        self.employee.save()

        mock_tracking_upsert.return_value = [1]

        new_mapping = XeroMapping.objects.create(
            company=self.company,
            commodity=self.commodity,
            xero_account='200',
            transaction_type='deductions',
            item_type='movements'
        )
        mock_mapping_upsert.return_value = new_mapping

        payload = {
            'xero_mappings': [{
                'transaction_type': 'deductions',
                'commodity': self.commodity.id,
                'xero_account': '200',
                'item_type': 'movements'
            }],
            'tracking_categories': [{
                'name': 'Department',
                'options': ['Sales']
            }]
        }

        response = self.client.post(
            f'/companies/{self.company.id}/xero/mappings/',
            payload,
            format='json'
        )

        self.assertEqual(response.status_code, 201)
        mock_mapping_upsert.assert_called_once()

    @patch('core.companies.models.XeroMapping.upsert')
    def test_create_mapping_error(self, mock_mapping_upsert):
        self.employee.is_staff = True
        self.employee.save()

        error_mapping = XeroMapping(
            company=self.company,
            commodity=self.commodity,
            xero_account='200',
            transaction_type='deductions'
        )
        error_mapping.errors = {'__all__': ['This combination already exists']}
        mock_mapping_upsert.return_value = error_mapping

        payload = {
            'xero_mappings': [{
                'transaction_type': 'deductions',
                'commodity': self.commodity.id,
                'xero_account': '200'
            }]
        }

        response = self.client.post(
            f'/companies/{self.company.id}/xero/mappings/',
            payload,
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertIn('errors', response.data[0])

@tag('view')
class CompanyXeroTrackingCategoriesViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)
        self.company = CompanyFactory()

        self.xero_connection = XeroConnection.objects.create(
            company=self.company,
            refresh_token="test_refresh_token",
            expires_in="2090-01-02T12:00:00Z"
        )

    @patch('core.companies.models.XeroConnection.get_tracking_categories')
    def test_get_tracking_categories_as_staff(self, mock_get):
        self.employee.is_staff = True
        self.employee.save()

        mock_get.return_value = [
            {
                'trackingCategoryId': '123',
                'name': 'Department',
                'options': [
                    {
                        'id': '456',
                        'name': 'Sales',
                    },
                    {
                        'id': '789',
                        'name': 'Marketing',
                    }
                ]
            }
        ]

        response = self.client.get(
            f'/companies/{self.company.id}/xero/tracking-categories/'
        )

        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data) > 0)
        self.assertEqual(response.data[0]['name'], 'Department')
        self.assertEqual(len(response.data[0]['options']), 2)
        self.assertEqual(response.data[0]['options'][0]['name'], 'Sales')

    def test_get_tracking_categories_no_connection(self):
        self.employee.is_staff = True
        self.employee.save()
        self.xero_connection.delete()

        response = self.client.get(
            f'/companies/{self.company.id}/xero/tracking-categories/'
        )
        self.assertEqual(response.status_code, 404)

    @patch('core.companies.models.XeroConnection.get_tracking_categories')
    def test_get_tracking_categories_error(self, mock_get):
        self.employee.is_staff = True
        self.employee.save()

        mock_get.side_effect = Exception("API Error")

        response = self.client.get(
            f'/companies/{self.company.id}/xero/tracking-categories/'
        )
        self.assertEqual(response.status_code, 400)
        self.assertIn('error', response.data)
        self.assertEqual(response.data['error'], 'API Error')

    def test_get_tracking_categories_wrong_company(self):
        other_company = CompanyFactory()
        self.employee.company_id = other_company.id
        self.employee.save()

        response = self.client.get(
            f'/companies/{self.company.id}/xero/tracking-categories/'
        )
        self.assertEqual(response.status_code, 403)

@tag('view')
class CompanyAcquisitionRecipientsViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)
        self.company = CompanyFactory()

        self.recipients_data = {
            'acquisition_recipients': [
                '<EMAIL>',
                '<EMAIL>'
            ]
        }

    def test_get_recipients_with_data(self):
        self.company.acquisition_recipients = {
            'emails': self.recipients_data['acquisition_recipients']
        }
        self.company.save()

        response = self.client.get(
            f'/companies/{self.company.id}/acquisition-recipients/'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['emails'][0], '<EMAIL>')

    def test_get_recipients_empty(self):
        response = self.client.get(
            f'/companies/{self.company.id}/acquisition-recipients/'
        )
        self.assertEqual(response.status_code, 200)
        self.assertIsNone(response.data)

    def test_get_recipients_company_not_found(self):
        response = self.client.get('/companies/99999/acquisition-recipients/')
        self.assertEqual(response.status_code, 404)

    @patch('core.companies.models.Company.upsert_acquisition_recipients')
    def test_update_recipients(self, mock_upsert):
        mock_upsert.return_value = None

        response = self.client.put(
            f'/companies/{self.company.id}/acquisition-recipients/',
            self.recipients_data,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        mock_upsert.assert_called_once_with(self.recipients_data)

@tag('view')
class CompanyTaggedNgrViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)
        self.company = CompanyFactory()
        self.test_ngr_id = 'NGR123'

    @patch('core.companies.models.Company.get_tagged_ngr_or_logical')
    def test_get_regular_tag(self, mock_get_tagged):
        mock_get_tagged.return_value = self.test_ngr_id

        response = self.client.get(
            f'/companies/{self.company.id}/ngrs/tags/some_tag/'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['ngrId'], self.test_ngr_id)
        mock_get_tagged.assert_called_once_with('some_tag')

    @patch('core.companies.models.Company.get_tagged_ngr_or_logical')
    def test_get_warehouse_invoice_fallback_to_title_transfer(self, mock_get_tagged):
        mock_get_tagged.return_value = self.test_ngr_id

        response = self.client.get(
            f'/companies/{self.company.id}/ngrs/tags/warehouse_invoice/'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['ngrId'], self.test_ngr_id)
        mock_get_tagged.assert_called_once_with('title_transfer')

    @patch('core.ngrs.models.Ngr.objects.filter')
    @patch('core.companies.models.Company.get_tagged_ngr_or_logical')
    def test_get_warehouse_invoice_with_existing_tag(self, mock_get_tagged, mock_ngr_filter):
        mock_ngr_filter.return_value.exists.return_value = True
        mock_get_tagged.return_value = self.test_ngr_id

        response = self.client.get(
            f'/companies/{self.company.id}/ngrs/tags/warehouse_invoice/'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['ngrId'], self.test_ngr_id)
        mock_get_tagged.assert_called_once_with('warehouse_invoice')

    def test_get_tag_company_not_found(self):
        response = self.client.get('/companies/99999/ngrs/tags/some_tag/')
        self.assertEqual(response.status_code, 404)

@tag('view')
class CompanyReportRecipientsViewTest(AuthSetup):
    def setUp(self):
        super().setUp()

        self.system_company =  CompanyFactory(type_id=SYSTEM_TYPE_ID)
        self.test_employee = EmployeeFactory(company=self.company, is_staff=True)

        self.recipients_data = {
            'report_recipients': ['<EMAIL>', '<EMAIL>']
        }
        self.client.force_authenticate(self.test_employee)

    def test_update_recipients_as_system_company(self):
        response = self.client.post(
            f'/companies/{self.company.id}/report-recipients/',
            self.recipients_data,
            format='json'
        )
        self.assertEqual(response.status_code, 200)

        updated_company = Company.objects.get(id=self.company.id)
        self.assertEqual(
            updated_company.report_recipients,
            self.recipients_data['report_recipients']
        )

    def test_update_recipients_company_not_found(self):
        response = self.client.post(
            '/companies/99999/report-recipients/',
            self.recipients_data,
            format='json'
        )
        self.assertEqual(response.status_code, 404)

    def test_update_recipients_null_data(self):
        response = self.client.post(
            f'/companies/{self.company.id}/report-recipients/',
            {'report_recipients': None},
            format='json'
        )
        self.assertEqual(response.status_code, 200)

        updated_company = Company.objects.get(id=self.company.id)
        self.assertEqual(updated_company.report_recipients, None)

@tag('view')
class CompanySSOEmployeeUpsertViewTest(AuthSetup):
    def setUp(self):
        super().setUp()

        self.company = CompanyFactory()
        self.valid_payload = {
            'username': '<EMAIL>',
            'jwt_id_token': 'valid.jwt.token',
            'first_name': 'Test',
            'last_name': 'User',
            'email': '<EMAIL>',
            'type_id': self.employee.type_id
        }

    def test_create_new_employee(self):
        response = self.client.post(
            f'/companies/{self.company.id}/sso/employees/upsert/',
            self.valid_payload,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.valid_payload['username'], response.data['user']['username'])

        created_employee = Employee.objects.get(username=self.valid_payload['username'])
        self.assertEqual(created_employee.company, self.company)
        self.assertEqual(created_employee.first_name, self.valid_payload['first_name'])
        self.assertEqual(created_employee.email, self.valid_payload['email'])

    def test_update_existing_employee(self):
        existing_employee = EmployeeFactory(
            company=self.company,
            username=self.valid_payload['username']
        )
        original_password = existing_employee.password

        payload = {
            **self.valid_payload,
            'username': existing_employee.username,
            'email': existing_employee.email
        }

        response = self.client.post(
            f'/companies/{self.company.id}/sso/employees/upsert/',
            payload,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.valid_payload['username'], response.data['user']['username'])

        existing_employee.refresh_from_db()
        self.assertIsNotNone(existing_employee.last_login)
        self.assertEqual(existing_employee.password, original_password)

    def test_missing_username(self):
        payload = self.valid_payload.copy()
        del payload['username']

        response = self.client.post(
            f'/companies/{self.company.id}/sso/employees/upsert/',
            payload,
            format='json'
        )
        self.assertEqual(response.status_code, 400)

    def test_missing_jwt_token(self):
        payload = self.valid_payload.copy()
        del payload['jwt_id_token']

        response = self.client.post(
            f'/companies/{self.company.id}/sso/employees/upsert/',
            payload,
            format='json'
        )
        self.assertEqual(response.status_code, 400)

    def test_company_not_found(self):
        response = self.client.post(
            '/companies/99999/sso/employees/upsert/',
            self.valid_payload,
            format='json'
        )
        self.assertEqual(response.status_code, 404)

    def test_invalid_employee_data(self):
        invalid_payload = self.valid_payload.copy()
        invalid_payload['email'] = 'not_an_email'

        response = self.client.post(
            f'/companies/{self.company.id}/sso/employees/upsert/',
            invalid_payload,
            format='json'
        )
        self.assertEqual(response.status_code, 400)

@tag('view')
class CompanyApprovedBuyersListViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)
        self.company = CompanyFactory()

        self.buyer1 = CompanyFactory(
            business_name="Alpha Corp",
            abn="***********",
            mobile="1234567890",
            website="www.alpha.com"
        )
        self.buyer2 = CompanyFactory(
            business_name="Beta Ltd",
            abn="***********",
            mobile="0987654321",
            website="www.beta.com"
        )

        self.company.approved_buyers.add(self.buyer1, self.buyer2)
        self.company.save()

    def test_get_approved_buyers_list(self):
        response = self.client.get(
            f'/companies/{self.company.id}/web/approved-buyers/'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 2)

    def test_search_approved_buyers(self):
        response = self.client.get(
            f'/companies/{self.company.id}/web/approved-buyers/?search=Alpha'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Corp')

    def test_order_by_name_asc(self):
        response = self.client.get(
            f'/companies/{self.company.id}/web/approved-buyers/?order_by=name&order=asc'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Alpha Corp')

    def test_order_by_name_desc(self):
        response = self.client.get(
            f'/companies/{self.company.id}/web/approved-buyers/?order_by=name&order=desc'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['results'][0]['name'], 'Beta Ltd')


@tag('view')
class CompanyApprovedBuyersByABNView(AuthSetup):

    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)
        self.company = CompanyFactory()
        self.buyer_company = CompanyFactory()

    def test_add_approved_buyers(self):
        new_buyer = CompanyFactory()
        response = self.client.post(
            f'/companies/abn/{self.company.abn}/approved-buyers/',
            {'abns': [new_buyer.abn]},
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            self.company.approved_buyers.filter(id=new_buyer.id).exists()
        )


@tag('view')
class CompanyApprovedBuyersViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)
        self.company = CompanyFactory()
        self.buyer_company = CompanyFactory()

    def test_get_approved_buyers_ids(self):
        self.company.approved_buyers.add(self.buyer_company)

        response = self.client.get(
            f'/companies/{self.company.id}/approved-buyers/'
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn(self.buyer_company.id, response.data['companyIds'])

    def test_get_approved_buyers_company_not_found(self):
        response = self.client.get('/companies/99999/approved-buyers/')
        self.assertEqual(response.status_code, 404)

    def test_add_approved_buyers(self):
        new_buyer = CompanyFactory()

        response = self.client.post(
            f'/companies/{self.company.id}/approved-buyers/',
            {'company_ids': [new_buyer.id]},
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(
            self.company.approved_buyers.filter(id=new_buyer.id).exists()
        )

    def test_remove_approved_buyer(self):
        self.company.approved_buyers.add(self.buyer_company)

        response = self.client.delete(
            f'/companies/{self.company.id}/approved-buyers/?company_id={self.buyer_company.id}'
        )
        self.assertEqual(response.status_code, 200)
        self.assertFalse(
            self.company.approved_buyers.filter(id=self.buyer_company.id).exists()
        )

    def test_remove_approved_buyer_company_not_found(self):
        response = self.client.delete(
            f'/companies/99999/approved-buyers/?company_id={self.buyer_company.id}'
        )
        self.assertEqual(response.status_code, 404)

@tag('view')
class CompanyContractBidsViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)

        self.company = self.employee.company

        now = timezone.now() - datetime.timedelta(days=1)
        end_time = timezone.now() + datetime.timedelta(days=1)
        delivery_end_time = timezone.now() + datetime.timedelta(days=10)

        site = FarmFactory(company=self.company)

        other_company = CompanyFactory()
        other_buyer_contact = EmployeeFactory(company=other_company)
        other_buyer_ngr = NgrFactory(company=other_company)

        contract_bid_params = {
            'site_id': site.id,
            'commodity_id': 1,
            'season': '22/23',
            'grade_id': 7,
            'payment_term_id': 1,
            'payment_scale_id': 1,
             'buyer': {
                'company_id': other_company.id,
                'contact_id': other_buyer_contact.id,
                'ngr_id': other_buyer_ngr.id
            },
            'price': 100,
            'limit': 100,
            'track': 'TRACK1',
            'start_date_time': now.strftime('%Y-%m-%d %H:%M:%S'),
            'end_date_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
            'delivery_start_date': now.strftime('%Y-%m-%d'),
            'delivery_end_date': delivery_end_time.strftime('%Y-%m-%d'),
            'group_id': None,
            'status': 'active',
            'created_by': self.employee
        }
        self.active_bid1 = ContractBid.persist(contract_bid_params)

        self.archived_bid = ContractBid.persist({
            **contract_bid_params,
            'buyer': {
                'company_id': other_company.id,
                'contact_id': other_buyer_contact.id,
                'ngr_id': other_buyer_ngr.id
            },
            'status': 'inactive',
            'track': 'TRACK2',
            'created_by': self.employee
        })

    @patch('core.common.models.CommonFilters')
    def test_get_active_bids(self, mock_common_filters):
        mock_common_filters.return_value.conditional_filtering.return_value = {}

        response = self.client.get(f'/companies/{self.company.id}/contract_bids/active/')
        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data['results']) > 0)
        self.assertEqual(response.data['results'][0]['status'], 'active')
        self.assertEqual(response.data['results'][0]['id'], self.active_bid1.id)

    @patch('core.common.models.CommonFilters')
    def test_get_my_active_bids(self, mock_common_filters):
        mock_common_filters.return_value.conditional_filtering.return_value = {}

        response = self.client.get(f'/companies/{self.company.id}/contract_bids/my_active/')
        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data['results']) > 0)
        self.assertEqual(response.data['results'][0]['id'], self.active_bid1.id)

    @patch('core.common.models.CommonFilters')
    def test_get_archived_bids(self, mock_common_filters):
        mock_common_filters.return_value.conditional_filtering.return_value = {}

        response = self.client.get(f'/companies/{self.company.id}/contract_bids/archived/')
        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data['results']) > 0)
        self.assertEqual(response.data['results'][0]['status'], 'inactive')
        self.assertEqual(response.data['results'][0]['id'], self.archived_bid.id)

    @patch('core.common.models.CommonFilters')
    def test_search_bids(self, mock_common_filters):
        mock_common_filters.return_value.conditional_filtering.return_value = {}

        response = self.client.get(f'/companies/{self.company.id}/contract_bids/active/?search=TRACK1')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['track'], 'TRACK1')
        self.assertEqual(response.data['results'][0]['id'], self.active_bid1.id)

    @patch('core.common.models.CommonFilters')
    def test_order_by_price_desc(self, mock_common_filters):
        mock_common_filters.return_value.conditional_filtering.return_value = {}

        response = self.client.get(f'/companies/{self.company.id}/contract_bids/active/?order_by=price&order=desc')
        self.assertEqual(response.status_code, 200)
        prices = [bid['price'] for bid in response.data['results']]
        self.assertEqual(prices, sorted(prices, reverse=True))

@tag('view')
class CompanyCashPricesViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)

        self.company = CompanyFactory()
        self.employee.company = self.company
        self.employee.save()

        site = CompanySiteFactory(company=self.company)

        now = timezone.now()
        self.params = {
            'site': site,
            'commodity_id': 1,
            'season': '22/23',
            'grade_id': 7,
            'payment_term_id': 1,
            'payment_scale_id': 1,
            'buyer': PartyFactory(role='Buyer', company=self.company),
            'price': 100,
            'limit': 100,
            'track': 'TRACK1',
            'start_date_time': now,
            'end_date_time': now + datetime.timedelta(days=1),
            'status': 'active'
        }
        self.active_price = CashPrices.create(
            self.params
        )

        self.archived_price = CashPrices.create(
            {
                **self.params,
                'status': 'inactive',
                'track': 'Track2',
                'start_date_time': now - datetime.timedelta(days=2),
                'end_date_time': now - datetime.timedelta(days=1),
                'buyer': PartyFactory(role='Buyer', company=self.company),
            }
        )

    @patch('core.common.models.CommonFilters')
    def test_get_active_prices(self, mock_common_filters):
        mock_common_filters.return_value.conditional_filtering.return_value = {}

        response = self.client.get(
            f'/companies/{self.company.id}/cash_prices/active/'
        )

        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data['results']) > 0)
        self.assertEqual(response.data['results'][0]['status'], 'active')
        self.assertEqual(response.data['results'][0]['id'], self.active_price.id)

    @patch('core.common.models.CommonFilters')
    def test_get_site_active_prices(self, mock_common_filters):
        mock_common_filters.return_value.conditional_filtering.return_value = {}
        response = self.client.get(
            f'/companies/{self.company.id}/cash_prices/site-active-prices/'
        )

        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data['results']) > 0)
        self.assertEqual(response.data['results'][0]['id'], self.active_price.id)

    @patch('core.common.models.CommonFilters')
    def test_get_archived_prices(self, mock_common_filters):
        mock_common_filters.return_value.conditional_filtering.return_value = {}
        response = self.client.get(
            f'/companies/{self.company.id}/cash_prices/archived/'
        )

        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data['results']) > 0)
        self.assertEqual(response.data['results'][0]['status'], 'inactive')
        self.assertEqual(response.data['results'][0]['id'], self.archived_price.id)

    @patch('core.common.models.CommonFilters')
    def test_search_prices(self, mock_common_filters):
        mock_common_filters.return_value.conditional_filtering.return_value = {}

        response = self.client.get(
            f'/companies/{self.company.id}/cash_prices/active/?search=TRACK1'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['track'], 'TRACK1')

    @patch('core.common.models.CommonFilters')
    def test_order_by_price_desc(self, mock_common_filters):
        mock_common_filters.return_value.conditional_filtering.return_value = {}

        response = self.client.get(
            f'/companies/{self.company.id}/cash_prices/active/?order_by=price&order=desc'
        )
        self.assertEqual(response.status_code, 200)
        prices = [price['price'] for price in response.data['results']]
        self.assertEqual(prices, sorted(prices, reverse=True))

@tag('view')
class CompanyUnRegisterViewTest(AuthSetup):
    def setUp(self):
        super().setUp()

        self.system_company_id = SYSTEM_COMPANY_IDS[0]
        self.system_company = Company.moderators.filter(id=self.system_company_id).first()
        if not self.system_company:
            self.system_company = CompanyFactory(
                id=self.system_company_id
            )
        self.employee = EmployeeFactory(company=self.system_company, is_staff=True)

        self.client.force_authenticate(self.employee)

        self.company = CompanyFactory(
            owner_company_id=None,
            transaction_participation=True,
            mobile_participation=True,
            can_represent=True
        )

    @patch('core.companies.models.Company.expire_token')
    @patch('core.companies.models.Country.get_system_company_id')
    def test_unregister_company_as_system_company(self, mock_system_company_id, mock_expire_token):
        system_company_id = 999
        mock_system_company_id.return_value = system_company_id

        response = self.client.put(f'/companies/{self.company.id}/unregister/')

        self.assertEqual(response.status_code, 200)

        self.company.refresh_from_db()
        self.assertEqual(self.company.owner_company_id, system_company_id)
        self.assertFalse(self.company.transaction_participation)
        self.assertFalse(self.company.mobile_participation)
        self.assertFalse(self.company.can_represent)
        mock_expire_token.assert_called_once()

    def test_unregister_company_not_system_company(self):
        self.employee.company = CompanyFactory()
        self.employee.is_staff=False
        self.employee.save()

        response = self.client.put(f'/companies/{self.company.id}/unregister/')

        self.assertEqual(response.status_code, 403)

        self.company.refresh_from_db()
        self.assertIsNone(self.company.owner_company_id)
        self.assertTrue(self.company.transaction_participation)
        self.assertTrue(self.company.mobile_participation)
        self.assertTrue(self.company.can_represent)

    @patch('core.companies.models.Country.get_system_company_id')
    def test_unregister_non_existent_company(self, mock_system_company_id):
        mock_system_company_id.return_value = 999

        response = self.client.put('/companies/99999/unregister/')
        self.assertEqual(response.status_code, 403)

@tag('view')
class CompanyBHCSitesViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)

        self.company = CompanyFactory()

        self.address1 = AddressFactory()
        self.address2 = AddressFactory()
        self.address3 = AddressFactory()
        state = State.create({'name': 'TestState'})
        self.market_zone =  Marketzone.create(
            {'state': state,
             'name': 'TestMarketZone'}
        )
        self.region =  Region(name='TestRegion', marketzone=self.market_zone)
        self.region.save()

        self.bhc_site1 = CompanySite.objects.create(
            company=self.company,
            address=self.address1,
            market_zone=self.market_zone,
            region=self.region,
            mode='Rail',
            name='BHC Site 1'
        )

        self.bhc_site2 = CompanySite.objects.create(
            company=self.company,
            address=self.address2,
            market_zone=self.market_zone,
            region=self.region,
            mode='Rail',
            name='BHC Site 2'
        )

        self.non_bhc_site = CompanySite.objects.create(
            company=self.company,
            address=self.address3,
            market_zone=self.market_zone,
            region=self.region,
            mode=None,
            name='Farm Site'
        )

    @patch('core.company_sites.models.CompanySite.qs2dict')
    def test_get_bhc_sites(self, mock_qs2dict):
        expected_data = [
            {
                'id': self.bhc_site1.id,
                'name': 'BHC Site 1',
                'address': {'id': self.address1.id},
                'marketZone': {'id': self.market_zone.id},
                'region': {'id': self.region.id}
            },
            {
                'id': self.bhc_site2.id,
                'name': 'BHC Site 2',
                'address': {'id': self.address2.id},
                'marketZone': {'id': self.market_zone.id},
                'region': {'id': self.region.id}
            }
        ]
        mock_qs2dict.return_value = expected_data

        response = self.client.get(f'/companies/{self.company.id}/bhc/locations/')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, expected_data)

        mock_qs2dict.assert_called_once()
        queryset_arg = mock_qs2dict.call_args[1]['queryset']
        self.assertEqual(queryset_arg.count(), 2)
        self.assertEqual(
            mock_qs2dict.call_args[1]['many_to_one_relations'],
            ['address', 'market_zone', 'region']
        )

    def test_get_bhc_sites_empty_company(self):
        empty_company = CompanyFactory()

        response = self.client.get(f'/companies/{empty_company.id}/bhc/locations/')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, [])

    def test_get_bhc_sites_non_existent_company(self):
        response = self.client.get('/companies/99999/bhc/locations/')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, [])

@tag('view')
class CompanyOrdersSearchViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.client.force_authenticate(self.employee)
        self.company = CompanyFactory()

        self.site = CompanySiteFactory(
            company=self.company, address=AddressFactory(latitude=15.43, longitude=135.96, location_type="companysite"))
        self.site_1 = FarmFactory(
            company=self.company,
            address=AddressFactory(latitude=14.43, longitude=133.96, location_type="farm")
        )
        consignor = ContractCommodityHandlerFactory(
           role='Consignor',
           handler=self.site
        )
        consignee = ContractCommodityHandlerFactory(
            role='Consignee',
            handler=self.site_1
        )

        freight_pickup = FreightPickupFactory(
            consignor=consignor,
            date_time='2090-01-02T10:00:00Z'
        )
        freight_delivery = FreightDeliveryFactory(
            consignee=consignee,
            date_time='2090-01-02T15:00:00Z'
        )

        self.freight_order = FreightOrderFactory(
            identifier='TEST123',
            status='confirmed',
            commodity=Commodity.objects.get(name='wheat'),
            unaccounted_tonnage=100.0,
            freight_pickup=freight_pickup,
            freight_delivery=freight_delivery
        )

    @patch('core.services.external.google.GoogleMaps.get_distance')
    def test_valid_order_search(self, mock_distance):
        mock_distance.return_value = {
            'status': 'OK',
            'distance': '100 km',
            'duration': '2 hours'
        }

        response = self.client.get(
            f'/companies/{self.company.id}/orders/TEST123/',
            {
                'load_type': 'pickup',
                'site_id': self.site.id,
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertFalse(response.data['errors'])
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['orderId'], self.freight_order.id)
        self.assertEqual(response.data['unaccountedTonnage'], 100.0)

    def test_invalid_order_identifier(self):
        response = self.client.get(
            f'/companies/{self.company.id}/orders/INVALID123/',
            {'load_type': 'pickup', 'site_id': self.site.id}
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn('This order is not valid', response.data['errors'])

    def test_missing_load_type(self):
        response = self.client.get(
            f'/companies/{self.company.id}/orders/TEST123/',
            {'site_id': self.site.id}
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn('Please select load type', response.data['errors'])

    def test_missing_site(self):
        response = self.client.get(
            f'/companies/{self.company.id}/orders/TEST123/',
            {'load_type': 'pickup'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn('Please select site', response.data['errors'])

    @patch('core.services.external.google.GoogleMaps.get_distance')
    def test_order_with_site_details(self, mock_distance):
        mock_distance.return_value = {
            'status': 'OK',
            'distance': '100 km',
            'duration': '2 hours'
        }

        response = self.client.get(
            f'/companies/{self.company.id}/orders/TEST123/',
            {
                'load_type': 'pickup',
                'site_id': self.site.id
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn('pickupSite', response.data)
        self.assertIn('deliverySite', response.data)

    def test_invalid_date_range(self):
        response = self.client.get(
            f'/companies/{self.company.id}/orders/TEST123/',
            {
                'load_type': 'pickup',
                'site_id': self.site.id,
                'booking_date': '2024-01-01'
            }
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn('This order is not valid for this date', response.data['errors'])

@tag('view')
class CompanyPowerBIReportViewTest(AuthSetup):
    def test_get_400(self):
        company = CompanyFactory()
        user = EmployeeFactory(company=company)

        response = self.client.get(
            '/companies/{}/power-bi/report/'.format(company.id),
            HTTP_AUTHORIZATION=f'Token {user.refresh_token()}',
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'error': 'Power BI report not configured for this company'})

    def test_get_403(self):
        company = CompanyFactory()
        user = EmployeeFactory()

        response = self.client.get(
            '/companies/{}/power-bi/report/'.format(company.id),
            HTTP_AUTHORIZATION=f'Token {user.refresh_token()}',
            format='json'
        )

        self.assertEqual(response.status_code, 403)

    @patch('core.services.external.power_bi.PowerBI.get_report_metadata')
    def test_get_200(self, service_mock):
        service_mock.return_value = 'data'
        company = CompanyFactory(power_bi_report_id='report-id')
        user = EmployeeFactory(company=company)

        response = self.client.get(
            '/companies/{}/power-bi/report/'.format(company.id),
            HTTP_AUTHORIZATION=f'Token {user.refresh_token()}',
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {'report': 'data'})
