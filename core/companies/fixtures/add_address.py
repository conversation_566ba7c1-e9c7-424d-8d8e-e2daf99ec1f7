import csv
import json
import urllib.request as req
from core.companies.models import Company, AddedCompany
from core.locations.models import Location
from django.conf import settings

def update_address():
    csvfile = open('/code/core/companies/fixtures/74278839342_clients_update_address.csv', "r")
    fieldnames = ['ABN', 'Address']
    reader = csv.DictReader(csvfile, fieldnames)
    failures = []
    url = "https://maps.googleapis.com/maps/api/geocode/json?key={key}&address=".format(key=settings.GOOGLE_API_KEY)
    for i, row in enumerate(reader):
        if(i != 0):
            params = dict(row).copy()
            abn = params['ABN'].strip()
            company = Company.objects.prefetch_related('address').filter(abn=abn).first()
            if not company:
                failure = {}
                failure[abn] = 'Does not exist'
                print("******************************Failure: ", failure)
                failures.append(failure)
            else:
                address = params['Address'].strip()
                try:
                    response = req.urlopen(url + address.replace(" ", "%20"))
                    results = json.loads(response.read().decode())
                    location_data = results['results'][0]['geometry']['location']
                    company.address.name = address
                    company.address.address = address
                    company.address.latitude = location_data['lat']
                    company.address.longitude = location_data['lng']
                    company.address.save()
                    if company.address.errors:
                        failure = {}
                        failure[abn] = company.address.errors
                        print("******************************Failure: ", failure)
                        failures.append(failure)
                    else:
                        print("Updated: ", company.abn)
                except Exception as ex:
                    failure = {}
                    failure[abn] = ex
                    print("******************************Failure: ", failure)
                    failures.append(failure)
    print(failures)

