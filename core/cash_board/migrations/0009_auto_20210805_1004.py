# Generated by Django 2.2.20 on 2021-08-05 10:04

import core.validation.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cash_board', '0008_auto_20210805_0957'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cashprices',
            name='buyer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cash_prices_buyer', to='contracts.Party'),
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='end_date_time',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='grade',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='commodities.Grade'),
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='season',
            field=models.Char<PERSON>ield(max_length=5, validators=[core.validation.validators.season_validator]),
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='start_date_time',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='track',
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name='historicalcashprices',
            name='end_date_time',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='historicalcashprices',
            name='season',
            field=models.CharField(max_length=5, validators=[core.validation.validators.season_validator]),
        ),
        migrations.AlterField(
            model_name='historicalcashprices',
            name='start_date_time',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='historicalcashprices',
            name='track',
            field=models.CharField(max_length=255),
        ),
    ]
