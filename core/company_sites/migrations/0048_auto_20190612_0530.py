# Generated by Django 2.1.7 on 2019-06-12 05:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0047_auto_20190612_0523'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='freightslot',
            name='farm',
        ),
        migrations.RemoveField(
            model_name='freightslot',
            name='site',
        ),
        migrations.RemoveField(
            model_name='historicalfreightslot',
            name='farm',
        ),
        migrations.RemoveField(
            model_name='historicalfreightslot',
            name='site',
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='location_id',
            field=models.PositiveIntegerField(),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='location_type',
            field=models.ForeignKey(limit_choices_to={'model__in': ('farm', 'companysite')}, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.ContentType'),
        ),
        migrations.AlterField(
            model_name='historicalfreightslot',
            name='location_id',
            field=models.PositiveIntegerField(),
        ),
    ]
