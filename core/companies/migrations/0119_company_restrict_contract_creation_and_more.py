# Generated by Django 4.1.7 on 2023-04-05 05:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0117_company_octopus_integration_and_more_squashed_0118_rename_octopus_integration_company_octopusbot_integration_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='restrict_contract_creation',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='company',
            name='type_of_contract',
            field=models.CharField(choices=[('purchase_contract', 'Purchase Contract'), ('sale_contract', 'Sale Contract'), ('sale_purchase_contract', 'Sale & Purchase Contract')], default='purchase_contract', max_length=30),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='restrict_contract_creation',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='type_of_contract',
            field=models.CharField(choices=[('purchase_contract', 'Purchase Contract'), ('sale_contract', 'Sale Contract'), ('sale_purchase_contract', 'Sale & Purchase Contract')], default='purchase_contract', max_length=30),
        ),
    ]
