# Generated by Django 2.1.1 on 2018-09-06 13:43

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0002_auto_20180816_1018'),
    ]

    operations = [
        migrations.AlterField(
            model_name='bhcsite',
            name='mobile',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='bhcsite',
            name='phone',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='company',
            name='mobile',
            field=models.CharField(max_length=10, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='company',
            name='website',
            field=models.CharField(blank=True, max_length=100, null=True, validators=[django.core.validators.RegexValidator('^(http:\\/\\/www\\.|https:\\/\\/www\\.|http:\\/\\/|https:\\/\\/)?[a-z0-9]+([\\-\\.]{1}[a-z0-9]+)*\\.[a-z]{2,5}(:[0-9]{1,5})?(\\/.*)?$', 'Must be a website address')]),
        ),
        migrations.AlterField(
            model_name='historicalbhcsite',
            name='mobile',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='historicalbhcsite',
            name='phone',
            field=models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='mobile',
            field=models.CharField(max_length=10, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='website',
            field=models.CharField(blank=True, max_length=100, null=True, validators=[django.core.validators.RegexValidator('^(http:\\/\\/www\\.|https:\\/\\/www\\.|http:\\/\\/|https:\\/\\/)?[a-z0-9]+([\\-\\.]{1}[a-z0-9]+)*\\.[a-z]{2,5}(:[0-9]{1,5})?(\\/.*)?$', 'Must be a website address')]),
        ),
    ]
