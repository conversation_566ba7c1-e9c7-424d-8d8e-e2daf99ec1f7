from django.core.management.base import BaseCommand
from django.db.models import Q
from django.utils import timezone
from core.common.constants import SEASON_NA
from core.companies.models import Company
from core.contracts.models import Contract, TitleTransfer, StockAllocation
from core.freights.models import <PERSON>eightContract, FreightOrder
from core.loads.models import Load
from core.vendor_decs.models import CommodityVendorDec
from core.company_sites.models import FreightSlot
from core.farms.models import WarehouseFees, Storage


class Command(BaseCommand):
    help = 'Updates season to NA for all Company related entities'

    def __init__(self):
        super().__init__()
        self.company = None
        self.commodity_id = None
        self.batch_size = 1000

    def update_contracts(self):
        contract_qs = Contract.objects.filter(
            viewer_company_ids__contains=[self.company.id],
            commodity_id=self.commodity_id
        ).exclude(season=SEASON_NA).filter(season__isnull=False)
        contract_ids = list(contract_qs.values_list('id', flat=True))
        updated_contract_count = contract_qs.update(
            season=SEASON_NA,
            updated_at=timezone.now()
        )
        self.stdout.write(f"Updated Contracts record count {updated_contract_count}, IDs: {contract_ids}")
        return updated_contract_count

    def update_freight_orders(self):
        freight_order_qs = FreightOrder.objects.filter(
            viewer_company_ids__contains=[self.company.id],
            commodity_id=self.commodity_id
        ).exclude(season=SEASON_NA).filter(season__isnull=False)
        freight_order_ids = list(freight_order_qs.values_list('id', flat=True))
        updated_freight_order_count = freight_order_qs.update(
            season=SEASON_NA,
            updated_at=timezone.now()
        )
        self.stdout.write(f"Updated FreightOrder records count {updated_freight_order_count}, IDs: {freight_order_ids}")
        return updated_freight_order_count

    def update_freight_contracts(self):
        freight_contract_qs = FreightContract.objects.filter(
            viewer_company_ids__contains=[self.company.id],
            commodity_id=self.commodity_id
        ).exclude(season=SEASON_NA).filter(season__isnull=False)
        freight_contract_ids = list(freight_contract_qs.values_list('id', flat=True))
        updated_freight_contract_count = freight_contract_qs.update(
            season=SEASON_NA,
            updated_at=timezone.now()
        )
        self.stdout.write(f"Updated FreightContract records count: {updated_freight_contract_count}, IDs: {freight_contract_ids}")
        return updated_freight_contract_count

    def update_title_transfers(self):
        title_transfer_qs = TitleTransfer.objects.filter(
            viewer_company_ids__contains=[self.company.id],
            commodity_id=self.commodity_id
        ).exclude(season=SEASON_NA).filter(season__isnull=False)
        title_transfer_ids = list(title_transfer_qs.values_list('id', flat=True))
        updated_title_transfer_count = title_transfer_qs.update(
            season=SEASON_NA,
            updated_at=timezone.now()
        )
        self.stdout.write(f"Updated TitleTransfer records count {updated_title_transfer_count}, IDs: {title_transfer_ids}")
        return updated_title_transfer_count

    def update_vendor_dec(self):
        vendor_dec_qs = CommodityVendorDec.objects.filter(
            Q(seller__company=self.company) |
            Q(buyer__company=self.company) |
            Q(customer__company=self.company) |
            Q(contract__viewer_company_ids__contains=[self.company.id]) |
            Q(order__viewer_company_ids__contains=[self.company.id]) |
            Q(movement__viewer_company_ids__contains=[self.company.id])
        ).exclude(season=SEASON_NA).filter(season__isnull=False)
        vendor_dec_ids = list(vendor_dec_qs.values_list('id', flat=True))
        updated_vendor_dec_count = vendor_dec_qs.update(
            season=SEASON_NA,
            updated_at=timezone.now()
        )
        self.stdout.write(f"Updated CommodityVendorDec records count {updated_vendor_dec_count}, IDs: {vendor_dec_ids}")
        return updated_vendor_dec_count

    def update_freight_slots(self):
        freight_slot_qs = FreightSlot.objects.filter(
            (
                    Q(site__company=self.company) |
                    Q(order__viewer_company_ids__contains=[self.company.id]) |
                    Q(movement__viewer_company_ids__contains=[self.company.id])
            ) &
            Q(commodity_id=self.commodity_id)
        ).exclude(season=SEASON_NA).filter(season__isnull=False)
        freight_slot_ids = list(freight_slot_qs.values_list('id', flat=True))
        updated_freight_slot_count = freight_slot_qs.update(
            season=SEASON_NA,
            updated_at=timezone.now()
        )
        self.stdout.write(f"Updated FreightSlot records count {updated_freight_slot_count}, IDs: {freight_slot_ids}")
        return updated_freight_slot_count

    def update_warehouse_fees(self):
        warehouse_fees_qs = WarehouseFees.objects.filter(
            (
                    Q(site__company=self.company) |
                    Q(for_company=self.company)
            ) &
            Q(commodity_id=self.commodity_id)
        ).exclude(season=SEASON_NA).filter(season__isnull=False)
        warehouse_fees_ids = list(warehouse_fees_qs.values_list('id', flat=True))
        updated_warehouse_fees_count = warehouse_fees_qs.update(
            season=SEASON_NA,
            updated_at=timezone.now()
        )
        self.stdout.write(f"Updated WarehouseFees records count {updated_warehouse_fees_count}, IDs: {warehouse_fees_ids}")
        return updated_warehouse_fees_count

    def update_loads(self):
        load_ids = set()

        title_transfer_load_ids = Load.objects.filter(
            title_transfer__viewer_company_ids__contains=[self.company.id],
            commodity_id=self.commodity_id
        ).exclude(season=SEASON_NA).filter(season__isnull=False).values_list('id', flat=True)
        load_ids.update(title_transfer_load_ids)

        movement_load_ids = Load.objects.filter(
            movement__viewer_company_ids__contains=[self.company.id],
            commodity_id=self.commodity_id
        ).exclude(season=SEASON_NA).filter(season__isnull=False).values_list('id', flat=True)
        load_ids.update(movement_load_ids)

        direct_load_ids = Load.objects.filter(
            (
                    Q(farm__company=self.company) |
                    Q(ngr__company=self.company)
            ) &
            Q(commodity_id=self.commodity_id)
        ).exclude(season=SEASON_NA).filter(season__isnull=False).values_list('id', flat=True)
        load_ids.update(direct_load_ids)

        load_ids = list(load_ids)

        updated_loads_count = 0
        for i in range(0, len(load_ids), self.batch_size):
            batch_ids = load_ids[i:i + self.batch_size]
            updated = Load.objects.filter(id__in=batch_ids).update(
                season=SEASON_NA,
                updated_at=timezone.now()
            )
            updated_loads_count += updated
            self.stdout.write(f"Updated loads count {updated}, IDs: {batch_ids}")
        self.stdout.write(f"Total loads updated: {updated_loads_count}")
        return updated_loads_count

    def update_stock_allocations(self):
        stock_allocation_qs = StockAllocation.objects.filter(
            (
                    Q(contract__viewer_company_ids__contains=[self.company.id]) |
                    Q(site__company=self.company)
            ) &
            Q(commodity_id=self.commodity_id)
        ).exclude(season=SEASON_NA).filter(season__isnull=False)
        stock_allocation_ids = list(stock_allocation_qs.values_list('id', flat=True))
        updated_stock_allocation_count = stock_allocation_qs.update(
            season=SEASON_NA,
            updated_at=timezone.now()
        )
        self.stdout.write(f"Updated StockAllocation records count {updated_stock_allocation_count}, IDs: {stock_allocation_ids}")
        return updated_stock_allocation_count

    def handle(self, *args, **options):
        try:
            for combo in [
                {'abn': '***********', 'commodity_id': 27},
                {'abn': '9429051407957', 'commodity_id': 229},
            ]:
                self.company = Company.objects.filter(abn=combo['abn']).first()
                self.commodity_id = combo['commodity_id']
                updated_contract_count = self.update_contracts()
                updated_freight_order_count = self.update_freight_orders()
                updated_freight_contract_count = self.update_freight_contracts()
                updated_title_transfer_count = self.update_title_transfers()
                updated_loads_count = self.update_loads()
                updated_vendor_decs_count = self.update_vendor_dec()
                updated_freight_slots_count = self.update_freight_slots()
                updated_warehouse_fees_count = self.update_warehouse_fees()
                updated_stock_allocation_count = self.update_stock_allocations()

                summary = (
                    f"\n{combo['abn']} Update Summary:\n"
                    f"Contracts: {updated_contract_count}\n"
                    f"FreightOrders: {updated_freight_order_count}\n"
                    f"FreightContracts: {updated_freight_contract_count}\n"
                    f"TitleTransfers: {updated_title_transfer_count}\n"
                    f"Loads: {updated_loads_count}\n"
                    f"Vendor Decs: {updated_vendor_decs_count}\n"
                    f"Freight Slots: {updated_freight_slots_count}\n"
                    f"Warehouse Fees: {updated_warehouse_fees_count}\n"
                    f"Stock Allocations: {updated_stock_allocation_count}\n"
                )
                for storage in Storage.objects.filter(farm__company=self.company):
                    storage.recreate_storage_levels_from_loads_inline(
                        {'storage_id': storage.id, 'commodity_id': self.commodity_id})
                self.stdout.write(self.style.SUCCESS(summary))

        except Exception as e:
            error_message = f"Error occurred: {str(e)}"
            self.stdout.write(self.style.ERROR(error_message))
