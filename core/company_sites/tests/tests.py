# pylint: disable=consider-iterating-dictionary
import datetime
import os
import subprocess
from statistics import mean

import pytz
from django.core import mail
from django.test import tag
from django.utils import timezone
from freezegun import freeze_time
from mock import patch, Mock, ANY, call, PropertyMock
from django.db.models import Sum

from core.common.constants import INLOAD, OUTLOAD
from core.common.tests import ACTestCase, AuthSetup
from core.timezones.utils import DateTimeUtil
from core.companies.mock_data import COMPANY_VALID_MOCK_DATA
from core.companies.models import Company
from core.companies.tests.factories import CompanyFactory
from core.company_sites.mock_data import COMPANY_SITE_VALID_MOCK_DATA
from core.company_sites.models import CompanySite, FreightSlot, SlotComment, SiteManagementSettings, SlotReport
from core.company_sites.tasks import push_slots_events_to_sftp, read_slot_receipts_from_sftp
from core.company_sites.tests.factories import CompanySiteFactory
from core.contracts.tests.factories import ContractFactory, PartyFactory
from core.countries.models import Country
from core.farms.tests.factories import FarmFactory, StorageFactory
from core.freights.constants import FREIGHT_SLOT_OUTLOAD_VOID, FREIGHT_SLOT_INLOAD_VOID, CUSTOMER_ONLY_TYPE_ID, \
    SELLER_TO_BUYER_TYPE_ID
from core.freights.models import FreightOrder
from core.freights.tests.factories import FreightOrderFactory, FreightMovementFactory, FreightDeliveryFactory, \
    FreightPickupFactory
from core.jobs.models import Job
from core.loads.models import Load
from core.loads.tests.factories import LoadFactory
from core.ngrs.tests.factories import NgrFactory
from core.profiles.mock_data import EMPLOYEE_VALID_MOCK_DATA
from core.profiles.models import Employee, EmployeeType
from core.profiles.tests.factories import EmployeeFactory
from core.trucks.factories import TruckFactory
from core.loads.models import LoadTruckConfiguration
from core.trucks.models import TruckCategory,TruckCheckpointLog
from core.devices.constants import (
    INTERRUPTION_LEVEL_TIME_SENSITIVE
)
from core.commodities.models import Commodity
from core.contracts.models import Contract
from core.company_sites.constants import BOOKED_SINGLE_SLOT_WITH_FP, FLEET_TRUCK_MAIL_SUBJECT


@tag('model')
class CompanySiteTest(ACTestCase):
    def setUp(self):
        super().setUp()
        self.company = Company.get_or_create(COMPANY_VALID_MOCK_DATA)
        EmployeeType.create({'name': 'company_admin', 'id': 1})
        self.employee = Employee.register({
            **EMPLOYEE_VALID_MOCK_DATA,
            'company': {
                'id': self.company.id,
            }
        })

    def test_create_site_success(self):
        _company_site = CompanySite.create_with_location(COMPANY_SITE_VALID_MOCK_DATA, kwargs={
            'company_id': self.company.id,
            'market_zone_id': 1,
            'region_id': 1,
        })
        self.assertIsNotNone(_company_site.id)
        for _key in ['name']:
            self.assertEqual(
                getattr(_company_site, _key),
                COMPANY_SITE_VALID_MOCK_DATA.get(_key)
            )
        self.assertEqual(_company_site.company_id, self.company.id)
        self.assertEqual(_company_site.market_zone_id, 1)
        self.assertEqual(_company_site.region_id, 1)

    def test_create_site_without_region_success(self):
        _company_site = CompanySite.create_with_location(COMPANY_SITE_VALID_MOCK_DATA, kwargs={
            'company_id': self.company.id,
            'market_zone_id': 1,
        })
        self.assertIsNotNone(_company_site.id)
        for _key in ['name']:
            self.assertEqual(
                getattr(_company_site, _key),
                COMPANY_SITE_VALID_MOCK_DATA.get(_key)
            )
        self.assertEqual(_company_site.company_id, self.company.id)
        self.assertEqual(_company_site.market_zone_id, 1)
        self.assertEqual(_company_site.region_id, None)

    def test_create_site_without_company_fail(self):
        _company_site = CompanySite.create_with_location(COMPANY_SITE_VALID_MOCK_DATA, kwargs={
            'market_zone_id': 1,
            'region_id': 1,
        })
        self.assertIsNone(_company_site.id)
        self.assertIsNotNone(_company_site.errors)
        self.assertTrue('company' in _company_site.errors)

    def test_create_site_without_fields_fail(self):
        _company_site = CompanySite.create_with_location({}, kwargs={
            'company_id': self.company.id,
            'market_zone_id': 1,
            'region_id': 1,
        })
        self.assertIsNone(_company_site.id)
        self.assertIsNotNone(_company_site.errors)
        for _key in ['name']:
            self.assertTrue(_key in _company_site.errors)

        self.assertTrue('company' not in _company_site.errors)

    def test_company_address(self):
        company_site = CompanySite(id=2)
        self.assertIsNone(company_site.company_address)
        company_site.company_id = self.company.id
        self.assertEqual(company_site.company_address, self.company.address.to_dict())

    def test_handler(self):
        company_site = CompanySite(id=2)
        self.assertIsNone(company_site.handler)
        company_site.company = Company(id=1)
        self.assertEqual(company_site.handler, Company(id=1))

    def test_update_site_success(self):
        _company_site = CompanySite.create_with_location(COMPANY_SITE_VALID_MOCK_DATA, kwargs={
            'company_id': self.company.id,
            'market_zone_id': 1,
            'region_id': 1,
        })
        _updated_data = {
            'name': 'Site 1 updated',
            'address': {
                'address': 'Sector 54 updated',
                'latitude': 28.4594965,
                'longitude': 77.0266383}
        }
        _company_site = CompanySite.update_with_location(
            _company_site.id, {**_updated_data}, self.employee
        )
        self.assertEqual(_company_site.errors, {})
        self.assertEqual(_company_site.name, _updated_data['name'])
        self.assertEqual(_company_site.address.address, _updated_data['address']['address'])


@tag('model')
class FreightSlotTest(ACTestCase): # pylint: disable=too-many-public-methods
    def test_order_number(self):
        slot = FreightSlot()
        self.assertIsNone(slot.order_number())

        slot.delivery_order_number = '123'
        self.assertEqual(slot.order_number(), '123')

        slot.order = FreightOrder(id=1, identifier='FO123')
        self.assertEqual(slot.order_number(), 'FO123')

        slot.delivery_order_number = '123'
        self.assertEqual(slot.order_number(), 'FO123')

    @patch('core.company_sites.models.FreightSlot.slotcomment_set')
    def test_comments_count(self, comment_set_mock):
        comment_set_mock.count = Mock(return_value=1000)

        self.assertEqual(FreightSlot().comments_count, 1000)

    @patch('core.company_sites.models.FreightSlot.notify_updated')
    @patch('core.company_sites.models.FreightSlot.publish_update_to_ably')
    @patch('core.company_sites.models.FreightSlot.is_updated_by_freight_provider')
    @patch('core.common.models.RawModel.update')
    def test_update(
            self, update_mock, is_updated_by_freight_provider_mock,
            publish_update_to_ably_mock, notify_updated_mock,
    ):
        slot1 = FreightSlot(id=1, site_id=100, type='inload')
        slot2 = FreightSlot(id=1, site_id=200, type='outload', commodity_id=14)
        slot1.refresh_from_db = Mock()
        slot2.refresh_from_db = Mock()
        slot2.is_updated_by_freight_provider = Mock(return_value=True)
        update_mock.return_value = slot2
        is_updated_by_freight_provider_mock.return_value = True

        updated_slot = FreightSlot.update(instance=slot1, data={})

        self.assertEqual(
            updated_slot.provider_updated_fields.sort(),
            ['site_id', 'type', 'commodity_id', 'created_at'].sort()
        )
        update_mock.assert_called_once_with(None, {}, None, slot1)
        notify_updated_mock.assert_called_once_with(slot1.to_dict())
        publish_update_to_ably_mock.assert_called_once_with(slot1.to_dict(), [])

    def test_is_updated_by_freight_provider(self):
        self.assertTrue(
            FreightSlot(
                updated_by=Employee(company_id=1), freight_provider_id=1
            ).is_updated_by_freight_provider()
        )
        self.assertFalse(
            FreightSlot(
                updated_by=Employee(company_id=2), freight_provider_id=1
            ).is_updated_by_freight_provider()
        )
        self.assertFalse(
            FreightSlot(
                updated_by=Employee(company_id=2), freight_provider_id=None
            ).is_updated_by_freight_provider()
        )

    def test_clean(self):
        for status in ['in_progress', 'booked', 'completed', 'delayed']:
            slot = FreightSlot(
                status=status,
                type='inload',
                start='2017-01-01',
                end='2017-01-02',
                booking_number='b1',
                delivery_order_number='o1',
                commodity_id=1,
                grade_id=1,
                site_id=1,
            )

            slot.clean()

            self.assertEqual(
                slot.errors,
                {
                    'freight_provider_id': ['This field cannot be null']
                }
            )

        for status in ['planned', 'cancelled']:
            slot = FreightSlot(
                status=status,
                type='inload',
                start='2017-01-01',
                end='2017-01-02',
                booking_number='b1',
                delivery_order_number='o1',
                commodity_id=1,
                grade_id=1,
                site_id=1,
            )

            slot.clean()

            self.assertEqual(slot.errors, {})

    @patch(
        'core.company_sites.models.SiteManagementSettings.get_default_view_date_range',
        Mock(return_value={})
    )
    @patch('core.company_sites.models.FreightSlot.objects')
    def test_company_slots(self, manager_mock):
        select_mock = Mock()
        select_mock.filter = Mock(return_value='queryset')
        manager_mock.select_related = Mock(return_value=select_mock)

        self.assertEqual(FreightSlot.company_slots(123), 'queryset')
        manager_mock.select_related.assert_called_once_with('site')
        select_mock.filter.assert_called_once_with(site__company_id=123)

    @patch(
        'core.company_sites.models.SiteManagementSettings.get_default_view_date_range',
        Mock(return_value={})
    )
    def test_open_or_company_booked_slots(self):
        company_with_site = CompanyFactory(type_id=4)
        company_with_truck = CompanyFactory(type_id=2)
        driver = EmployeeFactory(company=company_with_truck)
        company_random = CompanyFactory()
        truck = TruckFactory(company=company_with_truck)

        company_site = FarmFactory(company=company_with_site)
        open_slot = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        booked_slot = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': company_site,
                'type': 'inload',
                'status': 'booked',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
                'truck_id': truck.id,
                'driver_id': driver.id,
            }
        )

        self.assertEqual(booked_slot.freight_provider_id, booked_slot.truck.company_id)
        self.assertEqual(FreightSlot.objects.count(), 2)

        slots_for_random_company = FreightSlot.open_or_company_booked_slots(
            company_random.id, None, None, company_with_site.id
        )

        self.assertEqual(slots_for_random_company.count(), 1)
        self.assertEqual(slots_for_random_company.first().id, open_slot.id)

        slots_for_random_company = FreightSlot.open_or_company_booked_slots(
            company_with_truck.id, None, None, company_with_site.id
        )

        self.assertEqual(slots_for_random_company.count(), 2)
        self.assertEqual(
            sorted(list(slots_for_random_company.values_list('id', flat=True))),
            sorted([open_slot.id, booked_slot.id])
        )

        self.assertEqual(
            FreightSlot.open_or_company_booked_slots(
                company_with_truck.id, None, None, company_random.id
            ).count(),
            0
        )

    def test_to_sse_event(self):
        self.maxDiff = None
        company_with_site = CompanyFactory(type_id=4)
        SiteManagementSettings(company=company_with_site, minimum_tonnage=0.1).save()
        company_site = FarmFactory(company=company_with_site)
        slot = FreightSlot(
            site=company_site, commodity_id=1, grade_id=2, created_at='2022-01-01 10:00:00',
            start='2022-01-01 10:00:00',
            end='2022-01-01 10:30:00'
        )
        slot.save()
        self.assertEqual(
            slot.to_sse_event(),
            '{\n "bookingNumber": null,\n "bothSitesOrderBookingOn": false,\n "commentsCount": 0,\n "commodity": {\n  "displayName": "Wheat",\n  "id": 1,\n  "name": "wheat",\n  "unit": "MT"\n },\n "commodityContractId": null,\n "commodityContractNumber": null,\n "commodityId": 1,\n "counterSlotId": null,\n "customer": null,\n "deliveryOrderNumber": null,\n "driver": null,\n "driverId": null,\n "end": "2022-01-01T10:30:00Z",\n "excludedCommodityIds": null,\n "excludedGradeIds": null,\n "freightProvider": null,\n "freightProviderId": null,\n "grade": {\n  "commodityId": 1,\n  "id": 2,\n  "name": "AH14"\n },\n "gradeId": 2,\n "hasParentFreightProvider": false,\n "hasSiblings": false,\n "hoursBeforeCancellationStops": 0,\n "id": '+ str(slot.id) +',\n "isDeliverySite": false,\n "isProvider": false,\n "isSubProvider": false,\n "isTrailerSlot": false,\n "isUrgentForManager": false,\n "isUrgentForProvider": false,\n "movementId": null,\n "movementIdentifier": null,\n "movementStatus": null,\n "onRoute": false,\n "orderId": null,\n "parentGradeName": "",\n "parentSlotId": null,\n "pits": null,\n "priority": false,\n "providerUpdatedFields": [],\n "restrictSlotCancellation": false,\n "restrictedVisibleToCarrier": false,\n "restrictionReason": null,\n "season": null,\n "settingsUpdatedAt": '+ str(datetime.datetime.timestamp(slot.settings.updated_at)) +',\n "siteId": '+str(company_site.id)+',\n "start": "2022-01-01T10:00:00Z",\n "status": "planned",\n "subFreightProvider": null,\n "subFreightProviderId": null,\n "tonnage": null,\n "trailerSlotIds": [],\n "truck": null,\n "truckId": null,\n "type": null,\n "updatedAt": ' + str(datetime.datetime.timestamp(slot.updated_at)) + ',\n "varietyName": null\n}'  # pylint: disable=line-too-long
        )

    @patch('core.company_sites.models.ABLY')
    def test_publish_new_slots_to_ably(self, ABLY_mock):
        ABLY_mock.publish = Mock()

        slot = FreightSlot(freight_provider_id=400, created_at='2019-01-01', id=100,)
        slot.site = CompanySite(company_id=200)
        slot.publish_new_slots_to_ably()

        self.assertEqual(
            ABLY_mock.publish.mock_calls[0],
            call(
                'freight-slots-new',
                '200',
                [100],
            )
        )

        self.assertEqual(
            ABLY_mock.publish.mock_calls[1],
            call(
                'freight-slots-new',
                '400',
                [100],
            )
        )

    @patch('core.company_sites.models.ABLY')
    def test_publish_update_to_ably(self, ABLY_mock):
        ABLY_mock.publish = Mock()

        slot = FreightSlot(freight_provider_id=400, created_at='2019-01-01', id=100,)
        slot.site = CompanySite(company_id=200)
        slot.publish_update_to_ably()

        self.assertEqual(
            ABLY_mock.publish.mock_calls[0],
            call(
                'freight-slots-update',
                '200',
                slot.to_sse_event()
            )
        )

        self.assertEqual(
            ABLY_mock.publish.mock_calls[1],
            call(
                'freight-slots-update',
                '400',
                slot.to_sse_event()
            )
        )

    @patch('core.company_sites.models.ABLY')
    def test_publish_update_to_ably_with_fp_changed(self, ABLY_mock):
        ABLY_mock.publish = Mock()

        old_slot = FreightSlot(freight_provider_id=500, created_at='2019-01-01', id=100,)
        slot = FreightSlot(freight_provider_id=400, created_at='2019-01-01', id=100,)
        slot.site = CompanySite(company_id=200)
        old_slot.site = CompanySite(company_id=200)
        slot.publish_update_to_ably(old_slot.to_dict())

        self.assertEqual(
            ABLY_mock.publish.mock_calls[0],
            call(
                'freight-slots-update',
                '500',
                slot.to_sse_event()
            )
        )

        self.assertEqual(
            ABLY_mock.publish.mock_calls[1],
            call(
                'freight-slots-update',
                '200',
                slot.to_sse_event()
            )
        )

        self.assertEqual(
            ABLY_mock.publish.mock_calls[2],
            call(
                'freight-slots-update',
                '400',
                slot.to_sse_event()
            )
        )

    @patch('core.company_sites.models.ABLY')
    def test_publish_delete_to_ably(self, ABLY_mock):
        ABLY_mock.publish = Mock()

        slot = FreightSlot(id=100, freight_provider_id=400)
        slot.site = CompanySite(company_id=200)
        slot.publish_delete_to_ably()

        self.assertEqual(
            ABLY_mock.publish.mock_calls[0],
            call(
                'freight-slots-delete',
                '200',
                [100],
            )
        )

        self.assertEqual(
            ABLY_mock.publish.mock_calls[1],
            call(
                'freight-slots-delete',
                '400',
                [100],
            )
        )

    @patch('core.company_sites.models.ABLY')
    def test_publish_delete_multiple_to_ably(self, ABLY_mock):
        ABLY_mock.publish = Mock()

        slot = FreightSlot(id=100, freight_provider_id=400)
        slot.site = CompanySite(company_id=200)
        slot.publish_delete_to_ably([100, 1000])

        self.assertEqual(
            ABLY_mock.publish.mock_calls[0],
            call(
                'freight-slots-delete',
                '200',
                [100, 1000]
            )
        )

        self.assertEqual(
            ABLY_mock.publish.mock_calls[1],
            call(
                'freight-slots-delete',
                '400',
                [100, 1000]
            )
        )

    def test_forward_siblings(self):
        slot = FreightSlot(id=123)
        self.assertEqual(slot.forward_siblings, [123])
        slot.siblings = [123]
        self.assertEqual(slot.forward_siblings, [123])
        slot.siblings = [121, 122, 123]
        self.assertEqual(slot.forward_siblings, [123])
        slot.siblings = [121, 122, 123, 124, 125, 128]
        self.assertEqual(slot.forward_siblings, [123, 124, 125, 128])
        slot.siblings = [121, 122, 124, 125, 128]
        self.assertEqual(slot.forward_siblings, [123])

    @patch('core.company_sites.models.FreightSlot.notify_created')
    @patch('core.company_sites.models.FreightSlot.objects.filter')
    @patch('core.company_sites.models.FreightSlot.objects.bulk_create')
    def test_persist_many(self, bulk_create_mock, filter_mock, notify_created_mock):
        filter_mock.return_value = Mock(update=Mock(return_value=None))
        expected_slot1 = FreightSlot(id=1, start=datetime.datetime(2019, 1, 2))
        expected_slot2 = FreightSlot(id=2, start=datetime.datetime(2019, 1, 26))
        expected_slot1.publish_new_slots_to_ably = Mock()
        bulk_create_mock.return_value = [expected_slot1, expected_slot2]
        payload = [
            {'site_id': 1, 'start': '26-01-2019'},
            {'site_id': 1, 'start': '02-01-2019'},
            {'start': '01-01'}
        ]

        slots = FreightSlot.persist_many(123, payload, False, True)

        self.assertEqual(len(slots), 2)
        expected_slot1.publish_new_slots_to_ably.assert_called_once()
        notify_created_mock.assert_called_once_with([expected_slot1, expected_slot2])
        filter_mock.assert_called_once_with(id__in=[1, 2])
        filter_mock().update.assert_called_once_with(siblings=[1, 2])

    @patch('core.company_sites.models.SlotComment.objects.bulk_create')
    @patch('core.company_sites.models.FreightSlot.notify_created')
    @patch('core.company_sites.models.FreightSlot.objects.filter')
    @patch('core.company_sites.models.FreightSlot.objects.bulk_create')
    def test_persist_many_with_comments(
            self, bulk_create_mock, filter_mock, notify_created_mock, comment_bulk_create_mock,
    ):
        filter_mock.return_value = Mock(update=Mock(return_value=None))
        expected_slot1 = FreightSlot(id=1, start=datetime.datetime(2019, 1, 2))
        expected_slot2 = FreightSlot(id=2, start=datetime.datetime(2019, 1, 26))
        expected_slot1.publish_new_slots_to_ably = Mock()
        bulk_create_mock.return_value = [expected_slot1, expected_slot2]
        payload = [
            {'site_id': 1, 'start': '26-01-2019', 'comment': 'First Comment'},
            {'site_id': 1, 'start': '02-01-2019'},
        ]

        slots = FreightSlot.persist_many(123, payload, False, True)

        self.assertEqual(len(slots), 2)
        expected_slot1.publish_new_slots_to_ably.assert_called_once()
        notify_created_mock.assert_called_once_with([expected_slot1, expected_slot2])
        comment_bulk_create_mock.assert_called_once_with(
            [
                SlotComment(slot=expected_slot1, comment='First Comment'),
                SlotComment(slot=expected_slot2, comment='First Comment'),
            ]
        )
        filter_mock.assert_called_once_with(id__in=[1, 2])
        filter_mock().update.assert_called_once_with(siblings=[1, 2])

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.WebPush.send_many')
    @patch('core.company_sites.models.notify')
    def test_notify_created_for_no_provider(self, notify_mock, send_many_mock, notify_mobile_devices_mock):
        notify_mock.send = Mock()
        slot = FreightSlot(id=1)
        slot.notify_created([slot])
        notify_mock.send.assert_not_called()
        send_many_mock.assert_not_called()
        notify_mobile_devices_mock.assert_not_called()

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.WebPush.send_many')
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_planned_created_for_no_provider_employees(
            self, notify_mock, provider_employees_mock, send_many_mock, notify_mobile_devices_mock
    ):
        notify_mock.send = Mock()
        provider_employees_mock.return_value = Employee.objects.none()
        slot = FreightSlot(
            id=1,
            freight_provider_id=100,
            status='planned',
            start=datetime.datetime(2011, 1, 1),
            end=datetime.datetime(2011, 1, 2)
        )
        slot.notify_created([slot])
        notify_mock.send.assert_not_called()
        send_many_mock.assert_not_called()
        notify_mobile_devices_mock.assert_not_called()

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_created_booked_for_recurring_with_provider_employees(
            self, notify_mock, provider_employees_mock, notify_mobile_devices_mock
    ):
        notify_mock.send = Mock()
        provider_employees = Employee.objects.filter(id=1)
        provider_employees_mock.return_value = provider_employees
        slot1 = FreightSlot(
            id=1,
            freight_provider_id=100,
            status='booked',
            start=datetime.datetime(2011, 1, 1, 10, 10),
            end=datetime.datetime(2011, 1, 2, 10, 20),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        slot2 = FreightSlot(
            id=2,
            freight_provider_id=100,
            status='booked',
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        slot1.notify_created([slot1, slot2])
        notification_msg = '<b>Admin Root (SR000)</b> from <b>AgriChain</b> has booked multiple slots for <b>VIC</b> ' \
                           'site from <b>start_date start_time</b> to <b>end_date end_time</b>'
        notify_mock.send.assert_called_once_with(
            sender=slot1.updated_by,
            verb='Booked',
            description=notification_msg,
            app='site_bookings',
            entity='freight_slot',
            action_object=slot1,
            start=str(slot1.start),
            end=str(slot2.end),
            recipient=ANY,
            site_id=123,
            company_id=456,
        )
        self.assertEqual(list(notify_mock.send.call_args[1]['recipient'].values_list('id', flat=True)), [1])

        mobile_msg = notification_msg.replace('<b>', '').replace('</b>', '')
        mobile_msg = mobile_msg.replace('start_date', '01/01/2011').replace('start_time', '09:10 PM')
        mobile_msg = mobile_msg.replace('end_date', '03/02/2011').replace('end_time', '09:10 PM')
        notify_mobile_devices_mock.assert_called_once_with(
            ANY,
            {
                'notification_type': 5,
                'message_txt': mobile_msg,
                'ios_msg_title': 'Slot Notification',
                'extra': {'ac_notification_id': slot1.id, 'ac_notification_type': 'slot',
                          'movement_id': slot1.movement_id}
            },
            interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
        )
        self.assertEqual(list(notify_mock.send.call_args[1]['recipient'].values_list('id', flat=True)), [1])

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.WebPush.send_many')
    @patch('core.company_sites.models.FreightSlot.site_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_updated_by_bookie_with_no_site_employees(
            self, notify_mock, provider_employees_mock, site_employees_mock, send_many_mock, notify_mobile_devices_mock
    ):
        notify_mock.send = Mock()
        site_employees_mock.return_value = Mock(exists=Mock(return_value=False), values_list=Mock(return_value=[]))
        provider_employees_mock.return_value = Mock(exists=Mock(return_value=False), values_list=Mock(return_value=[]))
        slot = FreightSlot(
            id=1,
            freight_provider_id=100,
            status='planned',
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        slot.updated_by.company_id = 100
        slot.save()

        slot.notify_updated(slot.to_dict())

        notify_mock.send.assert_not_called()
        send_many_mock.assert_not_called()
        notify_mobile_devices_mock.assert_not_called()

    @patch('core.profiles.models.Employee.objects.filter')
    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.FreightSlot.site_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_booked_by_bookie(
            self, notify_mock, provider_employees_mock, site_employees_mock, notify_mobile_devices_mock,
            employee_filter_mock
    ):
        notify_mock.send = Mock()
        site_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1]))
        site_employees.__iter__ = Mock(return_value=iter([]))
        site_employees_mock.return_value = site_employees
        provider_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[100]))
        provider_employees.__iter__ = Mock(return_value=iter([]))
        provider_employees_mock.return_value = provider_employees

        expected_recipients = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1, 100]))
        expected_recipients.__iter__ = Mock(return_value=iter([Mock(id=1), Mock(id=100)]))
        employee_filter_mock.return_value = expected_recipients

        before_update_slot = FreightSlot(
            id=1,
            status='planned',
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        after_update_slot = FreightSlot(
            id=1,
            freight_provider_id=100,
            status='booked',
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        after_update_slot.is_updated_by_freight_provider = Mock(return_value=True)

        after_update_slot.notify_updated(before_update_slot.to_dict())

        notification_msg = '<b>Admin Root (SR000)</b> from <b>AgriChain</b> has booked the slot for <b>VIC</b> ' \
                           'site on <b>start_date</b> at <b>start_time</b>'
        notify_mock.send.assert_called_once_with(
            sender=after_update_slot.updated_by,
            verb='Booked',
            description=notification_msg,
            app='site_management',
            entity='freight_slot',
            action_object=after_update_slot,
            start=str(after_update_slot.start),
            end=str(after_update_slot.end),
            recipient=expected_recipients,
            site_id=123,
            company_id=456,
        )
        mobile_msg = notification_msg.replace('<b>', '').replace('</b>', '')
        mobile_msg = mobile_msg.replace('start_date', '02/02/2011').replace('start_time', '09:10 PM')
        notify_mobile_devices_mock.assert_called_once_with(
            [1, 100],
            {
                'notification_type': 5,
                'message_txt': mobile_msg,
                'ios_msg_title': 'Slot Notification',
                'extra': {'ac_notification_id': after_update_slot.id, 'ac_notification_type': 'slot',
                          'movement_id': after_update_slot.movement_id}
            },
            interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
        )

    @patch('core.profiles.models.Employee.objects.filter')
    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.FreightSlot.site_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_cancelled_booking_by_bookie(
            self, notify_mock, provider_employees_mock, site_employees_mock, notify_mobile_devices_mock,
            employee_filter_mock
    ):
        notify_mock.send = Mock()
        site_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1]))
        site_employees.__iter__ = Mock(return_value=iter([]))
        site_employees_mock.return_value = site_employees
        provider_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[100]))
        provider_employees.__iter__ = Mock(return_value=iter([]))
        provider_employees_mock.return_value = site_employees

        expected_recipients = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1, 100]))
        expected_recipients.__iter__ = Mock(return_value=iter([Mock(id=1), Mock(id=100)]))
        employee_filter_mock.return_value = expected_recipients

        before_update_slot = FreightSlot(
            id=1,
            status='booked',
            freight_provider_id=100,
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        after_update_slot = FreightSlot(
            id=1,
            freight_provider_id=None,
            status='planned',
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        after_update_slot.is_updated_by_freight_provider = Mock(return_value=True)

        after_update_slot.notify_updated(before_update_slot.to_dict())

        notification_msg = '<b>Admin Root (SR000)</b> from <b>AgriChain</b> has cancelled their booking for the slot ' \
                           'for <b>VIC</b> site on <b>start_date</b> at <b>start_time</b>'
        notify_mock.send.assert_called_once_with(
            sender=after_update_slot.updated_by,
            verb='Cancelled',
            description=notification_msg,
            app='site_management',
            entity='freight_slot',
            action_object=after_update_slot,
            start=str(after_update_slot.start),
            end=str(after_update_slot.end),
            recipient=expected_recipients,
            site_id=123,
            company_id=456,
        )
        mobile_msg = notification_msg.replace('<b>', '').replace('</b>', '')
        mobile_msg = mobile_msg.replace('start_date', '02/02/2011').replace('start_time', '09:10 PM')
        notify_mobile_devices_mock.assert_called_once_with(
            [1, 100],
            {
                'notification_type': 5,
                'message_txt': mobile_msg,
                'ios_msg_title': 'Slot Notification',
                'extra': {'ac_notification_id': after_update_slot.id, 'ac_notification_type': 'slot',
                          'movement_id': after_update_slot.movement_id}
            },
            interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
        )

    @patch('core.profiles.models.Employee.objects.filter')
    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.FreightSlot.site_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_updated_slot_by_bookie(
            self, notify_mock, provider_employees_mock, site_employees_mock, notify_mobile_devices_mock,
            employee_filter_mock
    ):
        notify_mock.send = Mock()
        site_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1]))
        site_employees.__iter__ = Mock(return_value=iter([]))
        site_employees_mock.return_value = site_employees
        provider_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[100]))
        provider_employees.__iter__ = Mock(return_value=iter([]))
        provider_employees_mock.return_value = site_employees

        expected_recipients = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1, 100]))
        expected_recipients.__iter__ = Mock(return_value=iter([Mock(id=1), Mock(id=100)]))
        employee_filter_mock.return_value = expected_recipients

        before_update_slot = FreightSlot(
            id=1,
            status='booked',
            freight_provider_id=100,
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
            truck_id=None,
        )
        after_update_slot = FreightSlot(
            id=1,
            freight_provider_id=100,
            status='booked',
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
            truck_id=1,
        )
        after_update_slot.is_updated_by_freight_provider = Mock(return_value=True)

        after_update_slot.notify_updated(before_update_slot.to_dict())

        notification_msg = '<b>Admin Root (SR000)</b> from <b>AgriChain</b> has updated their booking details for ' \
                           'the slot for <b>VIC</b> site on <b>start_date</b> at <b>start_time</b>'
        notify_mock.send.assert_called_once_with(
            sender=after_update_slot.updated_by,
            verb='Changed',
            description=notification_msg,
            app='site_management',
            entity='freight_slot',
            action_object=after_update_slot,
            start=str(after_update_slot.start),
            end=str(after_update_slot.end),
            recipient=expected_recipients,
            site_id=123,
            company_id=456,
        )
        mobile_msg = notification_msg.replace('<b>', '').replace('</b>', '')
        mobile_msg = mobile_msg.replace('start_date', '02/02/2011').replace('start_time', '09:10 PM')
        notify_mobile_devices_mock.assert_called_once_with(
            [1, 100],
            {
                'notification_type': 5,
                'message_txt': mobile_msg,
                'ios_msg_title': 'Slot Notification',
                'extra': {'ac_notification_id': after_update_slot.id, 'ac_notification_type': 'slot',
                          'movement_id': after_update_slot.movement_id}
            },
            interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
        )

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.WebPush.send_many')
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_updated_by_operator_no_provider_employees(
            self, notify_mock, provider_employees_mock, send_many_mock, notify_mobile_devices_mock
    ):
        notify_mock.send = Mock()
        provider_employees_mock.return_value = Employee.objects.none()
        before_update_slot = FreightSlot(
            id=1,
            status='booked',
            freight_provider_id=100,
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
            truck_id=None,
        )
        after_update_slot = FreightSlot(
            id=1,
            freight_provider_id=100,
            status='booked',
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
            truck_id=1,
        )
        after_update_slot.is_updated_by_freight_provider = Mock(return_value=False)

        after_update_slot.notify_updated(before_update_slot.to_dict())

        notify_mock.send.assert_not_called()
        send_many_mock.assert_not_called()
        notify_mobile_devices_mock.assert_not_called()

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_booked_by_operator(
            self, notify_mock, provider_employees_mock, notify_mobile_devices_mock
    ):
        notify_mock.send = Mock()
        provider_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1]))
        provider_employees.__iter__ = Mock(return_value=iter([]))
        provider_employees_mock.return_value = provider_employees
        before_update_slot = FreightSlot(
            id=1,
            status='planned',
            freight_provider_id=100,
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
            truck_id=None,
        )
        after_update_slot = FreightSlot(
            id=1,
            freight_provider_id=100,
            status='booked',
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
            truck_id=1,
        )
        after_update_slot.is_updated_by_freight_provider = Mock(return_value=False)

        after_update_slot.notify_updated(before_update_slot.to_dict())

        notification_msg = '<b>Admin Root (SR000)</b> from <b>AgriChain</b> has booked a slot for you for <b>VIC</b> ' \
                           'site on <b>start_date</b> at <b>start_time</b>'
        notify_mock.send.assert_called_once_with(
            sender=after_update_slot.updated_by,
            verb='Booked',
            description=notification_msg,
            app='site_bookings',
            entity='freight_slot',
            action_object=after_update_slot,
            start=str(after_update_slot.start),
            end=str(after_update_slot.end),
            recipient=provider_employees,
            site_id=123,
            company_id=456,
        )
        mobile_msg = notification_msg.replace('<b>', '').replace('</b>', '')
        mobile_msg = mobile_msg.replace('start_date', '02/02/2011').replace('start_time', '09:10 PM')
        notify_mobile_devices_mock.assert_called_once_with(
            [1],
            {
                'notification_type': 5,
                'message_txt': mobile_msg,
                'ios_msg_title': 'Slot Notification',
                'extra': {'ac_notification_id': after_update_slot.id, 'ac_notification_type': 'slot',
                          'movement_id': after_update_slot.movement_id}
            },
            interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
        )

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_cancelled_booking_by_operator(
            self, notify_mock, provider_employees_mock, notify_mobile_devices_mock
    ):
        notify_mock.send = Mock()
        provider_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1]))
        provider_employees.__iter__ = Mock(return_value=iter([]))
        provider_employees_mock.return_value = provider_employees
        before_update_slot = FreightSlot(
            id=1,
            status='booked',
            freight_provider_id=100,
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        after_update_slot = FreightSlot(
            id=1,
            freight_provider_id=100,
            status='planned',
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        after_update_slot.is_updated_by_freight_provider = Mock(return_value=False)

        after_update_slot.notify_updated(before_update_slot.to_dict())

        notification_msg = '<b>Admin Root (SR000)</b> from <b>AgriChain</b> has cancelled your booking for ' \
                           '<b>VIC</b> site on <b>start_date</b> at <b>start_time</b>'
        notify_mock.send.assert_called_once_with(
            sender=after_update_slot.updated_by,
            verb='Cancelled',
            description=notification_msg,
            app='site_bookings',
            entity='freight_slot',
            action_object=after_update_slot,
            start=str(after_update_slot.start),
            end=str(after_update_slot.end),
            recipient=provider_employees,
            site_id=123,
            company_id=456,
        )
        mobile_msg = notification_msg.replace('<b>', '').replace('</b>', '')
        mobile_msg = mobile_msg.replace('start_date', '02/02/2011').replace('start_time', '09:10 PM')
        notify_mobile_devices_mock.assert_called_once_with(
            [1],
            {
                'notification_type': 5,
                'message_txt': mobile_msg,
                'ios_msg_title': 'Slot Notification',
                'extra': {'ac_notification_id': after_update_slot.id, 'ac_notification_type': 'slot',
                          'movement_id': after_update_slot.movement_id}
            },
            interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
        )

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_status_change_by_operator(
            self, notify_mock, provider_employees_mock, notify_mobile_devices_mock
    ):
        notify_mock.send = Mock()
        provider_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1]))
        provider_employees.__iter__ = Mock(return_value=iter([]))
        provider_employees_mock.return_value = provider_employees
        company = Company(id=456)
        company.sitemanagementsettings = SiteManagementSettings()
        before_update_slot = FreightSlot(
            id=1,
            status='booked',
            freight_provider_id=100,
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company=company),
        )
        after_update_slot = FreightSlot(
            id=1,
            freight_provider_id=100,
            status='in_progress',
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company=company),
        )
        after_update_slot.is_updated_by_freight_provider = Mock(return_value=False)

        after_update_slot.notify_updated(before_update_slot.to_dict())

        notification_msg = '<b>Admin Root (SR000)</b> from <b>AgriChain</b> has marked your slot for <b>VIC</b> ' \
                           'site on <b>start_date</b> at <b>start_time</b> as <b>In Progress</b>'
        notify_mock.send.assert_called_once_with(
            sender=after_update_slot.updated_by,
            verb='Changed',
            description=notification_msg,
            app='site_bookings',
            entity='freight_slot',
            action_object=after_update_slot,
            start=str(after_update_slot.start),
            end=str(after_update_slot.end),
            recipient=provider_employees,
            site_id=123,
            company_id=456,
        )
        mobile_msg = notification_msg.replace('<b>', '').replace('</b>', '')
        mobile_msg = mobile_msg.replace('start_date', '02/02/2011').replace('start_time', '09:10 PM')
        notify_mobile_devices_mock.assert_called_once_with(
            [1],
            {
                'notification_type': 5,
                'message_txt': mobile_msg,
                'ios_msg_title': 'Slot Notification',
                'extra': {'ac_notification_id': after_update_slot.id, 'ac_notification_type': 'slot',
                          'movement_id': after_update_slot.movement_id}
            },
            interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
        )

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_update_by_operator(
            self, notify_mock, provider_employees_mock, notify_mobile_devices_mock
    ):
        notify_mock.send = Mock()
        provider_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1]))
        provider_employees.__iter__ = Mock(return_value=iter([]))
        provider_employees_mock.return_value = provider_employees
        before_update_slot = FreightSlot(
            id=1,
            status='booked',
            freight_provider_id=100,
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        after_update_slot = FreightSlot(
            id=1,
            freight_provider_id=100,
            status='booked',
            start=datetime.datetime(2011, 2, 2, 10, 20),
            end=datetime.datetime(2011, 2, 3, 10, 30),
            site=CompanySite(id=123, name='VIC', company_id=456),
        )
        after_update_slot.is_updated_by_freight_provider = Mock(return_value=False)

        after_update_slot.notify_updated(before_update_slot.to_dict())

        notification_msg = '<b>Admin Root (SR000)</b> from <b>AgriChain</b> has updated details of your slot for ' \
                           '<b>VIC</b> site on <b>start_date</b> at <b>start_time</b>'
        notify_mock.send.assert_called_once_with(
            sender=after_update_slot.updated_by,
            verb='Changed',
            description=notification_msg,
            app='site_bookings',
            entity='freight_slot',
            action_object=after_update_slot,
            recipient=provider_employees,
            start=str(after_update_slot.start),
            end=str(after_update_slot.end),
            site_id=123,
            company_id=456,
        )
        mobile_msg = notification_msg.replace('<b>', '').replace('</b>', '')
        mobile_msg = mobile_msg.replace('start_date', '02/02/2011').replace('start_time', '09:20 PM')
        notify_mobile_devices_mock.assert_called_once_with(
            [1],
            {
                'notification_type': 5,
                'message_txt': mobile_msg,
                'ios_msg_title': 'Slot Notification',
                'extra': {'ac_notification_id': after_update_slot.id, 'ac_notification_type': 'slot',
                          'movement_id': after_update_slot.movement_id}
            },
            interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
        )

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.FreightSlot.provider_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_notify_provider_change_by_operator(
            self, notify_mock, provider_employees_mock, notify_mobile_devices_mock
    ):
        notify_mock.send = Mock()
        provider1_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1]))
        provider1_employees.__iter__ = Mock(return_value=iter([]))
        provider2_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1]))
        provider2_employees.__iter__ = Mock(return_value=iter([]))
        provider_employees_mock.side_effect = [provider1_employees, provider2_employees]
        before_update_slot = FreightSlot(
            id=1,
            status='booked',
            freight_provider_id=100,
            start=datetime.datetime(2011, 2, 2, 10, 10),
            end=datetime.datetime(2011, 2, 3, 10, 10),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        after_update_slot = FreightSlot(
            id=1,
            freight_provider_id=101,
            status='booked',
            start=datetime.datetime(2011, 2, 2, 10, 20),
            end=datetime.datetime(2011, 2, 3, 10, 30),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        after_update_slot.is_updated_by_freight_provider = Mock(return_value=False)

        after_update_slot.notify_updated(before_update_slot.to_dict())

        self.assertEqual(notify_mock.send.call_count, 2)
        self.assertEqual(notify_mobile_devices_mock.call_count, 2)
        self.assertEqual(
            notify_mock.send.call_args_list[0],
            call(
                sender=after_update_slot.updated_by,
                verb='Cancelled',
                description='<b>Admin Root (SR000)</b> from <b>AgriChain</b> has cancelled your booking for <b>VIC</b> site on <b>start_date</b> at <b>start_time</b>', # pylint: disable=line-too-long
                app='site_bookings',
                entity='freight_slot',
                action_object=after_update_slot,
                start=str(after_update_slot.start),
                end=str(after_update_slot.end),
                recipient=provider1_employees,
                site_id=123,
                company_id=456,
            )
        )
        self.assertEqual(
            notify_mock.send.call_args_list[1],
            call(
                sender=after_update_slot.updated_by,
                verb='Booked',
                description='<b>Admin Root (SR000)</b> from <b>AgriChain</b> has booked a slot for you for <b>VIC</b> site on <b>start_date</b> at <b>start_time</b>', # pylint: disable=line-too-long
                app='site_bookings',
                entity='freight_slot',
                action_object=after_update_slot,
                start=str(after_update_slot.start),
                end=str(after_update_slot.end),
                recipient=provider2_employees,
                site_id=123,
                company_id=456,
            )
        )

    def test_is_provider(self):
        user_mock = Mock(company_id=100)

        self.assertFalse(FreightSlot().is_provider(None))
        self.assertFalse(FreightSlot().is_provider(user_mock))
        self.assertFalse(FreightSlot(freight_provider_id=200).is_provider(user_mock))
        self.assertTrue(FreightSlot(freight_provider_id=100).is_provider(user_mock))

    def test_is_sub_provider(self):
        user_mock = Mock(company_id=100)

        self.assertFalse(FreightSlot().is_sub_provider(None))
        self.assertFalse(FreightSlot().is_sub_provider(user_mock))
        self.assertFalse(FreightSlot(freight_provider_id=100).is_sub_provider(user_mock))
        self.assertTrue(
            FreightSlot(
                freight_provider_id=200, sub_freight_provider_id=100
            ).is_sub_provider(user_mock)
        )

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.WebPush.send_many')
    @patch('core.company_sites.models.notify')
    def test_commented_by_operator_with_no_provider(self, notify_mock, send_many_mock, notify_mobile_devices_mock):
        notify_mock.send = Mock()
        FreightSlot()
        notify_mock.send.assert_not_called()
        send_many_mock.assert_not_called()
        notify_mobile_devices_mock.assert_not_called()

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.WebPush.send_many')
    @patch('core.company_sites.models.FreightSlot.site_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_commented_by_provider_with_no_site_employees(
            self, notify_mock, site_employees_mock, send_many_mock, notify_mobile_devices_mock
    ):
        notify_mock.send = Mock()
        site_employees_mock.return_value = None
        slot = FreightSlot()
        slot.notify_commented_by_provider()
        notify_mock.send.assert_not_called()
        send_many_mock.assert_not_called()
        notify_mobile_devices_mock.assert_not_called()

    @patch('core.company_sites.models.MobilePushNotification.notify_mobile_devices')
    @patch('core.company_sites.models.FreightSlot.site_employees', new_callable=PropertyMock)
    @patch('core.company_sites.models.notify')
    def test_commented_by_provider_with_site_employees(
            self, notify_mock, site_employees_mock, notify_mobile_devices_mock
    ):
        notify_mock.send = Mock()
        site_employees = Mock(exists=Mock(return_value=True), values_list=Mock(return_value=[1]))
        site_employees.__iter__ = Mock(return_value=iter([]))
        site_employees_mock.return_value = site_employees
        slot = FreightSlot(
            freight_provider_id=100,
            site_id=123,
            start=datetime.datetime(2011, 2, 2, 10, 20),
            end=datetime.datetime(2011, 2, 3, 10, 30),
            site=CompanySite(name='VIC', id=123, company_id=456),
        )
        slot.notify_commented_by_provider()
        notification_msg = '<b>Admin Root (SR000)</b> from <b>AgriChain</b> has added a comment against the slot of ' \
                           '<b>VIC</b> site on <b>start_date</b> at <b>start_time</b>'
        notify_mock.send.assert_called_once_with(
            sender=slot.updated_by,
            verb='Commented',
            description=notification_msg,
            app='site_management',
            action_object=slot,
            recipient=site_employees,
            entity='freight_slot',
            start=str(slot.start),
            end=str(slot.end),
            site_id=slot.site_id,
            company_id=456,
        )
        mobile_msg = notification_msg.replace('<b>', '').replace('</b>', '')
        mobile_msg = mobile_msg.replace('start_date', '02/02/2011').replace('start_time', '09:20 PM')
        notify_mobile_devices_mock.assert_called_once_with(
            [1],
            {
                'notification_type': 5,
                'message_txt': mobile_msg,
                'ios_msg_title': 'Slot Notification',
                'extra': {'ac_notification_id': slot.id, 'ac_notification_type': 'slot',
                          'movement_id': slot.movement_id}
            },
            interruption_level=None
        )

    @patch('core.company_sites.models.FreightSlot.settings', new_callable=PropertyMock)
    def test_localize_report_data(self, settings_mock):
        settings_mock.return_value = SiteManagementSettings()
        slot = FreightSlot(
            commodity_id=1,
            grade_id=1,
            freight_provider_id=100,
            site_id=123,
            freight_provider=Company(id=100, business_name="Provider"),
            start=datetime.datetime(2011, 1, 1, 1, 0, tzinfo=pytz.UTC),
            end=datetime.datetime(2011, 1, 1, 2, 0, tzinfo=pytz.UTC),
            site=CompanySite(name='VIC', id=123, company_id=456, company=Company(id=456)),
            created_at=datetime.datetime(2011, 10, 10, 10, 10, tzinfo=pytz.UTC),
            updated_at=datetime.datetime(2012, 10, 10, 10, 10, tzinfo=pytz.UTC),
            tonnage=100,
            customer='Customer',
            delivery_order_number='DELORDERNO',
            booking_number='BNNo',
            pits='Pits',
        )

        self.assertEqual(
            slot.localized_report_data('Asia/Calcutta'),
            [
                'VIC',
                '',
                None,
                'Planned',
                'No',
                '01/01/2011',
                '06:30 AM',
                None,
                '07:30 AM',
                None,
                None,
                None,
                None,
                None,
                None,
                'Wheat',
                'APH1',
                None,
                100,
                'Customer',
                None,
                'DELORDERNO',
                None,
                'BNNo',
                'Pits',
                'Provider',
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                '10/10/2011 03:40 PM',
                'Admin Root (SR000)',
                '10/10/2012 03:40 PM',
                'Admin Root (SR000)'
            ]
        )

        self.assertEqual(
            slot.localized_report_data('Asia/Calcutta', True),
            [
                'VIC',
                '',
                None,
                'No',
                '01/01/2011',
                '06:30 AM',
                None,
                '07:30 AM',
                None,
                None,
                None,
                None,
                None,
                None,
                'Wheat',
                'APH1',
                100,
                'Customer',
                None,
                'DELORDERNO',
                None,
                'BNNo',
                'Pits',
                'Provider',
                None,
                None,
                None,
                None,
                None,
                None,
                None,
                '10/10/2011 03:40 PM',
                'Admin Root (SR000)',
                '10/10/2012 03:40 PM',
                'Admin Root (SR000)'
            ]
        )

    def test_to_localized_report_data(self):
        slot = FreightSlot()
        slot.localized_report_data = Mock(return_value=['foo', 'bar'])
        self.assertEqual(
            FreightSlot.to_localized_report_data([slot], 'Asia/Calcutta'),
            [['foo', 'bar']]
        )
        slot.localized_report_data.assert_called_once_with('Asia/Calcutta')

    def test_cancel_booking_for_trailer_slot(self):
        site = CompanySiteFactory()
        order = FreightOrderFactory()
        movement = FreightMovementFactory(order=order)
        slot = FreightSlot(
            tonnage=10, site=site, movement=movement, order=order,
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now()
        )

        slot.cancel_booking_for_trailer_slot()

        self.assertEqual(slot.tonnage, None)
        self.assertEqual(slot.movement_id, None)
        self.assertEqual(slot.order_id, None)

    def test_inferred_tonnage_unit_with_value(self):
        freight_slot = FreightSlot() # no commodity
        freight_slot_1 = FreightSlot(commodity=Commodity.objects.get(name='wheat'))
        freight_slot_2 = FreightSlot(commodity=Commodity.objects.get(name='Organic Popcorn'))

        self.assertEqual(freight_slot.inferred_tonnage_unit, "MT") # default
        self.assertEqual(freight_slot_1.inferred_tonnage_unit, "MT")
        self.assertEqual(freight_slot_2.inferred_tonnage_unit, "LB")

    @patch('core.company_sites.models.FreightSlot.booked_by', new_callable=PropertyMock)
    def test_send_fleet_truck_booking_mail_no_booked_by(self, mock_booked_by):
        user = EmployeeFactory(email='<EMAIL>')
        mock_booked_by.return_value = user
        start = timezone.now()
        end = start + timezone.timedelta(days=1)
        freight_slot = FreightSlot(start=start, end=end, site=FarmFactory())

        freight_slot.send_fleet_truck_booking_mail()
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, FLEET_TRUCK_MAIL_SUBJECT)
        self.assertEqual(sent_mail.recipients(), [user.email])

    def test_order_delivery_stats_without_site_ids(self): # pylint: disable=too-many-locals
        company = CompanyFactory()
        site = FarmFactory(company=company)
        start = timezone.now() - timezone.timedelta(days=10)
        end = start + timezone.timedelta(days=1)
        slot = FreightSlot(site=site, start=start, end=end)
        slot.save()
        customer = PartyFactory(role='Customer', company=company)
        freight_pickup = FreightPickupFactory(date_time=start)
        freight_pickup_2 = FreightPickupFactory(date_time=start)
        freight_delivery = FreightDeliveryFactory(date_time=end)
        freight_delivery_2 = FreightDeliveryFactory(date_time=end)
        order = FreightOrderFactory(
            type_id=SELLER_TO_BUYER_TYPE_ID, seller=customer, freight_pickup=freight_pickup,
            freight_delivery=freight_delivery
        )
        customer_order = FreightOrderFactory(
            type_id=CUSTOMER_ONLY_TYPE_ID, customer=customer, freight_pickup=freight_pickup_2,
            freight_delivery=freight_delivery_2
        )
        seller_stats, customer_stats, orders = FreightSlot.order_delivery_stats(company_id=company.id)
        self.assertEqual(set(orders.values_list('id', flat=True)), {customer_order.id, order.id})
        self.assertIsNotNone(customer_stats)
        self.assertIsNotNone(seller_stats)
        self.assertTrue(customer.company_id in customer_stats)
        self.assertTrue(customer.company_id in seller_stats)

    @patch('core.company_sites.models.FreightSlot.objects.filter')
    def test_get_booking_cancelled_slots_stats(self, mock_slot_filter):
        mock_queryset = Mock()
        mock_order_queryset = Mock()

        mock_order_queryset.values_list.return_value = [1, 2, 3]

        mock_slot1 = Mock()
        mock_slot1.id = 101
        mock_slot1.history.all.return_value = [
            Mock(status='booked', order_id=1, tonnage=10, next_record=Mock(status='planned'))
        ]

        mock_slot2 = Mock()
        mock_slot2.id = 102
        mock_slot2.history.all.return_value = [
            Mock(status='booked', order_id=2, tonnage=20, next_record=Mock(status='planned'))
        ]

        mock_slot3 = Mock()
        mock_slot3.id = 103
        mock_slot3.history.all.return_value = [
            Mock(status='booked', order_id=3, tonnage=30, next_record=Mock(status='cancelled'))
        ]

        mock_queryset.exclude.return_value = [mock_slot1, mock_slot2, mock_slot3]


        planned_status_slots = {101: True, 102: True, 103: False}

        mock_slot_filter.side_effect = lambda *args, **kwargs: Mock(
            exists=lambda: planned_status_slots.get(kwargs.get('id'), False)
        )

        never_rebooked, _ = FreightSlot.get_booking_cancelled_slots_stats(mock_queryset, mock_order_queryset)

        self.assertEqual(never_rebooked['count'], 2)
        self.assertEqual(never_rebooked['tonnage'], 30)

    @patch('core.common.pdfgenerator.ERRBIT_LOGGER.log')
    @patch('core.companies.models.Company.update_slot_information')
    @patch('core.companies.models.Company.external_booking_connection', new_callable=PropertyMock)
    def test_update_external_booking(self, mock_external_booking_connection, mock_update_slot_information, mock_log):
        mock_external_booking_connection.return_value = True
        expected_response = {'valid_connection': True, 'errors': None}
        mock_update_slot_information.return_value = expected_response
        company = CompanyFactory()
        site = FarmFactory(company=company)
        start = timezone.now() - timezone.timedelta(days=10)
        end = start + timezone.timedelta(days=1)
        slot = FreightSlot(site=site, start=start, end=end, movement=FreightMovementFactory())
        slot.save()
        result = slot.update_external_booking()

        self.assertIsNone(result)
        mock_log.assert_called_once_with(f'Failed to update None for external booking {slot.movement.identifier}.')

    @patch('core.companies.models.Company.update_slot_information')
    @patch('core.companies.models.Company.external_booking_connection', new_callable=PropertyMock)
    def test_update_external_booking_with_errors(self, mock_external_booking_connection, mock_update_slot_information):
        mock_external_booking_connection.return_value = True
        expected_response = {'valid_connection': True, 'errors': ['test'], 'booking_number': 'B1234'}
        mock_update_slot_information.return_value = expected_response
        company = CompanyFactory()
        site = FarmFactory(company=company)
        start = timezone.now() - timezone.timedelta(days=10)
        end = start + timezone.timedelta(days=1)
        slot = FreightSlot(site=site, start=start, end=end, movement=FreightMovementFactory())
        slot.save()
        result = slot.update_external_booking()
        self.assertEqual(result, ['test'])

    @patch('core.company_sites.models.FreightSlot.draft_load_exists_on_booking', new_callable=PropertyMock)
    def test_cannot_cancel_reasons_with_draft_load(self, mock_draft_load_exists_on_booking):
        mock_draft_load_exists_on_booking.return_value = True
        slot = FreightSlot()

        reasons = slot.cannot_cancel_reasons()

        self.assertEqual(reasons, ['Draft load exists on booking.'])
    @patch('core.company_sites.models.FreightSlot.draft_load_exists_on_booking', new_callable=PropertyMock)
    def test_cannot_cancel_reasons_without_draft_load(self, mock_draft_load_exists_on_booking):
        mock_draft_load_exists_on_booking.return_value = False
        slot = FreightSlot()

        reasons = slot.cannot_cancel_reasons()

        self.assertEqual(reasons, [])

    @patch('core.company_sites.models.ABLY.publish')
    @patch('core.company_sites.models.FreightSlot.sub_freight_provider_id', new_callable=PropertyMock)
    def test_publish_to_ably_for_sub_provider_with_id(self, mock_sub_provider_id ,mock_ably_publish):
        mock_sub_provider_id.return_value = 123
        slot = FreightSlot()

        channel = 'test_channel'
        data = {'test_key': 'test_value'}

        slot._FreightSlot__publish_to_ably_for_sub_provider(channel, data)

        mock_ably_publish.assert_called_once_with(channel, '123', data)

    def test_get_count_and_tonnage_with_list(self):
        slot1 = Mock(tonnage=10)
        slot2 = Mock(tonnage=20)
        slot3 = Mock(tonnage=None)

        queryset = [slot1, slot2, slot3]

        count, total_tonnage = FreightSlot.get_count_and_tonnage(queryset)

        self.assertEqual(count, 3)
        self.assertEqual(total_tonnage, 30)

    def test_get_count_and_tonnage_with_queryset(self):
        mock_queryset = Mock()
        mock_queryset.count.return_value = 5
        mock_queryset.aggregate.return_value = {'total_tonnage': 150}

        count, total_tonnage = FreightSlot.get_count_and_tonnage(mock_queryset)


        self.assertEqual(count, 5)
        self.assertEqual(total_tonnage, 150)
        mock_queryset.count.assert_called_once()
        mock_queryset.aggregate.assert_called_once_with(total_tonnage=Sum('tonnage'))

    def test_to_cancelled_count_with_queryset(self):
        mock_queryset = Mock()
        mock_queryset.exclude.return_value = [Mock(), Mock()]
        slot1_history = Mock()
        slot1_history.order_by.return_value = [
            Mock(status='booked', freight_provider_id=1, next_record=Mock(status='planned')),
            Mock(status='planned')
        ]

        slot2_history = Mock()
        slot2_history.order_by.return_value = [
            Mock(status='booked', freight_provider_id=2, next_record=Mock(status='planned')),
            Mock(status='planned')
        ]

        mock_queryset.exclude.return_value[0].history = slot1_history
        mock_queryset.exclude.return_value[1].history = slot2_history


        provider_ids = [1, 2]
        count = FreightSlot.to_cancelled_count(mock_queryset, provider_ids)
        self.assertEqual(count, 2)
        count = FreightSlot.to_cancelled_count(mock_queryset, [])
        self.assertEqual(count, 2)

        count = FreightSlot.to_cancelled_count(mock_queryset, [99])
        self.assertEqual(count, 0)

        mock_queryset.exclude.assert_called_with(status='restricted')
        slot1_history.order_by.assert_called_with('history_date')
        slot2_history.order_by.assert_called_with('history_date')

    def test_to_cancelled_booking_report_with_queryset(self):
        mock_queryset = Mock()
        mock_queryset.exclude.return_value = [Mock(), Mock()]

        slot1_history = Mock()
        slot1_history.order_by.return_value = [
            Mock(
                status='booked',
                next_record=Mock(status='planned'),
                instance=Mock(localized_report_data=Mock(return_value={'report': 'data1'}))
            )
        ]

        slot2_history = Mock()
        slot2_history.order_by.return_value = [
            Mock(
                status='booked',
                next_record=Mock(status='planned'),
                instance=Mock(localized_report_data=Mock(return_value={'report': 'data2'}))
            )
        ]

        mock_queryset.exclude.return_value[0].history = slot1_history
        mock_queryset.exclude.return_value[1].history = slot2_history

        result = FreightSlot.to_cancelled_booking_report(mock_queryset, "UTC")

        self.assertEqual(len(result), 2)
        self.assertEqual(result[0], {'report': 'data1'})
        self.assertEqual(result[1], {'report': 'data2'})
        mock_queryset.exclude.assert_called_once_with(status='restricted')
        slot1_history.order_by.assert_called_once_with('history_date')
        slot2_history.order_by.assert_called_once_with('history_date')
        slot1_history.order_by.return_value[0].instance.localized_report_data.assert_called_once_with("UTC", True)
        slot2_history.order_by.return_value[0].instance.localized_report_data.assert_called_once_with("UTC", True)

    @patch('core.company_sites.models.FreightSlot.inferred_tonnage', new_callable=Mock)
    def test_update_load_tonnage_with_different_tonnage(self, mock_inferred_tonnage):
        freight_slot = FreightSlot(id=1, tonnage=100, _entered_tonnage=90)
        mock_inferred_tonnage.return_value = 120

        mock_load = Mock()
        mock_load.net_weight = 110

        freight_slot._FreightSlot__update_load_tonnage(mock_load)


        self.assertEqual(mock_load.estimated_net_weight, 100)
        self.assertEqual(mock_load._entered_estimated_net_weight, 90)
        self.assertIsNone(mock_load.gross_weight)
        self.assertIsNone(mock_load.tare_weight)
        mock_load.save.assert_called_once()

    @patch('core.company_sites.models.FreightSlot.should_update_load')
    @patch('core.company_sites.models.FreightSlot.load', new_callable=PropertyMock)
    def test_update_load(self, mock_load, mock_should_update_load):

        freight_slot = FreightSlot()

        mock_should_update_load.return_value = True
        mock_load.freight_provider_id = 200
        mock_load.truck_id = 300
        mock_load.driver_id = 400
        mock_load.save = Mock()

        freight_slot.update_load(last_version={})

        mock_should_update_load.assert_called_once()
        self.assertEqual(mock_load.freight_provider_id, 200)
        self.assertEqual(mock_load.truck_id, 300)
        self.assertEqual(mock_load.driver_id, 400)

    @patch('core.company_sites.models.FreightSlot.history')
    def test_loading_time_with_valid_data(self, mock_history):
        freight_slot = FreightSlot()
        freight_slot.is_completed = Mock(return_value=True)
        mock_in_progress = Mock()
        mock_in_progress.status = 'in_progress'
        mock_in_progress.history_date = datetime.datetime(2025, 3, 17, 10, 0, 0)
        mock_next_record = Mock()
        mock_next_record.history_date = datetime.datetime(2025, 3, 17, 11, 30, 0)
        mock_in_progress.next_record = mock_next_record
        mock_history.filter.return_value.order_by.return_value.first.return_value = mock_in_progress

        loading_time = freight_slot.loading_time()

        self.assertIsNotNone(loading_time)
        self.assertEqual(loading_time, 5400)
        freight_slot.is_completed.assert_called_once()
        mock_history.filter.assert_called_once_with(status='in_progress')
        mock_history.filter().order_by.assert_called_once_with('-history_date')

    @patch('core.company_sites.models.FreightSlot.history')
    def test_loading_time_with_exception(self, mock_history):
        freight_slot = FreightSlot()
        freight_slot.is_completed = Mock(return_value=True)

        mock_history.filter.side_effect = Exception("Unexpected error")

        loading_time = freight_slot.loading_time()

        self.assertIsNone(loading_time)
        freight_slot.is_completed.assert_called_once()
        mock_history.filter.assert_called_once_with(status='in_progress')

    def test_movement_load_time_with_valid_data(self):
        company = CompanyFactory()
        site = FarmFactory(company=company)
        start = timezone.now() - timezone.timedelta(days=10)
        end = start + timezone.timedelta(days=1)
        movement = FreightMovementFactory(status='delivered')
        inload = LoadFactory(movement=movement, type=INLOAD, date_time=start)
        outload = LoadFactory(movement=movement, type=OUTLOAD, date_time=end)
        slot = FreightSlot(
            site=site, start=start, end=end, freight_provider=company, movement=movement
        )
        slot.save()

        load_time = slot.movement_load_time()
        load_time_diff = inload.date_time - outload.date_time
        self.assertIsNotNone(load_time)
        self.assertEqual(load_time, load_time_diff.total_seconds())

    def test_name_with_all_attributes(self):
        company = CompanyFactory()
        site = FarmFactory(company=company)
        start = timezone.now() - timezone.timedelta(days=10)
        end = start + timezone.timedelta(days=1)
        truck = TruckFactory()
        commodity_id = Commodity.objects.filter(name='wheat').first().id
        slot = FreightSlot(
            site=site, start=start, end=end, truck=truck, freight_provider=company, commodity_id=commodity_id
        )
        slot.save()
        formatted_start = start.astimezone(pytz.UTC).strftime('%d/%m/%Y %I:%M %p')
        formatted_time = start.astimezone(pytz.UTC).strftime('%I:%M %p')

        self.assertEqual(slot.name(tz="UTC", include_date=True), f'{formatted_start} - {formatted_time} - Wheat')
        self.assertEqual(slot.name(tz="UTC", include_date=False), f'{formatted_time} - {formatted_time} - Wheat')

    def test_slot_category_planned(self):
        freight_slot = FreightSlot()
        freight_slot.status = 'planned'
        result = freight_slot.slot_category()

        self.assertIsNone(result)

    def test_slot_category_external_outload(self):
        freight_slot = FreightSlot()
        freight_slot.status = 'booked'
        freight_slot.type = 'outload'
        order = FreightOrder()
        order.commodity_contract = Contract()
        freight_slot.order = order

        result = freight_slot.slot_category()

        self.assertEqual(result, 'External Outload')

    def test_slot_category_internal_outload(self):
        freight_slot = FreightSlot()
        freight_slot.status = 'completed'
        freight_slot.type = 'outload'

        result = freight_slot.slot_category()

        self.assertEqual(result, 'Internal Outload')

    def test_slot_category_external_inload(self):
        freight_slot = FreightSlot()
        freight_slot.status = 'completed'
        freight_slot.type = 'inload'

        order = FreightOrder()
        order.commodity_contract = Contract()
        freight_slot.order = order

        result = freight_slot.slot_category()

        self.assertEqual(result, 'External Inload')

    def test_slot_category_internal_inload(self):
        freight_slot = FreightSlot()
        freight_slot.status = 'booked'
        freight_slot.type = 'inload'

        result = freight_slot.slot_category()
        self.assertEqual(result, 'Internal Inload')

    @patch.object(FreightSlot, '_FreightSlot__notify')
    def test_notify_single_created_when_booked(self, mock_notify):
        freight_slot = FreightSlot()
        recipients = ['<EMAIL>', '<EMAIL>']
        freight_slot.is_booked = Mock(return_value=True)
        freight_slot._FreightSlot__notify_single_created(recipients)
        freight_slot.is_booked.assert_called_once()
        mock_notify.assert_called_once_with(
            BOOKED_SINGLE_SLOT_WITH_FP,
            'Booked',
            'site_bookings',
            recipients,
            interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
        )

    def test_get_filter_params_with_start_and_end(self):
        start = datetime.datetime(2025, 3, 10, 10, 0, 0)
        end = datetime.datetime(2025, 3, 15, 18, 0, 0)

        filter_params = FreightSlot.get_filter_params(start=start, end=end)

        expected_params = {
            'start__gte': start,
            'end__lt': end
        }

        self.assertEqual(filter_params, expected_params)

    @patch('core.company_sites.models.is_super_admin', new_callable=PropertyMock, return_value=True)
    def test_open_or_company_booked_slots_as_super_admin(self, mock_is_super_admin):
        company = CompanyFactory()
        site = FarmFactory(company=company)
        start = timezone.now() - timezone.timedelta(days=10)
        end = start + timezone.timedelta(days=1)
        filter_end = start + timezone.timedelta(days=2)
        truck = TruckFactory()
        slot = FreightSlot(site=site, start=start, end=end, truck=truck, freight_provider=company)
        slot.save()
        result_queryset = FreightSlot.open_or_company_booked_slots(
            company.id, start, filter_end, site.company_id
        )
        self.assertEqual(result_queryset.first().id, slot.id)

        mock_is_super_admin.return_value = False
        result_queryset = FreightSlot.open_or_company_booked_slots(
            company.id, start, filter_end, site.company_id, site.id, truck.rego, True
        )
        self.assertEqual(result_queryset.first().id, slot.id)


@tag('model')
class FreightSlotIntegrationTest(ACTestCase):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        self.maxDiff = None
        FreightSlot.history.all().delete()
        super().setUp()

        self.manager = EmployeeFactory(first_name='Site', last_name='Wala')
        SiteManagementSettings(company=self.manager.company, order_booking=True).save()
        self.site = CompanySiteFactory(company=self.manager.company)
        self.created_at = '2019-01-01 07:00:00+00'
        self.data = {
            'comment': 'slot comment', 'commodity_id': 1, 'type': 'inload',
            'start': '2019-01-01 10:00:00+00', 'end': '2019-01-01 11:00:00+00',
            'created_at': self.created_at, 'created_by_id': self.manager.id
        }
        self.tz = 'Asia/Calcutta'
        self.datetime_format = Country.get_country_format("datetime_with_seconds")
        self.format_date = lambda x: DateTimeUtil.localize_date(x, self.tz, self.datetime_format)
        self.provider = CompanyFactory(business_name='Truck Company')
        self.truck = TruckFactory(company=self.provider)
        self.driver = EmployeeFactory(first_name='Truck', last_name='Wala')
        self.order = FreightOrderFactory(provider=self.provider)

    # Created in planned (with start/end/commodity) ->
    # booked by FP with order/grade/tonnage/driver/truck ->
    # order no changed ->
    # manually marked-completed (load-created)
    def test_created_planned_to_completed_with_order_changed(self):  # pylint: disable=too-many-statements,too-many-branches
        with freeze_time(self.created_at):
            slots = FreightSlot.persist_many(self.site.id, self.data, True, True)

        self.assertEqual(len(slots), 1)

        slot = slots[0]
        self.assertEqual(slot.tonnage, None)
        expected_history = {
            '01/01/2019 12:30:00 PM': {
                'heading': 'Created by Site Wala',
                'items': [
                    {'Start': {'new': '01/01/2019 03:30:00 PM'}},
                    {'End': {'new': '01/01/2019 04:30:00 PM'}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:15:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'order_id': self.order.id, 'delivery_order_number': self.order.identifier,
                    'freight_provider_id': self.truck.company_id, 'truck_id': self.truck.id,
                    'driver_id': self.driver.id, 'tonnage': 10, 'grade_id': self.order.planned_grade_id,
                    'status': 'booked', 'updated_by_id': self.driver.id
                }
            )
        slot.refresh_from_db()

        self.assertEqual(slot.tonnage, 10)
        self.assertEqual(slot.order_id, self.order.id)
        self.assertIsNotNone(slot.movement_id)
        self.assertTrue(slot.is_booked())
        movement = slot.movement
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Truck Wala',
                'heading': "Slot Booked by Truck Wala",
                'items': [
                    {'Tonnage': {'new': '10.0 MT'}},
                    {'Grade': {'new': slot.grade.name}},
                    {'Truck': {'new': self.truck.rego}},
                    {'Driver': {'new': self.driver.name}},
                    {'Freight Provider': {'new': 'Truck Company'}},
                    {'Order': {'new': self.order.identifier}},
                    {'Movement': {'new': movement.identifier}},
                    {'Customer': {'new': movement.customer.company.name}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:30:00+00"):
            new_order = FreightOrderFactory(provider=self.provider, planned_grade=slot.grade, commodity=slot.commodity)
            FreightSlot.update(
                instance=slot,
                data={
                    'order_id': new_order.id, 'updated_by_id': self.manager.id,
                }
            )
        slot.refresh_from_db()

        self.assertTrue(slot.is_booked())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Site Wala',
                'heading': "Booking cancelled by Site Wala and Slot updated by Site Wala",
                'items': [
                    {'Order': {'new': new_order.identifier, 'old': self.order.identifier}},
                    {'Movement': {'new': slot.movement.identifier, 'old': movement.identifier}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:45:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'updated_by_id': self.manager.id, 'status': 'completed',
                }
            )
        slot.refresh_from_db()

        self.assertTrue(slot.is_completed())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Site Wala',
                'heading': "Slot marked Completed by Site Wala",
                'items': [
                    # items in real would be empty, this is because of time freeze, comparing with prev to prev history
                    {'Movement': {'new': slot.movement.identifier}},
                    {'Order': {'new': new_order.identifier}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

    # Created in booked (with start/end/order/fp/truck/driver/commodity) ->
    # some info changed by FP  -> manually-marked-completed with some info changed (inload-created)
    def test_booked_created_to_completed_with_order_booking(self):
        with freeze_time(self.created_at):
            slots = FreightSlot.persist_many(
                self.site.id,
                {
                    **self.data,
                    'order_id': self.order.id, 'delivery_order_number': self.order.identifier,
                    'freight_provider_id': self.truck.company_id, 'truck_id': self.truck.id,
                    'driver_id': self.driver.id, 'tonnage': 10, 'grade_id': self.order.planned_grade_id,
                    'status': 'booked', 'commodity_id': 1
                },
                return_slots=True
            )

        self.assertEqual(len(slots), 1)
        slot = slots[0]
        self.assertTrue(slot.is_booked())
        expected_history = {
            '01/01/2019 12:30:00 PM': {
                'heading': 'Created by Site Wala in Booked',
                'items': [
                    {'Start': {'new': '01/01/2019 03:30:00 PM'}},
                    {'End': {'new': '01/01/2019 04:30:00 PM'}},
                    {'Movement': {'new': slot.movement.identifier}},
                    {'Order No': {'new': self.order.identifier}},
                    {'Tonnage': {'new': '10.0 MT'}},
                    {'Grade': {'new': slot.grade.name}},
                    {'Commodity': {'new': slot.commodity.display_name}},
                    {'Freight Provider': {'new': 'Truck Company'}},
                    {'Truck': {'new': self.truck.rego}},
                    {'Driver': {'new': self.driver.name}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:15:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'updated_by_id': self.driver.id, 'tonnage': 12,
                }
            )
        slot.refresh_from_db()

        self.assertTrue(slot.is_booked())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Truck Wala',
                'heading': "Slot updated by Truck Wala",
                'items': [
                    {'Tonnage': {'new': '12.0 MT', 'old': '10.0 MT'}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:30:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'updated_by_id': self.manager.id, 'status': 'completed', 'tonnage': 20,
                }
            )
        slot.refresh_from_db()

        self.assertTrue(slot.is_completed())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Site Wala',
                'heading': "Slot marked Completed by Site Wala",
                'items': [
                    {'Tonnage': {'new': '20.0 MT', 'old': '12.0 MT'}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

    # Created in planned (with start/end) -> booked by FP with order/grade/tonnage/driver/truck
    # -> in-progress -> some info changed  -> completed (added-load)
    def test_planned_created_to_completed_with_order_booking(self):  # pylint: disable=too-many-statements,too-many-branches
        with freeze_time(self.created_at):
            slots = FreightSlot.persist_many(self.site.id, self.data, True, True)

        self.assertEqual(len(slots), 1)

        slot = slots[0]
        self.assertEqual(slot.tonnage, None)
        expected_history = {
            '01/01/2019 12:30:00 PM': {
                'heading': 'Created by Site Wala',
                'items': [
                    {'Start': {'new': '01/01/2019 03:30:00 PM'}},
                    {'End': {'new': '01/01/2019 04:30:00 PM'}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:15:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'order_id': self.order.id, 'delivery_order_number': self.order.identifier,
                    'freight_provider_id': self.truck.company_id, 'truck_id': self.truck.id,
                    'tonnage': 10, 'grade_id': self.order.planned_grade_id, 'status': 'booked',
                    'updated_by_id': self.driver.id, 'driver_id': self.driver.id,
                }
            )
        slot.refresh_from_db()

        self.assertEqual(slot.tonnage, 10)
        self.assertEqual(slot.order_id, self.order.id)
        self.assertIsNotNone(slot.movement_id)
        self.assertTrue(slot.is_booked())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Truck Wala',
                'heading': "Slot Booked by Truck Wala",
                'items': [
                    {'Tonnage': {'new': '10.0 MT'}},
                    {'Grade': {'new': slot.grade.name}},
                    {'Truck': {'new': self.truck.rego}},
                    {'Driver': {'new': self.driver.name}},
                    {'Freight Provider': {'new': 'Truck Company'}},
                    {'Order': {'new': self.order.identifier}},
                    {'Customer': {'new': slot.movement.customer.company.name}},
                    {'Movement': {'new': slot.movement.identifier}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:30:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'status': 'in_progress', 'updated_by_id': self.manager.id,
                    'tonnage': 14,
                }
            )
        slot.refresh_from_db()

        self.assertEqual(slot.tonnage, 14)
        self.assertTrue(slot.is_in_progress())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Site Wala',
                'heading': "Slot marked In Progress by Site Wala",
                'items': [
                    {'Tonnage': {'new': '14.0 MT', 'old': '10.0 MT'}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:45:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'start': '2019-01-01 10:15:00+00', 'end': '2019-01-01 11:15:00+00',
                    'updated_by_id': self.manager.id,
                }
            )
        slot.refresh_from_db()

        self.assertTrue(slot.is_in_progress())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Site Wala',
                'heading': "Slot updated by Site Wala",
                'items': [
                    {'Start': {'new': '01/01/2019 03:45:00 PM', 'old': '01/01/2019 03:30:00 PM'}},
                    {'End': {'new': '01/01/2019 04:45:00 PM', 'old': '01/01/2019 04:30:00 PM'}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 08:00:00+00"):
            load = Load(
                checkpoint=slot.movement.freight_delivery, type='inload', date_time='2019-01-01 12:00:00',
                created_by=self.manager, updated_by=self.manager, movement=slot.movement,
            )
            load.save()

        slot.refresh_from_db()

        self.assertTrue(slot.is_completed())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Site Wala',
                'heading': "Slot marked Completed by Site Wala",
                'items': [{'Truck': {'new': 'None', 'old': self.truck.rego}}]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

    # Created in planned (with start/end) -> booked by FP with order/grade/tonnage/driver/truck
    # -> in-progress -> some info changed  -> completed (added-load) -> reverted to booked
    def test_planned_created_to_completed_with_order_booking_reverted_to_booked(self):  # pylint: disable=too-many-statements,too-many-branches
        with freeze_time(self.created_at):
            slots = FreightSlot.persist_many(self.site.id, self.data, True, True)

        self.assertEqual(len(slots), 1)

        slot = slots[0]
        self.assertEqual(slot.tonnage, None)
        expected_history = {
            '01/01/2019 12:30:00 PM': {
                'heading': 'Created by Site Wala',
                'items': [
                    {'Start': {'new': '01/01/2019 03:30:00 PM'}},
                    {'End': {'new': '01/01/2019 04:30:00 PM'}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:15:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'order_id': self.order.id, 'delivery_order_number': self.order.identifier,
                    'freight_provider_id': self.truck.company_id, 'truck_id': self.truck.id,
                    'tonnage': 10, 'grade_id': self.order.planned_grade_id, 'status': 'booked',
                    'updated_by_id': self.driver.id, 'driver_id': self.driver.id,
                }
            )
        slot.refresh_from_db()

        self.assertEqual(slot.tonnage, 10)
        self.assertEqual(slot.order_id, self.order.id)
        self.assertIsNotNone(slot.movement_id)
        self.assertTrue(slot.is_booked())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Truck Wala',
                'heading': "Slot Booked by Truck Wala",
                'items': [
                    {'Tonnage': {'new': '10.0 MT'}},
                    {'Grade': {'new': slot.grade.name}},
                    {'Truck': {'new': self.truck.rego}},
                    {'Driver': {'new': self.driver.name}},
                    {'Freight Provider': {'new': 'Truck Company'}},
                    {'Order': {'new': self.order.identifier}},
                    {'Customer': {'new': slot.movement.customer.company.name}},
                    {'Movement': {'new': slot.movement.identifier}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 08:00:00+00"):
            load = Load(
                checkpoint=slot.movement.freight_delivery, type='inload', date_time='2019-01-01 12:00:00',
                created_by=self.driver, updated_by=self.driver, movement=slot.movement
            )
            load.save()

        slot.refresh_from_db()

        self.assertTrue(slot.is_completed())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Truck Wala',
                'heading': "Slot marked Completed by Truck Wala",
                'items': [{'Truck': {'new': 'None', 'old': self.truck.rego}}]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 08:15:00+00"):
            FreightSlot.update(instance=slot, data={'status': 'booked', 'updated_by_id': 1})

        slot.refresh_from_db()

        self.assertTrue(slot.is_booked())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Admin Root (SR000)',
                'heading': "Slot reverted to Booked by Admin Root (SR000)",
                'items': []
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

    # Created in planned (with start/end) -> booked by FP with order/grade/tonnage/driver/truck ->
    # cancelled booking by SM -> Rebooked ->
    # some info changed  -> completed (added-load)
    def test_planned_created_to_completed_with_rebooking(self):  # pylint: disable=too-many-statements,too-many-branches
        with freeze_time(self.created_at):
            slots = FreightSlot.persist_many(self.site.id, self.data, True, True)

        self.assertEqual(len(slots), 1)

        slot = slots[0]
        self.assertEqual(slot.tonnage, None)
        self.assertEqual(slot.commodity_id, 1)
        expected_history = {
            '01/01/2019 12:30:00 PM': {
                'heading': 'Created by Site Wala',
                'items': [
                    {'Start': {'new': '01/01/2019 03:30:00 PM'}},
                    {'End': {'new': '01/01/2019 04:30:00 PM'}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:15:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'order_id': self.order.id, 'delivery_order_number': self.order.identifier,
                    'freight_provider_id': self.truck.company_id, 'truck_id': self.truck.id,
                    'tonnage': 10, 'grade_id': self.order.planned_grade_id, 'status': 'booked',
                    'updated_by_id': self.driver.id, 'driver_id': self.driver.id,
                }
            )
        slot.refresh_from_db()

        self.assertEqual(slot.tonnage, 10)
        self.assertEqual(slot.order_id, self.order.id)
        self.assertIsNotNone(slot.movement_id)
        self.assertTrue(slot.is_booked())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Truck Wala',
                'heading': "Slot Booked by Truck Wala",
                'items': [
                    {'Tonnage': {'new': '10.0 MT'}},
                    {'Grade': {'new': slot.grade.name}},
                    {'Truck': {'new': self.truck.rego}},
                    {'Driver': {'new': self.driver.name}},
                    {'Freight Provider': {'new': 'Truck Company'}},
                    {'Order': {'new': self.order.identifier}},
                    {'Customer': {'new': slot.movement.customer.company.name}},
                    {'Movement': {'new': slot.movement.identifier}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:20:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'order_id': None, 'delivery_order_number': None,
                    'freight_provider_id': None, 'truck_id': None, 'driver_id': None,
                    'status': 'planned', 'updated_by_id': self.manager.id
                }
            )

        slot.refresh_from_db()

        self.assertIsNone(slot.order_id)
        self.assertIsNone(slot.movement_id)
        self.assertEqual(slot.commodity_id, 1)
        self.assertTrue(slot.is_planned())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Site Wala',
                'heading': "Booking cancelled by Site Wala",
                'items': []
            }
        }

        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 07:30:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'order_id': self.order.id, 'delivery_order_number': self.order.identifier,
                    'freight_provider_id': self.truck.company_id, 'truck_id': self.truck.id,
                    'tonnage': 15, 'grade_id': self.order.planned_grade_id, 'status': 'booked',
                    'updated_by_id': self.driver.id, 'driver_id': self.driver.id
                }
            )
        slot.refresh_from_db()

        self.assertEqual(slot.tonnage, 15)
        self.assertEqual(slot.order_id, self.order.id)
        self.assertIsNotNone(slot.movement_id)
        self.assertTrue(slot.is_booked())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Truck Wala',
                'heading': "Slot Booked by Truck Wala",
                'items': [
                    {'Tonnage': {'new': '15.0 MT'}},
                    {'Grade': {'new': slot.grade.name}},
                    {'Truck': {'new': self.truck.rego}},
                    {'Driver': {'new': self.driver.name}},
                    {'Freight Provider': {'new': 'Truck Company'}},
                    {'Order': {'new': self.order.identifier}},
                    {'Customer': {'new': slot.movement.customer.company.name}},
                    {'Movement': {'new': slot.movement.identifier}},
                ]
            }
        }

        with freeze_time("2019-01-01 07:45:00+00"):
            FreightSlot.update(
                instance=slot,
                data={
                    'start': '2019-01-01 10:15:00+00', 'end': '2019-01-01 11:15:00+00',
                    'updated_by_id': self.manager.id,
                }
            )
        slot.refresh_from_db()

        self.assertTrue(slot.is_booked())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Site Wala',
                'heading': "Slot updated by Site Wala",
                'items': [
                    {'Start': {'new': '01/01/2019 03:45:00 PM', 'old': '01/01/2019 03:30:00 PM'}},
                    {'End': {'new': '01/01/2019 04:45:00 PM', 'old': '01/01/2019 04:30:00 PM'}},
                ]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)

        with freeze_time("2019-01-01 08:00:00+00"):
            load = Load(
                checkpoint=slot.movement.freight_delivery, type='inload', date_time='2019-01-01 12:00:00',
                created_by=self.manager, updated_by=self.manager, movement=slot.movement
            )
            load.save()

        slot.refresh_from_db()

        self.assertTrue(slot.is_completed())
        expected_history = {
            **expected_history,
            self.format_date(slot.updated_at): {
                'by': 'Site Wala',
                'heading': "Slot marked Completed by Site Wala",
                'items': [{'Truck': {'new': 'None', 'old': self.truck.rego}}]
            }
        }
        history = slot.history_changeset(self.tz, self.datetime_format)
        self.assertEqual(history.keys(), expected_history.keys())
        for key, value in history.items():
            self.assertEqual(value['heading'], expected_history[key]['heading'])
            if 'by' in expected_history[key]:
                self.assertEqual(value['by'], expected_history[key]['by'])
            history_items = {}
            for item in value['items']:
                history_items = {**history_items, **item}
            expected_history_items = {}
            for item in expected_history[key]['items']:
                expected_history_items = {**expected_history_items, **item}

            self.assertEqual(history_items, expected_history_items)


@tag('view')
class SiteFreightSlotsViewTest(AuthSetup):
    @patch('core.company_sites.views.FreightSlotSerializer')
    @patch('core.company_sites.views.FreightSlot')
    def test_get_for_requesting_user(self, freight_slot_klass_mock, serializer_mock):
        freight_slot_klass_mock.company_slots = Mock(
            return_value=Mock(
                select_related=Mock(
                    return_value=Mock(prefetch_related=Mock(return_value=['slot1', 'slot2']))
                )
            )
        )
        expected_slots_response = [{'id': 'slot1'}, {'id': 'slot2'}]
        serializer_mock.return_value = Mock(data=expected_slots_response)

        response = self.client.get(
            '/company_sites/slots/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, expected_slots_response)
        freight_slot_klass_mock.company_slots.assert_called_once_with(
            self.company.id, None, None, None, None, None, False, []
        )
        serializer_mock.assert_called_once_with(['slot1', 'slot2'], context={'request': ANY}, many=True)

    def test_get_slots_with_filters(self):
        truck = TruckFactory(rego="Truck1", company=self.company)
        site = FarmFactory(id=456)
        order = FreightOrderFactory()
        slot = FreightSlot(status='booked', type='outload', start='2017-01-01', end='2017-01-02', site_id=site.id,
                           order_id=order.id, truck=truck,freight_provider_id=self.company.id)
        slot_1 = FreightSlot(status='planned', type='outload', start='2017-01-01', end='2017-01-02',
                             site_id=site.id, order_id=order.id, freight_provider_id=self.company.id)
        slot_2 = FreightSlot(status='in_progress', type='outload', start='2017-01-01', end='2017-01-02',
                             site_id=site.id, order_id=order.id, freight_provider_id=self.company.id)
        slot_3 = FreightSlot(status='planned', start='2017-01-01', end='2017-01-02', site_id=site.id,
                             order_id=order.id, truck=truck, freight_provider_id=self.company.id)
        slot.save()
        slot_1.save()
        slot_2.save()
        slot_3.save()
        response = self.client.get(
            '/company_sites/slots/?site_id=456&rego=Truck1',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        slot_ids = [slot['id'] for slot in response.data]
        self.assertIn(slot.id, slot_ids)
        self.assertIn(slot_3.id, slot_ids)

        response = self.client.get(
            '/company_sites/slots/?site_id=456&exclude_statuses=planned&exclude_statuses=booked',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], slot_2.id)

        response = self.client.get(
            '/company_sites/slots/?site_id=456&rego=Truck1&exclude_statuses=booked',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], slot_3.id)

    @patch('core.company_sites.views.FreightSlotSerializer')
    @patch('core.company_sites.views.FreightSlot')
    def test_get_for_company(self, freight_slot_klass_mock, serializer_mock):
        freight_slot_klass_mock.company_slots = Mock(
            return_value=Mock(
                select_related=Mock(
                    return_value=Mock(prefetch_related=Mock(return_value=['slot1', 'slot2']))
                )
            )
        )
        expected_slots_response = [{'id': 'slot1'}, {'id': 'slot2'}]
        serializer_mock.return_value = Mock(data=expected_slots_response)

        response = self.client.get(
            '/company_sites/slots/?company_id=123',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, expected_slots_response)
        freight_slot_klass_mock.company_slots.assert_called_once_with('123', None, None, None, None, None, False, [])
        serializer_mock.assert_called_once_with(['slot1', 'slot2'], context={'request': ANY}, many=True)


@tag('view')
class SiteOpenOrCompanyBookedFreightSlotsViewTest(AuthSetup):
    @patch('core.company_sites.views.FreightSlotSerializer')
    @patch('core.company_sites.views.FreightSlot')
    def test_get_for_requesting_user(self, freight_slot_klass_mock, serializer_mock):
        freight_slot_klass_mock.open_or_company_booked_slots = Mock(
            return_value=Mock(
                select_related=Mock(
                    return_value=Mock(prefetch_related=Mock(return_value=['slot1', 'slot2']))
                )
            )
        )
        expected_slots_response = [{'id': 'slot1'}, {'id': 'slot2'}]
        serializer_mock.return_value = Mock(data=expected_slots_response)

        response = self.client.get(
            '/company_sites/slots/open-or-self/?site_company_id=456',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, expected_slots_response)
        freight_slot_klass_mock.open_or_company_booked_slots.assert_called_once_with(
            self.company.id, None, None, '456', None, None, False, []
        )
        serializer_mock.assert_called_once_with(['slot1', 'slot2'], context={'request': ANY}, many=True)

    def test_get_slots_with_filters(self):
        truck = TruckFactory(rego="Truck1", company=self.company)
        site = FarmFactory(id=456)
        order = FreightOrderFactory()
        slot = FreightSlot(status='booked', type='outload', start='2017-01-01', end='2017-01-02', site_id=site.id,
                           order_id=order.id, truck=truck, freight_provider_id=self.company.id)
        slot_1 = FreightSlot(status='planned', type='outload', start='2017-01-01', end='2017-01-02', site_id=site.id,
                             order_id=order.id, freight_provider_id=self.company.id)
        slot_2 = FreightSlot(status='in_progress', type='outload', start='2017-01-01', end='2017-01-02',
                             site_id=site.id, order_id=order.id, freight_provider_id=self.company.id)
        slot_3 = FreightSlot(status='planned', start='2017-01-01', end='2017-01-02', site_id=site.id,
                             order_id=order.id, truck=truck, freight_provider_id=self.company.id)
        slot.save()
        slot_1.save()
        slot_2.save()
        slot_3.save()
        response = self.client.get(
            '/company_sites/slots/open-or-self/?site_id=456&rego=Truck1',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        slot_ids = [slot['id'] for slot in response.data]
        self.assertIn(slot.id, slot_ids)
        self.assertIn(slot_3.id, slot_ids)

        response = self.client.get(
            '/company_sites/slots/open-or-self/?site_id=456&exclude_statuses=planned&exclude_statuses=booked',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], slot_2.id)

        response = self.client.get(
            '/company_sites/slots/open-or-self/?site_id=456&rego=Truck1&exclude_statuses=booked',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['id'], slot_3.id)

    @patch('core.company_sites.views.FreightSlotSerializer')
    @patch('core.company_sites.views.FreightSlot')
    def test_get_for_company(self, freight_slot_klass_mock, serializer_mock):
        freight_slot_klass_mock.open_or_company_booked_slots = Mock(
            return_value=Mock(
                select_related=Mock(
                    return_value=Mock(prefetch_related=Mock(return_value=['slot1', 'slot2']))
                )
            )
        )
        expected_slots_response = [{'id': 'slot1'}, {'id': 'slot2'}]
        serializer_mock.return_value = Mock(data=expected_slots_response)

        response = self.client.get(
            '/company_sites/slots/open-or-self/?company_id=123&site_company_id=456',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, expected_slots_response)
        freight_slot_klass_mock.open_or_company_booked_slots.assert_called_once_with(
            '123', None, None, '456', None, None, False, []
        )
        serializer_mock.assert_called_once_with(['slot1', 'slot2'], context={'request': ANY}, many=True)

@tag('view')
class CompanySiteFreightSlotsViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.site = FarmFactory(id=123)

    @patch('core.company_sites.views.FreightSlot')
    def test_post_201(self, freight_slot_klass_mock):
        freight_slot_klass_mock.persist_many = Mock(return_value=[FreightSlot(id=100)])
        payload = {'type': 'outload'}

        response = self.client.post(
            '/company_sites/123/slots/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data, {'persisted': True, 'ids': [100]})
        freight_slot_klass_mock.persist_many.assert_called_once_with(
            site_id=123, data=[{**payload, 'created_by_id': ANY, 'updated_by_id': ANY}], request_origin='Web',
            return_slots=True
        )

    @patch('core.company_sites.views.FreightSlot')
    def test_post_multiple_201(self, freight_slot_klass_mock):
        freight_slot_klass_mock.persist_many = Mock(return_value=[FreightSlot(id=100), FreightSlot(id=101)])

        payload = [{'type': 'outload'}, {'type': 'inload'}]

        response = self.client.post(
            '/company_sites/123/slots/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data, {'persisted': True, 'ids': [100, 101]})
        freight_slot_klass_mock.persist_many.assert_called_once_with(
            site_id=123,
            data=[
                {'type': 'outload', 'created_by_id': ANY, 'updated_by_id': ANY},
                {'type': 'inload', 'created_by_id': ANY, 'updated_by_id': ANY},
            ],
            request_origin='Web',
            return_slots=True
        )

    @patch('core.company_sites.views.FreightSlot')
    def test_post_400(self, freight_slot_klass_mock):
        freight_slot_klass_mock.persist_many = Mock(return_value=False)
        payload = {'type': 'outload'}

        response = self.client.post(
            '/company_sites/123/slots/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'persisted': False, 'ids': []})
        freight_slot_klass_mock.persist_many.assert_called_once_with(
            site_id=123, data=[{**payload, 'created_by_id': ANY, 'updated_by_id': ANY}], request_origin='Web',
            return_slots=True
        )

    @patch('core.company_sites.views.FreightSlot')
    def test_post_multiple_400(self, freight_slot_klass_mock):
        freight_slot_klass_mock.persist_many = Mock(return_value=False)
        payload = [{'type': 'outload'}, {'type': 'inload'}]

        response = self.client.post(
            '/company_sites/123/slots/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'persisted': False, 'ids': []})
        freight_slot_klass_mock.persist_many.assert_called_once_with(
            site_id=123,
            data=[
                {'type': 'outload', 'created_by_id': ANY, 'updated_by_id': ANY},
                {'type': 'inload', 'created_by_id': ANY, 'updated_by_id': ANY},
            ],
            request_origin='Web',
            return_slots=True
        )

class FreightSlotTestData(AuthSetup):  # pylint: disable=too-many-instance-attributes
    def setup_data(self):
        self.order = FreightOrderFactory(commodity_id=1, planned_grade_id=1, season="23/24", planned_tonnage=500)
        self.provider_company = CompanyFactory(business_name='foo bar logistics')
        self.truck = TruckFactory(company=self.provider_company)
        self.driver = EmployeeFactory(company=self.provider_company)
        self.inload_farm = FarmFactory(company=self.company)
        self.outload_farm_company = CompanyFactory(business_name='outload farm company')
        self.outload_farm = FarmFactory(company=self.outload_farm_company)
        self.outload_slot = FreightSlot(
            start=timezone.now(), end=timezone.now(), site=self.outload_farm, status='planned')
        self.inload_slot = FreightSlot(
            start=timezone.now(), end=timezone.now(), site=self.inload_farm, status='planned')
        self.outload_trailer_slot = FreightSlot(
            start=timezone.now() + datetime.timedelta(hours=1), end=timezone.now() + datetime.timedelta(hours=1),
            site=self.outload_farm, status='planned')
        self.inload_trailer_slot = FreightSlot(
            start=timezone.now() + datetime.timedelta(hours=1), end=timezone.now() + datetime.timedelta(hours=1),
            site=self.inload_farm, status='planned')
        self.outload_slot.save()
        self.inload_slot.save()
        self.outload_trailer_slot.save()
        self.inload_trailer_slot.save()

    def get_payload(self):
        return {
        "assignToId": self.driver.id,
        "driverId": self.driver.id,
        "commodityId": 1,
        "communication": {
            "acceptanceRequired": False
        },
        "customer": {
            "companyId": self.order.customer.company_id
        },
        "estimatedDistance": "50.00",
        "estimatedTime": "2 hours",
        "freightDelivery": {
            "consignee": {
                "handlerId": self.inload_farm.id
            },
            "dateTime": timezone.now()
        },
        "freightPickup": {
            "consignor": {
                "handlerId": self.outload_farm.id
            },
            "dateTime": timezone.now()
        },
        "identifier": "M2409103F1DD85",
        "inloadSlotId": self.inload_slot.id,
        "outloadSlotId": self.outload_slot.id,
        "inloadSlotUpdatedAt": timezone.now().timestamp(),
        "outloadSlotUpdatedAt": timezone.now().timestamp(),
        "outloadTrailerSlots": {
            "trailer1": self.outload_trailer_slot.id,
            "trailer2": None,
            "trailer3": None
        },
        "inloadTrailerSlots": {
            "trailer1": self.inload_trailer_slot.id,
            "trailer2": None,
            "trailer3": None
        },
        "orderId": self.order.id,
        "plannedGradeId": 1,
        "plannedTonnage": 30,
        "plannedTruckId": self.truck.id,
        "providerId": self.provider_company.id,
        "season": "23/24",
        "status": "planned",
        "typeId": 1,
        "truckConfig": {
            "category_id": 1545,
            "steer_point_5": False,
            "steer_1_point_1": False,
            "permit_number": None,
            "declared_mass_limit": None,
            "accreditation_number": None,
            "load_sharing": True,
            "notice_number": None,
            "restricted": None
        }
    }


@tag('view')
class CompanySiteFreightSlotViewTest(FreightSlotTestData):
    def test_put_404(self):
        response = self.client.put(
            '/company_sites/123/slots/786/',
            {'type': 'outload'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 404)

    @patch('core.company_sites.views.FreightSlot.is_old_version')
    @patch('core.company_sites.views.FreightSlot.is_setting_updated_since')
    def test_put_200_with_new_slot(self, is_setting_updated_since_mock, is_old_version_mock):
        is_setting_updated_since_mock.return_value = False
        is_old_version_mock.return_value = False
        site = CompanySiteFactory()
        provider = CompanyFactory()
        order = FreightOrderFactory()
        movement = FreightMovementFactory(order=order)
        delta = datetime.timedelta(hours=1)
        date_format = "%Y-%m-%dT%H:%M:%SZ"
        old_slot = FreightSlot(
            status='booked',
            tonnage=10, site=site, movement=movement, order=order, freight_provider=provider,
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now()
        )
        new_slot = FreightSlot(
            status='planned', site=site, start=timezone.now() + delta,
            end=timezone.now() + delta
        )
        payload = {
            'type': 'outload',
            'status': 'booked',
            'site_id': site.id,
            'updated_at': old_slot.updated_at,
            'driver_id': 1,
            'freightProviderId': provider.id,
            'settings_updated_at': None,
        }
        movement.save()
        old_slot.save()
        new_slot.save()

        response = self.client.put(
            f"/company_sites/123/slots/{old_slot.id}/?new_slot_id={new_slot.id}",
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        old_slot.refresh_from_db()
        new_slot.refresh_from_db()
        movement.refresh_from_db()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get('id'), new_slot.id)
        self.assertEqual(response.data.get('start'), new_slot.start.strftime(date_format))
        self.assertEqual(response.data.get('end'), new_slot.end.strftime(date_format))
        self.assertEqual(response.data.get('status'), 'booked')
        self.assertEqual(new_slot.status, 'booked')
        self.assertEqual(old_slot.status, 'planned')
        self.assertEqual(new_slot.movement, movement)

    @patch('core.company_sites.views.FreightSlot.is_old_version')
    @patch('core.company_sites.views.FreightSlot.is_setting_updated_since')
    def test_put_200_with_new_slot_and_dual_slot_booking_and_old_slot_delayed(
            self, is_setting_updated_since_mock, is_old_version_mock):
        pickup_site = FarmFactory(allow_outload_slot_order_booking=True)
        SiteManagementSettings(company=pickup_site.company, minimum_tonnage=0.1, order_booking=True).save()
        delivery_site = FarmFactory(allow_inload_slot_order_booking=True)
        SiteManagementSettings(company=delivery_site.company, minimum_tonnage=0.1, order_booking=True).save()
        is_setting_updated_since_mock.return_value = False
        is_old_version_mock.return_value = False
        provider = CompanyFactory()
        order = FreightOrderFactory()
        order.freight_pickup.consignor.handler = pickup_site
        order.freight_pickup.consignor.save()
        order.freight_delivery.consignee.handler = delivery_site
        order.freight_delivery.consignee.save()
        movement = FreightMovementFactory(order=order)
        delta = datetime.timedelta(hours=1)
        date_format = "%Y-%m-%dT%H:%M:%SZ"
        inload_slot = FreightSlot(
            status='delayed',
            tonnage=10, site=delivery_site, movement=movement, order=order, freight_provider=provider,
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now(),
            type='inload'
        )
        outload_slot = FreightSlot(
            status='completed',
            tonnage=10, site=pickup_site, movement=movement, order=order, freight_provider=provider,
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now(),
            type='outload'
        )
        new_inload_slot = FreightSlot(
            status='planned', site=delivery_site, start=timezone.now() + delta,
            end=timezone.now() + delta
        )
        payload = {
            'type': 'outload',
            'status': 'booked',
            'site_id': delivery_site.id,
            'updated_at': inload_slot.updated_at,
            'driver_id': 1,
            'grade_id': inload_slot.grade_id,
            'freightProviderId': provider.id,
            'settings_updated_at': None,
            'order_id': order.id,
            'delivery_order_number': order.identifier,
        }
        movement.save()
        inload_slot.save()
        outload_slot.save()
        new_inload_slot.save()

        response = self.client.put(
            f"/company_sites/123/slots/{inload_slot.id}/?new_slot_id={new_inload_slot.id}",
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        inload_slot.refresh_from_db()
        outload_slot.refresh_from_db()
        new_inload_slot.refresh_from_db()
        movement.refresh_from_db()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get('id'), new_inload_slot.id)
        self.assertEqual(response.data.get('start'), new_inload_slot.start.strftime(date_format))
        self.assertEqual(response.data.get('end'), new_inload_slot.end.strftime(date_format))
        self.assertEqual(outload_slot.status, 'completed')
        self.assertEqual(outload_slot.order, order)
        self.assertEqual(outload_slot.movement, movement)
        self.assertEqual(inload_slot.status, 'planned')
        self.assertEqual(inload_slot.order, None)
        self.assertEqual(inload_slot.movement, None)
        self.assertEqual(new_inload_slot.status, 'delayed')
        self.assertEqual(new_inload_slot.movement, movement)
        self.assertEqual(new_inload_slot.order, order)

    @patch('core.company_sites.views.FreightSlot.is_old_version')
    @patch('core.company_sites.views.FreightSlot.is_setting_updated_since')
    def test_put_200_with_new_slot_and_dual_slot_booking_and_old_slot_in_progress(
            self, is_setting_updated_since_mock, is_old_version_mock):
        pickup_site = FarmFactory(allow_outload_slot_order_booking=True)
        SiteManagementSettings(company=pickup_site.company, minimum_tonnage=0.1, order_booking=True).save()
        delivery_site = FarmFactory(allow_inload_slot_order_booking=True)
        SiteManagementSettings(company=delivery_site.company, minimum_tonnage=0.1, order_booking=True).save()
        is_setting_updated_since_mock.return_value = False
        is_old_version_mock.return_value = False
        provider = CompanyFactory()
        order = FreightOrderFactory()
        order.freight_pickup.consignor.handler = pickup_site
        order.freight_pickup.consignor.save()
        order.freight_delivery.consignee.handler = delivery_site
        order.freight_delivery.consignee.save()
        movement = FreightMovementFactory(order=order)
        delta = datetime.timedelta(hours=1)
        date_format = "%Y-%m-%dT%H:%M:%SZ"
        inload_slot = FreightSlot(
            status='in_progress',
            tonnage=10, site=delivery_site, movement=movement, order=order, freight_provider=provider,
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now(),
            type='inload'
        )
        outload_slot = FreightSlot(
            status='completed',
            tonnage=10, site=pickup_site, movement=movement, order=order, freight_provider=provider,
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now(),
            type='outload'
        )
        new_inload_slot = FreightSlot(
            status='planned', site=delivery_site, start=timezone.now() + delta,
            end=timezone.now() + delta
        )
        payload = {
            'type': 'outload',
            'status': 'booked',
            'site_id': delivery_site.id,
            'updated_at': inload_slot.updated_at,
            'driver_id': 1,
            'grade_id': inload_slot.grade_id,
            'freightProviderId': provider.id,
            'settings_updated_at': None,
            'order_id': order.id,
            'delivery_order_number': order.identifier,
        }
        movement.save()
        inload_slot.save()
        outload_slot.save()
        new_inload_slot.save()

        response = self.client.put(
            f"/company_sites/123/slots/{inload_slot.id}/?new_slot_id={new_inload_slot.id}",
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        inload_slot.refresh_from_db()
        outload_slot.refresh_from_db()
        new_inload_slot.refresh_from_db()
        movement.refresh_from_db()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get('id'), new_inload_slot.id)
        self.assertEqual(response.data.get('start'), new_inload_slot.start.strftime(date_format))
        self.assertEqual(response.data.get('end'), new_inload_slot.end.strftime(date_format))
        self.assertEqual(outload_slot.status, 'completed')
        self.assertEqual(outload_slot.order, order)
        self.assertEqual(outload_slot.movement, movement)
        self.assertEqual(inload_slot.status, 'planned')
        self.assertEqual(inload_slot.order, None)
        self.assertEqual(inload_slot.movement, None)
        self.assertEqual(new_inload_slot.status, 'in_progress')
        self.assertEqual(new_inload_slot.movement, movement)
        self.assertEqual(new_inload_slot.order, order)

    @patch('core.company_sites.views.FreightSlot.is_old_version')
    @patch('core.company_sites.views.FreightSlot.is_setting_updated_since')
    def test_put_200_with_new_slot_and_dual_slot_booking_and_old_slot_booked(
            self, is_setting_updated_since_mock, is_old_version_mock):
        pickup_site = FarmFactory(allow_outload_slot_order_booking=True)
        SiteManagementSettings(company=pickup_site.company, minimum_tonnage=0.1, order_booking=True).save()
        delivery_site = FarmFactory(allow_inload_slot_order_booking=True)
        SiteManagementSettings(company=delivery_site.company, minimum_tonnage=0.1, order_booking=True).save()
        is_setting_updated_since_mock.return_value = False
        is_old_version_mock.return_value = False
        provider = CompanyFactory()
        order = FreightOrderFactory()
        order.freight_pickup.consignor.handler = pickup_site
        order.freight_pickup.consignor.save()
        order.freight_delivery.consignee.handler = delivery_site
        order.freight_delivery.consignee.save()
        movement = FreightMovementFactory(order=order)
        delta = datetime.timedelta(hours=1)
        date_format = "%Y-%m-%dT%H:%M:%SZ"
        inload_slot = FreightSlot(
            status='booked',
            tonnage=10, site=delivery_site, movement=movement, order=order, freight_provider=provider,
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now(),
            type='inload'
        )
        outload_slot = FreightSlot(
            status='completed',
            tonnage=10, site=pickup_site, movement=movement, order=order, freight_provider=provider,
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now(),
            type='outload'
        )
        new_inload_slot = FreightSlot(
            status='planned', site=delivery_site, start=timezone.now() + delta,
            end=timezone.now() + delta
        )
        payload = {
            'type': 'outload',
            'status': 'booked',
            'site_id': delivery_site.id,
            'updated_at': inload_slot.updated_at,
            'driver_id': 1,
            'grade_id': inload_slot.grade_id,
            'freightProviderId': provider.id,
            'settings_updated_at': None,
            'order_id': order.id,
            'delivery_order_number': order.identifier,
        }
        movement.save()
        inload_slot.save()
        outload_slot.save()
        new_inload_slot.save()

        response = self.client.put(
            f"/company_sites/123/slots/{inload_slot.id}/?new_slot_id={new_inload_slot.id}",
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        inload_slot.refresh_from_db()
        outload_slot.refresh_from_db()
        new_inload_slot.refresh_from_db()
        movement.refresh_from_db()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get('id'), new_inload_slot.id)
        self.assertEqual(response.data.get('start'), new_inload_slot.start.strftime(date_format))
        self.assertEqual(response.data.get('end'), new_inload_slot.end.strftime(date_format))
        self.assertEqual(outload_slot.status, 'completed')
        self.assertEqual(outload_slot.order, order)
        self.assertEqual(outload_slot.movement, movement)
        self.assertEqual(inload_slot.status, 'planned')
        self.assertEqual(inload_slot.order, None)
        self.assertEqual(inload_slot.movement, None)
        self.assertEqual(new_inload_slot.status, 'booked')
        self.assertEqual(new_inload_slot.movement, movement)
        self.assertEqual(new_inload_slot.order, order)

    @patch('core.company_sites.views.FreightSlot.is_old_version')
    @patch('core.company_sites.views.FreightSlot.is_setting_updated_since')
    def test_put_200_with_new_slot_and_no_order(self, is_setting_updated_since_mock, is_old_version_mock):
        from core.locations.tests.factories import AddressFactory
        is_setting_updated_since_mock.return_value = False
        is_old_version_mock.return_value = False
        site_company = CompanyFactory()
        SiteManagementSettings(company=site_company, order_booking=True).save()
        site = CompanySiteFactory(company=site_company,
                                  address=AddressFactory(latitude=14.43, longitude=133.96))
        provider = CompanyFactory()
        truck = TruckFactory(company=provider)
        driver = EmployeeFactory(company=provider)
        slot = FreightSlot(
            status='planned',
            tonnage=10, site=site, freight_provider=provider,
            start=timezone.now(), end=timezone.now()
        )
        slot.save()
        payload = {
            'type': 'outload',
            'status': 'booked',
            'site_id': site.id,
            'updated_at': slot.updated_at,
            'driver_id': driver.id,
            'truck_id': truck.id,
            'freight_provider_id': provider.id,
            'settings_updated_at': None,
        }

        response = self.client.put(
            f"/company_sites/{site.id}/slots/{slot.id}/",
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data['errors'], {'orderId': 'Slot cannot be booked without order.'})

    @patch('core.company_sites.views.FreightSlot.is_old_version')
    @patch('core.company_sites.views.FreightSlot.is_setting_updated_since')
    def test_put_200_with_new_slot_and_new_counter_slot(self, is_setting_updated_since_mock, is_old_version_mock): # pylint: disable=too-many-locals
        is_setting_updated_since_mock.return_value = False
        is_old_version_mock.return_value = False
        company_1 = CompanyFactory(business_name='Company 1')
        company_2 = CompanyFactory(business_name='Company 2')
        site = CompanySiteFactory(company=company_1, name="Site 1")
        counter_site = CompanySiteFactory(company=company_2, name="Site 2")
        provider = CompanyFactory(business_name='Logistics')
        order = FreightOrderFactory()
        movement = FreightMovementFactory(order=order)
        delta = datetime.timedelta(hours=1)
        date_format = "%Y-%m-%dT%H:%M:%SZ"
        old_slot = FreightSlot(
            status='booked', type='outload',
            tonnage=10, site=site, movement=movement, order=order, freight_provider=provider,
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now()
        )
        old_counter_slot = FreightSlot(
            status='booked', type='inload',
            tonnage=10, site=counter_site, movement=movement, order=order, freight_provider=provider,
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now()
        )
        new_slot = FreightSlot(
            status='planned', site=site, start=timezone.now() + delta,
            end=timezone.now() + delta
        )
        new_counter_slot = FreightSlot(
            status='planned', site=counter_site, start=timezone.now() + delta,
            end=timezone.now() + delta
        )
        payload = {
            'type': 'outload',
            'status': 'booked',
            'site_id': site.id,
            'updated_at': old_slot.updated_at,
            'counter_slot_updated_at': old_counter_slot.updated_at,
            'driver_id': 1,
            'freightProviderId': provider.id,
            'settings_updated_at': None,
            'counter_slot_settings_updated_at': None
        }
        movement.save()
        old_slot.save()
        old_counter_slot.save()
        new_slot.save()
        new_counter_slot.save()

        response = self.client.put(
            f"/company_sites/123/slots/{old_slot.id}/"
            f"?new_slot_id={new_slot.id}&new_counter_slot_id={new_counter_slot.id}",
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        old_slot.refresh_from_db()
        old_counter_slot.refresh_from_db()
        new_slot.refresh_from_db()
        new_counter_slot.refresh_from_db()
        movement.refresh_from_db()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data.get('id'), new_slot.id)
        self.assertEqual(response.data.get('start'), new_slot.start.strftime(date_format))
        self.assertEqual(response.data.get('end'), new_slot.end.strftime(date_format))
        self.assertEqual(response.data.get('status'), 'booked')
        self.assertEqual(new_slot.status, 'booked')
        self.assertEqual(new_counter_slot.status, 'booked')
        self.assertEqual(old_slot.status, 'planned')
        self.assertEqual(old_counter_slot.status, 'planned')
        self.assertEqual(new_slot.movement, movement)
        self.assertEqual(new_counter_slot.movement, movement)
        self.assertEqual(movement.outload_slot, new_slot)
        self.assertEqual(movement.inload_slot, new_counter_slot)


    @patch('core.company_sites.views.FreightSlotSerializer')
    @patch('core.company_sites.views.FreightSlot')
    def test_put_200(self, slot_klass_mock, serializer_mock):
        slot = FreightSlot(id=786)
        slot.is_setting_updated_since = Mock(return_value=False)
        expected_slot_response = {'id': 786, 'siteId': 123, 'type': 'outload'}
        payload = {'type': 'outload'}
        serializer_mock.return_value = Mock(data=expected_slot_response)

        slot_klass_mock.objects.get = Mock(return_value=slot)
        slot_klass_mock.update_from_view = Mock(return_value=(True, slot))

        response = self.client.put(
            '/company_sites/123/slots/786/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, expected_slot_response)
        slot_klass_mock.update_from_view.assert_called_once_with(
            self.employee,
            {**payload, 'updated_by_id': self.employee.id},
            slot.id,
            None,
            False,
            None,
            False,
            False,
            'Web'
        )
        serializer_mock.assert_called_once_with(slot, context={'request': ANY})

    @patch('core.company_sites.views.FreightSlotSerializer')
    @patch('core.company_sites.views.FreightSlot')
    def test_put_400_with_errors(self, slot_klass_mock, serializer_mock):
        slot = FreightSlot(id=786)
        slot.is_setting_updated_since = Mock(return_value=False)
        slot.errors = 'foo-error'
        expected_slot_response = {'id': 786, 'siteId': 123, 'type': 'outload', 'errors': 'foo-error'}
        payload = {'type': 'outload'}

        serializer_mock.return_value = Mock(data=expected_slot_response)
        slot_klass_mock.update_from_view = Mock(return_value=(False, slot))

        response = self.client.put(
            '/company_sites/123/slots/786/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, expected_slot_response)
        slot_klass_mock.update_from_view.assert_called_once_with(
            self.employee,
            {**payload, 'updated_by_id': self.employee.id},
            slot.id,
            None,
            False,
            None,
            False,
            False,
            'Web'
        )
        serializer_mock.assert_called_once_with(slot, context={'request': ANY})

    @patch('core.company_sites.views.FreightSlotSerializer')
    @patch('core.company_sites.views.FreightSlot')
    def test_put_400_with_reasons(self, slot_klass_mock, serializer_mock):
        slot = FreightSlot(id=786)
        slot.is_setting_updated_since = Mock(return_value=False)
        expected_slot_response = {'id': 786, 'siteId': 123, 'type': 'outload', 'reasons': [FREIGHT_SLOT_OUTLOAD_VOID],
                                'errors': {}}
        payload = {'type': 'outload'}

        serializer_mock.return_value = Mock(data=expected_slot_response)
        slot_klass_mock.update_from_view = Mock(return_value=(False, slot))

        response = self.client.put(
            '/company_sites/123/slots/786/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, expected_slot_response)
        slot_klass_mock.update_from_view.assert_called_once_with(
            self.employee,
            {**payload, 'updated_by_id': self.employee.id},
            slot.id,
            None,
            False,
            None,
            False,
            False,
            'Web'
        )
        serializer_mock.assert_called_once_with(slot, context={'request': ANY})

    @patch('core.company_sites.views.FreightSlot.is_old_version')
    @patch('core.company_sites.views.FreightSlot.is_setting_updated_since')
    def test_cancel_booked_slot_200(self, is_setting_updated_since_mock, is_old_version_mock):
        is_setting_updated_since_mock.return_value = False
        is_old_version_mock.return_value = False
        site = CompanySiteFactory()
        provider = CompanyFactory()
        order = FreightOrderFactory()
        movement = FreightMovementFactory(order=order)
        slot = FreightSlot(
            type='outload', status='booked',
            tonnage=10, site=site, movement=movement, order=order, freight_provider=provider,
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now()
        )
        payload = {
            'type': 'outload',
            'status': 'planned',
            'site_id': site.id,
            'updated_at': slot.updated_at,
            'settings_updated_at': None
        }

        slot.save()

        response = self.client.put(
            f'/company_sites/{site.id}/slots/{slot.id}/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        slot.refresh_from_db()
        movement.refresh_from_db()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(movement.status, 'void')
        self.assertEqual(slot.status, 'planned')

    @patch('core.company_sites.views.FreightSlot.is_old_version')
    @patch('core.company_sites.views.FreightSlot.is_setting_updated_since')
    def test_cancel_booked_slot_400(self, is_setting_updated_since_mock, is_old_version_mock):
        is_setting_updated_since_mock.return_value = False
        is_old_version_mock.return_value = False
        site = CompanySiteFactory()
        order = FreightOrderFactory()
        farm = FarmFactory(stocks_management=True)
        movement = FreightMovementFactory(order=order, status='delivered', planned_tonnage=50)
        LoadFactory(
            type='inload', checkpoint=movement.freight_delivery, status='completed',
            storage=StorageFactory(farm=farm), ngr=NgrFactory(), movement=movement
        )
        slot = FreightSlot(
            status='booked', tonnage=10, site=site, movement=movement, order=order, freight_provider=CompanyFactory(),
            commodity=movement.commodity, grade=movement.planned_grade, start=timezone.now(), end=timezone.now()
        )
        expected_slot_response = {'reasons': [FREIGHT_SLOT_INLOAD_VOID]}
        payload = {
            'type': 'outload',
            'status': 'planned',
            'site_id': site.id,
            'updated_at': slot.updated_at,
            'settings_updated_at': None
        }
        slot.save()
        movement.save()

        response = self.client.put(
            f'/company_sites/{site.id}/slots/{slot.id}/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        slot.refresh_from_db()
        movement.refresh_from_db()

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, expected_slot_response)
        self.assertEqual(movement.status, 'delivered')
        self.assertEqual(slot.status, 'booked')

    @patch('core.company_sites.views.FreightSlot.objects')
    def test_delete_204(self, slot_manager_mock):
        site = FarmFactory()
        user = EmployeeFactory(company=site.company)
        slot = FreightSlot(status='planned', site=site)
        slot.publish_delete_to_ably = Mock()
        slot_manager_mock.filter = Mock(
            return_value=Mock(
                delete=Mock(), exists=Mock(return_value=True), first=Mock(return_value=slot))
        )

        response = self.client.delete(
            '/company_sites/123/slots/786/',
            HTTP_AUTHORIZATION='Token ' + user.refresh_token(),
            format='json'
        )

        self.assertEqual(response.status_code, 204)
        slot_manager_mock.filter.assert_called_once_with(
            id=786, site_id=123
        )
        slot_manager_mock.filter().exists.assert_called_once()
        slot_manager_mock.filter().first.assert_called_once()
        slot_manager_mock.filter().delete.assert_called_once()
        slot.publish_delete_to_ably.assert_called_once()

    @patch('core.company_sites.views.FreightSlot.objects')
    def test_delete_400(self, slot_manager_mock):
        slot = FreightSlot(status='booked')
        slot.publish_delete_to_ably = Mock()
        slot_manager_mock.filter = Mock(
            return_value=Mock(
                delete=Mock(), exists=Mock(return_value=True), first=Mock(return_value=slot))
        )

        response = self.client.delete(
            '/company_sites/123/slots/786/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'alert': 'Slot needs to be in planned status for delete.'})
        slot_manager_mock.filter.assert_called_once_with(
            id=786, site_id=123
        )
        slot_manager_mock.filter().exists.assert_called_once()
        slot_manager_mock.filter().first.assert_called_once()
        slot_manager_mock.filter().delete.assert_not_called()

    @patch('core.company_sites.views.FreightSlot.objects')
    def test_delete_404(self, slot_manager_mock):
        slot_manager_mock.filter = Mock(
            return_value=Mock(delete=Mock(), exists=Mock(return_value=False))
        )

        response = self.client.delete(
            '/company_sites/123/slots/786/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )

        self.assertEqual(response.status_code, 404)
        slot_manager_mock.filter.assert_called_once_with(
            id=786, site_id=123
        )
        slot_manager_mock.filter().exists.assert_called_once()
        slot_manager_mock.filter().delete.assert_not_called()

    @patch('core.company_sites.models.FreightSlot.publish_update_to_ably')
    @patch('core.company_sites.models.FreightSlot.notify_all')
    @patch('core.company_sites.models.FreightSlot.notify_updated')
    def test_counter_slot_update_with_trailer_slot_booking(
            self, notify_updated_mock, notify_all_mock, publish_update_to_ably_mock
    ):
        self.setup_data()
        payload = self.get_payload()

        response = self.client.post(
            '/freights/contracts/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 201)
        self.inload_slot.refresh_from_db()
        self.assertEqual(self.inload_slot.status, 'booked')

        new_slot = FreightSlot(
            start=timezone.now(), end=timezone.now(), site=self.inload_farm, status='planned')
        new_slot.save()

        # update counter slot request
        response = self.client.put(
            f'/company_sites/{self.inload_farm.id}/slots/{self.inload_slot.id}/?new_slot_id={new_slot.id}',
            {
                "siteId": self.inload_farm.id, "type": "inload", "pits": None, "bookingNumber": None,
                "deliveryOrderNumber": self.order.identifier, "orderId": self.order.id, "status": "booked",
                "commodityId": 1, "gradeId": 1, "tonnage": 30, "driverId": self.driver.id, "priority": False,
                "truckId": self.truck.id, "customer": None, "freightProviderId": self.provider_company.id,
                "subFreightProviderId": self.provider_company.id, "restrictedVisibleToCarrier": False,
                'updatedAt': timezone.now().timestamp(), 'settingsUpdatedAt': timezone.now().timestamp()
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.inload_slot.refresh_from_db()
        self.assertEqual(self.inload_slot.status, 'planned')
        new_slot.refresh_from_db()
        self.assertEqual(new_slot.status, 'booked')
        publish_update_to_ably_mock.assert_called_once()
        notify_all_mock.assert_called_once()
        notify_updated_mock.assert_called_once()

    @patch('core.company_sites.models.FreightSlot.publish_update_to_ably')
    @patch('core.company_sites.models.FreightSlot.notify_updated')
    def test_counter_trailer_slot_update(self, notify_updated_mock, publish_update_to_ably_mock):
        self.setup_data()
        payload = self.get_payload()

        response = self.client.post(
            '/freights/contracts/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 201)
        self.inload_slot.refresh_from_db()
        self.assertEqual(self.inload_slot.status, 'booked')

        new_slot = FreightSlot(
            start=timezone.now(), end=timezone.now(), site=self.inload_farm, status='planned')
        new_slot.save()

        # update counter trailer slot request
        response = self.client.put(
            f'/company_sites/{self.outload_farm.id}/slots/{self.outload_slot.id}/',
            {
                "siteId": self.outload_farm.id, "type": "outload", "pits": None, "bookingNumber": None,
                "deliveryOrderNumber": self.order.identifier, "orderId": self.order.id, "status": "booked",
                "commodityId": 1, "gradeId": 1, "tonnage": 30, "driverId": self.driver.id, "priority": False,
                "truckId": self.truck.id, "customer": None, "freightProviderId": self.provider_company.id,
                "subFreightProviderId": self.provider_company.id, "restrictedVisibleToCarrier": False,
                'updatedAt': timezone.now().timestamp(), 'settingsUpdatedAt': timezone.now().timestamp(),
                'counterTrailerSlots': {'trailer1': new_slot.id, 'trailer2': None, 'trailer3': None}
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.inload_trailer_slot.refresh_from_db()
        self.assertEqual(self.inload_trailer_slot.status, 'planned')
        new_slot.refresh_from_db()
        self.assertEqual(new_slot.status, 'booked')
        publish_update_to_ably_mock.assert_called_once()
        notify_updated_mock.assert_called_once()

    @patch('core.company_sites.models.FreightSlot.publish_update_to_ably')
    @patch('core.company_sites.models.FreightSlot.notify_all')
    @patch('core.company_sites.models.FreightSlot.notify_updated')
    def test_cancel_booking_and_rebooking_slot(self, notify_updated_mock, notify_all_mock, publish_update_to_ably_mock):
        self.setup_data()
        payload = self.get_payload()

        response = self.client.post(
            '/freights/contracts/',
            payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 201)
        self.inload_slot.refresh_from_db()
        self.assertEqual(self.inload_slot.status, 'booked')

        # Cancelling the slot
        response = self.client.put(
            f'/company_sites/{self.outload_farm.id}/slots/{self.outload_slot.id}/',
            {
                "siteId": self.outload_farm.id, "type": "outload", "pits": None, "bookingNumber": None,
                "deliveryOrderNumber": self.order.identifier, "orderId": self.order.id, "status": "planned",
                "commodityId": 1, "gradeId": 1, "tonnage": 30, "driverId": self.driver.id, "priority": False,
                "truckId": self.truck.id, "customer": None, "freightProviderId": self.provider_company.id,
                "subFreightProviderId": self.provider_company.id, "restrictedVisibleToCarrier": False,
                'updatedAt': timezone.now().timestamp(), 'settingsUpdatedAt': timezone.now().timestamp(),
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.outload_slot.refresh_from_db()
        self.inload_slot.refresh_from_db()
        self.inload_trailer_slot.refresh_from_db()
        self.outload_trailer_slot.refresh_from_db()
        self.assertEqual(self.outload_slot.status, 'planned')
        self.assertEqual(self.inload_slot.status, 'planned')
        self.assertEqual(self.outload_trailer_slot.status, 'planned')
        self.assertEqual(self.inload_trailer_slot.status, 'planned')
        publish_update_to_ably_mock.assert_called_once()
        notify_updated_mock.assert_called_once()
        self.assertEqual(notify_all_mock.call_count, 3)

        # Rebooking same slot
        _payload = self.get_payload()
        response = self.client.post(
            '/freights/contracts/',
            _payload,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 201)
        self.outload_slot.refresh_from_db()
        self.inload_slot.refresh_from_db()
        self.inload_trailer_slot.refresh_from_db()
        self.outload_trailer_slot.refresh_from_db()
        self.assertEqual(self.outload_slot.status, 'booked')
        self.assertEqual(self.inload_slot.status, 'booked')
        self.assertEqual(self.outload_trailer_slot.status, 'booked')
        self.assertEqual(self.inload_trailer_slot.status, 'booked')


@tag('model')
class SlotCommentTest(ACTestCase):
    @patch('core.common.models.RawModel.create')
    def test_create_by_provider(self, create_mock):
        slot = FreightSlot(id=123, freight_provider_id=200)
        slot.notify_commented_by_provider = Mock()
        expected_comment = SlotComment(
            slot=slot, comment='comment', created_by=Employee(company_id=200)
        )
        create_mock.return_value = expected_comment

        self.assertEqual(SlotComment.create({'foo': 'bar'}), expected_comment)
        create_mock.assert_called_once_with(
            {'foo': 'bar'},
            None,
            None
        )
        slot.notify_commented_by_provider.assert_called_once()

    @patch('core.common.models.RawModel.create')
    def test_create_by_operator(self, create_mock):
        slot = FreightSlot(id=123, freight_provider_id=200, site=CompanySite(company_id=300))
        expected_comment = SlotComment(
            slot=slot, comment='comment', created_by=Employee(company_id=300)
        )
        expected_comment = SlotComment(slot=slot, comment='comment')
        create_mock.return_value = expected_comment

        self.assertEqual(SlotComment.create({'foo': 'bar'}), expected_comment)
        create_mock.assert_called_once_with(
            {'foo': 'bar'},
            None,
            None
        )


@tag('model')
class SiteManagementSettingsTest(ACTestCase):
    def test_csv_header(self):
        settings = SiteManagementSettings()
        self.assertEqual(
            settings.csv_header(),
            [
                'Site', 'Type (Optional)', 'Category', 'Status', 'Priority (Optional)', 'Date', 'Planned Start Time',
                'Actual Start Time', 'Planned End Time', 'Actual End Time', 'Site Entry Time',
                'Site Exit Time', 'Load Start Time', 'Load End Time', 'Time On Site',
                'Commodity (Optional)', 'Grade (Optional)', 'Booked Tonnage (MT)', 'Actual Tonnage (MT)',
                'Customer (Optional)', 'Movement No', 'Order No (Optional)', 'Contract No',
                'Booking No (Optional)', 'Pits (Optional)', 'Freight Provider',
                'Sub Freight Provider', 'Truck (Optional)', 'Driver (Optional)', 'Mobile',
                'Transaction Time', 'Variance', 'Travel Time',
                'Created At', 'Created By', 'Last Updated At', 'Last Updated By',
            ]
        )
        self.assertEqual(
            settings.csv_header(booking_cancellation=True),
            [
                'Site', 'Type (Optional)', 'Category', 'Priority (Optional)', 'Date', 'Planned Start Time',
                'Actual Start Time', 'Planned End Time', 'Actual End Time', 'Site Entry Time',
                'Site Exit Time', 'Load Start Time', 'Load End Time', 'Time On Site',
                'Commodity (Optional)', 'Grade (Optional)', 'Booked Tonnage (MT)',
                'Customer (Optional)', 'Movement No', 'Order No (Optional)', 'Contract No',
                'Booking No (Optional)', 'Pits (Optional)', 'Freight Provider',
                'Sub Freight Provider', 'Truck (Optional)', 'Driver (Optional)', 'Mobile',
                'Transaction Time', 'Variance', 'Travel Time',
                'Created At', 'Created By', 'Last Updated At', 'Last Updated By',
            ]
        )

    @patch('core.company_sites.models.DateTimeUtil.days_after_end_of_month')
    @patch('core.company_sites.models.DateTimeUtil.days_before_start_of_month')
    @patch('core.company_sites.models.DateTimeUtil.days_after_end_of_week')
    @patch('core.company_sites.models.DateTimeUtil.days_before_start_of_week')
    @patch('core.company_sites.models.DateTimeUtil.tomorrow')
    @patch('core.company_sites.models.DateTimeUtil.yesterday')
    @patch('core.company_sites.models.SiteManagementSettings.objects')
    def test_get_default_view_date_range(
            self, manager_mock, yesterday_mock, tomorrow_mock, days_before_start_of_week_mock,
            days_after_end_of_week_mock, days_before_start_of_month_mock,
            days_after_end_of_month_mock
    ):
        today = timezone.now().date()
        yesterday_mock.return_value = '2019-01-01'
        tomorrow_mock.return_value = '2019-01-03'
        days_before_start_of_week_mock.return_value = '2019-01-01'
        days_after_end_of_week_mock.return_value = '2019-01-08'
        days_before_start_of_month_mock.return_value = '2018-12-31'
        days_after_end_of_month_mock.return_value = '2019-02-01'
        settings = SiteManagementSettings()
        settings.default_view = 'day'
        manager_mock.filter = Mock(return_value=Mock(first=Mock(return_value=settings)))

        self.assertEqual(
            SiteManagementSettings.get_default_view_date_range(123),
            {
                'start__gte': '2019-01-01',
                'end__lt': '2019-01-03',
            }
        )
        yesterday_mock.assert_called_once()
        tomorrow_mock.assert_called_once()

        settings.default_view = 'week'
        self.assertEqual(
            SiteManagementSettings.get_default_view_date_range(123),
            {
                'start__gte': '2019-01-01',
                'end__lt': '2019-01-08',
            }
        )
        days_before_start_of_week_mock.assert_called_once_with(today, 0)
        days_after_end_of_week_mock.assert_called_once_with(today, 1)

        settings.default_view = 'month'
        self.assertEqual(
            SiteManagementSettings.get_default_view_date_range(123),
            {
                'start__gte': '2018-12-31',
                'end__lt': '2019-02-01',
            }
        )
        days_before_start_of_month_mock.assert_called_once_with(today, 0)
        days_after_end_of_month_mock.assert_called_once_with(today, 1)

        manager_mock.filter.assert_called_with(company_id=123)

@tag('job')
class PushSlotToSFTPTest(ACTestCase):
    def tearDown(self):
        self.remove_temp_files('slots_*')
        super().tearDown()

    def setUp(self):
        self.remove_temp_files('slots_*')
        super().setUp()
        self.site_company = CompanyFactory(business_name='SiteWala', agrichain_as_vendor_number='VendorNumber')
        self.provider = CompanyFactory(business_name='TruckWala')
        self.truck = TruckFactory(rego='TRUCKREGO', company=self.provider)
        self.contract = ContractFactory(
            identifier='CCIDENT', commodity_id=1, contract_number='CCIDENT', material_code='Best Material')
        self.order = FreightOrderFactory(
            identifier='FOIDENT', provider=self.provider, commodity_id=1, commodity_contract=self.contract)
        self.site = CompanySiteFactory(
            name='ExternallyControlledSite',
            _external_connection_configurations={
                'hostname': 'sftp.server.com',
                'username': 'sftpuser',
                'password': 'sftppassword'
            },
            company=self.site_company,
        )
        self.site.add_external_plan_code(self.site_company.id, '5022')

        # Aus address
        self.site.address.latitude = -37.804121
        self.site.address.longitude = 144.941616
        self.site.address.save()

        self.slot = FreightSlot(
            type='inload',
            site=self.site,
            start='2022-06-01 10:00:00+00:00',
            end='2022-06-01 10:30:00+00:00',
            status='planned'
        )
        self.slot.save()
        self.assertTrue(self.slot.persisted)

    @patch('core.services.external.aws.S3.upload')
    @patch('pysftp.Connection')
    def test_push_task_success(self, sftp_connection_klass_mock, s3_upload_mock):  # pylint: disable=too-many-locals,too-many-statements
        context_mock = Mock(execute=Mock())
        cd_mock = Mock()
        cd_mock.__enter__ = Mock(return_value=context_mock)
        cd_mock.__exit__ = Mock(return_value=None)

        sftp_connection_mock = Mock(cd=Mock(return_value=cd_mock), put=Mock(return_value='Success'))
        sftp_connection_klass_mock.return_value = sftp_connection_mock

        # booking
        updated_slot = FreightSlot.update(
            instance=self.slot,
            data={
                'status': 'booked',
                'order_id': self.order.id,
                'commodity_id': 1,
                'grade_id': 1,
                'tonnage': 15,
                'truck_id': self.truck.id,
                'driver_id': self.truck.created_by_id,
                'freight_provider_id': self.truck.company_id
            }
        )

        self.assertEqual(updated_slot.truck_id, self.truck.id)
        self.assertIsNotNone(updated_slot.movement_id)
        self.assertTrue(self.order.id == updated_slot.order_id == updated_slot.movement.order_id)
        temp_jobs = Job.objects.filter(status='__temp__')
        self.assertEqual(temp_jobs.count(), 1)
        job_params = temp_jobs.first().params
        self.assertEqual(job_params['slot_id'], self.slot.id)
        self.assertEqual(job_params['site_id'], self.site.id)
        self.assertEqual(
            job_params['csv_row'],
            [
                'VendorNumber',
                'INLOAD',
                updated_slot.movement.identifier,
                'FOIDENT',
                'CCIDENT',
                'Wheat',
                'Best Material',
                '2022-06-01 20:00:00+10:00',
                15,
                '5022',
                'TRUCKREGO',
                f'"{self.truck.created_by.name}"',
                'APH1',
                'booked'
            ]
        )

        slot = FreightSlot.objects.get(id=self.slot.id)
        # cancellation
        updated_slot2 = FreightSlot.update(
            instance=slot,
            data={
                'status': 'planned',
                'commodity_id': None,
                'grade_id': None,
                'tonnage': None,
                'truck_id': None,
                'driver_id': None,
                'freight_provider_id': None
            }
        )

        self.assertEqual(updated_slot2.truck_id, None)
        self.assertIsNone(updated_slot2.movement_id)
        self.assertIsNone(updated_slot2.order_id)
        slot.refresh_from_db()
        self.assertIsNone(slot._last_pushed_at)
        temp_jobs = Job.objects.filter(status='__temp__').order_by('-created_at')
        self.assertEqual(temp_jobs.count(), 2)
        job_params = temp_jobs.first().params
        self.assertEqual(job_params['slot_id'], slot.id)
        self.assertEqual(job_params['site_id'], self.site.id)
        self.assertEqual(
            job_params['csv_row'],
            [
                'VendorNumber',
                'INLOAD',
                updated_slot.movement.identifier,
                'FOIDENT',
                'CCIDENT',
                'Wheat',
                'Best Material',
                '2022-06-01 20:00:00+10:00',
                None,
                '5022',
                'TRUCKREGO',
                None,
                'APH1',
                'cancelled'
            ]
        )

        time_to_push = '2022-06-01 10:15:20.987654'
        with freeze_time(time_to_push):
            push_slots_events_to_sftp(False)

        self.assertEqual(Job.objects.filter(status='__temp__').count(), 0)
        self.assertEqual(Job.objects.filter(status='__batch_success__').count(), 2)
        self.assertEqual(
            Job.objects.filter(
                status='__batch_success__',
                artifacts={
                    'filename': 'slots_20220601101520.987654.csv',
                    'timestamp': '2022-06-01 10:15:20.987654+00:00'
                }
            ).count(),
            2
        )
        files = self.read_files('slots_*')
        self.assertEqual(len(files), 1)
        file_path = files[0]
        self.assertEqual(file_path, '/tmp/slots_20220601101520.987654.csv')
        file_name = file_path.split('/')[-1]
        lines = open(file_path, 'r', encoding='utf-8').readlines()  # pylint: disable=consider-using-with
        self.assertEqual(len(lines), 3)
        self.assertTrue('booked' in lines[1])
        self.assertTrue(updated_slot.movement.identifier in lines[1])
        self.assertTrue('cancelled' in lines[2])
        self.assertTrue(updated_slot.movement.identifier in lines[2])
        file_content = open(file_path, 'r', encoding='utf-8').read()  # pylint: disable=consider-using-with
        file_lines = file_content.split('\n')
        self.assertEqual(
            file_lines[0],
            "Vendor No,Type,Movement No,Order No,Contract No,Commodity,Material Code,Slot Date Time,Booked Tonnage,Site,Truck,Driver,Grade,Status"  # pylint: disable=line-too-long
        )
        self.assertEqual(
            file_lines[1],
            f'VendorNumber,INLOAD,{updated_slot.movement.identifier},FOIDENT,CCIDENT,Wheat,Best Material,2022-06-01 20:00:00+10:00,15.0,5022,TRUCKREGO,"Admin Root (SR000)",APH1,booked'  # pylint: disable=line-too-long
        )
        self.assertEqual(
            file_lines[2],
            f"VendorNumber,INLOAD,{updated_slot.movement.identifier},FOIDENT,CCIDENT,Wheat,Best Material,2022-06-01 20:00:00+10:00,,5022,TRUCKREGO,,APH1,cancelled"  # pylint: disable=line-too-long
        )
        s3_upload_mock.assert_called_once_with(f'mauri/sftp/slots/{file_name}', file_content)
        sftp_connection_mock.cd.assert_called_once_with('./agrichain/Inbound/TruckSchedule/')
        sftp_connection_mock.put.assert_called_once_with(file_path)
        self.slot.refresh_from_db()
        self.assertEqual(str(self.slot._last_pushed_at), '2022-06-01 10:15:20.987654+00:00')

    @patch('core.services.internal.errbit.ERRBIT_LOGGER.raise_errbit')
    @patch('core.services.external.aws.S3.upload')
    def test_push_task_failure(self, s3_upload_mock, errbit_log_mock):
        # booking
        updated_slot = FreightSlot.update(
            instance=self.slot,
            data={
                'status': 'booked',
                'order_id': self.order.id,
                'commodity_id': 1,
                'grade_id': 1,
                'tonnage': 15,
                'truck_id': self.truck.id,
                'driver_id': self.truck.created_by_id,
                'freight_provider_id': self.truck.company_id
            }
        )

        temp_jobs = Job.objects.filter(status='__temp__')
        self.assertEqual(temp_jobs.count(), 1)

        push_slots_events_to_sftp(False)

        self.assertEqual(temp_jobs.count(), 0)
        updated_jobs = Job.objects.filter(status='__batch_failed__')
        self.assertEqual(updated_jobs.count(), 1)
        files = self.read_files('slots_*')
        self.assertEqual(len(files), 1)
        file_name = files[0].split('/')[-1]
        self.assertTrue(file_name.startswith('slots_'))
        lines = open(files[0], 'r', encoding='utf-8').readlines()  # pylint: disable=consider-using-with
        self.assertEqual(len(lines), 2)
        self.assertTrue('booked' in lines[1])
        self.assertTrue(updated_slot.movement.identifier in lines[1])
        s3_upload_mock.assert_not_called()
        errbit_log_mock.assert_called_once_with(
            f"PushSlotsEventsToSFTP: Unable to push events to SFTP (Exception ('sftp.server.com', 22)) for jobs: {list(updated_jobs.values_list('id', flat=True))}"  # pylint: disable=line-too-long
        )
        self.slot.refresh_from_db()
        self.assertIsNone(self.slot._last_pushed_at)


@tag('job')
class ReadSlotReceiptsFromSFTP(ACTestCase):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        super().setUp()
        self.site_company = CompanyFactory(
            business_name='SiteWala', agrichain_as_vendor_number='VendorNumber', abn='***********'  # mauri
        )
        self.ngr = NgrFactory(ngr_number='Unknown_foobar', company=self.site_company)
        self.provider = CompanyFactory(business_name='TruckWala')
        self.truck = TruckFactory(rego='TRUCKREGO', company=self.provider)
        self.site = CompanySiteFactory(
            name='ExternallyControlledSite',
            _external_connection_configurations={
                'hostname': 'sftp.server.com',
                'username': 'sftpuser',
                'password': 'sftppassword'
            },
            company=self.site_company
        )
        self.order = FreightOrderFactory(identifier='FOIDENT', provider=self.provider, commodity_id=1)
        self.commodity_contract = self.order.commodity_contract
        self.commodity_contract.seller.ngr = NgrFactory(
            company=self.commodity_contract.seller.company, ngr_number='SELLERNGR')
        self.commodity_contract.buyer.ngr = NgrFactory(
            company=self.commodity_contract.buyer.company, ngr_number='BUYERNGR')
        self.commodity_contract.seller.save()
        self.commodity_contract.buyer.save()
        self.order.freight_delivery.consignee.handler = self.site
        self.order.freight_delivery.consignee.save()
        self.slot = FreightSlot(
            type='inload',
            site=self.site,
            start='2022-06-01 10:00:00+00:00',
            end='2022-06-01 10:30:00+00:00',
            status='planned'
        )
        self.slot.save()
        self.assertTrue(self.slot.persisted)

    @patch('core.freights.models.generate_identifier')
    @patch('core.services.external.aws.S3.upload')
    @patch('pysftp.Connection')
    def test_read_task_success(
            self, sftp_connection_klass_mock, s3_upload_mock, gen_identifier_mock
    ):  # pylint: disable=too-many-locals,too-many-statements
        fm_identifier = 'FM123456'
        gen_identifier_mock.return_value = fm_identifier
        context_mock = Mock(execute=Mock())
        cd_mock = Mock()
        cd_mock.__enter__ = Mock(return_value=context_mock)
        cd_mock.__exit__ = Mock(return_value=None)
        test_dir_path = os.path.abspath(os.path.dirname(__file__))
        sample_file_path = test_dir_path + '/samples/TSR1_20220630_093504.csv'

        def cp_file(source_path, dest_path):
            self.assertEqual(source_path, './agrichain/Outbound/TruckScaleReceipt/TSR1_20220630_093504.csv')
            subprocess.call(f'cp {sample_file_path} {dest_path}', shell=True)

        sftp_connection_mock = Mock(
            listdir=Mock(return_value=['TSR1_20220630_093504.csv']),
            get=cp_file,
            rename=Mock()
        )
        sftp_connection_klass_mock.return_value = sftp_connection_mock

        # booking
        updated_slot = FreightSlot.update(
            instance=self.slot,
            data={
                'status': 'booked',
                'order_id': self.order.id,
                'commodity_id': 1,
                'grade_id': 1,
                'tonnage': 15,
                'truck_id': self.truck.id,
                'driver_id': self.truck.created_by_id,
                'freight_provider_id': self.truck.company_id
            }
        )

        self.assertIsNotNone(updated_slot.movement_id)
        movement = updated_slot.movement
        self.assertEqual(movement.identifier, fm_identifier)
        self.assertTrue(self.order.id == updated_slot.order_id == movement.order_id)
        self.assertFalse(movement.active_inloads.exists())
        self.assertFalse(movement.active_outloads.exists())

        results = read_slot_receipts_from_sftp()

        process_file_paths = list(results.keys())
        self.assertEqual(len(process_file_paths), 1)
        processed_file = process_file_paths[0]
        self.assertTrue(processed_file.startswith('/tmp/sftp_receipts_'))
        self.assertTrue(processed_file.endswith('/TSR1_20220630_093504.csv'))
        self.assertTrue(results[processed_file], {'success': True, 'result': [{'result': True, 'errors': {}}]})

        updated_slot.refresh_from_db()

        inload = updated_slot.load
        movement.refresh_from_db()
        self.assertEqual(updated_slot.status, 'completed')
        self.assertEqual(movement.status, 'completed')
        self.assertEqual(inload.grade.name, 'APW1')
        self.assertEqual(float(inload.net_weight), 42.03)
        self.assertEqual(float(inload.gross_weight), 76.34)
        self.assertEqual(float(inload.tare_weight), 34.31)
        self.assertEqual(str(inload.date_time), "2022-06-30 07:24:00+00:00")
        self.assertEqual(inload.specs, {'prgr': 19, 'mogr': 12, 'scrn': 0, 'twt': 35.0})
        self.assertEqual(inload.storage.name, "StorageBinAsSilo")
        self.assertEqual(inload.storage.farm.name, "ExternallyControlledSite")
        self.assertEqual(inload.external_system, "mauri_sftp")
        self.assertIsNotNone(inload.external_reference)

        outload = movement.outload
        self.assertEqual(outload.net_weight, 42.1)
        self.assertEqual(outload.ngr.ngr_number, 'SELLERNGR')
        self.assertEqual(outload.commodity_id, inload.commodity_id)
        self.assertEqual(outload.grade_id, inload.grade_id)
        self.assertEqual(outload.season, inload.season)
        self.assertEqual(outload.storage.is_gate, True)
        self.assertEqual(outload.storage.farm_id, movement.freight_pickup.consignor.handler_id)
        self.assertEqual(outload.type, 'outload')
        self.assertEqual(outload.checkpoint, movement.freight_pickup)
        self.assertEqual(outload.movement, movement)
        self.assertEqual(outload.external_system, "mauri_sftp")
        self.assertIsNotNone(outload.external_reference)

        s3_upload_mock.assert_called_once_with(
            'mauri/sftp/receipts/TSR1_20220630_093504.csv', open(sample_file_path, 'r', encoding='utf-8').read())  # pylint: disable=consider-using-with
        sftp_connection_mock.rename.assert_called_once_with(
            './agrichain/Outbound/TruckScaleReceipt/TSR1_20220630_093504.csv',
            './agrichain/Outbound/TruckScaleReceipt/Archive/TSR1_20220630_093504.csv'
        )

@tag('view')
class FreightSlotCommentsViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        self.company_site = FarmFactory(company=company_with_site)
        self.open_slot = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': self.company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)
        comment = SlotComment.create( params={"slot": self.open_slot,"comment": 'First Comment'})
        self.comment = comment

    def test_get_200(self):
        url = f'/company_sites/{self.company_site.id}/slots/{self.open_slot.id}/comments/'
        response = self.client.get(url, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data[0]['comment'], 'First Comment')

    def test_post_200(self):
        data = {
            "comment": "Test comment"
        }
        url = f'/company_sites/{self.company_site.id}/slots/{self.open_slot.id}/comments/'
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, 200)

@tag('view')
class FreightSlotCommentViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        company_site = FarmFactory(company=company_with_site)
        open_slot = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)
        comment = SlotComment.create( params={"slot": open_slot,"comment": 'First Comment'})
        self.comment = comment

    def test_put_200_update_comment(self):
        updated_data = {
            'comment': 'Updated comment content'
        }

        response = self.client.put(f'/company_sites/comments/{self.comment.id}/', updated_data, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['comment'], updated_data['comment'])

    def test_put_404_comment_not_found(self):
        non_existent_id = 99999
        url = f'/company_sites/comments/{non_existent_id}/'

        response = self.client.put(url, {'comment': 'This should fail'}, format='json')
        self.assertEqual(response.status_code, 404)

@tag('view')
class FreightSlotCanCancelViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        company_site = FarmFactory(company=company_with_site)
        self.open_slot = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):

        slot_id = self.open_slot.id
        url = f'/company_sites/slots/{slot_id}/cancel/'
        response = self.client.get(url, format='json')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {"result": True, "reasons": []})

    def test_get_404(self):

        slot_id = 901111
        url = f'/company_sites/slots/{slot_id}/cancel/'
        response = self.client.get(url, format='json')

        self.assertEqual(response.status_code, 404)

@tag('view')
class FreightSlotStatsViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        company_site_1 = FarmFactory(company=company_with_site)
        self.slot1 = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': company_site_1,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.company_with_site_2 = CompanyFactory(type_id=4)
        company_site_2 = FarmFactory(company=self.company_with_site_2)
        self.slot2 = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': company_site_2,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )

    def test_get_200(self):
        url = '/company_sites/slots/stats/'
        staff_user = Employee.objects.filter(is_staff=True).first()
        admin_token = staff_user.token_key
        response = self.client.get(
            url,
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['all']['count'], 2)

        response = self.client.get(
            url + '?distribution=True',
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['distribution']['all'].first()['count'], 2)

        response = self.client.get(
            url + f'?company_id={self.company_with_site_2.id}',
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['all']['count'], 1)

@tag('view')
class FreightSlotViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        company_site = FarmFactory(company=company_with_site)
        self.slot = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):
        slot_id = self.slot.id
        url = f'/company_sites/slots/{slot_id}/'
        response = self.client.get(url, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['id'], slot_id)

        url = f'/company_sites/slots/{slot_id}/?verbose=True'
        verbose_response = self.client.get(url, format='json')

        self.assertEqual(verbose_response.status_code, 200)
        self.assertEqual(response.data['id'], slot_id)
        self.assertGreater(len(verbose_response.data.keys()), len(response.data.keys()))

@tag('view')
class CompanySiteSettingsViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        self.company_site = FarmFactory(company=company_with_site)
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):
        site_id = self.company_site.id
        url = f'/company_sites/{site_id}/settings/'

        response = self.client.get(url, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertIsNotNone(response.data['id'])

@tag('view')
class CompanySiteFreightSlotHistoryViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        self.company_site = FarmFactory(company=company_with_site)
        self.open_slot = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': self.company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):
        site_id = self.company_site.id
        url = f'/company_sites/{site_id}/slots/{self.open_slot.id}/history/'

        response = self.client.get(url, format='json')

        self.assertEqual(response.status_code, 200)

@tag('view')
class SlotOrdersViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        self.company_site = FarmFactory(company=company_with_site)
        self.freight_order = FreightOrderFactory()
        self.slot1 = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': self.company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
                'order': self.freight_order
            }
        )

        self.grain_order = FreightOrderFactory(type_id=3)
        self.slot2 = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': self.company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b12',
                'commodity_id': 1,
                'grade_id': 1,
                'order': self.grain_order
            }
        )

        self.slot3 = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': self.company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b13',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):
        url = f'/company_sites/slots/{self.slot1.id}/orders/'
        response = self.client.get(url, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["freightOrder"]["id"], self.freight_order.id)
        self.assertIsNone(response.data["grainOrder"])
        self.assertIsNone(response.data["siteOrder"])

        url = f'/company_sites/slots/{self.slot2.id}/orders/'
        response = self.client.get(url, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data["grainOrder"]["id"], self.grain_order.id)
        self.assertIsNone(response.data["freightOrder"])
        self.assertIsNone(response.data["siteOrder"])

        url = f'/company_sites/slots/{self.slot3.id}/orders/'
        response = self.client.get(url, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertIsNone(response.data["freightOrder"])
        self.assertIsNone(response.data["grainOrder"])
        self.assertIsNone(response.data["siteOrder"])


    def test_get_404(self):
        url = f'/company_sites/slots/{9999}/orders/'

        response = self.client.get(url, format='json')

        self.assertEqual(response.status_code, 404)

@tag('view')
class CarrierComparisonReportViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        self.company_site = FarmFactory(company=company_with_site)
        self.slot = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': self.company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):
        url = '/company_sites/reports/carrier_comparison/'
        url += f'?company_id={self.employee.company.id}&start=2024-01-01&end=2024-07-01'
        staff_user = Employee.objects.filter(is_staff=True).first()
        admin_token = staff_user.token_key
        response = self.client.get(
            url,
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)

    def test_get_400(self):
        url = '/company_sites/reports/carrier_comparison/'
        staff_user = Employee.objects.filter(is_staff=True).first()
        admin_token = staff_user.token_key
        response = self.client.get(
            url,
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 400)

        url = f'/company_sites/reports/carrier_comparison/?company_id={self.employee.company.id}'
        response = self.client.get(
            url,
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 400)

@tag('view')
class SiteComparisonReportViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        company_site = FarmFactory(company=company_with_site)
        FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):
        url = '/company_sites/reports/site_comparison/'
        url += f'?company_id={self.employee.company.id}&start=2024-01-01&end=2024-07-01'
        staff_user = Employee.objects.filter(is_staff=True).first()
        admin_token = staff_user.token_key
        response = self.client.get(
            url,
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)

    def test_get_400(self):
        url = '/company_sites/reports/site_comparison/'
        staff_user = Employee.objects.filter(is_staff=True).first()
        admin_token = staff_user.token_key
        response = self.client.get(
            url,
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 400)

        url = f'/company_sites/reports/site_comparison/?company_id={self.employee.company.id}'
        response = self.client.get(
            url,
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 400)

@tag('view')
class SlotOrderDeliveryReportViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        company_site = FarmFactory(company=company_with_site)
        FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):
        url = '/company_sites/reports/order_delivery/'
        staff_user = Employee.objects.filter(is_staff=True).first()
        admin_token = staff_user.token_key
        response = self.client.get(
            url,
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)

@tag('view')
class SiteBookingCancelledSlotsCSVViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        company_site = FarmFactory(company=company_with_site)
        FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):
        url = '/company_sites/slots/booking-cancelled/csv/'
        staff_user = Employee.objects.filter(is_staff=True).first()
        admin_token = staff_user.token_key
        response = self.client.get(
            url,
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )

        self.assertEqual(response.status_code, 200)

@tag('view')
class SiteSlotsCSVViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        self.company_site = FarmFactory(company=company_with_site)
        self.slot = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': self.company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):
        url = '/company_sites/slots/csv/'
        staff_user = Employee.objects.filter(is_staff=True).first()
        admin_token = staff_user.token_key
        response = self.client.get(
            url,
            HTTP_AUTHORIZATION='Token ' + admin_token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)

@tag('view')
class  CompanySiteFreightSlotForwardDeleteViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        self.company_site = FarmFactory(company=company_with_site)
        self.slot = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': self.company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):
        url =  f'/company_sites/{self.company_site.id}/slots/{self.slot.id}/forward/'
        response = self.client.delete(
            url,
            format='json'
        )

        self.assertEqual(response.status_code, 200)

@tag('view')
class FreightSlotTruckConfigViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        self.company_site = FarmFactory(company=company_with_site)
        movement = FreightMovementFactory()
        truck_category = TruckCategory.objects.create()
        self.truck_category_2 = TruckCategory.objects.create()

        self.open_slot = FreightSlot.create(
            {
                'start': '2019-01-01',
                'end': '2019-01-01',
                'site': self.company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
                'movement_id': movement.id
            }
        )
        LoadTruckConfiguration.create({
            "movement": movement,
            "load_type": "inload",
            "category": truck_category
        })
        self.client.force_authenticate(user=self.employee)

    def test_put_200(self):
        url =  f'/company_sites/slots/{self.open_slot.id}/truck/'
        data = {
            'category_id': self.truck_category_2.id,
            'steer_1_point_1': 600,
            'steer_point_5': 650,
            'permit_number': 'PER-2024-0123',
            'declared_mass_limit': 500,
            'accreditation_number': 'ACC-987654',
            'load_sharing': True,
            'notice': True,
            'notice_number': '19',
            'restricted': 'R',
            'type': 'inload'
        }
        response = self.client.put(
            url,
            data,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['category']['id'], data['category_id'])

@tag('view')
class CompanySitePlannedFreightSlotsViewTest(AuthSetup):
    def setUp(self):
        self.employee = EmployeeFactory()
        company_with_site = CompanyFactory(type_id=4)
        self.company_site = FarmFactory(company=company_with_site)
        self.slot1 = FreightSlot.create(
            {
                'start': '2024-01-01',
                'end': '2024-01-01',
                'site': self.company_site,
                'status': 'planned',
                'booking_number': 'b1',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.slot2 = FreightSlot.create(
            {
                'start': '2024-05-01',
                'end': '2024-05-01',
                'site': self.company_site,
                'type': 'inload',
                'status': 'planned',
                'booking_number': 'b23',
                'commodity_id': 1,
                'grade_id': 1,
            }
        )
        self.client.force_authenticate(user=self.employee)

    def test_get_200(self):
        url =  f'/company_sites/{self.company_site.id}/slots/planned/?isRecommendation=True'

        response = self.client.get(url, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)

        url =  f'/company_sites/{self.company_site.id}/slots/planned/?include_slot_id={self.slot2.id}'

        response = self.client.get(url, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        self.assertTrue(any(obj["id"] == self.slot2.id for obj in response.data))

        url =  f'/company_sites/{self.company_site.id}/slots/planned/?include_slot_ids={self.slot2.id},9999'

        response = self.client.get(url, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        self.assertTrue(any(obj["id"] == self.slot2.id for obj in response.data))

@tag('model')
class SlotReportTest(ACTestCase): # pylint: disable=too-many-public-methods,too-many-instance-attributes

    def setUp(self):
        super().setUp()
        self.company1 = CompanyFactory(business_name='Company 1')
        self.company2 = CompanyFactory(business_name='Company 2')
        self.company3 = CompanyFactory(business_name='Company 3')

        self.farm1 = FarmFactory(company=self.company1)
        self.farm2 = FarmFactory(company=self.company1)
        self.farm3 = FarmFactory(company=self.company2)
        self.farm4 = FarmFactory(company=self.company2)

        self.start= '2024-01-01'
        self.end= '2024-07-01'
        self.tz= 'America/New_York'

    def test_get_sites_with_company_id(self):
        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz, company_id=self.company1.id)
        farms = slot_report.get_sites()

        self.assertEqual(len(farms), 2)
        self.assertIn(self.farm1, farms)
        self.assertIn(self.farm2, farms)
        self.assertNotIn(self.farm3, farms)
        self.assertNotIn(self.farm4, farms)

    def test_get_sites_with_site_ids(self):
        site_ids = [self.farm2.id, self.farm3.id]
        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz, site_ids=site_ids)
        farms = slot_report.get_sites()

        self.assertEqual(farms.count(), 2)
        self.assertIn(self.farm2, farms)
        self.assertIn(self.farm3, farms)
        self.assertNotIn(self.farm1, farms)
        self.assertNotIn(self.farm4, farms)

    def test_get_sites_with_single_site_id(self):
        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz, site_id=self.farm4.id)
        farms = slot_report.get_sites()

        self.assertEqual(farms.count(), 1)
        self.assertIn(self.farm4, farms)
        self.assertNotIn(self.farm1, farms)
        self.assertNotIn(self.farm2, farms)
        self.assertNotIn(self.farm3, farms)

    def test_get_providers_with_valid_extra_filters(self):
        extra_filters = {'freight_provider_id__in': [self.company1.id, self.company3.id]}

        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz, extra_filters=extra_filters)
        providers = slot_report.get_providers()

        self.assertEqual(len(providers), 2)
        self.assertIn(self.company1, providers)
        self.assertIn(self.company3, providers)
        self.assertNotIn(self.company2, providers)

    def test_get_providers_with_empty_extra_filters(self):
        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz, extra_filters={})
        providers = slot_report.get_providers()
        self.assertEqual(providers, None)

    def test_get_providers_with_invalid_extra_filters(self):
        extra_filters = {'freight_provider_id__in': [999, 1000]}

        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz, extra_filters=extra_filters)
        providers = slot_report.get_providers()

        self.assertEqual(len(providers), 0)

    def test_append_sites_with_multiple_sites(self):
        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz, company_id=self.company1.id)

        rows = [["Header Row"]]
        slot_report.append_sites(rows)
        self.assertEqual(rows[-3], ["Sites Included"])
        self.assertIn([self.farm1.name], rows)
        self.assertIn([self.farm2.name], rows)
        self.assertNotIn([self.farm3.name], rows)
    def test_append_sites_with_no_sites(self):
        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz, company_id=self.company3.id)

        rows = [["Header Row"]]
        slot_report.append_sites(rows)
        self.assertEqual(len(rows), 1)
        self.assertEqual(rows[0], ["Header Row"])

    def test_append_providers_with_multiple_providers(self):
        slot_report = SlotReport(
            start=self.start, end=self.end, tz=self.tz,
            extra_filters={'freight_provider_id__in': [self.company1.id, self.company3.id]}
        )

        rows = [["Header Row"]]
        slot_report.append_providers(rows)

        self.assertEqual(rows[-3], ["Carriers Included"])
        self.assertIn([self.company1.name], rows)
        self.assertIn([self.company3.name], rows)

    def test_append_providers_with_no_providers(self):
        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz, extra_filters={})

        rows = [["Header Row"]]
        slot_report.append_providers(rows)

        self.assertEqual(len(rows), 1)
        self.assertEqual(rows[0], ["Header Row"])
    @patch("core.company_sites.tests.tests.SlotReport.to_site_comparison_csv_row")
    @patch("core.company_sites.tests.tests.FreightSlot.history.filter")
    def test_get_carrier_comparison_csv_rows_with_multiple_providers(self, mock_freight_filter, mock_csv_row):
        mock_csv_row.return_value = ["Data 1", "Data 2"]
        mock_freight_filter.return_value.values_list.return_value = [self.company1.id]
        site_ids = [self.farm2.id, self.farm3.id]
        extra_filters = {'freight_provider_id__in': [self.company1.id, self.company3.id]}

        rows = SlotReport.get_carrier_comparison_csv_rows(
            start=self.start, end=self.end, tz=self.tz,
            company_id=self.company1.id, site_ids=site_ids, extra_filters=extra_filters
        )

        self.assertIn([self.farm1.name], rows)
        self.assertIn([self.farm2.name], rows)
        self.assertIn(["Overall", "Data 1", "Data 2"], rows)

    @patch("core.company_sites.models.SlotReport.time_period_distribution")
    @patch("core.timezones.utils.DateTimeUtil.localize")
    @patch("core.common.utils.is_continuous_list_of_numbers")
    def test_peak_hours_continous(self, mock_is_continuous, mock_localize, mock_time_period_distribution):
        mock_time_period_distribution.return_value = [
            {'start': datetime.datetime(2024, 3, 12, 8, 0, tzinfo=pytz.UTC), 'count': 5},
            {'start': datetime.datetime(2024, 3, 12, 9, 0, tzinfo=pytz.UTC), 'count': 10},
            {'start': datetime.datetime(2024, 3, 12, 10, 0, tzinfo=pytz.UTC), 'count': 10},
            {'start': datetime.datetime(2024, 3, 12, 11, 0, tzinfo=pytz.UTC), 'count': 7}]
        mock_is_continuous.return_value = True
        mock_localize.side_effect = lambda dt, tz: dt.replace(tzinfo=None)
        slot_report = SlotReport(start=datetime.datetime(2024, 3, 12),
                                 end=datetime.datetime(2024, 3, 13),
                                 tz=pytz.UTC)
        response = slot_report.peak_hours
        self.assertEqual(response, [{'hour_from': 9, 'hour_to': 11}])
    @patch("core.company_sites.models.SlotReport.time_period_distribution")
    @patch("core.timezones.utils.DateTimeUtil.localize")
    @patch("core.common.utils.is_continuous_list_of_numbers")
    def test_peak_hours_non_continuous(self, mock_is_continuous, mock_localize, mock_time_period_distribution):
        mock_time_period_distribution.return_value = [
            {'start': datetime.datetime(2024, 3, 12, 8, 0, tzinfo=pytz.UTC), 'count': 5},
            {'start': datetime.datetime(2024, 3, 12, 10, 0, tzinfo=pytz.UTC), 'count': 10},
            {'start': datetime.datetime(2024, 3, 12, 12, 0, tzinfo=pytz.UTC), 'count': 10},
            {'start': datetime.datetime(2024, 3, 12, 14, 0, tzinfo=pytz.UTC), 'count': 7}
        ]
        mock_is_continuous.return_value = False
        mock_localize.side_effect = lambda dt, tz: dt.replace(tzinfo=None)

        slot_report = SlotReport(start=self.start,end=self.end,tz=self.tz)
        response = slot_report.peak_hours
        expected_response = [
            {'hour_from': 10, 'hour_to': 11},
            {'hour_from': 12, 'hour_to': 13},
        ]
        self.assertEqual(response, expected_response)
    @patch("core.company_sites.models.SlotReport.time_period_distribution")
    @patch("core.timezones.utils.DateTimeUtil.localize")
    @patch("core.common.utils.is_continuous_list_of_numbers")
    def test_peak_hours_text_with_peak_hours(self, mock_is_continuous, mock_localize, mock_time_period_distribution):
        mock_time_period_distribution.return_value = [
            {'start': datetime.datetime(2024, 3, 12, 8, 0, tzinfo=pytz.UTC), 'count': 5},
            {'start': datetime.datetime(2024, 3, 12, 10, 0, tzinfo=pytz.UTC), 'count': 10},
            {'start': datetime.datetime(2024, 3, 12, 12, 0, tzinfo=pytz.UTC), 'count': 10},
            {'start': datetime.datetime(2024, 3, 12, 14, 0, tzinfo=pytz.UTC), 'count': 7},
        ]
        mock_is_continuous.return_value = True
        mock_localize.side_effect = lambda dt, tz: dt.replace(tzinfo=None)

        slot_report = SlotReport(start=self.start,end=self.end,tz=self.tz)
        response = slot_report.peak_hours_text

        expected_response = "10:00 AM - 11:00 AM & 12:00 PM - 01:00 PM"
        self.assertEqual(response, expected_response)

    @patch("core.timezones.utils.DateTimeUtil.timedelta_format")
    def test_loading_times_distribution(self, mock_timedelta_format):

        mock_timedelta_format.side_effect = lambda x: f"{x} sec"

        mock_slot1, mock_slot2, mock_slot3 = [Mock() for _ in range(3)]
        mock_slot1.movement_load_time.return_value = 30.0
        mock_slot2.movement_load_time.return_value = 60.0
        mock_slot3.movement_load_time.return_value = 90.0

        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz)

        slot_report.queryset = [mock_slot1, mock_slot2, mock_slot3]

        result = slot_report.loading_times_distribution()

        self.assertEqual(result["max"], f"{90.0} sec")
        self.assertEqual(result["min"], f"{30.0} sec")
        self.assertEqual(result["avg"], f"{mean([30.0, 60.0, 90.0])} sec")

    @patch("core.company_sites.models.SlotReport.cancelled_count")
    @patch("core.company_sites.models.SlotReport._SlotReport__get_percentage")
    def test_cancelled_percentage(self, mock_get_percentage, mock_cancelled_count):
        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz)

        mock_cancelled_count.return_value = 2
        mock_get_percentage.return_value = 20.0

        result = slot_report.cancelled_percentage

        self.assertEqual(result, 20.0)

    @patch("core.company_sites.models.SlotReport.peak_hours_text", new_callable=PropertyMock)
    @patch("core.company_sites.models.SlotReport.loading_times_distribution")
    @patch("core.company_sites.models.SlotReport.booked_count", new_callable=PropertyMock)
    @patch("core.company_sites.models.SlotReport.complete_count", new_callable=PropertyMock)
    @patch("core.company_sites.models.SlotReport.incomplete_count", new_callable=PropertyMock)
    @patch("core.company_sites.models.SlotReport.cancelled_count", new_callable=PropertyMock)
    @patch("core.company_sites.models.SlotReport.complete_percentage", new_callable=PropertyMock)
    @patch("core.company_sites.models.SlotReport.incomplete_percentage", new_callable=PropertyMock)
    @patch("core.company_sites.models.SlotReport.cancelled_percentage", new_callable=PropertyMock)
    def test_to_site_comparison_csv_row(
        self,
        mock_cancelled_percentage,
        mock_incomplete_percentage,
        mock_complete_percentage,
        mock_cancelled_count,
        mock_incomplete_count,
        mock_complete_count,
        mock_booked_count,
        mock_loading_times_distribution,
        mock_peak_hours_text
    ):

        mock_booked_count.return_value = 50
        mock_complete_count.return_value = 40
        mock_incomplete_count.return_value = 10
        mock_cancelled_count.return_value = 5
        mock_complete_percentage.return_value = 80.0
        mock_incomplete_percentage.return_value = 20.0
        mock_cancelled_percentage.return_value = 10.0
        mock_loading_times_distribution.return_value = {"avg": "1h", "max": "2h", "min": "30m"}
        mock_peak_hours_text.return_value = "09:00 AM - 11:00 AM"

        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz)
        result = slot_report.to_site_comparison_csv_row()

        expected_row = [50,40,10,5,80.0,20.0,10.0,"1h","2h","30m","09:00 AM - 11:00 AM"]

        self.assertEqual(result, expected_row)

    @patch.object(SlotReport, "to_site_comparison_csv_row")
    @patch.object(SlotReport, "append_providers")
    @patch.object(TruckCheckpointLog, "aggregate_spent_time_by_trucks_on_sites")
    @patch.object(TruckCheckpointLog, "waiting_time")
    @patch.object(TruckCheckpointLog, "overall_average_time_spent")
    def test_get_site_comparison_csv_rows_with_company_id(
        self,
        mock_overall_avg_time,
        mock_waiting_time,
        mock_aggregate_time_by_trucks,
        mock_append_providers,
        mock_to_site_comparison_csv_row,
    ):
        mock_aggregate_time_by_trucks.return_value = "1h"
        mock_waiting_time.return_value = "15m"
        mock_overall_avg_time.return_value = "2h"
        mock_to_site_comparison_csv_row.return_value = [
            50, 40, 10, 5, 80.0, 20.0, 10.0, "1h", "2h", "30m", "09:00 AM - 11:00 AM"
            ]

        rows = SlotReport.get_site_comparison_csv_rows(
            start=self.start, end=self.end, tz=self.tz, company_id=self.company1.id
        )

        self.assertIn(self.farm1.name, rows[0])
        self.assertIn(self.farm2.name, rows[1])
        mock_to_site_comparison_csv_row.assert_called()
        mock_aggregate_time_by_trucks.assert_called()
        mock_waiting_time.assert_called()
        mock_overall_avg_time.assert_called()
        mock_append_providers.assert_called()

    @patch.object(SlotReport, "to_site_comparison_csv_row")
    @patch.object(SlotReport, "append_providers")
    @patch.object(TruckCheckpointLog, "aggregate_spent_time_by_trucks_on_sites")
    @patch.object(TruckCheckpointLog, "waiting_time")
    @patch.object(TruckCheckpointLog, "overall_average_time_spent")
    def test_get_site_comparison_csv_rows_with_site_ids(
        self,
        mock_overall_avg_time,
        mock_waiting_time,
        mock_aggregate_time_by_trucks,
        mock_append_providers,
        mock_to_site_comparison_csv_row,
    ):
        mock_aggregate_time_by_trucks.return_value = "1h"
        mock_waiting_time.return_value = "15m"
        mock_overall_avg_time.return_value = "2h"
        mock_to_site_comparison_csv_row.return_value = [
            50, 40, 10, 5, 80.0, 20.0, 10.0, "1h", "2h", "30m", "09:00 AM - 11:00 AM"
            ]
        rows = SlotReport.get_site_comparison_csv_rows(
            start=self.start, end=self.end, tz=self.tz, site_ids=[self.farm1.id, self.farm2.id]
        )

        self.assertIn(self.farm1.name, rows[0])
        self.assertIn(self.farm2.name, rows[1])
        self.assertNotIn(self.farm3.name, rows[1])
        mock_to_site_comparison_csv_row.assert_called()
        mock_aggregate_time_by_trucks.assert_called()
        mock_waiting_time.assert_called()
        mock_overall_avg_time.assert_called()
        mock_append_providers.assert_called()

    def test_get_site_comparison_csv_rows_with_exception(self):
        with self.assertRaises(ValueError):
            SlotReport.get_site_comparison_csv_rows(start=self.start, end=self.end, tz=self.tz)

    @patch.object(SlotReport, 'booked_count', new_callable=PropertyMock)
    def test_get_percentage_zero_total_count(self, mock_booked_count):
        mock_booked_count.return_value = 0
        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz)
        result = slot_report._SlotReport__get_percentage(5)
        self.assertEqual(result, 0.0)
    @patch.object(SlotReport, 'booked_count', new_callable=PropertyMock)
    def test_get_percentage_rounding(self, mock_booked_count):
        mock_booked_count.return_value = 3
        slot_report = SlotReport(start=self.start, end=self.end, tz=self.tz)
        result = slot_report._SlotReport__get_percentage(1)
        self.assertEqual(result, 33.33)


@tag('view')
class CompanySiteFreightSlotMixedDeleteViewTest(AuthSetup):
    def test_delete(self):
        site = FarmFactory()
        site2 = FarmFactory()
        employee = EmployeeFactory(company=site.company)
        random_employee = EmployeeFactory()

        planned_slot1 = FreightSlot(site=site, start='2022-01-01 10:00:00', end='2022-01-01 10:30:00', status='planned')
        planned_slot1.save()
        planned_slot2 = FreightSlot(site=site, start='2023-01-01 10:00:00', end='2023-01-01 10:30:00', status='planned')
        planned_slot2.save()
        planned_slot3 = FreightSlot(
            site=site2, start='2023-01-01 10:00:00', end='2023-01-01 10:30:00', status='planned')
        planned_slot3.save()
        not_planned_slot = FreightSlot(
            site=site, start='2022-01-01 10:00:00', end='2022-01-01 10:30:00', status='booked', freight_provider_id=1)
        not_planned_slot.save()

        self.assertEqual(FreightSlot.objects.count(), 4)


        response = self.client.delete(
            f'/company_sites/{site.id}/slots/mixed-delete/',
            {'slot_ids': [planned_slot1.id, planned_slot2.id, planned_slot3.id, not_planned_slot.id]},
            HTTP_AUTHORIZATION = 'Token ' + employee.refresh_token(),
            format = 'json'
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(FreightSlot.objects.count(), 4)

        response = self.client.delete(
            f'/company_sites/{site.id}/slots/mixed-delete/',
            {'slot_ids': [planned_slot1.id, planned_slot2.id, not_planned_slot.id]},
            HTTP_AUTHORIZATION = 'Token ' + employee.refresh_token(),
            format = 'json'
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(FreightSlot.objects.count(), 4)

        response = self.client.delete(
            f'/company_sites/{site.id}/slots/mixed-delete/',
            {'slot_ids': [planned_slot3.id]},
            HTTP_AUTHORIZATION = 'Token ' + employee.refresh_token(),
            format = 'json'
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(FreightSlot.objects.count(), 4)

        response = self.client.delete(
            f'/company_sites/{site.id}/slots/mixed-delete/',
            {'slot_ids': [planned_slot1.id, planned_slot2.id]},
            HTTP_AUTHORIZATION = 'Token ' + random_employee.refresh_token(),
            format = 'json'
        )
        self.assertEqual(response.status_code, 403)
        self.assertEqual(FreightSlot.objects.count(), 4)

        response = self.client.delete(
            f'/company_sites/{site.id}/slots/mixed-delete/',
            {'slot_ids': [planned_slot1.id, planned_slot2.id]},
            HTTP_AUTHORIZATION = 'Token ' + employee.refresh_token(),
            format = 'json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(sorted(response.data['ids']), sorted([planned_slot1.id, planned_slot2.id]))
        self.assertEqual(FreightSlot.objects.count(), 2)
        self.assertEqual(FreightSlot.objects.filter(id__in=[planned_slot1.id, planned_slot2.id]).exists(), False)
