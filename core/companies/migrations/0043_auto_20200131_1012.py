# Generated by Django 2.1.11 on 2020-01-31 10:12

from django.db import migrations


def update_representer_settings(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    Company.objects.filter(type_id=2).update(can_represent=True)


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0042_auto_20200131_1005'),
    ]

    operations = [
        migrations.RunPython(update_representer_settings)
    ]
