# Generated by Django 4.0.7 on 2022-12-12 07:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0109_rename_variety_mandate_company_is_variety_mandatory_in_cash_price_and_title_transfer_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='is_independent_site_buyer',
            field=models.BooleanField(blank=True, default=False, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='is_selling_to_independent_site_buyer',
            field=models.BooleanField(blank=True, default=False, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='is_independent_site_buyer',
            field=models.BooleanField(blank=True, default=False, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='is_selling_to_independent_site_buyer',
            field=models.BooleanField(blank=True, default=False, null=True),
        ),
    ]
