# Generated by Django 2.1.7 on 2019-06-12 05:23

from django.db import migrations
from django.contrib.contenttypes.models import ContentType

def populate_location_type_and_location_id(apps, schema_editor):
    FreightSlot = apps.get_model('company_sites', 'FreightSlot')
    HistoricalFreightSlot = apps.get_model('company_sites', 'HistoricalFreightSlot')
    content_type = ContentType.objects.filter(model='companysite').first()

    if not content_type:
        return

    for slot in FreightSlot.objects.all():
        slot.location_id = slot.site_id
        slot.location_type_id = content_type.id
        HistoricalFreightSlot.objects.filter(id=slot.id).update(
            location_id=slot.site_id, location_type_id=content_type.id
        )
        slot.save()

    # dormant history for delete slots
    HistoricalFreightSlot.objects.filter(location_id__isnull=True).delete()

class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0046_auto_20190612_0522'),
    ]

    operations = [
        migrations.RunPython(populate_location_type_and_location_id)
    ]
