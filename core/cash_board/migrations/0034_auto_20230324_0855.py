# Generated by Django 4.1.7 on 2023-03-24 08:55

from django.db import migrations
from django.db.models import F
from pydash import compact, get

from core.common.constants import SYSTEM_COMPANY_IDS, AU_ROOT_USER_ID


def update_cashed_out_contracts(apps, schema_editor):
    Contract = apps.get_model('contracts', 'Contract')
    contracts = Contract.objects.filter(cash_price__isnull=False).exclude(
        created_by__company_id=F('seller__company_id')
    ).exclude(created_by__company_id=F('buyer__company_id'))
    contract_ids = contracts.values_list('id', flat=True)
    contracts.update(created_by_id=AU_ROOT_USER_ID, updated_by_id=AU_ROOT_USER_ID, owner_id=AU_ROOT_USER_ID)
    for contract in Contract.objects.filter(id__in=contract_ids):
        viewer_company_ids = compact({
            contract.owner_id,
            get(contract, 'seller.company_id'),
            get(contract, 'seller.represented_by_id'),
            *get(contract, 'seller.ngr.owner_company_ids', []),
            get(contract, 'buyer.company_id'),
            get(contract, 'buyer.represented_by_id'),
            *get(contract, 'buyer.ngr.owner_company_ids', []),
            get(contract, 'created_by.company_id'),
            *SYSTEM_COMPANY_IDS
        })
        Contract.objects.filter(id=contract.id).update(viewer_company_ids=viewer_company_ids)


class Migration(migrations.Migration):

    dependencies = [
        ('cash_board', '0033_alter_cashprices_buyer_alter_cashprices_created_by_and_more'),
    ]

    operations = [
        migrations.RunPython(update_cashed_out_contracts)
    ]
