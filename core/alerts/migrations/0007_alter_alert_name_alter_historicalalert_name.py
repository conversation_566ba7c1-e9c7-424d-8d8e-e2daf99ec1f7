# Generated by Django 4.1.7 on 2023-04-03 07:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0006_alter_historicalrecipient_custom_emails_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='alert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit'), ('inload_done', 'Inload Done'), ('class_1_material', 'Class 1 material')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalalert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit'), ('inload_done', 'Inload Done'), ('class_1_material', 'Class 1 material')], max_length=100),
        ),
    ]
