import factory
from factory import Sequence, SubFactory
from factory.fuzzy import FuzzyText

from core.companies.models import Company, ExternalPortal, CompanyGroup
from core.locations.tests.factories import AddressFactory
from core.companies.constants import FEES


class CompanyFactory(factory.django.DjangoModelFactory):
    abn = FuzzyText(length=11, chars=['1', '2', '3', '4', '5', '6', '7', '8', '9'])
    entity_name = Sequence("Entity-{}".format)
    business_name = Sequence("Business-{}".format)
    mobile = FuzzyText(length=8, prefix='04', chars=['1', '2', '3', '4', '5', '6', '7', '8', '9'])
    address = SubFactory(AddressFactory)

    class Meta:
        model = Company


class CompanyGroupFactory(factory.django.DjangoModelFactory):
    name = Sequence("CompanyGroup-{}".format)
    type = FEES
    owner_company = SubFactory(CompanyFactory)

    class Meta:
        model = CompanyGroup


class ExternalPortalFactory(factory.django.DjangoModelFactory):
    company = SubFactory(CompanyFactory)
    portal = 'CHS Broadbent'
    url = 'https://chs.broadbent.com'
    username = 'username'
    password = 'password'

    class Meta:
        model = ExternalPortal
