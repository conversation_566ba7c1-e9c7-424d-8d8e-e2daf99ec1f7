from drf_yasg import openapi
from drf_yasg.inspectors import SwaggerAutoSchema


start_param = openapi.Parameter(
    'start', openapi.IN_QUERY, description="DATE STRING (YYYY-MM-DD HH:MM:SS)", type=openapi.TYPE_STRING, required=True)
end_param = openapi.Parameter(
    'end', openapi.IN_QUERY, description="DATE STRING (YYYY-MM-DD HH:MM:SS)", type=openapi.TYPE_STRING, required=True)


class ACSwaggerAutoSchema(SwaggerAutoSchema):
    def get_operation(self, operation_keys=None):
        operation = super().get_operation(operation_keys)

        # Define X-API-Key as a global header parameter
        x_api_key_param = openapi.Parameter(
            'X-API-Key',
            openapi.IN_HEADER,
            description="API Key (optional if IP is whitelisted)",
            type=openapi.TYPE_STRING,
            required=False
        )

        # Add X-API-Key header to each operation
        operation['parameters'] = operation.get('parameters', []) + [x_api_key_param]

        return operation
