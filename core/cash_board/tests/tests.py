import os
from datetime import timedelta
from unittest.mock import patch, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, ANY

from dateutil.relativedelta import relativedelta
from django.core.exceptions import ValidationError
from django.db import models
from django.test import tag
from django.utils import timezone
from mock.mock import call

from core.cash_board.models import CashPrices, CashPricesMapping
from core.cash_board.tests.factories import CashPriceFactory
from core.cash_board.mock_data import PAYMENT_TERM_MOCK_DATA, PAYMENT_SCALE_MOCK_DATA
from core.common.constants import AEST_TZ
from core.common.models import CommonFilters
from core.common.tests import ACTestCase, AuthSetup
from core.timezones.utils import DateTimeUtil
from core.companies.tests.factories import CompanyFactory
from core.contracts.models import Party, TitleTransfer
from core.contracts.tests.factories import ContractFactory
from core.farms.tests.factories import FarmFactory, StorageFactory
from core.loads.models import Load
from core.loads.tests.factories import LoadFactory
from core.ngrs.models import Ngr<PERSON>ustainabilityDeclaration
from core.ngrs.tests.factories import NgrFactory
from core.profiles.mock_data import EMPLOYEE_CASH_PRICE_FILTER_MOCK_DATA
from core.profiles.models import Download, EmployeeViewFilters, Employee
from core.profiles.tests.factories import EmployeeFactory


@tag('model')
class CashPricesTest(ACTestCase):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        super().setUp()
        self.buyer_company = CompanyFactory(business_name='Buyer', type_id=1)
        self.buyer_contact = EmployeeFactory(company=self.buyer_company)
        self.buyer_ngr = NgrFactory(company=self.buyer_company)
        self.site = FarmFactory(company=self.buyer_company)
        self.buyer = Party.create(
            {'role': 'Buyer', 'company': self.buyer_company, 'contact': self.buyer_contact, 'ngr': self.buyer_ngr})
        self.now = timezone.now()
        self.params = {
            'site': self.site,
            'commodity_id': 1,
            'season': '22/23',
            'grade_id': 7,
            'payment_term_id': 1,
            'payment_scale_id': 1,
            'buyer': self.buyer,
            'price': 100,
            'limit': 100,
            'track': 'track',
            'start_date_time': self.now,
            'end_date_time': self.now + timedelta(days=1)
        }
        self.cash_price = CashPrices.create(self.params)

    def test_clean(self):
        self.assertTrue(self.cash_price.persisted)
        cash_price = CashPrices(**self.params)
        with self.assertRaises(ValidationError) as ex:
            cash_price.clean()
        self.assertEqual(
            ex.exception.message_dict,
            {'cash_price_exists': ['Cash Price already exists'], 'cash_price_id': [f'{self.cash_price.id}']}
        )

        cash_price = CashPrices(**{**self.params, 'commodity_id': 2})
        cash_price.clean()

    def test_total_transaction_tonnage(self):
        self.assertEqual(self.cash_price.total_transaction_tonnage, 0)

        ContractFactory(cash_price=self.cash_price, tonnage=100)
        ContractFactory(cash_price=self.cash_price, tonnage=50)

        self.assertEqual(self.cash_price.total_transaction_tonnage, 150)

    def test_limit_remaining(self):
        self.assertEqual(self.cash_price.limit_remaining, 100)

        ContractFactory(cash_price=self.cash_price, tonnage=81.33)
        self.assertEqual(self.cash_price.limit_remaining, 18.67)

        ContractFactory(cash_price=self.cash_price, tonnage=49.86)
        self.assertEqual(self.cash_price.limit_remaining, -31.19)

    def test_is_completable(self):
        self.assertFalse(self.cash_price.is_completable)

        ContractFactory(cash_price=self.cash_price, tonnage=50)
        self.assertFalse(self.cash_price.is_completable)

        ContractFactory(cash_price=self.cash_price, tonnage=50)
        self.assertTrue(self.cash_price.is_completable)

    def test_calculate_halt_status_for_repost(self):
        self.assertEqual(self.cash_price.calculate_halt_status_for_repost(), 'inactive')

        ContractFactory(cash_price=self.cash_price, tonnage=50)
        self.assertEqual(self.cash_price.calculate_halt_status_for_repost(), 'partial')

        ContractFactory(cash_price=self.cash_price, tonnage=50)
        self.assertEqual(self.cash_price.calculate_halt_status_for_repost(), 'completed')

        ContractFactory(cash_price=self.cash_price, tonnage=50)
        self.assertEqual(self.cash_price.calculate_halt_status_for_repost(), 'inactive')

    def test_persist(self):
        ContractFactory(cash_price=self.cash_price, tonnage=100)

        payload = {**self.params.copy(), 'site_id': self.site.id}
        payload.pop('site')
        payload.pop('buyer', True)
        payload['buyer'] = {'company_id': self.buyer_company.id, 'contact_id': self.buyer_contact.id}
        payload['mark_complete'] = self.cash_price.id
        payload['created_by_id'] = self.buyer_contact.id
        cash_price = CashPrices.persist(payload)

        self.assertTrue(cash_price.persisted)
        self.assertEqual(cash_price.site_id, self.site.id)
        self.assertEqual(cash_price.buyer.company_id, self.buyer_company.id)

        self.cash_price.refresh_from_db()
        self.assertEqual(self.cash_price.status, 'completed')

    @patch('core.cash_board.models.CashPrices.limit_remaining', new_callable=PropertyMock)
    def test_cannot_create_title_transfer_reasons(self, limit_remaining_mock):
        limit_remaining_mock.side_effect = [100, 0]

        self.assertEqual(self.cash_price.cannot_create_title_transfer_reasons(), [])
        self.assertEqual(self.cash_price.cannot_create_title_transfer_reasons(), ["Cash Price Completed"])

    @patch('core.cash_board.models.CashPrices.limit_remaining', new_callable=PropertyMock)
    def test_mark_complete(self, limit_remaining_mock):
        limit_remaining_mock.side_effect = [100, 0]

        self.cash_price.mark_complete()
        self.cash_price.refresh_from_db()

        self.assertNotEqual(self.cash_price.status, 'completed')

        self.cash_price.mark_complete()
        self.cash_price.refresh_from_db()

        self.assertEqual(self.cash_price.status, 'completed')

    def test_get_available_stock(self):
        storage = StorageFactory(farm=self.cash_price.site)
        seller_ngr = NgrFactory()
        self.assertEqual(self.cash_price.get_available_stock(seller_ngr_id=seller_ngr.id), (0, None))

        load1 = LoadFactory(
            storage=storage, commodity=self.cash_price.commodity, grade=self.cash_price.grade,
            season=self.cash_price.season, ngr=seller_ngr, estimated_net_weight=100, type='inload'
        )
        load2 = LoadFactory(
            storage=storage, commodity=self.cash_price.commodity, grade=self.cash_price.grade,
            season=self.cash_price.season, ngr=seller_ngr, estimated_net_weight=50, type='outload'
        )

        self.assertEqual(self.cash_price.get_available_stock(load_id=load1.id), (50, load1))
        self.assertEqual(self.cash_price.get_available_stock(seller_ngr_id=seller_ngr.id), (50, load2))

    def test_has_enough_stock_by_seller_ngr_id(self):
        load = Load(id=100)
        self.cash_price.get_available_stock = Mock(return_value=(50, load))

        self.assertEqual(self.cash_price.has_enough_stock(seller_ngr_id=123, cash_out_tonnage=100), (False, load))
        self.assertEqual(self.cash_price.has_enough_stock(seller_ngr_id=123, cash_out_tonnage=50), (True, load))

        self.cash_price.get_available_stock.assert_called_with(load_id=None, seller_ngr_id=123)

    def test_has_enough_stock_by_load_id(self):
        load = Load(id=100)
        self.cash_price.get_available_stock = Mock(return_value=(50.15, load))

        self.assertEqual(self.cash_price.has_enough_stock(load_id=100, cash_out_tonnage=100.15), (False, load))
        self.assertEqual(self.cash_price.has_enough_stock(load_id=100, cash_out_tonnage='50.15'), (True, load))
        self.assertEqual(self.cash_price.has_enough_stock(load_id=100, cash_out_tonnage=50.15), (True, load))
        self.assertEqual(self.cash_price.has_enough_stock(load_id=100, cash_out_tonnage=50.15345), (True, load))
        self.assertEqual(self.cash_price.has_enough_stock(load_id=100, cash_out_tonnage='50.15345'), (True, load))
        self.assertEqual(self.cash_price.has_enough_stock(load_id=100, cash_out_tonnage='50.15645'), (False, load))
        self.assertEqual(self.cash_price.has_enough_stock(load_id=100, cash_out_tonnage=50.15645), (False, load))

        self.cash_price.get_available_stock.assert_called_with(load_id=100, seller_ngr_id=None)

    def test_create_contract_and_title_transfer_with_site_show_cash_prices_to_all(self):
        self.cash_price.site.company.show_cash_prices_to_all = True
        self.cash_price.site.company.save()
        storage = StorageFactory(farm=self.cash_price.site)
        seller_ngr = NgrFactory()
        load = LoadFactory(
            storage=storage, commodity=self.cash_price.commodity, grade=self.cash_price.grade,
            season=self.cash_price.season, ngr=seller_ngr, estimated_net_weight=50, type='inload'
        )
        params = {
            'identifier': 'TTCASHPRICE',
            'communication': None,
            'load_id': load.id,
        }

        title_transfer = self.cash_price.create_contract_and_title_transfer(
            user=self.buyer_contact, params=params, tz=AEST_TZ
        )

        self.assertIsInstance(title_transfer, TitleTransfer)
        self.assertTrue(title_transfer.persisted)
        self.assertEqual(title_transfer.identifier, 'TTCASHPRICE')
        self.assertEqual(title_transfer.process_on, DateTimeUtil.localize(timezone.now(), AEST_TZ).date())
        self.assertEqual(title_transfer.commodity_id, self.cash_price.commodity_id)
        self.assertEqual(title_transfer.grade.name, self.cash_price.grade_name)
        self.assertEqual(title_transfer.season, self.cash_price.season)
        self.assertEqual(title_transfer.seller.company.name, seller_ngr.company.name)
        self.assertEqual(title_transfer.seller.ngr_id, seller_ngr.id)
        self.assertEqual(title_transfer.buyer.ngr_id, self.buyer_ngr.id)
        self.assertEqual(title_transfer.buyer.company.name, self.buyer_company.name)
        self.assertEqual(title_transfer.tonnage, 50)

        contract = title_transfer.commodity_contract

        self.assertTrue(contract.identifier.startswith('C'))
        self.assertEqual(contract.document_type.name, 'contract')
        self.assertEqual(contract.type.name, 'fixed_grade')
        self.assertEqual(contract.delivery_onus, 'Seller')
        self.assertEqual(contract.commodity_id, self.cash_price.commodity_id)
        self.assertEqual(contract.grade_id, self.cash_price.grade_id)
        self.assertEqual(contract.season, self.cash_price.season)
        self.assertEqual(contract.price, self.cash_price.price)
        self.assertEqual(contract.payment_term_id, self.cash_price.payment_term_id)
        self.assertEqual(contract.payment_scale_id, self.cash_price.payment_scale_id)
        self.assertEqual(contract.delivery_start_date, DateTimeUtil.localize(timezone.now(), AEST_TZ).date())
        self.assertEqual(contract.delivery_end_date, DateTimeUtil.localize(timezone.now(), AEST_TZ).date())
        self.assertEqual(contract.administration.invoicing, 'Buyer RCTI')
        self.assertEqual(contract.price_point.name, 'delivered_site')
        self.assertEqual(contract.tolerance.name, "Nil Tolerance")
        consignee = contract.consignees.first()
        self.assertEqual(consignee.handler_id, self.cash_price.site_id)

    def test_create_contract_and_title_transfer_with_site_show_cash_prices_to_all_false(self):
        self.cash_price.site.company.show_cash_prices_to_all = False
        self.cash_price.site.company.save()
        storage = StorageFactory(farm=self.cash_price.site)
        seller_ngr = NgrFactory()
        load = LoadFactory(
            storage=storage, commodity=self.cash_price.commodity, grade=self.cash_price.grade,
            season=self.cash_price.season, ngr=seller_ngr, estimated_net_weight=50, type='inload'
        )
        params = {
            'identifier': 'TTCASHPRICE',
            'communication': None,
            'load_id': load.id,
        }

        title_transfer = self.cash_price.create_contract_and_title_transfer(
            user=self.buyer_contact, params=params, tz=AEST_TZ
        )

        self.assertIsInstance(title_transfer, TitleTransfer)
        self.assertTrue(title_transfer.persisted)
        self.assertEqual(title_transfer.identifier, 'TTCASHPRICE')
        self.assertTrue(title_transfer.process_on >= timezone.now().date())
        self.assertEqual(title_transfer.commodity_id, self.cash_price.commodity_id)
        self.assertEqual(title_transfer.grade.name, self.cash_price.grade_name)
        self.assertEqual(title_transfer.season, self.cash_price.season)
        self.assertEqual(title_transfer.seller.company.name, seller_ngr.company.name)
        self.assertEqual(title_transfer.seller.ngr_id, seller_ngr.id)
        self.assertEqual(title_transfer.buyer.ngr_id, self.buyer_ngr.id)
        self.assertEqual(title_transfer.buyer.company.name, self.buyer_company.name)
        self.assertEqual(title_transfer.tonnage, 50)

        contract = title_transfer.commodity_contract

        self.assertTrue(contract.identifier.startswith('C'))
        self.assertEqual(contract.document_type.name, 'contract')
        self.assertEqual(contract.type.name, 'fixed_grade')
        self.assertEqual(contract.delivery_onus, 'Seller')
        self.assertEqual(contract.commodity_id, self.cash_price.commodity_id)
        self.assertEqual(contract.grade_id, self.cash_price.grade_id)
        self.assertEqual(contract.season, self.cash_price.season)
        self.assertEqual(contract.price, self.cash_price.price)
        self.assertEqual(contract.payment_term_id, self.cash_price.payment_term_id)
        self.assertEqual(contract.payment_scale_id, self.cash_price.payment_scale_id)
        self.assertEqual(contract.delivery_start_date, DateTimeUtil.localize(timezone.now(), AEST_TZ).date())
        self.assertEqual(contract.delivery_end_date, DateTimeUtil.localize(timezone.now(), AEST_TZ).date())
        self.assertEqual(contract.administration.invoicing, 'Buyer RCTI')
        self.assertEqual(contract.price_point.name, 'ex_farm_store')
        self.assertEqual(contract.tolerance.name, "Nil Tolerance")
        consignor = contract.consignors.first()
        self.assertEqual(consignor.handler_id, self.cash_price.site_id)

    def test_mark_inactive(self):
        cash_price1 = CashPriceFactory(site=self.site)
        cash_price2 = CashPriceFactory(site=self.site, grade_id=37)
        cash_price3 = CashPriceFactory(site=self.site, grade_id=37, status='completed')
        self.assertEqual(cash_price1.status, 'active')
        self.assertEqual(cash_price2.status, 'active')
        self.assertEqual(cash_price3.status, 'completed')
        CashPrices.mark_inactive([cash_price1.id, cash_price2.id, cash_price3.id])
        cash_price1.refresh_from_db()
        cash_price2.refresh_from_db()
        cash_price3.refresh_from_db()
        self.assertEqual(cash_price1.status, 'inactive')
        self.assertEqual(cash_price2.status, 'inactive')
        self.assertEqual(cash_price3.status, 'completed')

    def test_create_contract_and_title_transfer_for_broker_company(self):
        self.broker_company = CompanyFactory(business_name='Ag Broker', type_id=2)
        self.broker_contact = EmployeeFactory(company=self.broker_company)
        self.cash_price.site.company.show_cash_prices_to_all = True
        self.cash_price.site.company.save()
        self.cash_price.buyer.company.represented_by = self.broker_company
        self.cash_price.buyer.company.save()
        storage = StorageFactory(farm=self.cash_price.site)
        seller_ngr = NgrFactory()
        load = LoadFactory(
            storage=storage, commodity=self.cash_price.commodity, grade=self.cash_price.grade,
            season=self.cash_price.season, ngr=seller_ngr, estimated_net_weight=50, type='inload'
        )
        params = {
            'identifier': 'TTCASHPRICE',
            'communication': None,
            'load_id': load.id,
        }

        title_transfer = self.cash_price.create_contract_and_title_transfer(
            user=self.broker_contact, params=params, tz=AEST_TZ
        )

        self.assertIsInstance(title_transfer, TitleTransfer)
        self.assertTrue(title_transfer.persisted)
        self.assertEqual(title_transfer.identifier, 'TTCASHPRICE')
        self.assertEqual(title_transfer.process_on, DateTimeUtil.localize(timezone.now(), AEST_TZ).date())
        self.assertEqual(title_transfer.commodity_id, self.cash_price.commodity_id)
        self.assertEqual(title_transfer.grade.name, self.cash_price.grade_name)
        self.assertEqual(title_transfer.season, self.cash_price.season)
        self.assertEqual(title_transfer.seller.company.name, seller_ngr.company.name)
        self.assertEqual(title_transfer.seller.ngr_id, seller_ngr.id)
        self.assertEqual(title_transfer.buyer.ngr_id, self.buyer_ngr.id)
        self.assertEqual(title_transfer.buyer.company.name, self.buyer.company.name)
        self.assertEqual(title_transfer.tonnage, 50)

        contract = title_transfer.commodity_contract

        self.assertTrue(contract.identifier.startswith('B'))
        self.assertEqual(contract.document_type.name, 'broker_note')
        self.assertEqual(contract.type.name, 'fixed_grade')
        self.assertEqual(contract.delivery_onus, 'Seller')
        self.assertEqual(contract.commodity_id, self.cash_price.commodity_id)
        self.assertEqual(contract.grade_id, self.cash_price.grade_id)
        self.assertEqual(contract.season, self.cash_price.season)
        self.assertEqual(contract.price, self.cash_price.price)
        self.assertEqual(contract.payment_term_id, self.cash_price.payment_term_id)
        self.assertEqual(contract.payment_scale_id, self.cash_price.payment_scale_id)
        self.assertEqual(contract.delivery_start_date, DateTimeUtil.localize(timezone.now(), AEST_TZ).date())
        self.assertEqual(contract.delivery_end_date, DateTimeUtil.localize(timezone.now(), AEST_TZ).date())
        self.assertEqual(contract.administration.invoicing, 'Buyer RCTI')
        self.assertEqual(contract.price_point.name, 'delivered_site')
        self.assertEqual(contract.tolerance.name, "Nil Tolerance")
        consignee = contract.consignees.first()
        self.assertEqual(consignee.handler_id, self.cash_price.site_id)
        self.assertEqual(contract.administration.brokered_by, self.broker_company)
        self.assertEqual(contract.administration.broker_contact, self.broker_contact)


@tag('view')
class CashPricesViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.trader = CompanyFactory(business_name='Agrichain Trader')
        self.site = FarmFactory(company=self.trader, name='AS Pickup Site')

    def test_get(self):  # pylint: disable=too-many-statements
        # active
        cash_price1 = CashPriceFactory(site=self.site)  # bar1
        cash_price2 = CashPriceFactory(site=self.site, grade_id=37)  # bar2
        cash_price3 = CashPriceFactory(site=self.site, grade_id=38)  # bar3
        cash_price4 = CashPriceFactory(site=self.site, commodity_id=1, grade_id=173)  # APW2

        # inactive
        today = timezone.now()
        yesterday = today - timedelta(days=1)
        tomorrow = today + timedelta(days=1)
        CashPriceFactory(site=self.site, start_date_time=yesterday, end_date_time=yesterday)  # past
        CashPriceFactory(site=self.site, start_date_time=tomorrow)  # future
        CashPriceFactory(site=self.site, status='inactive')
        CashPriceFactory(site=self.site, status='completed')
        CashPriceFactory(site=self.site, status='partial', grade_id=274)  # barx

        self.assertEqual(CashPrices.objects.filter(site=self.site).count(), 9)
        self.assertEqual(CashPrices.active.filter(site=self.site).count(), 4)
        self.assertEqual(CashPrices.archived.filter(site=self.site).count(), 5)

        response = self.client.get(
            '/cash_board/cash-prices/?search=bar1',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['siteId'], self.site.id)
        self.assertEqual(response.data['results'][0]['siteName'], self.site.display_name)
        self.assertEqual(response.data['results'][0]['gradeName'], 'BAR1')

        response = self.client.get(
            '/cash_board/cash-prices/?search=site&order_by=grade_name',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 4)
        self.assertEqual(response.data['results'][0]['gradeName'], 'APW2')
        self.assertEqual(response.data['results'][0]['id'], cash_price4.id)
        self.assertEqual(response.data['results'][1]['gradeName'], 'BAR1')
        self.assertEqual(response.data['results'][1]['id'], cash_price1.id)
        self.assertEqual(response.data['results'][2]['gradeName'], 'BAR2')
        self.assertEqual(response.data['results'][2]['id'], cash_price2.id)
        self.assertEqual(response.data['results'][3]['gradeName'], 'BAR3')
        self.assertEqual(response.data['results'][3]['id'], cash_price3.id)

        response = self.client.get(
            '/cash_board/cash-prices/?search=barley&order_by=grade_name',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 3)
        self.assertEqual(response.data['results'][0]['gradeName'], 'BAR1')
        self.assertEqual(response.data['results'][0]['id'], cash_price1.id)
        self.assertEqual(response.data['results'][1]['gradeName'], 'BAR2')
        self.assertEqual(response.data['results'][1]['id'], cash_price2.id)
        self.assertEqual(response.data['results'][2]['gradeName'], 'BAR3')
        self.assertEqual(response.data['results'][2]['id'], cash_price3.id)

        response = self.client.get(
            '/cash_board/cash-prices/?search=barx',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 0)

        # unauthenticated
        response = self.client.get(
            '/cash_board/cash-prices/?search=barley&order_by=grade_name',
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 3)
        self.assertEqual(response.data['results'][0]['gradeName'], 'BAR1')
        self.assertEqual(response.data['results'][0]['id'], cash_price1.id)
        self.assertEqual(response.data['results'][1]['gradeName'], 'BAR2')
        self.assertEqual(response.data['results'][1]['id'], cash_price2.id)
        self.assertEqual(response.data['results'][2]['gradeName'], 'BAR3')
        self.assertEqual(response.data['results'][2]['id'], cash_price3.id)

        response = self.client.get(
            '/cash_board/cash-prices/?search=barley&grade__id__in=36',
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['gradeName'], 'BAR1')
        self.assertEqual(response.data['results'][0]['id'], cash_price1.id)

        #mobile requests should not be impacted from filters
        EmployeeViewFilters.objects.create(employee=self.employee, filters=EMPLOYEE_CASH_PRICE_FILTER_MOCK_DATA)
        filter_params = CommonFilters(self.employee).conditional_filtering(key='all_cash_price')
        web_response = self.client.get(
            '/cash_board/cash-prices/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        mobile_response = self.client.get(
            '/cash_board/cash-prices/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            HTTP_SYSTEMNAME='android',
            format='json'
        )
        self.assertEqual(filter_params, models.Q(grade__id__in=[38]))
        self.assertEqual(mobile_response.status_code, 200)
        self.assertEqual(web_response.status_code, 200)
        self.assertEqual(len(mobile_response.data['results']), 4) #all results
        self.assertEqual(len(web_response.data['results']), 1) #only filtered results

    @patch('core.cash_board.views.CashPrices')
    def test_post_201(self, cash_price_mock):
        cash_price = CashPriceFactory()
        cash_price_mock.persist = Mock(return_value=cash_price)
        response = self.client.post(
            '/cash_board/cash-prices/',
            {
                'site_id': 1
            },
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data['id'], cash_price.id)
        self.assertEqual(response.data['status'], 'active')
        self.assertEqual(response.data['siteId'], cash_price.site_id)
        cash_price_mock.persist.assert_called_once_with(
            {
                'site_id': 1,
                'created_by_id': self.employee.id,
                'updated_by_id': self.employee.id
            })

    @patch('core.cash_board.views.CashPrices')
    def test_post_400(self, cash_price_mock):
        cash_price_mock.persist.side_effect = Exception('something not there')

        response = self.client.post(
            '/cash_board/cash-prices/',
            {
                'site_id': 1
            },
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'errors': ('something not there',)})
        cash_price_mock.persist.assert_called_once_with(
            {
                'site_id': 1,
                'created_by_id': self.employee.id,
                'updated_by_id': self.employee.id
            })


@tag('view')
class CashPriceViewTest(AuthSetup):
    @patch('core.cash_board.views.CashPrices.limit_remaining', 100)
    def test_delete_200(self):
        cash_price1 = CashPriceFactory(limit=100, status='active')
        cash_price2 = CashPriceFactory(limit=200, status='inactive')
        cash_price3 = CashPriceFactory(limit=100, status='inactive')

        response = self.client.delete(
            '/cash_board/cash-prices/1234567/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 404)

        response = self.client.delete(
            f'/cash_board/cash-prices/{cash_price1.id}/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 404)
        self.assertTrue(CashPrices.objects.filter(id=cash_price1.id).exists())

        response = self.client.delete(
            f'/cash_board/cash-prices/{cash_price2.id}/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertTrue(CashPrices.objects.filter(id=cash_price2.id).exists())

        response = self.client.delete(
            f'/cash_board/cash-prices/{cash_price3.id}/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertFalse(CashPrices.objects.filter(id=cash_price3.id).exists())

    def test_get_200(self):
        site = FarmFactory(company=self.company)
        cash_price = CashPriceFactory(limit=100, status='active', site=site)
        response = self.client.get(
            f'/cash_board/cash-prices/{cash_price.id}/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['id'], cash_price.id)

    def test_get_404(self):
        response = self.client.get(
            '/cash_board/cash-prices/123445/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 404)

        company2 = CompanyFactory(business_name='foobar', show_cash_prices_to_all=False)
        site = FarmFactory(company=company2)
        cash_price = CashPriceFactory(limit=100, status='active', site=site)
        response = self.client.get(
            f'/cash_board/cash-prices/{cash_price.id}/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 403)


@tag('view')
class CanCreateCashBoardViewTest(AuthSetup):
    def test_get_400(self):
        response = self.client.get(
            '/cash_board/1234567/can-create-title-transfer/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.data,
            {
                'result': False,
                'reasons': "Cash Price might not be active. Please refresh your page to load active cash prices.",
            }
        )

        cash_price = CashPriceFactory(status='inactive')

        response = self.client.get(
            f'/cash_board/{cash_price.id}/can-create-title-transfer/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.data,
            {
                'result': False,
                'reasons': "Cash Price might not be active. Please refresh your page to load active cash prices.",
            }
        )

    @patch('core.cash_board.views.CashPrices.cannot_create_title_transfer_reasons')
    def test_get_200_false(self, reasons_mock):
        reasons_mock.return_value = ['Blah Blah']
        cash_price = CashPriceFactory()
        response = self.client.get(
            f'/cash_board/{cash_price.id}/can-create-title-transfer/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.data,
            {
                'result': False,
                'reasons': ['Blah Blah'],
            }
        )

    @patch('core.cash_board.views.CashPrices.cannot_create_title_transfer_reasons')
    def test_get_200_true(self, reasons_mock):
        reasons_mock.return_value = []
        cash_price = CashPriceFactory()
        response = self.client.get(
            f'/cash_board/{cash_price.id}/can-create-title-transfer/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.data,
            {
                'result': True,
                'reasons': [],
            }
        )


@tag('view')
class CashPriceStatusTransitionViewTest(AuthSetup):
    def test_get_404(self):
        response = self.client.get(
            '/cash_board/cash-prices/1234567/foobar/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 404)

    def test_get_200(self):
        cash_price1 = CashPriceFactory(status='completed')
        cash_price2 = CashPriceFactory(status='active')

        response = self.client.get(
            f'/cash_board/cash-prices/{cash_price1.id}/completed/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {
            'errors': 'cash price is already completed'
        })

        response = self.client.get(
            f'/cash_board/cash-prices/{cash_price2.id}/completed/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {
            'result': True
        })
        cash_price2.refresh_from_db()
        self.assertEqual(cash_price2.status, 'completed')


@tag('view')
class CashPriceExistsInStatusViewTest(AuthSetup):
    def test_get(self):
        cash_price1 = CashPriceFactory(status='active')
        cash_price2 = CashPriceFactory(status='inactive')

        response = self.client.get(
            f'/cash_board/cash-prices/{cash_price1.id}/archived/check/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {
            'result': False
        })

        response = self.client.get(
            f'/cash_board/cash-prices/{cash_price1.id}/active/check/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {'result': True})

        response = self.client.get(
            f'/cash_board/cash-prices/{cash_price2.id}/archived/check/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {'result': True})

        response = self.client.get(
            f'/cash_board/cash-prices/{cash_price2.id}/active/check/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {'result': False})


@tag('view')
class TitleTransfersViewTest(AuthSetup):
    def test_get_404(self):
        response = self.client.get(
            '/cash_board/1234567/title-transfers/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 404)

    def test_get_200(self):
        tomorrow = timezone.now() + timedelta(days=1)
        cash_price = CashPriceFactory()
        grower1_company = CompanyFactory()
        buyer_company = CompanyFactory()
        grower1 = EmployeeFactory(company=grower1_company)

        grower1_party = Party.create({
            'role': 'Seller',
            'company': grower1_company,
        })
        buyer1_party = Party.create({
            'role': 'Buyer',
            'company': buyer_company,
        })
        contract = ContractFactory(
            contract_number='C1',
            tonnage=1000,
            cash_price=cash_price,
            seller=grower1_party,
            buyer=buyer1_party
        )

        title_transfer = TitleTransfer.create(
            {
                'commodity_contract': contract,
                'tonnage': 100,
                'identifier': 'T1',
                'process_on': tomorrow,
                'grade': contract.grade,
                'commodity_id': contract.commodity_id,
                'seller': grower1_party,
                'buyer': buyer1_party,
            }
        )
        title_transfer.set_viewer_company_ids()

        response = self.client.get(
            f'/cash_board/{cash_price.id}/title-transfers/',
            HTTP_AUTHORIZATION=f"Token {grower1.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['id'], title_transfer.id)
        self.assertEqual(response.data['results'][0]['contractNumber'], 'C1')

    def test_post_404(self):
        response = self.client.post(
            '/cash_board/1234567/title-transfers/',
            {'site_id': 1},
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 404)

        cash_price = CashPriceFactory(status='inactive')

        response = self.client.post(
            f'/cash_board/{cash_price.id}/title-transfers/',
            {'site_id': 1},
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 404)

    @patch('core.cash_board.views.CashPrices.mark_complete')
    @patch('core.cash_board.views.CashPrices.create_contract_and_title_transfer')
    @patch('core.cash_board.views.CashPrices.has_enough_stock')
    def test_post_201(
            self, has_enough_stock_mock, create_contract_and_title_transfer_mock, mark_complete_mock
    ):
        cash_price = CashPriceFactory(sustainable_commodity=True, price=101)
        ngr = NgrFactory()
        NgrSustainabilityDeclaration.create(
            {
                'ngr': ngr,
                'commodity': cash_price.commodity,
                'season': cash_price.season,
            }
        )
        load = LoadFactory(extras={'foo': 'bar'})
        has_enough_stock_mock.return_value = (True, load)
        title_transfer_mock = Mock(persisted=True, to_dict=Mock(return_value={'identifier': 'TT1'}))
        title_transfer_mock.id = 'tt-id'
        create_contract_and_title_transfer_mock.return_value = title_transfer_mock

        payload = {'seller': {'ngr_id': ngr.id}, 'load_id': load.id, 'tonnage': 100}
        response = self.client.post(
            f'/cash_board/{cash_price.id}/title-transfers/',
            payload,
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data, {'identifier': 'TT1'})
        load.refresh_from_db()
        self.assertEqual(
            load.extras,
            {'foo': 'bar', 'cashprice_price': 101, 'cashprice_id': cash_price.id, 'title_transfer_id': 'tt-id'}
        )
        has_enough_stock_mock.assert_called_once_with(load.id, ngr.id, 100)
        create_contract_and_title_transfer_mock.assert_called_once_with(
            self.employee, {**payload, 'created_by_id': self.employee.id, 'updated_by_id': self.employee.id}, AEST_TZ,
            is_cashed_out=True
        )
        mark_complete_mock.assert_called_once()

    @patch('core.cash_board.views.CashPrices.mark_complete')
    @patch('core.cash_board.views.CashPrices.create_contract_and_title_transfer')
    @patch('core.cash_board.views.CashPrices.has_enough_stock')
    def test_post_400_sustainable_commodity(
            self, has_enough_stock_mock, create_contract_and_title_transfer_mock, mark_complete_mock
    ):
        cash_price = CashPriceFactory(sustainable_commodity=True, price=101)
        ngr = NgrFactory()

        payload = {'seller': {'ngr_id': 12344553}, 'load_id': 123, 'tonnage': 100}
        response = self.client.post(
            f'/cash_board/{cash_price.id}/title-transfers/',
            payload,
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'errors': "NGR is not valid"})

        payload = {'seller': {'ngr_id': ngr.id}, 'load_id': 123, 'tonnage': 100}
        response = self.client.post(
            f'/cash_board/{cash_price.id}/title-transfers/',
            payload,
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.data,
            {
                'errors': "Sorry, we are unable to complete your request. "
                          "Cashing out against a sustainable commodity cash price requires a self declaration "
                          "to be completed on the NGR.com website prior to cashing."
            }
        )
        has_enough_stock_mock.assert_not_called()
        create_contract_and_title_transfer_mock.assert_not_called()
        mark_complete_mock.assert_not_called()

    @patch('core.cash_board.views.CashPrices.mark_complete')
    @patch('core.cash_board.views.CashPrices.create_contract_and_title_transfer')
    @patch('core.cash_board.views.CashPrices.has_enough_stock')
    def test_post_400_not_enough_stock(
            self, has_enough_stock_mock, create_contract_and_title_transfer_mock, mark_complete_mock
    ):
        has_enough_stock_mock.return_value = (False, None)
        cash_price = CashPriceFactory(sustainable_commodity=False, price=101)
        payload = {'seller': {'ngr_id': 12344553}, 'load_id': 123, 'tonnage': 100}
        response = self.client.post(
            f'/cash_board/{cash_price.id}/title-transfers/',
            payload,
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'errors': "You don't have enough stock"})

        has_enough_stock_mock.assert_called_once_with(123, 12344553, 100)
        create_contract_and_title_transfer_mock.assert_not_called()
        mark_complete_mock.assert_not_called()

    @patch('core.cash_board.views.CashPrices.mark_complete')
    @patch('core.cash_board.views.CashPrices.create_contract_and_title_transfer')
    @patch('core.cash_board.views.CashPrices.has_enough_stock')
    def test_post_400_title_transfer_failed(
            self, has_enough_stock_mock, create_contract_and_title_transfer_mock, mark_complete_mock
    ):
        has_enough_stock_mock.return_value = (True, None)
        create_contract_and_title_transfer_mock.return_value = Mock(persisted=False)
        cash_price = CashPriceFactory(sustainable_commodity=False, price=101)
        payload = {'seller': {'ngr_id': 12344553}, 'load_id': 123, 'tonnage': 100}
        response = self.client.post(
            f'/cash_board/{cash_price.id}/title-transfers/',
            payload,
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'errors': "Cannot cash out more than remaining"})

        has_enough_stock_mock.assert_called_once_with(123, 12344553, 100)
        create_contract_and_title_transfer_mock.assert_called_once_with(
            self.employee, {**payload, 'created_by_id': self.employee.id, 'updated_by_id': self.employee.id}, AEST_TZ,
            is_cashed_out=True
        )
        mark_complete_mock.assert_not_called()

    def test_get_200_company_visibility(self): # pylint: disable=too-many-locals
        tomorrow = timezone.now() + timedelta(days=1)
        cash_price = CashPriceFactory()

        grower1_company = CompanyFactory()
        grower2_company = CompanyFactory()
        buyer_company = CompanyFactory()
        site_owner_company = CompanyFactory()

        grower1 = EmployeeFactory(company=grower1_company)
        grower2 = EmployeeFactory(company=grower2_company)
        buyer = EmployeeFactory(company=buyer_company)
        site_owner = EmployeeFactory(company=site_owner_company)

        farm1 = FarmFactory(company=site_owner_company)
        farm2 = FarmFactory(company=site_owner_company)

        storage1 = StorageFactory(farm=farm1)
        storage2 = StorageFactory(farm=farm2)

        grower1_party = Party.create({
            'role': 'Seller',
            'company': grower1_company,
        })
        grower2_party = Party.create({
            'role': 'Seller',
            'company': grower2_company,
        })
        buyer1_party = Party.create({
            'role': 'Buyer',
            'company': buyer_company,
        })
        buyer2_party = Party.create({
            'role': 'Buyer',
            'company': buyer_company,
        })

        contract1 = ContractFactory(
            contract_number='C1',
            tonnage=1000,
            cash_price=cash_price,
            seller=grower1_party,
            buyer=buyer1_party
        )
        contract2 = ContractFactory(
            contract_number='C2',
            tonnage=500,
            cash_price=cash_price,
            seller=grower2_party,
            buyer=buyer2_party
        )

        title_transfer1 = TitleTransfer.create({
            'commodity_contract': contract1,
            'tonnage': 100,
            'identifier': 'T1',
            'process_on': tomorrow,
            'grade': contract1.grade,
            'commodity_id': contract1.commodity_id,
            'created_by': grower1,
            'seller': grower1_party,
            'buyer': buyer1_party,
            'storage': storage1,
            'bhc_site': farm1
        })
        title_transfer1.set_viewer_company_ids()

        title_transfer2 = TitleTransfer.create({
            'commodity_contract': contract2,
            'tonnage': 100,
            'identifier': 'T2344343',
            'process_on': tomorrow,
            'grade': contract2.grade,
            'commodity_id': contract2.commodity_id,
            'created_by': grower2,
            'seller': grower2_party,
            'buyer': buyer2_party,
            'storage': storage2,
            'bhc_site': farm2
        })
        title_transfer2.set_viewer_company_ids()

        response = self.client.get(
            f'/cash_board/{cash_price.id}/title-transfers/',
            HTTP_AUTHORIZATION=f"Token {grower1.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['id'], title_transfer1.id)
        self.assertEqual(response.data['results'][0]['contractNumber'], 'C1')

        response = self.client.get(
            f'/cash_board/{cash_price.id}/title-transfers/',
            HTTP_AUTHORIZATION=f"Token {grower2.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['id'], title_transfer2.id)
        self.assertEqual(response.data['results'][0]['contractNumber'], 'C2')

        response = self.client.get(
            f'/cash_board/{cash_price.id}/title-transfers/',
            HTTP_AUTHORIZATION=f"Token {buyer.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 2)
        title_transfer_ids = {tt['id'] for tt in response.data['results']}
        self.assertEqual(
            title_transfer_ids,
            {title_transfer1.id, title_transfer2.id}
        )

        response = self.client.get(
            f'/cash_board/{cash_price.id}/title-transfers/',
            HTTP_AUTHORIZATION=f"Token {site_owner.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data['results']), 2)
        title_transfer_ids = {tt['id'] for tt in response.data['results']}
        self.assertEqual(
            title_transfer_ids,
            {title_transfer1.id, title_transfer2.id}
        )

@tag('view')
class UploadCashPricesViewTest(AuthSetup):

    @patch('core.cash_board.views.Download')
    @patch('core.cash_board.models.S3')
    def test_generate_cash_prices_csv_success(self, s3_model_mock, download_mock):  # pylint: disable=too-many-locals
        CashPricesMapping.create(
            {
                'upload_cashboard_info': {
                    'grade': {},
                    'payment_term': {},
                    'payment_scale': {},
                    'track': 'Foo-Track'
                }
            }
        )
        trader = CompanyFactory(business_name='Agrichain Trader')
        site = FarmFactory(company=trader, name='AS Pickup Site')
        trader.approved_buyers.add(self.employee.company)
        existing_count = CashPrices.objects.count()
        download = Download.create({
            'employee': self.employee,
            'module': 'cash_prices',
            'name': 'cash_prices_upload',
       })

        self.assertTrue(download.persisted)
        download_mock.create.return_value = download
        download_mock.filter.return_value = download_mock
        download_mock.select_related.return_value = download_mock
        download_mock.get.return_value = download
        filepath = os.path.dirname(__file__) + '/samples/cash_prices_upload.csv'
        file = open(filepath, 'r', encoding='UTF-8')  # pylint: disable=consider-using-with
        response = self.client.post(
            '/cash_board/cash-prices/csv/',
            {'file': file},
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='multipart'
        )
        download.refresh_from_db()
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data, {'failures': 0, 'success': 3, 'url': ANY})
        self.assertEqual(download.status, 'ready')

        download_path = f'cash/prices/csvs/{self.employee.id}/cash_prices_upload'
        s3_model_mock.upload.assert_called_once_with(download_path, ANY)
        resulted_csv = s3_model_mock.upload.call_args[0][1].readlines()
        self.assertEqual(len(resulted_csv), 4)
        # pylint: disable=line-too-long
        self.assertEqual(
            resulted_csv,
            [
                b'Location,Location Owner,Grade,Season,Contract payment terms,Contract payment scales,Contract terms and conditions,Price,Qty,Start date,End date,Sustainable,Buyer (Optional) - Leave blank if uploading for your company. Enter buyer company name if posting on behalf of Buyer,Status,Failure Reason\r\n',  # pylint: disable=line-too-long
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,100.00,02/11/22,04/11/26,Y,,Success,\r\n',
                b'AS Pickup Site,Agrichain Trader,bar2,22/23,5 DEOW of Delivery,Flat,,100.00,100.00,02/11/22,04/11/26,N,,Success,\r\n',
                b'as pickup site,agrichain trader,bar3,22/23,5 deow of Delivery,flat,,100.00,100.00,02/11/22,04/11/26,y,,Success,\r\n'
            ]
        )
        # pylint: enable=line-too-long

        new_count = existing_count + 3
        self.assertEqual(CashPrices.objects.count(), new_count)
        cash_prices_created = CashPrices.objects.filter(created_by=self.employee)
        self.assertEqual(cash_prices_created.count(), new_count)

        # self.assertEqual(
        #     cash_prices_created.filter(start_date_time='2022-11-01 13:00:00+0000').count(), new_count)
        # in AEDT this works, in AEST subtract 1 hour from time string
        self.assertEqual(
            cash_prices_created.filter(end_date_time='2026-11-04 13:59').count(), new_count)

        cash_prices = site.cashprices_set.all()
        self.assertEqual(cash_prices.count(), 3)
        self.assertEqual(set(cash_prices.values_list('commodity__name', flat=True)), {'barley'})
        self.assertEqual(set(cash_prices.values_list('buyer__company_id', flat=True)),
                         {self.employee.company_id})
        self.assertEqual(set(cash_prices.values_list('buyer__ngr__ngr_number', flat=True)),
                         {'UNKNOWN_Foo Corporation'})

    @patch('core.cash_board.views.Download')
    @patch('core.cash_board.models.S3')
    def test_generate_cash_prices_csv_failure(self, s3_model_mock, download_mock):  # pylint: disable=too-many-locals
        download = Download.create({
                                       'employee': self.employee,
                                       'module': 'cash_prices',
                                       'name': 'cash_prices_upload'
                                   })

        self.assertTrue(download.persisted)
        download_mock.create.return_value = download
        download_mock.filter.return_value = download_mock
        download_mock.select_related.return_value = download_mock
        download_mock.get.return_value = download
        filepath = os.path.dirname(__file__) + '/samples/cash_prices_upload.csv'
        file = open(filepath, 'r', encoding='UTF-8')  # pylint: disable=consider-using-with
        response = self.client.post(
            '/cash_board/cash-prices/csv/',
            {'file': file},
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='multipart'
        )
        download.refresh_from_db()
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data, {'failures': 3, 'success': 0, 'url': ANY})
        self.assertEqual(download.status, 'ready')

        download_path = f'cash/prices/csvs/{self.employee.id}/cash_prices_upload'
        s3_model_mock.upload.assert_called_once_with(download_path, ANY)
        resulted_csv = s3_model_mock.upload.call_args[0][1].readlines()
        self.assertEqual(len(resulted_csv), 4)
        # pylint: disable=line-too-long
        self.assertEqual(
            resulted_csv,
            [
                b'Location,Location Owner,Grade,Season,Contract payment terms,Contract payment scales,Contract terms and conditions,Price,Qty,Start date,End date,Sustainable,Buyer (Optional) - Leave blank if uploading for your company. Enter buyer company name if posting on behalf of Buyer,Status,Failure Reason\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,100.00,02/11/22,04/11/26,Y,,Failure,Location Owner did not match a company name on AgriChain\r\n',
                b'AS Pickup Site,Agrichain Trader,bar2,22/23,5 DEOW of Delivery,Flat,,100.00,100.00,02/11/22,04/11/26,N,,Failure,Location Owner did not match a company name on AgriChain\r\n',
                b'as pickup site,agrichain trader,bar3,22/23,5 deow of Delivery,flat,,100.00,100.00,02/11/22,04/11/26,y,,Failure,Location Owner did not match a company name on AgriChain\r\n'
            ]
        )
        # pylint: enable=line-too-long

    @patch('core.cash_board.views.Download')
    @patch('core.cash_board.models.S3')
    @patch('core.cash_board.models.CashPrices.validate_cash_price_upload_csv')
    def test_generate_cash_prices_csv_all_scenarios(self, validate_cash_prices_mock, s3_model_mock, download_mock):  # pylint: disable=too-many-locals, line-too-long
        validate_cash_prices_mock.return_value = []

        CashPricesMapping.create(
            {
                'upload_cashboard_info': {
                    'grade': {},
                    'track': 'MGC',
                    'payment_term': PAYMENT_TERM_MOCK_DATA,
                    'payment_scale': PAYMENT_SCALE_MOCK_DATA
                }
            }
        )
        trader = CompanyFactory(business_name='Agrichain Trader')
        site = FarmFactory(company=trader, name='AS Pickup Site')
        site2 = FarmFactory(company=trader, name='Bendigo Central', is_cash_prices_enabled=False)
        trader.approved_buyers.add(self.employee.company)
        existing_count = CashPrices.objects.count()
        access_grain = CompanyFactory(business_name='Access Grain Pty Ltd')  # not approved buyer
        FarmFactory(company=access_grain, name='Berriwillock')
        download = Download.create({
            'employee': self.employee,
            'module': 'cash_prices',
            'name': 'cash_prices_upload',
        })

        self.assertTrue(download.persisted)
        download_mock.create.return_value = download
        download_mock.filter.return_value = download_mock
        download_mock.select_related.return_value = download_mock
        download_mock.get.return_value = download
        filepath = os.path.dirname(__file__) + '/samples/cash_prices_csv_upload_all_scenarios.csv'
        file = open(filepath, 'r', encoding='UTF-8')  # pylint: disable=consider-using-with
        today = timezone.now().date()
        response = self.client.post(
            '/cash_board/cash-prices/csv/',
            {'file': file},
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='multipart'
        )
        download.refresh_from_db()
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data, {'failures': 44, 'success': 15, 'url': ANY})
        self.assertEqual(download.status, 'ready')

        download_path = f'cash/prices/csvs/{self.employee.id}/cash_prices_upload'
        s3_model_mock.upload.assert_called_once_with(download_path, ANY)
        resulted_csv = s3_model_mock.upload.call_args[0][1].readlines()
        self.assertEqual(len(resulted_csv), 63)  # headers are split in two lines due to \n character in buyer column name  # pylint: disable=line-too-long
        self.assertEqual(CashPrices.objects.count(), existing_count + 15)
        self.assertEqual(CashPrices.objects.filter(created_by=self.employee).count(), existing_count + 15)
        # pylint: disable=line-too-long
        self.assertEqual(
            resulted_csv[0:16],
            [
                b'Location,Location Owner,Grade,Season,Contract payment terms,Contract payment scales,Contract terms and conditions,Price,Qty,Start date,End date,Sustainable,Buyer (Optional) - Leave blank if uploading for your company. Enter buyer company name if posting on behalf of Buyer,Status,Failure Reason\r\n',
                b'AS Pickup Site,AgriChain Trader ,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Success,\r\n',
                b'AS Pickup Site, Agrichain Trader,bar2,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,100.00,04.11.22,05.11.25,N,,Success,\r\n',
                b'as pickup site,agrichain  trader,bar3,22/23,5 deow of Delivery,flat ,Optional free text,100.00,100.,04-11-22,05-11-25,y,,Success,\r\n',
                b' as pickup site,agrichain  trader,sp1,2022/2023,5 deow of Delivery , flat,Optional free text,100,1000,04/11/2022,04/11/2025,n,,Success,\r\n',
                b'as pickup site ,agrichain  trader,bks1,22/23 , 5 deow of Delivery,Flat,Optional free text,100.0,1000.0,4/11/2022,4/11/2025,Yes,,Success,\r\n',
                b'as  pickup  site,agrichain  trader,cantw , 2022/23,5 deow of  Delivery,Flat,Optional free text,100.23,1000,04-11-2022,04-11-2025,T,,Success,\r\n',
                b'as pickup site,agrichain  trader ,cantw ,21/22,12 days End of Week,AOF pay scale basis 42% oil and 0% admix,Optional free text,100.2345,100,04/11/22,04/11/25,True,,Success,\r\n',
                b'as pickup site, agrichain  trader, cantw,22/23,14 days End of Week,AOF pay scale basis 42% oil (cap 46%) and 0%  admix,Optional free text,0100.34,0100,04/11/22 ,04/11/25 ,t,,Success,\r\n',
                b'as pickup site,agrichain  trader ,can1 ,21/22,12 days End of Week,"AOF - Basis 42% Oil, 0% Admix",Optional free text,100.2345,100,04/11/22,04/11/25,True,,Success,\r\n',
                b'as pickup site, agrichain  trader, can2,22/23,14 days End of Week,"AOF - Basis 42% Oil (Cap 46%), 0% Admix",Optional free text,0100.34,0100,04/11/22 ,04/11/25 ,t,,Success,\r\n',
                b'as pickup site,agrichain   trader,bar1,23/24,14 days end of month,Flat,Optional free text,987.98,987, 04/11/22, 04/11/25,No,,Success,\r\n',
                b'as pickup site,agrichain  trader,cm2,22 / 23,30 days End of Week,Flat,Optional free text,232.99,232,04/11/22,04/11/25,F,,Success,\r\n',
                b'as pickup site,agrichain  trader,cm1,2022/2023,2 days End of Week,Flat,,100.00,1000.00,04/11/22,04/11/25,False ,,Success,\r\n',
                b'as pickup site,agrichain  trader,pl1,2022/2023,2 days End of Week,Flat,,100.00,1000.00,04/11/22,04/11/25,f,,Success,\r\n',
                b'as pickup site,agrichain  trader,malt1,2022/2023,2 days End of Week,Flat,,100.00,1000.00,04/11/22,04/11/25 13:20,f,,Success,\r\n'
            ]
        )
        self.assertEqual(
            resulted_csv[16:],
            [
                b',AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Location cannot be blank\r\n',
                b'Narromine Qube Agri,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Location did not match a site name on AgriChain\r\n',
                b'AS Pickup Site,,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Location owner cannot be blank\r\n',
                b'AS Pickup Site,AgriChain Trade,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Location Owner did not match a company name on AgriChain\r\n',
                b'AS Pickup Site,AgriChain Trader,,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Grade cannot be blank\r\n',
                b'AS Pickup Site,AgriChain Trader,bar10,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Grade did not match a grade name on AgriChain\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Sustainable,,Failure,Sustainable Value is not amongst the allowed Values:; Y/Yes/T/True/N/No/F/False\r\n',
                b'AS Pickup Site,AgriChain Trader,PRIME,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Sustainable check is not allowed for this commodity\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Season cannot be blank\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Contract payment terms cannot be blank\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DAYs of delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Contract Payment Term did not match payment term on AgriChain\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Contract payment scales cannot be blank\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,42% Oil and 0% admix,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,Contract Payment Scale did not match payment scale on AgriChain\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,"AOF - Basis 42% Oil (Cap 46%), 0% Admix",Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,This cash price with exact same details already exists\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,,1000.00,04/11/22,04/11/25,Y,,Failure,Price cannot be blank\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,-100,1000.00,04/11/22,04/11/25,Y,,Failure,Price is not valid\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,(100),1000.00,04/11/22,04/11/25,Y,,Failure,Price is not valid\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,abc,1000.00,04/11/22,04/11/25,Y,,Failure,Price is not valid\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100/3,1000.00,04/11/22,04/11/25,Y,,Failure,Price is not valid\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00.00,1000.00,04/11/22,04/11/25,Y,,Failure,Price is not valid\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,,04/11/22,04/11/25,Y,,Failure,Qty cannot be blank\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,-1000,04/11/22,04/11/25,Y,,Failure,Qty is not valid\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,(1000),04/11/22,04/11/25,Y,,Failure,Qty is not valid\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,a1000,04/11/22,04/11/25,Y,,Failure,Qty is not valid\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000/3,04/11/22,04/11/25,Y,,Failure,Qty is not valid\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00.00,04/11/22,04/11/25,Y,,Failure,Qty is not valid\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,,04/11/25,Y,,Failure,Start date cannot be blank\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,4-Nov-2022,04/11/25,Y,,Failure,This cash price with exact same details already exists\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,4-Aug-2026,04/10/26,Y,,Failure,Future cash prices cannot be added\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,4 Nov 22,04/11/25,Y,,Failure,This cash price with exact same details already exists\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,041122,04/11/25,Y,,Failure,This cash price with exact same details already exists\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,,Y,,Failure,End date cannot be blank\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,05/07/28,4-Nov-2024,Y,,Failure,Future cash prices cannot be added\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,05/07/28,4 Nov,Y,,Failure,Future cash prices cannot be added\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,05/07/28,4 Nov 24,Y,,Failure,Future cash prices cannot be added\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,05/07/28,041122,Y,,Failure,Future cash prices cannot be added\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,05/11/28,04/11/25,Y,,Failure,Future cash prices cannot be added\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,05/07/28,04/11/21,Y,,Failure,Future cash prices cannot be added\r\n',
                b'AS Pickup Site,AgriChain Trader,APH2,21/22,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/22,Y,,Failure,Past Cash Prices are not allowed\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/30,Y,,Failure,Invalid End date. Maximum value of End date can be {}.\r\n'.decode().format(
                    (today + relativedelta(years=5)).strftime("%d-%m-%Y")).encode(),
                b'AS Pickup Site,AgriChain Trader ,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/25,Y,,Failure,This cash price with exact same details already exists\r\n',
                b'Berriwillock,Access Grain Pty Ltd,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,04/11/22,04/11/24,Y,,Failure,You are not an approved buyer. Please contact site manager\r\n',
                b'Bendigo Central,agrichain  trader,pl1,2022/2023,2 days End of Week,Flat,,100,1000,04/11/2022,04/11/2024,f,,Failure,Cash Price posting is currently disabled for this site.\r\n',
                b'AS Pickup Site,AgriChain Trader,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,1000.00,4/10/24,04/11/24 25:10,Y,,Failure,"Invalid End Date. End Date should be in any of the following formats: \n',
                b'DD/MM/YY HH:MM \n', b'DD-MM-YY HH:MM \n', b'DD.MM.YY HH:MM"\r\n'
            ]
        )
        # pylint: enable=line-too-long

        self.assertEqual(CashPrices.objects.filter(site=site).count(), 15)
        self.assertEqual(CashPrices.objects.filter(site=site2).count(), 0)
        self.assertEqual(
            DateTimeUtil.localize_time(CashPrices.objects.filter(site=site).last().end_date_time, AEST_TZ),
            '02:20 PM'
        )

    @patch('core.cash_board.views.Download')
    @patch('core.cash_board.models.S3')
    @patch('core.cash_board.models.CashPrices.validate_cash_price_upload_csv')
    def test_generate_cash_prices_csv_all_scenarios_with_optional_field(self, validate_cash_prices_mock, s3_mock, download_mock):  # pylint: disable=too-many-locals, line-too-long
        validate_cash_prices_mock.return_value = []

        CashPricesMapping.create(
            {
                'upload_cashboard_info': {
                    'grade': {},
                    'track': 'MGC',
                    'payment_term': PAYMENT_TERM_MOCK_DATA,
                    'payment_scale': PAYMENT_SCALE_MOCK_DATA
                }
            }
        )
        from core.ngrs.models import Ngr
        trader = CompanyFactory(business_name='Agrichain Trader')
        FarmFactory(company=trader, name='AS Pickup Site')
        trader.approved_buyers.add(self.employee.company)

        existing_count = CashPrices.objects.count()
        agrichain_grower = CompanyFactory(business_name='Agrichain Grower')
        Ngr.create_unknown(agrichain_grower)
        grower_unknown_ngr = Ngr.objects.filter(ngr_number__icontains='UNKNOWN_', company=agrichain_grower).first()
        NgrFactory(ngr_number="55555588", company=agrichain_grower)
        grower_ngr_1 = NgrFactory(ngr_number="55555566", company=agrichain_grower)

        EmployeeFactory(company=agrichain_grower)

        NgrFactory(ngr_number="11111133", company=self.company)
        user_ngr_1 = NgrFactory(ngr_number="34444444", company=self.company)
        FarmFactory(company=self.company, name='Foo Farm')
        download = Download.create({
            'employee': self.employee,
            'module': 'cash_prices',
            'name': 'cash_prices_upload',
        })

        self.assertTrue(download.persisted)
        download_mock.create.return_value = download
        download_mock.filter.return_value = download_mock
        download_mock.select_related.return_value = download_mock
        download_mock.get.return_value = download
        filepath = os.path.dirname(__file__) + '/samples/cash_prices_csv_with_optional_field.csv'
        file = open(filepath, 'r', encoding='UTF-8')  # pylint: disable=consider-using-with
        response = self.client.post(
            '/cash_board/cash-prices/csv/',
            {'file': file},
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='multipart'
        )
        download.refresh_from_db()
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data, {'failures': 2, 'success': 4, 'url': ANY})
        self.assertEqual(download.status, 'ready')

        download_path = f'cash/prices/csvs/{self.employee.id}/cash_prices_upload'
        s3_mock.upload.assert_called_once_with(download_path, ANY)
        resulted_csv = s3_mock.upload.call_args[0][1].readlines()
        self.assertEqual(len(resulted_csv), 7)  # headers are split in two lines due to \n character in buyer column name  # pylint: disable=line-too-long
        self.assertEqual(CashPrices.objects.count(), existing_count + 4)
        self.assertEqual(CashPrices.objects.filter(created_by=self.employee).count(), existing_count +4)
        # pylint: disable=line-too-long
        self.assertEqual(
            resulted_csv,
            [
                b'Location,Location Owner,Grade,Season,Contract payment terms,Contract payment scales,Contract terms and conditions,Price,Qty,Start date,End date,Sustainable,Buyer (Optional) - Leave blank if uploading for your company. Enter buyer company name if posting on behalf of Buyer,Buyer NGR (optional),Status,Failure Reason\r\n',
                b'AS Pickup Site,AgriChain Trader ,bar1,22/23,5 DEOW of Delivery,Flat,Optional free text,120.00,1000.00,04/11/22,04/11/25,Y,,,Success,\r\n',
                b'AS Pickup Site, Agrichain Trader,bar2,22/23,5 DEOW of Delivery,Flat,Optional free text,100.00,100.00,04.11.22,05.11.25,N,,34444441,Failure,Buyer NGR not found in system\r\n',
                b'as pickup site,agrichain  trader,bar3,22/23,5 deow of Delivery,flat ,Optional free text,130.00,100.,04-11-22,05-11-25,y,,34444444,Success,\r\n',
                b'as pickup site,agrichain  trader,sp1,2022/2023,5 deow of Delivery , flat,Optional free text,100,1000,04/11/2022,04/11/2025,n,agrichain grower,34444444,Failure,Buyer NGR selected does not belong to the Buyer company\r\n',
                b'Foo Farm ,Foo Corporation,bks1,22/23 , 5 deow of Delivery,Flat,Optional free text,140.0,1000.0,4/11/2022,4/11/2025,Yes,agrichain grower,55555566,Success,\r\n',
                b'Foo Farm,Foo Corporation,cantw , 2022/23,5 deow of  Delivery,Flat,Optional free text,150.23,1000,04-11-2022,04-11-2025,T,agrichain grower,,Success,\r\n'
            ])

        cp = CashPrices.objects.filter(price=120).first()
        self.assertEqual(
            cp.buyer.ngr.id, Ngr.objects.filter(ngr_number__icontains='UNKNOWN_', company=self.company).first().id)

        cp_1 = CashPrices.objects.filter(price=130).first()
        self.assertEqual(cp_1.buyer.ngr.id, user_ngr_1.id)

        cp_2 = CashPrices.objects.filter(price=140).first()
        self.assertEqual(cp_2.buyer.ngr.id, grower_ngr_1.id)

        cp_3 = CashPrices.objects.filter(price=150.23).first()
        self.assertEqual(cp_3.buyer.ngr.id, grower_unknown_ngr.id)


@tag('view')
class CashPricesBulkInactiveViewTest(AuthSetup):
    @patch('core.cash_board.views.CashPrices')
    def test_put_200(self, cash_prices_klass_mock):
        cash_prices_klass_mock.mark_inactive = Mock()
        cash_price_manager_mock = Mock()
        cash_prices_klass_mock.active = cash_price_manager_mock
        cash_price_filter_mock = Mock()
        cash_price_manager_mock.filter = Mock(return_value=cash_price_filter_mock)
        cash_price_filter_mock.values_list = Mock(return_value=[10000, 10001, 10002])
        response = self.client.put(
            '/cash_board/cash-prices/inactive/',
            {'cash_price_ids': [10000, 10001]},
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {'success': True})
        cash_prices_klass_mock.mark_inactive.assert_called_once_with([10000, 10001], self.employee)

    @patch('core.cash_board.views.CashPrices')
    def test_put_400(self, cash_prices_klass_mock):
        cash_prices_klass_mock.mark_inactive = Mock()
        response = self.client.put(
            '/cash_board/cash-prices/inactive/',
            {'cash_price_ids': []},
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 400)

        cash_price_manager_mock = Mock()
        cash_prices_klass_mock.active = cash_price_manager_mock
        cash_price_filter_mock = Mock()
        cash_price_manager_mock.filter = Mock(return_value=cash_price_filter_mock)
        cash_price_filter_mock.values_list = Mock(return_value=[10000, 10002])
        response = self.client.put(
            '/cash_board/cash-prices/inactive/',
            {'cash_price_ids': [10000, 10001]},
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'errors': 'Invalid Transaction'})


@tag('view')
class ExternalCashPricesViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.buyer_abn = '***********'
        self.buyer_company = CompanyFactory(abn=self.buyer_abn)
        self.buyer_contact = EmployeeFactory(company=self.buyer_company)
        self.site = FarmFactory()
        self.payload = {
            "start_date_time": "2023-01-01",
            "end_date_time": "2030-01-01",
            "price": 180.65,
            "buyer_abn": self.buyer_abn,
            "commodity": 'barley',
            "grade": 'PL1',
            "season": '24/25',
            "limit": 900.16,
            "payment_term": '30 days eow',
            "payment_scale": 'flat',
            "site_id": self.site.id,
            "created_at": "2023-10-09 05:30:00",
            "updated_at": "2023-10-09 05:30:00",
            "external_reference": 'GS-CP-001',
        }

    def test_get_200(self):
        response = self.client.post(
            '/cash_board/cash-prices/external/grainstor/',
            self.payload.copy(),
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 201)

        response = self.client.get(
            f'/cash_board/cash-prices/external/grainstor/?site_id={self.site.id}&status=active',
            HTTP_AUTHORIZATION=f"Token {self.token}"
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data) >= 1)
        self.assertEqual(response.data.get('count'), 1)
        self.assertNotEqual(response.data.get('results'), [])

        response = self.client.get(
            f'/cash_board/cash-prices/external/grainstor/?site_id={self.site.id}&status=active&grades=APH1',
            HTTP_AUTHORIZATION=f"Token {self.token}"
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data) >= 1)
        self.assertEqual(response.data.get('count'), 0)
        self.assertEqual(response.data.get('results'), [])

        response = self.client.get(
            f'/cash_board/cash-prices/external/grainstor/?site_id={self.site.id}'
            f'&status=active&commodities=Wheat,Barley&grades=APH1',
            HTTP_AUTHORIZATION=f"Token {self.token}"
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data) >= 1)
        self.assertEqual(response.data.get('count'), 1)
        self.assertNotEqual(response.data.get('results'), [])

        response = self.client.get(
            f'/cash_board/cash-prices/external/grainstor/?site_id={self.site.id}'
            f'&status=active&commodities=Wheat&grades=PL1',
            HTTP_AUTHORIZATION=f"Token {self.token}"
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(len(response.data) >= 1)
        self.assertEqual(response.data.get('count'), 1)
        self.assertNotEqual(response.data.get('results'), [])


    def test_post_201(self):
        response = self.client.post(
            '/cash_board/cash-prices/external/grainstor/',
            self.payload.copy(),
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 201)
        cash_price = CashPrices.objects.last()
        self.assertEqual(cash_price.id, response.data['id'])
        self.assertEqual(response.data['status'], 'active')
        self.assertEqual(cash_price.created_by, self.employee)
        self.assertEqual(cash_price.updated_by, self.employee)

    def test_post_400(self):
        response = self.client.post(
            '/cash_board/cash-prices/external/grainstor/',
            self.payload.copy(),
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 201)

        payload = self.payload.copy()
        payload.pop('external_reference')
        response = self.client.post(
            '/cash_board/cash-prices/external/grainstor/',
            payload,
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'externalReference': 'must be mandatory and unique.'})

        payload = self.payload.copy()
        payload['external_reference'] = 'GS-CP-002'
        payload['site_id'] = 999999
        response = self.client.post(
            '/cash_board/cash-prices/external/grainstor/',
            payload,
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'siteId': 'Not Found'})

        payload = self.payload.copy()
        payload['external_reference'] = 'GS-CP-002'
        payload.pop('buyer_abn')
        response = self.client.post(
            '/cash_board/cash-prices/external/grainstor/',
            payload,
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.data, {'errors': ({'buyer_abn': 'must be mandatory and needs to be present in AC.'}, None, None)})

        payload = self.payload.copy()
        payload['external_reference'] = 'GS-CP-002'
        payload['buyer_abn'] = 'UNKNOWN_ABN'
        response = self.client.post(
            '/cash_board/cash-prices/external/grainstor/',
            payload,
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.data, {'errors': ({'buyer_abn': 'must be mandatory and needs to be present in AC.'}, None, None)})

        payload = self.payload.copy()
        payload['external_reference'] = 'GS-CP-002'
        payload.pop('grade')
        response = self.client.post(
            '/cash_board/cash-prices/external/grainstor/',
            payload,
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            str(response.data),
            str({'errors': ({'grade': [ValidationError(['This field cannot be null.'])]}, None, None)})
        )


@tag('view')
class ExternalCashPricesActivateViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.site = FarmFactory(externally_sync_source='grainstor', name='External Site')
        self.buyer = CompanyFactory()
        self.buyer_contact = EmployeeFactory(company=self.buyer)
        self.random_cash_price = CashPriceFactory()
        self.assertTrue(self.random_cash_price.persisted)
        self.cash_price_params = {
            "start_date_time": "2023-01-01",
            "end_date_time": "2030-01-01",
            "price": 180.65,
            "buyer": {'company_id': self.buyer.id, 'contact_id': self.buyer_contact.id},
            "commodity_id": 1,
            "grade_id": 1,
            "season": '24/25',
            "limit": 900.16,
            "payment_term_id": 1,
            "payment_scale_id": 1,
            "site_id": self.site.id,
            "created_by_id": self.buyer_contact.id,
            "updated_by_id": self.buyer_contact.id,
        }

    def test_get_200(self):
        response = self.client.get(
            '/cash_board/cash-prices/external/grainstor/activate/',
            HTTP_AUTHORIZATION=f"Token {self.token}"
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, [])

        cash_price = CashPrices.persist(self.cash_price_params)
        self.assertTrue(cash_price.persisted)
        self.assertTrue(cash_price.is_external_created_internally)
        self.assertEqual(cash_price.external_system, 'grainstor')
        self.assertIsNotNone(cash_price.external_reference)

        response = self.client.get(
            '/cash_board/cash-prices/external/grainstor/activate/',
            HTTP_AUTHORIZATION=f"Token {self.token}"
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['limit'], 900.16)
        self.assertEqual(response.data[0]['externalReference'], cash_price.external_reference)
        self.assertEqual(response.data[0]['externalSystem'], 'grainstor')
        self.assertEqual(response.data[0]['id'], cash_price.id)
        self.assertNotEqual(response.data[0]['id'], self.random_cash_price.id)

        response = self.client.get(
            f'/cash_board/cash-prices/external/grainstor/activate/?site_id={self.random_cash_price.site_id}',
            HTTP_AUTHORIZATION=f"Token {self.token}"
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 0)

        response = self.client.get(
            f'/cash_board/cash-prices/external/grainstor/activate/?site_id={self.site.id}',
            HTTP_AUTHORIZATION=f"Token {self.token}"
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)

    def test_put_200_activate_without_pending_title_transfer(self):
        response = self.client.put(
            '/cash_board/cash-prices/external/grainstor/activate/',
            {'external_reference': 'GS-CP-001'},
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'errors': 'Invalid GS-CP-001'})

        cash_price = CashPrices.persist(self.cash_price_params)
        self.assertTrue(cash_price.persisted)

        response = self.client.put(
            '/cash_board/cash-prices/external/grainstor/activate/',
            {
                'external_reference': 'GS-CP-001'
            },
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'errors': 'Invalid GS-CP-001'})

        response = self.client.put(
            '/cash_board/cash-prices/external/grainstor/activate/',
            {
                'external_reference': ['GS-CP-001', cash_price.external_reference]
            },
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'errors': 'Invalid GS-CP-001'})

        old_reference = cash_price.external_reference
        response = self.client.put(
            '/cash_board/cash-prices/external/grainstor/activate/',
            {
                'external_reference': [cash_price.external_reference]
            },
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 204)
        cash_price.refresh_from_db()
        self.assertFalse(cash_price.is_external_created_internally)
        self.assertIsNotNone(cash_price.external_reference)
        self.assertNotEqual(cash_price.external_reference, old_reference)

        response = self.client.get(
            '/cash_board/cash-prices/external/grainstor/activate/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 0)

    @patch('core.jobs.models.Job.schedule_job_for_task')
    def test_put_200_activate_with_pending_title_transfer(self, schedule_job_for_task_mock):
        cash_price = CashPrices.persist(self.cash_price_params)
        self.assertTrue(cash_price.persisted)

        seller_company = CompanyFactory()
        seller_contact = EmployeeFactory(company=seller_company)
        title_transfer = cash_price.create_contract_and_title_transfer(
            cash_price.created_by,
            {
                'tonnage': 10,
                'identifier': 'TT123',
                'seller': {
                    'company_id': seller_company.id,
                    'contact_id': seller_contact.id
                }
            },
            'Australia/Sydney'
        )
        self.assertTrue(title_transfer.persisted)
        self.assertEqual(title_transfer.status, 'planned')

        old_reference = cash_price.external_reference
        response = self.client.put(
            '/cash_board/cash-prices/external/grainstor/activate/',
            {
                'external_reference': [cash_price.external_reference]
            },
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 204)
        cash_price.refresh_from_db()
        self.assertFalse(cash_price.is_external_created_internally)
        self.assertIsNotNone(cash_price.external_reference)
        self.assertNotEqual(cash_price.external_reference, old_reference)
        self.assertEqual(schedule_job_for_task_mock.call_count, 3)
        self.assertEqual(
            schedule_job_for_task_mock.mock_calls[0],
            call('add_parties_to_directories', {'contract_ids': [title_transfer.commodity_contract_id]}, True)
        )
        self.assertEqual(schedule_job_for_task_mock.mock_calls[1], call('process_title_transfer', title_transfer.id))
        self.assertEqual(
            schedule_job_for_task_mock.mock_calls[2],
            call('send_title_transfer_created_mail', params={'id': title_transfer.id})
        )


@tag('view')
class ExternalCashPriceViewTest(AuthSetup):
    def setUp(self):
        super().setUp()
        self.site = FarmFactory(externally_sync_source='grainstor', name='External Site')
        self.buyer = CompanyFactory()
        self.buyer_contact = EmployeeFactory(company=self.buyer)
        self.random_cash_price = CashPriceFactory()
        self.assertTrue(self.random_cash_price.persisted)
        cash_price_params = {
            "start_date_time": "2023-01-01",
            "end_date_time": "2030-01-01",
            "price": 180.65,
            "buyer": {'company_id': self.buyer.id, 'contact_id': self.buyer_contact.id},
            "commodity_id": 1,
            "grade_id": 1,
            "season": '24/25',
            "limit": 900.16,
            "payment_term_id": 1,
            "payment_scale_id": 1,
            "site_id": self.site.id,
            "created_by_id": self.buyer_contact.id,
            "updated_by_id": self.buyer_contact.id,
        }
        self.cash_price_internally_created = CashPrices.persist(cash_price_params)
        self.assertTrue(self.cash_price_internally_created.persisted)
        self.assertTrue(self.cash_price_internally_created.is_external_created_internally)

        payload = {
            "start_date_time": "2023-01-01",
            "end_date_time": "2030-01-01",
            "price": 180.65,
            "buyer_abn": self.buyer.abn,
            "commodity": 'barley',
            "grade": 'PL1',
            "season": '24/25',
            "limit": 1900.16,
            "payment_term": '30 days eow',
            "payment_scale": 'flat',
            "site_id": self.site.id,
            "created_at": "2023-10-09 05:30:00",
            "updated_at": "2023-10-09 05:30:00",
            "external_reference": 'GS-CP-001',
        }
        response = self.client.post(
            '/cash_board/cash-prices/external/grainstor/',
            payload.copy(),
            HTTP_AUTHORIZATION=f"Token {self.token}",
            format='json'
        )
        self.assertEqual(response.status_code, 201)
        self.cash_price_externally_created = CashPrices.objects.filter(external_reference='GS-CP-001').first()
        self.assertFalse(self.cash_price_externally_created.is_external_created_internally)

        self.cash_price_not_external = CashPriceFactory()

    def test_delete(self):
        self.assertEqual(self.cash_price_externally_created.status, 'active')
        self.assertEqual(self.cash_price_internally_created.status, 'active')
        self.assertEqual(self.cash_price_not_external.status, 'active')

        response = self.client.delete(
            f'/cash_board/cash-prices/external/grainstor/{self.cash_price_not_external.id}/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
        )
        self.assertEqual(response.status_code, 404)

        response = self.client.delete(
            f'/cash_board/cash-prices/external/grainstor/{self.cash_price_internally_created.external_reference}/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
        )
        self.assertEqual(response.status_code, 204)
        self.cash_price_internally_created.refresh_from_db()
        self.assertEqual(self.cash_price_internally_created.status, 'inactive')

        response = self.client.delete(
            f'/cash_board/cash-prices/external/grainstor/{self.cash_price_externally_created.external_reference}/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
        )
        self.assertEqual(response.status_code, 204)
        self.cash_price_externally_created.refresh_from_db()
        self.assertEqual(self.cash_price_externally_created.status, 'inactive')

        response = self.client.delete(
            f'/cash_board/cash-prices/external/grainstor/{self.cash_price_externally_created.external_reference}/',
            HTTP_AUTHORIZATION=f"Token {self.token}",
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'errors': 'cash price is already inactive'})

    @patch('core.jobs.models.Job.schedule_job_for_task')
    def test_put(self, schedule_job_for_task_mock):
        seller_company = CompanyFactory()
        seller_contact = EmployeeFactory(company=seller_company)
        seller_ngr = NgrFactory(company=seller_company)

        root_user_company = Employee.objects.get(id=1).company
        root_user_company.show_email_popup = True
        root_user_company.save()

        with self.captureOnCommitCallbacks(execute=True):
            response = self.client.put(
                f'/cash_board/cash-prices/external/grainstor/{self.cash_price_externally_created.external_reference}/',
                {
                    'tonnage': 10,
                    'identifier': 'EXTT123',
                    'seller_abn': seller_company.abn,
                    'seller_ngr_number': seller_ngr.ngr_number
                },
                HTTP_AUTHORIZATION=f"Token {self.token}",
                format='json'
            )
        self.assertEqual(response.status_code, 200)
        self.cash_price_externally_created.refresh_from_db()
        self.assertEqual(self.cash_price_externally_created.calculate_halt_status_for_repost(), 'partial')
        self.assertEqual(self.cash_price_externally_created.status, 'active')
        self.assertEqual(self.cash_price_externally_created.limit_remaining, 1890.16)
        title_transfer = TitleTransfer.objects.get(identifier='EXTT123')
        self.assertEqual(title_transfer.tonnage, 10)
        self.assertEqual(title_transfer.status, 'planned')
        self.assertEqual(title_transfer.commodity_contract.cash_price_id, self.cash_price_externally_created.id)
        self.assertEqual(
            title_transfer.commodity_contract.buyer.company_id, self.cash_price_externally_created.buyer.company_id)
        self.assertEqual(title_transfer.commodity_contract.seller.company_id, seller_company.id)
        self.assertEqual(title_transfer.commodity_contract.seller.ngr_id, seller_ngr.id)
        self.assertEqual(title_transfer.commodity_contract.seller.contact_id, seller_contact.id)
        self.assertEqual(title_transfer.seller.company_id, seller_company.id)
        self.assertEqual(title_transfer.seller.ngr_id, seller_ngr.id)
        self.assertEqual(title_transfer.seller.contact_id, seller_contact.id)
        self.assertEqual(schedule_job_for_task_mock.call_count, 4)
        self.assertEqual(
            schedule_job_for_task_mock.mock_calls[0],
            call('add_parties_to_directories',
                 {'contract_ids': [title_transfer.commodity_contract_id]}, True)
        )
        self.assertEqual(
            schedule_job_for_task_mock.mock_calls[1],
            call(job_type='send_contract_created_mail', params=title_transfer.commodity_contract_id)
        )
        self.assertEqual(
            schedule_job_for_task_mock.mock_calls[2],
            call('process_title_transfer', title_transfer.id)
        )
        self.assertEqual(
            schedule_job_for_task_mock.mock_calls[3],
            call('send_title_transfer_created_mail', params={'id': title_transfer.id})
        )
