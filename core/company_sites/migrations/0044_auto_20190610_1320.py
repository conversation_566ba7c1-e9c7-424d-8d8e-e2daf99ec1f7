# Generated by Django 2.1.7 on 2019-06-10 13:20

from django.db import migrations

def update_default_title_order(apps, schema_editor):
    Settings = apps.get_model('company_sites', 'SiteManagementSettings')
    for setting in Settings.objects.all():
        setting.title_order = [
            'start', 'tonnage', 'commodity', 'grade', 'bookingNumber', 'freightProvider'
        ]
        setting.save()

class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0043_auto_20190610_1319'),
    ]

    operations = [
        migrations.RunPython(update_default_title_order)
    ]
