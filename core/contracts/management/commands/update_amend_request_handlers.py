from django.core.management.base import BaseCommand
from django.db import models
from core.contracts.models import ContractAcceptanceRequest
from core.freights.models import FreightOrderAcceptanceRequest, FreightContractAcceptanceRequest
from core.farms.models import Storage, Company, Farm


class Command(BaseCommand):
    help = 'Fix amendment request'

    def handle(self, *args, **kwargs):  # pylint: disable=unused-argument
        requests = ContractAcceptanceRequest.objects.filter(type='amend').filter(
            models.Q(args__consignor__isnull=False) |
            models.Q(args__consignee__isnull=False) |
            models.Q(args__consignees__isnull=False)
        )
        fo_requests = FreightOrderAcceptanceRequest.objects.filter(type='amend').filter(
            models.Q(args__freight_pickup__consignor__isnull=False) |
            models.Q(args__freight_delivery__consignee__isnull=False)
        )
        fm_requests = FreightContractAcceptanceRequest.objects.filter(type='amend').filter(
            models.Q(args__freight_pickup__consignor__isnull=False) |
            models.Q(args__freight_delivery__consignee__isnull=False)
        )
        self.update_amend_requests(requests, 'contract')
        self.update_amend_requests(fo_requests, 'fo')
        self.update_amend_requests(fm_requests, 'fm')

    def update_amend_requests(self, requests, request_type): # pylint: disable=too-many-branches
        for request in requests:
            request_id = request.contract_id if not request_type == 'fo' else request.order_id
            print('**************** Start ', request_type)
            print('**************** Start ', request_id)
            print('**************** Start ', request.id)
            if request_type == 'contract':
                params = request.args
            else:
                params = request.args['freight_pickup'] if 'freight_pickup' in request.args.keys() else {}
            if 'consignor' in params.keys() and params['consignor'] and 'handler_type' in params['consignor'].keys():
                if 'sites' in params['consignor'].keys():
                    params['consignor'], x = self.update_site_args(params['consignor'], []) # pylint: disable=unused-variable,line-too-long
                elif params['consignor']['handler_type'] == 'company':
                    params['consignor'] = self.update_handler_without_sites(params['consignor'])
                params['consignor'].pop('handler_type')
            if request_type != 'contract':
                params = request.args['freight_delivery'] if 'freight_delivery' in request.args.keys() else {}
            if 'consignees' in params.keys() and params['consignees'] and request_type == 'contract':
                new_consignees = []
                for index, consignee in enumerate(params['consignees']):
                    if 'sites' in consignee.keys():
                        params['consignees'][index], new_consignees = self.update_site_args(consignee, new_consignees)
                    elif 'handler_type' in consignee.keys() and consignee['handler_type'] == 'company':
                        params['consignees'][index] = self.update_handler_without_sites(consignee)
                params['consignees'] = params['consignees'] + new_consignees
                request = self.fix_consignees(request)
            elif 'consignee' in params.keys() and params['consignee']:
                new_consignees = []
                if 'sites' in params['consignee'].keys():
                    params['consignee'], new_consignees = self.update_site_args(params['consignee'], new_consignees)
                elif params['consignee']['handler_type'] == 'company':
                    params['consignee'] = self.update_handler_without_sites(params['consignee'])
                if request_type == 'contract':
                    params['consignees'] = []
                    params['consignees'].append(params['consignee'])
                    params['consignees'] = params['consignees'] + new_consignees
                    request = self.fix_consignees(request)
            request.save()
            print('**************** Done')

    @staticmethod
    def update_site_args(handler, new_consignees):
        for index, site in enumerate(handler['sites']):
            location_type = site['location_type'] if 'location_type' in site.keys() else None
            if location_type in ['storage', 'companysite']:
                location_id = site['location_id']
                if location_type == 'storage':
                    storage = Storage.objects.filter(id=location_id).first()
                    if storage.storage_type == 'home':
                        location_id = None
                    elif storage.storage_type == 'system':
                        location_id = storage.location_id
                if location_id:
                    if index == 0:
                        handler['handler_id'] = location_id
                        handler['sites'][index]['location_type'] = 'storage'
                        handler['sites'][index]['location_id'] = Storage.objects.filter(
                            farm_id=location_id,
                            is_gate=True).first().id
                    else:
                        new_consignee = {}
                        new_consignee['handler_id'] = location_id
                        new_consignee['ld'] = handler['sites'][index]['ld']
                        new_consignee['sites'] = {}
                        new_consignee['sites']['location_type'] = 'storage'
                        new_consignee['sites']['location_id'] = Storage.objects.filter(
                            farm_id=location_id,
                            is_gate=True).first().id
                        new_consignee['sites'] = [new_consignee['sites']]
                        handler['sites'][index]['deleted'] = True
                        new_consignees.append(new_consignee)
        return handler, new_consignees

    @staticmethod
    def fix_consignees(request):
        params = request.args
        for index, consignee in enumerate(params['consignees']):
            print('***********', consignee)
            params['consignees'][index]['position'] = index + 1
            params['consignees'][index].pop('handler_type') if 'handler_type' in consignee.keys() else None
            if 'sites' in consignee.keys():
                sites = []
                for site in consignee['sites']:
                    if 'deleted' not in site.keys():
                        if 'ld' in site.keys():
                            params['consignees'][index]['ld'] = site['ld']
                            site.pop('ld')
                        sites.append(site)
                params['consignees'][index]['sites'] = sites
            if index == 0:
                params['consignee'] = consignee
        return request

    @staticmethod
    def update_handler_without_sites(params):
        company = Company.objects.filter(id=params['handler_id']).first()
        main_farm = Farm.objects.filter(company_id=company.id, name=company.business_name).first()
        if not main_farm:
            main_farm = Farm.objects.filter(company_id=company.id).order_by('id').first()
        params['handler_id'] = main_farm.id
        return params
