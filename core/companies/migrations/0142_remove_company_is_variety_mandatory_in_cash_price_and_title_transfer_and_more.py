# Generated by Django 4.1.9 on 2023-08-16 03:30

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0141_remove_subcontractor_contractor_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='company',
            old_name='is_variety_mandatory_in_cash_price_and_title_transfer',
            new_name='is_variety_mandatory',
        ),
        migrations.AddField(
            model_name='company',
            name='load_type_for_variety_mandatory',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('inload', 'Inload'), ('inload_and_outload', 'Inload and Outload'), ('title_transfers_and_cash_outs', 'Title Transfers and Cash Outs')], max_length=255), blank=True, default=list, null=True, size=None),
        ),
        migrations.AddField(
            model_name='company',
            name='user_type_for_variety_mandatory',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('growers', 'Growers'), ('non_growers', 'Non Growers')], max_length=255), blank=True, default=list, null=True, size=None),
        ),
        migrations.RenameField(
            model_name='historicalcompany',
            old_name='is_variety_mandatory_in_cash_price_and_title_transfer',
            new_name='is_variety_mandatory',
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='load_type_for_variety_mandatory',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('inload', 'Inload'), ('inload_and_outload', 'Inload and Outload'), ('title_transfers_and_cash_outs', 'Title Transfers and Cash Outs')], max_length=255), blank=True, default=list, null=True, size=None),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='user_type_for_variety_mandatory',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('growers', 'Growers'), ('non_growers', 'Non Growers')], max_length=255), blank=True, default=list, null=True, size=None),
        ),
    ]
