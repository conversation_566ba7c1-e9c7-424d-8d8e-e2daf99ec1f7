# Generated by Django 2.1.3 on 2019-01-11 10:07

from django.db import migrations

def farm_directory_updates(apps, schema_editor):
    Farm = apps.get_model('farms', 'Farm')
    FarmDirectory = apps.get_model('companies', 'FarmDirectory')
    for farm in Farm.objects.filter(company__owner_company_id__isnull=False):
        if not FarmDirectory.objects.filter(farm_id=farm.id, company_id=farm.created_by.company_id).exists():
            dir = FarmDirectory(farm_id=farm.id, company_id=farm.created_by.company_id)
            dir.save()

class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0016_auto_20190110_0810'),
    ]

    operations = [
        migrations.RunPython(farm_directory_updates)
    ]
