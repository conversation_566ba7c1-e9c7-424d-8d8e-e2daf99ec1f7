from django.db import models
from django.utils import timezone
from django_fsm import <PERSON><PERSON><PERSON>ield

from core.contract_bids import constants
from core.common.constants import DEFAULT_PAYMENT_TERM_ID, BHC_TYPE_ID
from core.common.models import BaseModel
from core.common.utils import get_grade_name, generate_identifier, to_display_attr
from core.contracts.models import Party
from core.contracts.constants import (CONTRACT_DOCUMENT_TYPE_ID, CONTRACT_DOCUMENT_TYPE, DELIVERY_ONUS_SELLER,
                                      PRICE_POINTS_DELIVERED_SITE_ID, FIXED_GRADE_CONTRACT_TYPE_ID, NIL_BROKERAGES,
                                      SELLER_TO_INVOICE_BUYER, TOLERANCE_5_OR_20_PERCENT)
from core.validation.validators import season_validator


class ActiveContractBidManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):
        now = timezone.now()
        return super().get_queryset().filter(
            start_date_time__lte=now,
            end_date_time__gte=now
        ).exclude(
            status__in=['inactive', 'completed'])


class DefaultCompanyManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):  # pylint: disable=useless-super-delegation
        return super().get_queryset()


class ArchivedCashPricesManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):
        now = timezone.now()
        return super().get_queryset().filter(
            models.Q(start_date_time__gte=now) |
            models.Q(end_date_time__lte=now) |
            models.Q(status__in=['inactive', 'completed'])
        )


class ContractBid(BaseModel):
    class Meta:
        db_table = 'contract_bids'

    active = ActiveContractBidManager()
    objects = DefaultCompanyManager()
    archived = ArchivedCashPricesManager()

    site = models.ForeignKey('farms.Farm', on_delete=models.PROTECT)
    commodity = models.ForeignKey(
        'commodities.Commodity', on_delete=models.DO_NOTHING)
    season = models.CharField(max_length=5, validators=[season_validator])
    grade = models.ForeignKey('commodities.Grade', on_delete=models.DO_NOTHING)
    payment_term = models.ForeignKey(
        'contracts.PaymentTerm', on_delete=models.DO_NOTHING, default=DEFAULT_PAYMENT_TERM_ID)
    payment_scale = models.ForeignKey(
        'contracts.PaymentScale',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
    )
    price = models.FloatField(null=True, blank=True)
    limit = models.FloatField(null=True, blank=True)
    track = models.CharField(max_length=255, null=True, blank=True)
    buyer = models.ForeignKey('contracts.Party', on_delete=models.DO_NOTHING, related_name='contract_bid_buyer')
    start_date_time = models.DateTimeField()
    end_date_time = models.DateTimeField()
    status = FSMField(
        max_length=40,
        choices=constants.STATUSES,
        null=False,
        blank=False,
        default='active',
        db_index=True,
    )
    delivery_start_date = models.DateField(blank=True, null=True)
    delivery_end_date = models.DateField(blank=True, null=True)
    group = group = models.ForeignKey('companies.CompanyGroup', on_delete=models.DO_NOTHING, null=True, blank=True)

    FILLABLES = [
        'site_id',
        'commodity_id',
        'track',
        'grade_id',
        'price',
        'limit',
        'season',
        'buyer',
        'payment_term_id',
        'end_date_time',
        'start_date_time',
        "variety_id",
        "tonnage",
        "identifier",
        "total_sale",
        "mark_complete",
        "status",
        "payment_scale_id",
        'buyer_abn',
        'seller_abn',
        'seller_ngr_number',
        'payment_term',
        'payment_scale',
        'commodity',
        'grade',
        'variety',
        'created_at',
        'updated_at',
        'delivery_start_date',
        'delivery_end_date',
        'group_id',
        'quantity'
    ]

    @property
    def grade_name(self):
        return get_grade_name(self)

    @staticmethod
    def build_party(params, role):
        if params:
            return Party(role=role, **params)

    def calculate_halt_status_for_repost(self):
        limit_remaining = self.limit_remaining

        # limit = 100 and total_tonnage = 100 -> limit_remaining = 0 ->  completed
        # limit = 100 and total_tonnage = 0 -> limit_remaining = 100 ->  can be marked as inactive
        # not calling is_completable -- saving limit_remaining call again

        if limit_remaining == 0:
            return 'completed'
        return 'inactive'

    @classmethod
    def persist(cls, data):
        buyer = cls.build_party(data.pop('buyer', None), 'Buyer')
        contract_bid = cls(**data)
        buyer.save()
        contract_bid.buyer = buyer
        contract_bid.save()
        return contract_bid

    def can_view(self, user):
        return self.created_by_id == user.id or not self.group or (self.group and self.group.companies.filter(
            id=user.company_id).exists())

    def calculate_levy_for_delivered_site(self, quantity):
        contract_value = quantity * self.price
        levy = self.commodity.levy()
        is_bhc = self.site.company.type_id == BHC_TYPE_ID
        if levy['multiplier'] == 'tonnage':
            return max(round((levy['value'] * quantity), 2), 0.0)
        if is_bhc:
            return max(round(levy['value'] * (contract_value - 20 * quantity), 2), 0.0)
        else:
            return 0.0

    def status_display_name(self):
        if self.status == 'inactive' or (self.end_date_time <= timezone.now() and self.status == 'active'):
            return to_display_attr(constants.STATUSES, self.calculate_halt_status_for_repost())
        return to_display_attr(constants.STATUSES, self.status)

    def create_contract(self, data, user):
        from core.contracts.models import Contract
        from core.ngrs.models import Ngr
        seller_ngrs = Ngr.objects.filter(company_id=user.company_id)
        tonnage = data.get('quantity')
        contract_data = {
            'administration': {'invoicing': SELLER_TO_INVOICE_BUYER},
            'buyer': {
                'company_id': self.buyer.company_id,
                'contact_id': self.buyer.contact_id,
                'ngr_id': self.buyer.ngr_id,
            },
            'carry_frequency': 'Monthly',
            'consignees': [{
                'handler_id': self.site_id,
            }],
            'contract_bid_id': self.id,
            'contract_date': timezone.now(),
            'communication': {"acceptance_required": False},
            'delivery_start_date': self.delivery_start_date,
            'delivery_end_date': self.delivery_end_date,
            'document_type_id': CONTRACT_DOCUMENT_TYPE_ID,
            'delivery_onus': DELIVERY_ONUS_SELLER,
            'identifier': generate_identifier(CONTRACT_DOCUMENT_TYPE),
            'commodity_id': self.commodity_id,
            'grade_id': self.grade_id,
            'season': self.season,
            'payment_term_id': self.payment_term_id,
            'payment_scale_id': self.payment_scale_id,
            'price': self.price,
            'price_point_id': PRICE_POINTS_DELIVERED_SITE_ID,
            'seller': {
                'company_id': user.company_id,
                'ngr_id': seller_ngrs.first().id if seller_ngrs.count() == 1 else None,
                'contact_id': user.id,
            },
            'tonnage': tonnage,
            'type_id': FIXED_GRADE_CONTRACT_TYPE_ID,
            'owner_id': self.buyer.company_id,
            "tolerance_id": TOLERANCE_5_OR_20_PERCENT,
            "brokerages": NIL_BROKERAGES,
            "levy": self.calculate_levy_for_delivered_site(float(tonnage)),
            'created_by_id': self.created_by_id,
            'updated_by_id': self.updated_by_id,
        }
        contract = Contract.persist(contract_data)
        if contract.persisted and self.limit_remaining == 0:
            self.status = 'completed'
            self.save()
        return contract

    @property
    def limit_remaining(self):
        return round(self.limit - self.total_transaction_tonnage, 2)

    @property
    def total_transaction_tonnage(self):
        return self.contracts.exclude(status='void').aggregate(
            total_tonnage=models.Sum('tonnage'))['total_tonnage'] or 0
