# Generated by Django 4.1.10 on 2023-07-31 10:14

import core.companies.models
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0139_remove_company_is_company_selected_for_grouping_and_more'),
    ]

    operations = [
        migrations.AlterModelManagers(
            name='company',
            managers=[
                ('objects', core.companies.models.NonSystemCompanyManager()),
                ('moderators', core.companies.models.SystemCompanyManager()),
                ('all', core.companies.models.DefaultCompanyManager()),
            ],
        ),
    ]
