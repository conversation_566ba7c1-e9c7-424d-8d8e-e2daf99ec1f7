# Generated by Django 4.1.10 on 2023-09-15 10:14

from django.db import migrations
from core.common.constants import BHC_TYPE_ID


def add_bhc_company_to_company_directories(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    AddedCompany = apps.get_model('companies', 'AddedCompany')
    bulk_creates = []

    for company in Company.objects.filter(type_id=BHC_TYPE_ID):
        bulk_creates.append(AddedCompany(subject_company=None, object_company=company))

    AddedCompany.objects.bulk_create(bulk_creates)


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0147_company_warehouse_inload_charged_after_and_more'),
    ]

    operations = [
        migrations.RunPython(add_bhc_company_to_company_directories),
    ]
