import itertools
import warnings
from collections import defaultdict, Counter
from copy import deepcopy
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

from django.apps import apps
from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>ield
from django.core import signing
from django.core.exceptions import ValidationError
from django.db import models
from django.db import transaction
from django.db.models import Q, Value
from django.db.models.functions import Concat, Replace
from django.utils import timezone
from django.utils.functional import cached_property
from pydash import get
from rest_framework.authtoken.models import Token

import core.common.constants
import core.services.external.aws
import core.services.internal.postgres
from core.commodities.constants import UNIT_ABBREVIATIONS
from core.commodities.models import Grade
from core.common.constants import AU_SYSTEM_COMPANY_ID
from core.common.constants import (LOGISTICS_TYPE_ID, GROWER_TYPE_ID, INLOAD,
                                   OUTLOAD, WAREHOUSE_STORAGE_FEES_MODEL,
                                   WAREHOUSE_INLOAD_FEES_MODEL, WAREHOUSE_THROUGHPUT_INLOAD_FEES_MODEL,
                                   WAREHOUSE_REGRADE_RESEASON_MODEL, WAREHOUSE_OUTLOAD_FEES_MODEL,
                                   WAREHOUSE_THROUGHPUT_OUTLOAD_FEES_MODEL,
                                   WAREHOUSE_TRANSFER_FEES_MODEL, FREIGHT_MOVEMENT_MODEL,
                                   TITLE_TRANSFER_MODEL, CARRY_ITEM_MODEL, EPR_ITEM_DB_MODEL, CUSTOM_ITEM_MODEL,
                                   GRAIN_LEVY_ITEM_MODEL, LOAD_MODEL, OBSERVER_TYPE_ID, WAREHOUSE_STOCK_SWAP_MODEL,
                                   SYSTEM_TYPE_ID, SYSTEM_COMPANY_IDS, EMPTY_VALUE, UNKNOWN, BHC_TYPE_ID)
from core.common.expressions import Strip, CustomRegexWrapper
from core.common.models import BaseModel, MasterDataMixin, RawModel, DeletedRecords, MergedEntity
from core.common.utils import (get_model_class_from_name, generate_identifier,
                               strip_special, get_request_country_id, to_display_attr)
from core.companies.constants import (DEFAULT_REFERENCE_NUMBER_LENGTH, DEFAULT_REFERENCE_NUMBER_START,
                                      CANNOT_CREATE_EMPLOYEE_FOR_SUBSCRIBER_COMPANY_REASON,
                                      CANNOT_CREATE_NGR_FOR_SUBSCRIBER_COMPANY_REASON, EMPLOYEES, NGR,
                                      PLAN_TYPE_CHOICES, PREMIUM_PLAN,
                                      CANNOT_CREATE_EMPLOYEE_FOR_REGISTERED_COMPANY_REASON,
                                      CANNOT_CREATE_COMPANY_EMPLOYEE_BASED_ON_ROLE_REASON,
                                      CANNOT_CREATE_NGR_FOR_REGISTERED_COMPANY_REASON,
                                      CANNOT_CREATE_COMPANY_NGR_BASED_ON_ROLE_REASON, DEFAULT_START_OF_WEEK,
                                      DEFAULT_END_OF_WEEK, LOGISTICS, SITES, STORAGE, LOGISTICS_LITE_PLAN, STORAGES,
                                      TRANSACTION_TYPE_MAPPER, WAREHOUSE_INLOAD_FEES_CHARGED_AT,
                                      NEW_COMPANY_SLACK_MESSAGE, FEES, CONTRACT_ALLOCATION_TYPES, CONTRACT_BIDS,
                                      WAREHOUSE_INVOICE_FREQUENCIES, WAREHOUSE_INVOICE_WEEKLY_FREQUENCY,
                                      WAREHOUSE_INVOICE_MONTHLY_FREQUENCY, ABN_STATUS_TYPES, SIMILARITY_SEARCH_SCORE)
from core.countries.constants import (AUSTRALIA_COUNTRY_ID, USA_COUNTRY_ID, AUSTRALIA_COUNTRY_CODE, USA_COUNTRY_CODE,
                                      CANADA_COUNTRY_ID, CANADA_COUNTRY_CODE, NZ_COUNTRY_ID, NZ_COUNTRY_CODE)
from core.countries.models import Country
from core.devices.constants import NOTIFICATION_TYPE_CONTRACT
from core.freights.constants import VOID_STATUS
from core.invoices.constants import WAREHOUSE
from core.locations.constants import AUSTRALIA
from core.locations.models import Location
from core.profiles.constants import (ROLE_BASED_CREATE_UPDATE_PERMS, EMPLOYEE_ROLE_DISPLAY_NAME, OFFICE_EMPLOYEE,
                                     COMPANY_NGR)
from core.services.external.impex_docs import ImpexDocs
from core.services.external.integrations.booking import ExternalBooking
from core.services.external.xero import Xero
from core.services.internal.errbit import ERRBIT_LOGGER
from core.services.internal.slack import Slack
from core.timezones.utils import DateTimeUtil
from core.validation import validators
from core.validation.validators import PHONE_MOBILE_REGEX, WEBSITE_REGEX


class CompanyType(BaseModel, MasterDataMixin):
    class Meta:
        db_table = "company_types"

    mandatory_props = BaseModel.mandatory_props + ['display_name']
    TYPES = (
        ('grower', 'Grower'),
        ('broker', 'Broker'),
        ('logistics', 'Logistics'),
        ('bhc', 'BHC'),
        ('trader', 'Trader'),
    )

    name = models.CharField(max_length=50, choices=TYPES, null=False, blank=False)

    def __str__(self):
        return self.get_name_display()

    @property
    def display_name(self):
        names = [x[1] for x in self.TYPES if x[0] == self.name]
        return names[0] if names else None


class NonSystemCompanyManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):
        return super().get_queryset().exclude(type_id=core.common.constants.SYSTEM_TYPE_ID)


class SystemCompanyManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):
        return super().get_queryset().filter(type_id=core.common.constants.SYSTEM_TYPE_ID)


class DefaultCompanyManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):  # pylint: disable=useless-super-delegation
        return super().get_queryset()


class EstablishmentMixin:
    def key_contact_name_for_user(self, user):
        if user and user.is_staff:
            return None
        key_contact_name = None
        key_contact = self._key_contact_for_user(user)
        if key_contact:
            key_contact_name = key_contact.employee.name

        return key_contact_name

    def key_contact_mobile_for_user(self, user):
        if user and user.is_staff:
            return

        key_contact_mobile = None
        key_contact = self._key_contact_for_user(user)
        if key_contact:
            key_contact_mobile = key_contact.employee.mobile

        return key_contact_mobile

    def _key_contact_for_user(self, user):
        key_contact = None
        if user and self.KEY_CONTACT_RELATION_NAME:
            key_contact = getattr(
                self, self.KEY_CONTACT_RELATION_NAME, None
            ).select_related('employee').filter(
                requester_company_id=user.company_id
            ).first()

        return key_contact

    def brokerages_for_user(self, user):
        return self.qs2dict(self._brokerage_objects_for_user(user))

    def _brokerage_objects_for_user(self, user):
        if get(user, 'company_id'):
            return self.brokerages_set.filter(broker_company_id=user.company_id)
        return []

    def create_brokerages(self, params):
        if params:
            return apps.get_model('contracts', 'Brokerage').build_with_save(
                params=params,
                entity_object=self,
                status='planned',
            )

    def update_brokerages(self, params, user):
        if user:
            self.brokerages_set.filter(broker_company_id=user.company_id).delete()
        if params:
            return self.create_brokerages(params)


class PlatformFeatures(RawModel):  # pylint: disable=too-many-instance-attributes
    class Meta:
        db_table = "platform_features"
        ordering = ['company_id']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.__apply_is_feature_enabled_fn()

    company = models.OneToOneField('companies.Company', on_delete=models.CASCADE, primary_key=True, related_name='platformfeatures') # pylint: disable=line-too-long
    companies = models.BooleanField(default=False)
    farms = models.BooleanField(default=False)
    stocks = models.BooleanField(default=False)
    contracts = models.BooleanField(default=False)
    orders = models.BooleanField(default=False)
    movements = models.BooleanField(default=False)
    invoices = models.BooleanField(default=False)
    site_management = models.BooleanField(default=False)
    site_bookings = models.BooleanField(default=False)
    action_centre = models.BooleanField(default=True)
    vendor_decs = models.BooleanField(default=True)
    title_transfers = models.BooleanField(default=False)
    cash_board = models.BooleanField(default=False)
    freight_scheduler = models.BooleanField(default=True)
    plan_type = models.CharField(max_length=32, choices=PLAN_TYPE_CHOICES, default=PREMIUM_PLAN)

    FILLABLES = [
        'companies',
        'farms',
        'stocks',
        'contracts',
        'orders',
        'movements',
        'invoices',
        'site_management',
        'site_bookings',
        'action_centre'
        'vendor_decs',
        'company_id',
        'title_transfers',
        'freight_scheduler'
    ]

    FEATURES = set(FILLABLES) - {'company_id'}

    def __apply_is_feature_enabled_fn(self):
        def is_feature_enabled_fn(attr):
            fn_name = 'is_' + attr + '_enabled'

            def fn(self):
                return getattr(self, attr, False)

            setattr(self.__class__, fn_name, fn)

        for attr in self.FEATURES:
            is_feature_enabled_fn(attr)

    def _enable_common(self):
        self.companies = True
        self.contracts = True
        self.orders = True
        self.movements = True
        self.invoices = True
        self.site_bookings = True
        self.action_centre = True
        self.site_management = True
        self.vendor_decs = True
        self.stocks = True
        self.title_transfers = True
        self.cash_board = True
        self.freight_scheduler = True

    def _disable_common(self):
        self.site_management = False
        self.contracts = False
        self.orders = False
        self.invoices = False
        self.stocks = False
        self.farms = False
        self.title_transfers = False
        self.cash_board = False
        self.freight_scheduler = False

    def build_logistic_lite(self):
        self._disable_common()
        self.action_centre = True
        self.companies = True
        self.orders = True
        self.movements = True
        self.vendor_decs = True
        self.site_bookings = True

    def _enable_broker_grower_common(self):
        self._enable_common()

    def build_grower(self):
        self._enable_broker_grower_common()

    def build_broker(self):
        self._enable_broker_grower_common()

    def build_logistics(self):
        self._enable_common()

    def build_bhc(self):
        self.build_logistics()

    def build_trader(self):
        self.build_logistics()

    def build_system(self):
        for feature in self.FEATURES:
            value = feature != 'farms'
            setattr(self, feature, value)

    def build(self):
        if self.company_id:
            build_fn = getattr(self, 'build_' + self.company.type.name, None)
            if build_fn:
                build_fn()

    def build_specific(self, features):
        for feature in self.FEATURES:
            setattr(self, feature, feature in features)

    def build_for_type(self, plan_type, type_id=None):
        if plan_type == LOGISTICS_LITE_PLAN:
            self.build_logistic_lite()
        elif plan_type == PREMIUM_PLAN:
            if type_id:
                if type_id in [core.common.constants.GROWER_TYPE_ID,
                               core.common.constants.BROKER_TYPE_ID]:
                    self._enable_broker_grower_common()
                if type_id in [
                    core.common.constants.LOGISTICS_TYPE_ID,
                    core.common.constants.BHC_TYPE_ID,
                    core.common.constants.TRADER_TYPE_ID]:
                    self.build_logistics()
            else:
                self._enable_common()
        self.plan_type = plan_type
        self.save()

    @classmethod
    def persist(cls, company_id, company=None, raise_exception=False, features=None, **kwargs):
        platform_features = cls(company_id=company_id, **kwargs)
        if company:
            platform_features.company = company

        if features:
            platform_features.build_specific(features=features)
        else:
            platform_features.build()

        if raise_exception:
            platform_features.save_only()
        else:
            platform_features.save()

        return platform_features


class CompanyGroup(BaseModel):
    class Meta:
        db_table = "company_groups"

    TYPES = (
        (FEES, 'Fees'),
        (CONTRACT_BIDS, 'Contract Bids')
    )

    name = models.CharField(max_length=255)
    type = models.CharField(choices=TYPES, default=FEES, max_length=255)
    owner_company = models.ForeignKey('companies.Company', related_name='owned_groups', on_delete=models.CASCADE)
    business_type = models.ForeignKey(
        CompanyType,
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True
    )

    FILLABLES = ['name', 'type', 'owner_company_id', 'created_by', 'updated_by', 'owner_company', 'business_type_id']

    @classmethod
    def create_group(cls, user, name, of_type=None, business_type_id=None):
        params = {
            'name': name,
            'type': of_type or FEES,
            'owner_company': user.company,
            'business_type_id': business_type_id,
            'created_by': user,
            'updated_by': user
        }
        return cls.create(params)

    def add_companies(self, company_ids):
        companies = Company.objects.filter(id__in=company_ids)
        siblings = self.siblings
        for company in companies:
            company.groups.remove(*siblings)
            company.groups.add(self)
        return companies

    @property
    def siblings(self):
        return CompanyGroup.objects.filter(owner_company_id=self.owner_company_id, type=self.type,
                                           business_type_id__isnull=True)

    def get_company_ids_belonging_to_group(self):
        return self.companies.values_list('id', flat=True)

    def add_directory_companies_of_business_type(self):
        companies = Company.directory_companies_only(self.owner_company_id).filter(type_id=self.business_type_id)
        self.companies.add(*companies)


class CompanyOverview:  # pragma: no cover
    def __init__(self, company, include_private=False):
        self.company = company
        self.include_private = include_private
        self._highlights = []
        self.__build_highlights()

    @property
    def public_highlights(self):
        return [
            highlight for highlight in self._highlights if highlight['public']
        ]

    @property
    def highlights(self):
        return self._highlights if self.include_private else self.public_highlights

    def to_dict(self):
        return {
            'highlights': self.highlights,
            'id': self.company.id,
            'abn': self.company.abn,
            'entity_name': self.company.entity_name,
            'business_name': self.company.business_name,
        }

    def __build_highlights(self):
        self.__set_abn_status()
        self.__set_subscriber()
        self.__set_registered()
        self.__set_ngr_account()
        self.__set_xero_account()
        self.__set_octopusbot()
        self.__set_power_bi()

    def __set_registered(self):
        is_registered = self.company.is_registered
        last_updated_at = None
        if is_registered:
            history = self.company.history.order_by('history_date')
            last_updated_at = get(history.filter(owner_company_id__isnull=True).first(), 'history_date')

        self._highlights.append({
            'id': 'registered',
            'label': 'Registered',
            'value': is_registered,
            'extras': {'last_updated_at': last_updated_at},
            'public': True,
            'order': 2
        })

    def __set_subscriber(self):
        is_subscriber = self.company.transaction_participation
        last_updated_at = None
        if is_subscriber:
            history = self.company.history.order_by('history_date')
            last_updated_at = get(history.filter(transaction_participation=True).first(), 'history_date')

        self._highlights.append({
            'id': 'subscriber',
            'label': 'Subscriber',
            'value': is_subscriber,
            'extras': {'last_updated_at': last_updated_at},
            'public': True,
            'order': 3
        })

    def __set_abn_status(self):
        self._highlights.append({
            'id': 'abn',
            'label': 'ABN Status',
            'value': self.company.abn_status if self.company.abn and self.company.abn != '-' else 'N/A',
            'extras': {'last_updated_at': self.company.abn_status_last_updated_at} if self.company.abn else {},
            'public': True,
            'order': 4
        })

    def __set_ngr_account(self):
        credential = self.company.ngr_portal_credential.first()
        self._highlights.append({
            'id': 'ngr',
            'label': 'NGR Account',
            'value': bool(credential),
            'extras': {'last_updated_at': credential.updated_at if credential else None},
            'public': False
        })

    def __set_xero_account(self):
        is_xero_account_linked = self.company.is_xero_account_linked
        last_updated_at = None
        if is_xero_account_linked:
            last_updated_at = self.company.xero_connection.updated_at
        self._highlights.append({
            'id': 'xero',
            'label': 'Xero',
            'value': is_xero_account_linked,
            'extras': {'last_updated_at': last_updated_at},
            'public': False,
            'order': 5
        })

    def __set_octopusbot(self):
        integration = self.company.octopusbot_integration
        last_updated_at = None
        if integration:
            history = self.company.history.order_by('history_date')
            last_updated_at = get(history.filter(octopusbot_integration=True).first(), 'history_date')
        self._highlights.append({
            'id': 'octopusbot',
            'label': 'OctopusBot',
            'value': integration,
            'extras': {'last_updated_at': last_updated_at},
            'public': False,
            'order': 7
        })

    def __set_power_bi(self):
        integration = self.company.power_bi
        last_updated_at = None
        if integration:
            history = self.company.history.order_by('history_date')
            last_updated_at = get(history.filter(power_bi=True).first(), 'history_date')
        self._highlights.append({
            'id': 'power_bi',
            'label': 'PowerBI',
            'value': integration,
            'extras': {'last_updated_at': last_updated_at},
            'public': False,
            'order': 6
        })


class Company(BaseModel, EstablishmentMixin):  # pylint: disable=too-many-instance-attributes, too-many-public-methods
    class Meta:
        db_table = "companies"
        constraints = [
            models.UniqueConstraint(fields=['abn'],
                                    condition=(~Q(abn=EMPTY_VALUE)),
                                    name='uniq_abn_except_dash'),
        ]

    WEEKDAYS = (
        (0, 'Sunday'),
        (1, 'Monday'),
        (2, 'Tuesday'),
        (3, 'Wednesday'),
        (4, 'Thursday'),
        (5, 'Friday'),
        (6, 'Saturday'),
    )
    MOBILE_FLOWS = (
        (LOGISTICS, 'Logistics'),
        (STORAGE, 'Storage'),
    )

    XERO_INVOICE_DRAFT_STATUS = 'DRAFT'
    XERO_INVOICE_STATUSES = (
        (XERO_INVOICE_DRAFT_STATUS, 'Draft'),
        ('SUBMITTED', 'Awaiting Approval'),
        ('AUTHORISED', 'Awaiting Payment'),
    )

    LOGO_PATH = 'companies/logos/'

    KEY_CONTACT_RELATION_NAME = 'company_key_contacts_set'

    CONTRACT_CREATION_RESTRICTIONS = (
        ('others_can_create_without_acceptance', 'Others can create without acceptance'),
        ('others_can_create_but_requires_acceptance', 'Others can create but requires acceptance'),
        ('others_cannot_create', 'Others cannot create'),
    )

    VARIETY_MANDATORY_LOAD_OPTIONS = (
        ('inload', 'Inload'),
        ('inload_and_outload', 'Inload and Outload'),
        ('title_transfers_and_cash_outs', 'Title Transfers and Cash Outs'),
    )

    VARIETY_MANDATORY_USER_OPTIONS = (
        ('growers', 'Growers'),
        ('non_growers', 'Non Growers'),
    )

    objects = NonSystemCompanyManager()
    moderators = SystemCompanyManager()
    all = DefaultCompanyManager()

    mandatory_props = BaseModel.mandatory_props + ['name', 'display_name', 'is_registered', 'country_code']
    common_many_to_one_relations = ['address', 'type', 'platformfeatures']
    report_recipients = ArrayField(models.EmailField(max_length=255), null=True, blank=True)
    abn = models.CharField(
        max_length=13,
    )
    entity_name = models.CharField(max_length=255, null=False, blank=False)
    business_name = models.CharField(max_length=255, null=False, blank=False)
    address = models.OneToOneField(
        Location,
        related_name='company',
        on_delete=models.DO_NOTHING,
    )
    mobile = models.CharField(
        max_length=10,
        null=False,
        blank=False,
        validators=[PHONE_MOBILE_REGEX]
    )
    website = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        validators=[WEBSITE_REGEX]
    )
    gst_number = models.CharField(
        max_length=30,
        null=True,
        blank=True,
    )

    owner_company_id = models.IntegerField(blank=True, null=True)
    type = models.ForeignKey(
        CompanyType,
        on_delete=models.DO_NOTHING,
        null=False,
        blank=False,
        default=5
    )
    start_of_week = models.IntegerField(choices=WEEKDAYS, null=False, blank=False, default=DEFAULT_START_OF_WEEK)
    end_of_week = models.IntegerField(choices=WEEKDAYS, null=False, blank=False, default=DEFAULT_END_OF_WEEK)
    payment_term = models.ForeignKey(
        'contracts.PaymentTerm',
        on_delete=models.DO_NOTHING,
        default=core.common.constants.DEFAULT_PAYMENT_TERM_ID
    )
    logo_path = models.CharField(max_length=255, null=True, blank=True)
    transaction_participation = models.BooleanField(default=False)
    mobile_participation = models.BooleanField(default=False)
    can_represent = models.BooleanField(default=False)  # can represent other parties in contract
    mobile_flow = models.CharField(choices=MOBILE_FLOWS, default=STORAGE, max_length=255)
    warehouse_charged_at = models.CharField(
        max_length=32,
        choices=WAREHOUSE_INLOAD_FEES_CHARGED_AT,
        default=OUTLOAD
    )  # warehouse invoicing charges
    warehouse_inload_charged_from = models.DateField(null=True, blank=True)
    warehouse_invoice_group_grades = models.BooleanField(default=False)
    restrict_slot_cancellation = models.BooleanField(default=False)
    show_costs_in_fm = models.BooleanField(default=False)
    hours_before_cancellation_stops = models.IntegerField(
        choices=list(zip(range(0, 49), range(0, 49))),
        null=False, blank=False, default=0)
    mobile_orders_within_delivery_range = models.BooleanField(default=False)
    purchase_contract_creation_restriction = models.CharField(choices=CONTRACT_CREATION_RESTRICTIONS, max_length=100,
                                                              default='others_can_create_without_acceptance')
    sale_contract_creation_restriction = models.CharField(choices=CONTRACT_CREATION_RESTRICTIONS, max_length=100,
                                                          default='others_can_create_without_acceptance')

    overdraft_transfer_allowed = models.BooleanField(default=True)
    brokerages_set = GenericRelation(
        'contracts.Brokerage', related_query_name='company',
        object_id_field='entity_id', content_type_field='entity_type'
    )

    idle_period_to_logoff = models.DurationField(default=timedelta(minutes=24*60))
    password_expiry_period = models.IntegerField(null=True, blank=True)
    approved_buyers = models.ManyToManyField(
        "self", symmetrical=False)
    can_upload_contract_csv = models.BooleanField(default=False)
    extras = models.JSONField(null=True, blank=True)
    rows_per_page = models.IntegerField(null=False, blank=False, default=30)
    enable_custom_csv = models.BooleanField(default=False)
    bulk_invoicing_from = models.DateField(null=True, blank=True)
    payment_due_days = models.IntegerField(default=15)
    multi_slot_booking = models.BooleanField(default=False)
    enable_transfer_to_approved_buyers = models.BooleanField(default=False)
    contract_invoicing_from = models.DateField(null=True, blank=True)
    agrichain_as_vendor_number = models.CharField(null=True, blank=True, max_length=255)
    bank_apca_number = models.CharField(max_length=6, null=True, blank=True)
    transaction_code = models.CharField(
        max_length=6,
        null=True,
        blank=True,
        unique=True
    )
    transaction_reference_number_configs = models.JSONField(
        default=dict,
        null=True,
        blank=True,
    )
    _payment_run_epr_levy_preference = models.JSONField(null=True, blank=True)
    payment_run = models.BooleanField(default=False)
    only_creator_can_amend_contract = models.BooleanField(default=False)
    only_creator_can_amend_order = models.BooleanField(default=False)  # for FO/GO
    warehouse_invoice_commodity_separated = models.BooleanField(default=False)
    mass_limit_emails = models.BooleanField(default=False)
    enable_my_stocks_on_mobile = models.BooleanField(default=False)
    automatically_add_inload = models.BooleanField(default=False)
    automatically_add_outload = models.BooleanField(default=False)

    # Xero
    xero_enabled = models.BooleanField(default=False)
    xero_client_id = models.CharField(max_length=255, null=True, blank=True)
    xero_client_secret = models.CharField(max_length=255, null=True, blank=True)
    xero_tenant_id = models.CharField(max_length=255, null=True, blank=True)
    xero_invoice_status = models.CharField(
        max_length=100, null=True, blank=True, choices=XERO_INVOICE_STATUSES, default=XERO_INVOICE_DRAFT_STATUS)

    acquisition_recipients = models.JSONField(null=True, blank=True)
    default_levy_epr = models.BooleanField(default=True, null=True, blank=True)
    show_cash_prices_to_all = models.BooleanField(default=True, null=True, blank=True)
    is_variety_mandatory = models.BooleanField(default=False, null=True, blank=True)
    load_type_for_variety_mandatory = ArrayField(
        models.CharField(choices=VARIETY_MANDATORY_LOAD_OPTIONS, max_length=255), null=True, blank=True, default=list)
    user_type_for_variety_mandatory = ArrayField(
        models.CharField(choices=VARIETY_MANDATORY_USER_OPTIONS, max_length=255), null=True, blank=True, default=list)
    is_independent_site_buyer = models.BooleanField(default=False, null=True, blank=True)
    is_selling_to_independent_site_buyer = models.BooleanField(default=False, null=True, blank=True)
    have_field = models.BooleanField(default=False)
    octopusbot_integration = models.BooleanField(default=False)
    power_bi = models.BooleanField(default=False)
    power_bi_report_id = models.CharField(max_length=100, null=True, blank=True)
    country = models.ForeignKey(
        'countries.Country', default=AUSTRALIA_COUNTRY_ID, on_delete=models.DO_NOTHING, related_name='companies')
    additional_mass_limit_codes = models.BooleanField(default=True, null=True, blank=True)
    groups = models.ManyToManyField('CompanyGroup', related_name='companies')
    acquisition_file_upload = models.BooleanField(default=False)
    contract_number_mandatory_in_transfers = models.BooleanField(default=False)
    is_variety_mandatory_in_commodity_dec = models.BooleanField(default=False)
    is_halal_declaration_mandatory = models.BooleanField(default=False)
    foreign_currency_allowed = models.BooleanField(default=False)
    fill_docket = models.BooleanField(default=True)
    freight_invoicing_from = models.DateField(null=True, blank=True)
    contract_allocations = ArrayField(
        models.CharField(choices=CONTRACT_ALLOCATION_TYPES, max_length=255), null=True, blank=True, default=list
    )
    _sftp_configuration = models.JSONField(null=True, blank=True)
    _expire_token_in_days = models.IntegerField(null=True, blank=True)
    warehouse_invoice_frequency = models.CharField(
        max_length=100, null=True, blank=True, choices=WAREHOUSE_INVOICE_FREQUENCIES,
        default=WAREHOUSE_INVOICE_MONTHLY_FREQUENCY
    )
    warehouse_invoice_start_of_week = models.IntegerField(
        choices=WEEKDAYS, null=True, blank=True, default=DEFAULT_START_OF_WEEK
    )
    _enable_facets = models.BooleanField(default=False)
    assign_truck_to_drivers = models.BooleanField(default=False)
    shrinkage_on_inload_for_all = models.BooleanField(default=False)
    include_levy_adjustments = models.BooleanField(default=True)
    movement_complete_manual = models.BooleanField(default=False)
    include_own_site_loads_in_acquisition_report = models.BooleanField(default=True)
    automatic_container_movement = models.BooleanField(default=False)
    pickup_details_warning = models.BooleanField(default=False)
    delivery_details_warning = models.BooleanField(default=False)
    abn_status = models.CharField(choices=ABN_STATUS_TYPES, max_length=100, null=True, blank=True)
    abn_status_last_updated_at = models.DateTimeField(null=True, blank=True)
    show_additional_specs = models.BooleanField(default=False)
    auto_subscribe_ngr = models.BooleanField(default=False)
    cor_texts = models.JSONField(null=True, blank=True)
    impex_docs_enabled = models.BooleanField(default=False)
    manual_shipment_creation_for_impex_docs = models.BooleanField(default=False)
    csv_split_blended_contracts = models.BooleanField(default=True)
    show_email_popup = models.BooleanField(default=True)
    non_grower_with_ngr = models.BooleanField(default=False, null=True)
    show_throughput_load = models.BooleanField(default=False)
    enable_resending_email = models.BooleanField(default=False)
    hide_contract_and_order_for_drivers = models.BooleanField(default=False)
    customised_pack_and_ship = models.BooleanField(default=False)

    FILLABLES = [
        'abn',
        'business_name',
        'entity_name',
        'address',
        'mobile',
        'website',
        'gst_number',
        'owner_company_id',
        'type_id',
        'address_id',
        'start_of_week',
        'end_of_week',
        'payment_term_id',
        'logo',
        'brokerages',
        'office_id',
        'transaction_participation',
        'mobile_participation',
        'can_represent',
        'mobile_flow',
        'asset_id',
        'asset',
        'add',
        'delete',
        'plan_type',
        'idle_period_to_logoff',
        'password_expiry_period',
        'report_recipients',
        'warehouse_charged_at',
        'warehouse_inload_charged_from',
        'restrict_slot_cancellation',
        'show_costs_in_fm',
        'hours_before_cancellation_stops',
        'mobile_orders_within_delivery_range',
        'purchase_contract_creation_restriction',
        'sale_contract_creation_restriction',
        'company_ids',
        'overdraft_transfer_allowed',
        'can_upload_contract_csv',
        'extras',
        'rows_per_page',
        'enable_custom_csv',
        'default_directory_company',
        'employees',
        'trucks',
        'officies',
        'ngrs',
        'storages',
        'farms',
        'fields',
        'offices',
        'warehouse_invoice_group_grades',
        'bulk_invoicing_from',
        'payment_due_days',
        'multi_slot_booking',
        'enable_transfer_to_approved_buyers',
        'contract_invoicing_from',
        'bank_apca_number',
        'transaction_code',
        'transaction_reference_number_configs',
        'payment_run_epr_levy_preference',
        'warehouse_invoice_commodity_separated',
        'mass_limit_emails',
        'code',
        'redirect_uri',
        'xero_enabled',
        'xero_client_id',
        'xero_client_secret',
        'xero_tenant_id',
        'xero_invoice_status',
        'enable_my_stocks_on_mobile',
        'show_cash_prices_to_all',
        'acquisition_recipients',
        'default_levy_epr',
        'automatically_add_inload',
        'automatically_add_outload',
        'is_variety_mandatory',
        'show_throughput_load',
        'load_type_for_variety_mandatory',
        'user_type_for_variety_mandatory',
        'is_independent_site_buyer',
        'is_selling_to_independent_site_buyer',
        'have_field',
        'include_parties',
        'only_creator_can_amend_contract',
        'only_creator_can_amend_order',
        'octopusbot_integration',
        'country_id',
        'additional_mass_limit_codes',
        'group_id',
        'group_type',
        'group_name',
        'is_add_new_group',
        'company_ids',
        'business_type_id',
        "grade",
        "tonnage",
        "external_reference",
        "season",
        "transtype",
        "farm_id",
        "commodity",
        "ngr_id",
        "storage",
        "change_to",
        "change_from",
        "change_to_ngr",
        "change_from_ngr",
        "date_time",
        "differential",
        "farm_company",
        "farm",
        "ngr_number",
        "loads",
        "process_on",
        "handler_id",
        "new_grade",
        "new_season",
        "variety",
        "acquisition_file_upload",
        "external_contract_number",
        "new_variety",
        "contract_number_mandatory_in_transfers",
        "is_variety_mandatory_in_commodity_dec",
        "is_halal_declaration_mandatory",
        "foreign_currency_allowed",
        "fill_docket",
        "freight_invoicing_from",
        "contract_allocations",
        "warehouse_invoice_frequency",
        "warehouse_invoice_start_of_week",
        'abns',
        'show_email_popup',
        'ngr_details',
        '_enable_facets',
        'shrinkage_on_inload_for_all',
        'assign_truck_to_drivers',
        'include_levy_adjustments',
        'movement_complete_manual',
        'include_own_site_loads_in_acquisition_report',
        'automatic_container_movement',
        'commodity_id',
        'rate',
        'pickup_details_warning',
        'delivery_details_warning',
        'email',
        'show_additional_specs',
        'auto_subscribe_ngr',
        'cor_texts',
        'auto_subscribe_ngr',
        'impex_docs_enabled',
        'manual_shipment_creation_for_impex_docs',
        'company_type',
        'user',
        'impex_docs_mappings',
        'grade_id',
        'variety_id',
        'product_code',
        'csv_split_blended_contracts',
        'payment_run',
        'non_grower_with_ngr',
        'power_bi_report_id',
        'hide_contract_and_order_for_drivers',
        'customised_pack_and_ship',
        'enable_resending_email'
    ]

    WEB_SEARCHABLE_FIELDS = [
        'business_name',
        'abn',
        'key_contact_name_for_user',
        'mobile',
        'address.address',
        'website',
        'type.display_name',
        'is_registered_text',
    ]

    def get_powerbi_report_metadata(self):
        if self.power_bi_report_id:
            from core.services.external.power_bi import PowerBI
            return PowerBI(self.power_bi_report_id).get_report_metadata()
        return None

    def get_overview(self, include_private=False):
        return CompanyOverview(self, include_private).to_dict()

    def get_sftp_service(self):
        if get(self._sftp_configuration, 'hostname') and get(self._sftp_configuration, 'location') :
            from core.services.external.sftp import SFTP
            return SFTP(
                hostname=self._sftp_configuration['hostname'],
                username=self._sftp_configuration['username'],
                password=self._sftp_configuration['password']
            )
        return None

    def push_to_company_SFTP(self, file):
        try:
            service = self.get_sftp_service()
            if service:
                service.put_file(file.name, self.sftp_location_path)
        except Exception as ex:  # pylint: disable=broad-except
            ERRBIT_LOGGER.raise_errbit(f"Unable to push {file.name} to {self.name} SFTP, Error: {str(ex)}")

    @property
    def sftp_location_path(self):
        return self._sftp_configuration.get('location') if self._sftp_configuration else None

    @classmethod
    def position_report_queryset(cls):
        return Company.objects.filter(
            id__in=Company.objects.filter(
                models.Q(octopusbot_integration=True) | models.Q(_sftp_configuration__isnull=False)
            ).values_list('id', flat=True)
        )

    @classmethod
    def get_test_company_ids(cls):
        return cls.objects.exclude(type_id=SYSTEM_TYPE_ID).filter(
            cls.test_companies_criteria()
        ).values_list('id', flat=True)

    @staticmethod
    def test_companies_criteria():
        return Q(
            business_name__icontains='agrichain'
        ) | Q(business_name__contains='Test')

    @staticmethod
    def similarity_search_criteria(search_str=None):
        similarity_score_criteria = (
            Q(business_name_similarity__gt=SIMILARITY_SEARCH_SCORE)
            | Q(entity_name_similarity__gt=SIMILARITY_SEARCH_SCORE)
        )

        if search_str:
            search_criteria = (
                Q(business_name__icontains=search_str)
                | Q(entity_name__icontains=search_str)
                | Q(extras__alias__icontains=search_str)
            )
            return search_criteria | similarity_score_criteria

        return similarity_score_criteria


    @property
    def is_aus(self):
        return self.country_id == AUSTRALIA_COUNTRY_ID

    @property
    def is_us(self):
        return self.country_id == USA_COUNTRY_ID

    @property
    def is_ca(self):
        return self.country_id == CANADA_COUNTRY_ID

    @property
    def is_nz(self):
        return self.country_id == NZ_COUNTRY_ID

    @property
    def country_code(self):
        if self.is_aus:
            return AUSTRALIA_COUNTRY_CODE
        if self.is_us:
            return USA_COUNTRY_CODE
        if self.is_ca:
            return CANADA_COUNTRY_CODE
        if self.is_nz:
            return NZ_COUNTRY_CODE

    @property
    def system_company_id(self):
        return self.country.system_company_id

    @property
    def verify_impex_docs_connection(self):
        if not self.impex_docs_enabled:
            return False
        return bool(self._get_impex_docs_token())

    def _get_impex_docs_token(self):
        connection = self.impex_docs_connection
        if connection:
            return connection.refresh()

    def _has_impex_docs_connection(self, condition):
        return bool(condition and self.impex_docs_connection and self.verify_impex_docs_connection)

    @property
    def has_impex_docs_connection(self):
        return self._has_impex_docs_connection(self.impex_docs_enabled)

    @property
    def has_impex_docs_connection_for_auto_creation(self):
        return self._has_impex_docs_connection(
            self.impex_docs_enabled and not self.manual_shipment_creation_for_impex_docs)

    @property
    def has_impex_docs_connection_for_manual_creation(self):
        return self._has_impex_docs_connection(self.impex_docs_enabled and self.manual_shipment_creation_for_impex_docs)

    @property
    def impex_docs_connection(self):
        return getattr(self, 'impexdocsconnection', None)

    def get_impex_docs_service(self, validate=False, **kwargs):
        if validate:
            params = self.get_impex_docs_params()
            if not params:
                return params
        else:
            connection = self.impex_docs_connection
            if connection:
                params = {
                    'client_id': connection.client_id,
                    'client_secret': connection.client_secret
                }

        return ImpexDocs(**params, **kwargs)

    def get_impex_docs_params(self):
        result = False
        if self.has_impex_docs_connection:
            connection = self.impex_docs_connection
            token = self._get_impex_docs_token()
            if token:
                result = {
                    'token': token,
                    'client_id': connection.client_id,
                    'client_secret': connection.client_secret,
                    'user_id': connection.user_id,
                    'user_email': connection.user_email
                }
        return result

    @property
    def is_impex_docs_account_linked(self):
        return bool(self.impex_docs_enabled and get(self.impex_docs_connection, 'id'))

    @property
    def impex_docs_client_id(self):
        connection = self.impex_docs_connection
        return connection.client_id if connection else None

    @property
    def impex_docs_client_secret(self):
        connection = self.impex_docs_connection
        return connection.client_secret if connection else None

    @property
    def impex_docs_user_email(self):
        connection = self.impex_docs_connection
        return connection.user_email if connection else None

    def is_existing_product_code(self, product_code, mapping_id=None):
        mappings = self.impex_product_mappings.filter(product_code=product_code)

        if mapping_id:
            mapping = ImpexDocsProductMapping.objects.get(id=mapping_id)
            mappings = mappings.exclude(id=mapping_id).exclude(commodity_id=mapping.commodity_id)
        return mappings.exists()

    def check_product_mapping_exists(self, commodity_id, variety_id, grade_id, mapping_id=None):
        mappings = self.impex_product_mappings.filter(models.Q(commodity_id=commodity_id))

        if variety_id:
            mappings = mappings.filter(variety_id=variety_id)
        if grade_id:
            mappings = mappings.filter(grade_id=grade_id)

        if mapping_id:
            mappings = mappings.exclude(id=mapping_id)
        return mappings.exists()

    @classmethod
    def best_match_company_by_name(cls, queryset, name):
        return cls.get_similarity_queryset(queryset, name).first()

    @classmethod
    def get_similarity_queryset(cls, queryset, name):
        return cls.similarity_search(
            queryset, name, ['business_name', 'entity_name'], ['extras__alias']
        ).filter(
            Company.similarity_search_criteria(name)
        ).order_by('-exact_match_score', '-contains', '-business_name_similarity', '-entity_name_similarity')

    def get_xero_service(self, validate=False, **kwargs):
        if validate:
            params = self.get_xero_params()
            if not params:
                return params
        else:
            params = {
                'client_id': self.xero_client_id,
                'client_secret': self.xero_client_secret,
                'tenant_id': self.xero_tenant_id
            }

        return Xero(**params, **kwargs)

    def set_xero_tenant_id(self):
        connection = self.xero_connection
        if connection:
            tenant_id = connection.get_tenant_id()
            if tenant_id:
                self.xero_tenant_id = tenant_id
                self.save()

    @property
    def is_xero_account_linked(self):
        return bool(self.xero_enabled and self.xero_connection)

    @property
    def is_valid_xero_connection(self):
        if not self.xero_enabled:
            return False
        connection = self.xero_connection
        return bool(connection and not connection.is_expired and self.verify_xero_connection)

    @property
    def verify_xero_connection(self):
        if not self.xero_enabled:
            return False

        try:
            return bool(self._get_xero_token())
        except:  # pylint: disable=bare-except
            pass

        return False

    @property
    def xero_connection(self):
        return getattr(self, 'xeroconnection', None)

    @property
    def has_tracking_categories(self):
        return self.xero_tracking_categories.exists()

    def get_xero_params(self):
        result = False
        if self.is_valid_xero_connection:
            token = self._get_xero_token()
            if token:
                result = {
                    'token': token,
                    'client_id': self.xero_client_id,
                    'client_secret': self.xero_client_secret,
                    'tenant_id': self.xero_tenant_id
                }

        return result

    def _get_xero_token(self):
        connection = self.xero_connection
        if connection:
            return connection.refresh()

    @property
    def external_booking_connection(self):
        return self.external_booking_connections.first()

    @property
    def has_external_booking_enabled(self):
        return self.external_booking_connections.exists()

    def get_external_booking_service(self, validate=False):
        params = None
        if validate:
            params = self.get_external_booking_params()
            if not params:
                return params
        else:
            connection = self.external_booking_connection
            if connection:
                params = {
                    'client_credentials': connection.encoded_client_credentials,
                    'refresh_token': connection.refresh_token,
                    'access_token': connection.access_token,
                    'base_url': connection.base_url,
                    'url_paths': connection.url_paths,
                }
        if params:
            return ExternalBooking(**params)

    def get_external_booking_params(self):
        result = False
        connection = self.external_booking_connection
        if connection:
            data = connection.refresh_tokens()
            result = {
                'client_credentials': connection.encoded_client_credentials,
                'refresh_token': get(data, 'refresh_token'),
                'access_token': get(data, 'access_token'),
                'base_url': connection.base_url,
                'url_paths': connection.url_paths,
            }
        return result

    def add_buyer_to_site_approved_buyers_if_not_present(self, company_id, send_mail=False):
        if not self.approved_buyers.filter(id=company_id).exists():
            buyer = Company.objects.filter(id=company_id).first()
            self.approved_buyers.add(buyer)
            if send_mail and buyer.has_company_admin():
                from core.jobs.models import Job
                Job.schedule_job_for_task(
                    'send_email_to_added_approved_buyer',
                    params={
                        'company_id': self.id,
                        'buyer_company_id': buyer.id,
                    }
                )

    def process_external_booking_operation(self, data, operation_name):
        connection = self.external_booking_connection
        if connection:
            result = {'valid_connection': True}
            service = self.get_external_booking_service(True)
            if service:
                func = get(service, operation_name)
                response = func(data, self.id) if operation_name == 'send_slot_information' else func(data)
                if get(response, 'success'):
                    result.update({'booking_number': get(response, 'bookingNumber')})
                elif isinstance(response, list):  # returning error messages list
                    result.update({'errors': [obj["message"] for obj in response]})
            return result

    def send_slot_information(self, data):
        return self.process_external_booking_operation(data, 'send_slot_information')

    def update_slot_information(self, data):
        return self.process_external_booking_operation(data, 'update_slot_information')

    def cancel_external_booking(self, data):
        return self.process_external_booking_operation(data, 'cancel_external_booking')

    def expire_external_booking(self, data):
        return self.process_external_booking_operation(data, 'expire_external_booking')

    def send_truck_vendor_declaration(self, data):
        return self.process_external_booking_operation(data, 'send_truck_vendor_declaration')

    @property
    def warehouse_fees_with_grades_combo_exists(self):
        from core.farms.models import WarehouseFees
        return WarehouseFees.objects.filter(site__company_id=self.id, grade__isnull=False).exists()

    @classmethod
    def create_by_name_and_random_abn(cls, name, user, type_id=LOGISTICS_TYPE_ID):
        random_abn = "999999{}".format(str(str(timezone.now().timestamp())[:5]))
        params = {
            'business_name': name,
            'entity_name': name,
            'abn': random_abn,
            'type_id': type_id,
            'mobile': '*********',
            'address': AUSTRALIA,
            'owner_company_id': user.company_id,
            'created_by': user,
            'updated_by': user
        }
        company = cls.create_with_location(params)

        if company.persisted:
            company.main_farm = apps.get_model('farms', 'Farm').create_main_farm(company=company)
            if company.main_farm.persisted:
                company.created_by.company.add_farm_to_directory(company.main_farm.id)
                company.created_by.company.add_company_to_directory(company.id)

            apps.get_model('ngrs', 'Ngr').create_unknown(company)

        return company

    @classmethod
    def create_by_abn(  # pylint: disable=too-many-locals
            cls, abn, user, type_id=GROWER_TYPE_ID,
            business_name=None, address_name=None, latitude=None, longitude=None, mobile=None
    ):
        country = Country.get_requesting_country()
        if country.get_abn_service_class:
            details = country.get_abn_service_class.get_details(abn)

            default_lat, default_lng = Country.get_default_lat_lng()
            if get(details, 'errors', {}).get('abn'):
                abn_errors = get(details, 'errors').get('abn')
                if 'Cancelled ABN' in abn_errors:
                    company = Company(abn=abn)
                    company.errors = {'abn': [f'The party has Cancelled ABN {abn}']}
                    return company

            if get(details, 'errors') or get(details, 'alert'):
                business_name = business_name or UNKNOWN
                entity_name = UNKNOWN
                address = AUSTRALIA
            else:
                business_name = business_name or details.business_name or details.entity_name
                entity_name = details.entity_name
                address = details.address or AUSTRALIA

            params = {
                'business_name': business_name,
                'entity_name': entity_name,
                'abn': abn,
                'address': {
                    'name': address_name or address,
                    'address': address_name or address,
                    'latitude': latitude or default_lat,
                    'longitude': longitude or default_lng,
                },
                'type_id': type_id,
                'mobile': mobile or '*********4',
                'owner_company_id': user.company_id,
                'created_by': user,
                'updated_by': user
            }

            company = cls.create_with_location(params)

            if company.persisted:
                company.main_farm = apps.get_model('farms', 'Farm').create_main_farm(company=company,)
                if company.main_farm.persisted:
                    company.created_by.company.add_farm_to_directory(company.main_farm.id)
                    company.created_by.company.add_company_to_directory(company.id)
                Company.add_company_to_super_directory(company.id)
                apps.get_model('ngrs', 'Ngr').create_unknown(company)

            return company
        return None

    @classmethod
    def find_by_name(cls, name, type_id=LOGISTICS_TYPE_ID):
        companies = cls.objects.filter(business_name__iexact=name.strip(), type_id=type_id)

        if companies.count() == 1:
            return companies.first()

        return None

    @classmethod
    def get_by_name(cls, name):
        name = strip_special(name)
        expression = CustomRegexWrapper('business_name').expression
        return cls.objects.annotate(_name=expression).filter(_name=name).first()

    @classmethod
    def find_by_abn(cls, abn):
        return cls.objects.filter(abn__iexact=abn.strip()).first()

    @classmethod
    def find_or_create_by_name(cls, name, user, type_id=LOGISTICS_TYPE_ID):
        company = cls.find_by_name(name, type_id)
        if not company:
            company = cls.create_by_name_and_random_abn(name, user, type_id)

        return company

    @classmethod
    def find_or_create_by_abn(cls, abn, user, type_id=GROWER_TYPE_ID):
        company = cls.find_by_abn(abn)
        if not company:
            company = cls.create_by_abn(abn, user, type_id)

        return company

    def reset_mobile_flow(self):
        if self.is_eligible_for_mobile_flow(LOGISTICS) and not self.is_mobile_flow_logistics:
            self.mobile_flow = LOGISTICS
            self.save()
        elif self.is_eligible_for_mobile_flow(STORAGE) and not self.is_mobile_flow_storage:
            self.mobile_flow = STORAGE
            self.save()

    def is_eligible_for_mobile_flow(self, flow):
        if flow == LOGISTICS:
            return self.type_id in [2, 3]
        if flow == STORAGE:
            return self.type_id in [1, 4, 5]

    @property
    def is_mobile_flow_logistics(self):
        return self.__is_mobile_flow_same_as(LOGISTICS)

    @property
    def is_mobile_flow_storage(self):
        return self.__is_mobile_flow_same_as(STORAGE)

    def __is_mobile_flow_same_as(self, flow):
        return self.mobile_flow == flow

    @classmethod
    def assign_system_manager(cls):
        cls._assign_manager(DefaultCompanyManager)

    @classmethod
    def assign_non_system_manager(cls):
        cls._assign_manager(NonSystemCompanyManager)

    @classmethod
    def _assign_manager(cls, manager_klass):
        manager = manager_klass()
        manager.model = cls
        cls.objects = manager
        cls._meta.default_manager = manager

    def my_stocked_farms(self, only_containers=False):
        from core.farms.models import Farm
        from core.loads.models import Load
        loads = Load.objects.exclude(status='void').filter(self.owned_and_managed_farm_loads_criteria())
        loads = Load.apply_containers_filter(loads, only_containers)
        return Farm.objects.filter(id__in=loads.filter(storage_id__isnull=False).values_list('farm_id', flat=True))

    def managed_farms_company_ids(self):
        return self.own_and_managed_farm_company_ids

    def get_acquisition_inloads(self):
        # Current Time -> 25/10/2022 16:01 UTC => 26/10/2022 00:01 AWST
        # Server is in UTC
        # timezone.now() => 25/10/2022 16:01 UTC
        # this report assumes that the data is for the previous day only
        # so anything created yesterday with date_time in past is also added
        farms = self.possible_stocked_farms(only_my_stocks=True)
        if not self.include_own_site_loads_in_acquisition_report:
            farms = farms.exclude(company_id=self.id)
        if not self.transaction_participation:
            farms = farms.filter(stocks_management=True)
        company_list = self.managed_farms_company_ids()
        data_date_time = timezone.now() - timedelta(days=1)  # 24/10/2022 16:01 UTC
        start_time = data_date_time.replace(hour=16, minute=0, second=0, microsecond=0)  # 24/10/2022 16:00 UTC
        end_time = start_time + timedelta(hours=23, minutes=59)   # 25/10/2022 15:59 UTC
        from core.loads.models import Load
        from core.farms.models import Storage
        loads = Load.objects.filter(
            ngr__owner_company_ids__overlap=list(set(company_list)),
            type=Load.INLOAD, farm_id__in=farms,
            storage__type__in=Storage.FIXED_STORAGE_TYPES
        ).filter(
            models.Q(date_time__gte=start_time, date_time__lte=end_time) |
            models.Q(created_at__gte=start_time, created_at__lte=end_time)
        ).exclude(ngr_id__isnull=True).order_by(
            'farm__name', 'commodity', 'grade', 'season', 'ngr', '-date_time'
        )
        # we are excluding own site loads if include_own_site_loads_in_acquisition_report is false
        # in case we have to include own site loads, internal movements from same site to same site has to be ignored
        # for now excluding from field to storages as well if site is same
        if self.include_own_site_loads_in_acquisition_report:
            loads = loads.exclude(source__in=[Load.SHRINKAGE_LOAD])
            loads = loads.exclude(
                movement__isnull=False,
                movement__freight_pickup__loads_set__farm_id=models.F('movement__freight_delivery__loads_set__farm_id')
            )
        return loads

    def possible_stocked_farms(self, only_my_farms=False, only_my_stocks=None, only_containers=False):
        if only_my_stocks in ['true', True]:
            return self.my_stocked_farms(only_containers).order_by('name')
        if only_my_stocks in ['false', False]:
            from core.loads.models import Load

            loads = Load.objects.exclude(status=VOID_STATUS).filter(self.third_party_stocks_criteria())
            loads = Load.apply_containers_filter(loads, only_containers)

            return self.farm_set.filter(id__in=loads.filter(storage_id__isnull=False).values_list('farm_id', flat=True))
        queryset = self.owned_and_managed_farms
        if not only_my_farms:
            queryset |= self.my_stocked_farms(only_containers)
            from core.farms.models import Farm
            queryset = Farm.objects.filter(id__in=queryset.values_list('id', flat=True))
        return queryset.order_by('name')

    def my_farms(self):
        return self.owned_and_managed_farms.order_by('name')

    @cached_property
    def owned_and_managed_farms(self):
        from core.farms.models import Farm
        return Farm.objects.filter(
            Q(company_id=self.id) | Q(broker_company_id=self.id) if self.is_broker else Q(company_id=self.id)
        )

    @cached_property
    def own_and_managed_farm_company_ids(self):
        return self.owned_and_managed_farms.values_list('company_id', flat=True)

    def my_and_third_party_loads_criteria(self):
        # this doesn't use self.third_party_loads_criteria because of
        # exclusion clause which is included in my loads criteria
        return self.owned_and_managed_farm_loads_criteria() | Q(farm_id__in=self.owned_and_managed_farms,
                                                                storage_id__isnull=False)

    def owned_and_managed_farm_loads_criteria(self):
        return self.owner_stocks_criteria(self.own_and_managed_farm_company_ids)

    @staticmethod
    def owner_stocks_criteria(owner_company_ids):
        from core.loads.models import Load
        return Load.owner_stocks_criteria(owner_company_ids)

    # filter my farms and exclude my single ngr
    def third_party_stocks_criteria(self):
        from core.loads.models import Load
        return Load.third_party_stocks_criteria(self.id)

    def to_csv_row(self, user, headers):
        key_contact = self._key_contact_for_user(user)
        country = self.country
        key_contact_number = get(key_contact, 'employee.mobile') \
                             and country.to_phone_format(get(key_contact, 'employee.mobile'))
        columns_mapping = {
            "Business Name": self.business_name,
            "Entity Name": self.entity_name,
            "ABN": self.abn,
            "Business Type": self.type.name,
            "Registered": self.is_registered,
            "Address": get(self, 'address.address'),
            "Company Phone/Mobile": country.to_phone_format(self.mobile),
            "Key Contact": get(key_contact, 'employee.name'),
            "Key Contact Email": get(key_contact, 'employee.email'),
            "Key Contact Mobile": key_contact_number,
            "Payment Term": self.payment_term.get_name_display() if self.payment_term_id else None,
            "Created On": DateTimeUtil.localize_date(self.created_at, country.timezone, country.get_format("datetime"))
        }
        if user.is_staff:
            created_by = self.created_by
            columns_mapping.update({
                "Subscriber": self.transaction_participation,
                "Mobile Subscriber": self.mobile_participation,
                "ID": self.id,
                "Created By": created_by.name,
                "Created By Company": created_by.company.name
            })
        return [columns_mapping.get(column) for column in headers]

    @classmethod
    def to_admin_stocks_upload_csv_row(cls, data_type, item):
        row = []
        if data_type == SITES:
            row = [
                get(item, 'company_name'),
                get(item, 'company_id'),
                get(item, 'name'),
                get(item, 'id'),
                get(item, 'address.address') or get(item, 'address.name'),
                get(item, 'market_zone.name'),
                get(item, 'region.name'),
            ]
        elif data_type == NGR:
            row = [
                get(item, 'company_name'),
                get(item, 'company_id'),
                get(item, 'ngr_number'),
                get(item, 'id'),
            ]
        elif data_type == STORAGES:
            row = [
                get(item, 'operator_name'),
                get(item, 'operator_id'),
                get(item, 'farm_name'),
                get(item, 'farm_id'),
                get(item, 'name'),
                get(item, 'id'),
                get(item, 'type_name'),
                get(item, 'size'),
            ]
        elif data_type == EMPLOYEES:
            row = [
                get(item, 'company.name'),
                get(item, 'company_id'),
                get(item, 'name'),
                get(item, 'id'),
                get(item, 'type.display_name'),
                get(item, 'title'),
                get(item, 'mobile'),
                get(item, 'email'),
            ]
        return row

    @property
    def plan_type(self):
        return getattr(self, 'platformfeatures', None) and self.platformfeatures.plan_type

    @property
    def is_independent_farmer(self):
        return self.is_grower and self.farm_set.count() == 1

    @property
    def display_name(self):
        return self.name

    @property
    def independent_farm_id(self):
        farm_id = None
        if self.is_grower:
            farms = self.farm_set.all()
            if len(farms) == 1:
                farm_id = farms[0].id

        return farm_id

    @property
    def independent_farm(self):
        farm = None
        if self.is_grower:
            farms = self.farm_set.all()
            if len(farms) == 1:
                farm = farms[0]

        return farm

    @property
    def company_id(self):
        return self.id

    @property
    def company_name(self):
        return self.name

    @property
    def employees_exist(self):
        return self.employee_set.exists()

    @property
    def active_non_drivers_employees(self):
        return self.employee_set.filter(is_active=True).exclude(type_id=core.common.constants.DRIVER_TYPE_ID)

    @property
    def name(self):
        return self.business_name

    @property
    def formatted_name(self):
        return UNKNOWN if self.is_system else self.name

    @property
    def formatted_entity_name(self):
        return UNKNOWN if self.is_system else self.entity_name

    @classmethod
    def formatted_names(cls, company_ids):
        return [
            company.formatted_name for company in Company.all.filter(
                id__in=company_ids).only('business_name', 'id', 'type_id')
        ]

    @property
    def is_subscriber(self):
        return bool(self.transaction_participation)

    @property
    def is_registered(self):
        return self.owner_company_id is None

    @property
    def is_registered_text(self):
        return 'Yes' if self.is_registered else 'No'

    @property
    def is_registered_or_managed_by_registered(self):
        return self.is_registered

    @property
    def is_transaction_participator_or_managed_by_transaction_participator(self):
        return self.transaction_participation

    @property
    def is_registered_grower(self):
        return self.is_grower and self.is_registered

    @property
    def is_unregistered_grower(self):
        return self.is_grower and not self.is_registered

    @property
    def is_registered_broker_or_logistics_or_trader(self):
        return self.is_registered and self.is_broker_or_logistics_or_trader

    @property
    def is_unregistered_broker_or_logistics_or_trader(self):
        return not self.is_registered and self.is_broker_or_logistics_or_trader

    @property
    def is_broker_or_logistics_or_trader(self):
        return self.is_broker or self.is_logistics or self.is_trader

    @property
    def is_grower(self):
        return self.type_id == core.common.constants.GROWER_TYPE_ID

    @property
    def is_broker(self):
        return self.type_id == core.common.constants.BROKER_TYPE_ID

    @property
    def is_logistics(self):
        return self.type_id == core.common.constants.LOGISTICS_TYPE_ID

    @property
    def is_trader(self):
        return self.type_id == core.common.constants.TRADER_TYPE_ID

    @property
    def is_system(self):
        return self.type_id == core.common.constants.SYSTEM_TYPE_ID

    @property
    def is_agrichain(self):
        return self.type_id == core.common.constants.SYSTEM_TYPE_ID and self.abn == core.common.constants.AGRICHAIN_ABN

    @property
    def is_allowed_as_buyer_for_pool_contract(self):
        return self.abn in core.common.constants.ABNS_FOR_POOL_CONTRACTS

    @property
    def logo_url(self):
        url = None
        if self.logo_path:
            url = core.services.external.aws.S3.public_url_for(self.logo_path)

        return url

    @classmethod
    def directory_companies_queryset(
            cls, company_id, filters=None, include_self=False, return_ids=False, country_code=None,
            include_system_company=False
        ):
        added_company_ids = cls.__get_added_companies_ids(company_id, filters=filters, country_code=country_code)

        if return_ids:
            if include_self:
                return set([*added_company_ids, company_id])
            return set(added_company_ids)
        country = Country.get_requesting_country(country_code)
        criteria = Q(id__in=added_company_ids)
        if include_self:
            criteria |= Q(id=company_id)
        company_model_manager = cls.all if include_system_company else cls.objects
        if country:
            return company_model_manager.filter(country=country).filter(criteria)
        return company_model_manager.filter(criteria)

    @classmethod
    def directory_companies(cls, company_id, filters=None, include_self=False, country_code=None):
        return cls.directory_companies_queryset(company_id, filters, include_self, country_code=country_code).\
            select_related('type', 'address').prefetch_related('farm_set')

    @classmethod
    def directory_companies_only(cls, company_id, filters=None, include_self=False, include_system_company=False):
        exclusions = []
        if not include_system_company:
            exclusions.extend(SYSTEM_COMPANY_IDS)
        if not include_self:
            exclusions.append(company_id)
        return cls.directory_companies_queryset(
            company_id, filters, include_self, include_system_company=include_system_company
        ).exclude(id__in=exclusions).select_related('platformfeatures')

    # This is only used for broadcasting SM messages on UI - Sny/Devansh
    def get_registered_directory_company_employees(self):
        from core.profiles.models import Employee
        return Employee.objects.filter(
            company_id__in=AddedCompany.objects.filter(
                subject_company_id=self.id, object_company__owner_company_id__isnull=True
            ).values_list('object_company_id').distinct()
        ).exclude(company_id=self.id)

    @staticmethod
    def bhc_companies():
        return Company.objects.filter(type_id=core.common.constants.BHC_TYPE_ID)

    def get_companies(self, return_ids=False):
        queryset = self.directory_companies_queryset(self.company_id)

        if return_ids:
            return list(queryset.values_list('id', flat=True))

        return queryset.prefetch_related(*Company.common_many_to_one_relations)

    companies = property(get_companies)

    @cached_property
    def directory_companies_ids(self):
        return list(Company.objects.values_list('id', flat=True)) if self.is_system else list(
            self.directory_companies_queryset(self.company_id, None, False, True)
        )

    @staticmethod
    def __get_added_companies_ids(company_id, filters=None, country_code=None):
        filters = filters or {}
        return AddedCompany.objects.filter(
            models.Q(
                subject_company_id=company_id, **filters) | models.Q(
                subject_company_id__isnull=True,
                object_company__country_id=get_request_country_id(country_code),
            )
        ).values_list(
            'object_company_id', flat=True
        )

    def get_system_storage_for(self, location_id):  #  -- should be deleted soon - Sny
        return self.owner_system_storages.filter(location_id=location_id).first()

    def get_farms(
            self,
            include_unaccepted=None,
            broker_company_id=None,
            self_farms_only = False,
            use_is_active_flag = True
        ):
        farm_kwargs = {'include_unaccepted': include_unaccepted} if include_unaccepted is not None else {}

        if self.is_grower:
            return self._grower_farms(**farm_kwargs, broker_company_id=broker_company_id)
        if self.is_broker and not self_farms_only:
            return self._broker_farms(**farm_kwargs)
        if self.is_logistics and not self_farms_only:
            return self._logistics_farms()

        filters = {}
        if use_is_active_flag:
            filters = {"is_active": True}

        return self.farm_set.filter(**filters)

    farms = property(get_farms)

    def get_directory_farms(self):
        return apps.get_model('farms', 'Farm').objects.filter(farmdirectory__company_id=self.id)

    def _grower_farms(self, include_unaccepted=True, broker_company_id=None):
        if not include_unaccepted:
            return self.farm_set.select_related('farmacceptancerequest').filter(
                Q(farmacceptancerequest__isnull=True) |
                Q(farmacceptancerequest__resolved=True)
            )
        if include_unaccepted and broker_company_id:
            return self.farm_set.filter(broker_company_id=broker_company_id)
        return self.farm_set.all()

    def _broker_farms(self, include_unaccepted=False):
        if not include_unaccepted:
            query_set = apps.get_model('farms', 'Farm').objects.select_related('farmacceptancerequest').filter()
            return query_set.filter(
                broker_company_id=self.id,
                farmacceptancerequest__resolved=True,
                farmacceptancerequest__accepted=True
            )
        return self.get_directory_farms()

    def _logistics_farms(self):
        return apps.get_model('farms', 'Farm').objects.select_related('farmacceptancerequest').filter(
            company_id__in=self.get_companies(return_ids=True)
        ).filter(
            Q(farmacceptancerequest__isnull=True) |
            Q(farmacceptancerequest__accepted=True, farmacceptancerequest__resolved=True)
        )

    def get_logical_ngr_id(self):
        return self.logical_ngr_id(self.id)

    @staticmethod
    def logical_ngr_id(company_id):
        from core.ngrs.models import Ngr
        ngrs_qs = Ngr.objects.filter(company_id=company_id)
        ngrs = ngrs_qs.exclude(ngr_number__icontains='UNKNOWN_').filter(ngr_type='single')
        if ngrs.count() == 1:
            return ngrs.first().id
        unknown_ngr = ngrs_qs.filter(ngr_number__icontains='UNKNOWN_').first()
        if unknown_ngr:
            return unknown_ngr.id
        default_ngr = ngrs.filter(Ngr.tag_criteria('title_transfer')).first()
        if default_ngr:
            return default_ngr.id
        ngrs_except_shrink_ngr = ngrs.exclude(Ngr.tag_criteria('shrinkage')).first()
        if ngrs_except_shrink_ngr:
            return ngrs_except_shrink_ngr.id
        return get(ngrs_qs.first(), 'id')

    def is_managed_by_user(self, user):
        return self.id == user.company_id

    def is_managing_farm(self, farm_id):
        from core.farms.models import Farm
        return Farm.objects.filter(
            id=farm_id,
            broker_company_id=self.id,
            farmacceptancerequest__accepted=True,
            farmacceptancerequest__resolved=True,
        ).exists()

    def is_farm_creator(self, farm_id):
        return apps.get_model('farms', 'Farm').objects.filter(id=farm_id, created_by__company_id=self.id).exists()

    @property
    def directory_farm_ids(self):
        return self.farm_directory_set.values_list('farm_id', flat=True)

    @property
    def managed_farms(self):
        from core.farms.models import Farm
        return Farm.accepted_farms({'id__in': self.directory_farm_ids})

    def directory_companies_farms(self, include_self=False):
        from core.farms.models import Farm

        company_ids = self.directory_companies_ids

        if include_self:
            company_ids.append(self.id)
        company_ids.append(self.country.system_company_id)
        return Farm.accepted_farms({'company_id__in': list(set(company_ids))})

    @classmethod
    def register(cls, params):
        params.pop('office_id', None)
        company = cls.create_with_location(params)
        company.post_create()
        return company

    def post_create(self):
        if self.persisted:
            from core.farms.models import Farm
            from core.ngrs.models import Ngr

            self.main_farm = Farm.create_main_farm(company=self)
            if self.main_farm.persisted:
                self.created_by.company.add_farm_to_directory(self.main_farm.id)

            Ngr.create_unknown(self)

    @property
    def main_site(self):
        return self.farm_set.filter(name=self.name).first()

    @classmethod
    def get_or_create_by_name(cls, name, type_id, country, owner_company=None):
        abn = country.get_abn_service_class.get_abn_by_name(name)
        if not abn:
            return False
        company = Company.objects.filter(abn=abn).first()
        if not company:
            from core.companies.tests.factories import CompanyFactory
            company = CompanyFactory(
                entity_name=name,
                business_name=name,
                abn=abn,
                country_id=get(owner_company, 'country_id'),
                type_id=type_id,
                owner_company_id=get(owner_company, 'id') or AU_SYSTEM_COMPANY_ID
            )
            company.post_create()
            company.create_platform_features()

        return company

    @classmethod
    def get_or_create(cls, params):
        company_id = params.get('id', None)

        if not company_id:
            return cls.create_with_location(params)

        return Company.objects.get(id=company_id)

    def create_platform_features(self, features=None, **kwargs):  # pylint: disable=inconsistent-return-statements
        if self.persisted:
            return PlatformFeatures.persist(company_id=self.id, raise_exception=True, features=features, **kwargs)

    def __make_participator_of(self, attr, save=True):
        if self.is_registered and not getattr(self, attr, None):
            setattr(self, attr, True)
            if save:
                self.save()

    def make_transaction_participator(self, save=True):
        self.__make_participator_of('transaction_participation', save)

    def make_mobile_participator(self, save=True):
        self.__make_participator_of('mobile_participation', save)

    def update_settings_after_create(self):
        if self.plan_type != 'logistics_lite':
            self.make_transaction_participator(False)
            self.make_mobile_participator(False)
            if self.is_broker:
                self.can_represent = True
        self.save()

    @classmethod
    @transaction.atomic
    def create_with_location(cls, params, kwargs=None, pre_validate_callback=None):
        params.pop('questions', None)
        params.pop('assets', None)
        kwargs = kwargs or {}
        cls._update_abn_in_params(params)
        from core.company_sites.models import SiteManagementSettings
        brokerages_params = params.pop('brokerages', None)
        platform_features = params.pop('platform_features', None)
        plan_type = params.pop('plan_type', PREMIUM_PLAN)
        is_default_directory_company = params.pop('default_directory_company', None)

        logo = params.pop('logo', None)
        if params.get('type_id', None) in [2, 3]:
            params['mobile_flow'] = 'logistics'
        if params.get('type_id', None) in [GROWER_TYPE_ID]:
            params['have_field'] = True
        if params.get('type_id', None) in [BHC_TYPE_ID]:
            is_default_directory_company = True
        company = super().create_with_location(params, kwargs, '_set_entity_name_via_abn_lookup')
        company.create_platform_features(features=platform_features, plan_type=plan_type)
        SiteManagementSettings(company=company, minimum_tonnage=0.1).save()
        if company.persisted:
            company.update_settings_after_create()
            if logo:
                company._upload_logo(logo)
            if brokerages_params:
                company.create_brokerages(brokerages_params)
            company.toggle_default_directory_company(is_default_directory_company)
        return company

    @classmethod
    def _update_abn_in_params(cls, params):
        abn = params.get('abn', None)
        if not abn:
            params['abn'] = EMPTY_VALUE

    def expire_token(self):
        warnings.warn(f"****Expiring Tokens for company {self.id}***", RuntimeWarning)
        Token.objects.filter(user__company_id=self.id).delete()

    @classmethod
    @transaction.atomic
    def update_with_location(cls, instance_id, params, user=None, update_fields=None, schedule_job_for_update=False):  # pylint: disable=arguments-differ,too-many-locals,arguments-renamed,too-many-statements,too-many-branches
        if get(user, 'is_superuser') and 'abn' in params:
            cls._update_abn_in_params(params)
        company = Company.objects.get(id=instance_id)
        brokerages_params_exist = 'brokerages' in params
        brokerages_params = params.pop('brokerages', None)
        plan_type = params.pop('plan_type', None)
        type_id = params.pop('type_id', None)
        logo = params.pop('logo', None)
        is_default_directory_company = params.pop('default_directory_company', None)
        idle_period_to_logoff = params.pop('idle_period_to_logoff', None)
        password_expiry_period = params.pop('password_expiry_period', None)
        mobile_participation_updated = ('mobile_participation' in params and
                                        params['mobile_participation'] != company.mobile_participation)
        transaction_reference_number_configs = params.pop('transaction_reference_number_configs', None)
        extras = params.get('extras', None)
        have_field = params.get('have_field', None)
        restrict_slot_cancellation = params.get('restrict_slot_cancellation', None)
        if not restrict_slot_cancellation:
            params['hours_before_cancellation_stops'] = 0
        if have_field:
            farm = company.farm_set.filter(name=company.name).first()
            if farm:
                from core.farm_fields.models import FarmField
                FarmField.create_unknown_farm_field(farm)
        if extras and company.extras:
            extras = {**company.extras, **extras}
            params['extras'] = extras
        if 'transaction_code' in params:
            company.transaction_code = params.pop('transaction_code', None)
        else:
            company.transaction_code = company.transaction_code

        company.transaction_reference_number_configs = company.get_parsed_transaction_configs(
            transaction_reference_number_configs
        )

        if logo:
            company._remove_logo()
            company._upload_logo(logo)
        if brokerages_params_exist and user:
            company.update_brokerages(brokerages_params, user)

        if plan_type and user and user.is_staff:
            platform_feature = get(company, 'platformfeatures')
            if platform_feature and platform_feature.plan_type != plan_type:
                platform_feature.build_for_type(plan_type, type_id)
                company.expire_token()
        is_company_updated = False
        update_relations = False
        if type_id and type_id != company.type_id:
            update_relations = GROWER_TYPE_ID in [type_id, company.type_id]
            company.type_id = type_id
            is_company_updated = True

        if idle_period_to_logoff and user and user.is_staff:
            company.idle_period_to_logoff = timedelta(hours=float(idle_period_to_logoff))
            is_company_updated = True

        password_expiry_period = int(Decimal(password_expiry_period)) if (
            password_expiry_period is not None and password_expiry_period != ""
        ) else None
        if company.password_expiry_period != password_expiry_period and user and user.is_staff:
            company.password_expiry_period = password_expiry_period
            is_company_updated = True

        if is_company_updated:
            company.save()
            if update_relations and not schedule_job_for_update:
                company.update_all_ngr_relations()

        if mobile_participation_updated:
            from core.trucks.models import Truck
            truck_params = {'updated_at': timezone.now()}
            if user:
                truck_params['updated_by'] = user
            Truck.objects.filter(company_id=company.id, is_active=True).update(**truck_params)

        if company.errors:
            return company
        company = super().update_with_location(instance_id, params, update_fields)

        company.toggle_default_directory_company(is_default_directory_company)

        return company

    def update_all_ngr_relations(self, **kwargs):
        for ngr in self.ngr_set.filter(is_active=True):
            ngr.update_relations(**kwargs)

    def can_merge_unknown_ngr_with_valid_single_ngr(self, exclude_ngr_id=None):
        ngrs = self.ngr_set.filter(is_active=True)
        if exclude_ngr_id:
            ngrs = ngrs.exclude(id=exclude_ngr_id)
        if ngrs.count() == 1:
            unknown = ngrs.filter(ngr_number__icontains='unknown').first()
            return bool(unknown and not unknown.bank_account_set.filter(bank_id__isnull=False).exists())

        return False

    def get_parsed_transaction_configs(self, transaction_configs):
        payload = {}
        if transaction_configs:
            for config in transaction_configs:
                if TRANSACTION_TYPE_MAPPER.get(config.get('transaction_type')):
                    transaction_type = TRANSACTION_TYPE_MAPPER[config.get('transaction_type')]
                    transaction_data = {
                        "prefix": config.get('prefix', ''),
                        "length": int(config.get('length', DEFAULT_REFERENCE_NUMBER_LENGTH)),
                        "start_from": int(config.get('start_from', DEFAULT_REFERENCE_NUMBER_START))
                    }
                    payload[transaction_type] = transaction_data
        return payload

    def toggle_default_directory_company(self, is_default):
        if is_default is True:
            self.mark_default_directory_company()
        elif is_default is False:
            self.unmark_default_directory_company()

    def mark_default_directory_company(self):
        if not self.is_default_directory_company:
            AddedCompany.create({'object_company': self})

    def unmark_default_directory_company(self):
        if self.is_default_directory_company:
            self.default_directory_queryset.delete()

    @property
    def is_default_directory_company(self):
        return self.default_directory_queryset.exists()

    @property
    def is_transaction_code_exists(self):
        return bool(self.transaction_code)

    @property
    def is_contract_internal_reference_on(self):
        return self.is_transaction_code_exists and self.is_internal_reference_setting_on_for(NOTIFICATION_TYPE_CONTRACT)

    @property
    def default_directory_queryset(self):
        return AddedCompany.objects.filter(subject_company_id__isnull=True, object_company_id=self.id)

    def is_internal_reference_setting_on_for(self, transaction_type):
        return bool(get(self.transaction_reference_number_configs, transaction_type, None))

    def __generate_internal_reference_number(self, prefix, ref_length, transaction_type):
        transaction_name = self.__get_transaction_name_from_mapper(transaction_type)
        sequence_name = self.__generate_sequence_name(transaction_name, self.id)
        sequence_next_value = str(core.services.internal.postgres.PostgresQL.next_value(sequence_name))
        zeros = (ref_length - max(len(prefix + sequence_next_value), 0))*'0'
        return f'{prefix}{zeros}{sequence_next_value}'

    def generate_internal_reference_number_for(self, transaction_type):
        company_transaction_config = self.transaction_reference_number_configs[transaction_type]
        prefix = self.transaction_code + get(company_transaction_config, 'prefix', '')
        return self.__generate_internal_reference_number(
            prefix, get(company_transaction_config, 'length'), transaction_type
        )

    def _upload_logo(self, logo):
        if logo and logo.get('base64') and logo.get('name'):
            self.logo_path = core.services.external.aws.S3.upload_base64(
                logo['base64'],
                self.LOGO_PATH + str(self.id) + '/' + logo['name'],
                False,
                True,
            )
            self.save()

    def _remove_logo(self):
        if self.logo_path:
            core.services.external.aws.S3.remove(self.logo_path)
            self.logo_path = None
            self.save()

    def sync_ngr_from_portal(self, ngr_number):
        from core.jobs.models import Job
        params = {
            'company_id': self.id,
            'ngr_number': ngr_number
        }
        Job.schedule_job_for_task('sync_ngr_from_portal', params)

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):  # pylint: disable=arguments-differ
        if self.is_grower and not self.have_field:
            self.have_field = True
        self._set_entity_name_via_abn_lookup()
        transaction_dirty_fields = self.__is_transaction_seq_dirty()
        if self.transaction_code and not self.__is_transaction_code_unique():
            self.errors.update({'transaction_code': ['Transaction Code + prefix combination is duplicate.']})
            return

        super().save(force_insert, force_update, using, update_fields,)

        if self.id and self.transaction_reference_number_configs:   # pylint: disable=too-many-nested-blocks
            current_transaction_types = self.transaction_reference_number_configs.keys()
            if self.transaction_code:
                for transaction_type in current_transaction_types:
                    seq_transaction_name = self.__get_transaction_name_from_mapper(transaction_type)
                    seq_name = self.__generate_sequence_name(seq_transaction_name, self.id)
                    transaction_config = self.transaction_reference_number_configs[transaction_type]
                    start_value = get(transaction_config, 'start_from', 1)
                    if transaction_dirty_fields and transaction_type in transaction_dirty_fields.keys():
                        if transaction_type in current_transaction_types:
                            sequence_last_value = core.services.internal.postgres.PostgresQL.last_value(seq_name)
                            if sequence_last_value < start_value:
                                core.services.internal.postgres.PostgresQL.update_seq(seq_name, start_value)
                        else:
                            core.services.internal.postgres.PostgresQL.drop_seq(seq_name)
                    else:
                        transaction_last_value = self.__get_last_value_from_transaction(seq_transaction_name) or 0
                        start_value = start_value if transaction_last_value < start_value else transaction_last_value
                        core.services.internal.postgres.PostgresQL.create_seq(
                            seq_name, owned_by=self._meta.db_table + '.id', start=start_value
                        )
        elif self.id and transaction_dirty_fields:
            for transaction_type in transaction_dirty_fields:
                seq_transaction_name = self.__get_transaction_name_from_mapper(transaction_type)
                seq_name = self.__generate_sequence_name(seq_transaction_name, self.id)
                core.services.internal.postgres.PostgresQL.drop_seq(seq_name)

    def __is_transaction_code_unique(self):
        if (not isinstance(self.transaction_reference_number_configs, dict) or
                not self.transaction_reference_number_configs.keys()):
            return True
        for transaction_type in self.transaction_reference_number_configs:  # pylint: disable=not-an-iterable
            transaction_code = self.transaction_code.strip("0") + get(
                self, f"transaction_reference_number_configs.{transaction_type}.prefix", "").strip("0")
            queryset = Company.objects.filter(
                transaction_code__isnull=False,
                **{f"transaction_reference_number_configs__{transaction_type}__isnull": False}
            ).annotate(
                clean_transaction_code=Strip('transaction_code'),
                clean_prefix=Strip(f'transaction_reference_number_configs__{transaction_type}__prefix')
            ).annotate(
                 transaction_code_clubbed=Concat(
                     'clean_transaction_code', 'clean_prefix', output_field=models.CharField())
            ).annotate(
                clean_code=Replace('transaction_code_clubbed', Value('"'), Value(''))
            )
            if self.id:
                queryset = queryset.exclude(id=self.id)
            return not queryset.filter(clean_code=transaction_code).exists()

    def __get_last_value_from_transaction(self, transaction_type):
        klass = get_model_class_from_name(transaction_type)
        transaction_klass = klass.objects.filter(
            models.Q(seller__company_id=self.id, seller_internal_reference_number__isnull=False) |
            models.Q(buyer__company_id=self.id, buyer_internal_reference_number__isnull=False)
        )
        instance = transaction_klass.order_by('-created_at').first()
        if instance:
            if instance.seller.company_id == self.id:
                internal_reference = instance.seller_internal_reference_number
                transaction_code = instance.seller.company.transaction_code
                transaction_configs = instance.seller.company.transaction_reference_number_configs
            else:
                internal_reference = instance.buyer_internal_reference_number
                transaction_code = instance.buyer.company.transaction_code
                transaction_configs = instance.buyer.company.transaction_reference_number_configs
            if internal_reference:
                return self.__get_last_value_from_reference(
                    internal_reference, transaction_code, transaction_configs, transaction_type) + 1

    @staticmethod
    def __get_last_value_from_reference(reference_number, transaction_code,
                                        company_transaction_config, transaction_type):
        prefix = transaction_code + get(company_transaction_config[transaction_type], 'prefix', '')
        if reference_number.startswith(prefix):
            return int(reference_number.replace(prefix, '', 1))
        return 0

    def __is_transaction_seq_dirty(self):
        return get(self.get_dirty_fields(), 'transaction_reference_number_configs', None)

    def __generate_sequence_name(self, transaction_name, company_id):
        return '_'.join([transaction_name, str(company_id)]) + '_IRN_seq'

    def __get_transaction_name_from_mapper(self, transaction_type):
        return list(TRANSACTION_TYPE_MAPPER.keys())[list(TRANSACTION_TYPE_MAPPER.values()).index(transaction_type)]

    def delete(self, **kwargs):  # pylint: disable=arguments-differ
        if not self.employees_exist and self.id:
            super().delete(**kwargs)
            return True

        return False

    def update_existing_farm(self, params, current_user):
        params.pop('created_by', None)
        params.pop('created_by_id', None)
        params.pop('created_at', None)

        existing_farm_id = params.pop('existing_id', params.pop('id', None))
        current_user.company.add_farm_to_directory(farm_id=existing_farm_id)
        return apps.get_model('farms', 'Farm').update_with_location(
            existing_farm_id, params, user=current_user, update_fields=params.keys()
        )

    @transaction.atomic
    def upsert_farm(self, params, current_user):
        if ('existing_id' in params) or ('id' in params):
            return self.update_existing_farm(params, current_user)

        farm_model = apps.get_model('farms', 'Farm')
        params.pop('brokerages', None) # To be deleted

        if current_user.company.is_broker and self.is_registered:
            params['broker_company_id'] = current_user.company_id

        params.pop('office', None)

        farm = farm_model.create_with_location(
            {**params, 'company': self},
            kwargs={},
            pre_validate_callback=None,
        )

        if farm.persisted:
            farm.add_to_broker_company_dir()
            farm.raise_acceptance_request()
            farm.send_created_notifications(current_user)
            farm.send_unregistered_company_farm_created_mail_to_company_admin(current_user)

        return farm

    def add_company_to_directory(self, object_company_id):
        if object_company_id in [self.country.system_company_id, self.id]:
            return
        added_company = None
        data = {
            'subject_company_id': self.id,
            'object_company_id': object_company_id,
        }

        if not AddedCompany.objects.filter(**data).exists():
            AddedCompany.create(data)
            added_company = Company.objects.filter(id=object_company_id).first()
            if added_company and added_company.is_grower and self.is_broker:
                for farm in added_company.farms:
                    if not self.is_in_farm_directory(farm.id):
                        FarmDirectory.create({'farm_id': farm.id, 'company_id': self.id})

        return added_company

    @classmethod
    def add_company_to_super_directory(cls, object_company_id):
        _company = cls.moderators.get(pk=Country.get_system_company_id())
        added_company = None
        data = {
            'subject_company_id': _company.id,
            'object_company_id': object_company_id,
        }

        if not AddedCompany.objects.filter(**data).exists():
            AddedCompany(**data).save_only()
            added_company = Company.objects.get(id=object_company_id)

        return added_company

    @transaction.atomic
    def add_farm_to_directory(self, farm_id):  # pylint: disable=inconsistent-return-statements
        if self.is_broker and not self.is_in_farm_directory(farm_id):
            farm = apps.get_model('farms', 'Farm').objects.get(id=farm_id)
            if self.id != farm.company_id:
                AddedCompany.objects.get_or_create(subject_company_id=self.id, object_company_id=farm.company_id)
                for farm in farm.company.farms:
                    if farm.id != farm_id and not self.is_in_farm_directory(farm.id):
                        FarmDirectory.create({'farm_id': farm.id, 'company_id': self.id})

            return FarmDirectory.create({'farm_id': farm_id, 'company_id': self.id})

    @transaction.atomic
    def add_farms_to_directory(self, farm_ids):
        for farm_id in farm_ids:
            self.add_farm_to_directory(farm_id)

    def is_in_farm_directory(self, farm_id):
        return farm_id and self.farm_directory_set.filter(farm_id=farm_id).exists()

    @classmethod
    def add_companies_to_directory(cls, subject_company_id, object_company_ids):
        for object_company_id in object_company_ids:
            if subject_company_id != object_company_id:
                AddedCompany.objects.get_or_create(
                    subject_company_id=subject_company_id,
                    object_company_id=object_company_id
                )
                object_comp = Company.objects.filter(id=object_company_id).first()
                subject_comp = Company.objects.filter(id=subject_company_id).first()
                if object_comp and object_comp.is_grower and subject_comp and subject_comp.is_broker:
                    for farm in object_comp.farms:
                        if not subject_comp.is_in_farm_directory(farm.id):
                            FarmDirectory.create({'farm_id': farm.id, 'company_id': subject_comp.id})

    @classmethod
    def delete_companies_from_directory(cls, subject_company_id, object_company_ids):
        AddedCompany.objects.filter(
            subject_company_id=subject_company_id, object_company_id__in=object_company_ids
        ).delete()
        FarmDirectory.objects.filter(
            company_id=subject_company_id, farm__company_id__in=object_company_ids
        ).delete()

    def add_company_to_respective_business_type_group(self, company):
        company.groups.add(*self.owned_groups.filter(business_type_id=company.type_id))

    def _set_entity_name_via_abn_lookup(self): # pylint: disable=no-self-argument, unused-argument
        if not self.entity_name:
            company_name = Company._Company__existing_abn_info(self.abn)
            if company_name:
                self.entity_name = company_name
            else:
                country = Country.get_requesting_country()
                if country.get_abn_service_class:
                    details = country.get_abn_service_class.get_details(self.abn)
                    error = getattr(details, 'errors', None) or {'abn': getattr(details, 'alert', None)}
                    if error.get('abn'):
                        self.errors.update({'abn': error.get('abn')})
                    else:
                        self.entity_name = details.entity_name
                        self.business_name = self.business_name or details.business_name

    # this is just for tests -- remove this when we have ABN mock service in place
    @staticmethod
    def __existing_abn_info(abn):  # pylint: disable=unused-private-member
        _info = {
            '***********': 'The Trustee for The Best Family Trust',
            '***********': 'AGRICHAIN PTY LTD',
            '***********': 'BEST PTY LTD',
            '***********': 'A R H AGRICULTURE PTY LTD',
            '***********': 'A.C.E. Agriculture Pty Ltd',
            '***********': 'A.G AGRICULTURE',
            '***********': 'TRADING CORP PTY LTD',
            '***********': 'LOGISTICS AGRI CORP PTY LTD',
            '***********': 'CAPITAL AGRI CORP PTY LTD',
            '***********': 'COLSTON, DAVID',
            '***********': 'SANDEEP KUMAR',
            '***********': 'The Trustee for Alliance Broking',
            '***********': 'FRENCH, ANNETTE',
            '***********': 'The Trustee for TEASDALE AG FAMILY TRUST',
            '***********': 'JOHNSON, LEAHLANI',
            '***********': 'AGGIE T PTY LTD',
            '***********': 'HOUGHTON, MICHAEL',
            '***********': 'PACIFIC NATIONAL (NSW) PTY LTD',
            '***********': 'TEASDALE PTY LTD',
            '***********': 'A.J SANDERSON & P.J SANDERSON',
            '***********': 'The trustee for The Richlowe Trust',
            '***********': 'BEERENBERG PTY LTD',
            '***********': 'BRIGHT, RHONDA',
            '***********': 'A.S BASSAN & A SINGH',
            '***********': 'SMITH, MADELYNE ELIZABETH',
        }
        return _info.get(abn, None)

    def get_company_admins(self):
        return self.employee_set.filter(type_id=core.common.constants.COMPANY_ADMIN_TYPE_ID, is_active=True)

    def get_company_admins_with_email(self):
        return self.get_company_admins().filter(email__isnull=False)

    def get_first_company_admin_with_email(self):
        return self.get_company_admins_with_email().order_by('id').first()

    def get_first_company_admin(self):
        return self.get_company_admins().order_by('id').first()

    def guess_contact(self):
        return self.get_first_company_admin_with_email() or self.get_first_company_admin() or self.employee_set.filter(
            is_active=True).first()

    def mark_registered(self, user=None, set_password=True, should_send_mail=True):
        from core.profiles.models import Employee
        from core.jobs.models import Job
        if not self.is_registered:
            self.owner_company_id = None
            if isinstance(user, Employee) and get(user, 'id'):
                self.updated_by = user
            if self.is_broker:
                self.can_represent = True
            admins = self.get_company_admins()
            if set_password:
                admin = admins.first()
                if admin:
                    admin.set_password(raw_password=core.common.constants.DEFAULT_PASSWORD)
                    admin.save()
            latest_admin = admins.order_by('-created_at').first()
            if should_send_mail:
                if latest_admin and not self.is_grower:
                    Job.schedule_job_for_task(
                        'send_email_for_activation',
                        params={
                            'full_name': latest_admin.name,
                            'first_name': latest_admin.first_name,
                            'email': latest_admin.email,
                            'username': latest_admin.username
                        }
                    )
                if self.is_grower:
                    Job.schedule_job_for_task(
                        'send_email_for_unregistered_grower_activation',
                        params={
                            'first_name': latest_admin.first_name,
                            'email': latest_admin.email,
                            'username': latest_admin.username,
                        }
                    )
            self.save()

    def is_added_company(self, company_id):
        return self.added_company_set_subject.filter(object_company_id=company_id).exists()

    def unassigned_trailers(self):
        return self.trailer_set.filter(truck_id__isnull=True)

    def can_register(self, user):
        return not self.is_registered and user.company.is_system and self.has_company_admin()

    def has_company_admin(self):
        return self.get_company_admins().exists()

    def admin(self):
        return self.get_company_admins().first()

    def cannot_create_employee_reasons(self, user):
        reasons = []
        if not user:
            return [CANNOT_CREATE_EMPLOYEE_FOR_REGISTERED_COMPANY_REASON]
        if user.company.is_system:
            return reasons
        if self.transaction_participation:
            role_based_permission = ROLE_BASED_CREATE_UPDATE_PERMS[OFFICE_EMPLOYEE]
            if user.company_id != self.id:
                reasons.append(CANNOT_CREATE_EMPLOYEE_FOR_SUBSCRIBER_COMPANY_REASON)
            elif user.type_id not in role_based_permission:
                roles = [EMPLOYEE_ROLE_DISPLAY_NAME[role] for role in role_based_permission]
                reasons.append(CANNOT_CREATE_COMPANY_EMPLOYEE_BASED_ON_ROLE_REASON.format(roles=', '.join(roles)))
        return reasons

    def cannot_create_ngr_reasons(self, user):
        reasons = []
        if not user:
            return [CANNOT_CREATE_NGR_FOR_REGISTERED_COMPANY_REASON]
        if user.company.is_system:
            return reasons
        if self.transaction_participation:
            role_based_permission = ROLE_BASED_CREATE_UPDATE_PERMS[COMPANY_NGR]
            if user.company_id != self.id:
                reasons.append(CANNOT_CREATE_NGR_FOR_SUBSCRIBER_COMPANY_REASON)
            elif user.type_id not in role_based_permission:
                roles = [EMPLOYEE_ROLE_DISPLAY_NAME[role] for role in role_based_permission]
                reasons.append(CANNOT_CREATE_COMPANY_NGR_BASED_ON_ROLE_REASON.format(roles=', '.join(roles)))
        return reasons

    def save_related_models(self):
        from core.key_contacts.models import KeyContact
        object_list = []
        for relation_field in self._meta._relation_tree:
            kwargs = {relation_field.name: self.pk}
            object_list.extend(relation_field.model.objects.filter(**kwargs))

        for obj in object_list:
            if get(obj, 'entity') in [
                'farm', 'truck', 'trailer', 'employee', 'ngr', 'farmfield', 'location'
            ] and get(obj, 'id'):
                DeletedRecords.objects.get_or_create(
                    entity_type=obj.entity,
                    entity_id=obj.id,
                )
                if obj.entity == 'farm':
                    for storage in obj.storages:
                        DeletedRecords.objects.get_or_create(entity_type=storage.entity, entity_id=storage.id)
                    for field in obj.farmfield_set.all():
                        DeletedRecords.objects.get_or_create(entity_type=field.entity, entity_id=field.id)
                KeyContact.objects.filter(employee_id=obj.id).delete() if obj.entity == 'employee' else None

        DeletedRecords.objects.get_or_create(
            entity_type=self.entity,
            entity_id=self.id,
        )

    def merge_and_transfer(self, data, purge_company_id):  # pylint: disable=too-many-locals,too-many-statements
        from core.profiles.models import Employee
        from core.ngrs.models import Ngr
        from core.farms.models import Farm, Storage
        from core.farm_fields.models import FarmField
        from core.trucks.models import Truck
        from core.key_contacts.models import KeyContact
        from core.contracts.models import Party
        from core.invoices.models import InvoiceParty
        from core.notes.models import Note

        def process(klass, key):
            if data.get(key, None):
                transfers = get(data, f'{key}.transfer', [])
                if transfers:
                    queryset = klass.objects.filter(id__in=transfers)
                    if key == 'ngrs':
                        for entry in queryset:
                            entry.company_id = self.id
                            entry.save()  # calling this to set owner_company_ids
                    else:
                        queryset.update(company_id=self.id, updated_at=timezone.now())
                    if klass == Employee:
                        Token.objects.filter(user_id__in=transfers).delete()
                merges = get(data, f'{key}.merge', [])
                for obj in merges:
                    if key == 'farms':
                        Farm.delete_external_plant_codes_of_purged_farm(get(obj, 'from'))
                        Farm.merge_site_gates(get(obj, 'from'), get(obj, 'to'))
                        Farm.merge_unkown_fields(get(obj, 'from'), get(obj, 'to'))
                        Farm.transfer_archived_storages(get(obj, 'from'), get(obj, 'to'))
                    klass.merge_entities(key, get(obj, 'from'), get(obj, 'to'))

        process(Employee, 'employees')
        process(Ngr, 'ngrs')
        process(Farm, 'farms')
        process(FarmField, 'fields')
        process(Storage, 'storages')
        process(Truck, 'trucks')

        purge_company = Company.objects.filter(id=purge_company_id).first()
        if purge_company.type_id != self.type_id and GROWER_TYPE_ID in [purge_company.type_id, self.type_id]:
            ngr_ids = [*get(data, 'ngrs.transfer', [])]
            for merge_ngr in get(data, 'ngrs.merge', []):
                ngr_ids.append(get(merge_ngr, 'from'))
                ngr_ids.append(get(merge_ngr, 'to'))
            for ngr in Ngr.objects.filter(id__in=ngr_ids):
                ngr.update_relations(update_related_models=False)

        KeyContact.objects.filter(
            Q(company_id=purge_company_id) |
            Q(requester_company_id=purge_company_id) |
            Q(farm__company_id=purge_company_id)
        ).delete()
        Party.objects.filter(company_id=purge_company_id).update(company_id=self.id)
        InvoiceParty.objects.filter(company_id=purge_company_id).update(company_id=self.id)
        merge_from_observers = purge_company.employee_set.filter(type_id=OBSERVER_TYPE_ID)
        merge_to_observers = self.employee_set.filter(type_id=OBSERVER_TYPE_ID)
        if merge_from_observers.exists():
            if merge_to_observers.exists():
                merge_to_observer = merge_to_observers.first()
                for observer in merge_from_observers:
                    Employee.merge_entities('employees', observer.id, merge_to_observer.id)
            else:
                merge_from_observers.update(company_id=self.id, updated_at=timezone.now())
        Note.objects.filter(company_id=purge_company.id).delete()
        purged_company_directory_companies = AddedCompany.objects.filter(
            subject_company_id=purge_company_id).values_list('object_company_id', flat=True)
        purged_company_added_as_directory_in_companies = AddedCompany.objects.filter(
            object_company_id=purge_company_id).values_list('subject_company_id', flat=True)
        Company.add_companies_to_directory(self.id, purged_company_directory_companies)
        for company_id in purged_company_added_as_directory_in_companies:
            Company.add_companies_to_directory(company_id, [self.id])

        for relation_field in purge_company._meta._relation_tree:  # pylint: disable=protected-access
            if get(relation_field, 'name') not in ['approved_buyers', 'subject_company', 'object_company',
                                                   'company', 'external_plant_codes']:
                kwargs = {relation_field.name: purge_company.pk}
                relation_field.model.objects.filter(**kwargs).update(**{relation_field.name: self.id})

        self.replace_viewer_company_id(purge_company_id, self.id)

        MergedEntity.persist(purge_company_id, self.id, self.__class__.__name__)

    @staticmethod
    def replace_viewer_company_id(old_company_id, new_company_id):
        from core.contracts.models import Contract, TitleTransfer
        from core.freights.models import FreightContract, FreightOrder
        for klass in [Contract, FreightContract, FreightOrder, TitleTransfer]:
            klass.objects.filter(viewer_company_ids__contains=[old_company_id]).update(
                viewer_company_ids=models.Func(
                    models.F('viewer_company_ids'),
                    models.Value(old_company_id),
                    models.Value(new_company_id),
                    function='array_replace'
                )
            )

    def is_bulk_invoicing_applicable_for_tenure(self, start_date):  # accepts [partial]date YYYY-MM or YYYY-MM-DD
        if self.bulk_invoicing_from:
            return self.bulk_invoicing_from <= start_date.date()
        return False

    def get_warehouse_invoice_pending_loads( # pylint: disable=too-many-locals
            self, start_date, end_date, ngr_id, ngr_company_id=None, commodity_id=None):
        from core.loads.models import Load
        from core.ngrs.models import Ngr
        warehouse_charged_at = self.warehouse_charged_at
        ngr_company_id = ngr_company_id or Ngr.objects.get(id=ngr_id).company_id
        until_date = (self.warehouse_inload_charged_from
                      if warehouse_charged_at == 'outload_or_transfer_until_date' else None)
        return Load.get_pending_warehouse_invoice_items(
            self, ngr_company_id, start_date, end_date, ngr_id, commodity_id, until_date)

    def get_third_party_loads(self):
        from core.loads.models import Load
        return Load.objects.filter(self.third_party_stocks_criteria()).exclude(
            status__in=['void', 'rejected']).filter(
            ~Q(source='system_load') | Load.storage_and_ownership_update_or_empty_loads_criteria()
        ).order_by()

    @staticmethod
    def filter_invoices_on_commodity(invoices, commodity_id):
        from core.loads.models import Load
        exclude_invoice_ids = []
        for invoice in invoices:
            invoice_item = invoice.invoiceitem_set.filter().first()
            if invoice_item:
                loads = Load.objects.filter(id__in=get(invoice_item, 'load_ids', []), commodity_id=commodity_id)
                if not loads:
                    exclude_invoice_ids.append(invoice.id)
        existing_invoices = invoices.exclude(id__in=exclude_invoice_ids)
        return existing_invoices

    @staticmethod
    def get_tenure(start_date, end_date):
        return f"{start_date.strftime('%d/%m/%Y')} - {end_date.strftime('%d/%m/%Y')}"

    def generate_warehouse_draft_invoice(  # pylint: disable=too-many-locals, too-many-statements,too-many-branches
            self, ngr_id, start_date, end_date, should_create_storage_fees, delete_existing_draft, payment_due_date,
            commodity_id=None
    ):
        tenure = self.get_tenure(start_date, end_date)
        from core.loads.models import Load, StockSwap, RegradeReseason
        from core.invoices.models import Invoice
        from core.ngrs.models import Ngr
        ngr = Ngr.objects.get(id=ngr_id)
        if self.id in [*SYSTEM_COMPANY_IDS, *ngr.primary_owner_company_ids]:
            return
        outloads, inloads, throughput_outloads, throughput_inloads = (
            self.get_warehouse_invoice_pending_loads(start_date, end_date, ngr_id, ngr.company_id, commodity_id)
        )
        storages = Load.get_warehouse_invoices_storage_pending_for_company(
            self, ngr.company_id, start_date, end_date, ngr_id, commodity_id) if should_create_storage_fees else []
        transfers = Load.get_warehouse_invoices_transfers_pending_for_company(
            self, ngr.company_id, start_date, end_date, ngr_id, commodity_id)
        stock_swaps = StockSwap.get_warehouse_invoices_stock_swaps_pending_for_company(
            self, ngr.company_id, start_date, end_date, ngr_id, commodity_id
        )
        regrade_reseasons = RegradeReseason.get_warehouse_invoices_regrade_reseasons_pending_for_company(
            self, ngr.company_id, start_date, end_date, ngr_id, commodity_id
        )
        gst_rate = self.country.gst_rate
        if (len(throughput_outloads) == 0 and len(throughput_inloads) == 0 and len(outloads) == 0
                and len(inloads) == 0 and len(storages) == 0 and len(transfers) == 0 and
                len(stock_swaps) == 0 and len(regrade_reseasons) == 0 and not Invoice.get_warehouse_active_invoices(
                    tenure, self.id, ngr.company_id, ngr_id, True, commodity_id).exists()):
            if delete_existing_draft:
                existing_invoices = Invoice.objects.filter(
                    tenure=tenure, payer__ngr_id=ngr_id, payee__company_id=self.id,
                    type=WAREHOUSE, status='draft'
                )
                if commodity_id:
                    existing_invoices = self.filter_invoices_on_commodity(existing_invoices, commodity_id)
                if existing_invoices.exists():
                    existing_invoices.delete()
            return

        def get_total_tonnage(item, key, tonnage_type='tonnage'):
            return float(abs(
                sum(
                    (
                        _item.get(tonnage_type) or _item.get('shrunk_tonnage') or _item.get('tonnage') or 0
                    ) for _item in get(item, key)
                )
            ))

        def get_throughput_sub_total(items, items_key, tonnage_type='tonnage'):
            total = 0
            for item in items:
                applicable_shrinkage = (get_total_tonnage(
                    item, items_key
                ) - get_total_tonnage(item, items_key, tonnage_type)) / get_total_tonnage(item, items_key)
                unshrunk_harvest_tonnage = item.get('harvest_tonnage', 0)
                harvest_tonnage = unshrunk_harvest_tonnage * (1 - applicable_shrinkage)
                _tonnage = abs(get_total_tonnage(item, items_key, tonnage_type) - harvest_tonnage)
                if _tonnage:
                    total += (float(item.get('rate', 0)) * _tonnage)
                if unshrunk_harvest_tonnage:
                    total += (float(item.get('warehouse_fee', 0)) * abs(harvest_tonnage))
            return total

        def get_sub_total(items, items_key, tonnage_type='tonnage'):
            return sum(
                (float(item.get('rate', 0)) * get_total_tonnage(item, items_key, tonnage_type)) for item in items)

        def get_tonnage(items, items_key, tonnage_type='tonnage'):
            return round(sum(get_total_tonnage(item, items_key, tonnage_type) for item in items), 2)

        final_sub_total = get_sub_total(outloads, 'loads', 'shrunk_tonnage')
        final_sub_total += get_sub_total(inloads, 'loads')
        final_sub_total += get_throughput_sub_total(throughput_outloads, 'loads', 'shrunk_tonnage')
        final_sub_total += get_throughput_sub_total(throughput_inloads, 'loads')
        final_sub_total += get_sub_total(storages, 'storages', 'shrunk_tonnage')
        final_sub_total += get_sub_total(transfers, 'loads')
        final_sub_total += get_sub_total(stock_swaps, 'loads')
        final_sub_total += get_sub_total(regrade_reseasons, 'loads')
        total_tonnage = get_tonnage(outloads, 'loads', 'shrunk_tonnage')
        total_tonnage += get_tonnage(inloads, 'loads')
        total_tonnage = get_tonnage(throughput_outloads, 'loads', 'shrunk_tonnage')
        total_tonnage += get_tonnage(throughput_inloads, 'loads')
        total_tonnage += get_tonnage(storages, 'storages', 'shrunk_tonnage')
        total_tonnage += get_tonnage(transfers, 'loads')
        total_tonnage += get_tonnage(stock_swaps, 'loads')
        total_tonnage += get_tonnage(regrade_reseasons, 'loads')

        invoice_items = []

        def get_description(site, commodity, grade, season, prefix=None, suffix=None):
            description = f"{site} | {commodity}"
            if grade:
                description += f" | {grade}"
            if season:
                description += f" | {season}"
            description += f" | {ngr.ngr_number}"
            if prefix:
                description = prefix + ' | ' + description
            if suffix:
                description = description + ' | ' + suffix
            return description

        for storage in storages:
            for _storage in storage.get('storages', []):
                tonnage = float(_storage.get('shrunk_tonnage') or 0) or float(_storage.get('tonnage') or 0)
                sub_total = round(tonnage * storage.get('rate', 0), 2)
                gst = round(sub_total * gst_rate, 2)
                invoice_items.append({
                    'description': get_description(
                        storage.get('site_name'), storage.get('commodity_name'),
                        _storage.get('grade'), _storage.get('season'), 'Storage Fees'),
                    'gst': gst,
                    'item_id': _storage['id'],
                    'item_type': WAREHOUSE_STORAGE_FEES_MODEL,
                    'load_ids': list(_storage['load_ids']),
                    'rate': storage.get('rate'),
                    'shrunk_tonnage': round(tonnage, 2),
                    'sub_total': sub_total,
                    'time_period': storage.get('time_periods'),
                    'tonnage': round(float(_storage.get('tonnage') or 0) or
                                     float(_storage.get('shrunk_tonnage') or 0), 2),
                    'total': round(sub_total + gst, 2)
                })

        def add_throughput_loads_to_invoice_items(throughput_loads, model_type):
            if model_type == INLOAD:
                throughput_model = WAREHOUSE_THROUGHPUT_INLOAD_FEES_MODEL
                harvest_model = WAREHOUSE_INLOAD_FEES_MODEL
            else:
                throughput_model = WAREHOUSE_THROUGHPUT_OUTLOAD_FEES_MODEL
                harvest_model = WAREHOUSE_OUTLOAD_FEES_MODEL
            throughput_heading = f"Throughput {model_type.capitalize()}s"
            harvest_heading = f"{model_type.capitalize()}s (Part of Throughput)"
            for throughput_load in throughput_loads:
                site_name = throughput_load.get('site_name')
                harvest_tonnage = throughput_load.get('harvest_tonnage', 0)
                throughput_rate = throughput_load.get('rate')
                commodity_name = throughput_load.get('commodity_name')
                warehouse_fee = throughput_load.get('warehouse_fee')
                for thp_load in throughput_load.get('loads', []):
                    applicable_shrinkage = 0
                    if thp_load.get('shrunk_tonnage'):
                        applicable_shrinkage = (
                                                       thp_load.get('tonnage') - thp_load.get('shrunk_tonnage')
                                               )/thp_load.get('tonnage')
                    shrunk_harvest_tonnage = harvest_tonnage * (1-applicable_shrinkage)
                    thp_tonnage = abs(float(thp_load.get('tonnage') or 0) + harvest_tonnage)
                    thp_shrunk_tonnage = abs(
                        float(thp_load.get('shrunk_tonnage') or thp_load.get('tonnage') or 0) + shrunk_harvest_tonnage)
                    if model_type == INLOAD:
                        thp_sub_total = round(thp_tonnage * float(throughput_rate or 0), 2)
                    else:
                        thp_sub_total = round(thp_shrunk_tonnage * float(throughput_rate or 0), 2)
                    thp_gst = round(thp_sub_total * gst_rate, 2)
                    if harvest_tonnage:
                        throughput_heading = throughput_heading + ' (Partial)'
                    if thp_tonnage:
                        invoice_items.append({
                            'description': get_description(
                                site_name, commodity_name,
                                thp_load.get('grade'), thp_load.get('season'), throughput_heading),
                            'gst': thp_gst,
                            'item_id': thp_load.get('id'),
                            'item_type': throughput_model,
                            'load_ids': list(thp_load['load_ids'])
                            if thp_load.get('load_ids') else [],
                            'throughput_load_ids_with_tonnage_mapping':
                                thp_load.get('throughput_load_ids_with_tonnage_mapping'),
                            'rate': throughput_rate,
                            'sub_total': thp_sub_total,
                            'tonnage': round(thp_tonnage, 2),
                            'shrunk_tonnage': round(thp_shrunk_tonnage, 2),
                            'total': round(thp_sub_total + thp_gst, 2)
                        })
                    if harvest_tonnage:
                        thp_tonnage = abs(float(harvest_tonnage))
                        thp_shrunk_tonnage = abs(float(shrunk_harvest_tonnage))
                        if model_type == INLOAD:
                            thp_sub_total = round(thp_tonnage * float(warehouse_fee or 0), 2)
                        else:
                            thp_sub_total = round(thp_shrunk_tonnage * float(warehouse_fee or 0), 2)
                        thp_gst = round(thp_sub_total * gst_rate, 2)
                        invoice_items.append({
                            'description': get_description(
                                site_name, commodity_name,
                                thp_load.get('grade'), thp_load.get('season'), harvest_heading),
                            'gst': thp_gst,
                            'item_id': thp_load.get('id'),
                            'item_type': harvest_model,
                            'load_ids': list(thp_load['load_ids']) if thp_load.get('load_ids') else [],
                            'throughput_load_ids_with_tonnage_mapping':
                                thp_load.get('throughput_load_ids_with_tonnage_mapping'),
                            'rate': warehouse_fee,
                            'sub_total': thp_sub_total,
                            'tonnage': round(thp_tonnage, 2),
                            'shrunk_tonnage': round(thp_shrunk_tonnage, 2),
                            'total': round(thp_sub_total + thp_gst, 2)
                        })

        add_throughput_loads_to_invoice_items(throughput_inloads, INLOAD)
        add_throughput_loads_to_invoice_items(throughput_outloads, OUTLOAD)

        def __add_loads_transfers_to_items(_items, load_type, item_type, _shrunk_tonnage=0, use_self_description=False):
            for _item in _items:
                for _load in _item.get('loads', []):
                    if load_type in ['Outloads', 'Inloads']:
                        _shrunk_tonnage = abs(float(_load.get('shrunk_tonnage') or _load.get('tonnage') or 0))
                    if load_type == 'Outloads':
                        _tonnage = abs(float(_load.get('shrunk_tonnage') or _load.get('tonnage') or 0))
                    else:
                        _tonnage = abs(float(_load.get('tonnage') or 0))
                    _sub_total = round(_tonnage * float(_item.get('rate') or 0), 2)
                    _gst = round(_sub_total * gst_rate, 2)
                    _load_ids = _load.get('load_ids', [])
                    if not isinstance(_load_ids, (list, set)):
                        _load_ids = list(_load['load_ids'].values_list('id', flat=True)) if _load.get(
                            'load_ids') else []
                    _description = get(_item, 'description') if use_self_description else get_description(
                        _item.get('site_name'), _item.get('commodity_name'), _load.get('grade'),
                        _load.get('season'), load_type, _item.get('suffix')
                    )
                    _item = {
                        'description': _description,
                        'gst': _gst,
                        'item_id': _load.get('id'),
                        'item_type': item_type,
                        'load_ids': _load_ids,
                        'throughput_load_ids_with_tonnage_mapping':
                            _load.get('throughput_load_ids_with_tonnage_mapping'),
                        'rate': _item.get('rate'),
                        'sub_total': _sub_total,
                        'total': round(_sub_total + _gst, 2),
                        'tonnage': abs(float(_load.get('tonnage') or 0))
                    }
                    if _shrunk_tonnage is not None:
                        _item['shrunk_tonnage'] = round(_shrunk_tonnage, 2)

                    invoice_items.append(_item)

        __add_loads_transfers_to_items(inloads, 'Inloads', WAREHOUSE_INLOAD_FEES_MODEL)
        __add_loads_transfers_to_items(outloads, 'Outloads', WAREHOUSE_OUTLOAD_FEES_MODEL)
        __add_loads_transfers_to_items(transfers, 'Transfers', WAREHOUSE_TRANSFER_FEES_MODEL, None)
        __add_loads_transfers_to_items(stock_swaps, None, WAREHOUSE_STOCK_SWAP_MODEL, None, True)
        __add_loads_transfers_to_items(regrade_reseasons, None, WAREHOUSE_REGRADE_RESEASON_MODEL, None, True)

        final_sub_total = round(final_sub_total, 2)
        gst = round(final_sub_total * gst_rate, 2)
        payer_company_id = ngr.primary_owner_company_ids[0] if \
            ngr.primary_owner_company_ids and ngr.company_id not in ngr.primary_owner_company_ids else ngr.company_id
        communication = None
        if delete_existing_draft:
            existing_invoices = Invoice.objects.filter(
                tenure=tenure, payer__ngr_id=ngr_id, payee__company_id=self.id,
                type=WAREHOUSE, status='draft'
            )
            if commodity_id:
                existing_invoices = self.filter_invoices_on_commodity(existing_invoices, commodity_id)
            if existing_invoices.exists():
                existing_invoice = existing_invoices.first()
                existing_communication = get(existing_invoice, 'invoice_communication')
                if existing_communication:
                    communication = existing_communication.clone()
                existing_invoices.delete()
        if invoice_items:
            data = {
                'gst': gst,
                'identifier': generate_identifier('invoice', self.country),
                'invoice_items': invoice_items,
                'payee': {'company_id': self.id, 'ngr_id': self.get_tagged_ngr_or_logical('warehouse_invoice') or
                                                           self.get_tagged_ngr_or_logical('title_transfer')},
                'payer': {'company_id': payer_company_id, 'ngr_id': ngr.id},
                'payment_due_date': payment_due_date.strftime('%Y-%m-%d'),
                'sub_total': final_sub_total,
                'tenure': tenure,
                'tonnage': round(total_tonnage, 2),
                'total': round(final_sub_total + gst, 2),
                'type': WAREHOUSE
            }
            invoice = Invoice.persist({**data, 'status': 'draft'})
            if invoice.id:
                payer_contact_id = get(invoice.payer_contacts().first(), 'id')
                if payer_contact_id:
                    payer = invoice.payer
                    payer.contact_id = payer_contact_id
                    payer.save()
            if communication:
                communication.invoice = invoice
                communication.save()
            if not invoice.total:
                invoice.delete()
            return invoice

    def bulk_invoice_for_tenure(
            self, start_date, end_date, ngr_ids=None, commodity_ids=None, delete_existing_draft=True
    ):  # pylint: disable=too-many-locals,too-many-statements
        if not self.is_bulk_invoicing_applicable_for_tenure(start_date):
            return []
        year, month = start_date.year, start_date.month

        # get all loads for all company storages where ngr is not own
        loads = self.get_third_party_loads()

        ngr_ids = ngr_ids or set(loads.values_list('ngr_id', flat=True))

        separate_by_commodity_enabled = get(self, 'warehouse_invoice_commodity_separated')

        if separate_by_commodity_enabled:
            commodity_ids = commodity_ids or set(loads.values_list('commodity_id', flat=True))
        else:
            commodity_ids = []

        invoices = []
        today = timezone.now().date()
        if self.warehouse_invoice_frequency == WAREHOUSE_INVOICE_MONTHLY_FREQUENCY:
            should_create_storage_fees = today.month > int(month) if today.year == int(year) else today.year > int(year)
        else:
            should_create_storage_fees = timezone.now().replace(tzinfo=None) > end_date
        payment_due_date = end_date + timedelta(days=self.payment_due_days)

        for ngr_id in ngr_ids:
            if commodity_ids:
                for commodity_id in commodity_ids:
                    invoice = self.generate_warehouse_draft_invoice(
                        ngr_id, start_date, end_date, should_create_storage_fees, delete_existing_draft,
                        payment_due_date, commodity_id
                    )
                    if invoice:
                        invoices.append(invoice)
            else:
                invoice = self.generate_warehouse_draft_invoice(
                    ngr_id, start_date, end_date, should_create_storage_fees, delete_existing_draft, payment_due_date
                )
                if invoice:
                    invoices.append(invoice)
        return invoices

    def log_new_company(self):
        slack_message = NEW_COMPANY_SLACK_MESSAGE.format(
            company_name=self.name,
            company_type=get(self.type, 'name'),
            company_abn=self.abn,
            env=settings.ENV.upper(),
            created_by=get(self.created_by, 'name'),
            created_by_company=get(self.created_by, 'company.name'),
            registered=self.is_registered,
            subscriber=self.transaction_participation
        )
        Slack(Slack.ALERTS_CHANNEL).log(slack_message)

    def upsert_payment_run_epr_levy_preferences(self, data):
        payment_run_epr_levy_preference = get(data, 'payment_run_epr_levy_preference')
        payee_company_id = get(payment_run_epr_levy_preference, 'payee_company_id')
        preferences = get(payment_run_epr_levy_preference, 'settings')
        existing_pref = {}
        if self._payment_run_epr_levy_preference:
            existing_pref = deepcopy(self._payment_run_epr_levy_preference)
            if str(payee_company_id) in self._payment_run_epr_levy_preference:
                self._payment_run_epr_levy_preference[str(payee_company_id)].update(preferences)
            else:
                self._payment_run_epr_levy_preference.update({payee_company_id: preferences})
        else:
            self._payment_run_epr_levy_preference = {payee_company_id: preferences}
        if existing_pref != self._payment_run_epr_levy_preference:
            Company.all.filter(id=self.id).update(
                _payment_run_epr_levy_preference=self._payment_run_epr_levy_preference,
                updated_at=timezone.now()
            )

    @property
    def get_payment_run_epr_levy_preference(self):
        return self._payment_run_epr_levy_preference

    @classmethod
    def get_or_create_by_abn(cls, abn, user, company_type_id=core.common.constants.TRADER_TYPE_ID, **kwargs):
        company = Company.find_by_abn(abn)
        if not company:
            country = Country.get_requesting_country()
            if country.get_abn_service_class:
                details = country.get_abn_service_class.get_details(abn)
                if get(details, 'entity_name'):
                    company = Company.create_by_abn(abn, user, company_type_id, **kwargs)
                    if company:
                        company.business_name = get(details, 'business_name') or get(details, 'entity_name')
                        company.save()
        return company

    def upsert_acquisition_recipients(self, data):
        if data:
            self.acquisition_recipients = {'emails': get(data, 'acquisition_recipients')}
            self.save()

    def get_tagged_ngr_id(self, tag_name):
        from core.ngrs.models import Ngr
        return get(self.ngr_set.filter(Ngr.tag_criteria(tag_name)), '0.id')

    def get_tagged_ngr_or_logical(self, tag_name):
        return self.get_tagged_ngr_id(tag_name) or self.get_logical_ngr_id()

    def get_alert_by_name(self, name):
        return self.alert_set.filter(name__iexact=name, is_active=True).first()

    def get_summary_data_for_receivals(self, report_data, summary, receivals_count,
                                       current_season_inload_total, farm_ids=None):
        current_season = self.country.default_season.name
        all_total_receivals = [item.get('Total Receivals Today', '') for item in report_data]
        total_receivals_today = sum(float(value.split()[0]) for value in all_total_receivals if value)

        unit = all_total_receivals[0].split()[1] if all_total_receivals else ''
        summary['Total Inloaded Today'] = f'{round(total_receivals_today, 2)} {unit}'
        summary[f'Total Inloaded of Season ({current_season})'] = f'{round(current_season_inload_total, 2)} {unit}'
        summary['Trucks In'] = receivals_count
        summary['Stocks available'] = self.remaining_tonnages_for_each_commodity_across_sites(report_data, 'inload', farm_ids) # pylint: disable=line-too-long

    def get_spec_avg_report(self, farm_ids=None):  # pylint: disable=too-many-locals
        report_data = []
        summary = {}
        total_receivals_count = 0
        farms_current_season_inload_total = 0
        farms = self.farm_set.filter(id__in=farm_ids) if farm_ids else self.farm_set.filter()
        for farm in farms:
            receivals_count, current_season_outload_total = farm.get_spec_avg_report(report_data)
            total_receivals_count += receivals_count
            farms_current_season_inload_total += current_season_outload_total
        self.get_summary_data_for_receivals(
            report_data, summary, total_receivals_count, farms_current_season_inload_total, farm_ids
        )
        return report_data, summary

    def remaining_tonnages_for_each_commodity_across_sites(self, report_data, load_type, farm_ids=None):
        from core.commodities.models import Commodity
        field_name = 'Commodity Received' if load_type == 'inload' else 'Commodity Outloaded'
        commodity_names_in_report_data = [item.get(field_name, '') for item in report_data]
        available_tonnages_for_commodities = {}
        farms = self.farm_set.filter(id__in=farm_ids) if farm_ids else self.farm_set.filter()
        for commodity in Commodity.objects.filter(country_id=self.country_id):
            total_available_tonnage = 0
            for farm in farms:
                available_tonnage = farm.remaining_tonnage_for_commodity(commodity.id)
                total_available_tonnage += available_tonnage
            if total_available_tonnage != 0 or commodity.display_name in commodity_names_in_report_data:
                available_tonnages_for_commodities[commodity.display_name] = f'{round(total_available_tonnage, 2)} {commodity.unit}' # pylint: disable=line-too-long
        return available_tonnages_for_commodities

    def get_summary_data_for_outloads(self, report_data, summary, outload_count, current_season_outload_total,
                                      previous_season_outload_total, farm_ids=None):
        current_season = self.country.default_season.name
        previous_season = self.country.prev_season.name

        all_total_outloads = [item.get('Total Today', '') for item in report_data]
        total_outload_today = sum(float(value.split()[0]) for value in all_total_outloads if value)

        unit = all_total_outloads[0].split()[1] if all_total_outloads else ''
        summary['Total Outloaded Today'] = f'{round(total_outload_today, 2)} {unit}'
        summary[f'Total Outloaded of Season ({current_season})'] = f'{round(current_season_outload_total, 2)} {unit}'
        summary[f'Total Outloaded of Season ({previous_season})'] = f'{round(previous_season_outload_total, 2)} {unit}'
        summary['Trucks Out'] = outload_count
        summary['Stocks remaining'] = self.remaining_tonnages_for_each_commodity_across_sites(report_data, 'outload', farm_ids) # pylint: disable=line-too-long

    def get_outload_report(self, farm_ids=None):  # pylint: disable=too-many-locals
        report_data = []
        summary = {}
        total_outload_count = 0
        farms_current_season_outload_total = 0
        farms_previous_season_outload_total = 0
        farms = self.farm_set.filter(id__in=farm_ids) if farm_ids else self.farm_set.filter()
        for farm in farms:
            outload_count, current_season_outload_total, previous_season_outload_total = farm.get_outload_report(
                report_data
            )
            total_outload_count += outload_count
            farms_current_season_outload_total += current_season_outload_total
            farms_previous_season_outload_total += previous_season_outload_total

        self.get_summary_data_for_outloads(
            report_data, summary, total_outload_count, farms_current_season_outload_total,
            farms_previous_season_outload_total, farm_ids
        )
        return report_data, summary

    @classmethod
    def get_company_with_external_booking_enabled(cls, params):
        pickup_handler_id = get(params, 'freight_pickup.consignor.handler_id')
        delivery_handler_id = get(params, 'freight_delivery.consignee.handler_id')
        from core.farms.models import Farm
        if pickup_handler_id and get(params, 'outload_slot_id'):
            pickup_handler = Farm.objects.filter(id=pickup_handler_id).first()
            consignor_company = get(pickup_handler, 'company')
            if get(consignor_company, 'external_booking_connection'):
                return consignor_company
        if delivery_handler_id and get(params, 'inload_slot_id'):
            delivery_handler = Farm.objects.filter(id=delivery_handler_id).first()
            consignee_company = get(delivery_handler, 'company')
            if get(consignee_company, 'external_booking_connection'):
                return consignee_company

    def get_external_plant_code_for_farm(self, farm_id):
        return get(self.externalplantcodecompany_set.filter(farm_id=farm_id).first(), 'code')

    def validate_abn(self):
        if self.abn != EMPTY_VALUE:
            validators.NUM_REGEX(self.abn)
            abn_length = get(self, 'country.config.abn.length') or 11
            if len(self.abn) != abn_length:
                raise ValidationError(
                    {'abn': f'Ensure this value has at least {abn_length} characters (it has {len(self.abn)}).'}
                )

    def clean_fields(self, exclude=None):
        self._clean_xero_enabled_field()
        self.validate_abn()
        if not self._expire_token_in_days:
            self._expire_token_in_days = None
        if self.payment_run is None:
            self.payment_run = False
        super().clean_fields(exclude=exclude)

    def _clean_xero_enabled_field(self):
        if not self.xero_enabled and self.xero_client_id and self.xero_client_secret:
            self.xero_enabled = True
        if self.xero_enabled and (not self.xero_client_id or not self.xero_client_secret):
            self.xero_enabled = False

    @staticmethod
    def get_subscriber_csv_headers():   # pragma: no cover
        return [
            'Country',
            'Name',
            'ABN',
            'Type',
            'Mobile Participation',
            'Mobile Flow',
            'Enable Transfer to Approved Buyers',
            'Contract Number Mandatory in Transfers',
            'Order Booking',
            'Load by Load Transfer',
            'Purchase Contract Creation Restriction',
            'Sale Contract Creation Restriction',
            'Mobile Orders Within Delivery Range',
            'Overdraft Transfer Allowed',
            'Can Upload Contract CSV',
            'Contract Allocations',
            'Only Creator Can Amend Contract',
            'Only Creator Can Amend Order',
            'Warehouse Invoice Commodity Separated',
            'Mass Limit Emails',
            'Enable My Stocks on Mobile',
            'Automatically Add Inload',
            'Automatically Add Outload',
            'Enable Custom CSV',
            'Multi Slot Booking',
            'Rows Per Page',
            'Show Cash Prices to All',
            'Octopusbot Integration',
            'PowerBI Integration',
            'PowerBI Report ID',
            'Acquisition File Upload',
            'Fill Docket',
            'Xero Status',
            'Alerts',
            'Payment Run',
        ]

    def to_subscriber_setting_csv_row(self):   # pragma: no cover
        sm_settings = get(self, 'sitemanagementsettings')
        xero = self.xero_connection if self.xero_enabled else False
        return [
            self.country.name,
            self.name,
            self.abn,
            self.type.display_name,
            self.mobile_participation,
            self.mobile_flow,
            self.enable_transfer_to_approved_buyers,
            self.contract_number_mandatory_in_transfers,
            get(sm_settings, 'order_booking'),
            get(sm_settings, 'load_by_load_transfer'),
            self.purchase_contract_creation_restriction,
            self.sale_contract_creation_restriction,
            self.mobile_orders_within_delivery_range,
            self.overdraft_transfer_allowed,
            self.can_upload_contract_csv,
            self.to_contract_allocations_display_names,
            self.only_creator_can_amend_contract,
            self.only_creator_can_amend_order,
            self.warehouse_invoice_commodity_separated,
            self.mass_limit_emails,
            self.enable_my_stocks_on_mobile,
            self.automatically_add_inload,
            self.automatically_add_outload,
            self.enable_custom_csv,
            self.multi_slot_booking,
            self.rows_per_page,
            self.show_cash_prices_to_all,
            self.octopusbot_integration,
            self.power_bi,
            self.power_bi_report_id,
            self.acquisition_file_upload,
            self.fill_docket,
            ('Expired' if xero.is_expired else 'Connected') if xero else 'Not Connected',
            ' | '.join(self.alert_set.values_list('name', flat=True)),
            self.payment_run
        ]

    @property
    def to_contract_allocations_display_names(self):
        return ', '.join([to_display_attr(CONTRACT_ALLOCATION_TYPES, allocation_type) for allocation_type in
                          self.contract_allocations])  # pylint: disable=not-an-iterable

    @classmethod
    def get_or_create_by_abn_and_add_employee(cls, payee, user):  # pylint: disable=too-many-locals,too-many-branches
        from core.profiles.models import Employee
        portal_ngr_abn = get(payee, 'abn')
        latitude = get(payee, 'latitude')
        longitude = get(payee, 'longitude')
        primary_contact_id = payee.get('primary_contact_id', None)
        if portal_ngr_abn:
            company = Company.find_by_abn(portal_ngr_abn)
            contacts = payee.get('users', []) or []
            if not company or not company.is_subscriber:
                primary_contact = get(
                    [contact for contact in contacts if contact.get('user_id', None) == primary_contact_id],
                    '0'
                ) or get(contacts, '0') or {}
                address = ''
                for address_field in [
                    'address_line_1', 'address_line_2', 'address_line_3', 'suburb', 'state', 'postcode'
                ]:
                    val = primary_contact.get(address_field)
                    if val:
                        address += val + ' '
                primary_contact_phones = primary_contact.get('phones', []) or []
                company_mobile = get(
                    [phone for phone in primary_contact_phones if phone.get('phone_type') == 'Primary Landline'],
                    '0.phone_number'
                ) or get(primary_contact, 'phones.0.phone_number')
                if company and company.id:
                    location = company.address
                    if location.latitude != latitude or location.longitude != location.longitude:
                        location.name = address
                        location.address = address
                        location.latitude = latitude
                        location.longitude = longitude
                        location.save()

                    update = False
                    update_relations = False
                    if company.type_id != GROWER_TYPE_ID and not company.non_grower_with_ngr:
                        company.type_id = GROWER_TYPE_ID
                        update = True
                        update_relations = True
                    if company.mobile != company_mobile and company_mobile:
                        company.mobile = company_mobile or company.mobile
                        update = True
                    if update:
                        company.save()
                        if update_relations:
                            company.update_all_ngr_relations()

                else:
                    company = Company.get_or_create_by_abn(
                        portal_ngr_abn, user, GROWER_TYPE_ID,
                        latitude=latitude, longitude=longitude, address_name=address, mobile=company_mobile
                    )
                if company and company.id:
                    for user_data in contacts:
                        if user_data:
                            first_name = get(user_data, 'first_name')
                            last_name = get(user_data, 'family_name')
                            user_phones = user_data.get('phones', []) or []
                            mobile = get(
                                [phone for phone in user_phones if phone.get('phone_type') == 'Primary Mobile'],
                                '0.phone_number'
                            ) or get(user_data, 'phones.0.phone_number')
                            email = get(user_data, 'email')
                            employee_data = {
                                'first_name': first_name,
                                'last_name': last_name,
                                'mobile': mobile,
                                'email': email,
                                'username': Employee.generate_username(first_name, last_name, mobile, email),
                                'type_id': core.common.constants.COMPANY_ADMIN_TYPE_ID,
                            }
                            Employee.add_to_company(
                                company, employee_data, user, notify_user=False, update=True)

            return company

    def get_tenure_dates_for_warehouse_invoice(self, date_obj):  # returns tenure dates
        if self.warehouse_invoice_frequency == WAREHOUSE_INVOICE_WEEKLY_FREQUENCY:
            start_date = DateTimeUtil.get_start_of_week_date_for_date(
                date_obj, self.warehouse_invoice_start_of_week
            )
            end_date = DateTimeUtil.get_end_of_week_date_for_date(start_date)
        else:
            start_date, end_date = DateTimeUtil.get_month_range(date_obj.month, date_obj.year)
        return start_date, end_date

    def get_tenure_for_warehouse_invoice(self, date_obj):  # returns tenure string
        if date_obj:
            start_date, end_date = self.get_tenure_dates_for_warehouse_invoice(date_obj)
            return f"{start_date.strftime('%d/%m/%Y')} - {end_date.strftime('%d/%m/%Y')}"

    def _should_update_abn_status(self):
        return (
            not self.abn_status_last_updated_at or
            (timezone.now() - self.abn_status_last_updated_at) > timedelta(hours=24)
        )

    def fetch_updated_abn_status(self):
        if not self._should_update_abn_status():
            return self.abn_status

        if not self.country.get_abn_service_class:
            return self.abn_status

        abn_status = self.country.get_abn_service_class.get_status(self.abn)

        new_abn_status = (abn_status or '').lower()
        current_abn_status = (self.abn_status or '').lower()

        changed = new_abn_status != current_abn_status
        if changed:
            self.abn_status = new_abn_status
            self.abn_status_last_updated_at = timezone.now()
            self.save()
        else:
            # avoid history but need to record that ABN check last ran on this company
            Company.objects.filter(id=self.id).update(abn_status_last_updated_at=timezone.now())

        return self.abn_status

    @staticmethod
    def get_counter_party_entity_counts(pk, querysets, limit=None):
        counts = defaultdict(int)
        for qs in querysets:
            for item in qs:
                counts[item[pk]] += item['count']
        if limit:
            top_keys = list(dict(sorted(counts.items(), key=lambda item: item[1], reverse=True)).keys())[:limit]
            counts = {k: v for k, v in counts.items() if k in top_keys}
        return counts

    @property
    def counter_parties(self):
        from core.contracts.models import Party
        return Party.objects.exclude(company_id=self.id)

    @property
    def counter_sites(self):
        from core.contracts.models import ContractCommodityHandler
        return ContractCommodityHandler.objects.exclude(handler__company_id=self.id)

    def get_counter_party_queryset_with_counts(self):
        queryset = self.counter_parties.values('company_id').annotate(count=models.Count('id'))

        contract_sellers = queryset.filter(contract_seller__viewer_company_ids__contains=[self.id])
        contract_buyers = queryset.filter(contract_buyer__viewer_company_ids__contains=[self.id])
        order_sellers = queryset.filter(freight_order_seller__viewer_company_ids__contains=[self.id])
        order_buyers = queryset.filter(freight_order_buyer__viewer_company_ids__contains=[self.id])
        order_customer = queryset.filter(customer_freight_order__viewer_company_ids__contains=[self.id])
        movement_sellers = queryset.filter(freight_contract_seller__viewer_company_ids__contains=[self.id])
        movement_buyers = queryset.filter(freight_contract_buyer__viewer_company_ids__contains=[self.id])
        movement_customer = queryset.filter(freight_contract_party__viewer_company_ids__contains=[self.id])

        return [
            contract_sellers.values('company_id', 'count'),
            contract_buyers.values('company_id', 'count'),
            order_sellers.values('company_id', 'count'),
            order_buyers.values('company_id', 'count'),
            order_customer.values('company_id', 'count'),
            movement_sellers.values('company_id', 'count'),
            movement_buyers.values('company_id', 'count'),
            movement_customer.values('company_id', 'count')
        ]

    def get_counter_site_queryset_with_counts(self):
        queryset = self.counter_sites.values('handler_id').annotate(count=models.Count('id'))
        contract_handlers = queryset.filter(contract__viewer_company_ids__contains=[self.id])
        order_pickup_handlers = queryset.filter(
            freight_contract_consignor__pickup_freight_order__viewer_company_ids__contains=[self.id])
        order_delivery_handlers = queryset.filter(
            freight_contract_consignee__delivery_freight_order__viewer_company_ids__contains=[self.id])
        movement_pickup_handlers = queryset.filter(
            freight_contract_consignor__movement__viewer_company_ids__contains=[self.id])
        movement_delivery_handlers = queryset.filter(
            freight_contract_consignee__movement__viewer_company_ids__contains=[self.id])

        return [
            contract_handlers.values('handler_id', 'count'),
            order_pickup_handlers.values('handler_id', 'count'),
            order_delivery_handlers.values('handler_id', 'count'),
            movement_pickup_handlers.values('handler_id', 'count'),
            movement_delivery_handlers.values('handler_id', 'count')
        ]

    def get_site_booking_queryset_with_counts(self):
        from core.company_sites.models import FreightSlot
        site_booking_customers = (
            FreightSlot.objects.filter(movement__created_by__company_id=self.id)
            .exclude(site__company_id=self.id)
            .values('site__company_id')
            .annotate(
                count=models.Count('site__company_id'),
                company_id=models.F('site__company_id'),
            )
        )
        return [
            site_booking_customers.values('company_id', 'count')
        ]

    @staticmethod
    def _get_score_cases(counts):
        score_cases = []
        total_transactions = sum(counts.values())
        if total_transactions > 0:
            for pk, count in counts.items():
                score = (count / total_transactions) * 100
                score_cases.append(models.When(id=pk, then=Value(score)))
        return score_cases

    def assign_ranking(self, queryset, limit=None, ranking_module=None, only_top_results=False, order_by_fields=None):
        if limit:
            limit = int(limit)
        exclude_self = False
        if ranking_module == 'site_booking':
            querysets = self.get_site_booking_queryset_with_counts()
            exclude_self = True
        else:
            querysets = self.get_counter_party_queryset_with_counts()

        score_cases = self._get_score_cases(
            self.get_counter_party_entity_counts('company_id', querysets, limit)
        )

        queryset = queryset.annotate(
            score=models.Case(
                *score_cases,
                default=Value(0.0),
                output_field=models.FloatField()
            ),
            rank=models.Case(
                *([models.When(id=self.id, then=models.F('score') + Value(100.0))] if not exclude_self else []),
                models.When(score__gt=0, then=models.F('score')),
                default=Value(-100.0),
                output_field=models.FloatField()
            )
        )
        order_by = ['-rank', 'business_name']
        if isinstance(order_by_fields, list):
            order_by = [*order_by_fields, *order_by]
        queryset = queryset.order_by(*order_by)
        if ranking_module == 'site_booking' and only_top_results:
            queryset = queryset.exclude(score__lte=0)
        if not exclude_self and limit:
            limit = limit + 1
        return queryset[:limit] if limit else queryset

    def assign_sites_ranking(self, queryset, limit=None):
        self_limit = None
        if limit:
            limit = int(limit)
            self_limit = self.farm_set.filter(is_active=True).count()

        score_cases = self._get_score_cases(
            self.get_counter_party_entity_counts('handler_id', self.get_counter_site_queryset_with_counts(), limit)
        )

        queryset = queryset.annotate(
            score=models.Case(
                *score_cases,
                default=Value(0.0),
                output_field=models.FloatField()
            ),
            rank=models.Case(
                models.When(company_id=self.id, then=models.F('score') + Value(100.0)),
                models.When(score__gt=0, then=models.F('score')),
                default=Value(-100.0),
                output_field=models.FloatField()
            )
        ).order_by('-rank', 'name', 'company__business_name')

        return queryset[:limit + self_limit] if limit else queryset

    @property
    def has_ngr_credentials(self):
        from core.ngrs.models import NgrPortalCredential
        return NgrPortalCredential.is_ngr_portal_linked(self.id)

    def get_most_used_commodity_ids(self):
        from core.loads.models import Load
        grade_ids = Load.get_grade_ids_order_by_usage(self.id)
        grades = {
            grade.id: grade.commodity_id for grade in Grade.objects.filter(id__in=grade_ids).only('id', 'commodity_id')
        }
        traversed_commodity_ids = []
        for grade_id in grade_ids:
            commodity_id = grades[grade_id]
            if commodity_id not in traversed_commodity_ids:
                traversed_commodity_ids.append(commodity_id)

        return {
            'commodity_ids': traversed_commodity_ids,
            'grade_ids': grade_ids,
            'variety_ids': Load.get_variety_ids_order_by_usage(self.id)
        }

    def get_most_used_specs_by_commodity(self, commodity_id=None):  # pylint: disable=too-many-locals
        from core.commodities.models import Commodity
        result = {}
        queryset = self.get_created_loads_queryset()
        if commodity_id:
            queryset = queryset.filter(commodity_id=commodity_id)
        for load in queryset.only('specs', 'commodity_id'):
            _commodity_id = str(load.commodity_id)
            if _commodity_id not in result:
                result[_commodity_id] = Counter()
            for key, value in load.specs.items():
                if value:
                    result[_commodity_id][key] += 1

        for _commodity_id, key_counter in result.items():
            commodity = Commodity.objects.get(id=_commodity_id)
            ordered_specs = []
            counter = 1
            ordered_spec_codes = [key for key, _ in key_counter.most_common()]
            for code in ordered_spec_codes:
                spec = next((spec for spec in commodity.specs if spec['code'].lower() == code.lower()), None)
                spec['order'] = counter
                ordered_specs.append(spec)
                counter += 1
            for spec in commodity.specs:
                if spec['code'].lower() not in ordered_spec_codes:
                    spec['order'] = counter
                    ordered_specs.append(spec)
                    counter += 1
            result[str(_commodity_id)] = ordered_specs
        return result

    def get_created_loads_queryset(self):
        from core.loads.models import Load
        return Load.objects.filter(
            created_at__date__gte=timezone.now() - timedelta(days=180),
            created_by__company=self,
            commodity__isnull=False,
        ).exclude(specs={}).exclude(status=VOID_STATUS)


class AddedCompany(BaseModel):
    class Meta:
        db_table = 'added_companies'
        unique_together = (('subject_company', 'object_company'),)

    subject_company = models.ForeignKey(
        Company,
        related_name='added_company_set_subject',
        on_delete=models.CASCADE,
        null=True,
        blank=True
    )
    object_company = models.ForeignKey(
        Company,
        related_name='added_company_set_object',
        on_delete=models.CASCADE,
    )


class FarmDirectory(BaseModel):
    class Meta:
        db_table = 'farm_directory'
        unique_together = (('company', 'farm'),)

    company = models.ForeignKey(Company, related_name='farm_directory_set', on_delete=models.CASCADE)
    farm = models.ForeignKey('farms.Farm', on_delete=models.CASCADE)


class SSOSetting(models.Model):
    class Meta:
        db_table = 'company_sso_settings'

    sso_url = models.TextField(null=True, blank=True)
    agrichain_auth_enabled = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    extras = models.JSONField()
    company = models.OneToOneField(Company, on_delete=models.CASCADE, related_name='sso_setting')

    def login_uri(self):
        return f'/companies/{self.company_id}/sso/azure/login'


class AbstractConnection(RawModel):
    EXPIRATION_UNITS = 'days'

    company = models.OneToOneField(Company, on_delete=models.CASCADE)
    access_token = models.TextField(null=True, blank=True)
    refresh_token = models.TextField(null=True, blank=True)
    expires_in = models.DateTimeField()

    class Meta:
        abstract = True

    def _reset_expires_in(self, expires_in='30'):
        delta = timedelta(
            days=int(expires_in)) if self.EXPIRATION_UNITS == 'days' else timedelta(seconds=int(expires_in))
        self.expires_in = timezone.now() + delta

    def reset(self, access_token, refresh_token=None, expires_in='30'):
        self.access_token = access_token
        self.refresh_token = refresh_token
        self._reset_expires_in(expires_in)
        self.save()

    @property
    def is_expired(self):
        return timezone.now() >= self.expires_in

    def refresh(self):
        return self.get_or_refresh_token() if not self.is_expired else self.handle_expired_token()

    def get_or_refresh_token(self):
        raise NotImplementedError

    def handle_expired_token(self):
        raise NotImplementedError

    @classmethod
    def create_connection(cls, company, data=None):
        raise NotImplementedError

    def exchange_code_with_service(self, data=None):
        raise NotImplementedError

    @classmethod
    def exchange_code(cls, company, data):
        connection = cls.objects.filter(company=company).first()
        if not connection:
            connection = cls.create_connection(company, data)

        result = connection.exchange_code_with_service(data)
        refresh_token = get(result, 'refresh_token')
        access_token = get(result, 'access_token')
        expires_in = get(result, 'expires_in', None)
        if refresh_token or access_token:
            connection.reset(access_token, refresh_token, expires_in)
        return result

    def get_service_data(self, service_method):
        """Generalized method to fetch data from a service using the access token."""
        token = self.refresh()
        service = self.get_service(token)
        return getattr(service, service_method)()

    def get_service(self, token):
        raise NotImplementedError


class ImpexDocsConnection(AbstractConnection):

    class Meta:
        db_table = 'company_impex_docs_connections'

    EXPIRATION_UNITS = 'seconds'

    client_id = models.CharField(max_length=255, null=True, blank=True)
    client_secret = models.CharField(max_length=255, null=True, blank=True)
    user_id = models.CharField(max_length=255, null=True, blank=True)
    user_email = models.CharField(max_length=255, null=True, blank=True)

    FILLABLES = [
        'company_id',
        'client_id',
        'client_secret',
        'user_id',
        'user_email',
        'token',
        'expires_in',
    ]

    def set_impex_docs_user(self):
        user = self.get_user()
        if user:
            self.user_id = get(user, 'user_id')
            self.user_email = get(user, 'email')
            self.save()

    @classmethod
    def create_connection(cls, company, data=None):
        return cls(
            company=company,
            client_id=get(data, 'client_id', None),
            client_secret=get(data, 'client_secret', None)
        )

    def exchange_code_with_service(self, data=None):
        service = self.company.get_impex_docs_service()
        return service.get_token()

    def get_token(self):
        service = self.company.get_impex_docs_service()
        result = service.get_token()
        token = get(result, 'access_token')
        expires_in = get(result, 'expires_in')
        if token:
            self.reset(token, None, expires_in)
        return result

    def get_or_refresh_token(self):
        return self.access_token

    def handle_expired_token(self):
        return get(self.get_token(), 'access_token')

    def get_user(self):
        return self.get_service_data('get_user')

    def get_service(self, token):
        return self.company.get_impex_docs_service(token=token)


class ImpexDocsProductMapping(BaseModel):
    class Meta:
        db_table = 'impex_docs_product_mappings'

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='impex_product_mappings')
    commodity = models.ForeignKey('commodities.Commodity', on_delete=models.SET_NULL, null=True, blank=True)
    grade = models.ForeignKey('commodities.Grade', on_delete=models.SET_NULL, null=True, blank=True)
    variety = models.ForeignKey('commodities.Variety', on_delete=models.DO_NOTHING, null=True, blank=True)
    product_code = models.CharField(max_length=300, null=True, blank=True)

    FILLABLES = [
        'company_id',
        'id',
        'grade_id',
        'commodity_id',
        'variety_id'
        'product_code'
    ]

    @classmethod
    def upsert(cls, data):
        mapping_id = data.pop('id', None)
        if mapping_id:
            mapping = ImpexDocsProductMapping.objects.filter(id=mapping_id).first()
            if mapping:
                for field in ['commodity_id', 'grade_id', 'variety_id', 'product_code']:
                    setattr(mapping, field, data.get(field, None) or None)
        else:
            mapping = ImpexDocsProductMapping(**data)
        mapping.full_clean()
        mapping.save()
        return mapping

    def is_duplicate(self):
        return self._get_duplicate_qs().exists()

    def _get_duplicate_qs(self):
        queryset = ImpexDocsProductMapping.objects.filter(company_id=self.company_id, commodity_id=self.commodity_id)

        queryset = queryset.filter(grade_id=self.grade_id) if self.grade_id else queryset.filter(grade_id__isnull=True)

        queryset = queryset.filter(variety_id=self.variety_id) if self.variety_id else queryset.filter(variety_id__isnull=True) # pylint: disable=line-too-long

        if self.id:
            queryset = queryset.exclude(id=self.id)

        return queryset

    def is_existing_product_code(self):
        queryset = ImpexDocsProductMapping.objects.exclude(commodity_id=self.commodity_id).filter(
            company_id=self.company_id, product_code=self.product_code)
        if self.id:
            queryset = queryset.exclude(id=self.id)

        return queryset.exists()

    def clean(self):
        if self.is_duplicate():
            raise ValidationError({'__all__': 'This combination already exists'})
        if self.is_existing_product_code():
            raise ValidationError({'product_code': 'Product code already exists for another commodity'})

class XeroConnection(AbstractConnection):
    class Meta:
        db_table = 'company_xero_connections'

    EXPIRATION_UNITS = 'days' # token expires in 30 days from issue

    @classmethod
    def create_connection(cls, company, data=None):
        return cls(company=company)

    def exchange_code_with_service(self, data=None):
        service = self.company.get_xero_service()
        return service.exchange_code(data.get('code'), data.get('redirect_uri'))

    def get_service(self, token):
        return self.company.get_xero_service(token=token)

    def get_tenant_id(self):
        return self.get_service_data('get_tenant_id')

    def get_or_refresh_token(self):
        service = self.company.get_xero_service()
        result = service.refresh_token(self.refresh_token)
        refresh_token = get(result, 'refresh_token')
        if refresh_token:
            self.reset(get(result, 'access_token'), refresh_token)
        return self.access_token

    def handle_expired_token(self):
        raise Exception('Refresh Token expired. Relink the account.')

    def get_accounts_and_items(self):
        token = self.refresh()
        service = self.company.get_xero_service(token=token)
        accounts = service.get_account_codes()
        items = service.get_item_codes()
        return {'items': items, 'accounts': accounts}

    def get_tracking_categories(self):
        token = self.refresh()
        service = self.company.get_xero_service(token=token)
        return service.get_tracking_categories_data()


class XeroTrackingCategory(BaseModel):
    class Meta:
        db_table = 'company_xero_tracking_categories'

    FILLABLES = [
        'xero_tracking_category_id',
        'name',
        'xero_option_id',
        'option_name',
        'company_id',
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='xero_tracking_categories')
    xero_tracking_category_id = models.CharField(max_length=300)
    name = models.CharField(max_length=300)
    xero_option_id = models.CharField(max_length=300)
    option_name = models.CharField(max_length=300)

    @classmethod
    def upsert(cls, payload, company):
        created_or_updated_tracking_category_ids = []
        for data in payload:
            category = cls.objects.filter(
                xero_tracking_category_id=data['xero_tracking_category_id'],
                xero_option_id=data['xero_option_id'],
                company_id=company.id
            ).first()
            if not category:
                category = cls(**data, company=company)
                category.full_clean_errors()
                category.save()
            if category.id:
                if data.get('option_name', None):
                    category.option_name = data['option_name']
                    category.save()
                created_or_updated_tracking_category_ids.append(category.id)
        return created_or_updated_tracking_category_ids


class XeroMapping(BaseModel):
    class Meta:
        db_table = 'company_xero_mappings'

    TRANSACTION_TYPES = (
        ('purchase_contract', 'Purchase Contract'),
        ('sale_contract', 'Sale Contract'),
        ('deductions', 'Deductions'),
        ('warehouse', 'Warehouse'),
        ('freight_invoice_payable', 'Freight Invoices Payable'),
        ('freight_invoice_receivable', 'Freight Invoices Receivable'),
    )

    ITEM_TYPE_MOVEMENTS = 'movements'
    ITEM_TYPE_TT = 'title_transfers'
    ITEM_TYPE_CARRY = 'carry'
    ITEM_TYPE_CUSTOM = 'custom'
    ITEM_TYPE_EPR = 'epr'
    ITEM_TYPE_LEVY = 'levy'
    ITEM_TYPE_CHEMICAL_APPLICATION = 'chemical_applications'
    ITEM_TYPE_BLENDING_FEES = 'blending_fees'

    # warehouse invoice item types
    ITEM_TYPE_INLOAD_FEES = 'inload_fees'
    ITEM_TYPE_OUTLOAD_FEES = 'outload_fees'
    ITEM_TYPE_STORAGE_FEES = 'storage_fees'
    ITEM_TYPE_TRANSFER_FEES = 'transfer_fees'
    ITEM_TYPE_STOCK_SWAP = 'stock_swap'
    ITEM_TYPE_REGRADE_RESEASON = 'regrade_reseason'

    ITEM_TYPES = (
        (ITEM_TYPE_TT, 'Title Transfers'),
        (ITEM_TYPE_MOVEMENTS, 'Freight Movements'),
        (ITEM_TYPE_CARRY, 'Carry Charges'),
        (ITEM_TYPE_CUSTOM, 'Custom Items'),
        (ITEM_TYPE_EPR, 'EPR'),
        (ITEM_TYPE_LEVY, 'Grain Levy'),
        (ITEM_TYPE_INLOAD_FEES, 'Inload Fees'),
        (ITEM_TYPE_OUTLOAD_FEES, 'Outload Fees'),
        (ITEM_TYPE_STORAGE_FEES, 'Storage Fees'),
        (ITEM_TYPE_TRANSFER_FEES, 'Transfer Fees'),
        (ITEM_TYPE_STOCK_SWAP, 'Stock Swap'),
        (ITEM_TYPE_REGRADE_RESEASON, 'Regrade Reseason'),
        (ITEM_TYPE_CHEMICAL_APPLICATION, 'Applications'),
        (ITEM_TYPE_BLENDING_FEES, 'Blending Fees'),
    )

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='xero_mappings')
    commodity = models.ForeignKey('commodities.Commodity', on_delete=models.SET_NULL, null=True, blank=True)
    grade = models.ForeignKey('commodities.Grade', on_delete=models.SET_NULL, null=True, blank=True)
    season = models.CharField(max_length=10, null=True, blank=True)
    item_type = models.CharField(null=True, blank=True, choices=ITEM_TYPES, max_length=250)
    xero_account = models.TextField()
    transaction_type = models.CharField(choices=TRANSACTION_TYPES, max_length=100)
    tracking_categories = models.ManyToManyField(XeroTrackingCategory, related_name='mapping_tracking_categories')
    xero_item_code = models.CharField(max_length=300, null=True, blank=True)
    site = models.ForeignKey('farms.Farm', on_delete=models.SET_NULL, null=True, blank=True)
    ngr = models.ForeignKey('ngrs.Ngr', on_delete=models.SET_NULL, null=True, blank=True)
    truck = models.ForeignKey('trucks.truck', on_delete=models.SET_NULL, null=True, blank=True)

    FILLABLES = [
        'company_id',
        'id',
        'xero_account',
        'transaction_type',
        'item_type',
        'season',
        'grade_id',
        'commodity_id',
        'xero_mappings',
        'tracking_categories',
        'xero_item_code',
        'site_id',
        'ngr_id',
        'truck_id'
    ]

    def is_duplicate(self):
        return self._get_duplicate_qs().exists()

    def _get_duplicate_qs(self):
        queryset = XeroMapping.objects.filter(
            commodity_id=self.commodity_id, grade_id=self.grade_id, season=self.season, xero_account=self.xero_account,
            company_id=self.company_id, item_type=self.item_type, transaction_type=self.transaction_type,
            site_id=self.site_id, ngr_id=self.ngr_id, truck_id=self.truck_id
        )
        if self.id:
            queryset = queryset.exclude(id=self.id)
        return queryset

    @classmethod
    def upsert(cls, data):
        mapping_id = data.pop('id', None)
        tracking_category_ids = data.pop('tracking_category_ids', [])
        if mapping_id:
            mapping = XeroMapping.objects.filter(id=mapping_id).first()
            if mapping:
                for field in ['commodity_id', 'grade_id', 'season', 'site_id', 'item_type', 'xero_account',
                              'xero_item_code', 'transaction_type', 'ngr_id', 'truck_id']:
                    setattr(mapping, field, data.get(field, None) or None)
        else:
            mapping = XeroMapping(**data)
        mapping.full_clean()
        mapping.save()
        if tracking_category_ids and not mapping.errors:
            mapping.tracking_categories.set(XeroTrackingCategory.objects.filter(id__in=tracking_category_ids))
        return mapping

    def __set_grade_by_commodity(self):
        new_value = self.grade_id  # pylint: disable=access-member-before-definition
        if self.commodity_id and new_value:
            if not Grade.objects.filter(id=new_value, commodity_id=self.commodity_id).exists():
                new_value = None
        else:
            new_value = None

        self.grade_id = new_value

    def clean(self):
        self.__set_grade_by_commodity()
        if not self.commodity_id and not self.grade_id and not self.season and not self.item_type:
            raise ValidationError({'__all__': 'One of the commodity, grade, season and item_type needs to be present'})
        if self.is_duplicate():
            raise ValidationError({'__all__': 'This combination already exists'})

    @staticmethod
    def get_item_type(
            item_type_model, is_chemical_application_item=False, is_blending_fees=False, is_blended_load=False
    ):  # pylint: disable=too-many-return-statements, too-many-branches
        if is_blending_fees:
            return XeroMapping.ITEM_TYPE_BLENDING_FEES
        if is_chemical_application_item:
            return XeroMapping.ITEM_TYPE_CHEMICAL_APPLICATION
        if item_type_model == FREIGHT_MOVEMENT_MODEL or is_blended_load:
            return XeroMapping.ITEM_TYPE_MOVEMENTS
        if item_type_model in [TITLE_TRANSFER_MODEL, LOAD_MODEL]:
            return XeroMapping.ITEM_TYPE_TT
        if item_type_model == CARRY_ITEM_MODEL:
            return XeroMapping.ITEM_TYPE_CARRY
        if item_type_model == EPR_ITEM_DB_MODEL:
            return XeroMapping.ITEM_TYPE_EPR
        if item_type_model == CUSTOM_ITEM_MODEL:
            return XeroMapping.ITEM_TYPE_CUSTOM
        if item_type_model == GRAIN_LEVY_ITEM_MODEL:
            return XeroMapping.ITEM_TYPE_LEVY
        if item_type_model == WAREHOUSE_INLOAD_FEES_MODEL:
            return XeroMapping.ITEM_TYPE_INLOAD_FEES
        if item_type_model == WAREHOUSE_OUTLOAD_FEES_MODEL:
            return XeroMapping.ITEM_TYPE_OUTLOAD_FEES
        if item_type_model == WAREHOUSE_STORAGE_FEES_MODEL:
            return XeroMapping.ITEM_TYPE_STORAGE_FEES
        if item_type_model == WAREHOUSE_TRANSFER_FEES_MODEL:
            return XeroMapping.ITEM_TYPE_TRANSFER_FEES
        if item_type_model == WAREHOUSE_STOCK_SWAP_MODEL:
            return XeroMapping.ITEM_TYPE_STOCK_SWAP
        if item_type_model == WAREHOUSE_REGRADE_RESEASON_MODEL:
            return XeroMapping.ITEM_TYPE_REGRADE_RESEASON
        return None

    @staticmethod
    def get_best_match(mappings, item_type, commodity_id, grade_id, season, site_id, ngr_id, truck_id=None, transaction_type=None):  # pylint: disable=too-many-locals,line-too-long
        if not mappings:
            return None

        if item_type in [XeroMapping.ITEM_TYPE_LEVY, XeroMapping.ITEM_TYPE_EPR]:
            transaction_type = 'deductions'

        if transaction_type:
            mappings = mappings.filter(transaction_type=transaction_type)

        parameters = {
            'commodity_id': commodity_id,
            'grade_id': grade_id,
            'season': season,
            'site_id': site_id,
            'ngr_id': ngr_id,
            'truck_id': truck_id,
            'item_type': item_type
        }
        keys = parameters.keys()
        all_combinations = []
        for combination in itertools.product([True, False], repeat=len(parameters)):
            combination_dict = {key: (parameters[key] if include else None) for key, include in zip(keys, combination)}
            if combination_dict['commodity_id'] is None and combination_dict['grade_id'] is not None:
                continue
            all_combinations.append(combination_dict)

        # sorting non nullable parameters in descending order
        all_combinations.sort(key=lambda comb: sum(1 for v in comb.values() if v is not None), reverse=True)

        for criteria in all_combinations:
            filter_criteria = {}
            for key, value in criteria.items():
                if value:
                    filter_criteria[key] = value
                else:
                    filter_criteria[key + '__isnull'] = True

            best_match = mappings.filter(**filter_criteria).first()
            if best_match:
                return best_match

        return None


class ExternalPortal(BaseModel):
    class Meta:
        db_table = 'company_external_portals'

    FILLABLES = [
        'company_id',
        'portal',
        'url',
        'password',
        'username',
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='external_portals')
    portal = models.CharField(max_length=300)
    url = models.URLField(null=True, blank=True)
    username = models.TextField()
    password = models.TextField()


class ExternalBookingConnection(RawModel):
    class Meta:
        db_table = 'company_external_booking_connections'

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='external_booking_connections')
    username = models.TextField()
    password = models.TextField()
    client_id = models.CharField(max_length=255, null=True, blank=True)
    client_secret = models.CharField(max_length=255, null=True, blank=True)
    encoded_client_credentials = models.TextField(null=True, blank=True)
    refresh_token = models.TextField()
    access_token = models.TextField()
    expires_in = models.DateTimeField()  # datetime when the access token expires
    base_url = models.TextField(null=True, blank=True)
    url_paths = models.JSONField(default=dict, blank=True)
    raw_password = None

    @property
    def is_expired(self):
        return timezone.now() >= self.expires_in

    def reset(self, refresh_token, access_token, expires_in):
        self.refresh_token = refresh_token
        self.access_token = access_token
        self._reset_expires_in(expires_in)
        self.save()

    def refresh_tokens(self):
        if self.refresh_token and self.is_expired:
            service = self.company.get_external_booking_service()
            result = service.refresh_tokens(self.refresh_token)
            if isinstance(result, dict) and 'error' in result.keys():
                return self.reset_all_tokens()
            refresh_token = get(result, 'refresh_token')
            access_token = get(result, 'access_token')
            expires_in = get(result, 'expires_in')
            if refresh_token and access_token:
                self.reset(refresh_token, access_token, expires_in)
            return result
        return {'refresh_token': self.refresh_token, 'access_token': self.access_token}

    def _reset_expires_in(self, seconds):
        self.expires_in = timezone.now() + timedelta(seconds=seconds)

    @staticmethod
    def get_signer():
        return signing.Signer(salt=settings.SECRET_KEY)

    def encrypt_password(self):
        signer = self.get_signer()
        self.password = signer.sign_object(self.password)

    def set_raw_password(self):
        signer = self.get_signer()
        self.raw_password = signer.unsign_object(self.password)

    @classmethod
    def create_credentials(cls, company_id, params):
        if not cls.get_queryset(company_id):
            credentials = cls(company_id=company_id, **params)
            credentials.encrypt_password()
            credentials.save()
        return cls.get_credentials(company_id)

    @classmethod
    def get_queryset(cls, company_id):
        return cls.objects.filter(company_id=company_id)

    @classmethod
    def get_credentials(cls, company_id):
        credentials = cls.get_queryset(company_id).first()
        if credentials:
            credentials.set_raw_password()
            return credentials

    def reset_all_tokens(self):
        service = self.company.get_external_booking_service()
        self.set_raw_password()
        result = service.refresh_all_tokens(self.username, self.raw_password)
        refresh_token = get(result, 'refresh_token')
        access_token = get(result, 'access_token')
        expires_in = get(result, 'expires_in')
        if refresh_token and access_token:
            self.reset(refresh_token, access_token, expires_in)
        return result


class ApplicationRate(RawModel):
    class Meta:
        db_table = 'company_application_rates'

    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, related_name='application_rates')
    commodity = models.ForeignKey('commodities.Commodity', on_delete=models.DO_NOTHING)
    rate = models.FloatField(default=0)

    @classmethod
    def is_duplicate(cls, data, company_id):
        if not isinstance(data, list):
            data = [data]
        return any(
            cls.objects.filter(
                commodity_id=get(data_row, 'commodity_id'), rate=float(get(data_row, 'rate')), company_id=company_id
            ).exists() for data_row in data
        )

    @classmethod
    def persist_many(cls, data, company_id):
        if not isinstance(data, list):
            data = [data]
        application_rates = []
        for data_row in data:
            application_rate = cls(**{**data_row, 'company_id': company_id})
            application_rate.save()
            application_rates.append(application_rate)
        return application_rates

    def mark_inactive(self):
        self.is_active = False
        self.save()

    @property
    def rate_display(self):
        return f'{self.rate} {get(UNIT_ABBREVIATIONS, self.commodity.price_unit)}/{self.commodity.country.display_unit}'
