# Generated by Django 5.1.8 on 2025-05-05 04:32

from django.db import migrations


def set_payment_run_true(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    Company.objects.filter(
        abn__in=[
            '***********', '***********', '***********', '***********',
            '***********', '***********', '***********', '***********'
        ]
    ).update(payment_run=True)


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0213_company_payment_run_historicalcompany_payment_run'),
    ]

    operations = [
        migrations.RunPython(set_payment_run_true),
    ]
