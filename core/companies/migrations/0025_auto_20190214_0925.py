# Generated by Django 2.1.5 on 2019-02-14 09:25

from django.db import migrations

def build_common(obj):
    obj.companies = True
    obj.contracts = True
    obj.orders = True
    obj.movements = True
    obj.invoices = True
    obj.site_bookings = True

def build_system(obj):
    obj.companies = True
    obj.contracts = True
    obj.orders = True
    obj.movements = True
    obj.invoices = True
    obj.site_bookings = True
    obj.site_management = True
    obj.farms = True
    obj.stocks = True

def build_grower(obj):
    build_common(obj)
    obj.farms = True
    obj.stocks = True

def build_broker(obj):
    build_system(obj)

def build_logistics(obj):
    build_common(obj)
    obj.site_management = True

def build_bhc(obj):
    build_logistics(obj)

def build_trader(obj):
    build_logistics(obj)

def create_platform_features(apps, schema_editor):
    PlatformFeatures = apps.get_model('companies', 'PlatformFeatures')
    Company = apps.get_model('companies', 'Company')
    for company in Company.objects.all():
        features = PlatformFeatures(company=company)
        if company.type_id == 1:
            build_grower(features)
        if company.type_id == 2:
            build_broker(features)
        if company.type_id == 3:
            build_logistics(features)
        if company.type_id == 4:
            build_bhc(features)
        if company.type_id == 5:
            build_trader(features)

        features.save()


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0024_platformfeatures'),
    ]

    operations = [
        migrations.RunPython(create_platform_features)
    ]
