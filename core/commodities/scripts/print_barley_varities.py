import csv
import json
import sys

csvfile = open(sys.argv[-1], "r")
fieldnames = ("Variety","Grade 1 Name","Grade 2 Name","Grade 3 Name","Maximum Grade")
reader = csv.DictReader(csvfile, fieldnames)
res = []
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _json = {
            "fields": {
                "created_at": "2018-04-02T08:00:00+00:00",
            },
        }
        _json["pk"] = i
        _json["model"] = "commodities.barleyvariety"
        _json["fields"]["name"] = _dict["Variety"]
        _json["fields"]["grade_1"] = _dict["Grade 1 Name"]
        _json["fields"]["grade_2"] = _dict["Grade 2 Name"]
        _json["fields"]["grade_3"] = _dict["Grade 3 Name"]
        _json["fields"]["maximum_grade"] = _dict["Maximum Grade"]
        res.append(_json)

print(json.dumps(res))
