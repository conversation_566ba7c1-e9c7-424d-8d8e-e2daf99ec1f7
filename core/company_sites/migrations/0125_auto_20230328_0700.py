# Generated by Django 4.1.7 on 2023-03-28 07:00

from django.db import migrations
from bulk_update.helper import bulk_update


def add_mass_limit_code_to_site_management_settings_default_fields(apps, schema_editor):
    SiteManagementSettings = apps.get_model("company_sites", "SiteManagementSettings")
    settings = SiteManagementSettings.objects.all()
    for setting in settings:
        setting.fields.append({
          "show": False,
          "defaultValue": None,
          "mandatory": False,
          "label": "Mass Limit Code",
          "name": "Mass Limit Code",
          "customisable": True,
          "id": "massLimitCode"
        })
    bulk_update(settings, update_fields=['fields'], batch_size=1000)


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0124_remove_freightslot_category_and_more'),
    ]

    operations = [
        migrations.RunPython(add_mass_limit_code_to_site_management_settings_default_fields)
    ]
