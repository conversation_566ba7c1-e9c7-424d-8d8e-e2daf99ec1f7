# Generated by Django 4.1.13 on 2024-02-14 06:15

import django.contrib.postgres.fields
from django.db import migrations, models

def update_contract_allocations(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    for company in Company.objects.filter(models.Q(contract_allocation=True) | models.Q(contract_fulfillment=True)):
        allocations = []
        if company.contract_allocation:
            allocations.extend(['direct_to_buyer', 'stock'])
        if company.contract_fulfillment:
            allocations.append('through_warehouse')
        company.contract_allocations = allocations
        company.save()

class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0159_company_freight_invoicing_from_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='contract_allocations',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(
                choices=[('through_warehouse', 'Through Warehouse'), ('direct_to_buyer', 'Direct To Buyer'),
                         ('stock', 'Stock')], max_length=255), blank=True, default=list, null=True, size=None),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='contract_allocations',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(
                choices=[('through_warehouse', 'Through Warehouse'), ('direct_to_buyer', 'Direct To Buyer'),
                         ('stock', 'Stock')], max_length=255), blank=True, default=list, null=True, size=None),
        ),
        migrations.RunPython(update_contract_allocations),
        migrations.RemoveField(
            model_name='company',
            name='contract_allocation',
        ),
        migrations.RemoveField(
            model_name='company',
            name='contract_fulfillment',
        ),
        migrations.RemoveField(
            model_name='historicalcompany',
            name='contract_allocation',
        ),
        migrations.RemoveField(
            model_name='historicalcompany',
            name='contract_fulfillment',
        ),
    ]
