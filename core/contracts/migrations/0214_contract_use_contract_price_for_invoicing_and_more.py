# Generated by Django 4.1.13 on 2024-08-21 09:02

from django.db import migrations, models

def update_existing_blended_contracts(apps, schema_editor):
    Contract = apps.get_model('contracts', 'Contract')
    Contract.objects.filter(type_id=9).update(use_contract_price_for_invoicing=False)


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0213_alter_contract_contract_bid'),
    ]

    operations = [
        migrations.AddField(
            model_name='contract',
            name='use_contract_price_for_invoicing',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='historicalcontract',
            name='use_contract_price_for_invoicing',
            field=models.BooleanField(default=True),
        ),
        migrations.RunPython(update_existing_blended_contracts)
    ]
