# Generated by Django 2.1.9 on 2019-06-18 11:11

from django.db import migrations
import django.db.models.manager


class Migration(migrations.Migration):

    dependencies = [
        ('conditions', '0010_auto_20190618_0726'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='generalcondition',
            name='company',
        ),
        migrations.RemoveField(
            model_name='generalcondition',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='generalcondition',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='historicalgeneralcondition',
            name='company',
        ),
        migrations.RemoveField(
            model_name='historicalgeneralcondition',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='historicalgeneralcondition',
            name='history_user',
        ),
        migrations.RemoveField(
            model_name='historicalgeneralcondition',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='historicalspecialcondition',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='historicalspecialcondition',
            name='employee',
        ),
        migrations.RemoveField(
            model_name='historicalspecialcondition',
            name='history_user',
        ),
        migrations.RemoveField(
            model_name='historicalspecialcondition',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='specialcondition',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='specialcondition',
            name='employee',
        ),
        migrations.RemoveField(
            model_name='specialcondition',
            name='updated_by',
        ),
        migrations.AlterModelManagers(
            name='condition',
            managers=[
                ('special_objects', django.db.models.manager.Manager()),
            ],
        ),
        migrations.DeleteModel(
            name='GeneralCondition',
        ),
        migrations.DeleteModel(
            name='HistoricalGeneralCondition',
        ),
        migrations.DeleteModel(
            name='HistoricalSpecialCondition',
        ),
        migrations.DeleteModel(
            name='SpecialCondition',
        ),
    ]
