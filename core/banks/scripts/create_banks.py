import csv
import json
import os

script_directory = os.path.dirname(os.path.abspath(__file__))
file_path = os.path.abspath(os.path.join('../../fixtures/banks.json'))

with open(file_path, 'r') as file:
    bank_data = json.load(file)

for data in bank_data:
    data['fields']['country_id'] = 1

canada_csv_data = []
with open('./canada_banks.csv', mode='r', newline='', encoding='utf-8') as file:
    csv_reader = csv.reader(file)
    for row in csv_reader:
        canada_csv_data.append(row)

pk = 15108
for data in canada_csv_data:
    bank_name = data[0]
    bsb_number = data[1]
    code = data[2]
    bank_data.append(dict(
        pk=pk,
        model="banks.bank",
        fields=dict(
            code=code,
            bsb_number=bsb_number,
            name=bank_name,
            country_id=3,
        )
    ))
    pk += 1

us_csv_data = []
with open('./us_banks.csv', mode='r', newline='', encoding='utf-8') as file:
    csv_reader = csv.reader(file)
    for row in csv_reader:
        us_csv_data.append(row)

for data in us_csv_data:
    bank_name = data[0]
    bsb_number = data[1]
    code = data[2]
    bank_data.append(dict(
        pk=pk,
        model="banks.bank",
        fields=dict(
            code=code,
            bsb_number=bsb_number,
            name=bank_name,
            country_id=2,
        )
    ))
    pk += 1

nz_csv_data = []
with open('./nz_banks.csv', mode='r', newline='', encoding='utf-8') as file:
    csv_reader = csv.reader(file)
    for row in csv_reader:
        nz_csv_data.append(row)

for data in nz_csv_data:
    bank_name = data[0]
    bsb_number = data[1]
    code = data[2]
    bank_data.append(dict(
        pk=pk,
        model="banks.bank",
        fields=dict(
            code=code,
            bsb_number=bsb_number,
            name=bank_name,
            country_id=4,
        )
    ))
    pk += 1

print(json.dumps(bank_data))
with open(os.path.abspath(os.path.join('../../fixtures/banks.json')), 'w') as file:
    json.dump(bank_data, file, indent=3)



