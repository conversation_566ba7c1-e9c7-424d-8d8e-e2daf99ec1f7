# Generated by Django 4.1.13 on 2024-09-10 11:58

from django.db import migrations
from core.alerts.constants import ACQUISITION_REPORT_ALERT_CODE, FREQUENCY_DAILY, EMAIL_CHANNEL


def set_acquisition_alert_recipients(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    Alert = apps.get_model('alerts', 'Alert')
    Employee = apps.get_model('profiles', 'Employee')
    Recipient = apps.get_model('alerts', 'Recipient')

    for company in Company.objects.filter(acquisition_recipients__emails__isnull=False):
        employee_ids = Employee.objects.filter(
            company_id=company.id, email__in=company.acquisition_recipients['emails']).values_list('id', flat=True)
        if employee_ids:
            payload = {
                'name': ACQUISITION_REPORT_ALERT_CODE,
                'frequency': FREQUENCY_DAILY,
                'channel': EMAIL_CHANNEL,
                'company_id': company.id,
                'recipients': [
                    {
                        'party': 'own_company',
                        'employee_roles': [],
                        'employees': list(employee_ids)
                    }
                ]
            }
            recipients = payload.pop('recipients', None)
            alert = Alert(**payload)
            alert.save()
            if alert.id:
                for recipient_data in recipients:
                    recipient_data['alert'] = alert
                    emp_ids = recipient_data.pop('employees', None)
                    recipient = Recipient(**recipient_data)
                    recipient.save()
                    if emp_ids and recipient.id:
                        recipient.employees.set(Employee.objects.filter(id__in=emp_ids))


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0185_company_include_own_site_loads_in_acquisition_report_and_more'),
    ]

    operations = [
        migrations.RunPython(set_acquisition_alert_recipients)
    ]
