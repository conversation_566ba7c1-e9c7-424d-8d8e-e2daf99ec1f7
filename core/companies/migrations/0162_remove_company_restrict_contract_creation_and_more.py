# Generated by Django 4.1.13 on 2024-02-23 11:04

from django.db import migrations, models


def set_purchase_sale_contract_restriction_based_on_old_settings(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    for company in Company.objects.filter(restrict_contract_creation=True):
        if company.type_of_contract == 'purchase_contract':
            company.purchase_contract_creation_restriction = 'others_cannot_create'
        elif company.type_of_contract == 'sale_contract':
            company.sale_contract_creation_restriction = 'others_cannot_create'
        else:
            company.purchase_contract_creation_restriction = 'others_cannot_create'
            company.sale_contract_creation_restriction = 'others_cannot_create'
        company.save()


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0161_companygroup_business_type_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='purchase_contract_creation_restriction',
            field=models.CharField(choices=[('others_can_create_without_acceptance', 'Others can create without acceptance'), ('others_can_create_but_requires_acceptance', 'Others can create but requires acceptance'), ('others_cannot_create', 'Others cannot create')], default='others_can_create_without_acceptance', max_length=100),
        ),
        migrations.AddField(
            model_name='company',
            name='sale_contract_creation_restriction',
            field=models.CharField(choices=[('others_can_create_without_acceptance', 'Others can create without acceptance'), ('others_can_create_but_requires_acceptance', 'Others can create but requires acceptance'), ('others_cannot_create', 'Others cannot create')], default='others_can_create_without_acceptance', max_length=100),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='purchase_contract_creation_restriction',
            field=models.CharField(choices=[('others_can_create_without_acceptance', 'Others can create without acceptance'), ('others_can_create_but_requires_acceptance', 'Others can create but requires acceptance'), ('others_cannot_create', 'Others cannot create')], default='others_can_create_without_acceptance', max_length=100),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='sale_contract_creation_restriction',
            field=models.CharField(choices=[('others_can_create_without_acceptance', 'Others can create without acceptance'), ('others_can_create_but_requires_acceptance', 'Others can create but requires acceptance'), ('others_cannot_create', 'Others cannot create')], default='others_can_create_without_acceptance', max_length=100),
        ),
        migrations.RunPython(set_purchase_sale_contract_restriction_based_on_old_settings),
        migrations.RemoveField(
            model_name='company',
            name='restrict_contract_creation',
        ),
        migrations.RemoveField(
            model_name='company',
            name='type_of_contract',
        ),
        migrations.RemoveField(
            model_name='historicalcompany',
            name='restrict_contract_creation',
        ),
        migrations.RemoveField(
            model_name='historicalcompany',
            name='type_of_contract',
        ),
    ]
