# Generated by Django 4.2.15 on 2024-10-17 09:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0192_country_wise_unique_business_names'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='abn_status',
            field=models.CharField(blank=True, choices=[('active', 'Active'), ('cancelled', 'Cancelled')], max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='abn_status_last_updated_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='abn_status',
            field=models.CharField(blank=True, choices=[('active', 'Active'), ('cancelled', 'Cancelled')], max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='abn_status_last_updated_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
