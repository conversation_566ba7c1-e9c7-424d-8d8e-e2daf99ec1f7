import copy
import decimal
import json
import warnings
from datetime import timed<PERSON>ta, datetime

from decimal import Decimal, Context, localcontext
import inflect
import inflection
from deepmerge import Merger
from deepmerge.strategy.core import StrategyList
from dirtyfields import DirtyFieldsMixin
from django.apps import apps
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.core import exceptions
from django.core.exceptions import ValidationError, FieldDoesNotExist
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.db.models.expressions import CombinedExpression
from django.utils import timezone
from pydash import get, compact, flatten
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rest_framework.utils.urls import replace_query_param, remove_query_param
from simple_history.models import HistoricalRecords

from core.common.constants import (PAGE_SIZE_QUERY_PARAM, LAST_PAGE,
                                   FILTER_MAPPINGS, WAREHOUSE_INLOAD_FEES_MODEL,
                                   WAREHOUSE_OUTLOAD_FEES_MODEL, WAREHOUSE_STORAGE_FEES_MODEL,
                                   WAREHOUSE_TRANSFER_FEES_MODEL, LOAD_MODEL,
                                   SYSTEM_COMPANY_IDS, AU_ROOT_USER_ID, WAREHOUSE_STOCK_SWAP_MODEL,
                                   WAREHOUSE_REGRADE_RESEASON_MODEL,
                                   WAREHOUSE_THROUGHPUT_INLOAD_FEES_MODEL, WAREHOUSE_THROUGHPUT_OUTLOAD_FEES_MODEL,
                                   OUTLOAD, INLOAD,
                                   PREDEFINED_DATE_RANGE_FILTER_FOR_NUMBER_OF_DAYS_KEYS,
                                   FILTERS_KEY_MAPPING,
                                   PREDEFINED_DATE_RANGE_FILTER_FOR_TIME_PERIOD_KEYS,
                                   CONTRACT_PREDEFINED_DATE_RANGE_FILTER_KEYS, ORDER_PREDEFINED_DATE_RANGE_FILTER_KEYS,
                                   SITE_LOADS_DATE_RANGE_FILTER_KEYS, TITLE_TRANSFER_DATE_RANGE_FILTER_KEYS,
                                   FREIGHT_MOVEMENT_DATE_RANGE_FILTER_KEYS, INVOICE_DATE_RANGE_FILTER_KEYS,
                                   PACK_ORDER_PREDEFINED_DATE_RANGE_FILTER_KEYS, CONTRACT_BIDS_DATE_RANGE_FILTER_KEYS)
from core.common.expressions import CustomRegexWrapper, Similarity
from core.common.utils import deepgetattr, strip_special, decode_string
from core.freights.constants import REQUEST_ORDER_TYPES_MAPPING, NULL_STATUSES
from core.timezones.utils import DateTimeUtil


# for file streaming
class Echo:
    """An object that implements just the write method of the file-like
    interface.
    """

    def write(self, value):
        """Write the value by returning it, instead of storing in a buffer."""
        return value


class NewListStrategies(StrategyList):
    """
    Contains the strategies provided for lists.
    """

    NAME = "list"

    @staticmethod
    def strategy_override(config, path, base, nxt):  # pylint: disable=unused-argument
        """ use the list nxt. """
        return nxt

    @staticmethod
    def strategy_prepend(config, path, base, nxt):  # pylint: disable=unused-argument
        """ prepend nxt to base. """
        return nxt + base

    @staticmethod
    def strategy_append(config, path, base, nxt):  # pylint: disable=unused-argument
        """ append nxt to base. """
        for _ele in nxt:
            base.append(_ele) if _ele not in base else None
        return base


class RawModel(DirtyFieldsMixin, models.Model):
    class Meta:
        abstract = True
        ordering = ['-id']

    def __init__(self, *args, **kwargs):
        self.errors = {}
        super().__init__(*args, **kwargs)

    FILLABLES = []
    mandatory_props = ['entity', 'is_active', ]

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    created_by = models.ForeignKey(
        'profiles.Employee',
        null=False,
        blank=False,
        default=AU_ROOT_USER_ID,
        on_delete=models.SET_DEFAULT,
        related_name='%(app_label)s_%(class)s_related_created_by',
        related_query_name='%(app_label)s_%(class)ss_created_by',
    )
    updated_by = models.ForeignKey(
        'profiles.Employee',
        null=False,
        blank=False,
        default=AU_ROOT_USER_ID,
        on_delete=models.SET_DEFAULT,
        related_name='%(app_label)s_%(class)s_related_updated_by',
        related_query_name='%(app_label)s_%(class)ss_updated_by',
    )

    @staticmethod
    def similarity_search(queryset, search_str, fields, extra_contains_fields=None):
        removable_characters = [
            '.', '-', "'", 'the ', '& ', 'for ', 'trust', 'trustee', 'transport', 'services', 'stockfeeds', 'pastoral',
            'limited', 'ltd', 'private', 'pty', 'service', 'grain', 'grains', 'farm', 'farms', 'crop', 'cropping',
            'rural', 'trustees', 'and', 'enterprise', 'enterprises', 'mill', 'mills', 'trading', 'australia', 'haulage',
            'logistics', 'grower', 'trader', 'traders', 'bulk', 'growers', 'storage'
        ]

        new_search_str = search_str
        for word in removable_characters:
            new_search_str = new_search_str.lower().replace(word, '').strip()
        new_search_str = new_search_str.strip()
        words = new_search_str.split(' ')
        first_word = words[0]

        def get_whens():
            whens = []
            for field in [*fields, *(extra_contains_fields or [])]:
                whens += [
                    models.When(**{f'{field}__istartswith': search_str, 'then': models.Value(3)}),
                    models.When(**{f'{field}__icontains': search_str, 'then': models.Value(2)}),
                    models.When(**{f'{field}__iendswith': search_str, 'then': models.Value(1)}),
                    models.When(**{f'{field}__istartswith': first_word, 'then': models.Value(0.5)})
                ]
            return whens

        def get_exact_match_whens():
            whens = []
            for field in [*(extra_contains_fields or [])]:
                operator = 'icontains' if field == 'extras__alias' else 'iexact'
                whens += [
                    models.When(**{f'{field}__{operator.replace("i", "")}': search_str, 'then': models.Value(11)}),
                    models.When(**{f'{field}__{operator}': search_str, 'then': models.Value(10)}),
                ]
            return whens

        return queryset.annotate(
            **{f'{field}_similarity': Similarity(models.F(field), models.Value(search_str)) for field in fields}
        ).annotate(
            contains=models.Case(
                *get_whens(),
                default=models.Value(0.1),
                output_field=models.IntegerField()
            ),
            exact_match_score=models.Case(
                *get_exact_match_whens(),
                default=models.Value(0.1),
                output_field=models.IntegerField()
            )
        )

    def to_dict(  # pylint: disable=too-many-arguments
            self,
            properties=(),
            user_properties=(),
            user=None,
            many_to_one_relations=None,
            one_to_many_relations=None,
            one_to_one_relations=None,
            excludes=(),
    ):
        _dict = self.__dict__
        _data = {}

        for _k, _v in _dict.items():
            if(
                    'model' not in _v.__class__.__module__ and
                    'prefetched' not in _k and
                    ('password_expiry' in _k or 'password' not in _k) and
                    '_original_state' not in _k and
                    'xero_client_secret' not in _k and
                    'xero_tenant_id' not in _k and
                    'pickup_viewer_company_ids' not in _k and
                    'delivery_viewer_company_ids' not in _k
            ):
                if _k == 'errors' and not _v:
                    continue

                _data[_k] = _v

        properties = [
            prop for prop in (
                list(properties) + [
                    _ for _ in getattr(self, 'mandatory_props', []) if _ in [
                        'entity', 'is_active', 'display_name', 'wheat_classification_zone_lookup_key', 'name',
                        'type_name', 'dominant_grade', 'is_gate', 'net_weight',
                        'farmemployee_signature_url', 'driver_signature_url', 'storage_type', 'logistics_signature_url',
                        'is_registered', 'is_fleet', 'my_company_logo_url', 'share_authority_url', '_datetime',
                        'site_name', 'ld', 'handler', 'farm_id', 'driver_signature_url', 'site_address', 'epr',
                        'estimated_tonnage', 'operator_name', 'reference_number', 'status', 'is_completed',
                        'is_pristine', 'ordered_assets', 'location_content_type',
                        'docket_image_url', 'url', 'is_bhc_site', 'is_quantity_based', 'price_unit', 'country_code',
                        'tonnage_unit', 'is_strict_quantity_based', 'quantity_label', 'type'
                    ]
                ]  # temp fix
            ) if prop not in excludes
        ]
        for _property in properties:
            _data[_property] = getattr(self, _property, None)
        for _property in user_properties:
            _data[_property] = getattr(self, _property, None)(user=user)

        if self.persisted:
            if many_to_one_relations:
                _data.update(RawModel._jsonify_many_to_one(
                    root=self,
                    relations=many_to_one_relations,
                ))
            if one_to_many_relations:
                _data.update(RawModel._jsonify_one_to_many(
                    root=self,
                    relations=one_to_many_relations,
                ))

            if one_to_one_relations:
                _data.update(RawModel._jsonify(
                    root=self,
                    relations=one_to_one_relations,
                ))
        return _data

    @property
    def entity(self):
        return self.__class__.__name__.lower()

    @property
    def persisted(self):
        return getattr(self, 'id', None) is not None

    @classmethod
    def qs2dict(  # pylint: disable=too-many-arguments
            cls,
            queryset=None,
            many_to_one_relations=None,
            one_to_many_relations=None,
            one_to_one_relations=None,
            properties=(),
            user_properties=(),
            user=None,
            one_to_many_fields_with_relations=None,
            excludes=(),
    ):
        if queryset is None:
            queryset = cls.objects.all()

        resp = []
        for _obj in queryset:
            _json = _obj.to_dict(
                excludes=excludes, properties=properties, user_properties=user_properties, user=user
            )
            merger = Merger([(list, "append"), (dict, "merge")], [
                            "override"], ["override"])
            merger.PROVIDED_TYPE_STRATEGIES[list] = NewListStrategies

            if many_to_one_relations:
                _json = merger.merge(_json, cls._jsonify_many_to_one(
                    root=_obj,
                    relations=many_to_one_relations,
                ))
            if one_to_many_relations:
                _json = merger.merge(_json, cls._jsonify_one_to_many(
                    root=_obj,
                    relations=one_to_many_relations,
                    fields_with_relations=one_to_many_fields_with_relations,
                ))
            if one_to_one_relations:
                _json = merger.merge(_json, cls._jsonify(
                    root=_obj,
                    relations=one_to_one_relations,
                ))
            resp.append(_json)
        return resp

    @classmethod
    def search(cls, params=None):
        queryset = cls.search_queryset(params)

        return cls.qs2dict(queryset=queryset)

    @classmethod
    def search_queryset(cls, params):
        params = params or {}
        attrs = dict(params)
        for k in list(attrs.keys()):
            attrs[k + '__in'] = attrs.pop(k)
        if 'name__in' in attrs:
            attrs['name__iregex'] = r'(' + \
                '|'.join(attrs.pop('name__in')) + ')'
        queryset = cls.objects.filter(**attrs).all()
        return queryset

    @classmethod
    def update(cls, instance_id=None, data=None, update_fields=None, instance=None):
        data = data or {}
        instance = instance or cls.objects.get(id=instance_id)
        for key, value in data.items():
            setattr(instance, key, value)
        instance.save(update_fields=update_fields)

        return instance

    @classmethod
    def create(cls, params, kwargs=None, pre_validate_callback=None):
        kwargs = kwargs or {}
        _obj = cls(**params)
        for _attr, _val in kwargs.items():
            setattr(_obj, _attr, _val)

        if pre_validate_callback:
            _func = getattr(_obj, pre_validate_callback, None)
            if _func:
                _func()

        try:
            _obj.full_clean()
            _obj.save()
        except ValidationError as _e:
            _obj.errors.update(_e.message_dict)

        return _obj

    @classmethod
    def create_with_exception(cls, **kwargs):
        kwargs = kwargs or {}
        instance = cls(**kwargs)
        instance.save_only()
        return instance

    @classmethod
    def create_with_location(cls, params, kwargs=None, pre_validate_callback=None):
        kwargs = kwargs or {}
        location_types = {
            'Company': 'company',
            'Storage': 'storage',
            'Farm': 'farm',
            'FarmField': 'farmfield',
        }
        name_field = 'name'
        data = copy.deepcopy(params)
        address_data = data.pop('address', {})
        address_data['name'] = address_data.get(
            'name',
            address_data.get('address', data.get(name_field, cls.__name__))
        )
        address_data['location_type'] = location_types[cls.__name__]
        if 'state' in address_data:
            from core.states.models import State
            state_name = address_data.pop('state')
            state = State.objects.filter(name__icontains=state_name).first() if state_name else None
            if state:
                address_data['state_id'] = state.id
        from core.locations.models import Location
        address = Location.create(address_data)
        if getattr(address, 'errors', None):
            obj = cls.create(data, kwargs, pre_validate_callback)
            obj.errors = getattr(obj, 'errors', {})
            obj.errors.update(address.errors)
        else:
            kwargs['address_id'] = address.id
            obj = cls.create(data, kwargs, pre_validate_callback)
            if not obj.id:
                address.delete()
        return obj

    @classmethod
    def update_with_location(cls, instance_id, params, update_fields=None):
        _data = copy.deepcopy(params)
        _address_data = _data.pop('address', {})
        _obj = cls.update(instance_id, _data, update_fields=update_fields)
        if not getattr(_obj, 'errors', None) and _address_data:
            from core.locations.models import Location
            from core.states.models import State
            state_name = _address_data.pop('state', None)
            if state_name and (state := State.objects.filter(name__icontains=state_name).first()):
                _address_data['state_id'] = state.id
            _obj.address = Location.update(_obj.address_id, _address_data)

        return _obj

    @classmethod
    def _jsonify_many_to_one(cls, root, relations):
        res = {}

        for _relation_query in relations:
            _double_underscore_split = _relation_query.split('__', 1)
            _ampersand_split = _double_underscore_split[0].split('&')

            for _ampersand_relation in _ampersand_split:
                _dot_split = _ampersand_relation.split('.')
                _relation_obj = getattr(root, _dot_split[0], None)
                res[_dot_split[0]] = _relation_obj.to_dict(
                ) if _relation_obj else None

                _deeper_relation_split =\
                    _double_underscore_split if len(_double_underscore_split) == 2 else\
                    _dot_split if len(_dot_split) == 2 else None

                if _relation_obj and _deeper_relation_split:
                    res[_dot_split[0]].update(cls._jsonify_many_to_one(
                        root=_relation_obj,
                        relations=[_deeper_relation_split[1]],
                    ))

        return res

    @classmethod
    def _jsonify_one_to_many(cls, root, relations, fields_with_relations=None):
        res = {}
        _engine = inflect.engine()
        for _relation in relations:  # pylint: disable=cell-var-from-loop
            one_to_many_relations = None
            _relation_obj = getattr(root, _relation + '_set', None)
            if fields_with_relations and _relation in fields_with_relations:
                _relation_obj = _relation_obj.only(
                    *(flatten(fields_with_relations[_relation].values())
                      if 'fields' in fields_with_relations[_relation] else [])
                ).select_related(*fields_with_relations[_relation].get('relations', [None]))
                one_to_many_relations = [
                    *fields_with_relations[_relation].get('relations', [])]

            res[_engine.plural(_relation)] = list(map(
                lambda o: o.to_dict(
                    one_to_many_relations=one_to_many_relations),  # pylint: disable=cell-var-from-loop
                _relation_obj.all()
            )) if _relation_obj else None
        return res

    @classmethod
    def _jsonify(cls, root, relations):  # pylint: disable=too-many-locals
        res = {}
        _engine = inflect.engine()
        merger = Merger([(list, "append"), (dict, "merge")],
                        ["override"], ["override"])
        merger.PROVIDED_TYPE_STRATEGIES[list] = NewListStrategies
        for _relation in relations:
            _relation_splits = _relation.split('__', 1)
            _ampersand_split = _relation_splits[0].split('&')
            for _single_relation in _ampersand_split:
                _rel = _single_relation
                if _rel.startswith("*"):
                    _rel = _rel[1:]
                    _key = _engine.plural(_rel)
                    _relation_obj = getattr(root, _rel + '_set', None)
                    _relation_obj_all = _relation_obj.all()
                    _relation_obj_items = list(map(
                        lambda o: o.to_dict(),
                        _relation_obj_all
                    )) if _relation_obj else []
                    res.setdefault(_key, [])
                    res[_key] = merger.merge(res[_key], _relation_obj_items)
                    if _relation_obj and len(_relation_splits) > 1:
                        for index, _record in enumerate(_relation_obj_all):
                            _relation_obj_dict = cls._jsonify(
                                root=_record,
                                relations=[_relation_splits[1]],
                            )
                            res[_key][index] = merger.merge(
                                res[_key][index], _relation_obj_dict
                            )
                else:
                    _relation_obj = getattr(root, _rel, None)
                    _relation_obj_dict = _relation_obj.to_dict() if _relation_obj else None
                    res.setdefault(_rel, None)
                    res[_rel] = merger.merge(res[_rel], _relation_obj_dict)
                    if _relation_obj and len(_relation_splits) > 1:
                        _relation_obj_dict = cls._jsonify(
                            root=_relation_obj,
                            relations=[_relation_splits[1]],
                        )
                        res.setdefault(_rel, None)
                        res[_rel] = merger.merge(res[_rel], _relation_obj_dict)

        return res

    @classmethod
    def content_type_ids_for(cls, model_names):
        _content_types = ContentType.objects.filter(model__in=model_names)

        _res = []
        for _name in model_names:
            # pylint: disable=cell-var-from-loop
            _res.append(
                list(filter(lambda _ct: _ct.model == _name, _content_types))[0].id)

        return _res

    @classmethod
    def set_content_type_params(cls, params, _id, _type, obj):  # pylint:disable=too-many-branches
        _engine = inflect.engine()
        _param_id = params.pop(_id, None)
        _param_type = params.pop(_type, None)

        if _param_type == 'titletransfer':
            _klass = apps.get_model('contracts', 'TitleTransfer')
        elif _param_type == 'freightcontract':
            _klass = apps.get_model('freights', 'FreightContract')
        elif _param_type == 'brokerage':
            _klass = apps.get_model('contracts', 'Brokerage')
        elif _param_type == 'customitem':
            _klass = apps.get_model('invoices', 'CustomItem')
        elif _param_type == 'carryitem':
            _klass = apps.get_model('invoices', 'CarryItem')
        elif _param_type == 'subscriptionitem':
            _klass = apps.get_model('invoices', 'SubscriptionItem')
        elif _param_type == 'grainlevy':
            _klass = apps.get_model('invoices', 'GrainLevy')
        elif _param_type == 'epr':
            _klass = apps.get_model('invoices', 'EprItem')
        elif _param_type == 'freightorder':
            _klass = apps.get_model('freights', 'FreightOrder')
        elif _param_type == WAREHOUSE_INLOAD_FEES_MODEL:
            _klass = apps.get_model('invoices', 'WarehouseInloadItem')
        elif _param_type == WAREHOUSE_OUTLOAD_FEES_MODEL:
            _klass = apps.get_model('invoices', 'WarehouseOutloadItem')
        elif _param_type == WAREHOUSE_THROUGHPUT_INLOAD_FEES_MODEL:
            _klass = apps.get_model('invoices', 'WarehouseThroughputInloadItem')
        elif _param_type == WAREHOUSE_THROUGHPUT_OUTLOAD_FEES_MODEL:
            _klass = apps.get_model('invoices', 'WarehouseThroughputOutloadItem')
        elif _param_type == WAREHOUSE_STORAGE_FEES_MODEL:
            _klass = apps.get_model('invoices', 'WarehouseStorageItem')
        elif _param_type == WAREHOUSE_TRANSFER_FEES_MODEL:
            _klass = apps.get_model('invoices', 'WarehouseTransferItem')
        elif _param_type == LOAD_MODEL:
            _klass = apps.get_model('loads', 'Load')
        elif _param_type == WAREHOUSE_STOCK_SWAP_MODEL:
            _klass = apps.get_model('loads', 'StockSwap')
        elif _param_type == WAREHOUSE_REGRADE_RESEASON_MODEL:
            _klass = apps.get_model('loads', 'RegradeReseason')
        elif _param_type == 'warehouse':
            _klass = apps.get_model('farms', 'WarehouseFees')
        else:
            _klass_name = inflection.camelize(_param_type)
            _klass = apps.get_model(_engine.plural(_param_type), _klass_name)

        _obj = _klass.objects.filter(id=_param_id).first()
        params[obj] = _obj
        return params

    @staticmethod
    def order_queryset(queryset, order_by_map, order_by, order, default_order_by_field='created_at', distinct=False):
        if not order_by:
            return queryset

        _order_by = order_by_map.get(inflection.underscore(order_by), [default_order_by_field])
        is_desc = order == 'desc'
        order = '-' if is_desc else ''
        if isinstance(_order_by, tuple):
            # tuples declaration means if else on fields null values
            # also sort null at the end for both asc and desc
            prev_field = None
            when_criterion = []
            for i, field in enumerate(_order_by):
                when_criterion.append(
                    models.When(**{f"{field}__isnull": False, 'then': models.F(field)}) if i == 0 else
                    models.When(**{f"{prev_field}__isnull": True, 'then': models.F(field)})
                )
                prev_field = field

            is_date_type_field = 'date' in order_by
            ordering_case = models.Case(
                models.When(**{
                    f"{order_by}__isnull": True,
                    'then': models.Value(
                        datetime.min if is_desc else datetime.max) if is_date_type_field else models.Value(None)
                }),
                default=models.F(order_by)
            )
            queryset = queryset.annotate(
                **{
                    order_by: models.Case(
                        *when_criterion,
                        default=models.Value(None),
                        output_field=models.DateTimeField() if is_date_type_field else models.CharField()
                    )
                }
            ).order_by(
                ordering_case.desc() if is_desc else ordering_case
            )
            if distinct:
                queryset = queryset.distinct()
        else:
            queryset = queryset.order_by(*[order + f for f in _order_by])
        return queryset

    def save(self, *args, **kwargs):  # pylint: disable=arguments-differ,signature-differs
        try:
            self.full_clean(validate_constraints=False)
            self.__sanitize_errors_for_sys_admin()
            if not self.errors:
                super().save(*args, **kwargs)
        except ValidationError as ex:
            self.errors.update(get(ex, 'message_dict') or {})
            self.__sanitize_errors_for_sys_admin()
            if not self.errors:
                super().save(*args, **kwargs)

    def save_only(
            self,
            force_insert=False,
            force_update=False,
            using=None,
            update_fields=None
    ):
        super().save(
            force_insert,
            force_update,
            using,
            update_fields,
        )

    def full_clean_errors(self, reraise=False, update=False):
        try:
            self.errors = {}
            self.full_clean(validate_constraints=False)
            self.__sanitize_errors_for_sys_admin()
        except ValidationError as ex:
            self.errors.update(ex.message_dict)
            self.__sanitize_errors_for_sys_admin()
            if update:
                _errors = self.errors.copy()
                for error, value in _errors.items():
                    if 'Contract with this Template name' in value[0]:
                        pass
                    elif 'exists' in value[0]:
                        self.errors.pop(error)
            if reraise and self.errors:
                ex.instance = self
                raise ex

    def __sanitize_errors_for_sys_admin(self):
        provider_id = get(compact([get(self, 'provider_id'), get(self, 'freight_provider_id')]), '0')
        if provider_id in SYSTEM_COMPANY_IDS:
            self.errors.pop('freight_provider', None)
            self.errors.pop('provider', None)

    def is_match(self, search_str, user=None):
        result = False
        search_str = search_str.lower()
        for field in self.WEB_SEARCHABLE_FIELDS:
            if not result:
                attr = deepgetattr(self, field)
                if callable(attr):
                    data = str(attr(user)).lower()
                else:
                    data = str(attr).lower()

                result = search_str in data

        return result

    @classmethod
    def ids_not_in(cls, ids):
        qs = cls.objects.filter(id__in=ids)
        if qs.count() == len(ids):
            return []
        return list(set(ids) - set(qs.values_list('id', flat=True)))

    @classmethod
    def now(cls):
        return timezone.now()

    @classmethod
    def find_by_name(cls, name):  # smart search by name
        name = strip_special(name)
        expression = CustomRegexWrapper('name').expression
        return cls.objects.annotate(_name=expression).filter(_name=name).first()


class BaseModel(RawModel):
    class Meta:
        abstract = True

    history = HistoricalRecords(inherit=True)

    @classmethod
    def get_history_events(cls, instance, relations=None, field_prefix=None):
        events = []
        histories = instance.history.order_by('-history_date')
        for history in histories:
            history_user = history.history_user
            event = {
                'history_id': history.history_id,
                'history_type': history.history_type,
                'history_date': history.history_date,
                'history_user': get(history_user, 'name'),
                'history_user_company': get(history_user, 'company.name'),
                'diff': cls.get_diff(history, field_prefix),
                'snapshot': history.instance.to_dict(),
                'entity': instance.__class__.__name__,
            }

            if hasattr(history, 'status'):
                event['status'] = history.status
            if hasattr(history, 'role'):
                event['role'] = history.role
            events.append(event)

        for relation in (relations or []):
            related_instance = get(instance, relation)
            if related_instance:
                if isinstance(related_instance, models.Manager):
                    for _related_instance in related_instance.filter():
                        events += cls.get_history_events(instance=_related_instance, field_prefix=relation)
                else:
                    events += cls.get_history_events(instance=related_instance, field_prefix=relation)

        return sorted(events, key=lambda x: x['history_date'], reverse=True)

    @classmethod
    def get_diff(cls, history, field_prefix=None):
        prev_record = history.prev_record
        diff = {}
        if prev_record:
            def get_value(record, field, default_value=None):
                for suffix in ['display_name', 'name', 'ngr_number', 'rego', 'identifier', 'username']:
                    val = get(record, f"{field}.{suffix}")
                    if val:
                        break
                return val or default_value
            delta = history.diff_against(prev_record)
            for change in delta.changes:
                field_name = field_prefix + '.' + change.field if field_prefix else change.field
                old_value = get_value(prev_record, change.field, change.old)
                new_value = get_value(history, change.field, change.new)
                if old_value != new_value:
                    diff[field_name] = {'old': old_value, 'new': new_value}
        return diff

    @staticmethod
    def get_viewer_company_ids_append_expression(company_id):
        return CombinedExpression(
            models.F('viewer_company_ids'),
            '||',
            models.Value([company_id], ArrayField(models.IntegerField()))
        )

    @staticmethod
    def _search(queryset, fields, is_staff=False, search_str=None, use_exact_match=False):
        if not search_str:
            return queryset
        search_str = decode_string(search_str)

        criteria = models.Q(id__in=[])
        matching_in = '__iexact'
        if not use_exact_match:
            matching_in = '__icontains'
        for pattern in [search_str]:
            [criteria.add(models.Q(**{f + matching_in: pattern}), models.Q.OR) for f in fields]


        if is_staff:
            criteria.add(models.Q(id__icontains=search_str), models.Q.OR)

        return queryset.filter(criteria)

    @classmethod
    def pause_history(cls):
        cls.skip_history_when_saving = True

    @classmethod
    def unpause_history(cls):
        if hasattr(cls, 'skip_history_when_saving'):
            del cls.skip_history_when_saving

    @classmethod
    def updated_last_since(cls, since, extra_filters=None):
        object_history = cls.history.filter(history_date__gte=since)
        if extra_filters:
            object_history = object_history.filter(**extra_filters)
        unique_objects_history = {}
        for obj in object_history:
            existing_object = unique_objects_history.get(obj.id, None)
            if existing_object:
                if existing_object.history_date > obj.history_date:
                    unique_objects_history[obj.id] = obj
            else:
                unique_objects_history[obj.id] = obj

        return [_history.instance for _history in unique_objects_history.values()]

    @classmethod
    def merge_entities(cls, key, merge_from_id, merge_to_id):  # pylint: disable=too-many-locals,too-many-branches
        if merge_from_id == merge_to_id:
            raise ValidationError(f'Merging {key} {merge_from_id} to {merge_to_id} is not allowed.')
        recalculate_stock = cls.__name__ in ('Storage', 'Ngr', 'Farm')
        stock_from_params = None
        for relation_klass in cls._meta._relation_tree:  # pylint: disable=protected-access
            from core.profiles.models import Employee
            model_name = relation_klass.model.__name__
            if model_name == 'Recipient':
                continue
            is_stock = model_name == 'Stock'
            if is_stock and recalculate_stock:
                continue
            is_load = model_name == 'Load'
            if relation_klass.name == 'farms' and model_name == 'Employee':
                for employee in Employee.objects.filter(farms__id=merge_from_id):
                    employee.farms.remove(merge_from_id)
                    employee.farms.add(merge_to_id)
                continue
            is_farm = cls.__name__ == 'Farm' and model_name in ('Storage', 'Ngr')
            if cls.update_when_no_unique_constriant(key, relation_klass, merge_to_id):
                to_params = {relation_klass.name: merge_to_id}
                try:
                    relation_klass.model._meta.get_field('updated_at')
                    to_params['updated_at'] = timezone.now()
                except FieldDoesNotExist:
                    pass
                from_params = {relation_klass.name: merge_from_id}
                if is_load or is_farm:
                    stock_from_params = from_params
                from core.profiles.models import EmployeeAPIRateLimit
                if relation_klass.model == EmployeeAPIRateLimit:
                    relation_klass.model.objects.filter(**from_params).delete()
                from core.alerts.models import EmployeeAlert
                if relation_klass.model == EmployeeAlert and cls.__name__ == 'Farm':
                    from core.alerts.models import EmployeeAlertSite
                    EmployeeAlertSite.objects.filter(site_id=merge_from_id).update(site_id=merge_to_id)
                elif relation_klass.model == EmployeeAlert and cls.__name__ == 'Employee' and key == 'employees':
                    EmployeeAlert.objects.filter(**from_params).delete()
                else:
                    relation_klass.model.objects.filter(**from_params).update(**to_params)
                if key == 'employees':
                    from rest_framework.authtoken.models import Token
                    Token.objects.filter(user_id__in=[merge_from_id, merge_to_id]).delete()
        if recalculate_stock and stock_from_params:
            from core.stocks.models import Stock
            Stock.recalculate_stocks(stock_from_params)

        MergedEntity.persist(merge_from_id, merge_to_id, cls.__name__)

    @classmethod
    def update_when_no_unique_constriant(cls, key, relation_klass, update_to):
        from core.profiles.models import EmployeeViewFilters, EmployeeReportPreferences
        from core.farms.models import Shrinkage, WarehouseFees, FarmAcceptanceRequest
        from rest_framework.authtoken.models import Token
        from core.companies.models import FarmDirectory
        from core.key_contacts.models import KeyContact

        unique_constraints = {
            'employees': [Token, EmployeeViewFilters, EmployeeReportPreferences],
            'farms': [FarmDirectory, Shrinkage, WarehouseFees, FarmAcceptanceRequest, KeyContact],
            'ngrs': [],
            'fields': [],
            'storages': [],
            'trucks': [],
            'farm_fields': []
        }
        if 'Historical' in relation_klass.model.__name__:
            return False

        return relation_klass.model not in unique_constraints[key] or not relation_klass.model.objects.filter(
            **{relation_klass.name: update_to}
        ).exists()


class RoundedDecimalField(models.DecimalField):
    """
    Usage: my_field = RoundedDecimalField("my field", max_digits = 6, decimal_places = 2)
    """

    def __init__(self, *args, **kwargs):
        warnings.warn(
            "RoundedDecimalField is DEPRECATED, use RoundedFloatField",
            RuntimeWarning,
        )
        super().__init__(*args, **kwargs)
        self.decimal_ctx = decimal.Context(
            prec=self.max_digits,
            rounding=decimal.ROUND_HALF_UP
        )

    def to_python(self, value):
        res = super().to_python(value)
        if not res:
            return res

        return self.decimal_ctx.create_decimal(
            res
        ).quantize(
            decimal.Decimal(
                10) ** - self.decimal_places  # pylint: disable=invalid-unary-operand-type
        )


class RoundedFloatField(models.FloatField):
    def to_python(self, value):
        val = super().to_python(value)
        try:
            val = round(val, 2)
            return val
        except TypeError as ex:
            raise exceptions.ValidationError(
                self.error_messages["invalid"],
                code="invalid",
                params={"value": val}
            ) from ex


class NoDeleteQuerySet(models.query.QuerySet):
    def delete(self):
        pass


class NoDeleteManager(models.Manager):
    def get_queryset(self):
        return NoDeleteQuerySet(self.model, using=self._db)


class MasterDataMixin(models.Model):
    class Meta:
        abstract = True

    objects = NoDeleteManager()

    def delete(self):  # pylint: disable=arguments-differ
        return False


class InstanceCustomFuncMixin:
    def __init__(self):
        self.__apply_has_attr_fn()
        self.__apply_is_status_fn()

    def __apply_has_attr_fn(self):
        def has_attr_fn(attr):
            fn_name = 'has_' + attr

            def fn(self):
                return getattr(self, attr, False)

            setattr(self.__class__, fn_name, fn)

        if getattr(self, 'FILLABLES', None):
            attrs = set(self.FILLABLES) - \
                set(getattr(self, 'CUSTOM_FUNC_EXCLUSIONS', []))
            for attr in attrs:
                has_attr_fn(attr)

    def __apply_is_status_fn(self):
        def is_status_fn(status, field_name):
            fn_name = 'is_' + status

            def fn(self):
                return getattr(self, field_name, None) == status

            setattr(self.__class__, fn_name, fn)

        fields = self._meta.get_fields()
        status_field = list(filter(lambda f: f.name == 'status', fields))[0]
        if status_field:
            choices = getattr(status_field, 'choices', [])
            for choice in choices:
                is_status_fn(choice[0], status_field.name)


class CustomPageNumberPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = PAGE_SIZE_QUERY_PARAM
    max_page_size = 100

    @staticmethod
    def replace_protocol(url):
        if settings.ENV not in ['dev', 'ci'] and url:
            url = url.replace('http:', 'https:')
        return url

    def get_paginated_response(self, data, **extras):
        return Response(self.get_paginated_response_data(data, **extras))

    def get_paginated_response_data(self, data, **extras):
        return {
            'links': {
                'first': self.get_first_link(),
                'previous': self.get_previous_link(),
                'next': self.get_next_link(),
                'last': self.get_last_link()
            },
            # django has 1 based indexing for pages while the frontend has 0 based indexing
            'page': self.page.number-1,
            'page_size': self.get_page_size(self.request),
            'count': self.page.paginator.count,
            'results': data,
            **extras
        }

    def get_previous_link(self):
        url = super().get_previous_link()
        return replace_query_param(
            self.replace_protocol(
                url), self.page_size_query_param, self.get_page_size(self.request)
        ) if url else None

    def get_next_link(self):
        url = super().get_next_link()
        return replace_query_param(
            self.replace_protocol(
                url), self.page_size_query_param, self.get_page_size(self.request)
        ) if url else None

    def get_first_link(self):
        url = self.replace_protocol(self.request.build_absolute_uri())
        url = remove_query_param(url, self.page_query_param)
        return replace_query_param(url, self.page_size_query_param, self.get_page_size(self.request))

    def get_last_link(self):
        url = self.replace_protocol(self.request.build_absolute_uri())
        url = replace_query_param(url, self.page_query_param, LAST_PAGE)
        return replace_query_param(url, self.page_size_query_param, self.get_page_size(self.request))


class PublicPageNumberPagination(CustomPageNumberPagination):
    page_size = 5
    max_page_size = 25


class BaseCommunication(BaseModel):
    class Meta:
        abstract = True

    DEFAULT_BODY = "Please refer to the attached and below documentation reflecting business conducted."

    recipients = models.JSONField(null=True, blank=True)
    subject = models.TextField(null=True, blank=True)
    body = models.TextField(null=True, blank=True, default=DEFAULT_BODY)
    footer = models.TextField(null=True, blank=True)
    resolved = models.BooleanField(default=False)
    scheduled = models.BooleanField(default=False)
    mail_status = models.JSONField(null=True, blank=True, default=dict)
    past_record = models.JSONField(null=True, blank=True, default=dict)

    def mark_resolved(self, user=None):
        self.resolved = True
        if user:
            self.updated_by = user
        self.save()

    def update_mail_status(self, user=None, is_success=True):
        mail_status = {
            'mail_sent': is_success,
            'mail_sent_at': timezone.now() if is_success else None,
        }
        self.mail_status = json.dumps(mail_status, cls=DjangoJSONEncoder)
        if user:
            self.updated_by = user
        self.save()

    @property
    def all_recipient_emails(self):
        return compact(flatten(get(self, 'recipients', {}).values()))


class HierarchyObject:
    def __init__(self, obj, user, origin=False):
        self._obj = obj
        self._user = user
        self._origin = origin

    def to_dict(self):
        return {
            'entity': self._obj.entity,
            'origin': self._origin,
            'label': self._obj.verbose_description(self._user),
            'href': self._obj.web_href,
            'total': self._obj.inferred_tonnage,
            'bars': self._obj.tonnage_distribution(self._user),
            'children': [HierarchyObject(o, self._user).to_dict() for o in self._obj.direct_descendants]
        }


class CommonFilters:
    def __init__(self, user):
        self.user = user
        self.filter_data = {}
        self._cached_filter_data = {}

    def get_filter_data(self, key):
        if key not in self._cached_filter_data:
            queryset = self.user.employee_view_filters.filter(filters__has_key=key)
            if queryset.exists():
                data = queryset.first()
                self._cached_filter_data[key] = data
            else:
                self._cached_filter_data[key] = None
        return self._cached_filter_data[key]

    def get_status_list(self, key):
        filter_data = self.get_filter_data(key)
        if filter_data:
            filter_data = dict(filter(lambda x: x[1], filter_data.filters[key].items()))
            return filter_data.get("status__in", {})
        return {}

    def conditional_filtering(self, key, company_id=None, tz=None):  # pylint: disable=too-many-return-statements, too-many-branches
        filter_data = self.get_filter_data(key)
        if filter_data:
            filters = copy.deepcopy(filter_data.filters)
            is_filters_updated = False
            for _key in filter_data.filters[key]:
                if _key.isdigit():
                    filters[key].pop(_key)
                    is_filters_updated = True
            if is_filters_updated:
                filter_data.filters = filters
                filter_data.save()
            filter_data = filter_data.filters[key]
            self.filter_data = dict(
                filter(lambda x: x[1], filter_data.items()))
            if key in ('freight_movement', 'pack_movement'):
                if not self.filter_data:
                    return None
                return self.apply_movement_specific_filters(key, tz)
            if key == 'invoice':
                return self.apply_invoice_specific_filters(tz)
            if key in ['warehouse_fee_standard', 'warehouse_fee_exception']:
                return self.filter_selected_company(filter_key='site_id', company_id=company_id)
            if key == 'contract':
                return self.apply_contract_specific_filters(tz)
            if key == 'title_transfer':
                return self.apply_title_transfer_specific_filters(tz)
            if key == 'site_loads_filters':
                return self.apply_site_loads_specific_filters(tz)
            if key == 'cog_order':
                return self.apply_cog_order_specific_filters(tz)
            if key == 'company_filters':
                return self.apply_company_specific_filters()
            if key in ['freight_order', 'request_order']:
                return self.apply_freight_and_request_orders_specific_filters(tz)
            if key == 'pack_order':
                return self.apply_pack_order_specific_filters(tz)
            if key in ['invoice_payable', 'invoice_receivable',
                       'freight_invoice_receivable', 'freight_invoice_payable']:
                return self.apply_transactable_specific_filters(key, tz)
            if key in ['active_contract_bids', 'self_contract_bids', 'archived_contract_bids']:
                return self.apply_contract_bids_specific_filters(tz)
        if key == 'contract':
            return models.Q(**self.filter_data),  # pylint: disable=trailing-comma-tuple
        else:
            return models.Q(**self.filter_data)

    def apply_invoice_specific_filters(self, tz):
        data = self.filter_data.copy()
        exclude_data = {}
        include_data = {}
        status_filter_items = get(data, 'status__in', []).copy()
        status_filter = {"status__in": []}
        self.filter_data = {}
        keys = [*INVOICE_DATE_RANGE_FILTER_KEYS]
        for key in keys:
            value = data.pop(key, None)
            if value:
                self.apply_predefined_date_range_filters('invoice', key, value, tz)
        if status_filter_items:
            for item in status_filter_items:
                if item == 'pending_payments':
                    include_data['invoicepayment__status__in'] = ['pending']
                    data['status__in'].remove(item)
                    status_filter['status__in'] = ['confirmed']

                if item == 'not_confirmed':
                    include_data['invoicepayment__status__in'] = [
                        'pending', 'rejected']
                    data['status__in'].remove(item)
                    status_filter['status__in'] = ['confirmed']

            status_filter['status__in'].extend(data.pop('status__in'))
            data = dict(filter(lambda x: x[1], data.items()))
            return ((~models.Q(**exclude_data) | models.Q(**include_data)) |
                    models.Q(**status_filter) & models.Q(**data) & models.Q(**self.filter_data))
        return models.Q(**data) & models.Q(**self.filter_data)

    def filter_selected_company(self, filter_key, company_id):
        new_filter = {}
        if get(self.filter_data, filter_key):
            if company_id in self.filter_data[filter_key].keys():
                new_filter[filter_key +
                           '__in'] = self.filter_data[filter_key][company_id]
                self.filter_data.pop(filter_key)
                new_filter = dict(
                    filter(lambda x: len(x[1]) > 0, new_filter.items()))
                return models.Q(models.Q(**self.filter_data) & models.Q(**new_filter))
        return models.Q(**self.filter_data)

    def get_load_filters_for_movement(self, key, tz, value=None):
        value = value or self.filter_data.pop(key, None)
        if value in PREDEFINED_DATE_RANGE_FILTER_FOR_NUMBER_OF_DAYS_KEYS:
            load_filters = {}
            days = get(self.get_date_range_filters_mapping(), f'{value}.days')
            modifier = get(self.get_date_range_filters_mapping(), f'{value}.modifier')
            end_range_filter = get(self.get_date_range_filters_mapping(), f'{value}.end_range_filter')
            current_date = timezone.now()
            if tz:
                current_date = datetime.now(DateTimeUtil.to_pytz_timezone(tz))
                current_date = current_date.replace(hour=0, minute=0, second=0, microsecond=0)
                current_date = DateTimeUtil.convert_to_utc(current_date)
            if modifier == 'plus':
                if value == 'today':
                    days = 1
                load_filters.update({'loads__date_time__gte': current_date.strftime("%Y-%m-%d %H:%M:%S")})
                load_filters.update(
                    {f'loads__date_time__{end_range_filter}': (
                            current_date + timedelta(days=days)
                    ).strftime("%Y-%m-%d %H:%M:%S")}
                )
            else:
                load_filters.update(
                    {'loads__date_time__gte': (current_date - timedelta(days=days)).strftime("%Y-%m-%d %H:%M:%S")}
                )
                load_filters.update(
                    {f'loads__date_time__{end_range_filter}': current_date.strftime("%Y-%m-%d %H:%M:%S")}
                )
            return load_filters
        elif value in PREDEFINED_DATE_RANGE_FILTER_FOR_TIME_PERIOD_KEYS:
            load_filters = {}
            start_date, end_date = get(self.get_date_range_filters_mapping(), value)
            if tz:
                start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)  # pylint: disable=unexpected-keyword-arg, no-value-for-parameter
                start_date = DateTimeUtil.convert_to_utc(DateTimeUtil.localize(start_date, tz))
                end_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)  # pylint: disable=unexpected-keyword-arg, no-value-for-parameter
                end_date = DateTimeUtil.convert_to_utc(DateTimeUtil.localize(end_date, tz))
            load_filters.update({'loads__date_time__gte': start_date.strftime("%Y-%m-%d %H:%M:%S")})
            load_filters.update({'loads__date_time__lte': end_date.strftime("%Y-%m-%d %H:%M:%S")})
            return load_filters

    def apply_movement_specific_filters(self, movement_type, tz):  # pylint: disable=too-many-locals,too-many-branches,too-many-statements
        planned_contract_filters = [
            'freight_pickup__date_time__gte', 'freight_pickup__date_time__lte',
            'freight_delivery__date_time__gte', 'freight_delivery__date_time__lte',
            'order__freight_container__pack_by_date__gte', 'order__freight_container__pack_by_date__lte',
            'planned_truck__isnull',
        ]

        key_map = {
            'freight_delivery__consignee__handler__id__in': [
                'inload__farm_id__in'
            ],
            'freight_pickup__consignor__handler__id__in': [
                'outload__farm_id__in',
            ],
            'planned_truck__id__in': [
                'loads__truck__id__in',
            ],
            'planned_grade__id__in': [
                'loads__grade__id__in',
            ],
            'season__in': [
                'loads__season__in',
            ],
            'freight_pickup__date_time__gte': [
                'planned_truck__isnull'
            ],
            'freight_pickup__date_time__lte': [
                'planned_truck__isnull'
            ],
            'freight_delivery__date_time__gte': [
                'planned_truck__isnull'
            ],
            'freight_delivery__date_time__lte': [
                'planned_truck__isnull'
            ]
        }

        storage_filter = [
            'freight_pickup__loads_set__farm_field_id__in',
            'freight_pickup__loads_set__storage_id__in',
            'freight_delivery__loads_set__storage_id__in',
            'freight_delivery__loads_set__farm_field_id__in'
        ]

        or_filters = {}
        count = 0
        filter_data_copy = self.filter_data.copy()
        statuses = filter_data_copy.pop('status__in', None) or []
        status_criterion = models.Q()
        is_auto_completed = 'auto_completed' in statuses
        is_auto_completed_without_generic_completed = is_auto_completed and 'completed' not in statuses
        invoiced_for = filter_data_copy.pop('invoiced_for', None) or []
        if statuses:
            if movement_type == 'pack_movement':
                if 'delivered' in statuses or 'completed' in statuses:
                    statuses = list({'delivered', 'completed', *statuses})
                if 'open' in statuses or 'confirmed' in statuses:
                    statuses = list({'open', 'confirmed', *statuses})
            statuses.remove('auto_completed') if is_auto_completed else None
            status_criterion = models.Q(status__in=statuses)
            if is_auto_completed_without_generic_completed:
                status_criterion |= models.Q(is_auto_completed=True, status='completed')
        criterion = status_criterion

        for key, value in key_map.items():
            if key in filter_data_copy:
                or_filters[key] = (filter_data_copy.pop(key), count)
                for val in value:
                    or_filters[val] = or_filters[key]
                count += 1

        outload_filters = {}
        inload_filters = {}
        for k, v in filter_data_copy.copy().items():
            if k.startswith('outload__'):
                outload_filters[k.replace('outload__', 'loads__')] = filter_data_copy.pop(k)
            if k.startswith('inload__'):
                inload_filters[k.replace('inload__', 'loads__')] = filter_data_copy.pop(k)
            if k.startswith('pack__load__'):
                value = filter_data_copy.pop(k)
                outload_filters[k.replace('pack__load__', 'loads__')] = value
                inload_filters[k.replace('pack__load__', 'loads__')] = value
        outloads_criteria = models.Q(**outload_filters, loads__type=OUTLOAD) if outload_filters else models.Q()
        inloads_criteria = models.Q(**inload_filters, loads__type=INLOAD) if inload_filters else models.Q()

        date_type_filters = filter_data_copy.pop('date_type_filters', [])
        date_range_filter = filter_data_copy.pop('date_range_filters', None)
        min_custom_date = filter_data_copy.pop('min_custom_date', None)
        max_custom_date = filter_data_copy.pop('max_custom_date', None)

        c = -1
        for key, value in or_filters.items():
            or_criteria = models.Q()
            for k, v in or_filters.items():
                if value[1] == v[1] and c != v[1]:
                    if k in planned_contract_filters:
                        if k == 'planned_truck__isnull':
                            or_criteria &= models.Q(**{k: False})
                        else:
                            or_criteria &= models.Q(**{k: v[0]})
                    elif k.startswith('outload__'):
                        or_criteria |= models.Q(**{k.replace('outload__', 'loads__'): v[0]}, loads__type=OUTLOAD)
                    elif k.startswith('inload__'):
                        or_criteria |= models.Q(**{k.replace('inload__', 'loads__'): v[0]}, loads__type=INLOAD)
                    else:
                        or_criteria |= models.Q(**{k: v[0]})
            c = value[1]
            criterion &= or_criteria

        pickup_storage_criteria = models.Q()
        delivery_storage_criteria = models.Q()

        for index, key in enumerate(storage_filter):
            if get(filter_data_copy, key):
                if index in [0, 1]:
                    if filter_data_copy[key] != []:
                        pickup_storage_criteria |= models.Q(**{key: filter_data_copy[key]})
                else:
                    if filter_data_copy[key] != []:
                        delivery_storage_criteria |= models.Q(**{key: filter_data_copy[key]})
                filter_data_copy.pop(key)

        show_missing_docket = False
        show_paused_docket = False
        sms_entry = False
        if 'show_missing_docket' in filter_data_copy:
            show_missing_docket = filter_data_copy.pop('show_missing_docket')
        if 'show_paused_docket' in filter_data_copy:
            show_paused_docket = filter_data_copy.pop('show_paused_docket')

        if 'sms_entry' in filter_data_copy:
            sms_entry = filter_data_copy.pop('sms_entry')

        show_missing_docket_criteria = models.Q()

        if show_paused_docket:
            filter_data_copy['meta__paused_docket_entry'] = True
        if show_missing_docket:
            show_missing_docket_criteria = models.Q(
                ~models.Q(
                    status__in=NULL_STATUSES
                ) &
                models.Q(
                    meta__docket_entry_needed=True
                ) & ~models.Q(
                    loads__farm__company__fill_docket=False
                ) & ~models.Q(
                    freight_pickup__consignor__handler__company__fill_docket=False
                ) & ~models.Q(
                    freight_delivery__consignee__handler__company__fill_docket=False
                ) & models.Q(meta__paused_docket_entry=bool(show_paused_docket))
            )
        sms_criteria = models.Q()
        from core.freights.models import FreightContract
        if sms_entry:
            sms_criteria = FreightContract.get_contracts_option_type_zero_criteria()
        if filter_data_copy.pop('outload_date_range', None):
            outload_filters = self.get_load_filters_for_movement('outload_date_range', tz)
            outloads_criteria = (models.Q(**outload_filters, loads__type=OUTLOAD) if outload_filters else
                                 outloads_criteria)
        if filter_data_copy.pop('inload_date_range', None):
            inload_filters = self.get_load_filters_for_movement('inload_date_range', tz)
            inloads_criteria = models.Q(**inload_filters, loads__type=INLOAD) if inload_filters else inloads_criteria
        if filter_data_copy.pop('pack_by_date_range', None):
            pack_by_date_range_filters = self.get_load_filters_for_movement('pack_by_date_range', tz)
            outloads_criteria = (models.Q(**pack_by_date_range_filters, loads__type=OUTLOAD) if
                                 pack_by_date_range_filters else outloads_criteria)
            inloads_criteria = (models.Q(**pack_by_date_range_filters, loads__type=INLOAD) if
                                pack_by_date_range_filters else inloads_criteria)

        keys = [*FREIGHT_MOVEMENT_DATE_RANGE_FILTER_KEYS]
        for index, key in enumerate(keys):
            value = filter_data_copy.pop(key, None)
            if value:
                self.apply_predefined_date_range_filters(
                    'movement', key, value, tz, filter_data_mapping=filter_data_copy
                )

        criteria = (
                models.Q(
                    models.Q(**filter_data_copy) & models.Q(criterion)
                ) & pickup_storage_criteria & delivery_storage_criteria & sms_criteria & show_missing_docket_criteria
        )
        if movement_type == 'freight_movement':
            date_or_criteria = models.Q()

            for date_type in date_type_filters:
                date_type = f'{date_type}_range'
                if date_type in ['outload_date_range', 'inload_date_range']:
                    load_type = OUTLOAD if date_type.startswith('outload') else INLOAD
                    load_filters = {}
                    if date_range_filter != 'custom':
                        load_filters = self.get_load_filters_for_movement(date_type, tz, date_range_filter)
                    elif min_custom_date and max_custom_date:
                        load_filters = {'loads__date_time__gte': min_custom_date,
                                        'loads__date_time__lte': max_custom_date}
                    date_or_criteria |= (models.Q(**load_filters, loads__type=load_type) if load_filters else
                                         models.Q())
                else:
                    filter_data_copy = {}
                    if date_range_filter != 'custom':
                        self.apply_predefined_date_range_filters(
                            'movement', date_type, date_range_filter, tz, filter_data_mapping=filter_data_copy
                        )
                    elif min_custom_date and max_custom_date:
                        checkpoint_type = 'freight_pickup' if date_type.startswith('freight_pickup') else 'freight_delivery' # pylint: disable=line-too-long
                        filter_data_copy = {f'{checkpoint_type}__date_time__gte': min_custom_date,
                                            f'{checkpoint_type}__date_time__lte': max_custom_date}
                    if filter_data_copy:
                        date_or_criteria |= models.Q(**filter_data_copy)

            final_criteria = [models.Q(criteria), models.Q(outloads_criteria),
                              models.Q(inloads_criteria), models.Q(date_or_criteria)]


            if not invoiced_for:
                return final_criteria
            criteria_method_mapping = {
                'commodity_contract': FreightContract.commodity_contract_invoice_criteria,
                'freight': FreightContract.freight_invoice_criteria,
            }
            invoicing_criteria = models.Q()
            for key, criteria_method in criteria_method_mapping.items():
                if key in invoiced_for:
                    invoicing_criteria |= criteria_method(self.user)
            final_criteria.append(models.Q(invoicing_criteria))
            return final_criteria
        return [models.Q(criteria), models.Q(outloads_criteria | inloads_criteria)]

    def apply_predefined_date_range_filters(self, entity, key, value, tz=None, filter_data_mapping=None): # pylint: disable=too-many-locals
        filter_key = get(FILTERS_KEY_MAPPING, f"{key}.{entity}.field")
        field_type = get(FILTERS_KEY_MAPPING, f"{key}.{entity}.type")
        datetime_format = "%Y-%m-%d"
        if field_type == 'datetime':
            datetime_format = "%Y-%m-%d %H:%M:%S"
        if value in PREDEFINED_DATE_RANGE_FILTER_FOR_NUMBER_OF_DAYS_KEYS:
            days = get(self.get_date_range_filters_mapping(), f'{value}.days')
            modifier = get(self.get_date_range_filters_mapping(), f'{value}.modifier')
            end_range_filter = get(self.get_date_range_filters_mapping(), f'{value}.end_range_filter')
            current_date = timezone.now()
            if field_type == 'datetime' and tz:
                current_date = datetime.now(DateTimeUtil.to_pytz_timezone(tz))
                current_date = current_date.replace(hour=0, minute=0, second=0, microsecond=0)
                current_date = DateTimeUtil.convert_to_utc(current_date)
                if value == 'today':
                    days = 1  # Add 1 day to today if field type is datetime to filter on current day's data
            if modifier == 'plus':
                new_filters = {
                    f'{filter_key}__gte': current_date.strftime(datetime_format),
                    f'{filter_key}__{end_range_filter}': (
                        current_date + timedelta(days=days)
                    ).strftime(datetime_format)
                }
            else:
                new_filters = {
                    f'{filter_key}__gte': (
                        current_date - timedelta(days=days)
                    ).strftime(datetime_format),
                    f'{filter_key}__{end_range_filter}': current_date.strftime(datetime_format)
                }

            self.filter_data.update(new_filters)
            if filter_data_mapping is not None:
                filter_data_mapping.update(new_filters)

        elif value in PREDEFINED_DATE_RANGE_FILTER_FOR_TIME_PERIOD_KEYS:
            start_date, end_date = get(self.get_date_range_filters_mapping(), value)
            if field_type == 'datetime' and tz:
                start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)  # pylint: disable=unexpected-keyword-arg, no-value-for-parameter
                start_date = DateTimeUtil.convert_to_utc(DateTimeUtil.localize(start_date, tz))
                end_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)  # pylint: disable=unexpected-keyword-arg, no-value-for-parameter
                end_date = DateTimeUtil.convert_to_utc(DateTimeUtil.localize(end_date, tz))

            new_filters = {
                f'{filter_key}__gte': start_date.strftime(datetime_format),
                f'{filter_key}__lte': end_date.strftime(datetime_format)
            }

            self.filter_data.update(new_filters)
            if filter_data_mapping is not None:
                filter_data_mapping.update(new_filters)

    def apply_contract_specific_filters(self, tz):
        consignee_criteria = models.Q()
        consignor_criteria = models.Q()
        consignee_empty_criteria = models.Q()
        consignor_empty_criteria = models.Q()
        keys = ['consignee__handler__id__in', 'consignor__handler__id__in', *CONTRACT_PREDEFINED_DATE_RANGE_FILTER_KEYS]
        for index, key in enumerate(keys):
            value = self.filter_data.pop(key, None) if key in CONTRACT_PREDEFINED_DATE_RANGE_FILTER_KEYS else None
            if get(self.filter_data, key) or value:
                if key in CONTRACT_PREDEFINED_DATE_RANGE_FILTER_KEYS:
                    self.apply_predefined_date_range_filters('contract', key, value, tz)
                elif 'None' in self.filter_data[key]:
                    self.filter_data[key].remove('None')
                    if index == 0:
                        if self.filter_data[key]:
                            consignee_criteria |= models.Q(
                                contractcommodityhandler_set__handler__id__in=self.filter_data[key],
                                contractcommodityhandler_set__role='Consignee')
                        consignee_empty_criteria = models.Q(
                            role='Consignee', contract=models.OuterRef('pk'))
                    else:
                        if self.filter_data[key]:
                            consignor_criteria |= models.Q(
                                contractcommodityhandler_set__handler__id__in=self.filter_data[key],
                                contractcommodityhandler_set__role='Consignor')
                        consignor_empty_criteria = models.Q(
                            role='Consignor', contract=models.OuterRef('pk'))
                    self.filter_data.pop(key)
                elif key == 'consignee__handler__id__in' and get(self.filter_data, key):
                    consignee_criteria = models.Q(
                        contractcommodityhandler_set__handler__id__in=self.filter_data[key],
                        contractcommodityhandler_set__role='Consignee'
                    )
                    self.filter_data.pop(key)
                elif key == 'consignor__handler__id__in' and get(self.filter_data, key):
                    consignor_criteria = models.Q(
                        contractcommodityhandler_set__handler__id__in=self.filter_data[key],
                        contractcommodityhandler_set__role='Consignor'
                    )
                    self.filter_data.pop(key)
        return models.Q(**self.filter_data), consignee_criteria, consignee_empty_criteria, consignor_criteria, consignor_empty_criteria  # pylint: disable=line-too-long

    def apply_site_loads_specific_filters(self, tz):
        data = self.filter_data.copy()
        include_data = {}
        source_type_filters = models.Q()
        self.filter_data = {}
        for key, value in data.items():
            if value:
                if key == 'created_at__lte':
                    self.filter_data.update({'date_time__lte': value})
                elif key == 'created_at__gte':
                    self.filter_data.update({'date_time__gte': value})
                elif key == 'stock_owner__in':
                    self.filter_data.update({'ngr__company_id__in': value})
                elif key == 'load_source_type__in':
                    source_type_filters = self.get_source_type_filters(value)
                elif key == 'planned_grade__id__in':
                    self.filter_data.update({'grade__id__in': value})
                    include_data['grade__id__in'] = value
                else:
                    self.filter_data.update({key: value})

        keys = [*SITE_LOADS_DATE_RANGE_FILTER_KEYS]
        for key in keys:
            value = self.filter_data.pop(key, None)
            if value:
                self.apply_predefined_date_range_filters('site_loads', key, value, tz)

        return models.Q(**self.filter_data) & source_type_filters

    def get_source_type_filters(self, source_types):
        from core.loads.models import Load
        include_filters = models.Q()
        for item in source_types:
            if item == 'freight_movements':
                include_filters |= Load.movement_all_loads_criteria()
            elif item == 'title_transfers':
                include_filters |= Load.title_transfer_loads_criteria()
            elif item == 'direct_loads':
                include_filters |= Load.direct_loads_criteria()
            elif item == 'shrinkages':
                include_filters |= Load.shrinkage_loads_criteria()
            elif item == 'stock_updates':
                include_filters |= Load.update_stock_loads_criteria()
            elif item == 'stock_empty':
                include_filters |= Load.stock_empty_loads_criteria()
            elif item == 'storage_stock_updates':
                include_filters |= Load.storage_stock_update_loads_criteria()
            elif item == 'storage_empty':
                include_filters |= Load.storage_stock_empty_loads_criteria()
            elif item == 'storage_transfers':
                include_filters |= Load.storage_transfer_loads_criteria()
            elif item == 'regrades':
                include_filters |= Load.regrade_reseason_loads_criteria()
            elif item == 'stock_swaps':
                include_filters |= Load.stock_swap_loads_criteria()
        return include_filters

    def apply_company_specific_filters(self):
        user_company = self.user.company
        approved_buyers_filter_enabled = self.filter_data.pop('approved_buyers', None)
        group_in = self.filter_data.pop('group__in', None)
        company_type_ids = self.filter_data.pop('primary_business__in', None)

        approved_buyers_criteria = models.Q()
        company_type_criteria = models.Q()
        groups_criteria = models.Q()


        if approved_buyers_filter_enabled:
            approved_buyer_ids = list(user_company.approved_buyers.values_list('id', flat=True))
            approved_buyers_criteria |= models.Q(id__in=approved_buyer_ids)

        if company_type_ids and len(company_type_ids) > 0:
            company_type_criteria |= models.Q(type_id__in=company_type_ids)
        if group_in:
            from core.companies.models import CompanyGroup
            group = CompanyGroup.objects.get(id=group_in)
            groups_criteria |= models.Q(groups=group)

        return models.Q(**self.filter_data) & approved_buyers_criteria & company_type_criteria & groups_criteria

    def apply_contract_bids_specific_filters(self, tz):
        keys = [*CONTRACT_BIDS_DATE_RANGE_FILTER_KEYS]
        for key in keys:
            value = self.filter_data.pop(key, None)
            if value:
                self.apply_predefined_date_range_filters('contract_bids', key, value, tz)

        return models.Q(**self.filter_data)

    def apply_title_transfer_specific_filters(self, tz):
        from core.farms.models import Storage

        types = self.filter_data.pop('type__in', None)
        sites = self.filter_data.pop('site__id__in', None)
        types_criteria = models.Q()
        site_criteria = models.Q()

        if types and len(types) > 0:
            for val in types:
                if val == 'cashed_transfers':
                    types_criteria |= models.Q(
                        commodity_contract__cash_price__isnull=False
                    )
                elif val == 'contract':
                    types_criteria |= models.Q(
                        commodity_contract__isnull=False, commodity_contract__cash_price__isnull=True)
                elif val == 'external':
                    types_criteria |= models.Q(
                        commodity_contract__isnull=True)
        if sites and len(sites) > 0:
            storage_ids = Storage.objects.filter(
                farm_id__in=sites).values_list('id', flat=True)
            site_criteria |= models.Q(storage_id__in=storage_ids)

        keys = [*TITLE_TRANSFER_DATE_RANGE_FILTER_KEYS]
        for key in keys:
            value = self.filter_data.pop(key, None)
            if value:
                self.apply_predefined_date_range_filters('title_transfer', key, value, tz)

        return models.Q(**self.filter_data) & types_criteria & site_criteria

    def filter_to_csv_row(self, csv_header, statuses=None, tz=None):  # pylint: disable=too-many-branches
        filter_csv_row = len(csv_header) * ['']
        for i in self.filter_data:  # pylint: disable=consider-using-dict-items
            if not FILTER_MAPPINGS.get(i, False):
                continue

            if csv_header.count(FILTER_MAPPINGS[i].get('name', '')) == 0:
                continue

            index = csv_header.index(FILTER_MAPPINGS[i].get('name', ''))

            if FILTER_MAPPINGS[i].get('model', False) and FILTER_MAPPINGS[i].get('app', False):
                values = apps.get_model(FILTER_MAPPINGS[i]['app'], FILTER_MAPPINGS[i]['model']).\
                    objects.filter(id__in=self.filter_data[i]).values_list(
                        FILTER_MAPPINGS[i].get('property', 'name'), flat=True)
                values = " / ".join(values)
            elif i == 'status__in':
                values = list(set(map(lambda x: dict(statuses)
                                  [x], self.filter_data[i])))
                values = " / ".join(values)
            elif i in ('updated_at__lte', 'updated_at__gte'):
                if self.filter_data.get('updated_at__gte') and not self.filter_data.get('updated_at__lte'):
                    values = self.filter_data['updated_at__gte']
                if self.filter_data.get('updated_at__lte') and not self.filter_data.get('updated_at__gte'):
                    values = self.filter_data['updated_at__lte']
                if self.filter_data.get('updated_at__lte') and self.filter_data.get('updated_at__gte'):
                    values = self.filter_data['updated_at__gte'] + \
                        " to " + self.filter_data['updated_at__lte']
            elif i == 'type_id__in':
                type_ids = self.filter_data['type_id__in']
                values = [REQUEST_ORDER_TYPES_MAPPING.get(id) for id in type_ids if id in REQUEST_ORDER_TYPES_MAPPING]
            elif i in ('freight_pickup__date_time__gte', 'freight_delivery__date_time__lte'):
                values = self.filter_data[i]
                if tz:
                    values = DateTimeUtil.localize_date(values, tz)
            elif i == 'payment_due_date__gte':
                values = 'After ' + self.filter_data[i]
            elif i == 'payment_due_date__lte':
                values = 'Before ' + self.filter_data[i]
            else:
                values = self.filter_data[i]
                values = " / ".join(values)

            if i == 'contract__commodity__id__in':
                filter_csv_row[index] += f"Commodity - {values}"
            elif i in ('payment_due_date__gte', 'payment_due_date__lte'):
                filter_csv_row[index] += f" {csv_header[index]} - {values}"
            else:
                filter_csv_row[index] = f"{csv_header[index]} - {values}"
        return filter_csv_row

    def apply_cog_order_specific_filters(self, tz):
        buyer_ids = self.filter_data.pop('commodity_contract__buyer__company__id__in', None)
        seller_ids = self.filter_data.pop('commodity_contract__seller__company__id__in', None)
        buyer_criteria = models.Q(commodity_contract__buyer__company__id__in=buyer_ids) | \
                         models.Q(buyer__company__id__in=buyer_ids) if buyer_ids else models.Q()
        seller_criteria = models.Q(commodity_contract__seller__company__id__in=seller_ids) | \
                          models.Q(seller__company__id__in=seller_ids) if seller_ids else models.Q()
        self.apply_orders_specific_filters(tz)

        return models.Q(**self.filter_data) & buyer_criteria & seller_criteria

    def apply_freight_and_request_orders_specific_filters(self, tz):
        self.apply_orders_specific_filters(tz)
        return models.Q(**self.filter_data)

    def apply_orders_specific_filters(self, tz):
        keys = [*ORDER_PREDEFINED_DATE_RANGE_FILTER_KEYS]
        for key in keys:
            value = self.filter_data.pop(key, None)
            if value:
                self.apply_predefined_date_range_filters('order', key, value, tz)

    def apply_pack_order_specific_filters(self, tz):
        keys = [*PACK_ORDER_PREDEFINED_DATE_RANGE_FILTER_KEYS]
        for key in keys:
            value = self.filter_data.pop(key, None)
            if value:
                self.apply_predefined_date_range_filters('pack_order', key, value, tz)
        return models.Q(**self.filter_data)

    def apply_transactable_specific_filters(self, transactable_type, tz):
        keys = [*INVOICE_DATE_RANGE_FILTER_KEYS]
        for key in keys:
            value = self.filter_data.pop(key, None)
            if value:
                self.apply_predefined_date_range_filters(transactable_type, key, value, tz)
        return models.Q(**self.filter_data)

    @staticmethod
    def get_date_range_filters_mapping():
        return {
            'today': {'days': 0, 'modifier': 'plus', 'end_range_filter': 'lte'},
            'yesterday': {'days': 1, 'modifier': 'minus', 'end_range_filter': 'lt'},
            'last_7_days': {'days': 7, 'modifier': 'minus', 'end_range_filter': 'lte'},
            'last_10_days': {'days': 10, 'modifier': 'minus', 'end_range_filter': 'lte'},
            'last_15_days': {'days': 15, 'modifier': 'minus', 'end_range_filter': 'lte'},
            'last_30_days': {'days': 30, 'modifier': 'minus', 'end_range_filter': 'lte'},
            'current_week': DateTimeUtil.get_current_week_start_and_end_date(),
            'current_month': DateTimeUtil.get_current_month_start_and_end_date(),
            'current_quarter': DateTimeUtil.get_current_quarter_start_and_end_date(),
            'current_year': DateTimeUtil.get_current_year_start_and_end_date(),
            'last_week': DateTimeUtil.get_last_week_start_and_end_date(),
            'last_month': DateTimeUtil.get_last_month_start_and_end_date(),
            'last_quarter': DateTimeUtil.get_last_quarter_start_and_end_date(),
            'last_year': DateTimeUtil.get_last_year_start_and_end_date(),
        }


class DeletedRecords(models.Model):
    class Meta:
        db_table = "deleted_records"

    created_at = models.DateTimeField(default=timezone.now)
    entity_type = models.CharField(max_length=50)
    entity_id = models.IntegerField()


class MergedEntity(models.Model):
    class Meta:
        db_table = "merged_entities"

    from_id = models.IntegerField()
    to_id = models.IntegerField()
    entity_model_name = models.CharField(max_length=100)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    created_by = models.ForeignKey(
        'profiles.Employee', default=AU_ROOT_USER_ID, on_delete=models.SET_DEFAULT,
        related_name='merge_created_by_set'
    )
    updated_by = models.ForeignKey(
        'profiles.Employee', default=AU_ROOT_USER_ID, on_delete=models.SET_DEFAULT,
        related_name='merge_updated_by_set'
    )

    @classmethod
    def persist(cls, from_entity_id, to_entity_id, entity_model_name):
        return cls(from_id=from_entity_id, to_id=to_entity_id, entity_model_name=entity_model_name).save()

    @classmethod
    def get_to_id_by_from_id(cls, from_id, entity_model_name):
        visited = set()
        while from_id is not None and from_id not in visited:
            visited.add(from_id)
            next_entity = get(cls.get_by_from_id(from_id, entity_model_name), 'to_id')
            if next_entity is None:
                break
            from_id = next_entity
        return from_id

    @classmethod
    def get_by_from_id(cls, from_id, entity_model_name):
        return cls.objects.filter(from_id=from_id, entity_model_name__iexact=entity_model_name).first()

class Number:
    _CONTEXT = Context(prec=60)

    @staticmethod
    def decimal_context(func):
        def wrapper(*args, **kwargs):
            with localcontext() as ctx:
                ctx.prec = Number._CONTEXT.prec
                return func(*args, **kwargs)
        return wrapper

    @staticmethod
    @decimal_context
    def add(*args):
        if not args:
            return 0.0

        result = sum(Decimal(str(arg)) for arg in args)

        return float(result)

    @staticmethod
    def sum(numbers):
        return Number.add(*numbers)

    @staticmethod
    @decimal_context
    def subtract(a, b):
        dec_a = Decimal(str(a))
        dec_b = Decimal(str(b))

        result = dec_a - dec_b

        return float(result)

    @staticmethod
    @decimal_context
    def multiply(*args):
        if not args:
            return 0.0

        result = Decimal('1')

        for arg in args:
            result *= Decimal(str(arg))

        return float(result)

    @staticmethod
    @decimal_context
    def divide(a, b):
        dec_a = Decimal(str(a))
        dec_b = Decimal(str(b))

        if dec_b == 0:
            return float('nan')

        result = dec_a / dec_b

        return float(result)
