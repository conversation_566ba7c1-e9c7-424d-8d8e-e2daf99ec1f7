from django.dispatch import receiver

from core.audit_history.models import AuditHistory
from core.common.constants import AU_SYSTEM_COMPANY_ID

from core.cash_board.signals import cash_price_audit


@receiver(cash_price_audit)
def _cash_price_audit(description_func, instance, **kwargs):
    description = description_func()
    created_by = kwargs.get('created_by')
    acceptance_request_id = kwargs.get('acceptance_request_id')
    if not created_by:
        created_by = AU_SYSTEM_COMPANY_ID  # default admin support
    AuditHistory.create_record(description, created_by, instance, {'acceptance_request_id': acceptance_request_id})
