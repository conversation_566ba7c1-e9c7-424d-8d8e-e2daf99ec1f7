# Generated by Django 4.2.15 on 2024-09-24 05:26

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0216_auto_20240917_0726'),
    ]

    operations = [
        migrations.RunSQL('CREATE INDEX idx_tt_identifier_trgm ON title_transfers USING gin(upper(identifier) gin_trgm_ops);'),
        migrations.RunSQL('CREATE INDEX idx_tt_status_trgm ON title_transfers USING gin(upper(status) gin_trgm_ops);'),
        migrations.RunSQL('CREATE INDEX idx_tt_season_trgm ON title_transfers USING gin(upper(season) gin_trgm_ops);'),
        migrations.RunSQL('CREATE INDEX idx_tt_ext_ref_num_trgm ON title_transfers USING gin(upper(external_reference_number) gin_trgm_ops);'),
        migrations.RunSQL('CREATE INDEX idx_tt_ref_num_trgm ON title_transfers USING gin(upper(reference_number) gin_trgm_ops);'),
        migrations.RunSQL('CREATE INDEX idx_cc_identifier_trgm ON contracts USING gin(upper(identifier) gin_trgm_ops);'),
        migrations.RunSQL('CREATE INDEX idx_cc_num_trgm ON contracts USING gin(upper(contract_number) gin_trgm_ops);'),
        migrations.RunSQL('CREATE INDEX idx_cc_status_trgm ON contracts USING gin(upper(status) gin_trgm_ops);'),
        migrations.RunSQL('CREATE INDEX idx_cc_season_trgm ON contracts USING gin(upper(season) gin_trgm_ops);'),
    ]
