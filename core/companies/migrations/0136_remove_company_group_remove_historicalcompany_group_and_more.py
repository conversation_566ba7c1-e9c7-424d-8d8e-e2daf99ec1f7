# Generated by Django 4.1.9 on 2023-06-29 03:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0135_company_group_historicalcompany_group'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='company',
            name='group',
        ),
        migrations.RemoveField(
            model_name='historicalcompany',
            name='group',
        ),
        migrations.AddField(
            model_name='company',
            name='groups',
            field=models.ManyToManyField(related_name='groups', to='companies.companygroup'),
        ),
    ]
