# Generated by Django 4.1.10 on 2023-09-05 05:52

from django.db import migrations
from bulk_update.helper import bulk_update


def add_trailers_to_site_management_settings_default_fields(apps, schema_editor):
    SiteManagementSettings = apps.get_model("company_sites", "SiteManagementSettings")
    settings = SiteManagementSettings.objects.all()
    for setting in settings:
        setting.fields.append({
            "show": True,
            "defaultValue": None,
            "mandatory": False,
            "label": "Trailer 1",
            "name": "Trailer 1",
            "customisable": True,
            "id": "trailer_1"
        })
        setting.fields.append({
            "show": True,
            "defaultValue": None,
            "mandatory": False,
            "label": "Trailer 2",
            "name": "Trailer 2",
            "customisable": True,
            "id": "trailer_2"
        })
        setting.fields.append({
            "show": True,
            "defaultValue": None,
            "mandatory": False,
            "label": "Trailer 3",
            "name": "Trailer 3",
            "customisable": True,
            "id": "trailer_3"
        })
    bulk_update(settings, update_fields=['fields'], batch_size=1000)


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0131_remove_freightslot_rego_search_option_and_more'),
    ]

    operations = [
        migrations.RunPython(add_trailers_to_site_management_settings_default_fields)
    ]
