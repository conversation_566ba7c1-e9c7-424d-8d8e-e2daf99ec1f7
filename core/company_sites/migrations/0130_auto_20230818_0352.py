# Generated by Django 4.1.10 on 2023-08-18 03:52

from django.db import migrations


def update_sm_settings_tooltip(apps, schema_editor):
    SiteManagementSettings = apps.get_model('company_sites', 'SiteManagementSettings')
    for setting in SiteManagementSettings.objects.filter(tooltip_order__icontains='subfreightprovider'):
        setting.tooltip_order = list(set([
            'truckOwner' if tooltip == 'subFreightProvider' else tooltip for tooltip in setting.tooltip_order
        ]))
        setting.save()
    for setting in SiteManagementSettings.objects.filter(title_order__icontains='subfreightprovider'):
        setting.title_order = list(set([
            'truckOwner' if title == 'subFreightProvider' else title for title in setting.title_order
        ]))
        setting.save()


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0129_remove_sitemanagementsettings_subcontractors_source'),
    ]

    operations = [
        migrations.RunPython(update_sm_settings_tooltip)
    ]
