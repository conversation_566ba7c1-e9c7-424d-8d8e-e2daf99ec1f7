# Generated by Django 2.1.7 on 2019-06-10 13:19

import core.company_sites.models
from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0042_auto_20190527_0941'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitemanagementsettings',
            name='tooltip_order',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=100), default=core.company_sites.models.default_tooltip_order, size=None),
        ),
    ]
