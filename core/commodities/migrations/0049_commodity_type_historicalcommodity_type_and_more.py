# Generated by Django 4.1.13 on 2024-04-01 08:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0048_alter_commodity_unit_alter_historicalcommodity_unit'),
    ]

    operations = [
        migrations.AddField(
            model_name='commodity',
            name='type',
            field=models.CharField(choices=[('grain', 'Grain'), ('chemical', 'Chemical')], default='commodity', max_length=100),
        ),
        migrations.AddField(
            model_name='historicalcommodity',
            name='type',
            field=models.CharField(choices=[('grain', 'Grain'), ('chemical', 'Chemical')], default='commodity', max_length=100),
        ),
        migrations.AlterField(
            model_name='commodity',
            name='unit',
            field=models.CharField(choices=[('MT', 'MT'), ('Bales', 'Bales'), ('m³', 'm³'), ('BU', 'BU'), ('LB', 'LB'), ('CWT', 'CWT'), ('kg', 'kg'), ('l', 'l')], default='MT', max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalcommodity',
            name='unit',
            field=models.CharField(choices=[('MT', 'MT'), ('Bales', 'Bales'), ('m³', 'm³'), ('BU', 'BU'), ('LB', 'LB'), ('CWT', 'CWT'), ('kg', 'kg'), ('l', 'l')], default='MT', max_length=100),
        ),
    ]
