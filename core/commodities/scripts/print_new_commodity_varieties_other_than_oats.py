import csv
import json
import sys

res = []
count = int(sys.argv[-1])
commodity_id = int(sys.argv[-2])
csvfile = open(sys.argv[-3], 'r')
fieldnames = ("Variety")
reader = csv.DictReader(csvfile)
for i, row in enumerate(reader):
    _dict = dict(row)
    _json = {
        "pk": count,
        "model": "commodities.variety",
        "fields": {
            "name": _dict["Variety"],
            "details": {},
            "commodity_id": commodity_id,
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "created_by": 1,
            "updated_by": 1,
        },
    }
    count += 1
    res.append(_json)

print(len(res))
print(json.dumps(res))
