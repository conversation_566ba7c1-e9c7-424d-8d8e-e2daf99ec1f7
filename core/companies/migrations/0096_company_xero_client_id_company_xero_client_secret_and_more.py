# Generated by Django 4.0.6 on 2022-09-09 04:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0095_xeroconnection'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='xero_client_id',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='xero_client_secret',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='xero_tenant_id',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='xero_client_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='xero_client_secret',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='historicalcompany',
            name='xero_tenant_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
