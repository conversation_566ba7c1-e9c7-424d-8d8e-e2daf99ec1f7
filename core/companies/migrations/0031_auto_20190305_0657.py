# Generated by Django 2.1.5 on 2019-03-05 06:57

import django.contrib.postgres.fields.jsonb
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0030_auto_20190305_0655'),
    ]

    operations = [
        migrations.AddField(
            model_name='companysetup',
            name='questions',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompanysetup',
            name='questions',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True),
        ),
    ]
