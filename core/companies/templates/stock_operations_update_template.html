{% load static %}
{% load app_filters %}
{% with date_time_format="M d Y, h:i A" date_format="M d, Y" %}
<html>
<head>
    <meta charset="UTF-8">
    <style>
        table {
            border-collapse: collapse;
            width: 60%;
        }

        th, td {
            padding: 4px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        tr:hover {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <p>Hi,</p>
    <p>We would like to inform you that a stock operation has been performed at site <strong>{{site_name}}</strong> by <strong>{{updated_by}}</strong> on the AgriChain platform. Please see the details below:</p>
    <p><strong>Transaction Details:</strong></p>
    <ul>
        <li><strong>Load Identifier: </strong>{{load_identifier}}</li>
        <li><strong>Type: </strong>{{operation_type}}</li>
        <li><strong>Date: </strong>{{updated_at}}</li>
        {% if show_ngr %}
            <li><strong>NGR: </strong>{{stock_owner_ngr}}</li>
        {% endif %}
        {% if show_commodity_and_storage %}
            <li><strong>Commodity: </strong>{{commodity_name}}</li>
            <li><strong>Storage: </strong>{{storage_name}}</li>
        {% endif %}
        <li><strong>Grade: </strong>{{grade_name}}</li>
        <li><strong>Season: </strong>{{season_name}}</li>
    </ul>
    <p><strong>Modification Details:</strong> {{modification_details}}</p>
    <br/>
    {% if show_changes_table and updated_details.items %}
        <table border="1">
        <tr>
            <th>Field</th>
            <th>Current Value</th>
            <th>Updated Value</th>
        </tr>
        {% for field, values in updated_details.items %}
        <tr>
            <td>{{ field | capfirst }}</td>
            <td>{{ values.existing }}</td>
            <td>{{ values.updated }}</td>
        </tr>
        {% endfor %}
        </table>
    {% endif %}
    {% if show_value_table and updated_details %}
        <table border="1">
        <tr>
            <th>Field</th>
            <th>Value</th>
        </tr>
        {% for field, values in updated_details %}
        <tr>
            <td>{{ field | capfirst }}</td>
            <td>{{ values }}</td>
        </tr>
        {% endfor %}
        </table>
    {% endif %}
    <br/>
    Have a great day!
    <br/>
    <br/>
    Thank you<br/>
    </p>
  </body>
</body>
</html>
{% endwith %}
