# Generated by Django 2.2.20 on 2021-07-28 06:18

import core.validation.validators
import dirtyfields.dirtyfields
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import simple_history.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('farms', '0082_auto_20210714_1907'),
        ('contracts', '0148_auto_20210614_0839'),
        ('commodities', '0020_auto_20190917_0442'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalCashPrices',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('season', models.CharField(blank=True, max_length=5, null=True, validators=[core.validation.validators.season_validator])),
                ('price', models.IntegerField()),
                ('limit', models.IntegerField()),
                ('track', models.CharField(blank=True, max_length=255, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('buyer', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='contracts.Party')),
                ('commodity', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='commodities.Commodity')),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='cash_board_cashpricess_created_by', to=settings.AUTH_USER_MODEL)),
                ('grade', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='commodities.Grade')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('payment_term', models.ForeignKey(blank=True, db_constraint=False, default=10, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='contracts.PaymentTerm')),
                ('site', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='farms.Farm')),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='cash_board_cashpricess_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical cash prices',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='CashPrices',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('season', models.CharField(blank=True, max_length=5, null=True, validators=[core.validation.validators.season_validator])),
                ('price', models.IntegerField()),
                ('limit', models.IntegerField()),
                ('track', models.CharField(blank=True, max_length=255, null=True)),
                ('buyer', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='cash_prices_buyer', to='contracts.Party')),
                ('commodity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='commodities.Commodity')),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='cash_board_cashprices_related_created_by', related_query_name='cash_board_cashpricess_created_by', to=settings.AUTH_USER_MODEL)),
                ('grade', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='commodities.Grade')),
                ('payment_term', models.ForeignKey(default=10, on_delete=django.db.models.deletion.DO_NOTHING, to='contracts.PaymentTerm')),
                ('site', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='farms.Farm')),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='cash_board_cashprices_related_updated_by', related_query_name='cash_board_cashpricess_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'cash_prices',
            },
            bases=(dirtyfields.dirtyfields.DirtyFieldsMixin, models.Model),
        ),
    ]
