from datetime import datetime

from django import template
from pydash import get

from core.common.constants import AEST_TZ, AWST_TZ
from core.contracts.constants import CONSIGNOR_MANDATORY_PRICE_POINTS, CONSIGNEE_MANDATORY_PRICE_POINTS
from core.common.utils import get_request_tz, to_currency
from core.countries.models import Country
from core.timezones.utils import DateTimeUtil

register = template.Library()


@register.filter(name='str_to_date')
def str_to_date(value, date_format):
    return datetime.strptime(value, date_format).date() if value else value

@register.filter(name='date_timezone')
def date_timezone(value):
    tz = get_request_tz() or AEST_TZ
    return DateTimeUtil.localize_date(value, tz, Country.get_country_format("date_display"))


@register.filter(name='localize_tz')
def localize_tz(value, args):
    fmt, tz = args.split('|') if '|' in args else (args, None)
    tz = get_request_tz() or tz or Country.get_requesting_country().timezone
    return DateTimeUtil.localize_date(value, tz, fmt)


@register.filter(name='aest_date_format')
def aest_date_format(value):
    return DateTimeUtil.localize_date(value, AEST_TZ, '%b %d, %Y') if value else value


@register.filter(name='awst_date_format')
def awst_date_format(value):
    return DateTimeUtil.localize_date(value, AWST_TZ, '%b %d, %Y') if value else value


@register.filter(name='aest_time_format')
def aest_time_format(value):
    return DateTimeUtil.localize_time(value, AEST_TZ) if value else value


@register.filter(name='format_price')
def format_price(value, currency=None):
    currency = currency or Country.get_requesting_country().currency
    if not isinstance(value, (int, float)):
        return f'{currency} {str(value)}' \
            if value is not None and '$' not in value and '%' not in value and value != 'N/A' else value
    return to_currency(value, True, currency)

@register.filter(name='humanise_payment_term')
def humanise_payment_term(payment_term):
    return payment_term.get_name_display()

@register.filter(name='human_price')
def human_price(value):
    return to_currency(value) if value and isinstance(value, (int, float, str)) else value


@register.filter(name='to_quantity_unit')
def to_quantity_unit(value, quantity_unit=None):
    if quantity_unit:
        return value and value.replace('mt', quantity_unit).replace('MT', quantity_unit)
    return value


@register.filter(name='mt_to_country_display_unit')
def mt_to_country_display_unit(value, country_code=None):
    country = Country.get_requesting_country(country_code)
    to_unit = get(country, 'config.display_unit')
    return value.replace('mt', to_unit).replace('MT', to_unit) if to_unit and value else value


@register.filter(name='to_country_label')
def to_country_label(key, country_code=None):
    country = Country.get_requesting_country(country_code)
    return country.get_label(key)


@register.filter(name='to_country_config_value')
def to_country_config_value(key, country_code=None):
    country = Country.get_requesting_country(country_code)
    return get(country.config, key)


@register.filter(name='country_check')
def country_check(key, country_code=None):
    country = Country.get_requesting_country(country_code)
    return get(country.config, key)


@register.filter(name='is_visible_for_country')
def is_visible_for_country(key, country_code=None):
    country = Country.get_requesting_country(country_code)
    return not country.is_hidden(key)


@register.filter(name='to_country_phone_format')
def to_country_phone_format(value, country_code=None):
    country = Country.get_requesting_country(country_code)
    return country.to_phone_format(value)


@register.filter(name='to_country_bank_account_format')
def to_country_bank_account_format(bank_account, country_code=None):
    country = Country.get_requesting_country(country_code)
    return country.to_bank_account_format(bank_account)


@register.filter(name='to_country_branch_number_format')
def to_country_branch_number_format(value, country_code=None):
    country = Country.get_requesting_country(country_code)
    return country.to_branch_number_format(value)

@register.filter(name='mandatory_price_points')
def mandatory_price_points(_value, checkpoint_type=None):
    return (
        CONSIGNOR_MANDATORY_PRICE_POINTS if checkpoint_type == "pickup" else CONSIGNEE_MANDATORY_PRICE_POINTS
    )
