from pydash import get
from rest_framework import serializers


class HistoryEventSerializer(serializers.Serializer):  # pylint: disable=abstract-method
    history_id = serializers.IntegerField()
    history_type = serializers.CharField()
    history_date = serializers.DateTimeField()
    history_user = serializers.CharField()
    history_user_company = serializers.CharField()
    diff = serializers.JSONField()
    entity = serializers.CharField()
    snapshot = serializers.JSONField()

    class Meta:
        fields = (
            'history_id', 'history_type', 'history_date', 'history_user', 'history_user_company', 'diff', 'snapshot'
        )

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if not get(instance, 'status'):
            data.pop('status', None)
        if not get(instance, 'role'):
            data.pop('role', None)
        return data
