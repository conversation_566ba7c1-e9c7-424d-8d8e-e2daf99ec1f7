from django.core.management import BaseCommand

from core.alerts.constants import STOCK_OPERATIONS_UPDATE_ALERT_CODE
from core.alerts.models import <PERSON>ert
from core.farms.models import Farm


class Command(BaseCommand):
    help = 'Add alert for company sites having stocks management ON'

    def handle(self, *args, **options):
        company_ids = Farm.objects.filter(stocks_management=True).values_list('company_id', flat=True)
        for company_id in set(company_ids):
            employee_roles = ["company_admin"]
            employees = []
            if stock_operation_update_alert := Alert.objects.filter(company_id=company_id,
                    name=STOCK_OPERATIONS_UPDATE_ALERT_CODE).first():
                recipient = stock_operation_update_alert.recipient_set.filter(party='own_company').first()
                if recipient:
                    employee_roles = recipient.employee_roles
                    employees = list(recipient.employees.values_list('id', flat=True))
            alert_payload = {
                "name": "stock_auto_update_alert",
                "channel": "email",
                "recipients": [
                    {
                        "party": "own_company",
                        "employee_roles": employee_roles,
                        "employees": employees
                    }
                ],
                "company_id": company_id,
            }
            Alert.persist(alert_payload)
