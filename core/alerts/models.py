import csv
import datetime
import io
import time

from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.core.mail import EmailMessage
from django.db import models
from django.template.loader import render_to_string
from django.utils import timezone
from pydash import get, compact, flatten

from core.alerts.constants import (CHANNEL_TYPES, RECIPIENT_PARTY_TYPES, ALERT_FREQUENCY, ALERT_DESCRIPTION,
    EMAIL_CHANNEL, SMS_CHANNEL, ALERT_NAMES, SPEC_AVG_REPORT_ALERT_CODE,
    MASS_LIMIT_BREACH_ALERT_CODE, DELIVERY_CONFIRMATION_SMS_ALERT_CODE,
    PIC<PERSON><PERSON>_CONFIRMATION_SMS_ALERT_CODE, CLASS_1_MATERIAL_ALERT_CODE,
    PICKUP_CONFIRMATION_ALERT_CODE, DELIVERY_CONFIRMATION_ALERT_CODE,
    OUTLOAD_REPORT_ALERT_CODE, SLOT_BOOKING_UPDATE_ALERT_CODE,
    TRUCK_CLEANLINESS_DECLARATION_NOT_FILLED_ALERT_CODE, MISSING_DOCKET_ALERT_CODE,
    DOCKET_CLARIFICATION_ALERT_CODE, MISSING_DOCKET_PICKUP_SITE_ALERT_CODE,
    STOCK_OPERATIONS_UPDATE_ALERT_CODE, STOCK_ALERT_DATA_LIMIT,
    ACQUISITION_REPORT_ALERT_CODE, NEW_CASH_PRICE_FOR_MY_STOCKS_ALERT_CODE,
    LEADING_CASH_PRICE_WARNING_ALERT_CODE, MOBILE_PUSH_NOTIFICATION_CHANNEL,
    BULK_INVOICE_ALERT_CODE, STOCK_AUTO_UPDATE_ALERT_CODE, RECEIVE_ALERT_DESCRIPTION,
    RECEIVE_ALERT_NAMES, RECEIVE_ALERT_ROLES
)
from core.common.constants import (FARM_ADMIN_TYPE_ID, COMPANY_ADMIN_TYPE_ID, SUPPORT_EMAIL,
                                   FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME, AEST_AEDT_TZ,
                                   OFFICE_ADMIN_TYPE_ID, OFFICE_EMPLOYEE_TYPE_ID, FARM_EMPLOYEE_TYPE,
                                   STATIC_URL_PREFIX, DRIVER_TYPE_ID, OUTLOAD, INLOAD, AWST_TZ)
from core.common.models import BaseModel
from core.common.utils import (to_display_attr, convert_image_to_mime_type,
                               get_request_country_code, get_filename_with_timestamp)
from core.companies.models import Company
from core.countries.models import Country
from core.devices.constants import NOTIFICATION_TYPE_NEW_CASH_PRICE_FOR_STOCKS, NOTIFICATION_TYPE_CASH_PRICE, \
    NOTIFICATION_TYPE_LEADING_CASH_PRICE_WARNING
from core.freights.constants import (DOCKET_CLARIFICATION_CSV_HEADERS, MISSING_DOCKET_SMS_EMAIL_HEADERS,
                                     MISSING_DOCKET_SMS_FOR_SITE_EMAIL_HEADERS, VOID_STATUS)
from core.loads.constants import (DIRECT_LOAD, REGRADED_LOAD, STORAGE_TRANSFER_LOAD, STOCK_SWAP_LOAD, STOCK_UPDATE_LOAD,
                                  STORAGE_STOCK_UPDATE_LOAD, STORAGE_STOCK_EMPTY_LOAD, STOCK_EMPTY_LOAD)
from core.mobile_messages.constants import MESSAGE_TYPE_INLOAD_DONE, MESSAGE_TYPE_OUTLOAD_DONE
from core.mobile_messages.models import MobileMessage
from core.services.internal.pdf import HTMLToPDF
from core.timezones.utils import DateTimeUtil

KEY_CONTACT_ROLE = 'key_contact'


class AbstractAlert:
    def __init__(self, alert, **kwargs):
        self.alert = alert
        self.kwargs = kwargs
        self._recipients = None
        self.data = None
        self.service = None
        self.country = kwargs.get('country', None) or get_request_country_code()

    def process(self):
        raise NotImplementedError

    def make_service(self, _):
        raise NotImplementedError

    def send(self, _):
        raise NotImplementedError

    def make_data(self):
        pass

    @property
    def company(self):
        return self.alert.company

    @property
    def timezone(self):
        return self.company.country.timezone

    @property
    def recipients(self):
        if self._recipients is None:
            self._recipients = self.get_recipients()
        return self._recipients

    def get_recipients(self):
        return self.alert.get_recipients(self.company)

class EmailAlert(AbstractAlert):
    template = None
    FROM_EMAIL = FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME
    CC = None
    REPLY_TO = [SUPPORT_EMAIL]
    CONTENT_SUBTYPE = 'html'
    subject = None

    def get_template_args(self):
        return {
            'country_code': self.country,
        }

    def get_subject(self):
        return self.subject

    def get_body(self):
        return render_to_string(self.template, self.get_template_args())

    def process(self):
        from core.profiles.models import Employee
        if get(self.alert, 'name') in [OUTLOAD_REPORT_ALERT_CODE, SPEC_AVG_REPORT_ALERT_CODE] and self.alert.siterecipient_set.exists(): # pylint: disable=line-too-long
            site_groups = SiteRecipient.group_employees_based_on_site(self.alert)
            for farm_ids, employee_ids in site_groups.items():
                self.make_data(list(farm_ids))  # pylint: disable=too-many-function-args
                emails = compact(Employee.objects.filter(id__in=employee_ids).values_list('email', flat=True))
                if emails:
                    self.farm_names = ', '.join(self.alert.company.farm_set.filter(
                        id__in=list(farm_ids)).values_list('name', flat=True))
                    self.send(emails)
        else:
            self.make_data()
            self.send(self.recipients)

    def send(self, emails):
        if not emails:
            return
        self.make_service(emails)
        self.attach()
        try:
            return self.service.send()
        except:  # pylint: disable=bare-except
            time.sleep(5)
            self.make_service(emails)  # retry needs to re-attach the file and make a new email connection
            self.attach()
            return self.service.send()

    def make_service(self, recipient_emails):
        self.service = EmailMessage(
            subject=self.get_subject(),
            body=self.get_body(),
            to=recipient_emails,
            from_email=self.FROM_EMAIL,
            reply_to=self.REPLY_TO,
            cc=self.CC,
        )
        self.service.content_subtype = self.CONTENT_SUBTYPE

    def attach(self):
        pass

    def can_fetch_recipients_for_party(self, party):
        return party != 'own_company' or (party == 'own_company' and not self.alert.siterecipient_set.exists())


class MissingDocketAlert(EmailAlert):
    REPLY_TO = [SUPPORT_EMAIL]

    template = 'missing_docket_replies.html'

    def __init__(self, alert, unanswered_docket_sms_data, **kwargs):
        super().__init__(alert, **kwargs)
        self.unanswered_docket_sms_data = unanswered_docket_sms_data
        self.user = None
        self.country = self.alert.company.country
        self.now_formatted = None
        self.file_name = None
        self.file_content = None
        self.employee_ids = kwargs.get('employee_ids', None)

    def process(self):
        from core.profiles.models import Employee
        self.make_data()
        if self.file_content:
            if self.alert.siterecipient_set.exists():
                emails = compact(
                    Employee.objects.filter(id__in=self.employee_ids or []).values_list('email', flat=True))
                if emails:
                    self.send(emails)
            else:
                for _, recipient_emails in self.recipients.items():
                    self.send(recipient_emails)

    def get_template_args(self):
        return {
            'user': self.user,
            'urlprefix': STATIC_URL_PREFIX,
            'is_for_provider': self.alert.name == MISSING_DOCKET_ALERT_CODE,
            **super().get_template_args()
        }

    def get_recipients(self):
        recipients = {}
        own_company = self.alert.company
        for recipient in self.alert.recipient_set.filter():
            party = recipient.party
            company = None
            if party == 'own_company':
                company = own_company
            if company:
                company_recipients = recipient.get_employee_communication_channel_value(
                    company, self.alert.recipient_field)
                if company.id in recipients:
                    recipients[company.id] = [*{*recipients[company.id], *company_recipients}]
                else:
                    recipients[company.id] = company_recipients

        return recipients

    def attach(self):
        file_name = f'missing_dockets_{self.now_formatted.replace("/", "")}.csv'
        self.service.attach(f'{file_name}', self.file_content, 'text/csv')
        self.service.attach(convert_image_to_mime_type())


class MissingDocketAlertForProviders(MissingDocketAlert):

    def make_data(self):
        now = timezone.now()
        self.user = self.unanswered_docket_sms_data['user']
        buff = io.StringIO()
        writer = csv.writer(buff, dialect='excel', delimiter=',')
        writer.writerow(MISSING_DOCKET_SMS_EMAIL_HEADERS)
        for message_movement in sorted(self.unanswered_docket_sms_data['messages'],
                                       key=lambda x: x[0].sent_at, reverse=True):
            message, movement = message_movement
            country_code = message.country.config.get('phone_code').replace('+', '') or '61'
            writer.writerow(
                [
                    get(movement, "identifier"),
                    get(movement, "customer.company.name"),
                    DateTimeUtil.localize_date(
                        get(movement, "outload.date_time"),
                        self.country.timezone,
                        self.country.get_format("date"),
                    ),
                    DateTimeUtil.localize_time(
                        get(movement, "outload.date_time"),
                        self.country.timezone,
                        self.country.get_format("time"),
                    ),
                    get(movement, "commodity.display_name"),
                    get(movement, "planned_grade.name"),
                    (
                        get(movement, "outload.storage.farm.name")
                        or get(movement, "freight_pickup.consignor.handler.name")
                    ),
                    (
                        get(movement, "inload.storage.farm.name")
                        or get(movement, "freight_delivery.consignee.handler.name")
                    ),
                    get(movement, "rego"),
                    get(movement, "driver.name"),
                    message.recipient[len(country_code):],
                    DateTimeUtil.localize_date(
                        message.sent_at,
                        self.country.timezone,
                        self.country.get_format("datetime"),
                    ),
                    (
                        DateTimeUtil.localize_date(
                            message.last_reminder_sent_at,
                            self.country.timezone,
                            self.country.get_format("datetime"),
                        )
                        if message.last_reminder_sent_at
                        else ""
                    ),
                ]
            )
        self.now_formatted = DateTimeUtil.localize_date(now, self.country.get_format('date'))
        self.file_content = buff.getvalue()

    def get_subject(self):
        return f"Request for Assistance: Missing Dockets from Drivers on AgriChain - {self.now_formatted}"


class MissingDocketAlertForPickupSite(MissingDocketAlert):

    def make_data(self):
        now = timezone.now()
        self.user = self.unanswered_docket_sms_data['user']
        buff = io.StringIO()
        writer = csv.writer(buff, dialect='excel', delimiter=',')
        writer.writerow(MISSING_DOCKET_SMS_FOR_SITE_EMAIL_HEADERS)
        for message_movement in sorted(self.unanswered_docket_sms_data['messages'],
                                       key=lambda x: x[0].sent_at, reverse=True):
            message, movement = message_movement
            country_code = message.country.config.get('phone_code').replace('+', '') or '61'
            writer.writerow(
                [
                    get(movement, "identifier"),
                    get(movement, "customer.company.name"),
                    get(movement, "provider.name"),
                    DateTimeUtil.localize_date(
                        get(movement, "outload.date_time"),
                        self.country.timezone,
                        self.country.get_format("date"),
                    ),
                    DateTimeUtil.localize_time(
                        get(movement, "outload.date_time"),
                        self.country.timezone,
                        self.country.get_format("time"),
                    ),
                    get(movement, "commodity.display_name"),
                    get(movement, "planned_grade.name"),
                    (
                        get(movement, "outload.storage.farm.name")
                        or get(movement, "freight_pickup.consignor.handler.name")
                    ),
                    get(movement, "freight_delivery.consignee.handler.name"),
                    get(movement, "rego"),
                    get(movement, "driver.name"),
                    message.recipient[len(country_code):],
                    DateTimeUtil.localize_date(
                        message.sent_at,
                        self.country.timezone,
                        self.country.get_format("datetime"),
                    ),
                    (
                        DateTimeUtil.localize_date(
                            message.last_reminder_sent_at,
                            self.country.timezone,
                            self.country.get_format("datetime"),
                        )
                        if message.last_reminder_sent_at
                        else ""
                    ),
                ]
            )
        self.now_formatted = DateTimeUtil.localize_date(now, self.country.get_format('date'))
        self.file_content = buff.getvalue()

    def get_subject(self):
        return f"Request for Assistance: Missing Dockets from Drivers on AgriChain | Pickup Site - {self.now_formatted}"


class SpecAverageReportAlert(EmailAlert):
    template = 'avg_specs.html'

    def __init__(self, alert, **kwargs):
        super().__init__(alert, **kwargs)
        self.date_time = None
        self.summary = None
        self.farm_names = ''

    def make_data(self, farm_ids=None):
        self.date_time = DateTimeUtil.localize(timezone.now(), self.timezone) - datetime.timedelta(days=1)
        self.data, self.summary = self.company.get_spec_avg_report(farm_ids)

    def get_subject(self):
        return f'Daily Receivals Report - {self.date_time.strftime(Country.get_country_format("date"))}'

    def get_template_args(self):
        farm_term = 'Farm' if self.alert.company.is_grower else 'Site'
        return {
            'company': self.alert.company,
            'report_data': self.data,
            'summary': self.summary,
            'send_date_time': self.date_time,
            'farm_names': self.farm_names,
            'farm_term': f'{farm_term}s' if not self.farm_names or len(self.farm_names.split(',')) > 1 else farm_term,
            **super().get_template_args()
        }


class SlotUpdateAlert(EmailAlert):
    action = None
    slots = None
    movement = None
    order = None
    updated_fields = None

    template = 'slot_update_alert_mail.html'

    def get_subject(self):
        if self.action == 'Book':
            key = 'Slot' if self.slots.count() == 1 else 'Slots'
            return f'[Booked] {key} at {self.slots.first().site.name}'
        elif self.action == 'Cancel':
            key = 'Slot' if self.slots.count() == 1 else 'Slots'
            return f'[Cancelled] {key} at {self.slots.first().site.name}'
        else:
            return f'[Updated] Slot at {self.slots.first().site.name}'

    def __init__(self, alert, action, slots, movement, order, updated_fields, **kwargs):
        super().__init__(alert, **kwargs)
        self.date_time = None
        self.action = action
        self.slots = slots
        self.movement = movement  # this is a queryset (should have been named movements)
        self.order = order
        self.updated_fields = updated_fields
        slot = slots and slots.first()
        self.site = get(slot, 'site')

    def get_template_args(self):
        return {
            'slots': self.slots,
            'action': self.action,
            'movement': self.movement,
            'order': self.order,
            'updated_fields': self.updated_fields,
            **super().get_template_args()
        }

    def process(self):
        for _, recipient_emails in self.recipients.items():
            self.send(recipient_emails)

    def get_recipients(self): #pylint: disable=too-many-branches
        recipients = {}
        for recipient in self.alert.recipient_set.filter():
            party = recipient.party
            company = None
            slot = self.slots.first()
            order = self.order or get(slot, 'order')
            movement = self.movement.first() if self.movement else None
            movement = get(slot, 'movement') or movement
            role = AlertRole.CREATOR.value
            if party == 'own_company':
                company = get(slot, 'site.company')
                role = AlertRole.OWN_COMPANY_SITE.value
            elif party == 'customer' and order:
                company = get(order, 'customer.company')
                role = AlertRole.CUSTOMER.value
            elif party == 'freight_provider' and order:
                company = get(order, 'provider')
                role = AlertRole.FREIGHT_PROVIDER.value
            elif party == 'sub_freight_provider':
                company = get(slot, 'sub_freight_provider') or get(movement, 'planned_truck.company')
                role = AlertRole.SUB_FREIGHT_PROVIDER.value
            elif party == 'seller' and order:
                company = get(order, 'seller.company') or get(order, 'commodity_contract.seller.company')
                role = AlertRole.SELLER.value
            elif party == 'buyer' and order:
                company = get(order, 'buyer.company') or get(order, 'commodity_contract.buyer.company')
                role = AlertRole.BUYER.value

            if company and self.can_fetch_recipients_for_party(party):
                recipient.get_recipients_for_company(
                    company, recipients, movement,
                    role = role,
                    site = self.site
                )

        site_recipient = self.alert.siterecipient_set.filter(site_id=get(slot, 'site_id')).first()
        if site_recipient and get(site_recipient, 'recipient'):
            role = AlertRole.OWN_COMPANY_SITE.value
            site_recipient.recipient.get_recipients_for_company(
                get(slot, 'site.company'), recipients, movement,
                role = role,
                site = self.site
            )

        return recipients

class AcquisitionReportAlert(EmailAlert):
    template = 'acquisition_mail_template.html'

    def __init__(self, alert, **kwargs):
        super().__init__(alert, **kwargs)
        self.date_time = DateTimeUtil.localize(timezone.now(), AWST_TZ) - datetime.timedelta(days=1)

    def get_subject(self):
        return "Acquisition File | {} - {}".format(self.company.name,
                                                   self.date_time.strftime(Country.get_country_format("date")))

    def attach(self):
        from core.farms.constants import get_acquisition_csv_header
        buff = io.StringIO()
        writer = csv.writer(buff, dialect='excel', delimiter=',')
        country = self.company.country
        acquisition_csv_headers = get_acquisition_csv_header(country)
        writer.writerow([*acquisition_csv_headers, 'Status'])
        loads = self.company.get_acquisition_inloads()
        rows = []
        traversed = set()
        def get_row(load):
            if load.id not in traversed:
                traversed.add(load.id)
                status = get(load.title_transfer, 'status') or get(
                    load.freight_movement_resolved, 'status') or (
                    load.status if load.status == VOID_STATUS else None)
                return [
                    *load.to_acquisition_csv_row(AWST_TZ, acquisition_csv_headers, country, None, self.company),
                    status
                ]
            return None

        def traverse(queryset, chunk_size=50):
            for _load in queryset.iterator(chunk_size=chunk_size):
                row = get_row(_load)
                if row:
                    rows.append(row)

        voided_loads = loads.filter(pickup__movement__status=VOID_STATUS).union(
            loads.filter(delivery__movement__status=VOID_STATUS)).union(
            loads.filter(title_transfer__status=VOID_STATUS)).union(
            loads.filter(status=VOID_STATUS))

        # Story-8633293290
        # TT/Movement with Void Status (Marked Yesterday) should be on Top
        # TT/Movement with Load Date in past (Before Yesterday) and Created Date = Yesterday should be Second
        # TT/Movement with Load Date = Yesterday and Created Date = Yesterday should be Third
        traverse(voided_loads.order_by('date_time'))
        traverse(loads.exclude(id__in=traversed).order_by('date_time'))

        writer.writerows(rows)
        self.service.attach(
            get_filename_with_timestamp('acquisition_file_', 'csv'), buff.getvalue(), 'text/csv')
        self.service.attach(convert_image_to_mime_type())

    def get_template_args(self):
        return {'report_date': self.date_time}


class BulkInvoiceAlert(EmailAlert):

    def __init__(self, alert, invoice, user, **kwargs):
        super().__init__(alert, **kwargs)
        self.invoice = invoice
        self.user = user


class OutloadReportAlert(EmailAlert):
    template = 'outload_report.html'

    def __init__(self, alert, **kwargs):
        super().__init__(alert, **kwargs)
        self.date_time = None
        self.summary = {}
        self.farm_names = ''

    def make_data(self, farm_ids=None):
        self.date_time = DateTimeUtil.localize(timezone.now(), self.timezone) - datetime.timedelta(days=1)
        self.data, self.summary = self.alert.company.get_outload_report(farm_ids)

    def get_subject(self):
        return f'Daily Outload Report - {self.date_time.strftime(Country.get_country_format("date"))}'

    def get_template_args(self):
        farm_term = 'Farm' if self.alert.company.is_grower else 'Site'
        return {
            'company': self.alert.company,
            'report_data': self.data,
            'summary': self.summary,
            'send_date_time': self.date_time,
            'farm_names': self.farm_names,
            'farm_term': f'{farm_term}s' if not self.farm_names or len(self.farm_names.split(',')) > 1 else farm_term,
            **super().get_template_args()
        }


class MassLimitAlert(EmailAlert):
    template = 'truck_overweight_mail.html'
    CC = None
    BCC = None
    REPLY_TO = None

    def __init__(self, alert, load, entity, **kwargs):
        # for now entity is Movement
        super().__init__(alert, **kwargs)
        self.load = load
        self.load_site = self.load.get_site() if self.load else None
        self.entity = entity
        self.pdf = None

    def get_recipients(self):
        recipients = {}
        for recipient in self.alert.recipient_set.filter():
            party = recipient.party
            company = None
            if party == 'own_company':
                company = get(self.load_site, 'farm.company')
            elif party == 'customer':
                company = get(self.entity, 'customer.company')
            elif party == 'freight_provider':
                company = get(self.entity, 'provider')
            if company and self.can_fetch_recipients_for_party(party):
                recipients[company.id] = recipient.get_employee_communication_channel_value(
                    company, self.alert.recipient_field, self.entity)

        site = get(self.load, 'farm')
        site_recipient = self.alert.siterecipient_set.filter(site_id=get(site, 'id')).first()
        if site_recipient and get(site_recipient, 'recipient'):
            site_recipient.recipient.get_recipients_for_company(get(site, 'company'), recipients, self.entity)
        return recipients

    def process(self):
        for company_id, recipient_emails in self.recipients.items():
            self.make_data(company_id)
            self.send(recipient_emails)

    def make_data(self, company_id): # pylint: disable=arguments-differ
        from core.freights.tasks import FreightMail
        freight_mail = FreightMail(self.entity, False)
        can_view_pickup = self.entity.can_view_pickup(user=None, company_id=company_id)
        can_view_delivery = self.entity.can_view_delivery(user=None, company_id=company_id)
        self.pdf = freight_mail.pdf('freight_contract_preview.html', {
            'urlprefix': STATIC_URL_PREFIX, 'movement': self.entity, 'act': False,
            'can_view_pickup': can_view_pickup, 'can_view_delivery': can_view_delivery
        })

    def attach(self):
        self.service.attach(
            f'{self.entity.identifier}.pdf', self.pdf, 'application/pdf'
        )
        self.service.attach(convert_image_to_mime_type())

    def get_subject(self):
        return f"Mass limit breach at {self.load_site.farm.name} by {self.entity.provider.display_name}" \
               f" [{self.load.truck.rego}]"

    def get_template_args(self):
        date_time = get(self.load, 'date_time')
        country_timezone = self.timezone
        return {
            'date': DateTimeUtil.localize_date(date_time, country_timezone),
            'time': DateTimeUtil.localize_time(date_time, country_timezone),
            'load': self.load,
            'load_site': self.load_site,
            'contract': self.entity,
            'mass_limit': self.load.max_allowed_weight,
            'overweight': self.load.overweight,
            'timezone': AEST_AEDT_TZ,
            **super().get_template_args()
        }


class Class1MaterialAlert(EmailAlert):
    template = 'class_1_material_mail.html'

    def __init__(self, alert, entity, **kwargs):
        # for now entity is Movement
        super().__init__(alert, **kwargs)
        self.vendor_dec = get(kwargs, 'vendor_dec')
        self.entity = entity
        self.pdf = None
        self.site = None

    def get_recipients(self):
        recipients = {}
        load_type = get(self.kwargs, 'load_type')
        for recipient in self.alert.recipient_set.filter():
            party = recipient.party
            company = None
            if party == 'own_company':
                if load_type == 'outload':
                    self.site = get(self.entity, 'freight_pickup.consignor.handler')
                    company = get(self.site, 'company')
                else:
                    self.site = get(self.entity, 'freight_delivery.consignee.handler')
                    company = get(self.site, 'company')
            elif party == 'customer':
                company = get(self.entity, 'customer.company')
            elif party == 'freight_provider':
                company = get(self.entity, 'provider')
            if company and self.can_fetch_recipients_for_party(party):
                recipient.get_recipients_for_company(company, recipients, self.entity, self.vendor_dec)

        site_recipient = self.alert.siterecipient_set.filter(site_id=get(self.site, 'id')).first()
        if site_recipient and get(site_recipient, 'recipient'):
            site_recipient.recipient.get_recipients_for_company(get(self.site, 'company'), recipients,
                                                                self.entity, self.vendor_dec)
        return recipients

    def make_data(self):
        html = render_to_string(
            'truck_cleanliness_vendordec.html', {
                'instance': self.vendor_dec,
                'unit': self.vendor_dec.commodity.unit,
                'country_code': self.vendor_dec.country_code
            }
        )
        self.pdf = HTMLToPDF.from_string(html, False)

    def attach(self):
        self.service.attach(
            f'{self.vendor_dec.identifier}.pdf', self.pdf, 'application/pdf'
        )
        self.service.attach(convert_image_to_mime_type())

    def get_subject(self):
        return f'CLASS 1 DANGEROUS material reported for previous load | {self.vendor_dec.assigned_rego} | ' \
               f'{self.entity.identifier} | {self.entity.created_at.strftime("%d-%m-%Y")}'

    def process(self):
        self.make_data()
        for _, recipient_emails in self.recipients.items():
            self.send(recipient_emails)

    def get_template_args(self):
        load_type = get(self.kwargs, 'load_type')
        if load_type == 'outload':
            booking_date = get(self.entity, 'freight_pickup.date_time')
        else:
            booking_date = get(self.entity, 'freight_delivery.date_time')
        tz = get(self.site, 'timezone.location')
        return {
            'site': self.site,
            'contract': self.entity,
            'vendor_dec': self.vendor_dec,
            'booking_date': DateTimeUtil.localize_date(booking_date, tz),
            'booking_time': DateTimeUtil.localize_time(booking_date, tz),
            'load_type': load_type,
            **super().get_template_args()
        }


class BaseCheckpointAlert(EmailAlert):
    load = None
    entity = None
    partyMapping = {}
    pdf = None
    load_company = None

    def __init__(self, alert, load, entity, **kwargs):
        super().__init__(alert, **kwargs)
        self.entity = entity
        self.load = load
        self.site = get(load, 'farm')
        self.pdf = None
        self.partyMapping = {}
        self.load_company = get(self.load, 'farm.company')

    def get_recipients(self): #pylint: disable=too-many-branches
        recipients = {}
        for recipient in self.alert.recipient_set.filter():
            party = recipient.party
            company = None
            party_for_mapping = 'common'
            role = AlertRole.CREATOR.value
            if party == 'own_company':
                company = self.load_company
                role = AlertRole.PICKUP_SITE.value if self.load.is_outload else AlertRole.DELIVERY_SITE.value
            elif party == 'customer':
                party_for_mapping = party
                company = get(self.entity, 'customer.company')
                role = AlertRole.CUSTOMER.value
            elif party == 'delivery_site':
                # Only when alert is Pickup Alert
                company = (
                    get(self.entity, 'freight_delivery.consignee.handler.company') or
                    get(self.entity, 'order.freight_delivery.consignee.handler.company')
                )
                role = AlertRole.DELIVERY_SITE.value
            elif party == 'pickup_site':
                # Only when alert is Delivery Alert
                company = (
                    get(self.entity, 'freight_pickup.consignor.handler.company') or
                    get(self.entity, 'order.freight_pickup.consignor.handler.company')
                )
                role = AlertRole.PICKUP_SITE.value
            elif party == 'freight_provider':
                party_for_mapping = 'provider'
                company = get(self.entity, 'provider')
                role = AlertRole.FREIGHT_PROVIDER.value
            elif (party == 'sub_freight_provider' and
                  get(self.load, 'freight_provider_id') != get(self.entity, 'provider_id')):
                party_for_mapping = 'provider'
                company = get(self.load, 'freight_provider')
                role = AlertRole.SUB_FREIGHT_PROVIDER.value
            elif party == 'stock_owner':
                company = get(self.load, 'ngr.company')
                role = AlertRole.STOCK_OWNER.value
            if company and self.can_fetch_recipients_for_party(party):
                if get(self.partyMapping, company.id) not in ['customer', 'provider']:
                    self.partyMapping.update({company.id: party_for_mapping})
                recipient.get_recipients_for_company(
                    company, recipients, self.entity,
                    role = role,
                    site = self.site
                )

        site_recipient = self.alert.siterecipient_set.filter(site_id=get(self.load, 'farm_id')).first()
        if site_recipient and get(site_recipient, 'recipient'):
            role = AlertRole.PICKUP_SITE.value if self.load.is_outload else AlertRole.DELIVERY_SITE.value
            site_recipient.recipient.get_recipients_for_company(
                self.load_company, recipients, self.entity,
                role = role,
                site = self.site
            )
        return recipients

    def get_note(self, party):
        company_id = ""
        if party == 'provider':
            company_id = self.entity.provider.company_id
        elif party == 'customer':
            company_id = self.entity.customer.company_id
        if not company_id:
            return ""
        return self.entity.note_set.filter(company_id=company_id).first()

    def make_data_for_party(self, party, company_id):
        html = render_to_string(
            'freight_contract_preview.html',
            {
                'movement': self.entity,
                'act': False,
                'urlprefix': STATIC_URL_PREFIX,
                'party': party,
                'footer': True,
                'note': self.get_note(party),
                'can_view_pickup': (
                    self.entity.can_view_pickup(user=None, company_id=company_id)
                    if self.entity
                    else True
                ),
                'can_view_delivery': (
                    self.entity.can_view_delivery(user=None, company_id=company_id)
                    if self.entity
                    else True
                ),
            },
        )
        self.pdf = HTMLToPDF.from_string(html, False)

    def attach(self):
        self.service.attach(
            f'{self.entity.identifier}.pdf', self.pdf, 'application/pdf'
        )
        self.service.attach(convert_image_to_mime_type())

    def process(self):
        for company_id, recipient_emails in self.recipients.items():
            self.make_data_for_party(self.partyMapping.get(company_id), company_id)
            self.send(recipient_emails)

    def get_template_args(self):
        slot = self.entity.outload_slot if self.load.type == 'outload' else self.entity.inload_slot
        site_operator = None
        load_creator = get(self.load, 'created_by')
        if get(load_creator, 'company_id') == get(self.load_company, 'id'):
            site_operator = get(load_creator, 'name')
        from core.trucks.models import TruckCheckpointLog
        truck_entry_time = TruckCheckpointLog.get_truck_entry_time_for_load(self.load)
        return {
            'contract': self.entity,
            'load': self.load,
            'slot': slot,
            'slot_url': slot.site_booking_url if slot else None,
            'site_operator': site_operator,
            'time_in': truck_entry_time,
            **super().get_template_args()
        }


class PickupInformationAlert(BaseCheckpointAlert):
    template = 'pickup_information_alert_mail.html'

    def get_subject(self):
        tz = get(self.load, 'storage.farm.timezone.location')
        rego = get(self.load, 'site_name') if get(self.entity, 'is_pack_movement') else get(self.load, 'truck.rego')
        return (f"{rego} LOADED | {self.entity.identifier} | "
                f"{DateTimeUtil.localize_date(self.load.date_time, tz)}")


class DeliveryInformationAlert(BaseCheckpointAlert):
    template = 'delivery_information_alert_mail.html'

    def get_subject(self):
        tz = get(self.load, 'storage.farm.timezone.location')
        rego = get(self.load, 'site_name') if get(self.entity, 'is_pack_movement') \
            else get(self.load, 'truck.rego')
        return (f"{rego} UNLOADED | {self.entity.identifier} | "
                f"{DateTimeUtil.localize_date(self.load.date_time, tz)}")


class TruckCleanlinessDeclarationPendingAlert(EmailAlert):
    template = 'truck_cleanliness_declaration_pending_mail.html'

    def __init__(self, alert, movement, **kwargs):
        super().__init__(alert, **kwargs)
        self.movement = movement
        self.current_slot = None
        self.company_site = None
        self.date_time = None
        self.pickup_site = None
        self.delivery_site = None

    def get_recipients(self):
        recipients = {}
        own_company = self.company
        self.pickup_site = get(self.movement, 'freight_pickup.consignor.handler')
        self.delivery_site = get(self.movement, 'freight_delivery.consignee.handler')
        if get(self.pickup_site, 'company_id') == own_company.id:
            self.current_slot = get(self.movement, 'outload_slot')
            self.company_site = self.pickup_site
            self.date_time = get(self.movement, 'freight_pickup.date_time')
        elif get(self.delivery_site, 'company_id') == own_company.id:
            self.current_slot = get(self.movement, 'inload_slot')
            self.company_site = self.delivery_site
            self.date_time = get(self.movement, 'freight_delivery.date_time')
        for recipient in self.alert.recipient_set.filter():
            party = recipient.party
            company = None
            if party == 'own_company':
                company = own_company
            elif party == 'freight_provider':
                company = get(self.movement, 'provider')
            elif party == 'sub_freight_provider':
                company = get(self.current_slot, 'sub_freight_provider')
            if company and self.can_fetch_recipients_for_party(party):
                recipient.get_recipients_for_company(company, recipients)

        site_recipient = self.alert.siterecipient_set.filter(site_id=get(self.company_site, 'id')).first()
        if site_recipient and get(site_recipient, 'recipient'):
            site_recipient.recipient.get_recipients_for_company(own_company, recipients)
        return recipients

    def process(self):
        for _, recipient_emails in self.recipients.items():
            self.send(recipient_emails)

    def get_template_args(self):
        pickup_delivery_date_time = None
        site_timezone = get(self.company_site, 'timezone.location')
        if self.date_time and site_timezone:
            pickup_delivery_date_time = (f"{DateTimeUtil.localize_date(self.date_time, site_timezone)}"
                                         f" {DateTimeUtil.localize_time(self.date_time, site_timezone)}")

        return {
            'movement': self.movement,
            'slot': self.current_slot,
            'date_time': pickup_delivery_date_time
        }

    def get_subject(self):
        return (f"Truck Cleanliness Declaration Not Filled | {self.movement.identifier} |"
               f" {get(self.current_slot, 'rego') or get(self.movement, 'planned_truck.rego')} |"
                f" {get(self.pickup_site, 'display_name')} | {get(self.delivery_site, 'display_name')}")


class SMSAlert(AbstractAlert):
    message_type = None
    message = None

    def get_message(self, _):
        return self.message

    def process(self):
        for company_id, mobile_numbers in self.recipients.items():
            self.send(mobile_numbers, company_id)

    def send(self, mobile_numbers, company_id):  # pylint: disable=arguments-differ
        if not mobile_numbers:
            return
        for mobile_number in compact(set(mobile_numbers)):
            self.make_service(mobile_number, company_id)
            if get(self.service, 'sender'):
                self.service.save()
                self.service.send()

    def make_service(self, mobile_number, company_id):  # pylint: disable=arguments-differ
        self.service = MobileMessage(
            type=self.message_type,
            recipient=mobile_number,
            message=self.get_message(company_id),
            country_id=self.load.country_id,
            args={'load_type': self.load.type, 'load_id': self.load.id}
        )
        self.service.clean()


class InloadDoneAlert(SMSAlert):
    message_type = MESSAGE_TYPE_INLOAD_DONE

    def __init__(self, alert, load, **kwargs):
        super().__init__(alert, **kwargs)
        self.load = load

    def get_recipients(self):
        recipients = {}
        for recipient in self.alert.recipient_set.filter():
            party = recipient.party
            company = None
            if party == 'truck_owner':
                company = get(self.load, 'truck.company')
            elif party == 'stock_owner':
                company = get(self.load, 'ngr.company')

            if company:
                company_recipients = recipient.get_employee_communication_channel_value(
                    company, self.alert.recipient_field, self.load
                )
                if company.id not in recipients:
                    recipients[company.id] = []
                recipients[company.id] = {*recipients[company.id], *company_recipients}
        return recipients

    def get_message(self, company_id):
        if company_id == get(self.load, 'ngr.company_id'):
            return self.load.get_inload_done_sms_content_to_customer_key_contact()
        elif company_id == get(self.load, 'truck.company_id'):
            return self.load.get_load_done_sms_content()


class OutloadDoneAlert(SMSAlert):
    message_type = MESSAGE_TYPE_OUTLOAD_DONE

    def __init__(self, alert, load, **kwargs):
        super().__init__(alert, **kwargs)
        self.load = load

    def get_recipients(self):
        recipients = {}
        for recipient in self.alert.recipient_set.filter():
            party = recipient.party
            company = None
            if party == 'truck_owner':
                company = get(self.load, 'truck.company')

            if company:
                company_recipients = recipient.get_employee_communication_channel_value(
                    company, self.alert.recipient_field, self.load
                )
                if company.id not in recipients:
                    recipients[company.id] = []
                recipients[company.id] = {*recipients[company.id], *company_recipients}
        return recipients

    def get_message(self, _):
        return self.load.get_load_done_sms_content()


class DocketClarificationAlert(EmailAlert):
    template = 'docket_clarification_template.html'

    def __init__(self, alert, pending_dockets=None, alert_type='daily', **kwargs):
        super().__init__(alert, **kwargs)
        self.pending_dockets = pending_dockets
        self.is_daily_alert = bool(alert_type == 'daily')
        self.company_docket_clarifications = None
        self.docket_for_company_as_pickup_delivery_site = None
        self.clarification_count = None
        self.file_content = None
        self.csv = None

    def get_recipients(self):
        recipients = {}
        own_company = self.alert.company
        for recipient in self.alert.recipient_set.filter():
            party = recipient.party
            company = None
            if party == 'own_company':
                company = own_company
            if company:
                company_recipients = recipient.get_employee_communication_channel_value(
                    company, self.alert.recipient_field
                )
                if company.id in recipients:
                    recipients[company.id] = [*{*recipients[company.id], *company_recipients}]
                else:
                    recipients[company.id] = company_recipients

        return recipients

    def process(self):
        self.make_data()
        for _, recipient_emails in self.recipients.items():
            if self.clarification_count > 0:
                self.send(recipient_emails)

    def make_data(self):
        self.company_docket_clarifications = (self.pending_dockets
        .filter(
            models.Q(freight_contract__customer__company_id=self.alert.company_id) |
            models.Q(freight_contract__seller__company_id=self.alert.company_id) |
            models.Q(freight_contract__buyer__company_id=self.alert.company_id) |
            models.Q(freight_contract__provider_id=self.alert.company_id) |
            models.Q(freight_contract__freight_pickup__consignor__handler__company_id=self.alert.company_id,
                     load_type=OUTLOAD
                     ) |
            models.Q(freight_contract__freight_delivery__consignee__handler__company_id=self.alert.company_id,
                     load_type=INLOAD)
            )
        )
        self.clarification_count = self.company_docket_clarifications.count()
        if not self.is_daily_alert and self.clarification_count > 0:
            buff = io.StringIO()
            writer = csv.writer(buff, dialect='excel', delimiter=',')
            writer.writerow(DOCKET_CLARIFICATION_CSV_HEADERS)
            from core.settings import WEB_URL
            for item in self.company_docket_clarifications:
                url = f"{WEB_URL}/#/?referrerUrl=/freights/movements/{item.freight_contract.id}/details"
                writer.writerow([
                    item.date_time.strftime('%d/%m/%y'),
                    item.freight_contract.identifier,
                    item.load_display,
                    get(item.freight_contract, 'outload.farm.display_name'),
                    item.category_display,
                    ', '.join(item.get_sub_category_display_values),
                    item.comment,
                    f'=HYPERLINK("{url}", "Resolve")',
                ])
            self.file_content = buff.getvalue()

    def get_template_args(self):
        args = {'customer_company_name': self.alert.company.name, 'is_daily_alert': self.is_daily_alert}
        if self.company_docket_clarifications:
            if not self.is_daily_alert:
                return args
            docket_data = []
            from core.settings import WEB_URL
            for item in self.company_docket_clarifications:
                docket_data.append({
                    'date': item.date_time.strftime('%d/%m/%y'),
                    'identifier': item.freight_contract.identifier,
                    'load_type': item.load_display,
                    'pickup_site': get(item.freight_contract, 'outload.farm.display_name'),
                    'category': item.category_display,
                    'sub_category': ', '.join(item.get_sub_category_display_values),
                    'comments': item.comment,
                    'action': f"{WEB_URL}/#/?referrerUrl=/freights/movements/{item.freight_contract.id}/details",
                })
            self.clarification_count = len(docket_data)
            return {'docket_data': docket_data, **args}


    def get_subject(self):
        if self.is_daily_alert:
            return f"AgriChain | Docket Clarification | {self.clarification_count} Dockets Require Your Attention Today"
        else:
            return (f"AgriChain | Docket Clarification Weekly Summary | "
                    f"{self.clarification_count} Dockets Require Your Attention")

    def attach(self):
        if not self.is_daily_alert:
            file_name = get_filename_with_timestamp('docket_clarification_', 'csv')
            self.service.attach(
                f'{file_name}', self.file_content, 'text/csv'
            )


class StockOperationsUpdateAlert(EmailAlert): # pylint: disable=too-many-instance-attributes
    template = 'stock_operations_update_template.html'

    def __init__(self, alert, farm, user, operation_name, updated_at, **kwargs):
        super().__init__(alert, **kwargs)
        self.farm = farm
        self.user = user
        self.operation_name = operation_name
        self.updated_at = updated_at
        self.is_void = kwargs.get('is_void', False)
        self.is_updated = kwargs.get('is_update_operation')
        self.site_name = get(kwargs.get('load'), 'farm.name') or get(farm, 'name')

        self.load = kwargs.get('load')
        self.storage = kwargs.get('storage')
        self.stock = kwargs.get('stock')
        self.load_identifier = kwargs.get('load_identifier')
        self.outload = get(kwargs.get('stock'), 'outload') or kwargs.get('outload')
        self.changes = kwargs.get('changes')
        self.stock_ngrs = kwargs.get('stock_ngrs')
        self.commodities = kwargs.get('commodities')
        self.grade_names = kwargs.get('grade_names')
        self.seasons = kwargs.get('seasons')
        self.storages = kwargs.get('storages')
        self.stock_owners = None

    def get_recipients(self): # pylint: disable=too-many-branches
        recipients = {}
        from core.ngrs.models import Ngr
        ngr_numbers = self.stock_ngrs or [get(self.load, 'ngr.ngr_number')]
        if ngr_numbers:
            stock_owner_id = Ngr.objects.filter(ngr_number__in=ngr_numbers).values_list('company_id', flat=True)
            self.stock_owners = Company.objects.filter(id__in=stock_owner_id)
        for recipient in self.alert.recipient_set.filter():
            party = recipient.party
            companies = []
            if party == 'own_company':
                companies = [get(self.load, 'farm.company') or get(self.farm, 'company')]
            elif party == 'stock_owner':
                companies = self.stock_owners
            if companies:
                for company in companies:
                    if company and self.can_fetch_recipients_for_party(party):
                        recipient.get_recipients_for_company(company, recipients)
        site = get(self.load, 'farm') or self.farm
        site_recipient = self.alert.siterecipient_set.filter(site_id=get(site, 'id')).first()
        if site_recipient and get(site_recipient, 'recipient'):
            site_recipient.recipient.get_recipients_for_company(get(site, 'company'), recipients)

        return recipients

    def process(self):
        self.make_data()
        for _, recipient_emails in self.recipients.items():
            self.send(recipient_emails)

    def make_data(self):
        pass

    def get_modification_details(self):
        if self.is_void:
            return (
                f"Your stock operation {self.operation_name} for commodity "
                f"{get(self.load, 'commodity.display_name')} has been voided."
            )

        if self.operation_name == DIRECT_LOAD:
            loaded = "loaded" if get(self.load, 'is_inload') else "unloaded"
            if self.is_updated:
                return (
                    f"A quantity of {get(self.load, 'net_weight')} {get(self.load, 'entered_unit')} "
                    f"has been updated."
                )
            return (
                f"A quantity of {get(self.load, 'net_weight')} {get(self.load, 'entered_unit')} "
                f"{get(self.load, 'commodity.display_name')} has been {loaded} for NGR "
                f"{get(self.load, 'ngr.ngr_number')}."
            )

        if self.operation_name in [STORAGE_STOCK_UPDATE_LOAD, STOCK_UPDATE_LOAD,
                                   STOCK_SWAP_LOAD, STORAGE_TRANSFER_LOAD, REGRADED_LOAD]:
            return "The following fields have been updated:"

        if self.operation_name in [STORAGE_STOCK_EMPTY_LOAD, STOCK_EMPTY_LOAD]:
            return "The stock with following details have been completely emptied."

    def format_items(self, items):
        suffix = ' and more, ' if len(items) >= STOCK_ALERT_DATA_LIMIT else ''
        return ', '.join(items) + suffix

    def get_updated_data(self):
        updated_data = {}

        ngr_numbers = self.format_items(self.stock_ngrs) if self.stock_ngrs else ''
        commodity_names = self.format_items(self.commodities) if self.commodities else ''
        grade_names = self.format_items(self.grade_names) if self.grade_names else ''
        seasons = self.format_items(self.seasons) if self.seasons else ''
        storages_names = self.format_items(self.storages) if self.storages else ''

        if self.is_updated:
            existing_load = self.load.history.first()
            updated_data = {
                'quantity': {
                    'existing': (
                        f"{get(existing_load, 'estimated_net_weight')} {get(self.load, 'entered_unit')}"
                    ),
                    'updated': f"{get(self.load, 'net_weight')} {get(self.load, 'entered_unit')}"
                },
                'NGR': {
                    'existing': get(existing_load, 'ngr.ngr_number'),
                    'updated': get(self.load, 'ngr.ngr_number')
                },
                'commodity': {
                    'existing': get(existing_load, 'commodity.display_name'),
                    'updated': get(self.load, 'commodity.display_name')
                },
                'grade': {
                    'existing': get(existing_load, 'grade.name'),
                    'updated': get(self.load, 'grade.name')
                },
                'season': {
                    'existing': get(existing_load, 'season'),
                    'updated': get(self.load, 'season')
                }
            }
        if get(self.load, 'is_regraded_load'):
            updated_data = {
                'variety': {
                    'existing': get(self.outload, 'variety.name'),
                    'updated': get(self.load, 'variety.name')
                },
                'grade': {
                    'existing': get(self.outload, 'grade.name'),
                    'updated': get(self.load, 'grade.name')
                },
                'season': {
                    'existing': get(self.outload, 'season'),
                    'updated': get(self.load, 'season')
                },
                'Regraded tonnage': {
                    'existing': "-",
                    'updated': f"{get(self.load, 'net_weight')} {get(self.load, 'entered_unit')}"
                },
                'Regrade/Reseason Differential (per MT)': {
                    'existing': '-',
                    'updated': get(self.stock, 'regrade_reseason_differential')
                }
            }
        if get(self.load, 'is_stock_swap_load'):
            updated_data = {
                'site': {
                    'existing': f"{get(self.outload, 'farm.name')}",
                    'updated': f"{get(self.load, 'farm.name')}"
                },
                'swapped tonnage': {
                    'existing': "-",
                    'updated': f"{get(self.load, 'net_weight')} {get(self.load, 'entered_unit')}"
                },
            }
        if get(self.load, 'is_storage_transfer_load'):
            updated_data = {
                "storage location": {
                    "existing": f"{get(self.outload, 'storage.name')}",
                    "updated": f"{get(self.load, 'storage.name')}"
                },
                "transferred tonnage": {
                    "existing": "-",
                    "updated": f"{get(self.load, 'net_weight')} {get(self.load, 'entered_unit')}"
                }
            }
        if self.operation_name == STOCK_EMPTY_LOAD:
            stock_owners_names = (
                ', '.join(self.stock_owners.values_list('business_name', flat=True)) if self.stock_owners else ''
            )
            updated_data = [
                ('stock owners', stock_owners_names),
                ('NGR', ngr_numbers),
                ('commodity', commodity_names),
                ('season', seasons),
                ('grade', grade_names),
            ]
        if self.operation_name == STORAGE_STOCK_EMPTY_LOAD:
            updated_data = [
                ('storage', storages_names),
                ('commodity', commodity_names),
                ('season', seasons),
                ('grade', grade_names),
            ]
        if self.changes:
            updated_data = {}
            for change, values in self.changes.items():
                updated_data[change] = {
                        "existing": values[0],
                        "updated": values[1]
                }
        return updated_data

    def get_template_args(self):
        tz = get(self.user, 'country.config.timezone')
        ngr_numbers = self.format_items(self.stock_ngrs) if self.stock_ngrs else ''
        commodity_names = self.format_items(self.commodities) if self.commodities else ''
        grade_names = self.format_items(self.grade_names) if self.grade_names else ''
        season_names = self.format_items(self.seasons) if self.seasons else ''

        args = {
            'site_name': self.site_name,
            'updated_by': get(self.user, 'name'),
            'load_identifier': (
                get(self.load, 'extras.identifier') or
                get(self.load, 'identifier', '').split('_')[0]or self.load_identifier
            ),
            'operation_type': self.operation_name + ' Voided' if self.is_void else self.operation_name,
            'updated_at': DateTimeUtil.localize_date(
                self.updated_at, tz, Country.get_country_format("datetime")
            ),
            'stock_owner_ngr': get(self.load, 'ngr.ngr_number') or ngr_numbers,
            'commodity_name': get(self.load, 'commodity.display_name') or commodity_names,
            'grade_name': get(self.load, 'grade.name') or grade_names,
            'season_name': get(self.load, 'season') or season_names,
            'storage_name': get(self.load, 'storage.name') or get(self.storage, 'name'),
            'modification_details': self.get_modification_details(),
            'updated_details': self.get_updated_data(),
        }
        if self.operation_name not in [
            STORAGE_STOCK_UPDATE_LOAD, STORAGE_TRANSFER_LOAD, STORAGE_STOCK_EMPTY_LOAD, STOCK_EMPTY_LOAD
        ]:
            args.update({'show_ngr': True})
        if self.operation_name not in [STORAGE_STOCK_EMPTY_LOAD, STOCK_EMPTY_LOAD]:
            args.update({'show_commodity_and_storage': True, 'show_changes_table': True})
        else:
            args.update({'show_value_table': True})
        if self.is_void:
            args.update({'show_commodity_and_storage': False, 'show_value_table': False, 'show_changes_table': False})

        return args

    def get_subject(self):
        subject = f"AgriChain | Change in Stock on {self.site_name} | {self.operation_name}"
        return subject + " Voided" if self.is_void else subject


class StockAutoUpdateAlert(EmailAlert): # pylint: disable=too-many-instance-attributes
    template = 'stock_auto_update_template.html'

    def __init__(self, alert, stock_owner_company_name, business_change_text, **kwargs):
        super().__init__(alert, **kwargs)
        self.stock_owner_company_name = stock_owner_company_name
        self.business_change_text = business_change_text

    def get_recipients(self):
        recipients = {}
        own_company = self.alert.company
        for recipient in self.alert.recipient_set.filter():
            party = recipient.party
            company = None
            if party == 'own_company':
                company = own_company
            if company:
                company_recipients = recipient.get_employee_communication_channel_value(
                    company, self.alert.recipient_field)
                if company.id in recipients:
                    recipients[company.id] = [*{*recipients[company.id], *company_recipients}]
                else:
                    recipients[company.id] = company_recipients

        return recipients

    def process(self):
        for _, recipient_emails in self.recipients.items():
            self.send(recipient_emails)

    def get_template_args(self):
        args = {
            'stock_owner_company_name': self.stock_owner_company_name,
            'business_change_text': self.business_change_text
        }
        return args

    def get_subject(self):
        return f"AgriChain | Auto Update to Stock owned by {self.stock_owner_company_name} at your sites"
class MobilePushNotificationAlert(AbstractAlert):
    def process(self):
        self.send(flatten(self.recipients.values()), self.make_data())

    def send(self, employee_ids, args):  # pylint: disable=arguments-differ
        from core.devices.models import MobilePushNotification
        MobilePushNotification.notify_mobile_devices(list(employee_ids), args)

    def make_service(self):  # pylint: disable=arguments-differ
        pass

    def get_recipients(self):
        recipients = {}
        for recipient in self.alert.recipient_set.filter():
            if self.alert.company:
                recipient.get_recipients_for_company(self.alert.company, recipients)
        return recipients


class NewCashPriceForMyStocksAlert(MobilePushNotificationAlert):
    def __init__(self, alert, cash_price, **kwargs):
        super().__init__(alert, **kwargs)
        self.cash_price = cash_price

    def make_data(self):
        site_name = self.cash_price.site.name
        return {
            'notification_type': NOTIFICATION_TYPE_NEW_CASH_PRICE_FOR_STOCKS,
            'message_txt': f'There is a new leading cash price for {self.cash_price.commodity.display_name} '
                           f'{self.cash_price.grade.name} {self.cash_price.season} on {site_name}. '
                           f'Click here to check the latest prices',
            'ios_msg_title': f'New Price Posted at {site_name}',
            'extra': {
                'ac_notification_id': self.cash_price.id,
                'ac_notification_type': NOTIFICATION_TYPE_CASH_PRICE,
                'notification_type': NOTIFICATION_TYPE_NEW_CASH_PRICE_FOR_STOCKS
            }
        }


class LeadingCashPriceAlert(MobilePushNotificationAlert):
    def __init__(self, alert, cash_price, **kwargs):
        super().__init__(alert, **kwargs)
        self.cash_price = cash_price

    def make_data(self):
        site_name = self.cash_price.site.name
        return {
            'notification_type': NOTIFICATION_TYPE_LEADING_CASH_PRICE_WARNING,
            'message_txt': f'You are no longer the leading cash price on {site_name} for'
                           f' {self.cash_price.commodity.display_name} {self.cash_price.grade.name}'
                           f' {self.cash_price.season}. Click here to check cash prices. ',
            'ios_msg_title': f'New Leading Price Posted at {site_name}',
            'extra': {
                'ac_notification_id': self.cash_price.id,
                'ac_notification_type': NOTIFICATION_TYPE_CASH_PRICE,
                'notification_type': NOTIFICATION_TYPE_LEADING_CASH_PRICE_WARNING
            }
        }

class Alert(BaseModel):
    CHANNEL_ATTRIBUTES = {
        EMAIL_CHANNEL: EMAIL_CHANNEL,
        SMS_CHANNEL: 'mobile',
        MOBILE_PUSH_NOTIFICATION_CHANNEL: MOBILE_PUSH_NOTIFICATION_CHANNEL
    }

    class Meta:
        db_table = "alerts"

    company = models.ForeignKey(Company, on_delete=models.CASCADE)  # creator company (may not be same as recipient)
    name = models.CharField(max_length=100, choices=ALERT_NAMES)
    channel = models.CharField(max_length=100, choices=CHANNEL_TYPES)
    frequency = models.CharField(max_length=100, choices=ALERT_FREQUENCY, null=True, blank=True)
    hours_before_trigger = models.IntegerField(null=True, blank=True)

    FILLABLES = [
        'company_id',
        'name',
        'channel',
        'frequency',
        'recipients',
        'is_active',
        'hours_before_trigger',
        'site_recipients',
        'include_own_site_loads_in_acquisition_report'
    ]

    def is_valid(self):
        if self.name not in self.get_alert_codes():
            raise ValueError('alert name is not valid.')

    @staticmethod
    def get_alert_codes():
        return [name[0] for name in ALERT_NAMES]

    def prepare(self, **kwargs):
        self.is_valid()
        alert_code_to_class_mapping = {
            MISSING_DOCKET_ALERT_CODE: MissingDocketAlertForProviders,
            MISSING_DOCKET_PICKUP_SITE_ALERT_CODE: MissingDocketAlertForPickupSite,
            SPEC_AVG_REPORT_ALERT_CODE: SpecAverageReportAlert,
            MASS_LIMIT_BREACH_ALERT_CODE: MassLimitAlert,
            DELIVERY_CONFIRMATION_SMS_ALERT_CODE: InloadDoneAlert,
            PICKUP_CONFIRMATION_SMS_ALERT_CODE: OutloadDoneAlert,
            CLASS_1_MATERIAL_ALERT_CODE: Class1MaterialAlert,
            PICKUP_CONFIRMATION_ALERT_CODE: PickupInformationAlert,
            DELIVERY_CONFIRMATION_ALERT_CODE: DeliveryInformationAlert,
            OUTLOAD_REPORT_ALERT_CODE: OutloadReportAlert,
            SLOT_BOOKING_UPDATE_ALERT_CODE: SlotUpdateAlert,
            TRUCK_CLEANLINESS_DECLARATION_NOT_FILLED_ALERT_CODE: TruckCleanlinessDeclarationPendingAlert,
            DOCKET_CLARIFICATION_ALERT_CODE: DocketClarificationAlert,
            STOCK_OPERATIONS_UPDATE_ALERT_CODE: StockOperationsUpdateAlert,
            STOCK_AUTO_UPDATE_ALERT_CODE: StockAutoUpdateAlert,
            ACQUISITION_REPORT_ALERT_CODE: AcquisitionReportAlert,
            NEW_CASH_PRICE_FOR_MY_STOCKS_ALERT_CODE: NewCashPriceForMyStocksAlert,
            LEADING_CASH_PRICE_WARNING_ALERT_CODE: LeadingCashPriceAlert,
            BULK_INVOICE_ALERT_CODE: BulkInvoiceAlert
        }
        if klass := alert_code_to_class_mapping.get(self.name):
            return klass(self, **kwargs)

    def process(self, **kwargs):
        return self.prepare(**kwargs).process()

    @property
    def alert_type(self):
        return to_display_attr(CHANNEL_TYPES, self.channel)

    @property
    def recipients(self):
        parties = self.recipient_set.distinct('party').values_list('party', flat=True)
        return ', '.join([to_display_attr(RECIPIENT_PARTY_TYPES, party) for party in parties])

    @property
    def frequency_display_name(self):
        return to_display_attr(ALERT_FREQUENCY, self.frequency)

    @property
    def alert_display_name(self):
        return to_display_attr(ALERT_NAMES, self.name)

    @classmethod
    def persist(cls, data):
        recipients = data.pop('recipients', None)
        site_recipients = data.pop('site_recipients', [])
        include_own_site_loads_in_acquisition_report = data.pop('include_own_site_loads_in_acquisition_report', False)
        alert = cls(**data)
        alert.save()
        if alert.persisted:
            for recipient in recipients:
                recipient['alert'] = alert
                Recipient.persist(recipient)
            for site_recipient in site_recipients:
                if get(site_recipient, 'site_id'):
                    SiteRecipient.create(site_recipient, alert)
            if alert.name == ACQUISITION_REPORT_ALERT_CODE:
                alert.company.include_own_site_loads_in_acquisition_report = include_own_site_loads_in_acquisition_report # pylint: disable=line-too-long
                alert.company.save()
        return alert

    @property
    def description(self):
        return ALERT_DESCRIPTION.get(self.name)

    @property
    def recipient_field(self):
        return self.CHANNEL_ATTRIBUTES.get(self.channel)

    def remove_old_recipients(self, recipients, site_recipients):
        party = [recipient['party'] for recipient in recipients]
        if site_recipients:
            party.append('own_company')
        elif not site_recipients and self.siterecipient_set.filter():
            SiteRecipient.remove_existing_recipients(self.id)
        self.recipient_set.exclude(party__in=party).delete()

    def remove_old_site_recipients(self, site_recipients):
        Recipient.objects.filter(alert_id=self.id, party='own_company').exclude(
            id__in=self.siterecipient_set.filter().values_list('recipient_id', flat=True)).delete()
        self.siterecipient_set.exclude(
            site_id__in=[site_recipient['site_id'] for site_recipient in site_recipients]).delete()

    def update_alert(self, data):
        recipients = data.pop('recipients', [])
        site_recipients = data.pop('site_recipients', [])
        include_own_site_loads_in_acquisition_report = data.pop('include_own_site_loads_in_acquisition_report', None)
        if recipients:
            self.remove_old_recipients(recipients, site_recipients)

        for recipient_data in recipients:
            alert_recipient = self.recipient_set.filter(party__iexact=get(recipient_data, 'party')).first()
            if alert_recipient:
                alert_recipient.edit(recipient_data)
            else:
                recipient_data['alert'] = self
                Recipient.persist(recipient_data)

        if site_recipients:
            self.remove_old_site_recipients(site_recipients)

        for site_recipient_data in site_recipients:
            site_recipient = self.siterecipient_set.filter(site_id=site_recipient_data['site_id']).first()
            if site_recipient and get(site_recipient, 'recipient'):
                recipient_data = site_recipient_data['recipient']
                site_recipient.recipient.edit(recipient_data)
            else:
                SiteRecipient.create(site_recipient_data, self)
        self.update(self.id, data)
        if include_own_site_loads_in_acquisition_report is not None:
            self.company.include_own_site_loads_in_acquisition_report = include_own_site_loads_in_acquisition_report
            self.company.save()

    @classmethod
    def get_by_name(cls, name):
        return cls.objects.filter(name__iexact=name)

    @classmethod
    def get_active_alert_by_name(cls, name):
        return cls.get_by_name(name).filter(is_active=True)

    @classmethod
    def get_by_name_and_company(cls, name, company_id):
        return cls.get_by_name(name).filter(company_id=company_id).first()

    @classmethod
    def get_active_alert_by_name_and_company(cls, name, company_id):
        return cls.get_active_alert_by_name(name).filter(company_id=company_id).first()

    def get_recipients(self, recipient_company=None):
        recipient_company = recipient_company or self.company
        communication_channel_values = set()
        for recipient in self.recipient_set.filter(is_active=True):
            communication_channel_values = {
                *communication_channel_values,
                *recipient.get_employee_communication_channel_value(
                    recipient_company, self.recipient_field)
            }
        return communication_channel_values

    def recipient_queryset(self):
        return self.recipient_set.exclude(party='own_company') if self.siterecipient_set.exists() else self.recipient_set.filter() # pylint: disable=line-too-long


class Recipient(BaseModel):
    class Meta:
        db_table = "recipients"

    KEY_CONTACT_ROLE = 'key_contact'
    COMPANY_ADMIN_ROLE = 'company_admin'
    SITE_ADMIN_ROLE = 'site_admin'
    OFFICE_ADMIN_ROLE = 'office_admin'
    OFFICE_EMPLOYEE_ROLE = 'office_employee'
    SITE_EMPLOYEE_ROLE = 'site_employee'
    DRIVER_ROLE = 'driver'

    ROLES = {
        SITE_ADMIN_ROLE: FARM_ADMIN_TYPE_ID,
        COMPANY_ADMIN_ROLE: COMPANY_ADMIN_TYPE_ID,
        KEY_CONTACT_ROLE: None,     # This will be filled in separately
        OFFICE_ADMIN_ROLE: OFFICE_ADMIN_TYPE_ID,
        OFFICE_EMPLOYEE_ROLE: OFFICE_EMPLOYEE_TYPE_ID,
        SITE_EMPLOYEE_ROLE: FARM_EMPLOYEE_TYPE,
        DRIVER_ROLE: DRIVER_TYPE_ID,
    }

    alert = models.ForeignKey(Alert, on_delete=models.CASCADE)
    party = models.CharField(max_length=100, choices=RECIPIENT_PARTY_TYPES)
    employee_roles = ArrayField(models.CharField(max_length=255), null=True, blank=True, default=list)
    custom_emails = ArrayField(models.CharField(max_length=255), null=True, blank=True, default=list)
    employees = models.ManyToManyField('profiles.Employee', related_name='recipients')

    def clean(self):
        if not self.employee_roles:
            self.employee_roles = []
        if not self.custom_emails:
            self.custom_emails = []
        super().clean()

    @classmethod
    def persist(cls, data):
        from core.profiles.models import Employee
        employee_ids = data.pop('employees', None)
        recipient = cls(**data)
        recipient.clean()
        recipient.save()
        if employee_ids and not recipient.errors:
            recipient.employees.set(Employee.objects.filter(id__in=employee_ids))
        return recipient

    def edit(self, data):
        from core.profiles.models import Employee
        employee_ids = data.pop('employees', [])
        data['employee_roles'] = data.get('employee_roles', []) or []
        Recipient.update(instance=self, data=data)
        self.employees.set(Employee.objects.filter(id__in=employee_ids))

    def get_employee_communication_channel_value(
        self, recipient_company, attribute, entity=None, vendor_dec=None, site=None, role=None
    ):
        """
        1. company should be the recipient
        2. attribute should be employee.<field> -- can be email or mobile (future) and employee id for mobile push
           notification
        """

        if recipient_company.is_system:
            return []

        employee_ids = self.get_employee_ids(recipient_company, entity, vendor_dec) or []
        if attribute == MOBILE_PUSH_NOTIFICATION_CHANNEL:
            return employee_ids

        employee_ids_with_active_alerts = employee_ids
        if self.alert.name in [
            PICKUP_CONFIRMATION_ALERT_CODE,
            DELIVERY_CONFIRMATION_ALERT_CODE,
            SLOT_BOOKING_UPDATE_ALERT_CODE
        ]:
            employee_ids_with_active_alerts = self._filter_employees_with_active_alerts(
                employee_ids=employee_ids,
                alert_name=self.alert.name,
                channel=attribute,
                role=role,
                site=site
            )
        from core.profiles.models import Employee
        employees = Employee.objects.filter(id__in=employee_ids_with_active_alerts)
        values = list(set(employees.filter(**{f"{attribute}__isnull": False}).values_list(attribute, flat=True)))
        return values

    def _filter_employees_with_active_alerts(
        self, employee_ids, alert_name, channel, role, site
    ): # pylint: disable=too-many-locals, too-many-branches
        receive_alert = ReceiveAlert.objects.filter(
            name=alert_name, channel=channel
        ).first()

        employee_alerts_qs = EmployeeAlert.objects.filter(
            employee_id__in=employee_ids,
            alert=receive_alert
        ).values('employee_id', 'is_active', 'roles')
        employee_alerts = {alert['employee_id']: alert for alert in employee_alerts_qs}

        active_alert_employees = []
        missing_alert_employees = []
        for employee_id in employee_ids:
            if employee_id not in employee_alerts:
                missing_alert_employees.append(employee_id)
            else:
                alert = employee_alerts[employee_id]
                if alert['is_active'] and role in alert['roles']:
                    active_alert_employees.append(employee_id)

        active_employee_ids = [
            *active_alert_employees,
            *missing_alert_employees
        ]
        if not site:
            return active_employee_ids

        alerts = EmployeeAlert.objects.filter(
            employee_id__in=active_alert_employees,
            alert=receive_alert,
            is_active=True
        ).prefetch_related('sites')
        for alert in alerts:
            if alert.include_internal_sites:
                site_ids = alert.sites.all().only('id').values_list('id', flat=True)
                from core.farms.models import Farm
                farm_site_ids = Farm.objects.filter(
                    company_id=alert.employee.company_id
                ).values_list('id', flat=True)
                if not site_ids:
                    site_ids = farm_site_ids
                if site_ids and get(site, 'id') in farm_site_ids and not get(site, 'id') in site_ids:
                    active_employee_ids.remove(alert.employee_id)

            if not alert.include_internal_sites:
                alert_site_company = get(site, 'company_id')
                if alert.employee.company_id == alert_site_company:
                    active_employee_ids.remove(alert.employee_id)

            if not alert.include_external_sites:
                alert_site_company = get(site, 'company_id')
                if alert.employee.company_id != alert_site_company:
                    active_employee_ids.remove(alert.employee_id)

        return active_employee_ids

    def driver_associated_with_entity(self, entity, vendor_dec=None):
        if self.alert.name in [PICKUP_CONFIRMATION_SMS_ALERT_CODE, DELIVERY_CONFIRMATION_SMS_ALERT_CODE]:
            driver = get(entity, 'driver') or get(entity, 'movement.driver')
        else:
            driver = get(vendor_dec, 'driver') or get(entity, 'driver')
        if driver:
            provider_ids = []
            if self.party == 'freight_provider':
                provider_ids.append(get(vendor_dec, 'provider_id') or get(entity, 'provider_id'))
            elif self.party == 'sub_freight_provider':
                provider_ids.append(get(entity, 'planned_truck.company_id'))
            elif self.party == 'truck_owner':
                slot = get(entity, 'movement.outload_slot') or get(entity, 'movement.inload_slot')
                truck_company_id = get(entity, 'truck.company_id') if get(entity, 'driver') else get(slot, 'truck.company_id') # pylint: disable=line-too-long
                provider_ids.append(truck_company_id)
            if driver.company_id in compact(provider_ids):
                return driver.id

    def get_employee_ids(self, recipient_company, entity=None,  vendor_dec=None):
        type_ids = []
        driver_id = None
        for role in self.employee_roles:
            if role != KEY_CONTACT_ROLE:
                if role in ['driver'] and self.party in ['freight_provider', 'sub_freight_provider', 'truck_owner']:
                    driver_id = self.driver_associated_with_entity(entity, vendor_dec)
                else:
                    type_ids.append(self.ROLES.get(role))
        employee_ids = list(recipient_company.employee_set.filter(type_id__in=type_ids).values_list('id', flat=True))
        if driver_id and recipient_company.employee_set.filter(id=driver_id).exists():
            employee_ids.append(driver_id)

        if KEY_CONTACT_ROLE in self.employee_roles and self.alert.company_id:  # pylint: disable=unsupported-membership-test
            from core.key_contacts.models import KeyContact
            key_contact_employee_id = KeyContact.get_for_company(self.alert.company_id, recipient_company.id)
            employee_ids.append(key_contact_employee_id) if key_contact_employee_id else None
        return [*self.employees.values_list('id', flat=True), *employee_ids]

    def get_recipients_for_company(self, company, recipients, movement=None, vendor_dec=None, site=None, role=None):
        company_recipients = self.get_employee_communication_channel_value(
            company, self.alert.recipient_field, movement, vendor_dec, site, role
        )
        if company.id in recipients:
            recipients[company.id] = [*{*recipients[company.id], *company_recipients}]
        else:
            recipients[company.id] = company_recipients
    @property
    def is_party_own_company(self):
        return self.party == 'own_company'


class SiteRecipient(BaseModel):
    class Meta:
        db_table = "site_recipients"

    site = models.ForeignKey('farms.Farm', on_delete=models.CASCADE)
    alert = models.ForeignKey(Alert, on_delete=models.CASCADE)
    recipient = models.ForeignKey(Recipient, on_delete=models.CASCADE)

    @classmethod
    def persist(cls, data):
        site_recipient = cls(**data)
        site_recipient.save()
        if site_recipient.errors:
            raise Exception(site_recipient.errors)
        return site_recipient

    @classmethod
    def remove_existing_recipients(cls, alert_id):
        site_recipients = cls.objects.filter(alert_id=alert_id)
        for site_recipient in site_recipients:
            site_recipient.recipient.delete()
        cls.objects.filter(alert_id=alert_id).delete()

    @classmethod
    def create(cls, data, alert):  # pylint: disable=arguments-differ
        recipient_data = data.pop('recipient')
        recipient_data['alert'] = alert
        recipient = Recipient.persist(recipient_data)
        if recipient and not recipient.errors:
            data['alert'] = alert
            data['recipient_id'] = recipient.id
            SiteRecipient.persist(data)

    @classmethod
    def group_employees_based_on_site(cls, alert):
        from collections import defaultdict

        def get_sites_for_each_employee(alert):
            employee_sites = {}
            for site_recipient in cls.objects.filter(alert_id=alert.id):
                recipient = site_recipient.recipient
                type_ids = [recipient.ROLES.get(role) for role in recipient.employee_roles]
                employee_ids = recipient.employees.values_list('id', flat=True)
                selected_role_employee_ids = alert.company.employee_set.filter(
                    type_id__in=type_ids).values_list('id', flat=True)
                for emp_id in list(employee_ids.union(selected_role_employee_ids)):
                    if get(employee_sites, emp_id):
                        employee_sites[emp_id].append(site_recipient.site_id)
                    else:
                        employee_sites[emp_id] = [site_recipient.site_id]
            return employee_sites

        employee_sites = get_sites_for_each_employee(alert)
        site_groups = defaultdict(list)
        for employee_id, site_ids in employee_sites.items():
            site_ids_tuple = tuple(sorted(site_ids))
            site_groups[site_ids_tuple].append(employee_id)
        return site_groups

class AlertRole(models.TextChoices):
    CUSTOMER = 'customer', 'Customer'
    FREIGHT_PROVIDER = 'freight_provider', 'Freight Provider'
    PICKUP_SITE = 'pickup_site', 'Pickup Site'
    DELIVERY_SITE = 'delivery_site', 'Delivery Site'
    SUB_FREIGHT_PROVIDER = 'sub_freight_provider', 'Sub Freight Provider'
    STOCK_OWNER = 'stock_owner', 'Stock Owner'
    OWN_COMPANY = 'own_company', 'Own Company'
    OWN_COMPANY_SITE = 'own_company_site', 'Own Company Site'
    SELLER = 'seller', 'Seller'
    BUYER = 'buyer', 'Buyer'
    CREATOR = 'creator', 'Creator'

class ReceiveAlert(BaseModel):
    CHANNEL_ATTRIBUTES = {
        EMAIL_CHANNEL: EMAIL_CHANNEL,
        SMS_CHANNEL: 'mobile',
        MOBILE_PUSH_NOTIFICATION_CHANNEL: MOBILE_PUSH_NOTIFICATION_CHANNEL
    }
    class Meta:
        db_table = "receive_alerts"

    name = models.CharField(max_length=100, choices=ALERT_NAMES)
    channel = models.CharField(max_length=100, choices=CHANNEL_TYPES)

class EmployeeAlert(BaseModel):
    class Meta:
        db_table = "employee_alerts"
        unique_together = ('employee', 'alert')

    employee = models.ForeignKey('profiles.Employee', on_delete=models.CASCADE)
    alert = models.ForeignKey('alerts.ReceiveAlert', on_delete=models.CASCADE)
    include_internal_sites = models.BooleanField(default=True)
    include_external_sites = models.BooleanField(default=True)
    sites = models.ManyToManyField('farms.Farm', through='EmployeeAlertSite')
    roles = ArrayField(
        models.CharField(max_length=50, choices=AlertRole.choices),
        default=list,
        blank=True
    )

    @classmethod
    def create_default_alerts_for_employee(cls, employee, existing_alerts, possible_alerts):
        existing_alert_ids = set(
            existing_alerts.values_list('alert_id', flat=True)
        )
        missing_alerts = possible_alerts.exclude(
            id__in=existing_alert_ids
        )

        alerts_to_create = []
        for alert in missing_alerts:
            roles = RECEIVE_ALERT_ROLES.get(alert.name, [])
            alerts_to_create.append(
                EmployeeAlert(
                    employee_id=employee.id,
                    alert=alert,
                    roles=roles,
                    include_internal_sites=True,
                    include_external_sites=True
                )
            )

        if alerts_to_create:
            EmployeeAlert.objects.bulk_create(alerts_to_create)

    @property
    def alert_name(self):
        return self.alert.name

    @property
    def alert_display_name(self):
        return to_display_attr(RECEIVE_ALERT_NAMES, self.alert.name)

    @property
    def description(self):
        return RECEIVE_ALERT_DESCRIPTION.get(self.alert.name)

    @property
    def recipient_field(self):
        return self.alert.CHANNEL_ATTRIBUTES.get(self.alert.channel)

    @property
    def alert_type(self):
        return to_display_attr(CHANNEL_TYPES, self.alert.channel)

    def update_alert(self, data):
        from core.farms.models import Farm

        site_ids = data.pop('sites', [])

        self.is_active = data.pop('is_active', self.is_active)
        self.include_internal_sites = data.pop('include_internal_sites', self.include_internal_sites)
        self.include_external_sites = data.pop('include_external_sites', self.include_external_sites)
        self.roles = data.pop('roles', self.roles if hasattr(self, 'roles') else [])

        self.save()

        if self.include_internal_sites:
            current_sites = EmployeeAlertSite.objects.filter(alert=self)
            current_sites.exclude(site_id__in=site_ids).delete()

            new_site_ids = set(site_ids) - set(current_sites.values_list('site_id', flat=True))
            if new_site_ids:
                internal_sites = Farm.objects.filter(
                    id__in=new_site_ids,
                    company_id=self.employee.company_id
                )
                through_objects = [
                    EmployeeAlertSite(
                        alert=self,
                        site=site
                    )
                    for site in internal_sites
                ]
                EmployeeAlertSite.objects.bulk_create(through_objects)
        else:
            EmployeeAlertSite.objects.filter(alert=self).delete()

class EmployeeAlertSite(BaseModel):
    class Meta:
        db_table = "employee_alert_sites"
        unique_together = ('alert', 'site')
        indexes = [
            models.Index(fields=['alert', 'site'], name='emp_alert_site_idx')
        ]

    alert = models.ForeignKey(EmployeeAlert, on_delete=models.CASCADE)
    site = models.ForeignKey('farms.Farm', on_delete=models.CASCADE)

    FILLABLES = [
        'alert_id',
        'site_id'
    ]
