# Generated by Django 4.0.6 on 2022-08-03 03:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('banks', '0014_alter_bank_bsb_number_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='historicalbank',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical bank', 'verbose_name_plural': 'historical banks'},
        ),
        migrations.AlterField(
            model_name='bank',
            name='bsb_number',
            field=models.CharField(max_length=15, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='bank',
            name='code',
            field=models.CharField(max_length=10, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='historicalbank',
            name='bsb_number',
            field=models.Char<PERSON>ield(max_length=15, null=True, blank=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='historicalbank',
            name='code',
            field=models.CharField(max_length=10, null=True, blank=True),
        ),
        migrations.AlterField(
            model_name='historicalbank',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterUniqueTogether(
            name='bank',
            unique_together={('name', 'code', 'bsb_number')},
        ),
    ]
