from django.core.management import BaseCommand

from core.common.utils import is_production, is_staging
from core.countries.models import CountryCredential as Credential


class Command(BaseCommand):
    help = 'Setup Country Credentials'

    def handle(self, *args, **options):
        credentials = []
        if is_production():
            credentials = [
                Credential(country_id=1, name=Credential.SMS_SERVICE, username='agrichain', password='BwGyrMSf'),
                Credential(country_id=2, name=Credential.SMS_SERVICE, username='agrichainus', password='67bn3w6gh3q2^k')
            ]
        if is_staging():
            credentials = [
                Credential(
                    country_id=1, name=Credential.SMS_SERVICE, username='agrichainautest1', password='5Bnes8hj3w9Bnqt'),
                Credential(
                    country_id=2, name=Credential.SMS_SERVICE, username='agrichaintest1', password='6H3Wq9nm3ghWa6')
            ]

        new_credentials = []

        if credentials:
            for credential in credentials:
                if not Credential.objects.filter(country_id=credential.country_id, name=credential.name).exists():
                    new_credentials.append(credential)

        if new_credentials:
            print("**Creating Country Credentials**")
            Credential.objects.bulk_create(new_credentials)
