# Generated by Django 2.1.7 on 2019-06-21 09:45

from django.db import migrations

def update_settings_default_fields(apps, schema_editor):
    SiteManagementSettings = apps.get_model('company_sites', 'SiteManagementSettings')
    for settings in SiteManagementSettings.objects.all():
        settings.fields.append({
            "show": True,
            "defaultValue": None,
            "mandatory": False,
            "label": "Sub Freight Provider",
            "name": "Sub Freight Provider",
            "customisable": True,
            "id": "subFreightProviderId"
        })
        settings.save()

class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0050_auto_20190620_1112'),
    ]

    operations = [
        migrations.RunPython(update_settings_default_fields)
    ]
