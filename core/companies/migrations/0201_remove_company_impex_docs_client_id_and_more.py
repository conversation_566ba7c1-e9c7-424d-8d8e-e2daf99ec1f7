# Generated by Django 4.2.15 on 2025-02-07 03:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0200_company_impex_docs_user_email_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='company',
            name='impex_docs_client_id',
        ),
        migrations.RemoveField(
            model_name='company',
            name='impex_docs_client_secret',
        ),
        migrations.RemoveField(
            model_name='company',
            name='impex_docs_user_email',
        ),
        migrations.RemoveField(
            model_name='company',
            name='impex_docs_user_id',
        ),
        migrations.RemoveField(
            model_name='historicalcompany',
            name='impex_docs_client_id',
        ),
        migrations.RemoveField(
            model_name='historicalcompany',
            name='impex_docs_client_secret',
        ),
        migrations.RemoveField(
            model_name='historicalcompany',
            name='impex_docs_user_email',
        ),
        migrations.RemoveField(
            model_name='historicalcompany',
            name='impex_docs_user_id',
        ),
        migrations.AddField(
            model_name='impexdocsconnection',
            name='impex_docs_client_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='impexdocsconnection',
            name='impex_docs_client_secret',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='impexdocsconnection',
            name='impex_docs_user_email',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='impexdocsconnection',
            name='impex_docs_user_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='impexdocsconnection',
            name='company',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='companies.company'),
        ),
    ]
