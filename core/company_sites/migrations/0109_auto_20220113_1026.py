# Generated by Django 3.2.10 on 2022-01-13 10:26

import core.validation.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0108_sitemanagementsettings_contract_csv_mapping_info'),
    ]

    operations = [
        migrations.AddField(
            model_name='freightslot',
            name='season',
            field=models.CharField(blank=True, max_length=5, null=True, validators=[core.validation.validators.season_validator]),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='season',
            field=models.CharField(blank=True, max_length=5, null=True, validators=[core.validation.validators.season_validator]),
        ),
    ]
