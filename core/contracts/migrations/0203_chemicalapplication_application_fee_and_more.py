# Generated by Django 4.1.13 on 2024-04-11 10:15

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0051_alter_commodity_type_alter_historicalcommodity_type'),
        ('contracts', '0202_chemicalapplication_commodity_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='chemicalapplication',
            name='application_fee',
            field=models.FloatField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='chemicalapplication',
            name='grade',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='commodities.grade'),
        ),
        migrations.AddField(
            model_name='historicalchemicalapplication',
            name='application_fee',
            field=models.FloatField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='historicalchemicalapplication',
            name='grade',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='commodities.grade'),
        ),
        migrations.AlterField(
            model_name='chemicalapplication',
            name='fee',
            field=models.FloatField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='chemicalapplication',
            name='price',
            field=models.FloatField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='historicalchemicalapplication',
            name='fee',
            field=models.FloatField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='historicalchemicalapplication',
            name='price',
            field=models.FloatField(blank=True, default=0, null=True),
        ),
    ]
