# Generated by Django 2.1.7 on 2019-05-07 17:51

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0025_auto_20190429_1548'),
    ]

    operations = [
        migrations.AddField(
            model_name='freightslot',
            name='siblings',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=[], null=True),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='siblings',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=[], null=True),
        ),
    ]
