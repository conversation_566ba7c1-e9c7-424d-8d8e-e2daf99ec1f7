import inflection
from django.db import models
from pydash import get
from rest_framework import status
from rest_framework.generics import ListAPIView, get_object_or_404
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView


from core.common.models import CustomPageNumberPagination
from core.common.constants import AEST_TZ
from core.common.exceptions import Http403
from core.common.utils import decode_string
from core.common.views.web_views import BaseHistoryView
from core.contract_bids.models import ContractBid
from core.contract_bids.serializers import ContractBidSerializer
from core.countries.constants import AUSTRALIA_COUNTRY_ID
from core.countries.models import Country


class ContractBidsView(ListAPIView):
    permission_classes = (AllowAny,)
    serializer_class = ContractBidSerializer
    pagination_class = CustomPageNumberPagination
    def get_queryset(self):  # pylint: disable=too-many-locals,too-many-statements,too-many-branches
        params = self.request.query_params.dict().copy()
        user = self.request.user
        country_id = get(user, 'country_id')
        if not country_id:
            country_id = get(
                Country.get_country_from_timezone(self.request.META.get('HTTP_REFERER_TZ', AEST_TZ)), 'id'
            ) or AUSTRALIA_COUNTRY_ID
        search_str = params.pop('search', None)
        params.pop('page', None)
        params.pop('page_size', None)
        order_by = params.pop('order_by', 'created_at')
        order = params.pop('order', 'desc')
        tab = params.pop('tab', None)

        if tab == 'my_active':
            contract_bids = ContractBid.active.filter(created_by__company_id=user.company_id)
        elif tab == 'archived':
            contract_bids = ContractBid.archived.filter(created_by__company_id=user.company_id)
        else:
            contract_bids = ContractBid.active.filter(commodity__country_id=country_id).filter(
                models.Q(group__isnull=True) |
                models.Q(group__isnull=False, group__companies=user.company_id)
            ).exclude(buyer__company_id=user.company_id)

        if search_str:
            search_str = decode_string(search_str)
            contract_bids = contract_bids.filter(
                models.Q(status__icontains=search_str) |
                models.Q(status__icontains=search_str.replace(' ', '_')) |
                models.Q(buyer__company__business_name__icontains=search_str) |
                models.Q(start_date_time__icontains=search_str) |
                models.Q(end_date_time__icontains=search_str) |
                models.Q(commodity__name__icontains=search_str) |
                models.Q(grade__name__icontains=search_str) |
                models.Q(season__icontains=search_str) |
                models.Q(price__icontains=search_str) |
                models.Q(track__icontains=search_str) |
                models.Q(created_at__icontains=search_str) |
                models.Q(limit__icontains=search_str) |
                models.Q(site__name__icontains=search_str) |
                models.Q(site__company__business_name__icontains=search_str)
            )

        order_by_map = {
            'site_name': ['site__company__business_name'],
            'track': ['track'],
            'id': ['id'],
            'buyer.company_name': ['buyer__company__business_name'],
            'commodity_name': ['commodity__name'],
            'grade_name': ['grade__name'],
            'season': ['season'],
            'price': ['price'],
            'payment_term.name': ['payment_term__name']
        }

        if order_by:
            _order_by = order_by_map.get(
                inflection.underscore(order_by), ['created_at'])
            order = '-' if order == 'desc' else ''
            contract_bids = contract_bids.order_by(*[order + f for f in _order_by])
        return contract_bids.select_related('site', 'commodity', 'grade', 'buyer', 'payment_term', 'group')

    def post(self, request):
        try:
            contract_bid = ContractBid.persist(request.data.copy())
            return Response(contract_bid.to_dict(), status=status.HTTP_201_CREATED)
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class ContractBidView(APIView):
    serializer_class = ContractBidSerializer

    def get_object(self, contract_bid_id):
        contract_bid = get_object_or_404(ContractBid.objects, id=contract_bid_id)
        if contract_bid and contract_bid.can_view(self.request.user):
            return contract_bid
        raise Http403()

    def get(self, request, contract_bid_id):
        contract_bid = self.get_object(contract_bid_id)
        if contract_bid:
            return Response(ContractBidSerializer(contract_bid, context={'request': request}).data,
                            status=status.HTTP_200_OK)

    def put(self, request, contract_bid_id):
        try:
            contract_bid = self.get_object(contract_bid_id)
            if request.data.get('status', None):
                contract_bid.status = 'inactive'
                contract_bid.save()
                return Response(status=status.HTTP_204_NO_CONTENT)
            contract_bid = ContractBid.update(contract_bid_id, data=request.data, instance=contract_bid)
            return Response(ContractBidSerializer(contract_bid, context={'request': request}).data,
                            status=status.HTTP_200_OK)
        except Exception as ex: # pylint: disable=broad-except
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class ContractBidContractView(APIView):
    def post(self, request, contract_bid_id):
        try:
            contract_bid = get_object_or_404(ContractBid.active, id=contract_bid_id)
            contract = contract_bid.create_contract(request.data, request.user)
            return Response(contract.to_dict(), status=status.HTTP_201_CREATED)
        except Exception as ex: # pylint: disable=broad-except
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class ContractBidHistoryView(BaseHistoryView):
    model = ContractBid
    lookup_url_kwarg = 'contract_bid_id'
