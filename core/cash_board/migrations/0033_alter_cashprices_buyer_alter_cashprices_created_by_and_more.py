# Generated by Django 4.0.8 on 2023-02-24 06:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('farms', '0101_auto_20230117_0425'),
        ('contracts', '0181_alter_contractpricepoint_name_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('cash_board', '0032_auto_20221111_0813'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cashprices',
            name='buyer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='cash_prices_buyer', to='contracts.party'),
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='site',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='farms.farm'),
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='updated_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='cashpricesmapping',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='cashpricesmapping',
            name='updated_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL),
        ),
    ]
