# Generated by Django 2.0.7 on 2018-08-16 10:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('companies', '0001_initial'),
        ('marketzones', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('regions', '0001_initial'),
        ('locations', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalcompanytype',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalcompanytype',
            name='history_user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalcompanytype',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='address',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='locations.Location'),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='history_user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='type',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.CompanyType'),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalbhcsite',
            name='address',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='locations.Location'),
        ),
        migrations.AddField(
            model_name='historicalbhcsite',
            name='company',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.Company'),
        ),
        migrations.AddField(
            model_name='historicalbhcsite',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalbhcsite',
            name='history_user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalbhcsite',
            name='market_zone',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='marketzones.Marketzone'),
        ),
        migrations.AddField(
            model_name='historicalbhcsite',
            name='region',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='regions.Region'),
        ),
        migrations.AddField(
            model_name='historicalbhcsite',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicaladdedcompany',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicaladdedcompany',
            name='history_user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicaladdedcompany',
            name='object_company',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.Company'),
        ),
        migrations.AddField(
            model_name='historicaladdedcompany',
            name='subject_company',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.Company'),
        ),
        migrations.AddField(
            model_name='historicaladdedcompany',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='companytype',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_companytype_related_created_by', related_query_name='companies_companytypes_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='companytype',
            name='updated_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_companytype_related_updated_by', related_query_name='companies_companytypes_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='company',
            name='address',
            field=models.OneToOneField(on_delete=django.db.models.deletion.DO_NOTHING, related_name='company', to='locations.Location'),
        ),
        migrations.AddField(
            model_name='company',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_company_related_created_by', related_query_name='companies_companys_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='company',
            name='type',
            field=models.ForeignKey(default=5, on_delete=django.db.models.deletion.DO_NOTHING, to='companies.CompanyType'),
        ),
        migrations.AddField(
            model_name='company',
            name='updated_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_company_related_updated_by', related_query_name='companies_companys_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='bhcsite',
            name='address',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='bhc_site_location', to='locations.Location'),
        ),
        migrations.AddField(
            model_name='bhcsite',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.Company'),
        ),
        migrations.AddField(
            model_name='bhcsite',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_bhcsite_related_created_by', related_query_name='companies_bhcsites_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='bhcsite',
            name='market_zone',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='marketzones.Marketzone'),
        ),
        migrations.AddField(
            model_name='bhcsite',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='regions.Region'),
        ),
        migrations.AddField(
            model_name='bhcsite',
            name='updated_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_bhcsite_related_updated_by', related_query_name='companies_bhcsites_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='addedcompany',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_addedcompany_related_created_by', related_query_name='companies_addedcompanys_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='addedcompany',
            name='object_company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='added_company_set_object', to='companies.Company'),
        ),
        migrations.AddField(
            model_name='addedcompany',
            name='subject_company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='added_company_set_subject', to='companies.Company'),
        ),
        migrations.AddField(
            model_name='addedcompany',
            name='updated_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_addedcompany_related_updated_by', related_query_name='companies_addedcompanys_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='addedcompany',
            unique_together={('subject_company', 'object_company')},
        ),
    ]
