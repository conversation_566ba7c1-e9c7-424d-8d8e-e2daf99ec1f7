from datetime import timedelta

import factory
from django.utils import timezone
from factory import SubFactory

from core.cash_board.models import CashPrices
from core.contracts.tests.factories import PartyFactory
from core.farms.tests.factories import FarmFactory


class CashPriceFactory(factory.django.DjangoModelFactory):
    site = SubFactory(FarmFactory)
    commodity_id = 2  # barley
    season = '22/23'
    grade_id = 36  # BAR1
    payment_term_id = 1
    price = 500
    limit = 100
    track = 'MGC'
    buyer = SubFactory(PartyFactory, role='Buyer')
    start_date_time = timezone.now()
    end_date_time = (timezone.now() + timedelta(days=5))

    class Meta:
        model = CashPrices
