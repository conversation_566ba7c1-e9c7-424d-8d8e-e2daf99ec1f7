# Generated by Django 4.1.13 on 2024-05-20 05:32

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0051_alter_commodity_type_alter_historicalcommodity_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='commodity',
            name='specs_array',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.JSONField(), blank=True, default=list, null=True, size=None),
        ),
        migrations.AddField(
            model_name='grade',
            name='specs_array',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.JSONField(), blank=True, default=list, null=True, size=None),
        ),
        migrations.AddField(
            model_name='historicalcommodity',
            name='specs_array',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.JSO<PERSON>ield(), blank=True, default=list, null=True, size=None),
        ),
        migrations.AddField(
            model_name='historicalgrade',
            name='specs_array',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.JSONField(), blank=True, default=list, null=True, size=None),
        ),
    ]
