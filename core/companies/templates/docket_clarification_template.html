{% load static %}
{% load app_filters %}
{% with date_time_format="M d Y, h:i A" date_format="M d, Y" %}
<html>
<head>
    <meta charset="UTF-8">
    <style>
        table {
            border-collapse: collapse;
            width: 60%;
        }

        th, td {
            padding: 4px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        tr:hover {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <p>Dear user,</p>
    {% if is_daily_alert %}
        <p>Please find below a list of movements in the last 24 hours with dockets that require your attention.</p>
    {% else %}
        <p>This is a weekly summary of all unresolved docket issues flagged for your movements till date.</p>
        <p>Please find attached a list of movements with dockets that require your attention.</p>
    {% endif %}
    <p>These issues have been flagged by the AgriChain team while processing the data. Kindly check the clarifications required at the earliest by clicking on the "Resolve" link against each movement in the report, to ensure that accurate data is created and maintained for these movements.</p>
    {% if is_daily_alert %}
        <br/>
        <table border="1">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Identifier</th>
                    <th>Load Type</th>
                    <th>Pickup Site</th>
                    <th>Category</th>
                    <th>Sub Category</th>
                    <th>Comments</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                {% for item in docket_data %}
                <tr>
                    <td>{{ item.date }}</td>
                    <td>{{ item.identifier }}</td>
                    <td>{{ item.load_type }}</td>
                    <td>{{ item.pickup_site}}</td>
                    <td>{{ item.category }}</td>
                    <td>{{ item.sub_category }}</td>
                    <td>{{ item.comments }}</td>
                    <td><a href={{ item.action }}>Resolve</a></td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        </br>
    {% endif %}
    <p>Please note: You have been subscribed to these alerts by someone within {{ customer_company_name }}. Please get in touch with the company admin to stop receiving these alerts. </br> </br>
    Thank you,<br/>
       Team AgriChain<br/>
    </p>
</body>
</html>
{% endwith %}
