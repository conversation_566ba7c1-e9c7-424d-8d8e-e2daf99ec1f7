# Generated by Django 3.2.6 on 2021-11-29 04:21

from django.db import migrations


# update lupins variety reference to correct commodity
def update_lupins(apps, schema_editor):
    Load = apps.get_model('loads', 'Load')
    Contract = apps.get_model('contracts', 'Contract')
    TitleTransfer = apps.get_model('contracts', 'TitleTransfer')
    FreightContract = apps.get_model('freights', 'FreightContract')
    FreightOrder = apps.get_model('freights', 'FreightOrder')
    Storage = apps.get_model('farms', 'Storage')
    CommodityVendorDec = apps.get_model('vendor_decs', 'CommodityVendorDec')

    albus_variety_ids = [692, 710, 712, 718, 721]
    angustifolious_variety_ids = [
        693, 694, 695, 696, 697, 698, 699, 700, 701, 702, 703, 705, 706, 707, 708, 709, 711, 713, 714, 715, 716, 717,
        719, 720, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733
    ]

    for klass in [
        Load, Contract, TitleTransfer, Storage, CommodityVendorDec
    ]:
        klass.objects.filter(variety_id__in=angustifolious_variety_ids).update(commodity_id=54)
        klass.objects.filter(variety_id__in=angustifolious_variety_ids, grade_id=126).update(grade_id=72)
        klass.objects.filter(variety_id__in=angustifolious_variety_ids, grade_id=265).update(grade_id=271)
        klass.objects.filter(variety_id__in=albus_variety_ids, grade_id=72).update(commodity_id=13, grade_id=126)

    for klass in [
        FreightContract, FreightOrder
    ]:
        klass.objects.filter(variety_id__in=angustifolious_variety_ids).update(commodity_id=54)
        klass.objects.filter(variety_id__in=angustifolious_variety_ids, planned_grade_id=126).update(
            planned_grade_id=72)
        klass.objects.filter(variety_id__in=angustifolious_variety_ids, planned_grade_id=265).update(
            planned_grade_id=271)
        klass.objects.filter(variety_id__in=albus_variety_ids, planned_grade_id=72).update(
            commodity_id=13, planned_grade_id=126)


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0024_auto_20211129_0414'),
    ]

    operations = [
        migrations.RunPython(update_lupins)
    ]
