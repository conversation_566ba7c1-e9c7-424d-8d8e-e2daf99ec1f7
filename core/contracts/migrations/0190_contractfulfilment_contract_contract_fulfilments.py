# Generated by Django 4.1.10 on 2023-10-09 06:35

import core.common.models
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0189_historicaltitletransfer_external_system_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContractFulfilment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tonnage', core.common.models.RoundedFloatField()),
                ('purchase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salecontractfulfilment_set', to='contracts.contract')),
                ('sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchasecontractfulfilment_set', to='contracts.contract')),
            ],
            options={
                'unique_together': {('purchase', 'sale')},
            },
        ),
        migrations.AddField(
            model_name='contract',
            name='contract_fulfilments',
            field=models.ManyToManyField(related_name='purchase_fulfilment_contracts', through='contracts.ContractFulfilment', to='contracts.contract'),
        ),
    ]
