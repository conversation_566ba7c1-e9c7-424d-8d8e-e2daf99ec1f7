from functools import wraps
from core.services.internal.errbit import ERRBIT_LOGGER


def one_history(fn):
    @wraps(fn)
    def wrapper(cls, *args, **kwargs):
        instance = None
        cls.pause_history()

        try:
            instance = fn(cls, *args, **kwargs)
        except Exception as ex:  # pylint: disable=broad-except
            ERRBIT_LOGGER.log(ex)
        finally:
            cls.unpause_history()
            if instance and not instance.errors:
                cls.history.bulk_history_create([instance])

        return instance

    return wrapper
