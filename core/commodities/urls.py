from django.urls import path
from django.views.decorators.cache import cache_page

from .views import (
    CommoditiesView, CommoditiesMobileView, CommodityMinimalView, CommodityGradesView, GradeMinimalView,
    VarietyMinimalView, GradeView, InterChangeableCommoditiesView, MaterialListView, VarietiesView,
    GradesMobileView, VarietiesMobileView
)

HOURS_24 = 60 * 60 * 24

app_name = 'core.commodities'
urlpatterns = [
    path('', cache_page(timeout=HOURS_24, key_prefix='commodities_all')(CommoditiesView.as_view()), name='commodity'),
    path('materials/', MaterialListView.as_view(), name='material'),
    path('<int:commodity_id>/', CommodityMinimalView.as_view(), name='commodity'),
    path('grades/<int:grade_id>/', GradeMinimalView.as_view(), name='grades'),
    path('varieties/<int:variety_id>/', VarietyMinimalView.as_view(), name='grades'),
    path('mobile/', CommoditiesMobileView.as_view(), name='commodity-mobile'),
    path('grades/mobile/', GradesMobileView.as_view(), name='grades-mobile'),
    path('varieties/mobile/', VarietiesMobileView.as_view(), name='varieties-mobile'),
    path(
        '<int:commodity_id>/grades/',
        CommodityGradesView.as_view(),
        name='commodity-grades',
    ),
    path(
        'grades/all/',
        cache_page(timeout=HOURS_24, key_prefix='grades_all')(GradeView.as_view()), name='all-grades'
    ),
    path(
        '<int:commodity_id>/interchangeable-commodities/',
        InterChangeableCommoditiesView.as_view(),
        name='interchangeable-commodities'
    ),
    path(
        'varieties/',
        VarietiesView.as_view(),
        name='variety',
    ),
]
