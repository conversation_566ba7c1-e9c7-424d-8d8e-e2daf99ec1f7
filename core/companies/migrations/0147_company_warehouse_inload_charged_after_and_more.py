# Generated by Django 4.1.9 on 2023-09-05 11:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0146_merge_20230818_0605'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='warehouse_inload_charged_after',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='warehouse_inload_charged_after',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='warehouse_charged_at',
            field=models.CharField(choices=[('inload', 'Inload'), ('outload', 'Outload'), ('outload/transfer', 'Outload/Transfer(whichever earlier)'), ('outload_or_transfer_until_date', 'Outload/Transfer until a specified date')], default='outload', max_length=32),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='warehouse_charged_at',
            field=models.CharField(choices=[('inload', 'Inload'), ('outload', 'Outload'), ('outload/transfer', 'Outload/Transfer(whichever earlier)'), ('outload_or_transfer_until_date', 'Outload/Transfer until a specified date')], default='outload', max_length=32),
        ),
    ]
