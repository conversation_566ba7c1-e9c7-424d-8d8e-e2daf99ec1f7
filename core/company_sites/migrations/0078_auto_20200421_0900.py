# Generated by Django 2.2 on 2020-04-21 09:00

from django.db import migrations

def update_slot_grade_ANWH(apps, schema_editor):
    FreightSlot = apps.get_model('company_sites', 'FreightSlot')
    Grade = apps.get_model('commodities', 'Grade')
    WRONG_ANWH_GRADE_ID = 5011
    RIGHT_ANWH_GRADE_ID = 193
    FreightSlot.objects.filter(grade_id=WRONG_ANWH_GRADE_ID).update(grade_id=RIGHT_ANWH_GRADE_ID)
    Grade.objects.filter(id=WRONG_ANWH_GRADE_ID).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0077_auto_20200413_0801'),
    ]

    operations = [
        migrations.RunPython(update_slot_grade_ANWH)
    ]
