# Generated by Django 2.1.11 on 2020-02-05 08:33

from django.db import migrations


def update_mobile_flow(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    Company.objects.filter(type_id__in=[2, 3]).update(mobile_flow='logistics')


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0044_auto_20200205_0832'),
    ]

    operations = [
        migrations.RunPython(update_mobile_flow)
    ]
