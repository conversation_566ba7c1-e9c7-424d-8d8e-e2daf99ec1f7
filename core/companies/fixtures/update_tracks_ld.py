import csv
from pydash import get
from core.farms.models import Farm

failures = []
empty_tracks = []

def get_data():
    f = open('/code/core/companies/fixtures/BHC.csv', 'r')
    fields = ('ABN', 'SiteName', 'SiteAddress', 'SiteLatitude', 'SiteLongitude', 'Phone', 'MarketZone', 'Region', 'State', 'Port', 'NTP', 'BHC', 'Mode', 'LD')
    data = list(csv.DictReader(f, fields))
    data = [dict(d) for d in data]
    data.pop(0)
    return data

def update_tracks():
    bhc_sites = Farm.objects.filter(mode__isnull=False)
    data = get_data();
    for d in data:
        abn = d['ABN'].strip()
        name = d['SiteName'].strip()
        company_name = d['BHC'].strip()
        site = bhc_sites.filter(name=name, company__abn=abn).first()
        if not site:
            site = bhc_sites.filter(name=name, company__business_name=company_name).first()
            if not site:
                sites = bhc_sites.filter(name=name)
                if sites.count() == 1:
                    site = sites.first()
        if site:
            if isinstance(site.tracks, dict):
                site.tracks = {**(get(site, 'tracks', {}) or {}), d['Port']: float(d['LD'])}
            else:
                site.tracks = {d['Port']: float(d['LD'])}
            site.save()
            if not site.tracks:
                empty_tracks.append({site.id: [*empty_tracks[site.id], d]})
        else:
            print("Couldn't find Site for data: ", d)
            failures.append(d)
    for site in bhc_sites:
        if isinstance(site.tracks, list):
            site.tracks = {t: None for t in site.tracks}
            site.save()
