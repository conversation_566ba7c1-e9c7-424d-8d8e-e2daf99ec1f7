from django.utils import timezone
from pydash import get
from rest_framework.serializers import ModelSerializer
from rest_framework.fields import Serializer<PERSON>ethod<PERSON><PERSON>
from rest_framework import serializers

import core
from core.common.utils import to_display_attr
from core.cash_board import constants
from core.cash_board.models import CashPrices
from core.contracts.serializers import PartyAssetNameSerializer, PaymentTermSerializer
from core.profiles.models import Employee


class CashPricesSerializer(ModelSerializer):
    site_name = serializers.CharField(source='site.display_name')
    site_company_id = serializers.IntegerField(source='site.company_id')
    commodity_name = serializers.CharField(source='commodity.display_name')
    buyer = SerializerMethodField()
    payment_term = PaymentTermSerializer()
    status_display_name = SerializerMethodField()
    company_admins_for_site = SerializerMethodField()
    is_commodity_sustainable = serializers.BooleanField(source='sustainable_commodity')
    price_unit = serializers.CharField(source='commodity.price_unit')

    class Meta:
        model = CashPrices
        fields = (
            'id', 'site_id', 'commodity_name', 'site_name', 'end_date_time', 'buyer',
            'commodity_id', 'season', 'grade_id', 'limit', 'price', 'start_date_time',
            'limit_remaining', 'grade_name', 'track', 'payment_term',
            'status', 'status_display_name', 'payment_scale', 'site_company_id', 'company_admins_for_site',
            'is_commodity_sustainable', 'price_unit'
        )

    def get_buyer(self, obj):
        return PartyAssetNameSerializer(obj.buyer).data

    def get_status_display_name(self, obj):
        if obj.status == 'inactive' or (obj.end_date_time <= timezone.now() and obj.status == 'active'):
            return to_display_attr(constants.STATUSES, obj.calculate_halt_status_for_repost())
        return to_display_attr(constants.STATUSES, obj.status)

    def get_company_admins_for_site(self, obj):
        site = get(obj, 'site')
        if site and get(site, 'stocks_management'):
            return Employee.objects.filter(
                company_id=get(site, 'company_id'),
                type_id=core.common.constants.COMPANY_ADMIN_TYPE_ID
            ).values_list('id', flat=True)


class ExternalCashPriceSerializer(ModelSerializer):
    commodity = serializers.CharField(source='commodity.name')
    grade = serializers.CharField(source='grade.name')
    variety = serializers.CharField(source='variety.name', allow_blank=True, allow_null=True)
    buyer = SerializerMethodField()
    payment_scale = serializers.CharField(source='payment_scale.name')
    payment_term = serializers.CharField(source='payment_term.name')
    created_by = serializers.CharField(source='created_by.name')
    updated_by = serializers.CharField(source='updated_by.name')

    class Meta:
        model = CashPrices
        fields = (
            'id', 'site_id', 'commodity', 'grade', 'variety', 'season', 'start_date_time', 'end_date_time',
            'buyer', 'price', 'limit', 'limit_remaining', 'payment_scale', 'payment_term', 'track',
            'status', 'external_system', 'external_reference', 'comments',
            'created_at', 'updated_at', 'created_by', 'updated_by', 'sustainable_commodity'
        )

    @staticmethod
    def get_buyer(obj):
        if obj.buyer_id:
            ngr = obj.buyer.ngr
            company_id = get(ngr, 'primary_owner_company_ids.0')
            company = obj.buyer.company
            if company_id:
                from core.companies.models import Company
                company = Company.objects.filter(id=company_id).first() or company
            return {'abn': company.abn, 'name': company.name}

        return None
