from pydash import get
from rest_framework.throttling import UserRateThrottle


class UserPublicAPILiteMinuteThrottle(UserRateThrottle):
    scope = 'user_public_lite_minute'


class UserPublicAPILiteDayThrottle(UserRateThrottle):
    scope = 'user_public_lite_day'


class UserPublicAPIPremiumDayThrottle(UserRateThrottle):
    scope = 'user_public_premium_day'


class ThrottleUtil:
    @staticmethod
    def get_limit_remaining(throttle, request, view):
        key = throttle.get_cache_key(request, view)
        if key is not None:
            history = throttle.cache.get(key, None)
            if history is None:
                return None
            while history and history[-1] <= throttle.timer() - throttle.duration:
                history.pop()
            remaining = throttle.num_requests - len(history)
        else:
            remaining = 'unlimited'

        return remaining

    @staticmethod
    def get_throttles_by_user_plan(user):
        # order is important, first one has to be minute throttle
        if get(user, 'api_rate_limit.is_premium'):
            return [UserPublicAPILiteMinuteThrottle(), UserPublicAPIPremiumDayThrottle()]
        return [UserPublicAPILiteMinuteThrottle(), UserPublicAPILiteDayThrottle()]
