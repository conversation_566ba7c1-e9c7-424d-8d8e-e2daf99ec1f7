# Generated by Django 2.1.9 on 2019-10-10 05:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0057_auto_20190912_0652'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitemanagementsettings',
            name='order_booking',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='historicalfreightslot',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='company_sites_freightslots_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalfreightslot',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='company_sites_freightslots_updated_by', to=settings.AUTH_USER_MODEL),
        ),
    ]
