# Generated by Django 2.1.3 on 2019-01-25 07:34

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0020_auto_20190125_0452'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='bhcsite',
            name='address',
        ),
        migrations.RemoveField(
            model_name='bhcsite',
            name='company',
        ),
        migrations.RemoveField(
            model_name='bhcsite',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='bhcsite',
            name='market_zone',
        ),
        migrations.RemoveField(
            model_name='bhcsite',
            name='region',
        ),
        migrations.RemoveField(
            model_name='bhcsite',
            name='updated_by',
        ),
        migrations.RemoveField(
            model_name='historicalbhcsite',
            name='address',
        ),
        migrations.RemoveField(
            model_name='historicalbhcsite',
            name='company',
        ),
        migrations.RemoveField(
            model_name='historicalbhcsite',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='historicalbhcsite',
            name='history_user',
        ),
        migrations.RemoveField(
            model_name='historicalbhcsite',
            name='market_zone',
        ),
        migrations.RemoveField(
            model_name='historicalbhcsite',
            name='region',
        ),
        migrations.RemoveField(
            model_name='historicalbhcsite',
            name='updated_by',
        ),
        migrations.DeleteModel(
            name='BHCSite',
        ),
        migrations.DeleteModel(
            name='HistoricalBHCSite',
        ),
    ]
