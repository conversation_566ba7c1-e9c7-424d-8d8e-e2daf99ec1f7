# Generated by Django 2.1.7 on 2019-06-12 05:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('company_sites', '0045_auto_20190611_0758'),
    ]

    operations = [
        migrations.AddField(
            model_name='freightslot',
            name='location_id',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='freightslot',
            name='location_type',
            field=models.ForeignKey(blank=True, limit_choices_to={'model__in': ('farm', 'companysite')}, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.ContentType'),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='location_id',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='location_type',
            field=models.ForeignKey(blank=True, db_constraint=False, limit_choices_to={'model__in': ('farm', 'companysite')}, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='contenttypes.ContentType'),
        ),
    ]
