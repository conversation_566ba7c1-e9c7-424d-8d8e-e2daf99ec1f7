# Generated by Django 2.2 on 2020-06-11 03:11

from django.db import migrations


def update_vendor_decs_for_logistic_lite_companies(apps, schema_editor):
    PlatformFeatures = apps.get_model('companies', 'PlatformFeatures')
    PlatformFeatures.objects.filter(plan_type='logistics_lite').update(vendor_decs=False)


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0047_platformfeatures_vendor_decs'),
    ]

    operations = [
        migrations.RunPython(update_vendor_decs_for_logistic_lite_companies)
    ]
