from core.common.constants import METER_CUBE, MT, BALES, BUSHEL, POUND, CENTURY_TONNE, KG, LITRE, LITRE_ABBREVIATION, \
    SHORT_TONNE, MODULES

CLASS_1 = 'Class 1'
CLASS_2 = 'Class 2'
CLASS_3 = 'Class 3'

UNIT_CHOICES = (
    (MT, MT),
    (BALES, BALES),
    (MODULES, MODULES),
    (METER_CUBE, METER_CUBE),
    (BUSHEL, BUSHEL),
    (POUND, POUND),
    (CENTURY_TONNE, CENTURY_TONNE),
    (KG, KG),
    (LITRE, LITRE),
)

EMPLOYEE_UNIT_CHOICES = (
    *UNIT_CHOICES,
    (SHORT_TONNE, SHORT_TONNE)
)

UNIT_ABBREVIATIONS = {
    MT: MT,
    BALES: BALES,
    MODULES: MODULES,
    METER_CUBE: METER_CUBE,
    BUSHEL: BUSHEL,
    POUND: POUND,
    CENTURY_TONNE: CENTURY_TONNE,
    KG:KG,
    LITRE: LITRE_ABBREVIATION
}

TYPE_CHOICES = (
    ('grain', 'Grain'),
    ('chemical', 'Chemical'),
)

CHICKPEA_DESI_ID = 6
CHICKPEA_KABULI_ID = 7
SOYBEAN_ID = 18
LUPINS_ALBUS_ID = 13
LUPINS_ANGUSTIFOLIUS_ID = 54
UNKNOWN_COMMODITY_ID = 260
UNKNOWN_GRADE_ID = 4227
UNKNOWN = "Unknown"

INTERCHANGEABLE_COMMODITIES_MAPPING = [
    ['wheat', 'wheat_seed'],
    ['barley', 'barley_seed'],
    ['field_peas', 'Field Peas Seed'],
    ['Chickpeas Seed', 'chickpeas_desi', 'chickpeas_kabuli'],
    ['canola', 'Canola Seed', 'canola_meal', 'Canola Oil'],
    ['faba_beans', 'Faba Beans Seed'],
    ['lupins_albus', 'lupins_angustifolius', 'Lupins Seed'],
    ['Stockfeed', 'fertiliser', 'Ingredients', 'canola_meal', 'General Goods'],
    ['oats', 'Hulled Oats', 'Oat Seed'],
    ['lentils', 'Lentils Seed'],
    ['Cotton Modules', 'Cotton Trash', 'Cotton Lint', 'cottonseed'],
    ['vetch', 'Vetch Hay', 'Vetch Seed', 'Vetch Silage'],
]


def get_item_tag_from_mappings(state=None):
    return {
        'Rye': 'Cereal rye',
        'Chickpeas': 'Chickpeas',
        'Faba Beans Seed': 'Faba beans',
        'Field Peas': 'Field peas',
        'Lupins': 'Lupins',
        'Mung Beans': 'Mung beans',
        'Pigeon Pea': 'Pigeon peas',
        'Vetch': 'Common vetch',
        'Canola': 'Rapeseeds',
        'Safflower': 'Safflower seeds',
        'Soybean': 'Soybeans',
        'Sunflower': 'Sunflower seeds',
        'Wheat': f'Wheat {state}' if state else 'Wheat',
    }
