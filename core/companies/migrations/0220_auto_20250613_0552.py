# Generated by Django 5.1.8 on 2025-06-13 05:52

from django.db import migrations

def update_canada_company_bn_number_to_9_digits(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    for company in Company.objects.filter(country_id=3, abn__isnull=False):
        if company.abn and len(company.abn) > 9:
            # keeps the last 9 characters of the abn
            new_abn = company.abn[-9:]
            if new_abn != company.abn:
                company.abn = new_abn
                company.updated_by_id = 1
                company.save(update_fields=['abn', 'updated_at', 'updated_by_id'])


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0219_merge_20250605_0657'),
    ]

    operations = [
        migrations.RunPython(update_canada_company_bn_number_to_9_digits),
    ]
