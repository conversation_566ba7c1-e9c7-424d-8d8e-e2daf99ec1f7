from django.core.management import BaseCommand

from core.companies.models import Company
from core.ngrs.models import Ngr


class Command(BaseCommand):
    help = 'Sets default shrink and title transfer ngrs for companies'

    def handle(self, *args, **options):
        companies = Company.objects.all()
        print("***Updating Default Ngrs***********")
        total = companies.count()
        current = 0
        for company in companies:
            logical_ngr_id = company.get_logical_ngr_id()
            if logical_ngr_id:
                current += 1
                print("Running {}/{}".format(current, total))
                ngr = Ngr.objects.get(id=logical_ngr_id)
                if not ngr.tags:
                    ngr.tags = []
                ngr.tags += ['shrinkage', 'title_transfer']
                ngr.save()
        print("***Updated Default Ngrs***********")
