# Generated by Django 2.2.20 on 2021-07-13 07:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0056_auto_20210707_1017'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='hours_before_cancellation_stops',
            field=models.IntegerField(choices=[(0, 0), (1, 1), (2, 2), (3, 3), (4, 4), (5, 5), (6, 6), (7, 7), (8, 8), (9, 9), (10, 10), (11, 11), (12, 12), (13, 13), (14, 14), (15, 15), (16, 16), (17, 17), (18, 18), (19, 19), (20, 20), (21, 21), (22, 22), (23, 23), (24, 24), (25, 25), (26, 26), (27, 27), (28, 28), (29, 29), (30, 30), (31, 31), (32, 32), (33, 33), (34, 34), (35, 35), (36, 36), (37, 37), (38, 38), (39, 39), (40, 40), (41, 41), (42, 42), (43, 43), (44, 44), (45, 45), (46, 46), (47, 47), (48, 48)], default=0),
        ),
        migrations.AddField(
            model_name='company',
            name='restrict_slot_cancellation',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='hours_before_cancellation_stops',
            field=models.IntegerField(choices=[(0, 0), (1, 1), (2, 2), (3, 3), (4, 4), (5, 5), (6, 6), (7, 7), (8, 8), (9, 9), (10, 10), (11, 11), (12, 12), (13, 13), (14, 14), (15, 15), (16, 16), (17, 17), (18, 18), (19, 19), (20, 20), (21, 21), (22, 22), (23, 23), (24, 24), (25, 25), (26, 26), (27, 27), (28, 28), (29, 29), (30, 30), (31, 31), (32, 32), (33, 33), (34, 34), (35, 35), (36, 36), (37, 37), (38, 38), (39, 39), (40, 40), (41, 41), (42, 42), (43, 43), (44, 44), (45, 45), (46, 46), (47, 47), (48, 48)], default=0),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='restrict_slot_cancellation',
            field=models.BooleanField(default=False),
        ),
    ]
