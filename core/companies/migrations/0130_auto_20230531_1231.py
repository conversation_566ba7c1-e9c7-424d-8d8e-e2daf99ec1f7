# Generated by Django 4.1.9 on 2023-05-31 12:31

from django.db import migrations

def backfill_logistic_lite_companies_platform_features(apps, schema_editor):
    platform_features = apps.get_model('companies', 'PlatformFeatures')
    fields_true = [
        'action_centre',
        'companies',
        'orders',
        'movements',
        'vendor_decs',
        'site_bookings',
    ]
    fields_false = [
        'site_management',
        'contracts',
        'invoices',
        'stocks',
        'farms',
        'title_transfers',
        'cash_board',
        'freight_scheduler',
    ]
    update_fields = {field: True for field in fields_true} | {
        field: False for field in fields_false
    }
    platform_features.objects.filter(plan_type='logistics_lite').update(**update_fields)

class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0129_alter_company_mobile_alter_historicalcompany_mobile'),
    ]

    operations = [
        migrations.RunPython(backfill_logistic_lite_companies_platform_features)
    ]
