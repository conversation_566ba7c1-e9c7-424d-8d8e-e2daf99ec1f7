# Generated by Django 4.1.13 on 2024-02-20 10:00

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0160_remove_company_contract_allocation_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='companygroup',
            name='business_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='companies.companytype'),
        ),
        migrations.AddField(
            model_name='historicalcompanygroup',
            name='business_type',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.companytype'),
        ),
    ]
