# Generated by Django 2.1.3 on 2019-01-24 11:32

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0011_auto_20190124_1113'),
    ]

    operations = [
        migrations.AlterField(
            model_name='companysite',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='historicalcompanysite',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')]),
        ),
    ]
