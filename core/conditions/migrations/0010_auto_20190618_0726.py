# Generated by Django 2.1.9 on 2019-06-18 07:26

from django.db import migrations

def populate_conditions_with_general_and_special(apps, schema_editor):
    Condition = apps.get_model('conditions', 'Condition')
    GeneralCondition = apps.get_model('conditions', 'GeneralCondition')
    SpecialCondition = apps.get_model('conditions', 'SpecialCondition')
    conditions = []
    for condition in GeneralCondition.objects.all():
        conditions.append(
            Condition(
                name=condition.name,
                details=condition.details,
                module=condition.type,
                type='general',
                company_id=condition.company_id,
            )
        )

    for condition in SpecialCondition.objects.all():
        conditions.append(
            Condition(
                name=condition.name,
                details=condition.details,
                module=condition.type,
                type='special',
                company_id=condition.employee.company_id,
            )
        )

    Condition.objects.bulk_create(conditions)

class Migration(migrations.Migration):

    dependencies = [
        ('conditions', '0009_auto_20190618_0724'),
    ]

    operations = [
        migrations.RunPython(populate_conditions_with_general_and_special)
    ]
