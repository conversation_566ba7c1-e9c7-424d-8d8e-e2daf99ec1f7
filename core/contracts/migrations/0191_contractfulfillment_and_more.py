# Generated by Django 4.1.10 on 2023-10-10 07:15

import core.common.models
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0190_contractfulfilment_contract_contract_fulfilments'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContractFulfillment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tonnage', core.common.models.RoundedFloatField()),
            ],
        ),
        migrations.RemoveField(
            model_name='contract',
            name='contract_fulfilments',
        ),
        migrations.DeleteModel(
            name='ContractFulfilment',
        ),
        migrations.AddField(
            model_name='contractfulfillment',
            name='purchase',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salecontractfulfillment_set', to='contracts.contract'),
        ),
        migrations.AddField(
            model_name='contractfulfillment',
            name='sale',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchasecontractfulfillment_set', to='contracts.contract'),
        ),
        migrations.AddField(
            model_name='contract',
            name='contract_fulfillments',
            field=models.ManyToManyField(related_name='purchase_fulfillment_contracts', through='contracts.ContractFulfillment', to='contracts.contract'),
        ),
        migrations.AlterUniqueTogether(
            name='contractfulfillment',
            unique_together={('purchase', 'sale')},
        ),
    ]
