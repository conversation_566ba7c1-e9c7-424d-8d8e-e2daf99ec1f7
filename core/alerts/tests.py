import datetime
from django.utils import timezone
from mock import patch, <PERSON><PERSON>, ANY
from mock.mock import call
from django.test import tag
from pydash import get

from core.alerts.constants import (MASS_LIMIT_BREACH_ALERT_CODE, MASS_LIMIT_BREACH_ALERT,
                                   DELIVERY_CONFIRMATION_SMS_ALERT_CODE, MISSING_DOCKET_ALERT_CODE,
                                   SPEC_AVG_REPORT_ALERT_CODE, OUTLOAD_REPORT_ALERT_CODE,
                                   PICKUP_CONFIRMATION_ALERT_CODE, CLASS_1_MATERIAL_ALERT_CODE,
                                   TRUCK_CLEANLINESS_DECLARATION_NOT_FILLED_ALERT_CODE, ACQUISITION_REPORT_ALERT_CODE,
                                   MISSING_DOCKET_PICKUP_SITE_ALERT_CODE, SLOT_BOOKING_UPDATE_ALERT_CODE,
                                   PICKUP_CONFIRMATION_SMS_ALERT_CODE, DOCKET_CLARIFICATION_ALERT_CODE,
                                   STOC<PERSON>_OPERATIONS_UPDATE_ALERT_CODE, STOCK_AUTO_UPDATE_ALERT_CODE)
from core.alerts.models import (Alert, MissingDocketAlertForProviders, MissingDocketAlertForPickupSite, SlotUpdateAlert,
                                Class1MaterialAlert, PickupInformationAlert, OutloadDoneAlert, DocketClarificationAlert,
                                StockOperationsUpdateAlert, StockAutoUpdateAlert)
from core.common.constants import (AEST_TZ, FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME, SUPPORT_EMAIL,
                                   COMPANY_ADMIN_TYPE_ID, STATIC_URL_PREFIX, AWST_TZ, BHC_TYPE_ID)
from core.common.tests import ACTestCase, AuthSetup
from core.timezones.utils import DateTimeUtil
from core.companies.tests.factories import CompanyFactory
from core.contracts.tests.factories import PartyFactory, ContractCommodityHandlerFactory
from core.farms.tests.factories import StorageFactory, FarmFactory
from core.freights.tests.factories import (FreightDeliveryFactory, FreightMovementFactory, FreightPickupFactory,
                                           FreightOrderFactory)
from core.key_contacts.models import KeyContact
from core.loads.models import Load
from core.loads.tests.factories import LoadFactory
from core.mobile_messages.constants import MESSAGE_TYPE_INLOAD_DONE
from core.ngrs.tests.factories import NgrFactory
from core.profiles.tests.factories import EmployeeFactory
from core.trucks.factories import TruckFactory


class AlertTestSetup(ACTestCase):  # pylint: disable=too-many-instance-attributes
    def set_up_alerts(self):
        from core.locations.tests.factories import AddressFactory
        self.company = CompanyFactory(type_id=4, country_id=1)
        self.site_1 = FarmFactory(company=self.company,
                                  address=AddressFactory(latitude=14.43, longitude=133.96, location_type="farm"))
        self.site_2 = FarmFactory(company=self.company,
                                  address=AddressFactory(latitude=12.48, longitude=130.96, location_type="farm"))
        self.employee_1 = EmployeeFactory(
            company_id=self.company.id, email='<EMAIL>', mobile='0404023385', type_id=10
        )
        self.company_admin = EmployeeFactory(
            company_id=self.company.id,
            type_id=COMPANY_ADMIN_TYPE_ID,
            email='<EMAIL>',
            mobile='**********'
        )
        self.site_admin = EmployeeFactory(
            company_id=self.company.id,
            type_id=4,
            email='<EMAIL>',
            mobile='**********'
        )
        self.customer = CompanyFactory(type_id=4)
        self.customer_site_admin = EmployeeFactory(
            company_id=self.customer.id, email='<EMAIL>', mobile='**********', type_id=4
        )
        self.customer_company_admin = EmployeeFactory(
            company_id=self.customer.id, email='<EMAIL>', mobile='**********', type_id=1
        )
        self.freight_provider = CompanyFactory(type_id=3)
        self.key_contact_employee = EmployeeFactory(
            company_id=self.freight_provider.id, email='<EMAIL>', mobile='**********'
        )
        KeyContact.create({
            'requester_company': self.company,
            'company': self.freight_provider,
            'employee': self.key_contact_employee,
        })
        self.mass_limit_alert = Alert.persist({
            'name': MASS_LIMIT_BREACH_ALERT_CODE,
            'frequency': None,
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'customer',
                    'employee_roles': ['site_admin'],
                    'employees': []
                },
                {
                    'party': 'own_company',
                    'employee_roles': ['company_admin'],
                    'employees': [self.employee_1.id]
                },
                {
                    'party': 'freight_provider',
                    'employee_roles': ['key_contact'],
                    'employees': []
                }
            ]
        })

        self.inload_done_alert = Alert.persist({
            'name': DELIVERY_CONFIRMATION_SMS_ALERT_CODE,
            'frequency': None,
            'channel': 'sms',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'stock_owner',
                    'employee_roles': ['company_admin'],
                    'employees': []
                },
            ]
        })

        self.spec_avg_report_alert = Alert.persist({
            'name': SPEC_AVG_REPORT_ALERT_CODE,
            'frequency': 'daily',
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [],
            'site_recipients': [
                {
                    'site_id': self.site_1.id,
                    'recipient': {
                        'party': 'own_company',
                        'employee_roles': ['company_admin'],
                        'employees': []
                    }
                },
                {
                    'site_id': self.site_2.id,
                    'recipient': {
                        'party': 'own_company',
                        'employee_roles': ['site_admin'],
                        'employees': []
                    }
                },
            ]
        })

        self.outload_report_alert = Alert.persist({
            'name': OUTLOAD_REPORT_ALERT_CODE,
            'frequency': 'daily',
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'own_company',
                    'employee_roles': [],
                    'employees': [self.employee_1.id, self.company_admin.id]
                },
            ]
        })

        self.acquisition_alert = Alert.persist({
            'name': ACQUISITION_REPORT_ALERT_CODE,
            'frequency': 'daily',
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'own_company',
                    'employee_roles': [],
                    'employees': [self.employee_1.id, self.company_admin.id]
                },
            ]
        })

        self.pickup_confirmation_alert = Alert.persist({
            'name': PICKUP_CONFIRMATION_ALERT_CODE,
            'frequency': 'daily',
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'freight_provider',
                    'employee_roles': ['driver'],
                    'employees': []
                },
            ],
        })
        self.class_1_material_alert = Alert.persist({
            'name': CLASS_1_MATERIAL_ALERT_CODE,
            'frequency': 'daily',
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'freight_provider',
                    'employee_roles': ['driver'],
                    'employees': []
                },
            ],
            'site_recipients': [
                {
                    'site_id': self.site_1.id,
                    'recipient': {
                        'party': 'own_company',
                        'employee_roles': ['company_admin'],
                        'employees': []
                    }
                },
                {
                    'site_id': self.site_2.id,
                    'recipient': {
                        'party': 'own_company',
                        'employee_roles': ['site_admin'],
                        'employees': []
                    }
                },
            ]

        })
        self.truck_cleanliness_declaration_pending_alert = Alert.persist({
            'name': TRUCK_CLEANLINESS_DECLARATION_NOT_FILLED_ALERT_CODE,
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'freight_provider',
                    'employee_roles': ['driver'],
                    'employees': []
                },
                {
                    'party': 'own_company',
                    'employee_roles': [],
                    'employees': [self.employee_1.id, self.company_admin.id]
                },
            ]
        })

        self.missing_docket_alert_for_provider = Alert.persist({
            'name': MISSING_DOCKET_ALERT_CODE,
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'own_company',
                    'employee_roles': [],
                    'employees': [self.employee_1.id, self.company_admin.id]
                },
            ],
            'hours_before_trigger': 1,
        })

        self.missing_docket_alert_for_site = Alert.persist({
            'name': MISSING_DOCKET_PICKUP_SITE_ALERT_CODE,
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [],
            'site_recipients': [
                {
                    'site_id': self.site_1.id,
                    'recipient': {
                        'party': 'own_company',
                        'employee_roles': ['company_admin'],
                        'employees': []
                    }
                },
                {
                    'site_id': self.site_2.id,
                    'recipient': {
                        'party': 'own_company',
                        'employee_roles': ['site_admin'],
                        'employees': []
                    }
                },
            ],
            'hours_before_trigger': 1,
        })

        self.slot_update_alert = Alert.persist({
                'name': SLOT_BOOKING_UPDATE_ALERT_CODE,
                'channel': 'email',
                'company_id': self.company.id,
                'recipients': [
                    {
                        'party': 'freight_provider',
                        'employee_roles': ['driver'],
                        'employees': []
                    },
                    {
                        'party': 'seller',
                        'employee_roles': [],
                        'employees': [self.company_admin.id]
                    },
                ],
                'site_recipients': [
                    {
                        'site_id': self.site_1.id,
                        'recipient': {
                            'party': 'own_company',
                            'employee_roles': ['company_admin'],
                            'employees': [self.employee_1.id]
                        }
                    },
                    {
                        'site_id': self.site_2.id,
                        'recipient': {
                            'party': 'own_company',
                            'employee_roles': ['site_admin'],
                            'employees': []
                        }
                    },

                ],
            })

        self.pickup_confirmation_sms_alert = Alert.persist({
            'name': PICKUP_CONFIRMATION_SMS_ALERT_CODE,
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'truck_owner',
                    'employee_roles': [],
                    'employees': [self.employee_1.id, self.company_admin.id]
                },
            ],
            'hours_before_trigger': 1,
        })

        self.docket_clarification_alert = Alert.persist({
            'name': DOCKET_CLARIFICATION_ALERT_CODE,
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'own_company',
                    'employee_roles': [],
                    'employees': [self.employee_1.id, self.company_admin.id]
                },
            ]
        })

        self.stock_update_alert = Alert.persist({
            'name': STOCK_OPERATIONS_UPDATE_ALERT_CODE,
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'own_company',
                    'employee_roles': [],
                    'employees': [self.employee_1.id, self.company_admin.id]
                },
                {
                    'party': 'stock_owner',
                    'employee_roles': ['site_admin'],
                    'employees': []
                }
            ]
        })
        self.stock_auto_update_alert = Alert.persist({
            'name': STOCK_AUTO_UPDATE_ALERT_CODE,
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'own_company',
                    'employee_roles': ['company_admin'],
                    'employees': [self.employee_1.id]
                }
            ]
        })



@tag('model')
class AlertTest(AlertTestSetup):
    def test_persist(self):
        self.set_up_alerts()
        self.assertEqual(self.mass_limit_alert.persisted, True)
        self.assertEqual(self.mass_limit_alert.company_id, self.company.id)
        self.assertEqual(self.mass_limit_alert.alert_display_name, MASS_LIMIT_BREACH_ALERT)
        self.assertEqual(self.mass_limit_alert.recipient_set.all().count(), 3)

    def test_get_recipients(self):
        self.set_up_alerts()
        recipients = self.inload_done_alert.get_recipients(self.customer)
        self.assertEqual(recipients, {'**********'})

    @patch('core.freights.tasks.FreightMail')
    @patch('core.alerts.models.MobileMessage')
    @patch('core.alerts.models.EmailMessage')
    def test_process(self, email_message_klass_mock, mobile_message_klass_mock, freight_mail_klass_mock):  # pylint: disable=too-many-locals
        email_message_mock = Mock(send=Mock(return_value=1))
        email_message_klass_mock.return_value = email_message_mock
        self.set_up_alerts()

        # Receival Report Alert
        self.spec_avg_report_alert.process()
        date_time = DateTimeUtil.localize(timezone.now(), AEST_TZ) - datetime.timedelta(days=1)
        expected_call_1 = call(
            subject=f'Daily Receivals Report - {date_time.strftime("%d/%m/%Y")}',
            body=ANY,
            to=['<EMAIL>'],
            from_email=FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME,
            reply_to=[SUPPORT_EMAIL],
            cc=None,
        )
        expected_call_2 = call(
            subject=f'Daily Receivals Report - {date_time.strftime("%d/%m/%Y")}',
            body=ANY,
            to=['<EMAIL>'],
            from_email=FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME,
            reply_to=[SUPPORT_EMAIL],
            cc=None,
        )
        email_message_klass_mock.assert_has_calls([expected_call_1, expected_call_2], any_order=True)

        # Mass Limit Alert
        email_message_mock_2 = Mock(send=Mock(return_value=1))
        email_message_klass_mock.return_value = email_message_mock_2
        freight_delivery = FreightDeliveryFactory()
        freight_delivery.save()
        storage = StorageFactory(farm=FarmFactory(company=self.company))
        customer = PartyFactory(role='Customer', company=self.customer)
        freight_contract = FreightMovementFactory(
            commodity_contract_id=None, planned_tonnage=10, customer=customer,
            provider=self.freight_provider, identifier='F123'
        )
        freight_contract.freight_delivery = freight_delivery
        freight_contract.save()
        ngr = NgrFactory(company=self.company, ngr_type='single')
        truck = TruckFactory(company=self.freight_provider, rego='X1T123')
        inload = LoadFactory(
            tare_weight=11, gross_weight=99, type=Load.INLOAD, checkpoint=freight_delivery,
            date_time='2023-03-14 12:12:12', commodity_id=1, storage=storage, ngr=ngr, truck=truck,
            movement=freight_contract
        )

        freight_mail_mock = Mock(pdf=Mock(return_value=Mock()))
        freight_mail_klass_mock.return_value = freight_mail_mock
        email_message_mock_2.attach = Mock()
        email_message_mock_2.send = Mock(return_value=1)
        self.mass_limit_alert.process(load=inload, entity=freight_contract)
        email_message_mock_2.send.assert_has_calls([call(), call(), call()])
        email_message_mock_2.attach.assert_has_calls([
            call(ANY, ANY, ANY), call(ANY),
            call(ANY, ANY, ANY), call(ANY),
            call(ANY, ANY, ANY), call(ANY)
        ])
        expected_pdf_calls = [
            call('freight_contract_preview.html',
                 {'urlprefix': STATIC_URL_PREFIX, 'movement': freight_contract, 'act': False, 'can_view_pickup': True,
                  'can_view_delivery': True}),
            call('freight_contract_preview.html',
                 {'urlprefix': STATIC_URL_PREFIX, 'movement': freight_contract, 'act': False, 'can_view_pickup': True,
                  'can_view_delivery': True}),
            call('freight_contract_preview.html',
                 {'urlprefix': STATIC_URL_PREFIX, 'movement': freight_contract, 'act': False, 'can_view_pickup': True,
                  'can_view_delivery': True})
        ]
        freight_mail_mock.pdf.assert_has_calls(expected_pdf_calls)

        # Inload Done Alert
        mobile_message_mock = Mock(send=Mock(return_value=1))
        mobile_message_mock.save = Mock()
        mobile_message_klass_mock.return_value = mobile_message_mock
        self.inload_done_alert.process(load=inload)
        mobile_message_klass_mock.assert_called_once_with(
            country_id=1,
            type=MESSAGE_TYPE_INLOAD_DONE,
            recipient='**********',
            message=ANY,
            args={'load_type': 'inload', 'load_id': inload.id}
        )
        mobile_message_mock.save.assert_called_once()
        mobile_message_mock.send.assert_called_once()

        # Truck Cleanliness Declaration Pending Alert
        farm = FarmFactory(company=self.company)
        consignor = ContractCommodityHandlerFactory(role='Consignor', handler=farm)
        freight_pickup = FreightPickupFactory(consignor=consignor)
        freight_contract = FreightMovementFactory(provider=self.freight_provider, freight_pickup=freight_pickup)
        email_message_mock_3 = Mock(send=Mock(return_value=1))
        email_message_klass_mock.return_value = email_message_mock_3
        email_message_mock_3.send = Mock(return_value=1)
        self.truck_cleanliness_declaration_pending_alert.process(movement=freight_contract)
        email_message_mock_3.send.assert_has_calls([call()])


    @patch('core.alerts.models.EmailMessage')
    def test_process_outload_report(self, email_message_klass_mock):
        email_message_mock = Mock(send=Mock(return_value=1))
        email_message_klass_mock.return_value = email_message_mock
        self.set_up_alerts()

        self.outload_report_alert.process()
        date_time = DateTimeUtil.localize(timezone.now(), AEST_TZ) - datetime.timedelta(days=1)
        email_message_klass_mock.assert_any_call(
            subject=f'Daily Outload Report - {date_time.strftime("%d/%m/%Y")}',
            body=ANY,
            to={'<EMAIL>', '<EMAIL>'},
            from_email=FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME,
            reply_to=[SUPPORT_EMAIL],
            cc=None,
        )
        email_message_mock.send.assert_called_once()

    @patch('core.alerts.models.EmailMessage')
    def test_process_acquisition_alert(self, email_message_klass_mock):
        email_message_mock = Mock(send=Mock(return_value=1))
        email_message_klass_mock.return_value = email_message_mock
        self.set_up_alerts()

        self.acquisition_alert.process()
        date_time = DateTimeUtil.localize(timezone.now(),AWST_TZ) - datetime.timedelta(days=1)
        email_message_klass_mock.assert_any_call(
            subject="Acquisition File | {} - {}".format(self.company.name, date_time.strftime("%d/%m/%Y")),
            body=ANY,
            to={'<EMAIL>', '<EMAIL>'},
            from_email=FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME,
            reply_to=[SUPPORT_EMAIL],
            cc=None,
        )
        email_message_mock.send.assert_called_once()

    @patch('core.alerts.models.MissingDocketAlert.process')
    @patch('core.freights.models.Toggle.get')
    def test_process_missing_docket_alert(self, mock_toggle,  mock_process):
        # pylint: disable=line-too-long
        from core.mobile_messages.models import SystemNumber, MobileMessage
        from core.mobile_messages.constants import MESSAGE_TYPE_DOCKET
        self.set_up_alerts()
        mock_toggle.return_value = True

        consignor = ContractCommodityHandlerFactory(role='Consignor', handler=self.site_1)
        freight_pickup = FreightPickupFactory(consignor=consignor)
        freight_contract = FreightMovementFactory(status='in_progress',
                                                  provider=self.company, freight_pickup=freight_pickup)
        SystemNumber(number='**********', country_id=self.company.country_id).save()
        instance = MobileMessage(
            recipient='61777777777',
            sender=SystemNumber.all(self.company.country_id).first(), type=MESSAGE_TYPE_DOCKET,
            country_id=self.company.country_id, sent_response={'sent': True}, sent_at=timezone.now(),
            args={'movement_id': freight_contract.id}
        )
        instance.save()

        from core.mobile_messages.tasks import notify_unreplied_messages_to_pickup_site, notify_unreplied_messages_to_provider
        notify_unreplied_messages_to_provider()
        mock_process.called_once_with(
            unanswered_docket_sms_data={'user': self.company_admin, 'provider': self.company, 'messages': [(instance, freight_contract)]})

        notify_unreplied_messages_to_pickup_site()
        mock_process.called_once_with(
            unanswered_docket_sms_data={
                'user': self.company_admin, 'provider': self.company, 'messages': [(instance, freight_contract)]
            }, employee_ids=[self.company_admin.id])


@tag('model')
class MissingDocketAlertForProvidersTest(AlertTestSetup):

    def test_notify_unreplied_messages_to_provider(self):
        # pylint: disable=line-too-long
        from core.mobile_messages.models import SystemNumber, MobileMessage
        from core.mobile_messages.constants import MESSAGE_TYPE_DOCKET
        self.set_up_alerts()
        consignor = ContractCommodityHandlerFactory(role='Consignor', handler=self.site_1)
        freight_pickup = FreightPickupFactory(consignor=consignor)
        freight_contract = FreightMovementFactory(status='in_progress',
                                                  provider=self.company, freight_pickup=freight_pickup)
        SystemNumber(number='**********', country_id=self.company.country_id).save()
        instance = MobileMessage(
            recipient='61777777777',
            sender=SystemNumber.all(self.company.country_id).first(), type=MESSAGE_TYPE_DOCKET,
            country_id=self.company.country_id, sent_response={'sent': True}, sent_at=timezone.now(),
            args={'movement_id': freight_contract.id}
        )
        instance.save()
        country = self.company.country
        country_code = instance.country.config.get('phone_code').replace('+', '') or '61'

        unanswered_docket_sms_data = {
            'user': self.company_admin, 'provider': self.company, 'messages': [(instance, freight_contract)]
        }
        docket_alert_class = MissingDocketAlertForProviders(alert=self.missing_docket_alert_for_provider,
                                                            unanswered_docket_sms_data=unanswered_docket_sms_data)
        docket_alert_class.make_data()
        self.assertEqual(
            docket_alert_class.file_content,
            'Movement No,Customer Name,Load Date,Load Time,Commodity,Grade,Pickup Site,Delivery Site,Rego,Driver Name,Driver Number,Original SMS Sent Date,Last Reminder Sent Date\r\n'
            f'{freight_contract.identifier},{freight_contract.customer.company.name},,,Wheat,APH1,{self.site_1.name},{freight_contract.freight_delivery.consignee.handler.name},,{freight_contract.driver.name},{instance.recipient[len(country_code):]},{DateTimeUtil.localize_date(instance.sent_at,country.timezone, country.get_format("datetime"))},\r\n'
        )


@tag('model')
class MissingDocketAlertForPickupSiteTest(AlertTestSetup):

    def test_notify_unreplied_messages_to_pickup_site(self):
        # pylint: disable=line-too-long
        from core.mobile_messages.models import SystemNumber, MobileMessage
        from core.mobile_messages.constants import MESSAGE_TYPE_DOCKET
        self.set_up_alerts()
        consignor = ContractCommodityHandlerFactory(role='Consignor', handler=self.site_1)
        freight_pickup = FreightPickupFactory(consignor=consignor)
        freight_contract = FreightMovementFactory(status='in_progress',
                                                  provider=self.company, freight_pickup=freight_pickup)
        SystemNumber(number='**********', country_id=self.company.country_id).save()
        instance = MobileMessage(
            recipient='61777777777',
            sender=SystemNumber.all(self.company.country_id).first(), type=MESSAGE_TYPE_DOCKET,
            country_id=self.company.country_id, sent_response={'sent': True}, sent_at=timezone.now(),
            args={'movement_id': freight_contract.id}
        )
        instance.save()
        country = self.company.country
        country_code = instance.country.config.get('phone_code').replace('+', '') or '61'

        unanswered_docket_sms_data = {
            'user': self.company_admin, 'provider': self.company, 'messages': [(instance, freight_contract)]
        }
        docket_alert_class = MissingDocketAlertForPickupSite(
            alert=self.missing_docket_alert_for_provider, unanswered_docket_sms_data=unanswered_docket_sms_data,
            employee_ids=[self.company_admin.id])
        docket_alert_class.make_data()
        self.assertEqual(
            docket_alert_class.file_content,
            'Movement No,Customer Name,Freight Provider,Load Date,Load Time,Commodity,Grade,Pickup Site,Delivery Site,Rego,Driver Name,Driver Number,Original SMS Sent Date,Last Reminder Sent Date\r\n'
            f'{freight_contract.identifier},{freight_contract.customer.company.name},{freight_contract.provider.name},,,Wheat,APH1,{self.site_1.name},{freight_contract.freight_delivery.consignee.handler.name},,{freight_contract.driver.name},{instance.recipient[len(country_code):]},{DateTimeUtil.localize_date(instance.sent_at, country.timezone, country.get_format("datetime"))},\r\n'
        )

@tag('model')
class SlotUpdateAlertTest(AlertTestSetup):

    def test_slot_update_subject_test(self):
        from core.company_sites.models import FreightSlot
        self.set_up_alerts()
        freight_pickup = FreightPickupFactory(consignor=ContractCommodityHandlerFactory(handler_id=self.site_1.id))
        freight_delivery = FreightDeliveryFactory(consignee=ContractCommodityHandlerFactory(handler_id=self.site_2.id))
        order = FreightOrderFactory(provider=self.company, freight_pickup=freight_pickup,
                                    freight_delivery=freight_delivery)

        movement = FreightMovementFactory(order_id=order.id)
        slot = FreightSlot(
            status='booked',
            type='outload',
            start='2017-01-01',
            end='2017-01-02',
            site_id=self.site_1.id,
            order_id=order.id,
            freight_provider_id=self.company.id
        )
        slot.movement_id = movement.id
        slot.save()

        booking = SlotUpdateAlert(alert=self.slot_update_alert, action='Book',
                                  slots=FreightSlot.objects.filter(id__in=[slot.id]), movement=movement,
                                  order=order, updated_fields=None)
        self.assertEqual(booking.get_subject(), f'[Booked] Slot at {self.site_1.name}')

        update = SlotUpdateAlert(alert=self.slot_update_alert, action='Update',
                                 slots=FreightSlot.objects.filter(id__in=[slot.id]), movement=movement, order=order,
                                 updated_fields=[{'field': 'tonnage', 'prev_value': "10 MT", 'current_value': "20 MT"}])
        self.assertEqual(update.get_subject(), f'[Updated] Slot at {self.site_1.name}')

        cancel = SlotUpdateAlert(alert=self.slot_update_alert, action='Cancel',
                                 slots=FreightSlot.objects.filter(id__in=[slot.id]), movement=movement, order=order,
                                 updated_fields=None)
        self.assertEqual(cancel.get_subject(), f'[Cancelled] Slot at {self.site_1.name}')

    def test_get_recipients(self):
        from core.company_sites.models import FreightSlot
        from core.freights.models import FreightContract
        self.set_up_alerts()
        provider = CompanyFactory()
        seller = CompanyFactory()
        driver = EmployeeFactory(type_id=9, company=provider, email='<EMAIL>')
        freight_pickup = FreightPickupFactory(consignor=ContractCommodityHandlerFactory(handler_id=self.site_1.id))
        freight_delivery = FreightDeliveryFactory(consignee=ContractCommodityHandlerFactory(handler_id=self.site_2.id))
        order = FreightOrderFactory(provider=provider, freight_pickup=freight_pickup, type_id=2,
                                    freight_delivery=freight_delivery)
        order.seller = PartyFactory(company=seller)
        order.save()
        movement = FreightMovementFactory(order_id=order.id, provider=provider, assign_to=driver)
        slot = FreightSlot(
            status='booked',
            type='outload',
            start='2017-01-01',
            end='2017-01-02',
            site_id=self.site_1.id,
            order_id=order.id,
            freight_provider_id=provider.id,
            driver_id=driver.id
        )
        slot.movement_id = movement.id
        slot.save()

        booking = SlotUpdateAlert(alert=self.slot_update_alert, action='Book',
                                  slots=FreightSlot.objects.filter(id__in=[slot.id]),
                                  movement=FreightContract.objects.filter(id__in=[movement.id]),
                                  order=order, updated_fields=None)
        recipients = booking.get_recipients()

        self.assertIn('<EMAIL>', recipients[self.company.id])
        self.assertIn('<EMAIL>', recipients[self.company.id])
        self.assertEqual(recipients[provider.id], ['<EMAIL>'])
        self.assertEqual(recipients[seller.id], ['<EMAIL>'])

# pylint: disable=too-many-instance-attributes
@tag('model')
class Class1MaterialAlertTest(AlertTestSetup):

    def setUp(self):
        from core.vendor_decs.models import CommodityVendorDec
        self.set_up_alerts()
        self.freight_provider = CompanyFactory(id=10054)
        self.driver = EmployeeFactory(company=self.freight_provider, email='<EMAIL>')
        self.truck = TruckFactory(company=self.freight_provider)
        self.movement = FreightMovementFactory(
            provider=self.freight_provider,
            freight_pickup=FreightPickupFactory(consignor=ContractCommodityHandlerFactory(handler_id=self.site_2.id)),
            freight_delivery=FreightDeliveryFactory(
                consignee=ContractCommodityHandlerFactory(handler_id=self.site_1.id))
        )
        self.movement.planned_truck = self.truck
        self.movement.save()

        self.params = {
            'id': 123,
            'assigned_rego': self.truck.rego,
            'driver_id': self.driver.id,
            'movement': self.movement,
            'provider': self.freight_provider,
            'commodity_id': self.movement.commodity_id,
            'prior_loads': {
                "2nd last load prior to current load": {},
                "3rd last load prior to current load": {},
                "Last load prior to current load": {}
            },
            "truck_cleanliness": {
                "2nd last cleaning prior to loading": {self.movement.truck.rego: "Pressure Cleaned"},
                "3rd last cleaning prior to loading": {self.movement.truck.rego: "Pressure Cleaned"},
                "Cleaning prior to loading": {self.movement.truck.rego: "Pressure Cleaned"},
                "If cleaning chemical used indicate type/brand": {self.movement.truck.rego: None},
                "Visual inspection prior to loading": {self.movement.truck.rego: "yes"}
            }
        }
        self.user = EmployeeFactory(company=self.company)
        self.vendor_dec = CommodityVendorDec.persist(self.params.copy(), self.user)
        self.instance = Class1MaterialAlert(alert=self.class_1_material_alert, entity=self.movement,
                                            vendor_dec=self.vendor_dec,  country=self.company.country)

    def test_get_recipients(self):
        recipients = self.instance.get_recipients()
        self.assertIn(self.company_admin.email, recipients[self.company.id])
        self.assertIn(self.user.email, recipients[self.company.id])
        self.assertEqual(recipients[self.freight_provider.id], ['<EMAIL>'])

    @patch('core.services.internal.pdf.HTMLToPDF.from_string')
    @patch('core.alerts.models.render_to_string')
    def test_make_data(self, mock_render_to_string, mock_from_string):
        mock_render_to_string.return_value = '<html><body>Vendor Dec</body></html>'
        mock_from_string.return_value = b'Vendor Dec Content'
        self.instance.make_data()

        mock_render_to_string.assert_called_once_with(
            'truck_cleanliness_vendordec.html',
            {
                'instance': self.vendor_dec,
                'unit': self.vendor_dec.commodity.unit,
                'country_code': self.vendor_dec.country_code
            }
        )
        mock_from_string.assert_called_once_with('<html><body>Vendor Dec</body></html>', False)
        self.assertEqual(self.instance.pdf, b'Vendor Dec Content')

    def test_get_template_args(self):
        self.instance.site = get(self.movement, 'freight_delivery.consignee.handler')

        tz = get(self.site_1, 'timezone.location')
        self.assertEqual(
            self.instance.get_template_args(),
            {'site': self.site_1, 'contract': self.movement, 'vendor_dec': self.vendor_dec,
             'booking_date': DateTimeUtil.localize_date(get(self.movement, 'freight_delivery.date_time'), tz),
             'booking_time': DateTimeUtil.localize_time(get(self.movement, 'freight_delivery.date_time'), tz),
             'load_type': None, 'country_code': self.company.country})

        instance_1 = Class1MaterialAlert(alert=self.class_1_material_alert, entity=self.movement,
                                         vendor_dec=self.vendor_dec, load_type='outload', country=self.company.country)
        instance_1.site = get(self.movement, 'freight_pickup.consignor.handler')
        self.assertEqual(
            instance_1.get_template_args(),
            {'site': self.site_2, 'contract': self.movement, 'vendor_dec': self.vendor_dec,
             'booking_date': DateTimeUtil.localize_date(get(self.movement, 'freight_pickup.date_time'), tz),
             'booking_time': DateTimeUtil.localize_time(get(self.movement, 'freight_pickup.date_time'), tz),
             'load_type': 'outload', 'country_code': self.company.country})


# pylint: disable=too-many-instance-attributes
@tag('model')
class BaseCheckpointAlertTest(AlertTestSetup):

    def setUp(self):
        self.set_up_alerts()
        self.freight_provider = CompanyFactory(id=10054)
        self.customer = CompanyFactory()
        self.customer_site_admin = EmployeeFactory(company=self.customer, email='<EMAIL>', type_id=4)
        self.driver = EmployeeFactory(company=self.freight_provider, email='<EMAIL>')
        self.truck = TruckFactory(company=self.freight_provider)
        self.pickup = FreightPickupFactory(consignor=ContractCommodityHandlerFactory(handler_id=self.site_2.id))
        self.delivery = FreightDeliveryFactory(
                consignee=ContractCommodityHandlerFactory(handler_id=self.site_1.id))
        self.movement = FreightMovementFactory(customer=PartyFactory(company=self.customer),
                                               provider=self.freight_provider,
                                               freight_pickup=self.pickup, freight_delivery=self.delivery)
        self.movement.planned_truck = self.truck
        self.movement.assign_to = self.driver
        self.movement.save()
        self.outload = LoadFactory(farm=self.site_2, type='outload')
        self.outload.checkpoint = self.pickup
        self.outload.movement = self.movement
        self.outload.save()

        self.inload = LoadFactory(farm=self.site_2, type='inload')
        self.inload.checkpoint = self.delivery
        self.inload.movement = self.movement
        self.inload.save()
        self.pickup_confirmation_alert = Alert.persist({
            'name': PICKUP_CONFIRMATION_ALERT_CODE,
            'frequency': 'daily',
            'channel': 'email',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'freight_provider',
                    'employee_roles': ['driver'],
                    'employees': []
                },
                {
                    'party': 'customer',
                    'employee_roles': ['site_admin'],
                    'employees': []
                },
            ],
            'site_recipients': [
                {
                    'site_id': self.site_1.id,
                    'recipient': {
                        'party': 'own_company',
                        'employee_roles': ['company_admin'],
                        'employees': []
                    }
                },
                {
                    'site_id': self.site_2.id,
                    'recipient': {
                        'party': 'own_company',
                        'employee_roles': ['site_admin'],
                        'employees': []
                    }
                },
            ]
        })
        self.pickup_instance = PickupInformationAlert(alert=self.pickup_confirmation_alert, entity=self.movement,
                                                      load=self.outload, country=self.company.country)

    def test_get_recipients(self):
        recipients = self.pickup_instance.get_recipients()
        self.assertEqual(recipients[self.freight_provider.id], [self.driver.email])
        self.assertEqual(recipients[self.site_2.company.id], [self.site_admin.email])
        self.assertEqual(recipients[self.customer.id], [self.customer_site_admin.email])

    @patch('core.services.internal.pdf.HTMLToPDF.from_string')
    @patch('core.alerts.models.render_to_string')
    def test_make_data(self, mock_render_to_string, mock_from_string):
        mock_render_to_string.return_value = '<html><body>Pickup Information</body></html>'
        mock_from_string.return_value = b'Pickup Information Content'
        self.pickup_instance.partyMapping = {
            self.freight_provider.id: 'provider',
            self.customer.id: 'customer',
        }
        party = self.pickup_instance.partyMapping.get(self.customer.id)
        self.pickup_instance.make_data_for_party(party, self.customer.id)

        mock_render_to_string.assert_called_once_with(
            'freight_contract_preview.html',
            {
                'movement': self.movement,
                'act': False,
                'urlprefix': STATIC_URL_PREFIX,
                'party': party,
                'footer': True,
                'note': self.pickup_instance.get_note(party),
                'can_view_pickup': (
                    self.movement.can_view_pickup(user=None, company_id=self.customer.id)
                    if self.movement
                    else True
                ),
                'can_view_delivery': (
                    self.movement.can_view_delivery(user=None, company_id=self.customer.id)
                    if self.movement
                    else True
                ),
            }
        )
        mock_from_string.assert_called_once_with('<html><body>Pickup Information</body></html>', False)

    def test_get_template_args(self):
        self.assertEqual(
            self.pickup_instance.get_template_args(),
            {'contract': self.movement, 'load': self.outload, 'slot': None, 'slot_url': None, 'site_operator': None,
             'time_in': None, 'country_code': self.company.country}
        )


@tag('model')
class OutloadDoneAlertTest(AlertTestSetup):

    def setUp(self):
        self.set_up_alerts()
        self.truck = TruckFactory(company=self.company)
        self.pickup = FreightPickupFactory(consignor=ContractCommodityHandlerFactory(handler_id=self.site_2.id))
        self.delivery = FreightDeliveryFactory(
            consignee=ContractCommodityHandlerFactory(handler_id=self.site_1.id))
        self.movement = FreightMovementFactory(provider=self.company,
                                               freight_pickup=self.pickup, freight_delivery=self.delivery)
        self.movement.planned_truck = self.truck
        self.movement.save()
        self.outload = LoadFactory(farm=self.site_2, type='outload', truck=self.truck)
        self.outload.checkpoint = self.pickup
        self.outload.movement = self.movement
        self.outload.save()

    def test_get_recipients(self):
        instance = OutloadDoneAlert(alert=self.pickup_confirmation_sms_alert, load=self.outload)
        recipients = instance.get_recipients()
        self.assertIn(self.company_admin.email, recipients[self.company.id])
        self.assertIn(self.employee_1.email, recipients[self.company.id])


@tag('model')
class DocketClarificationAlertTest(AlertTestSetup):

    def setUp(self):
        self.set_up_alerts()
        from core.freights.models import DocketClarification
        self.pickup = FreightPickupFactory(consignor=ContractCommodityHandlerFactory(handler_id=self.site_2.id))
        self.freight_contract = FreightMovementFactory(provider=self.company, freight_pickup=self.pickup)
        self.outload = LoadFactory(farm=self.site_2, type='outload')
        self.outload.checkpoint = self.pickup
        self.outload.movement = self.freight_contract
        self.outload.save()

        docket_clarification = DocketClarification(freight_contract=self.freight_contract, date_time=timezone.now(),
                                                   load_type='outload', category='Docket Issue', sub_category=['Site'])
        docket_clarification.save()
        twenty_four_hours_ago = timezone.now() - datetime.timedelta(hours=24)

        self.pending_dockets = DocketClarification.objects.filter(date_time__gte=twenty_four_hours_ago)
        self.instance = DocketClarificationAlert(alert=self.docket_clarification_alert,
                                                 pending_dockets=self.pending_dockets, alert_type='daily_and_weekly')

    def test_get_recipients(self):
        recipients = self.instance.get_recipients()[self.company.id]
        self.assertIn(self.company_admin.email, recipients)
        self.assertIn(self.employee_1.email, recipients)

    def test_make_data(self):
        # pylint: disable=line-too-long
        from core.settings import WEB_URL
        item = self.pending_dockets.first()
        url = f"{WEB_URL}/#/?referrerUrl=/freights/movements/{item.freight_contract.id}/details"

        self.instance.make_data()
        self.assertEqual(self.instance.clarification_count, 1)
        self.assertEqual(
            self.instance.file_content,
            'Date,Identifier,Load Type,Pickup Site,Category,Sub Category,Comments,Action\r\n'
            f'{item.date_time.strftime("%d/%m/%y")},{item.freight_contract.identifier},Outload,'
            f'{get(item.freight_contract, "outload.farm.display_name")},Docket Issue,,,"=HYPERLINK(""{url}"", ""Resolve"")"\r\n'
        )

    def test_template_args(self):
        #pylint: disable=line-too-long
        self.instance.company_docket_clarifications = self.pending_dockets
        self.assertEqual(
            self.instance.get_template_args(),
            {'customer_company_name': self.company.name, 'is_daily_alert': self.instance.is_daily_alert}
        )
        instance_2 = DocketClarificationAlert(
            alert=self.docket_clarification_alert, pending_dockets=self.pending_dockets)
        instance_2.company_docket_clarifications = self.pending_dockets
        item = self.pending_dockets.first()
        self.assertEqual(
            instance_2.get_template_args(),
            {'docket_data': [{'date': item.date_time.strftime('%d/%m/%y'),
                              'identifier': item.freight_contract.identifier, 'load_type': 'Outload',
                              'pickup_site': get(item.freight_contract, 'outload.farm.display_name'),
                              'category': 'Docket Issue', 'sub_category': '',
                              'comments': None,
                              'action': f'http://localhost:3000/#/?referrerUrl=/freights/movements/{self.freight_contract.id}/details'}],
             'customer_company_name': self.company.name, 'is_daily_alert': True}
        )

# pylint: disable=too-many-instance-attributes
@tag('model')
class StockOperationsUpdateAlertTest(AlertTestSetup):

    def setUp(self):
        from core.stocks.models import Stock
        self.set_up_alerts()
        self.load = LoadFactory(farm=self.site_1)
        self.instance = StockOperationsUpdateAlert(alert=self.stock_update_alert, operation_name="Direct Load",
                                                   farm=self.site_1, user=self.employee_1, updated_at=timezone.now(),
                                                   load=self.load, is_update_operation=True)

        self.stock_owner = CompanyFactory()
        self.ngr = NgrFactory(company=self.stock_owner)
        self.stock_owner_site_admin = EmployeeFactory(company=self.stock_owner, type_id=4)
        self.load_1 = LoadFactory(farm=self.site_1, ngr_id=self.ngr.id, type='inload')
        self.stock = Stock(ngr_id=self.load_1.ngr.id, commodity_id=self.load_1.commodity_id,
                           grade_id=self.load_1.grade_id, season=self.load_1.season, storage_id=self.load_1.storage_id)
        self.stock.inload = self.load_1
        self.stock.save()
        self.instance_1 = StockOperationsUpdateAlert(alert=self.stock_update_alert, operation_name="Stock Swap",
                                                     farm=get(self.stock.inload, 'farm'), load=self.stock.inload,
                                                     stock=self.stock, user=self.employee_1, updated_at=timezone.now())

    def test_get_recipients(self):
        recipients = self.instance.get_recipients()[self.company.id]
        self.assertIn(self.company_admin.email, recipients)
        self.assertIn(self.employee_1.email, recipients)

        recipients = self.instance_1.get_recipients()
        self.assertIn(self.company_admin.email, recipients[self.company.id])
        self.assertIn(self.employee_1.email, recipients[self.company.id])
        self.assertIn(self.stock_owner_site_admin.email, recipients[self.stock_owner.id])


@tag('model')
class StockAutoUpdateAlertTest(AlertTestSetup):

    def setUp(self):
        self.set_up_alerts()
        self.stock_owner = CompanyFactory(type_id=BHC_TYPE_ID)
        self.instance = StockAutoUpdateAlert(
            alert=self.stock_auto_update_alert, stock_owner_company_name=self.stock_owner.name,
            business_change_text="Grower to Non Grower")

    def test_get_recipients(self):
        recipients = self.instance.get_recipients()[self.company.id]
        self.assertIn(self.company_admin.email, recipients)
        self.assertIn(self.employee_1.email, recipients)

    def test_get_template_args(self):
        self.assertEqual(self.instance.get_template_args(),
                         {'stock_owner_company_name': self.stock_owner.name,
                          'business_change_text': "Grower to Non Grower"})

    def test_get_subject(self):
        self.assertEqual(self.instance.get_subject(),
                         f"AgriChain | Auto Update to Stock owned by {self.stock_owner.name} at your sites")

@tag('model')
class RecipientTest(AlertTestSetup):
    def test_get_employee_communication_channel_value(self):
        self.set_up_alerts()
        recipient = self.mass_limit_alert.recipient_set.filter(party='freight_provider').first()
        recipients = recipient.get_employee_communication_channel_value(self.freight_provider, 'email')
        self.assertEqual(recipients, ['<EMAIL>'])

        recipient = self.mass_limit_alert.recipient_set.filter(party='own_company').first()
        recipients = recipient.get_employee_communication_channel_value(self.company, 'email')
        self.assertEqual(set(recipients), {'<EMAIL>', '<EMAIL>'})

    def test_get_driver_of_the_movement_from_slot(self):
        from core.company_sites.models import FreightSlot
        self.set_up_alerts()
        site = FarmFactory(company=self.company)
        freight_provider = CompanyFactory(id=10054)
        driver = EmployeeFactory(company=freight_provider, email='<EMAIL>')
        movement = FreightMovementFactory(provider=freight_provider, assign_to_id=driver.id)
        slot = FreightSlot(start='2023-01-01', end='2023-01-01', site=site, freight_provider=freight_provider,
                           customer=self.company, driver=driver)
        slot.movement = movement
        slot.save()
        recipient = self.pickup_confirmation_alert.recipient_set.filter(party='freight_provider').first()
        recipients = recipient.get_employee_communication_channel_value(freight_provider, 'email', movement)

        self.assertEqual(len(recipients), recipient.employees.count()+1)
        self.assertIn(driver.email, recipients)

    def test_get_driver_of_the_movement_from_load(self):
        self.set_up_alerts()
        freight_provider = CompanyFactory(id=10054)
        driver = EmployeeFactory(company=freight_provider, email='<EMAIL>')
        movement = FreightMovementFactory(provider=freight_provider, assign_to_id=driver.id)
        LoadFactory(type='inload', driver_id=driver.id, movement=movement)
        recipient = self.pickup_confirmation_alert.recipient_set.filter(party='freight_provider').first()
        recipients = recipient.get_employee_communication_channel_value(freight_provider, 'email', movement)
        self.assertEqual(len(recipients), recipient.employees.count()+1)
        self.assertIn(driver.email, recipients)

    def test_get_driver_of_the_movement_for_class_1_material(self):
        from core.vendor_decs.models import CommodityVendorDec
        self.set_up_alerts()
        freight_provider = CompanyFactory(id=10054)
        driver = EmployeeFactory(company=freight_provider, email='<EMAIL>')
        movement = FreightMovementFactory(provider=freight_provider)
        params = {
            'id': 123,
            'driver_id': driver.id,
            'movement': movement,
            'provider': freight_provider,
        }
        user = EmployeeFactory(company=self.company)
        vendor_dec = CommodityVendorDec.persist(params.copy(), user)
        recipient = self.class_1_material_alert.recipient_set.filter(party='freight_provider').first()
        recipients = recipient.get_employee_communication_channel_value(freight_provider, 'email', movement, vendor_dec)
        self.assertEqual(len(recipients), recipient.employees.count() + 1)
        self.assertIn(driver.email, recipients)


@tag('view')
class AlertsViewTest(AlertTestSetup, AuthSetup):
    @patch('core.alerts.views.Alert')
    def test_post_201(self, alert_klass_mock):
        alert_mock = Mock()
        alert_klass_mock.persist = Mock(return_value=alert_mock)
        alert_mock.to_dict = Mock(return_value={'foo': 'bar'})
        alert_mock.errors = {}
        res = self.client.post(
            '/alerts/',
            {'name': 'foobar'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(res.status_code, 201)
        self.assertEqual(res.data, {'foo': 'bar'})
        alert_klass_mock.persist.assert_called_once_with({'name': 'foobar', 'created_by_id': ANY, 'updated_by_id': ANY})

    @patch('core.alerts.views.Alert')
    def test_post_400(self, alert_klass_mock):
        alert_mock = Mock()
        alert_klass_mock.persist = Mock(return_value=alert_mock)
        alert_mock.to_dict = Mock(return_value={'foo': 'bar'})
        res = self.client.post(
            '/alerts/',
            {'test_key': 'test_val'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(res.status_code, 400)

    def test_get_200(self):
        self.set_up_alerts()
        res = self.client.get(
            '/alerts/?company_id={}'.format(self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(res.status_code, 200)
        self.assertEqual(len(res.data), 15)


@tag('view')
class AlertViewTest(AlertTestSetup, AuthSetup):
    def test_get_200(self):
        self.set_up_alerts()
        res = self.client.get(
            '/alerts/{}/'.format(self.mass_limit_alert.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(res.status_code, 200)
        self.assertEqual(res.data['name'], 'mass_limit')
        self.assertEqual(res.data['companyId'], self.company.id)
        self.assertEqual(len(res.data['recipients']), 3)

    def test_get_404(self):
        res = self.client.get(
            '/alerts/400/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(res.status_code, 404)

    def test_put_200(self):
        self.set_up_alerts()
        recipient = self.inload_done_alert.recipient_set.filter(party='stock_owner').first()
        self.assertEqual(recipient.employee_roles, ['company_admin'])
        self.assertEqual(self.inload_done_alert.is_active, True)
        data = {
            'name': DELIVERY_CONFIRMATION_SMS_ALERT_CODE,
            'frequency': None,
            'channel': 'sms',
            'company_id': self.company.id,
            'recipients': [
                {
                    'party': 'stock_owner',
                    'employee_roles': ['company_admin', 'site_admin'],
                    'employees': []
                },
            ],
            'is_active': False
        }
        res = self.client.put(
            '/alerts/{}/'.format(self.inload_done_alert.id),
            data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(res.status_code, 200)
        self.inload_done_alert.refresh_from_db()
        recipient = self.inload_done_alert.recipient_set.filter(party='stock_owner').first()
        self.assertEqual(recipient.employee_roles, ['company_admin', 'site_admin'])
        self.assertEqual(self.inload_done_alert.is_active, False)

    def test_put_400(self):
        self.set_up_alerts()
        res = self.client.put(
            '/alerts/{}/'.format(self.inload_done_alert.id),
            {'test_key': 'test_val'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(res.status_code, 400)

    def test_put_404(self):
        res = self.client.put(
            '/alerts/100/',
            {'name': 'foobar'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(res.status_code, 404)


@tag('view')
class AlertCanConfigureAlertViewTest(AlertTestSetup, AuthSetup):
    def test_get_200(self):
        res = self.client.get(
            '/alerts/can-configure-alert/{}/?company_id={}'.
            format(DELIVERY_CONFIRMATION_SMS_ALERT_CODE, self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(res.status_code, 200)
        self.assertEqual(res.data, {'canConfigureAlert': True})
        self.set_up_alerts()
        res = self.client.get(
            '/alerts/can-configure-alert/{}/?company_id={}'.format(SPEC_AVG_REPORT_ALERT_CODE, self.company.id),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(res.status_code, 200)
        self.assertEqual(res.data, {'canConfigureAlert': False})
