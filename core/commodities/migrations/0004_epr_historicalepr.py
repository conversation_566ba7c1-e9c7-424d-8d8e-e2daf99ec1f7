# Generated by Django 2.1.2 on 2018-10-23 05:40
import django.db.models.deletion
import simple_history.models
from django.conf import settings
from django.db import migrations, models

import core.common.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('commodities', '0003_auto_20180827_1431'),
    ]

    operations = [
        migrations.CreateModel(
            name='EPR',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('rate', core.common.models.RoundedDecimalField(decimal_places=2, max_digits=8)),
                ('manager', models.CharField(max_length=255)),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='commodities_epr_related_created_by', related_query_name='commodities_eprs_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='commodities_epr_related_updated_by', related_query_name='commodities_eprs_updated_by', to=settings.AUTH_USER_MODEL)),
                ('variety', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='epr', to='commodities.Variety')),
            ],
            options={
                'db_table': 'variety_eprs',
            },
        ),
        migrations.CreateModel(
            name='HistoricalEPR',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('rate', core.common.models.RoundedDecimalField(decimal_places=2, max_digits=8)),
                ('manager', models.CharField(max_length=255)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_date', models.DateTimeField()),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('variety', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='commodities.Variety')),
            ],
            options={
                'verbose_name': 'historical epr',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
