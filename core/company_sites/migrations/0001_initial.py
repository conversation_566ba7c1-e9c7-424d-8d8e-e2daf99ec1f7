# Generated by Django 2.0.7 on 2018-08-16 10:18

import django.core.validators
import simple_history.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CompanySite',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(max_length=50)),
                ('phone', models.CharField(max_length=20, validators=[django.core.validators.RegexValidator('^(02|03|07|08)[0-9]+$', 'Must start with 02, 03, 07 or 08')])),
                ('email', models.EmailField(max_length=254)),
                ('delivery_hours_from', models.TimeField()),
                ('delivery_hours_to', models.TimeField()),
            ],
            options={
                'db_table': 'company_sites',
            },
        ),
        migrations.CreateModel(
            name='HistoricalCompanySite',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('name', models.CharField(max_length=50)),
                ('phone', models.CharField(max_length=20, validators=[django.core.validators.RegexValidator('^(02|03|07|08)[0-9]+$', 'Must start with 02, 03, 07 or 08')])),
                ('email', models.EmailField(max_length=254)),
                ('delivery_hours_from', models.TimeField()),
                ('delivery_hours_to', models.TimeField()),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_date', models.DateTimeField()),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
            ],
            options={
                'verbose_name': 'historical company site',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
