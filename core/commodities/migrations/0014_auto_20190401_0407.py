# Generated by Django 2.1.7 on 2019-04-01 04:07

from django.db import migrations

def variety_replacements(apps, schema_editor):
    HomeStorage = apps.get_model('farms', 'HomeStorage')
    Load = apps.get_model('loads', 'Load')
    Contract = apps.get_model('contracts', 'Contract')
    FreightContract = apps.get_model('freights', 'FreightContract')
    FreightOrder = apps.get_model('freights', 'FreightOrder')
    Variety = apps.get_model('commodities', 'Variety')

    # Canola - Strut(343) ->  Pioneer® Sturt TT(341)
    HomeStorage.objects.filter(variety_id=343).update(variety_id=341)
    Load.objects.filter(variety_id=343).update(variety_id=341)
    Contract.objects.filter(variety_id=343).update(variety_id=341)
    FreightContract.objects.filter(variety_id=343).update(variety_id=341)
    FreightOrder.objects.filter(variety_id=343).update(variety_id=341)

    Variety.objects.filter(id=343).delete()

    #<PERSON><PERSON> - <PERSON><PERSON><PERSON>(704) -> <PERSON><PERSON><PERSON>(705)
    HomeStorage.objects.filter(variety_id=704).update(variety_id=705)
    Load.objects.filter(variety_id=704).update(variety_id=705)
    Contract.objects.filter(variety_id=704).update(variety_id=705)
    FreightContract.objects.filter(variety_id=704).update(variety_id=705)
    FreightOrder.objects.filter(variety_id=704).update(variety_id=705)

    Variety.objects.filter(id=704).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0013_auto_20190322_1149'),
        ('farms', '0028_auto_20190305_0657'),
        ('loads', '0026_auto_20190125_0452'),
        ('contracts', '0064_auto_20190328_0454'),
        ('freights', '0041_auto_20190311_0850'),
    ]

    operations = [
        migrations.RunPython(variety_replacements)
    ]
