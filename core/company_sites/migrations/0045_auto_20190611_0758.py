# Generated by Django 2.1.7 on 2019-06-11 07:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('farms', '0033_auto_20190529_1047'),
        ('company_sites', '0044_auto_20190610_1320'),
    ]

    operations = [
        migrations.AddField(
            model_name='freightslot',
            name='farm',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='farms.Farm'),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='farm',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='farms.Farm'),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='site',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='company_sites.CompanySite'),
        ),
    ]
