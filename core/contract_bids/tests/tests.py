from datetime import timedelta

from django.test import tag
from django.urls import reverse
from django.utils import timezone

from core.contract_bids.models import ContractBid
from core.common.tests import AuthSetup
from core.contracts.models import Party
from core.farms.tests.factories import FarmFactory
from core.ngrs.tests.factories import NgrFactory
from core.profiles.tests.factories import EmployeeFactory
from core.companies.tests.factories import CompanyFactory


@tag('view')
class ContractBidsViewTest(AuthSetup):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        super().setUp()

        self.buyer_contact = EmployeeFactory(company=self.company)
        self.buyer_ngr = NgrFactory(company=self.company)
        self.site = FarmFactory(company=self.company)
        self.buyer = Party.create(
            {
                'role': 'Buyer',
                'company': self.company,
                'contact': self.buyer_contact,
                'ngr': self.buyer_ngr
            }
        )

        self.other_company = CompanyFactory()
        self.other_buyer_contact = EmployeeFactory(company=self.other_company)
        self.other_buyer_ngr = NgrFactory(company=self.other_company)
        self.other_buyer = Party.create(
            {
                'role': 'Buyer',
                'company': self.other_company,
                'contact': self.other_buyer_contact,
                'ngr': self.other_buyer_ngr
            }
        )

        self.now = timezone.now() - timedelta(days=1)
        end_time = timezone.now() + timedelta(days=1)
        delivery_end_time = timezone.now() + timedelta(days=10)

        self.params = {
            'site_id': self.site.id,
            'commodity_id': 1,
            'season': '22/23',
            'grade_id': 7,
            'payment_term_id': 1,
            'payment_scale_id': 1,
            'buyer': {
                'company_id': self.company.id,
                'contact_id': self.buyer_contact.id,
                'ngr_id': self.buyer_ngr.id
            },
            'price': 100,
            'limit': 100,
            'track': 'track',
            'start_date_time': self.now.strftime('%Y-%m-%d %H:%M:%S'),
            'end_date_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
            'delivery_start_date': self.now.strftime('%Y-%m-%d'),
            'delivery_end_date': delivery_end_time.strftime('%Y-%m-%d'),
            'group_id': None
        }
        get_test_params_1 = {
            'site_id': self.site.id,
            'commodity_id': 1,
            'season': '22/23',
            'grade_id': 7,
            'payment_term_id': 1,
            'payment_scale_id': 1,
            'buyer': {
                'company': self.other_company,
                'contact': self.other_buyer_contact,
                'ngr': self.other_buyer_ngr
            },
            'price': 100,
            'limit': 100,
            'track': 'track',
            'start_date_time': self.now.strftime('%Y-%m-%d %H:%M:%S'),
            'end_date_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
            'delivery_start_date': self.now.strftime('%Y-%m-%d'),
            'delivery_end_date': delivery_end_time.strftime('%Y-%m-%d'),
            'group_id': None
        }

        get_test_params_2 = get_test_params_1.copy()
        get_test_params_2.update({
            'price': 150,
            'limit': 200,
            'season': '23/24',
            'buyer': {'company': self.company, 'contact': self.buyer_contact, 'ngr': self.buyer_ngr}
        })

        get_test_params_3 = get_test_params_1.copy()
        get_test_params_3.update({
            'price': 150,
            'limit': 200,
            'season': '23/24',
            'buyer': {'company': self.company, 'contact': self.buyer_contact, 'ngr': self.buyer_ngr},
            'status': 'inactive'
        })

        self.test_contract_bid_1 = ContractBid.persist(get_test_params_1)
        self.test_contract_bid_2 = ContractBid.persist(get_test_params_2)
        self.test_contract_bid_2.created_by = self.employee
        self.test_contract_bid_2.save()
        self.test_contract_bid_3 = ContractBid.persist(get_test_params_3)
        self.test_contract_bid_3.created_by = self.employee
        self.test_contract_bid_3.save()


    def test_post_201(self):
        response = self.client.post(
            reverse('core.contract_bids:contract_bid-list'),
            data=self.params.copy(),
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(ContractBid.active.count(), 3)

        end_date = self.now + timedelta(days=20)
        data = self.params.copy()
        data.update({'limit': 200, 'delivery_end_date': end_date.strftime('%Y-%m-%d')})

        response = self.client.post(
            reverse('core.contract_bids:contract_bid-list'),
            data=data,
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(ContractBid.active.count(), 4)

    def test_get_list(self):
        response = self.client.get(
            reverse('core.contract_bids:contract_bid-list'),
            HTTP_AUTHORIZATION=f'Token {self.token}'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['buyer']['companyId'], self.other_company.id)

    def test_get_list_my_active_tab(self):
        response = self.client.get(
            reverse('core.contract_bids:contract_bid-list') + '?tab=my_active',
            HTTP_AUTHORIZATION=f'Token {self.token}'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['buyer']['companyId'], self.company.id)

    def test_get_list_my_archived_tab(self):
        response = self.client.get(
            reverse('core.contract_bids:contract_bid-list') + '?tab=archived',
            HTTP_AUTHORIZATION=f'Token {self.token}'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['buyer']['companyId'], self.company.id)

    def test_get_list_with_search(self):
        response = self.client.get(
            reverse('core.contract_bids:contract_bid-list') + '?search=track',
            HTTP_AUTHORIZATION=f'Token {self.token}'
        )

        self.assertEqual(response.status_code, 200)
        self.assertTrue(all('track' in bid['track'].lower() for bid in response.data['results']))

    def test_get_list_with_ordering(self):
        response = self.client.get(
            reverse('core.contract_bids:contract_bid-list') + '?order_by=price&order=asc',
            HTTP_AUTHORIZATION=f'Token {self.token}'
        )

        self.assertEqual(response.status_code, 200)
        prices = [bid['price'] for bid in response.data['results']]
        self.assertEqual(prices, sorted(prices))

@tag('view')
class ContractBidViewTest(AuthSetup):

    def setUp(self):
        super().setUp()
        self.buyer_contact = EmployeeFactory(company=self.company)
        self.buyer_ngr = NgrFactory(company=self.company)
        self.site = FarmFactory(company=self.company)
        self.buyer = Party.create(
            {'role': 'Buyer', 'company': self.company, 'contact': self.buyer_contact, 'ngr': self.buyer_ngr})
        self.now = timezone.now()
        end_time = self.now + timedelta(days=1)
        delivery_end_time = self.now + timedelta(days=10)
        self.params = {
            'site_id': self.site.id,
            'commodity_id': 1,
            'season': '22/23',
            'grade_id': 7,
            'payment_term_id': 1,
            'payment_scale_id': 1,
            'buyer': {'company': self.company, 'contact': self.buyer_contact, 'ngr': self.buyer_ngr},
            'price': 100,
            'limit': 100,
            'track': 'track',
            'start_date_time': self.now.strftime('%Y-%m-%d %H:%M:%S'),
            'end_date_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
            'delivery_start_date': self.now.strftime('%Y-%m-%d'),
            'delivery_end_date': delivery_end_time.strftime('%Y-%m-%d'),
            'group_id': None
        }
        self.contract_bid = ContractBid.persist(self.params)

    def test_put_200(self):
        response = self.client.put(
            reverse('core.contract_bids:contract_bid-detail',  kwargs={'contract_bid_id': self.contract_bid.id}),
            data={'price': 120, 'season': '24/25', 'limit': 200},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['price'], 120.0)
        self.assertEqual(response.data['season'], '24/25')
        self.assertEqual(response.data['limit'], 200)

        response_1 = self.client.put(
            reverse('core.contract_bids:contract_bid-detail', kwargs={'contract_bid_id': self.contract_bid.id}),
            data={'status': 'inactive'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response_1.status_code, 204)
        self.assertEqual(ContractBid.active.count(), 0)

    def test_get_detail(self):
        response = self.client.get(
            reverse('core.contract_bids:contract_bid-detail',
                   kwargs={'contract_bid_id': self.contract_bid.id}),
            HTTP_AUTHORIZATION=f'Token {self.token}'
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['id'], self.contract_bid.id)
        self.assertEqual(response.data['price'], 100)
        self.assertEqual(response.data['limit'], 100)
        self.assertEqual(response.data['season'], '22/23')
        self.assertEqual(response.data['siteId'], self.site.id)
        self.assertEqual(response.data['buyer']['companyId'], self.company.id)

    def test_get_detail_not_found(self):
        response = self.client.get(
            reverse('core.contract_bids:contract_bid-detail',
                   kwargs={'contract_bid_id': 99999}),
            HTTP_AUTHORIZATION=f'Token {self.token}'
        )
        self.assertEqual(response.status_code, 404)

@tag('view')
class ContractBidContractViewTest(AuthSetup):

    def setUp(self):
        super().setUp()
        self.buyer_contact = EmployeeFactory(company=self.company)
        self.buyer_ngr = NgrFactory(company=self.company)
        self.site = FarmFactory(company=self.company)
        self.buyer = Party.create(
            {'role': 'Buyer', 'company': self.company, 'contact': self.buyer_contact, 'ngr': self.buyer_ngr})
        self.now = timezone.now()
        end_time = self.now + timedelta(days=1)
        delivery_end_time = self.now + timedelta(days=10)
        self.params = {
            'site_id': self.site.id,
            'commodity_id': 1,
            'season': '22/23',
            'grade_id': 7,
            'payment_term_id': 1,
            'payment_scale_id': 1,
            'buyer': {'company': self.company, 'contact': self.buyer_contact, 'ngr': self.buyer_ngr},
            'price': 100,
            'limit': 100,
            'track': 'track',
            'start_date_time': self.now.strftime('%Y-%m-%d %H:%M:%S'),
            'end_date_time': end_time.strftime('%Y-%m-%d %H:%M:%S'),
            'delivery_start_date': self.now.strftime('%Y-%m-%d'),
            'delivery_end_date': delivery_end_time.strftime('%Y-%m-%d'),
            'group_id': None
        }
        self.contract_bid = ContractBid.persist(self.params)

    def test_post_200(self):
        response = self.client.post(
            reverse('core.contract_bids:contract_bid-contract',  kwargs={'contract_bid_id': self.contract_bid.id}),
            data={'quantity': 12},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.data['contractBidId'], self.contract_bid.id)
        self.assertEqual(self.contract_bid.limit_remaining, 88)
