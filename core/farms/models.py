import copy
import csv
import io
import json
import re
import time
from collections import defaultdict
from datetime import datetime, timedelta
from numbers import Number

import requests
from django.apps import apps
from django.conf import settings
from django.contrib.contenttypes.fields import GenericRelation
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models, transaction
from django.db.models import Q, Sum, UniqueConstraint, Count, Case, When, Value, CharField, F
from django.utils import timezone
from django.utils.functional import cached_property
from notifications.signals import notify
from pydash import get, compact, remove, flatten

import core.common.constants
from core.alerts.constants import STOCK_ALERT_DATA_LIMIT
from core.commodities.constants import EMPLOYEE_UNIT_CHOICES
from core.commodities.models import (
    Commodity,
    Variety,
    Grade,
    get_variety_by_loads_greatest_tonnage,
    get_grade_by_storage_levels_greatest_tonnage, get_grade_by_loads_greatest_tonnage)
from core.common.constants import BIG_NUMBER, SYSTEM_COMPANY_IDS, AU_ROOT_USER_ID, MT, BALES, MODULES, KM, MILES
from core.common.exceptions import ErrorException
from core.common.expressions import CustomRegexWrapper, Lower
from core.common.models import BaseModel, DeletedRecords, RawModel
from core.common.utils import (
    to_display_attr,
    is_staging,
    is_production,
    to_filter_clause, get_grade_display_name,
    strip_special, get_unique_list_of_objects, clean_string,
    get_request_country_code, generate_identifier, is_super_admin)
from core.companies.constants import (
    DEFAULT_START_OF_WEEK, DEFAULT_END_OF_WEEK, FEES, WAREHOUSE_INVOICE_MONTHLY_FREQUENCY
)
from core.companies.models import EstablishmentMixin, Company
from core.companies.tasks import date_time_filter_calculation
from core.countries.models import Country
from core.farms.constants import (CANNOT_CREATE_EMPLOYEE_FOR_REGISTERED_UNLINKED_FARM_TO_BROKER_REASON,
                                  CANNOT_CREATE_FARM_EMPLOYEE_BASED_ON_ROLE_REASON,
                                  CANNOT_CREATE_EMPLOYEE_FOR_REGISTERED_UNLINKED_FARM_TO_NON_BROKER_REASON,
                                  ACQUISITION_UPLOAD_CSV_HEADERS,
                                  STORAGE_TYPE_CONTAINER, EXTERNAL_SYNC_CHOICES, UNKNOWN_SITE)
from core.freights.constants import (PACK_ORDER_TYPE_ID, COMPLETED_STATUS, CUSTOMER_ONLY_TYPE_ID, DELIVERED_STATUS,
                                     NULL_STATUSES)
from core.loads.constants import (OPTION_TYPE_SILO_TO_SILO_TRANSFER,
                                  OPTION_TYPE_REGRADE_RESEASON_LOAD, OPTION_TYPE_STOCK_SWAP,
                                  EXTERNAL_LOAD_EXISTS_ERROR_MESSAGE, OPTION_TYPE_STORAGE_EMPTY_LOAD,
                                  OPTION_TYPE_STOCK_EMPTY_LOAD, OPTION_TYPE_STORAGE_UPDATE_LOAD,
                                  OPTION_TYPE_STOCK_UPDATE_LOAD, STORAGE_STOCK_UPDATE_LOAD, STOCK_UPDATE_LOAD,
                                  STORAGE_STOCK_EMPTY_LOAD, STOCK_EMPTY_LOAD, REGRADED_LOAD, STORAGE_TRANSFER_LOAD,
                                  STOCK_SWAP_LOAD)
from core.locations.constants import AUSTRALIA
from core.locations.models import Location
from core.marketzones.models import Marketzone
from core.profiles.constants import ROLE_BASED_CREATE_UPDATE_PERMS, EMPLOYEE_ROLE_DISPLAY_NAME, FARM_EMPLOYEE
from core.regions.models import Region
from core.services.external.aws import S3
from core.services.external.google import GoogleMaps
from core.services.internal.errbit import ERRBIT_LOGGER
from core.states.models import State
from core.timezones.models import Timezone
from core.timezones.serializers import TimezoneSerializer
from core.timezones.utils import DateTimeUtil
from core.trucks.constants import FLEET_REGO
from core.validation.validators import PHONE_MOBILE_REGEX, season_validator


# use for storage view - as we are considering net weight without shrinkages
def tonnage_sum_from_loads_ignoring_shrinkage(loads_qs, exclude_shrinkage=True):
    from core.loads.models import Load

    inloads = loads_qs.filter(type=Load.INLOAD)
    outloads = loads_qs.filter(type=Load.OUTLOAD)
    if exclude_shrinkage:
        inloads = inloads.exclude(id__in=Load.objects.filter(
            id__in=inloads.values_list('system_loads', flat=True), type=Load.INLOAD, source=Load.SHRINKAGE_LOAD
        ).values_list('id', flat=True))
        inloads = inloads.exclude(id__in=Load.objects.filter(
            id__in=outloads.values_list('system_loads', flat=True), type=Load.INLOAD, source=Load.SHRINKAGE_LOAD
        ).values_list('id', flat=True))

    inload_tonnages = Load.get_net_weight(inloads)
    outload_tonnages = Load.get_net_weight(outloads)
    return round(float(inload_tonnages - outload_tonnages), 2)


# use for ownership view - as we have to consider shrinkage for NGR
def tonnage_sum_from_loads_considering_shrinkage(loads_qs):
    from core.loads.models import Load

    loads = Load.objects.filter(id__in=loads_qs.values_list('id', flat=True))

    inloads = loads.filter(type=Load.INLOAD)
    outloads = loads.filter(type=Load.OUTLOAD)

    # fetching shrinkage loads associated with outloads and inloads to subtract their sum or we can use
    # unshrunk tonnage for outloads and shrunk tonnage for inloads for tonnage sum of outloads but that takes more time
    outload_shrinkage_loads = Load.objects.filter(id__in=outloads.values_list(
        'system_loads', flat=True), type=Load.INLOAD, source=Load.SHRINKAGE_LOAD).exclude(status='void')
    inload_shrinkage_loads = Load.objects.filter(id__in=inloads.values_list(
        'system_loads', flat=True), type=Load.INLOAD, source=Load.SHRINKAGE_LOAD).exclude(status='void')

    inload_tonnages = Load.get_net_weight(inloads)
    inload_shrinkage_tonnages = Load.get_net_weight(inload_shrinkage_loads)
    outload_tonnages = Load.get_net_weight(outloads)
    outload_shrinkage_tonnages = Load.get_net_weight(outload_shrinkage_loads)

    return round(inload_tonnages - inload_shrinkage_tonnages - outload_tonnages - outload_shrinkage_tonnages, 2)


def get_shrunk_tonnage_for_grower(tonnage, site_id, commodity_id, ngr_id):
    from core.ngrs.models import Ngr
    farm = Farm.objects.filter(id=site_id).first()
    ngr = Ngr.objects.filter(id=ngr_id).first()
    if farm and ngr:
        date_time = timezone.now()
        shrinkage = get(
            Shrinkage.get_shrinkage_for(
                site_id=site_id, commodity_id=commodity_id, for_company_id=ngr.company_id,
                start_date__lte=date_time, end_date__gte=date_time
            ),
            'percentage'
        ) or 0
        if shrinkage:
            tonnage = float(tonnage) - (float(tonnage) * (shrinkage / 100))
    return tonnage


def get_average_spec_params_by_loads(load_qs, total_tonnage, load_tonnage_map=None):
    if not load_qs.exists():
        return

    commodity = Commodity.objects.filter(id__in=load_qs.values_list('commodity_id', flat=True)).first()

    if not commodity:
        return

    def _get_spec_values_by_iteration(calc_func):
        result = {}
        remaining_tonnage = float(total_tonnage)

        for load in load_qs:
            if remaining_tonnage:
                if isinstance(load_tonnage_map, dict):
                    weight = get(load_tonnage_map, str(load.id)) or 0
                else:
                    weight = load.net_weight
                weight = float(weight)
                relevant_tonnage = remaining_tonnage if remaining_tonnage < weight else weight

                for spec in commodity.specs:
                    spec_code = spec['code'].lower()
                    inload_spec_param = load.specs.get(spec_code, None)

                    if isinstance(inload_spec_param, Number):
                        result[spec_code] = calc_func(result, spec_code, relevant_tonnage, load)

                remaining_tonnage -= relevant_tonnage

        return result

    def _spec_tonnages_calc_func(result, spec_code, relevant_tonnage, _):
        return result.get(spec_code, 0) + relevant_tonnage

    _spec_tonnages = _get_spec_values_by_iteration(_spec_tonnages_calc_func)

    def _spec_params_calc_func(result, spec_code, relevant_tonnage, load):
        return float(
            result.get(spec_code, 0)) + float(
            load.specs[spec_code]) * float(relevant_tonnage) / float(_spec_tonnages.get(spec_code))

    return _get_spec_values_by_iteration(_spec_params_calc_func)


def get_average_spec_params_by_storage_levels(storage_levels, tonnage=None, commodities=None, commodity=None):  # pylint: disable=too-many-branches
    if not storage_levels or not storage_levels.exists():
        return

    if not commodity:
        commodities = commodities or Commodity.objects.filter(
            id__in=storage_levels.values_list('load__commodity_id', flat=True)
        )
        commodity = commodities.first()

    total_tonnage = float(
        (storage_levels.aggregate(tonnage=Sum('tonnage'))['tonnage'] or 0) if tonnage is None else tonnage
    )

    def _get_spec_values_by_iteration(calc_func):
        result = {}
        remaining_tonnage = total_tonnage

        for level in storage_levels:
            if remaining_tonnage:
                level_tonnage = float(level.tonnage)
                relevant_tonnage = remaining_tonnage if remaining_tonnage < level_tonnage else level_tonnage

                for spec in commodity.specs:
                    spec_code = get(spec, 'code', '').lower()
                    spec_value = level.load.specs.get(spec_code, None)
                    if spec_value and isinstance(spec_value, str):
                        try:
                            spec_value = float(spec_value)
                        except ValueError:
                            spec_value = None

                    if isinstance(spec_value, Number):
                        result[spec_code] = calc_func(result, spec_code, relevant_tonnage, level)

                remaining_tonnage -= relevant_tonnage

        return result

    def _spec_tonnages_calc_func(result, spec_code, relevant_tonnage, _):
        return result.get(spec_code, 0) + relevant_tonnage

    _spec_tonnages = _get_spec_values_by_iteration(_spec_tonnages_calc_func)

    def _spec_params_calc_func(result, spec_code, relevant_tonnage, storage_level):
        return float(
            result.get(spec_code, 0)) + float(
            storage_level.load.specs[spec_code]) * float(relevant_tonnage) / float(_spec_tonnages.get(spec_code))

    return _get_spec_values_by_iteration(_spec_params_calc_func)


def get_merged_spec_params(specs_1, specs_2, tonnage_1, tonnage_2):
    merged_specs = {}

    spec_codes = {*specs_1.keys(), *specs_2.keys()}
    total_tonnage = float(tonnage_1) + float(tonnage_2)

    for spec_code in spec_codes:
        if specs_1.get(spec_code, None) and specs_2.get(spec_code, None):  # pylint: disable=no-else-continue
            merged_specs[spec_code] = (
                                              specs_1.get(spec_code) * float(tonnage_1) / total_tonnage
                                      ) + (specs_2.get(spec_code) * float(tonnage_2) / total_tonnage)
            continue
        elif specs_1.get(spec_code, None):
            merged_specs[spec_code] = specs_1.get(spec_code)
            continue
        elif specs_2.get(spec_code, None):
            merged_specs[spec_code] = specs_2.get(spec_code)

    return merged_specs


def get_variety_details_by_loads(loads, to_dict=True):
    if not loads:
        return {
            'is_blended': False,
            'variety': None,
            'dominant_variety': None
        }

    count = len(set(loads.values_list('variety_id', flat=True)))

    details = {
        'is_blended': count > 1,
        'variety': get(loads.first(), 'variety') if count == 1 else None,
        'dominant_variety': get_variety_by_loads_greatest_tonnage(loads),
    }

    if details['variety'] and to_dict:
        details['variety'] = details['variety'].to_dict(excludes=['epr'])

    return details


def get_season_by_storage_levels(storage_levels):
    if not storage_levels:
        return None

    seasons = list(set(storage_levels.values_list('load__season', flat=True)))
    return get_one_or_multiple(seasons)


def get_grade_by_storage_levels(storage_levels):
    return get_load_attr_by_storage_levels(storage_levels, 'grade_id', Grade)


def get_variety_by_storage_levels(storage_levels):
    return get_load_attr_by_storage_levels(storage_levels, 'variety_id', Variety)


def get_load_attr_by_storage_levels(storage_levels, attr, klass):
    if not storage_levels:
        return None

    load_attr = f'load__{attr}'

    values = set(storage_levels.order_by(load_attr).values_list(load_attr, flat=True))
    value = get_one_or_multiple(compact(values))
    if value in ['Multiple', None]:
        return value
    return get(klass.objects.filter(id=value).first(), 'name')


def get_one_or_multiple(values):
    count = len(values)
    if count == 1:
        return values[0]
    if count > 1:
        return 'Multiple'
    return None


def get_season_by_loads(loads):
    if not loads:
        return None

    seasons = list(set(loads.values_list('season', flat=True)))
    return get_one_or_multiple(seasons)


def get_quantity_by_storage_level(storage_levels):
    total_quantity = 0
    for storage_level in storage_levels:
        quantity = get(storage_level, 'load.quantity', 0)
        if quantity:
            total_quantity += quantity

    return total_quantity


def get_quantity_by_loads(loads):
    from core.loads.models import Load
    outload_quantity = loads.filter(type=Load.OUTLOAD).aggregate(quantity=Sum('quantity'))['quantity'] or 0
    inload_quantity = loads.filter(type=Load.INLOAD).aggregate(quantity=Sum('quantity'))['quantity'] or 0
    return inload_quantity - outload_quantity


def default_setup_asset_settings():
    return []


def commodity_specs_for_csv(specs):
    spec_params = []
    if specs:
        for k, v in specs.items():
            spec_params.append(k.lower())
            spec_params.append("{:.2f}".format(v))
    else:
        for _ in range(1, 7):
            spec_params.append('')
            spec_params.append('')
    return spec_params


def create_stocks_csv_row(ngr, farm, commodity, grade_display_name, season, unshrunk_tonnage,
                          custom_csv_headers, country, unit):
    unit = unit or country.display_unit
    display_unit = f' (in {unit})' if country.display_unit_in_brackets_for_csv else ''
    tonnage_label = country.get_label('tonnage')
    columns_mapping = {
        'Owner': ngr.owner_company if ngr else '',
        'NGR': ngr.ngr_number if ngr else '',
        'Location': farm.display_name,
        'Commodity': commodity.display_name,
        'Grade': grade_display_name,
        'Season': season,
        f'{tonnage_label} With Shrinkage{display_unit}': unshrunk_tonnage and "{:.5f}".format(
            float(unshrunk_tonnage)
        ),
        f'{tonnage_label} Unit': unit
    }
    if custom_csv_headers:
        return [columns_mapping.get(column) for column in custom_csv_headers]
    else:
        return list(columns_mapping.values())


class FarmAcceptanceRequest(BaseModel):
    class Meta:
        db_table = 'farm_acceptance_requests'

    GROWER_RAISED = 'grower_raised'
    BROKER_RAISED = 'broker_raised'

    types = (
        (GROWER_RAISED, 'Grower Raised'),
        (BROKER_RAISED, 'Broker Raised'),
    )

    farm = models.OneToOneField('farms.Farm', on_delete=models.CASCADE, primary_key=True)
    resolved = models.BooleanField(default=False)
    accepted = models.BooleanField(default=False)
    type = models.CharField(max_length=20, choices=types)
    rejection_reason = models.TextField(null=True, blank=True)

    @property
    def is_grower_raised(self):
        return self.type == self.GROWER_RAISED

    @property
    def is_broker_raised(self):
        return self.type == self.BROKER_RAISED

    @property
    def is_pending(self):
        return not self.resolved

    @property
    def is_accepted(self):
        return self.resolved and self.accepted

    @property
    def is_rejected(self):
        return self.resolved and not self.accepted

    def mark_accepted(self, user):
        self.mark_resolved(user, True)

    def mark_rejected(self, user):
        self.mark_resolved(user, False)

    def mark_resolved(self, user, accepted=False):
        if self.is_pending:
            self.resolved = True
            self.accepted = accepted
            self.updated_by = user
            self.save()

    @property
    def is_accepted_by_grower(self):
        return self.is_broker_raised and self.is_accepted

    def is_pending_for_user(self, user):
        result = False

        if self.is_pending:
            result = (
                self.is_grower_raised and user.company.is_broker and self.farm.broker_company_id == user.company_id
            ) or (
                self.is_broker_raised and user.company.is_grower and user.company_id == self.farm.company_id
            )

        return result

    def is_pending_on_grower_raised_by_broker_user(self, user):
        return self.is_pending and self.is_broker_raised and self.farm.broker_company_id == user.company_id

    def get_notifiers_for_acceptance(self, user):
        admins = []
        if self.is_accepted:
            if self.is_grower_raised:
                admins = self.farm.broker_and_farm_admins([user.id], False)
            if self.is_broker_raised:
                admins = self.farm.company_and_farm_admins()

        return admins

    def get_notifiers_for_rejectance(self, user):
        admins = []
        if self.is_rejected:
            if self.is_grower_raised:
                admins = self.farm.company_and_farm_admins()
            if self.is_broker_raised:
                admins = self.farm.broker_and_farm_admins([user.id], False)

        return admins

    @classmethod
    def create_broker_raised_grower_accepted_request(cls, farm):
        request = cls(
            farm=farm,
            resolved=True,
            accepted=True,
            type=cls.BROKER_RAISED,
            created_by_id=farm.created_by_id,
            updated_by_id=farm.updated_by_id,
        )
        request.save()

        return request

    @classmethod
    def create_broker_raised_grower_pending_request(cls, farm):
        request = cls(
            farm=farm,
            type=cls.BROKER_RAISED,
            created_by_id=farm.created_by_id,
            updated_by_id=farm.updated_by_id,
        )
        request.save()

        return request

    @classmethod
    def create_grower_raised_broker_accepted_request(cls, farm):
        request = cls(
            farm=farm,
            resolved=True,
            accepted=True,
            type=cls.GROWER_RAISED,
            created_by_id=farm.created_by_id,
            updated_by_id=farm.updated_by_id,
        )
        request.save()

        return request

    @classmethod
    def create_grower_raised_broker_pending_request(cls, farm):
        request = cls(
            farm=farm,
            type=cls.GROWER_RAISED,
            created_by_id=farm.created_by_id,
            updated_by_id=farm.updated_by_id,
        )
        request.save()

        return request


class BHCManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(mode__isnull=False)


class Farm(BaseModel, EstablishmentMixin):  # pylint: disable=too-many-public-methods,too-many-instance-attributes
    class Meta:
        db_table = "farms"

    objects = models.Manager()
    bhcs = BHCManager()

    mandatory_props = BaseModel.mandatory_props + ['display_name', 'name', 'is_bhc', 'ld']

    KEY_CONTACT_RELATION_NAME = 'farm_key_contacts_set'

    name = models.CharField(max_length=255, null=False, blank=False)
    address = models.OneToOneField(
        Location,
        related_name='farm',
        on_delete=models.DO_NOTHING,
    )
    market_zone = models.ForeignKey(
        Marketzone,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING
    )
    region = models.ForeignKey(
        Region, null=True, blank=True, on_delete=models.DO_NOTHING
    )
    company = models.ForeignKey(
        Company,
        null=False,
        blank=False,
        on_delete=models.CASCADE,
        related_name='farm_set'
    )
    mobile = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        validators=[PHONE_MOBILE_REGEX]
    )
    broker_company = models.ForeignKey(
        Company,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='broker_farm_set'
    )
    delivery_mandatory_fields = models.JSONField(null=True, blank=True)
    #bhc
    tracks = models.JSONField(null=True, blank=True)
    mode = models.CharField(max_length=255, null=True, blank=True)
    stocks_management = models.BooleanField(default=False)
    gross_first_inload = models.BooleanField(default=False)
    gross_first_outload = models.BooleanField(default=False)
    weigh_bridge = models.BooleanField(default=False)
    weigh_bridge_company_name = models.CharField(max_length=255, null=True, blank=True)
    copy_from_inload = models.BooleanField(default=False)
    values_to_copy = ArrayField(models.CharField(max_length=255), null=True, blank=True)
    copy_to_outload_for_storage = models.BooleanField(default=False)
    values_to_copy_for_outload_from_storage = ArrayField(models.CharField(max_length=255), null=True, blank=True)
    default_track = models.CharField(max_length=255, null=True, blank=True)
    populate_default_weights = models.BooleanField(default=False)
    is_cash_prices_enabled = models.BooleanField(default=True)
    external_plant_codes = models.ManyToManyField(
        Company, related_name='external_plant_codes', through='FarmExternalPlantCode'
    )
    allow_inload_slot_order_booking = models.BooleanField(default=True)
    allow_outload_slot_order_booking = models.BooleanField(default=True)

    # external
    externally_sync_source = models.CharField(max_length=255, null=True, blank=True, choices=EXTERNAL_SYNC_CHOICES)
    _external_connection_configurations = models.JSONField(null=True, blank=True)
    inarix_enabled = models.BooleanField(default=False)
    net_weight_when_packing_container = models.BooleanField(default=False)
    mandate_truck_vendor_dec = models.BooleanField(default=False)
    autoweigh_on_tare = models.BooleanField(default=False)
    autoweigh_on_gross = models.BooleanField(default=False)

    FILLABLES = [
        'name',
        'address',
        'market_zone_id',
        'region_id',
        'mobile',
        'office',
        'office_id',
        'company_id',
        'broker_company_id',
        'address_id',
        'is_grower_acceptance_required',
        'is_grower_request_accepted_by_broker',
        'is_broker_request_accepted_by_grower',
        'message',
        'existing_id',
        'tracks',
        'mode',
        'asset_id',
        'asset',
        'search_txt',
        'stocks_management',
        'gross_first_inload',
        'gross_first_outload',
        'weigh_bridge',
        'weigh_bridge_company_name',
        'values_to_copy',
        'copy_from_inload',
        'copy_to_outload_for_storage',
        'values_to_copy_for_outload_from_storage',
        'merge_from',
        'default_track',
        'populate_default_weights',
        'is_cash_prices_enabled',
        'external_plant_codes',
        'allow_inload_slot_order_booking',
        'allow_outload_slot_order_booking',
        'externally_sync_source',
        'inarix_enabled',
        'is_active',
        'net_weight_when_packing_container',
        'mandate_truck_vendor_dec',
        'autoweigh_on_tare',
        'autoweigh_on_gross',
    ]

    COMMON_RELATIONS = (
        'address',
        'company',
        'broker_company',
        'market_zone',
        'region',
    )

    WEB_SEARCHABLE_FIELDS = [
        'name',
        'mobile',
        'key_contact_name_for_user',
        'market_zone.name',
        'region.name',
        'address.address',
        'company.name',
        'broker_company.name',
    ]

    @property
    def is_overdraft_allowed(self):
        return self.company.overdraft_transfer_allowed

    @property
    def is_variety_mandatory(self):
        return self.company.is_variety_mandatory

    @property
    def user_type_for_variety_mandatory(self):
        return self.company.user_type_for_variety_mandatory or []

    @property
    def load_type_for_variety_mandatory(self):
        return self.company.load_type_for_variety_mandatory or []

    @property
    def sitemanagementsettings(self):
        from core.company_sites.models import SiteManagementSettings
        sm_settings = get(self.company, 'sitemanagementsettings')
        if get(
                sm_settings, 'order_booking'
        ) and not self.allow_inload_slot_order_booking and not self.allow_outload_slot_order_booking:
            dummy_settings = SiteManagementSettings.dummy_settings()
            if sm_settings.allowed_truck_company_ids:
                dummy_settings.allowed_truck_company_ids = sm_settings.allowed_truck_company_ids
            if sm_settings.load_by_load_transfer:
                dummy_settings.load_by_load_transfer = sm_settings.load_by_load_transfer
            return dummy_settings
        return sm_settings

    @property
    def settings(self):
        return None if (
                get(
                    self.company, 'sitemanagementsettings.order_booking'
                ) and self.allow_inload_slot_order_booking and self.allow_outload_slot_order_booking
        ) else self.sitemanagementsettings.to_dict()

    @cached_property
    def country_id(self):
        return self.company.country_id

    @cached_property
    def country_code(self):
        return self.company.country_code

    @cached_property
    def country(self):
        return self.company.country

    @classmethod
    def sorted_tracks(cls):
        return sorted(cls.all_tracks())
    @classmethod
    def all_tracks(cls):
        return list(set(flatten([list(t) for t in cls.sites_with_tracks().values_list('tracks', flat=True)])))

    @classmethod
    def sites_with_tracks(cls):
        return cls.objects.filter(mode__isnull=False)


    def update_relations(self, _=None):
        from core.contracts.models import Contract, TitleTransfer
        from core.freights.models import FreightOrder, FreightContract

        for contract in Contract.objects.filter(contractcommodityhandler_set__handler_id=self.id):
            contract.set_viewer_company_ids()
        delivery_orders = FreightOrder.objects.filter(freight_delivery__consignee__handler_id=self.id)
        pickup_orders = FreightOrder.objects.filter(freight_pickup__consignor__handler_id=self.id)
        orders = [*delivery_orders, *pickup_orders]
        for order in orders:
            order.set_viewer_company_ids()
        for movement in FreightContract.objects.filter(order__in=orders):
            movement.set_viewer_company_ids()
        other_movements = FreightContract.objects.exclude(order__in=orders)
        for movement in other_movements.filter(freight_pickup__consignor__handler_id=self.id):
            movement.set_viewer_company_ids()
        for movement in other_movements.filter(freight_delivery__consignee__handler_id=self.id):
            movement.set_viewer_company_ids()
        for title_transfer in TitleTransfer.objects.filter(storage__farm_id=self.id):
            title_transfer.set_viewer_company_ids()

    @classmethod
    def find_by_name(cls, name, company_id):  # pylint: disable=arguments-differ
        return cls.objects.filter(name__iexact=name.strip(), company_id=company_id).first()

    @classmethod
    def get_by_name(cls, name, company_id):  # extensive and smart search
        name = strip_special(name)
        expression = CustomRegexWrapper('name').expression
        return cls.objects.filter(company_id=company_id).annotate(_name=expression).filter(_name=name).first()

    @classmethod
    def create_by_name(cls, name, company_id):
        params = {'name': name.strip(), 'company_id': company_id, 'address': AUSTRALIA}
        return cls.create_with_location(params)

    @classmethod
    def find_or_create_by_name(cls, name, company_id):
        farm = cls.find_by_name(name, company_id)
        if not farm:
            farm = cls.create_by_name(name, company_id)

        return farm

    @property
    def is_slot_order_booking_on(self):
        return self.sitemanagementsettings.order_booking

    @property
    def minimum_tonnage(self):
        return self.sitemanagementsettings.minimum_tonnage

    @property
    def company_slot_order_booking(self):
        return bool(get(self, 'company.sitemanagementsettings.order_booking'))

    @property
    def is_outload_slot_order_booking_on(self):
        return self.company_slot_order_booking and self.allow_outload_slot_order_booking

    @property
    def is_inload_slot_order_booking_on(self):
        return self.company_slot_order_booking and self.allow_inload_slot_order_booking

    @property
    def ld(self):
        return get(list((get(self, 'tracks') or {}).values()), '0')

    @classmethod
    def to_display_names(cls, farm_ids):
        return [farm.display_name for farm in Farm.objects.filter(id__in=farm_ids)]

    @classmethod
    def create_with_location(cls, params, kwargs=None, pre_validate_callback=None):
        kwargs = kwargs or {}
        from core.farm_fields.models import FarmField
        if params and params.get('company_id', None) not in SYSTEM_COMPANY_IDS:
            return super().create_with_location(params, kwargs, pre_validate_callback)
        location_types = {
            'Company': 'company',
            'Farm': 'farm',
            'FarmField': 'farmfield',
        }
        name_field = 'name'
        data = copy.deepcopy(params)
        address_data = data.pop('address', {})
        address_data['name'] = address_data.get(
            'name',
            address_data.get('address', data.get(name_field, cls.__name__))
        )
        address_data['location_type'] = location_types[cls.__name__]
        if isinstance(get(address_data, 'state'), str):
            state_name = address_data.pop('state')
            if state_name:
                address_data['state'] = State.objects.filter(name__iexact=state_name).first()
        address = Location.create(address_data)
        if getattr(address, 'errors', None):
            obj = cls.create(data, kwargs, pre_validate_callback)
            obj.errors = getattr(obj, 'errors', {})
            obj.errors.update(address.errors)
        else:
            kwargs['address_id'] = address.id
            obj = cls.objects.bulk_create([cls(**data, **kwargs)])[0]
            if obj.id:
                Storage.create_gate(obj)
                FarmField.create_unknown_farm_field(obj)
        return obj

    def clean(self):
        super().clean()
        if not self.company_slot_order_booking:
            self.allow_inload_slot_order_booking = False
            self.allow_outload_slot_order_booking = False

    @cached_property
    def timezone(self):
        return TimezoneSerializer(self._timezone).data

    @cached_property
    def _timezone(self):
        address = self.address
        return Timezone.from_coordinates(lat=address.latitude, lng=address.longitude)

    @property
    def gate(self):
        return self.storages.filter(is_gate=True).first()

    @property
    def unknown_field(self):
        return self.farmfield_set.filter(is_unknown=True, is_active=True).first()

    @property
    def company_address(self):
        return self.company_id and self.company.address_id and self.company.address.to_dict()

    @property
    def handler(self):
        return self.company if self.company_id else None

    @property
    def is_bhc(self):
        return self.mode is not None

    @property
    def phone(self):
        return self.mobile

    @property
    def transaction_participation(self):
        return self.company.transaction_participation

    @property
    def abn(self):
        return self.company.abn

    @property
    def entity_name(self):
        return self.company.entity_name

    @property
    def company_name(self):
        return self.company.name

    @property
    def farm_acceptance_request(self):
        return getattr(self, 'farmacceptancerequest', None)

    @property
    def latest_acceptance_request(self):
        return self.farm_acceptance_request

    @property
    def is_request_accepted(self):
        request = self.farm_acceptance_request
        return request and request.is_accepted

    @property
    def start_of_week(self):
        return self.company.start_of_week if self.company_id else DEFAULT_START_OF_WEEK

    @property
    def end_of_week(self):
        return self.company.end_of_week if self.company_id else DEFAULT_END_OF_WEEK

    @property
    def is_grower_acceptance_required(self):
        request = self.farm_acceptance_request
        return request and request.is_pending and request.is_broker_raised

    @property
    def is_broker_request_accepted_by_grower(self):
        request = self.farm_acceptance_request
        return request and request.is_accepted and request.is_broker_raised

    @property
    def is_grower_request_accepted_by_broker(self):
        request = self.farm_acceptance_request
        return request and request.is_accepted and request.is_grower_raised

    @classmethod
    def accepted_farms(cls, filters):
        return cls.objects.select_related(
            'farmacceptancerequest'
        ).filter(
            **filters
        ).filter(
            models.Q(
                models.Q(
                    farmacceptancerequest__isnull=True
                ) | models.Q(
                    farmacceptancerequest__accepted=True,
                    farmacceptancerequest__resolved=True,
                ) | models.Q(
                    farmacceptancerequest__type='grower_raised',
                )
            )
        )

    def send_slot_update_alert(self, action, slots, movement=None, order=None, updated_fields=None):
        from core.alerts.constants import SLOT_BOOKING_UPDATE_ALERT_CODE
        site_company = self.company
        if site_company:
            alert = site_company.get_alert_by_name(SLOT_BOOKING_UPDATE_ALERT_CODE)
            if alert:
                alert.process(
                    action=action, slots=slots, movement=movement, order=order, updated_fields=updated_fields
                )

    def get_empty_storages(self, only_containers=False):
        return Storage.objects.filter(
            id__in=self.storages_data(return_empty=True, only_containers=only_containers)
        ).filter(farm_id=self.id)

    @staticmethod
    def send_stock_update_alert( # pylint: disable=too-many-locals
            operation_name, farm, user, updated_at, load=None, load_identifier=None,
            outload=None, changes=None, stock_ngrs=None, stock=None, storage=None,
            commodities=None, grade_names=None, seasons=None, is_void=False, storages=None, is_update_operation=False):
        if site_company := get(farm, 'company'):
            from core.alerts.constants import STOCK_OPERATIONS_UPDATE_ALERT_CODE
            alert = site_company.get_alert_by_name(STOCK_OPERATIONS_UPDATE_ALERT_CODE)
            if get(alert, 'is_active'):
                alert.process(operation_name=operation_name, farm=farm, user=user,
                              updated_at=updated_at, load=load, changes=changes, outload=outload,
                              stock_ngrs=stock_ngrs, stock=stock, storage=storage, load_identifier=load_identifier,
                              commodities=commodities, grade_names=grade_names, seasons=seasons, is_void=is_void,
                              storages=storages, is_update_operation=is_update_operation)

    def get_archived_storages(self):
        return Storage.archived.filter(farm_id=self.id)

    def non_empty_storages(self, only_containers=False):
        return self.storages_data(return_non_empty=True, only_containers=only_containers)

    def get_storage_stocks_queryset(self):
        return self.stock_set.filter(storage__type__in=Storage.FIXED_STORAGE_TYPES)

    def get_storage_stock_distribution_queryset(self):
        tolerance = 1e-9
        return self.get_storage_stocks_queryset().values(
            'commodity_id', 'storage_id'
        ).annotate(commodity_tonnage=models.Sum('tonnage')).exclude(
            commodity_tonnage__lte=0 + tolerance, commodity_tonnage__gte=0 - tolerance)

    def get_ownership_stock_distribution_queryset(self):
        tolerance = 1e-9
        return self.get_storage_stocks_queryset().filter(ngr_id__isnull=False).values(
            'commodity_id', 'farm_id', 'grade_id', 'season', 'ngr_id'
        ).annotate(commodity_tonnage=models.Sum('tonnage')).exclude(
            commodity_tonnage__lte=0 + tolerance, commodity_tonnage__gte=0 - tolerance)

    def has_stock(self):
        return (self.get_storage_stock_distribution_queryset().exists() or
                self.get_ownership_stock_distribution_queryset().exists())

    def activate_or_archive_farm_storages_and_fields(self):
        now = timezone.now()

        self.farmfield_set.filter().update(is_active=self.is_active, updated_at=now)
        self.storage_set.filter().update(is_active=self.is_active, updated_at=now)

    def create_containers_from_csv(self, order, content, user, headers):
        reader = csv.DictReader(io.StringIO(content), headers)
        for i, row in enumerate(reader):
            if i == 0:
                continue
            data_row = dict(row).copy()
            container = self.create_container_from_data(data_row, user)
            if container and not container.errors:
                from core.freights.models import FreightContract
                FreightContract.from_movements_csv(data_row, order, user)

    def create_container_from_data(self, data, user):
        container_name = data['Container']
        container = self.storage_set.inactive_containers.filter(name=container_name).first()
        if not container:
            data = {
                'name': container_name,
                'size': data['Container Capacity'].replace(' ', ''),
                'type': STORAGE_TYPE_CONTAINER
            }
            container = self.add_storage(data, user)
            if container.errors:
                active_container = self.storage_set.active_containers.filter(name=container_name).first()
                if active_container:
                    from core.freights.models import FreightContract
                    related_fm_exists = FreightContract.objects.exclude(status__in=NULL_STATUSES).filter(
                        container_number__iexact=container_name).exists()
                    if not related_fm_exists:
                        container = active_container
                        container.save()
        else:
            container.is_active = True
            container.save()
        return container

    def storages_data(
            self, return_empty=False, return_non_empty=False, user=None, only_containers=False, storage_id=None,
            include_grade_season=False
    ):  # pylint: disable=too-many-locals,too-many-branches,no-else-break,too-many-statements
        from core.loads.models import Load
        storages = {'all': {'commodities': {}, 'info': {'id': 'all'}}} if not return_non_empty else {}
        empty_storages = []
        if storage_id:
            storages_queryset = Storage.objects.filter(id=storage_id)
        else:
            storages_queryset = Storage.objects.filter(farm_id=self.id)
            if only_containers:
                storages_queryset = storages_queryset.filter(type=STORAGE_TYPE_CONTAINER)
            else:
                storages_queryset = storages_queryset.filter(type__in=Storage.FIXED_STORAGE_TYPES)
        commodities_cache = {}
        for storage in storages_queryset:
            loads = Load.objects.filter(
                storage_id=storage.id
            ).exclude(status='void').exclude(source=Load.SHRINKAGE_LOAD)
            commodities = {}
            is_empty = True
            fields_required = ['commodity_id', 'type', 'estimated_net_weight']
            if include_grade_season:
                fields_required.append('grade_id')
                fields_required.append('season')
            loads_values = loads.values(*fields_required)
            result = {}
            for values in loads_values:
                commodity_id = values['commodity_id']
                if commodity_id not in result:
                    result[commodity_id] = {'inload': 0, 'outload': 0}
                result[commodity_id][values['type']] += values['estimated_net_weight'] or 0
                if include_grade_season:
                    result[commodity_id]['grade_id'] = values.get('grade_id') or get(result[commodity_id], 'grade_id')
                    result[commodity_id]['season'] = values.get('season') or get(result[commodity_id], 'season')
            for commodity_id, info in result.items():
                total_tonnage = info['inload'] - info['outload']
                is_not_empty = total_tonnage != 0
                if is_not_empty:
                    if return_empty:
                        is_empty = False
                        break
                    if commodity_id not in commodities_cache:
                        commodities_cache[commodity_id] = Commodity.objects.get(id=commodity_id)
                    commodity = commodities_cache[commodity_id]
                    commodity_info = {'id': commodity.id, 'name': commodity.name,
                                      'display_name': commodity.display_name}
                    if include_grade_season:
                        commodity_info.update({'grade_id': info.get('grade_id'), 'season': info.get('season')})
                    commodities[str(commodity_id)] = {'info': commodity_info, 'total': total_tonnage}
                    if not return_non_empty and str(commodity_id) in storages['all']['commodities']:
                        existing_commodity = storages['all']['commodities'][str(commodity_id)]
                        existing_commodity['total'] = existing_commodity['total'] + total_tonnage
                    elif not return_non_empty:
                        storages['all']['commodities'][str(commodity_id)] = {
                            'info': commodity_info, 'total': total_tonnage
                        }
            info = {'id': str(storage.id), 'name': storage.name}
            if storage.type == STORAGE_TYPE_CONTAINER:
                from core.freights.models import FreightContract
                pack_movement = FreightContract.objects.filter(
                    type_id=PACK_ORDER_TYPE_ID, loads__storage_id=storage.id, loads__type=Load.INLOAD,
                    status__in=['delivered', 'completed']
                ).first()
                if pack_movement:
                    ngr_id = get(pack_movement.inload, 'ngr_id')
                    if ngr_id:
                        info.update({'ngr_id': ngr_id})
                    info.update({'container_tare': pack_movement.container_tare})

            if commodities:
                storages[str(storage.id)] = {'commodities': commodities, 'info': info}
            elif is_empty and return_empty:
                empty_storages.append(storage.id)
            elif is_empty and not return_non_empty:
                name = storage.name if user and storage.is_owner(user) else storage.verbose_name
                _storage = {'id': storage.id, 'name': name, 'type_name': storage.type_name,
                            'address': {
                                'name': get(storage, 'address.name'), 'address': get(storage, 'address.address')},
                            'farm_id': storage.farm_id, 'size': storage.size}
                empty_storages.append(_storage)
        storages = {'non-empty': storages, 'empty': empty_storages}
        storages = storages['non-empty'] if return_non_empty else storages
        storages = empty_storages if return_empty else storages

        return storages

    @transaction.atomic
    def add_storage(self, params, current_user=None):
        for field in ['commodity_id', 'grade_id', 'variety_id', 'specs']:  # backward compatibility, these are deleted
            params.pop(field, None)
        params['unit'] = get(current_user, 'unit') or get(self.company, 'country.display_unit')
        params['address'] = get(params, 'address', {})
        if not get(params['address'], 'latitude'):
            params['address']['latitude'] = self.address.latitude
        if not get(params['address'], 'longitude'):
            params['address']['longitude'] = self.address.longitude
        if not get(params['address'], 'address'):
            params['address']['address'] = self.address.address
        if not get(params['address'], 'suburb'):
            params['address']['suburb'] = self.address.suburb
        if not get(params['address'], 'state'):
            params['address']['state'] = self.address.state
        if not get(params['address'], 'post_code'):
            params['address']['post_code'] = self.address.post_code
        if not get(params['address'], 'street_name'):
            params['address']['street_name'] = self.address.street_name

        return Storage.create_with_location({**params, 'farm_id': self.id}, current_user=current_user)

    @transaction.atomic
    def add_load(self, storage, params, user):
        if not storage:
            return
        inload_payload = params.pop('inload', None)
        load_date_time = inload_payload.pop('date_time', None)
        inload = storage.create_inload(
            {
                **(inload_payload or {}),
                'date_time': load_date_time or datetime.today(),
                'created_by_id': user.id,
                'updated_by_id': user.id,
            },
            current_user=user
        )

        return inload

    @classmethod
    def validate_acquisition_upload_csv(cls, content, user):
        buff = io.BytesIO(content)
        reasons = []
        headers = ACQUISITION_UPLOAD_CSV_HEADERS.copy()
        if is_super_admin(user.company_id):
            headers.append('Action')
        header = [item.lower() for item in headers]
        header_length = len(header)
        is_first_row = True
        for row in buff:
            try:
                data_row = row.decode('utf-8-sig', errors='replace').split(',')
                if data_row[0].lower().strip() == 'enter a unique identifier':
                    continue
                if is_first_row and len(data_row) != header_length:
                    reasons.append("Header names should match the template")
                    break
                item = data_row[len(data_row) - 1]
                data_row.remove(item)
                data_row.append(item.rstrip())
                if is_first_row:
                    if not [clean_string(item.lower()) for item in data_row] == header:
                        reasons.append("Header names should match the template")
                    is_first_row = False
                elif len(data_row) > header_length:
                    reasons.append("Uploaded CSV contains additional columns which do not exist in the template")
            except Exception:  # pylint: disable=broad-except
                reasons.append("CSV format is not valid. Please ensure UTF-8 text encoding.")
                return list(set(reasons))
        return list(set(reasons))

    def get_system_storage_for(self, location_id):  #  -- should be deleted soon - Sny
        return self.systemstorage_set.filter(location_id=location_id).first()

    def admin(self):
        return self.employees.filter(type_id=core.common.constants.FARM_ADMIN_TYPE_ID).first()

    def office_admins(self, _=None):
        return []

    def get_farm_admins(self):
        farm_admins = self._get_direct_farm_admins()

        if self.is_alone:
            return farm_admins.union(self._get_company_admins())
        else:
            return farm_admins

    def _get_direct_farm_admins(self):
        return self.employees.filter(type_id=core.common.constants.FARM_ADMIN_TYPE_ID)

    def _get_company_admins(self):
        return apps.get_model('profiles', 'Employee').objects.filter(
            company_id=self.company_id,
            type_id=core.common.constants.COMPANY_ADMIN_TYPE_ID,
        )

    def _get_company_admins_with_emails(self):
        return apps.get_model('profiles', 'Employee').objects.filter(
            company_id=self.company_id,
            type_id=core.common.constants.COMPANY_ADMIN_TYPE_ID,
            email__isnull=False,
        )

    def _get_broker_company_admins(self):
        return apps.get_model('profiles', 'Employee').objects.filter(
            company_id=self.company_id,
            type_id=core.common.constants.COMPANY_ADMIN_TYPE_ID,
        )

    @classmethod
    def create_main_farm(
            cls, company_id=None, company=None
    ):
        if company_id and not company:
            company = Company.objects.get(id=company_id)

        farm = cls()
        farm.company = company
        farm._copy_from_company()

        farm.save()
        farm.create_grower_raised_broker_accepted_request()

        return farm

    def send_request_accepted_notifications(self, user=None):
        user = user or self.updated_by
        if user:
            request = self.farm_acceptance_request
            if request and request.is_accepted:
                admins = request.get_notifiers_for_acceptance(user)
                description = self.farm_accepted_msg(sender=user)
                self.send_notifications(admins, user, 'Accepted', description)

    def send_request_rejected_notifications(self, user=None):
        user = user or self.updated_by
        if user:
            request = self.farm_acceptance_request
            if request and request.is_rejected:
                admins = request.get_notifiers_for_rejectance(user)
                description = self.farm_rejected_msg(user)
                verb = 'Broker Rejected' if request.is_grower_raised else 'Grower Rejected'
                self.send_notifications(admins, user, verb, description)

    def send_broker_removed_notifications(self, user=None):
        user = user or self.updated_by
        if user:
            admins = self.brokers([user.id])
            description = self.broker_removed_msg(user)
            self.send_notifications(admins, user, 'Removed', description)

            if self.broker_company:
                admins = self.company_and_farm_admins([user.id])
                description = self.broker_removed_msg_for_admin(user, self.broker_company)
                self.send_notifications(admins, user, 'Removed', description)

    def send_new_broker_assigned_notifications(self, user=None):
        user = user or self.updated_by
        if user:
            admins = self.brokers([], False)
            description = self.new_farm_msg(user)
            self.send_notifications(admins, user, 'Created', description)

            admins = self.company_and_farm_admins([user.id])
            description = self.new_farm_msg_for_admin(user, self.broker_company)
            self.send_notifications(admins, user, 'Created', description)

    def send_updated_notifications(self, user=None):
        user = user or self.updated_by
        if user:
            request = self.farm_acceptance_request
            if user.company.is_broker and request and request.is_broker_raised and request.is_accepted:
                admins = self.company_and_farm_admins()
            else:
                admins = self.broker_and_farm_admins([user.id])

            description = self.farm_updated_msg(user)
            self.send_notifications(admins, user, 'Updated', description)

    def send_created_notifications(self, user=None):
        user = user or self.created_by
        if user:
            creator = self.created_by
            description = self.new_farm_msg(sender=creator)
            admins = []

            if user.company.is_broker:
                user.company.add_farm_to_directory(farm_id=self.id)
                admins = self._get_company_admins()
            if self.broker_company_id:
                admins = self._get_broker_company_admins()

            for admin in admins:
                notify.send(
                    sender=creator,
                    recipient=admin,
                    verb='Created',
                    action_object=self,
                    description=description,
                    entity='farm',
                    app='farms',
                )

    def send_unregistered_company_farm_created_mail_to_company_admin(self, user=None):
        from core.jobs.models import Job
        user = user or self.created_by
        if user:
            if not is_staging() and not is_production() and user.company.is_broker and not self.is_registered:
                for company_admin in self._get_company_admins_with_emails():
                    Job.schedule_job_for_task(
                        job_type='send_farm_added_to_unregistered_company_mail',
                        params={
                            'employee_name': company_admin.first_name,
                            'farm_name': self.name,
                            'creator_name': self.created_by.name,
                            'company_name': self.company.name,
                            'email': company_admin.email
                        }
                    )

    def raise_acceptance_request(self, user=None):
        transaction_participation = self.transaction_participation
        user = user or self.created_by
        is_broker = user.company.is_broker
        is_grower = user.company.is_grower

        if transaction_participation and is_broker:
            return self.create_broker_raised_grower_pending_request()
        if transaction_participation and is_grower:
            return self.create_grower_raised_broker_pending_request()
        if not transaction_participation and is_broker:
            return self.create_broker_raised_grower_accepted_request()

    def create_broker_raised_grower_accepted_request(self):
        if self.broker_company_id and not self.farm_acceptance_request:
            FarmAcceptanceRequest.create_broker_raised_grower_accepted_request(self)

    def create_grower_raised_broker_accepted_request(self):
        if self.broker_company_id and not self.farm_acceptance_request:
            FarmAcceptanceRequest.create_grower_raised_broker_accepted_request(self)

    def create_broker_raised_grower_pending_request(self):
        if self.broker_company_id and not self.farm_acceptance_request:
            FarmAcceptanceRequest.create_broker_raised_grower_pending_request(self)

    def create_grower_raised_broker_pending_request(self):
        if self.broker_company_id and not self.farm_acceptance_request:
            FarmAcceptanceRequest.create_grower_raised_broker_pending_request(self)

    def _copy_from_company(self):
        for field in ['name', 'mobile', 'created_by_id', 'updated_by_id']:
            setattr(self, field, getattr(self.company, field))
        self._copy_address_from_company()

    def _copy_address_from_company(self):
        address = Location(location_type='farm')
        for field in ['name', 'address', 'latitude', 'longitude', 'created_by_id', 'updated_by_id']:
            setattr(address, field, getattr(self.company.address, field))

        address.save()
        self.address = address

    @classmethod
    def is_alone_in_company(cls, company_id):
        return Farm.objects.filter(company_id=company_id).count() == 1

    @property
    def has_siblings(self):
        return Farm.objects.filter(company_id=self.company_id).exclude(pk=self.id).exists()

    @property
    def is_alone(self):
        return not self.has_siblings

    @property
    def storages(self):
        return Storage.objects.filter(farm_id=self.id)

    @property
    def display_name(self):
        if self.company.name == self.name or self.company.is_system:
            return self.name

        return self.company.name + ' (' + self.name + ')'

    @classmethod
    def storages_with_deps(
            cls,
            farm_id,
            storage_id=None,
            inclusions=('address', 'farm'),
            archive=False
    ):
        queryset = Storage.archived.filter(farm_id=farm_id) if archive else Storage.objects.filter(farm_id=farm_id)

        if storage_id:
            queryset = queryset.filter(id=storage_id)
        if inclusions:
            queryset = queryset.select_related(*inclusions)
        return queryset

    def get_storages_criteria_for_user_for_stocks(self, user, include_containers=False):
        if get(user, 'is_staff') or self.is_company_employee(user) or self.is_managed_by_user(user):
            criteria = models.Q(farm_id=self.id)
        else:
            return None
        if not include_containers:
            criteria &= models.Q(storage__type__in=Storage.FIXED_STORAGE_TYPES)
        return criteria

    def get_all_loads_at_farm(self, only_storage=False):
        if get(self, 'all_loads'):
            return self.all_loads  # pylint: disable=access-member-before-definition
        self.all_loads = self.get_loads_at_farm_including_void(only_storage).exclude(status='void')

        return self.all_loads

    def get_loads_at_farm_including_void(self, only_storage=False):
        if get(self, 'all_loads_including_void'):
            return self.all_loads_including_void  # pylint: disable=access-member-before-definition
        if only_storage:
            self.all_loads_including_void = self.loads.filter(storage__type__in=Storage.FIXED_STORAGE_TYPES)
        else:
            self.all_loads_including_void = self.loads.filter(Q(farm_field__isnull=False) |
                                                              Q(storage__type__in=Storage.FIXED_STORAGE_TYPES))
            self.all_loads_including_void = self.loads.filter(
                id__in=self.all_loads_including_void.values_list('id', flat=True))

        return self.all_loads_including_void

    def new_farm_msg(self, sender):
        if sender.company.type_id == 2:
            return '<b>' + sender.name + '</b> from <b>' + sender.company.name + \
                   '</b> has sent you a request to manage a new farm <b>' + self.name + '</b>'
        return '<b>' + sender.company.name + \
               '</b> has sent you a request to manage their company\'s farm <b>' + self.name + \
               '</b>'

    def new_farm_msg_for_admin(self, sender, broker):
        return '<b>' + sender.name + '</b> from <b>' + sender.company.name + \
               '</b> has sent a request to <b>' + broker.name + \
               '</b> to manage the farm <b>' + self.name + '</b>'

    def farm_accepted_msg(self, sender):
        return '<b>' + sender.company.name + \
               '</b> has accepted your request to manage the farm <b>' + self.name + '</b>'

    def farm_rejected_msg(self, sender):
        return '<b>' + sender.company.name + \
               '</b> has rejected your request to manage the farm <b>' + self.name + '</b>'

    def broker_removed_msg(self, sender):
        return '<b>' + sender.company.name + \
               '</b> has removed you from their company\'s farm <b>' + self.name + '</b>'

    def broker_removed_msg_for_admin(self, sender, broker):
        return '<b>' + sender.name + '</b> from <b>' + sender.company.name + \
               '</b> has removed <b>' + broker.name + '</b> from the farm <b>' + self.name + '</b>'

    def farm_updated_msg(self, sender):
        return '<b>' + sender.name + '</b> from <b>' + sender.company.name + \
               '</b> has updated the details of the farm <b>' + self.name + '</b>'

    def broker_and_farm_admins(self, exclude_ids=None, acceptance_required=True):
        exclude_ids = exclude_ids or []
        if self.broker_company_id and (not acceptance_required or (acceptance_required and (
                self.is_grower_request_accepted_by_broker or
                self.is_broker_request_accepted_by_grower))):
            admins = apps.get_model('profiles', 'Employee').objects.filter(
                Q(type_id=1, company_id=self.broker_company_id) |
                Q(type_id=1, company_id=self.company_id) |
                Q(type_id=4, farms__id=self.id)
            ).exclude(id__in=exclude_ids).all()
            return admins

        admins = apps.get_model('profiles', 'Employee').objects.filter(
            Q(type_id=1, company_id=self.company_id) |
            Q(type_id=4, farms__id=self.id)
        ).exclude(id__in=exclude_ids).all()
        return admins

    def company_and_farm_admins(self, exclude_ids=None):
        exclude_ids = exclude_ids or []
        return apps.get_model('profiles', 'Employee').objects.filter(
            Q(type_id=1, company_id=self.company_id) |
            Q(type_id=4, farms__id=self.id)
        ).exclude(id__in=exclude_ids).all()

    def brokers(self, exclude_ids=None, acceptance_required=True):
        exclude_ids = exclude_ids or []
        admins = []
        if self.broker_company_id and (not acceptance_required or (acceptance_required and self.is_request_accepted)):
            admins = apps.get_model('profiles', 'Employee').objects.filter(
                type_id=core.common.constants.COMPANY_ADMIN_TYPE_ID,
                company_id=self.broker_company_id,
            ).exclude(id__in=exclude_ids).all()
        return admins

    def is_associated(self, user):
        return self.is_company_employee(user) or self.is_managed_by_user(user)

    def can_edit(self, user):
        return user.is_staff or (
            self.transaction_participation and self.is_associated(user)
        ) or not self.transaction_participation

    @property
    def is_registered(self):
        return self.company.is_registered

    @property
    def is_allowed_as_buyer_for_pool_contract(self):
        return self.company_id and self.company.is_allowed_as_buyer_for_pool_contract

    def is_highlighted(self, user):
        return self.is_pending_request(user)

    def is_pending_request(self, user):
        request = self.farm_acceptance_request
        return request and request.is_pending_for_user(user)

    def is_pending_request_for_grower(self, user):
        request = self.farm_acceptance_request
        return request and request.is_pending_on_grower_raised_by_broker_user(user)

    @property
    def is_accepted_by_grower(self):
        request = self.farm_acceptance_request
        return bool(not request or (request and request.is_accepted_by_grower))

    def is_creator(self, user):
        return self.created_by.company_id == user.company_id

    def is_in_added_companies(self, user):
        return self.company.is_added_company(user.company_id)

    def is_managed_by_user(self, user):
        return self.is_managed and self.broker_company_id == user.company_id

    def is_in_user_directory(self, user):
        return user.company.is_in_farm_directory(farm_id=self.id)

    def is_company_employee(self, user):
        return user.company_id == self.company_id

    @cached_property
    def is_managed(self):
        return self.broker_company_id and get(self.farm_acceptance_request, 'is_accepted')

    def is_managed_by_registered_company(self):
        return self.is_managed and self.broker_company.is_registered

    def is_managed_by_transaction_participator(self):
        return self.is_managed and self.broker_company.transaction_participation

    @property
    def managing_broker_company(self):
        broker_company = None

        if self.is_managed:
            broker_company = self.broker_company

        return broker_company

    @property
    def is_registered_or_managed_by_registered(self):
        return self.is_registered or self.is_managed_by_registered_company()

    @property
    def is_transaction_participator_or_managed_by_transaction_participator(self):
        return self.transaction_participation or self.is_managed_by_transaction_participator()

    def _get_request_accepted_value_from_params(self, params):
        if 'is_grower_request_accepted_by_broker' in params:
            params.pop('is_broker_request_accepted_by_grower', None)
            return params.pop(
                'is_grower_request_accepted_by_broker',
                not self.transaction_participation
            )
        if 'is_broker_request_accepted_by_grower' in params:
            params.pop('is_grower_request_accepted_by_broker', None)
            return params.pop('is_broker_request_accepted_by_grower', False)

    def _mark_request_resolved(self, user, accepted=False):
        request = self.farm_acceptance_request
        if request:
            request.mark_resolved(user, accepted)

    @classmethod
    @transaction.atomic
    def update_with_location(cls, instance_id, params, user, update_fields=None):  # pylint: disable=arguments-differ, arguments-renamed
        old_farm = Farm.objects.get(id=instance_id)
        if 'broker_company_id' in params and params.get(
                'broker_company_id', False
        ) is None and old_farm.farm_acceptance_request and user.company.is_broker:
            old_farm.farm_acceptance_request.delete()

        if 'is_broker_request_accepted_by_grower' in params or 'is_grower_request_accepted_by_broker' in params:
            old_farm._mark_request_resolved(user, old_farm._get_request_accepted_value_from_params(params))

        params.pop('brokerages', None) # to be deleted
        params.pop('is_broker_request_accepted_by_grower', None)
        params.pop('is_grower_request_accepted_by_broker', None)
        params.pop('is_grower_acceptance_required', None)
        address = get(params, 'address')
        is_address_coordinates_updated = (
                get(address, 'longitude') != get(old_farm, 'address.longitude') or
                get(address, 'latitude') != get(old_farm, 'address.latitude')
        )
        if (
                is_address_coordinates_updated and get(old_farm, 'address.state_id') and
                get(old_farm.country, 'config.invoicing.state_wise_levy')
        ):
            state_name = GoogleMaps.get_state(address)
            if state_name:
                state = State.objects.filter(name=state_name).first()
                if state:
                    params['address'].update({'state_id': state.id})
        farm = super().update_with_location(instance_id, params, update_fields)
        if not farm.errors and is_address_coordinates_updated:
            farm.update_unknown_field_and_gate_storage_address_from_self()
        farm.raise_acceptance_request(user)

        if not farm.errors:
            farm._farm_update_notifications(old_farm, user)
            farm.add_to_broker_company_dir()

        return farm

    def update_unknown_field_and_gate_storage_address_from_self(self):
        addresses = compact([get(self.unknown_field, 'address'), get(self.gate, 'address')])
        for address in addresses:
            address.address = self.address.address
            address.name = self.address.name
            address.latitude = self.address.latitude
            address.longitude = self.address.longitude
            address.save()

    def update_distance_in_associated_movements_and_orders_if_required(self, old_latitude, old_longitude, user):
        if self.address.latitude != old_latitude or self.address.longitude != old_longitude:
            from core.jobs.models import Job
            Job.schedule_job_for_task(
                'update_distance_in_movements_and_orders',
                params={'entity_id': self.id, 'entity_type': 'farm', 'user_id': user.id}
            )

    def _farm_update_notifications(self, old_farm, user):
        self.send_request_accepted_notifications(user)
        self.send_request_rejected_notifications(user)
        if old_farm.broker_company_id != self.broker_company_id:
            old_farm.send_broker_removed_notifications(user)
        else:
            self.send_updated_notifications(user)
        if not old_farm.broker_company_id and self.broker_company_id and self.broker_company_id != user.company_id:
            self.send_new_broker_assigned_notifications(user)

    def add_to_broker_company_dir(self):
        if self.broker_company_id:
            self.company.add_company_to_directory(self.broker_company_id)
            self.broker_company.add_company_to_directory(self.company_id)
            self.broker_company.add_farm_to_directory(self.id)

    def send_notifications(self, admins, sender, verb, description):
        for recipient in admins:
            notify.send(
                sender=sender,
                recipient=recipient,
                verb=verb,
                action_object=self,
                description=description,
                entity='farm',
                app='farms',
            )

    @property
    def company_logo_url(self):
        return self.company.logo_url

    def cannot_create_employee_reasons(self, user):
        reasons = []
        if not user:
            return [CANNOT_CREATE_EMPLOYEE_FOR_REGISTERED_UNLINKED_FARM_TO_BROKER_REASON]
        if user.company.is_system:
            return reasons

        if self.is_registered:
            role_based_permission = ROLE_BASED_CREATE_UPDATE_PERMS[FARM_EMPLOYEE]
            if user.type_id not in role_based_permission:
                roles = [EMPLOYEE_ROLE_DISPLAY_NAME[role] for role in role_based_permission]
                reasons.append(CANNOT_CREATE_FARM_EMPLOYEE_BASED_ON_ROLE_REASON.format(roles=', '.join(roles)))
            elif not self.is_associated(user):
                if user.company.is_broker:
                    reasons.append(CANNOT_CREATE_EMPLOYEE_FOR_REGISTERED_UNLINKED_FARM_TO_BROKER_REASON)
                else:
                    reasons.append(CANNOT_CREATE_EMPLOYEE_FOR_REGISTERED_UNLINKED_FARM_TO_NON_BROKER_REASON)
        return reasons

    @property
    def standard_shrinkage_set(self):
        return self.shrinkage_set.filter(for_company__isnull=True)

    @property
    def special_shrinkage_set(self):
        return self.shrinkage_set.filter(for_company__isnull=False)

    @classmethod
    def to_storage_transfer_csv_row(cls, outload, inload, tz, headers, country, user):
        date_format = country.get_format('date')
        time_format = country.get_format('time')
        outload_spec_params = outload.commodity_specs_for_csv()
        inload_spec_params = inload.commodity_specs_for_csv()
        display_unit = user.unit or country.display_unit
        tonnage_label = country.get_label('tonnage')
        net_weight = outload.commodity.convert_to(round(get(outload, 'net_weight'), 2),
                                                  outload.entered_unit, display_unit)\
            if outload.truck else round(outload.commodity.convert_to(get(outload, 'net_weight'), None, display_unit), 2)
        columns_mapping = {
            'Identifier': outload.extras.get('identifier'),
            'Date': DateTimeUtil.localize_date(outload.date_time, tz, date_format),
            'Time': DateTimeUtil.localize_time(outload.date_time, tz, time_format),
            'Farm/Site': get(outload.handler, 'name'),
            'Outload Storage': get(outload, 'storage.name'),
            'Inload Storage': get(inload, 'storage.name'),
            'Outload Commmodity': get(outload, 'commodity.display_name'),
            'Outload Grade': get(outload, 'grade.name'),
            'Outload Variety': get(outload, 'variety.name'),
            'Outload Season': get(outload, 'season'),
            'Inload Commmodity': get(inload, 'commodity.display_name'),
            'Inload Grade': get(inload, 'grade.name'),
            'Inload Variety': get(inload, 'variety.name'),
            'Inload Season': get(inload, 'season'),
            'Quantity': net_weight,
            'Processed By': get(outload, 'created_by.first_name') + ' ' + get(outload, 'created_by.last_name'),
            'Comments': get(outload, 'comment'),
            'Outload SP1 Name': get(outload_spec_params, 0),
            'Outload SP1 Value': get(outload_spec_params, 1),
            'Outload SP2 Name': get(outload_spec_params, 2),
            'Outload SP2 Value': get(outload_spec_params, 3),
            'Outload SP3 Name': get(outload_spec_params, 4),
            'Outload SP3 Value': get(outload_spec_params, 5),
            'Outload SP4 Name': get(outload_spec_params, 6),
            'Outload SP4 Value': get(outload_spec_params, 7),
            'Outload SP5 Name': get(outload_spec_params, 8),
            'Outload SP5 Value': get(outload_spec_params, 9),
            'Outload SP6 Name': get(outload_spec_params, 10),
            'Outload SP6 Value': get(outload_spec_params, 11),
            'Inload SP1 Name': get(inload_spec_params, 0),
            'Inload SP1 Value': get(inload_spec_params, 1),
            'Inload SP2 Name': get(inload_spec_params, 2),
            'Inload SP2 Value': get(inload_spec_params, 3),
            'Inload SP3 Name': get(inload_spec_params, 4),
            'Inload SP3 Value': get(inload_spec_params, 5),
            'Inload SP4 Name': get(inload_spec_params, 6),
            'Inload SP4 Value': get(inload_spec_params, 7),
            'Inload SP5 Name': get(inload_spec_params, 8),
            'Inload SP5 Value': get(inload_spec_params, 9),
            'Inload SP6 Name': get(inload_spec_params, 10),
            'Inload SP6 Value': get(inload_spec_params, 11),
            f'{tonnage_label} Unit': display_unit,
        }
        if headers:
            return [columns_mapping.get(column) for column in headers]
        else:
            return list(columns_mapping.values())

    def merge_farm(self, merge_from_farm_id):
        Farm.delete_external_plant_codes_of_purged_farm(merge_from_farm_id)
        Farm.merge_site_gates(merge_from_farm_id, self.id)
        Farm.merge_unkown_fields(merge_from_farm_id, self.id)
        Farm.transfer_archived_storages(merge_from_farm_id, self.id)
        Farm.merge_entities('farms', merge_from_farm_id, self.id)

    @classmethod
    def delete_external_plant_codes_of_purged_farm(cls, farm_id):
        farm = cls.objects.filter(id=farm_id).first()
        farm.externalplantcodefarm_set.all().delete()

    @classmethod
    def merge_site_gates(cls, merge_from_farm_id, merge_to_farm_id):
        site_gates = Storage.objects.filter(farm_id=merge_from_farm_id, is_gate=True)
        merge_to_site_gate = Storage.objects.filter(farm_id=merge_to_farm_id, is_gate=True).first()
        if merge_to_site_gate:
            for site_gate in site_gates:
                merge_to_site_gate.merge_storage(site_gate.id)
                DeletedRecords.objects.get_or_create(entity_type=site_gate.entity, entity_id=site_gate.id)
            site_gates.delete()
        else:
            site_gates.update(**{'farm_id': merge_to_farm_id}, updated_at=timezone.now())

    @classmethod
    def merge_unkown_fields(cls, merge_from_farm_id, merge_to_farm_id):
        from core.farm_fields.models import FarmField
        unknown_fields = FarmField.objects.filter(farm_id=merge_from_farm_id, is_unknown=True)
        merge_to_unknown_field = FarmField.objects.filter(farm_id=merge_to_farm_id, is_unknown=True).first()
        if merge_to_unknown_field:
            for unknown_field in unknown_fields:
                merge_to_unknown_field.merge_farm_field(unknown_field.id)
                DeletedRecords.objects.get_or_create(entity_type=unknown_field.entity, entity_id=unknown_field.id)
            unknown_fields.delete()
        else:
            unknown_fields.update(**{'farm_id': merge_to_farm_id}, updated_at=timezone.now())

    @classmethod
    def transfer_archived_storages(cls, merge_from_farm_id, merge_to_farm_id):
        Storage.archived.filter(farm_id=merge_from_farm_id).update(
            **{'farm_id': merge_to_farm_id}, updated_at=timezone.now())

    def get_spec_avg_report(self, report_data):  # pylint: disable=too-many-locals
        from core.loads.models import Load
        farm_term = 'Farm' if self.company.is_grower else 'Site'
        time_filter = date_time_filter_calculation(self)
        grade_data = defaultdict(list)
        all_loads = self.get_all_loads_at_farm(True).exclude(
            source__in=[Load.TITLE_TRANSFER_SOURCE, Load.SHRINKAGE_LOAD]
        ).exclude(
            source=Load.SYSTEM_LOAD, option_type__in=[OPTION_TYPE_STOCK_SWAP,
                                                      OPTION_TYPE_REGRADE_RESEASON_LOAD,
                                                      OPTION_TYPE_SILO_TO_SILO_TRANSFER,
                                                      OPTION_TYPE_STOCK_EMPTY_LOAD,
                                                      OPTION_TYPE_STORAGE_EMPTY_LOAD,
                                                      OPTION_TYPE_STORAGE_UPDATE_LOAD,
                                                      OPTION_TYPE_STOCK_UPDATE_LOAD]
        ).exclude(status='void').exclude(movement__isnull=False,
                                         movement__freight_pickup__loads_set__farm_id=self.id,
                                         movement__freight_delivery__loads_set__farm_id=self.id,
                                         movement__freight_pickup__loads_set__storage_id__isnull=False)
        all_inloads = all_loads.filter(type=Load.INLOAD, title_transfer_id__isnull=True)
        inloads = all_inloads.filter(time_filter)
        query_set = inloads.values(
            'commodity_id', 'grade_id'
        ).annotate(
            id_count=Count('id')
        ).filter(id_count__gte=1).order_by()
        current_season = self.country.default_season.name
        current_season_inload_total = Load.get_net_weight(all_inloads.filter(season=current_season))

        for query in query_set:
            loads = inloads.filter(
                commodity_id=query.get('commodity_id'),
                grade_id=query.get('grade_id')
            )
            total_revievals = tonnage_sum_from_loads_ignoring_shrinkage(loads)
            loads_for_commodity = inloads.filter(
                commodity_id=query.get('commodity_id')
            )

            all_loads_for_commodity = all_loads.filter(commodity_id=query.get('commodity_id'))
            commodity_weight = tonnage_sum_from_loads_ignoring_shrinkage(loads_for_commodity)
            average_specs = get_average_spec_params_by_loads(loads, total_revievals)

            total_loads_for_commodity = tonnage_sum_from_loads_ignoring_shrinkage(all_loads_for_commodity)

            current_season_receivals = tonnage_sum_from_loads_ignoring_shrinkage(
                all_inloads.filter(commodity_id=query.get('commodity_id'), season=current_season)
            )
            # will need it later
            # remaining_in_current_season = tonnage_sum_from_loads_ignoring_shrinkage(all_loads_for_commodity.filter(season=current_season)) # pylint: disable=line-too-long

            commodity = loads.first().commodity
            commodity_name = get(commodity, 'display_name')
            grade_name = get(loads, '[0].grade_name', get(commodity.ungraded, 'name'))

            prev_len = len(grade_data.get(commodity_name, []))

            grade_data[commodity_name].append({
                'Grade ({}) Received'.format(
                    grade_name
                ): str(total_revievals) + " " + commodity.unit,
                'Average Specs': average_specs
            })

            new_len = len(grade_data.get(commodity_name, []))
            if new_len > prev_len:
                remove(*[report_data], {
                    f"{farm_term}": self.name,
                    "Commodity Received": commodity_name
                })
            report_data.append({
                f'{farm_term}': self.name,
                'Commodity Received': commodity_name,
                'Total Receivals Today': str(commodity_weight) + " " + commodity.unit,
                f'Season ({current_season})': str(current_season_receivals) + " " + commodity.unit,
                # f'Remaining ({current_season})': str(remaining_in_current_season) + " " + commodity.unit,
                f'Available on {farm_term.lower()}': str(total_loads_for_commodity) + " " + commodity.unit,
                'Trucks in': loads_for_commodity.filter(movement_id__isnull=False).count(),
                'grade_data': grade_data[commodity_name],
            })
        return inloads.filter(movement_id__isnull=False).count(), current_season_inload_total

    def remaining_tonnage_for_commodity(self, commodity_id):
        from core.loads.models import Load
        all_loads = self.get_all_loads_at_farm(True).exclude(
            source__in=[Load.TITLE_TRANSFER_SOURCE, Load.SHRINKAGE_LOAD]
        ).exclude(
            source=Load.SYSTEM_LOAD, option_type__in=[OPTION_TYPE_STOCK_SWAP, OPTION_TYPE_REGRADE_RESEASON_LOAD]
        ).exclude(status='void').filter(commodity_id=commodity_id)
        return tonnage_sum_from_loads_ignoring_shrinkage(all_loads)

    def get_outload_report(self, report_data):  # pylint: disable=too-many-locals
        from core.loads.models import Load
        farm_term = 'Farm' if self.company.is_grower else 'Site'
        time_filter = date_time_filter_calculation(self)
        grade_data = defaultdict(list)
        all_loads = self.get_all_loads_at_farm(True).exclude(
            source__in=[Load.TITLE_TRANSFER_SOURCE, Load.SHRINKAGE_LOAD]
        ).exclude(
            source=Load.SYSTEM_LOAD, option_type__in=[OPTION_TYPE_STOCK_SWAP,
                                                      OPTION_TYPE_REGRADE_RESEASON_LOAD,
                                                      OPTION_TYPE_SILO_TO_SILO_TRANSFER,
                                                      OPTION_TYPE_STORAGE_UPDATE_LOAD,
                                                      OPTION_TYPE_STOCK_UPDATE_LOAD,
                                                      OPTION_TYPE_STORAGE_EMPTY_LOAD,
                                                      OPTION_TYPE_STOCK_EMPTY_LOAD]
        ).exclude(status='void').exclude(movement__isnull=False,
                                         movement__freight_pickup__loads_set__farm_id=self.id,
                                         movement__freight_delivery__loads_set__farm_id=self.id,
                                         movement__freight_pickup__loads_set__storage_id__isnull=False)
        all_outloads = all_loads.filter(type=Load.OUTLOAD, title_transfer_id__isnull=True)
        outloads = all_outloads.filter(time_filter)
        query_set = outloads.values(
            'commodity_id', 'grade_id'
        ).annotate(
            id_count=Count('id')
        ).filter(id_count__gte=1).order_by()
        country = self.country
        current_season = country.default_season.name
        previous_season = country.prev_season.name
        current_season_outload_total = Load.get_net_weight(all_outloads.filter(season=current_season))
        previous_season_outload_total = Load.get_net_weight(all_outloads.filter(season=previous_season))
        for query in query_set:
            loads = outloads.filter(
                commodity_id=query.get('commodity_id'),
                grade_id=query.get('grade_id')
            )
            total_outload = Load.get_net_weight(loads)
            loads_for_commodity = outloads.filter(
                commodity_id=query.get('commodity_id')
            )
            all_load_for_commodity = all_loads.filter(
                commodity_id=query.get('commodity_id')
            )
            commodity_weight = Load.get_net_weight(loads_for_commodity)
            remaining_tonnage = tonnage_sum_from_loads_ignoring_shrinkage(all_load_for_commodity)

            current_season_outloads = Load.get_net_weight(
                all_outloads.filter(commodity_id=query.get('commodity_id'), season=current_season)
            )
            # will need it later
            # remaining_in_current_season = tonnage_sum_from_loads_ignoring_shrinkage(all_load_for_commodity.filter(season=current_season)) # pylint: disable=line-too-long

            previous_season_outloads = Load.get_net_weight(
                all_outloads.filter(commodity_id=query.get('commodity_id'), season=previous_season)
            )
            # remaining_in_previous_season = tonnage_sum_from_loads_ignoring_shrinkage(all_load_for_commodity.filter(season=previous_season)) # pylint: disable=line-too-long

            average_specs = get_average_spec_params_by_loads(loads, total_outload)

            commodity = loads.first().commodity
            commodity_name = get(commodity, 'display_name')
            grade_name = get(loads, '[0].grade_name', get(commodity.ungraded, 'name'))

            prev_len = len(grade_data.get(commodity_name, []))

            grade_data[commodity_name].append({
                'Grade ({}) Outloaded'.format(
                    grade_name
                ): str(total_outload) + " " + commodity.unit,
                'Specs': average_specs
            })

            new_len = len(grade_data.get(commodity_name, []))
            if new_len > prev_len:
                remove(*[report_data], {
                    f"{farm_term}": self.name,
                    "Commodity Outloaded": commodity_name
                })
            report_data.append({
                f'{farm_term}': self.name,
                'Commodity Outloaded': commodity_name,
                'Total Today': str(commodity_weight) + " " + commodity.unit,
                f'Season ({current_season})': str(current_season_outloads) + " " + commodity.unit,
                # f'Remaining ({current_season})': str(remaining_in_current_season) + " " + commodity.unit,
                f'Season ({previous_season})': str(previous_season_outloads) + " " + commodity.unit,
                # f'Remaining ({previous_season})': str(remaining_in_previous_season) + " " + commodity.unit,
                f'Remaining on {farm_term.lower()}': str(remaining_tonnage) + " " + commodity.unit,
                'Trucks out': loads_for_commodity.filter(movement_id__isnull=False).count(),
                'grade_data': grade_data[commodity_name],
            })
        return (outloads.filter(movement_id__isnull=False).count(), current_season_outload_total,
                previous_season_outload_total)

    @property
    def additional_mass_limit_codes(self):
        return get(self.company, 'additional_mass_limit_codes')

    def add_external_plan_code(self, company_id, code):
        external_plant_code = (self.externalplantcodefarm_set.filter(company_id=company_id).first() or
                               FarmExternalPlantCode(farm_id=self.id, company_id=company_id))
        external_plant_code.code = code
        external_plant_code.save()

    @property
    def owner_plant_code(self):
        return self.get_external_plant_code_for_company(self.company_id)

    def get_external_plant_code_for_company(self, company_id):
        return get(self.externalplantcodefarm_set.filter(company_id=company_id).first(), 'code')

    @property
    def market_zone_state_code(self):
        return get(self.market_zone, 'state.short_code')

    def update_state(self):
        address = self.address
        if address and address.longitude and address.latitude and get(
                self.country.config, 'invoicing.state_wise_levy'):
            state_name = GoogleMaps.get_state({'longitude': address.longitude, 'latitude': address.latitude})
            if state_name:
                state = State.objects.filter(name=state_name).first()
                if state and state.id != address.state_id:
                    address.state_id = state.id
                    address.save()



class CustomStorageManager(models.Manager):
    @property
    def active_containers(self):
        return self.filter(type=STORAGE_TYPE_CONTAINER, is_active=True)

    @property
    def inactive_containers(self):
        return self.filter(type=STORAGE_TYPE_CONTAINER, is_active=False)


class ActiveStoragesManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):
        return super().get_queryset().exclude(is_active=False)


class InActiveStoragesManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):
        return super().get_queryset().exclude(is_active=True)


class DefaultStoragesManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self): # pylint: disable=useless-super-delegation
        return super().get_queryset()


class Storage(BaseModel):  # pylint: disable=too-many-public-methods
    class Meta:
        db_table = 'storages'
        constraints = [
            models.UniqueConstraint(
                Lower('name'), 'farm',
                condition=models.Q(is_active=True),
                name='storages_name_uniq'
            ),
        ]

    mandatory_props = BaseModel.mandatory_props + [
        'name', 'operator_name', 'site_name', 'operator_id', 'stocks',
        'type_name', 'dominant_grade', 'is_gate', 'storage_type'
    ]

    TYPES = (
        ('silo', 'Silo'),
        ('bag', 'Bag'),
        ('bin', 'Bin'),
        ('shed', 'Shed'),
        ('bunker', 'Bunker'),
        ('field_hay_stack', 'Field Hay Stack'),
        ('silage_pit', 'Silage Pit'),
        ('tank', 'Tank'),
        ('container', 'Container'),
    )

    HOME = 'home'
    SYSTEM = 'system'
    SITE_GATE = 'SITE GATE'
    FARM_GATE = 'FARM GATE'
    SITE_GATE_SIZE = BIG_NUMBER
    SITE_GATE_TYPE = 'silo'

    FILLABLES = (
        'storage_id',
        'farm_id',
        'type',
        'size',
        'name',
        'address',
        'commodity_id',
        'variety_id',
        'grade_id',
        'specs',
        'address_id',
        'private',
        'tonnage',
        'season',
        'ngr_id',
        'add_current_stock',
        'target_price',
        'quantity',
        'barcode_number',
        'is_gate',
        'inload',
        'outload',
        'load_identifier',
        "commodity_id__in",
        "identifier",
        "season__in",
        "variety_id__in",
        "differential",
        "storage_id__in",
        "grade_id__in",
        "ngr_id__in",
        "comment",
        "created_by_id",
        "unit",
        "stocks_before_date_time",
    )

    home_storage_id = models.IntegerField(null=True, blank=True)
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE)
    type = models.CharField(max_length=255, choices=TYPES, db_index=True)
    size = models.FloatField(default=0)
    name = models.CharField(max_length=255)
    address = models.ForeignKey(
        Location,
        related_name='storage',
        on_delete=models.SET_NULL,
        null=True, blank=True,
    )
    barcode_number = models.CharField(null=True, blank=True, max_length=255)
    is_gate = models.BooleanField(default=False, null=True, blank=True,)
    unit = models.CharField(default=MT, choices=EMPLOYEE_UNIT_CHOICES, max_length=100, null=True, blank=True)
    handler_site_set = GenericRelation(
        'contracts.Site', related_query_name='storage',
        object_id_field='location_id', content_type_field='location_type'
    )

    storage_set = CustomStorageManager()
    objects = ActiveStoragesManager()
    archived = InActiveStoragesManager()
    all = DefaultStoragesManager()

    FIXED_STORAGE_TYPES = [_type[0] for _type in TYPES if _type[0] != STORAGE_TYPE_CONTAINER]

    def create_container_movement(self, user, data=None):  # pylint: disable=too-many-locals
        from core.trucks.models import Truck
        from core.freights.models import FreightContract
        from core.loads.models import Load
        from core.ngrs.models import Ngr
        inload = Load.objects.filter(storage=self, type=Load.INLOAD, movement__type_id=PACK_ORDER_TYPE_ID).exclude(
            status__in=NULL_STATUSES).first()
        if inload:
            country = inload.country
            movement = get(inload, 'movement')
            if movement and movement.linked_container_movement:
                return movement.linked_container_movement
            customer = get(movement, 'customer')
            external_reference_number = get(movement, 'external_reference_number')
            now = timezone.now()
            system_company = country.get_system_company()
            unknown_site = Farm.objects.filter(name=UNKNOWN_SITE, company=system_company).first()
            unknown_fleet = Truck.objects.filter(rego=FLEET_REGO, company=system_company).first()
            unknown_ngr = Ngr.objects.filter(ngr_number__icontains='UNKNOWN_', company=system_company).first()
            container_movement_inload_ngr_id = get(unknown_ngr, 'id') or get(inload, 'ngr_id')
            farm = unknown_site
            if farm_id := get(data, 'consignee.handler_id'):
                farm = Farm.objects.filter(id=farm_id).first()
            if get(movement, 'buyer'):
                container_movement_inload_ngr_id = get(movement, 'buyer.ngr_id')
                if not container_movement_inload_ngr_id and get(movement, 'buyer.company_id'):
                    container_movement_inload_ngr_id = movement.buyer.company.get_logical_ngr_id()
            truck_company_id = system_company.id
            if get(data, 'truck_id'):
                truck_company_id = get(Truck.objects.filter(id=get(data, 'truck_id')).first(), 'company_id')
            net_cargo = user.company.customised_pack_and_ship and get(data, 'split_weights.net_cargo', None)
            payload = {
                'created_by': user,
                'updated_by': user,
                'owner': user.company,
                'identifier': generate_identifier('freight_movement'),
                'type_id': CUSTOMER_ONLY_TYPE_ID,
                'status': DELIVERED_STATUS,
                'container_number': self.name,
                'customer': {
                    'company_id': get(customer, 'company_id') or inload.ngr.primary_owner_company_ids[0],
                    'contact_id': get(customer, 'contact_id'),
                    'ngr_id': inload.ngr_id,
                },
                'provider_id': truck_company_id,
                'planned_truck_id': get(data, 'truck_id', unknown_fleet.id),
                'commodity_id': inload.commodity_id,
                'season': inload.season,
                'variety_id': inload.variety_id,
                'planned_grade_id': inload.grade_id,
                'planned_tonnage': inload.net_weight,
                'external_reference_number': external_reference_number,
                'commodity_contract_id': get(movement, 'commodity_contract_id'),
                'freight_pickup': {
                    'consignor': {
                        'handler_id': self.farm_id,
                        'sites': [{
                            'location_type': self.entity,
                            'location_id': self.id,
                        }],
                    },
                    'outload': {
                        'type': 'outload',
                        'date_time': now,
                        'truck_id': get(data, 'truck_id', unknown_fleet.id),
                        'commodity_id': inload.commodity_id,
                        'season': inload.season,
                        'variety_id': inload.variety_id,
                        'grade_id': inload.grade_id,
                        'estimated_net_weight': net_cargo if net_cargo else get(data, 'net_weight', inload.net_weight),
                        'gross_weight': None if net_cargo else get(data, 'gross_weight', None),
                        'tare_weight': None if net_cargo else get(data, 'tare_weight', None),
                        'split_weights': get(data, 'split_weights', None),
                        'ngr_id': inload.ngr_id,
                        'specs': inload.specs,
                        'storage_id': self.id,
                        'created_by': user,
                        'updated_by': user,
                        'device_source': {
                            'web': True
                        }
                    },
                    'date_time': now
                },
                'freight_delivery': {
                    'consignee': {
                        'handler_id': farm.id,
                        'sites': [{
                            'location_type': self.entity,
                            'location_id': self.id,
                        }],
                    },
                    'inload': {
                        'type': 'inload',
                        'date_time': now,
                        'truck_id': get(data, 'truck_id', unknown_fleet.id),
                        'commodity_id': inload.commodity_id,
                        'season': inload.season,
                        'variety_id': inload.variety_id,
                        'grade_id': inload.grade_id,
                        'estimated_net_weight': net_cargo if net_cargo else get(data, 'net_weight', inload.net_weight),
                        'gross_weight': None if net_cargo else get(data, 'gross_weight', None),
                        'tare_weight': None if net_cargo else get(data, 'tare_weight', None),
                        'split_weights': get(data, 'split_weights', None),
                        'ngr_id': container_movement_inload_ngr_id,
                        'storage_id': self.id,
                        'created_by': user,
                        'updated_by': user,
                        'specs': inload.specs,
                        'device_source': {
                            'web': True
                        }
                    },
                    'date_time': now
                }
            }
            container_movement = FreightContract.persist(payload, user)
            container_movement_loads = movement.get_container().load_set.exclude(status='void').exclude(
                movement=movement).filter(type=Load.OUTLOAD)
            if container_movement_loads.count() > 1:
                old_container_movement = container_movement_loads.exclude(movement=container_movement).first().movement
                old_container_movement.loads.filter().delete()
                old_container_movement.delete()
            if empty_container_record := EmptyContainer.objects.filter(
                    truck_id=container_movement.inload.truck_id, is_active=True).order_by('-id').first():
                empty_container_record.is_active = False
                empty_container_record.save()
            if container_movement.persisted:
                self.transfer_to(farm)
                self.updated_at = now
                self.save(update_fields=['updated_at'])
            return container_movement

    @property
    def private(self):
        return False

    @property
    def storage_type(self):
        return self.HOME

    @property
    def company_id(self):
        return self.farm.company_id

    @property
    def operator_id(self):
        return self.company_id

    @property
    def operator_name(self):
        return self.farm.company.name

    @classmethod
    def find_by_name(cls, name, farm_id):  # pylint: disable=arguments-differ
        name = strip_special(name)
        expression = CustomRegexWrapper('name').expression
        return cls.objects.annotate(_name=expression).filter(_name=name, farm_id=farm_id).first()

    @classmethod
    def create_by_name(cls, name, farm_id, storage_type='silo'):
        params = {
            'farm_id': farm_id,
            'type': storage_type,
            'address': AUSTRALIA,
            'size': 100000,
            'name': name.strip()
        }
        storage = cls.create_with_location(params)

        if not storage.persisted:
            storage.delete()

        return storage

    @classmethod
    def create_with_location(   # pylint: disable=arguments-differ
            cls, params, kwargs=None, pre_validate_callback=None, current_user=None):
        params.pop('private', None)
        storage = super().create_with_location(params, kwargs, pre_validate_callback)

        if storage.id and current_user:
            cls.notify_change_users(storage, current_user, 'added')

        return storage

    @classmethod
    def update(cls, storage_id=None, data=None, current_user=None):  # pylint: disable=arguments-differ
        data = data or {}
        address_data = data.pop('address', {})
        data.pop('private', None)
        storage = super().update(storage_id, data)
        if not getattr(storage, 'errors', None) and address_data:
            if 'state' in address_data and isinstance(address_data['state'], str):
                address_data['state'] = State.objects.filter(name=address_data['state']).first()
            Location.update(storage.address_id, address_data)

            if current_user:
                cls.notify_change_users(storage, current_user, 'updated')

        return storage

    @classmethod
    def notify_change_users(cls, storage, current_user, verb):
        sender = current_user
        farm = Farm.objects.filter(id=storage.farm_id).get()
        admins = []
        if current_user.company.is_broker:
            admins = farm.company_and_farm_admins()
        elif farm.broker_company:
            admins = farm.broker_and_farm_admins([sender.id])

        description = storage.storage_msg(sender, farm, verb)
        cls.send_notifications(admins, sender, verb, farm, description)

    @classmethod
    def send_notifications(cls, admins, sender, verb, farm, description):  # pylint: disable=too-many-arguments
        for recipient in admins:
            notify.send(
                sender=sender,
                recipient=recipient,
                verb=verb.capitalize(),
                action_object=farm,
                description=description,
                entity='storage',
                app='farms',
            )

    @classmethod
    def find_or_create_by_name(cls, name, farm_id):
        storage = cls.find_by_name(name, farm_id)
        if not storage:
            storage = cls.create_by_name(name, farm_id)

        return storage

    def transfer_to(self, destination_farm_id):
        self.farm_id = destination_farm_id
        self.save()
        from core.loads.models import Stock
        self.load_set.update(farm_id=destination_farm_id, updated_at=timezone.now())
        Stock.objects.filter(storage_id=self.id).update(farm_id=destination_farm_id, updated_at=timezone.now())

    @property
    def farm_name(self):
        return get(self, 'farm.name')

    @property
    def type_name(self):
        return to_display_attr(self.TYPES, self.type)

    @property
    def storage_display_name(self):
        return "{handler_name} - {storage_name}".format(
            handler_name=get(self, 'farm.display_name'),
            storage_name=self.name
        )

    def is_owner(self, user):
        return get(user, 'company_id') in [
            get(self, 'farm.company_id'), get(self, 'farm.broker_company_id')
        ]

    def storage_display_name_by_owner(self, user):
        if self.is_owner(user):
            return self.name

        return self.storage_display_name

    @classmethod
    def create_gate(cls, farm):
        if farm.storages.filter(is_gate=True).exists() or not farm.is_active:
            return

        storage = cls(
            farm_id=farm.id,
            name=cls.FARM_GATE if farm.company.is_grower else cls.SITE_GATE,
            type=cls.SITE_GATE_TYPE,
            size=cls.SITE_GATE_SIZE,
            address_id=farm.address_id,
            created_by_id=farm.created_by_id,
            updated_by_id=farm.updated_by_id,
            updated_at=farm.updated_at,
            created_at=farm.created_at,
            is_gate=True,
        )
        storage.save()

        return storage

    @property
    def verbose_name(self):
        return "{handler_name} - {storage_name}".format(
            handler_name=get(self, 'farm.display_name'),
            storage_name=self.name
        )

    @staticmethod
    def get_storage_relation():
        return 'homestorage'

    @property
    def contracted_tonnage(self):
        from core.contracts.models import Site
        from core.contracts.constants import STORAGE_SOLD_TONNAGE_STATUS_EXCLUSIONS
        contract_tonnages = list(
            Site.objects.filter(
                location_type_id=BaseModel.content_type_ids_for(['storage'])[0],
                location_id=self.id, handler__role='Consignor',
            ).exclude(
                handler__contract_consignor__status__in=STORAGE_SOLD_TONNAGE_STATUS_EXCLUSIONS
            ).prefetch_related(
                'handler__contract_consignor'
            ).annotate(
                Sum('handler__contract_consignor__tonnage')
            ).values_list(
                'handler__contract_consignor__tonnage', flat=True
            )
        )
        return sum(tonnage or 0 for tonnage in contract_tonnages)

    @property
    def handler(self):
        return self.farm if self.farm_id else None

    @property
    def mobile(self):
        return get(self, 'farm.mobile', '')

    @property
    def has_multiple_stored_commodities(self):
        return len(self.__get_stored_commodity_ids()) > 1

    def __get_stored_commodity_ids(self):
        existing_loads = get(self, 'existing_loads')
        if existing_loads is not None:
            return list({load.commodity_id for load in existing_loads})
        else:
            return list(set(self.load_set.exclude(status='void').filter(
                storagelevel__isnull=False
            ).values_list('commodity_id', flat=True)))

    @cached_property
    def stored_commodity_id(self):
        commodity_ids = self.__get_stored_commodity_ids()
        return commodity_ids[0] if len(commodity_ids) == 1 else None

    @property
    def site_name(self):
        return self.name

    def get_stocks(self, loads=None, include_storage_levels=True, specs=True):
        return self.stock_values(include_storage_levels, loads, False, specs)

    def stock_values(self, include_storage_levels, loads, use_loads_serializer, specs=True):  # pylint: disable=too-many-locals
        stocks = []
        from core.loads.models import StorageLevel
        from core.ngrs.models import Ngr
        if not loads or not loads.exists():
            loads = self.load_set.exclude(status='void').select_related(
                'commodity', 'grade', 'ngr'
            ).order_by('-updated_at')

        if not loads.exists():
            return []

        commodity = loads.first().commodity

        ngr_ids = list(set(loads.values_list('ngr_id', flat=True).order_by('ngr_id')))
        season = get_season_by_loads(loads)
        for ngr_id in ngr_ids:
            combo_values = {
                'commodity_id': commodity.id,
                'ngr_id': ngr_id,
            }
            combo_loads = loads.filter(
                **to_filter_clause(combo_values)
            )
            variety_details = get_variety_details_by_loads(combo_loads)
            grade = get_grade_by_loads_greatest_tonnage(combo_loads)
            current_tonnage = tonnage_sum_from_loads_considering_shrinkage(combo_loads) or 0
            if current_tonnage == 0:
                continue
            ngr_data = None
            if ngr_id:
                ngr = Ngr.objects.filter(id=ngr_id).select_related('company').first()
                if ngr:
                    ngr_data = ngr.to_dict(properties=['company_name'])

            combo_stocks = {
                'storage_id': self.id,
                'commodity': commodity.to_dict(),
                'grade': grade or None,
                'ngr': ngr_data,
                'variety': variety_details['variety'],
                'commodity_id': commodity.id,
                'grade_id': get(grade, 'id'),
                'ngr_id': ngr_id,
                'variety_id': get(variety_details, 'variety.id'),
                'dominant_variety': variety_details['dominant_variety'],
                'is_variety_blended': variety_details['is_blended'],
                'season': season,
                'quantity': get_quantity_by_loads(combo_loads),
                'current_tonnage': current_tonnage,
                'current_throughput_tonnage': current_tonnage,
                'updated_at': loads[0].updated_at if loads else DateTimeUtil.get_datetime_from_epoch(0),
                'remaining_space': max(float(self.size or 0) - float(current_tonnage), 0),
            }
            combo_storage_levels = StorageLevel.objects.filter(load__in=combo_loads)
            if specs:
                combo_stocks['average_specs'] = get_average_spec_params_by_storage_levels(combo_storage_levels)
            if include_storage_levels:
                if use_loads_serializer:
                    from core.loads.serializers import StorageLevelSerializer
                    combo_stocks['storage_levels'] = StorageLevelSerializer(combo_storage_levels, many=True).data
                else:
                    combo_stocks['storage_levels'] = StorageLevel.qs2dict(
                        queryset=combo_storage_levels,
                        many_to_one_relations=StorageLevel.STOCKS_RELATIONS,
                    )

            stocks.append(combo_stocks)

        return stocks

    def get_stocks_data(self, storage_levels=None, include_storage_levels=True):
        return self.stock_values(include_storage_levels, storage_levels, True)

    stocks = property(get_stocks)
    stocks_data = property(get_stocks_data)

    @property
    def dominant_grade(self):
        return get_grade_by_storage_levels_greatest_tonnage(self.storage_levels)

    def storage_msg(self, sender, farm, verb):
        msg = '<b>' + sender.name + '</b> from <b>' + sender.company.name + '</b> has '
        msg += verb + ' a Home Storage <b>' + self.name + '</b> '
        msg += 'to ' if verb == 'added' else 'of '
        msg += 'the farm <b>' + farm.name + '</b>'
        return msg

    @staticmethod
    def current_tonnage(storage_levels, _=None):
        return storage_levels.aggregate(tonnage=Sum('tonnage'))['tonnage']

    def get_inloads(self, params=None):
        from core.loads.models import Load
        return Load.weighed_inloads.exclude(status='void').filter(
            storage_id=self.id, **(params if params else {})
        )

    def get_outloads(self, params=None):
        from core.loads.models import Load
        return Load.weighed_outloads.exclude(status='void').filter(
            storage_id=self.id, **(params if params else {})
        )

    def get_storage_levels(self, params=None):
        from core.loads.models import StorageLevel
        return StorageLevel.objects.exclude(load__status='void').filter(
            load__storage_id=self.id, **(params if params else {})
        )

    storage_levels = property(get_storage_levels)

    def get_storage_levels_derived_from_loads(self, _params): #pylint: disable=too-many-branches
        from core.loads.models import StorageLevel, Load
        _storage_levels = []
        if 'storage_id' not in _params.keys() and 'storage_id__in' not in _params.keys():
            _params.update(storage_id=self.id)

        _date_time_query = Q()
        if 'date' in _params and 'time' in _params:
            _date = _params.pop('date')
            _time = _params.pop('time')
            _date_time_query = Q(date_time__lte=_date + ' ' + _time)
        if 'date_time' in _params:
            _date_time = _params.pop('date_time')
            _date_time_query = Q(date_time__lte=_date_time)

        order_by_params = ['date_time', 'type']
        negative_balance = 0
        for _load in Load.weighed.exclude(status='void').exclude(source=Load.SHRINKAGE_LOAD).filter(
                _date_time_query, **_params).order_by(*order_by_params):
            if _load.type == Load.INLOAD:
                tonnage = _load.net_weight + negative_balance
                if tonnage > 0:
                    _new_storage_level = StorageLevel(
                        load_id=_load.id,
                        tonnage=tonnage,
                        created_by_id=_load.created_by_id,
                        updated_by_id=_load.updated_by_id,
                    )
                    _storage_levels.insert(0, _new_storage_level)
                    negative_balance = 0
                else:
                    negative_balance = tonnage
            elif _load.type == Load.OUTLOAD:
                _remaining_tonnage = _load.net_weight
                while _remaining_tonnage:
                    if _storage_levels:
                        if round(_storage_levels[0].tonnage - _remaining_tonnage, 2) >= 0.01:
                            _storage_levels[0].tonnage = round(_storage_levels[0].tonnage - _remaining_tonnage, 2)
                            _remaining_tonnage = 0
                        else:
                            _remaining_tonnage = round(_remaining_tonnage - _storage_levels[0].tonnage, 2)
                            _storage_levels.pop(0)
                    else:
                        break
                negative_balance = negative_balance - _remaining_tonnage

        return _storage_levels

    def sibling_storage_ids(self):
        return self.farm.storage_set.values('id')

    def recreate_storage_levels_from_loads_inline(self, params):
        from core.loads.models import Load, StorageLevel

        storage_id = get(params, 'storage_id')
        commodity_id = get(params, 'commodity_id')
        if storage_id and commodity_id:
            StorageLevel.remove_from_stock(storage_id, commodity_id)

        should_create = tonnage_sum_from_loads_ignoring_shrinkage(
            Load.objects.filter(**params).exclude(source=Load.SHRINKAGE_LOAD).exclude(status='void')) > 0
        storage_level_params = {'load__' + k: v for k, v in params.items()}
        new_levels = self.get_storage_levels_derived_from_loads(params)

        def _process():
            StorageLevel.objects.filter(**storage_level_params).delete()
            if should_create:
                for level in new_levels:
                    level.full_clean_errors(reraise=True)
                    if not level.errors:
                        level.save_only()
                        level.update_stock()

        try:
            _process()
        except ValidationError:
            return False
        except Exception:  # pylint: disable=broad-except
            time.sleep(5)
            try:
                _process()
            except ValidationError:
                return False
            except Exception as _ex:  # pylint: disable=broad-except
                ERRBIT_LOGGER.log(_ex)
                return False
        return True

    def recreate_storage_levels_from_loads(self, _params):
        if settings.ENV in ['dev', 'ci']:
            return self.recreate_storage_levels_from_loads_inline(_params)

        from core.jobs.models import Job
        for key, value in _params.copy().items():
            if not isinstance(value, (str, int, float)):
                _params[key] = str(value)
        job_params = {**_params, 'storage_id': self.id}

        if not Job.objects.filter(
            type='recreate_storage_levels_from_loads', status='pending', params={'params': job_params}
        ).exists():
            Job.schedule_job_for_task('recreate_storage_levels_from_loads', {'params': job_params})
        return True

    def _create_load(self, load_type, data, reraise_exception=False, current_user=None):
        Load = apps.get_model('loads', 'Load')
        return Load._create_load(
            load_type,
            {**data, 'storage_id': self.id},
            reraise_exception=reraise_exception,
            current_user=current_user
        )

    def create_inload(self, data, reraise_exception=False, current_user=None):
        Load = apps.get_model('loads', 'Load')
        return self._create_load(
            Load.INLOAD,
            data,
            reraise_exception=reraise_exception,
            current_user=current_user
        )

    def create_outload(self, data, reraise_exception=False, current_user=None):
        Load = apps.get_model('loads', 'Load')
        return self._create_load(
            Load.OUTLOAD,
            data,
            reraise_exception=reraise_exception,
            current_user=current_user
        )

    @classmethod
    def regrade_params(cls, filters):
        regrade_id = filters.pop('regrade_id', None)
        reseason = filters.pop('reseason', None)
        revariety_id = filters.pop('revariety_id', None)
        regrade_id = regrade_id[0] if regrade_id else None
        reseason = reseason[0] if reseason else None
        revariety_id = revariety_id[0] if revariety_id else None
        recommodity_id = filters.pop('recommodity_id', None)
        return regrade_id, reseason, revariety_id, recommodity_id

    @classmethod
    def process_filters(cls, filters):  # pylint: disable=too-many-locals
        from core.loads.models import Load
        filters_to_pop = ['created_by_id', 'updated_by_id', 'reseasoned_date', 'reseasoned_time']
        for key in filters_to_pop:
            filters.pop(key, None)

        commodity_ids = compact(filters.get('commodity_id__in', []))
        storage_ids = compact(filters.get('storage_id__in', []))
        ngr_ids = compact(filters.get('ngr_id__in', [])) or [None]
        grade_ids = compact(filters.get('grade_id__in', []))
        seasons = compact(filters.get('season__in', []))
        variety_ids = compact(filters.pop('variety_id__in', []))
        variety_id = variety_ids[0] if variety_ids else None

        farm_id = filters.pop('farm_id', None)
        tonnage = filters.pop('tonnage', 0) or 0
        if isinstance(tonnage, str):
            tonnage = float(tonnage.replace(' ', '').replace(',', ''))
        quantity = filters.pop('quantity', 0) or 0
        if isinstance(quantity, str):
            quantity = float(quantity.replace(' ', '').replace(',', ''))
        specs = filters.pop('specs', None)
        transaction_date_time = filters.pop('reseasoned_date_time', None) or timezone.now()
        identifier = filters.pop('identifier', None)
        comment = filters.pop('comment', None)
        differential = filters.pop('differential', None)
        external_system = filters.pop('external_system', None)
        external_reference = filters.pop('external_reference', None)
        empty_and_add = filters.pop('empty_and_add', True)
        stocks_before_date_time = filters.pop('stocks_before_date_time', None)
        loads = Load.objects.none()
        if storage_ids:
            if empty_and_add:
                filters.pop('grade_id__in', None)
                filters.pop('season__in', None)
            storage_view = True
            filtered_data = dict(filter(lambda x: compact(x[1]), filters.items()))
            filter_params = models.Q(**filtered_data)
            loads = Load.weighed.exclude(status='void').filter(filter_params).filter(
                farm_id=farm_id, storage_id__isnull=False)
            if stocks_before_date_time:
                loads = loads.filter(date_time__lte=stocks_before_date_time)
                transaction_date_time = stocks_before_date_time
        else:
            empty_and_add, storage_view = False, False
            storage_ids = [Storage.objects.filter(farm_id=farm_id, is_gate=True).first().id]

        return (commodity_ids, storage_ids, ngr_ids, grade_ids, seasons, variety_id, farm_id, tonnage, quantity, specs,
                transaction_date_time, identifier, comment, differential, external_system, external_reference,
                empty_and_add, storage_view, loads)

    @classmethod
    def get_current_stock(  # pylint: disable=too-many-locals
            cls, empty_and_add, storage_view, farm_id, storage_id, commodity_id, filter_params,
            stocks_before_date_time=None
    ):
        if empty_and_add:
            return 0, 0
        commodity = Commodity.objects.filter(id=commodity_id).first() if commodity_id else None
        precision = get(commodity, 'precision') or 2
        if storage_view and stocks_before_date_time:
            from core.loads.models import Load
            loads = Load.objects.filter(filter_params).filter(
                storage_id=storage_id, date_time__lte=stocks_before_date_time
            ).exclude(status='void')
            tonnage = tonnage_sum_from_loads_ignoring_shrinkage(loads)
            quantity = get_quantity_by_loads(loads)
            existing_tonnage = round(tonnage, precision)
            existing_quantity = round(quantity, precision)
            return existing_tonnage, existing_quantity
        else:
            from core.stocks.models import Stock
            stocks = Stock.objects.filter(filter_params)
            if storage_view:
                stocks = stocks.filter(storage_id=storage_id)
            else:
                stocks = stocks.filter(farm_id=farm_id, storage__type__in=Storage.FIXED_STORAGE_TYPES)
            stocks = stocks.aggregate(total_tonnage=Sum('tonnage'), total_quantity=Sum('quantity'))
            existing_tonnage = round(stocks['total_tonnage'] or 0, precision)
            existing_quantity = round(stocks['total_quantity'] or 0, precision)
            return existing_tonnage, existing_quantity

    @classmethod
    def update_stocks(cls, filters, empty=False, user_id=AU_ROOT_USER_ID, read_only=False, user=None):  # pylint: disable=too-many-locals, too-many-statements
        regrade_id, reseason, revariety_id, recommodity_id = cls.regrade_params(filters)
        is_regrade = bool(regrade_id or reseason or revariety_id)
        throughput = filters.pop('throughput', False)
        update_stock_params = filters.copy()
        (commodity_ids, storage_ids, ngr_ids, grade_ids, seasons, variety_id, farm_id, tonnage, quantity, specs,
         transaction_date_time, identifier, comment, differential, external_system, external_reference, empty_and_add,
         storage_view, loads) = cls.process_filters(filters)

        identifier_suffix = 0
        result_loads = []
        if identifier:
            from core.loads.models import Load
            identifier_suffix = Load.objects.filter(identifier__icontains=identifier).count()
        option_type = ((OPTION_TYPE_STOCK_EMPTY_LOAD if compact(ngr_ids) else OPTION_TYPE_STORAGE_EMPTY_LOAD) if empty
                       else (OPTION_TYPE_STOCK_UPDATE_LOAD if compact(ngr_ids) else OPTION_TYPE_STORAGE_UPDATE_LOAD))
        from core.ngrs.models import Ngr
        ngr_numbers = Ngr.objects.filter(id__in=ngr_ids).values_list('ngr_number', flat=True)[:STOCK_ALERT_DATA_LIMIT]
        commodities = Commodity.objects.filter(id__in=commodity_ids)[:STOCK_ALERT_DATA_LIMIT] or []
        commodities = [commodity.display_name for commodity in commodities]
        grade_names = Grade.objects.filter(id__in=grade_ids).values_list('name', flat=True)[:STOCK_ALERT_DATA_LIMIT]
        storages = Storage.objects.filter(id__in=storage_ids).values_list('name', flat=True)[:STOCK_ALERT_DATA_LIMIT]
        option_type_to_operation_mapping = {
            OPTION_TYPE_STOCK_EMPTY_LOAD: STOCK_EMPTY_LOAD,
            OPTION_TYPE_STORAGE_EMPTY_LOAD: STORAGE_STOCK_EMPTY_LOAD,
            OPTION_TYPE_STOCK_UPDATE_LOAD: STOCK_UPDATE_LOAD,
            OPTION_TYPE_STORAGE_UPDATE_LOAD: STORAGE_STOCK_UPDATE_LOAD,
        }
        alert_sent= False

        for ngr_id in ngr_ids:
            for storage_id in storage_ids:
                commodity_ids = commodity_ids or list(
                    set(loads.values_list('commodity_id', flat=True).order_by('commodity_id')))
                filtered_data = dict(filter(lambda x: compact(x[1]), filters.items()))
                filter_params = models.Q(**filtered_data)
                commodity_id = get(commodity_ids, '0')
                old_tonnage, _ = cls.get_current_stock(False, storage_view, farm_id, storage_id, commodity_id,
                                                       filter_params)
                if empty_and_add or empty:
                    (identifier_suffix, loads_to_be_created) = cls.empty_stocks(
                        farm_id, storage_id, ngr_id, commodity_ids, grade_ids, seasons, storage_view, loads, identifier,
                        identifier_suffix, transaction_date_time, specs, empty, external_system, external_reference,
                        user_id, option_type, read_only, update_stock_params, comment, throughput)
                    result_loads.extend(loads_to_be_created)
                    if not alert_sent and not is_regrade and empty and (
                            storage := Storage.objects.filter(id=storage_id).first()):
                        Farm.send_stock_update_alert(
                            operation_name=option_type_to_operation_mapping[option_type],
                            farm=get(storage, 'farm'), user=user, updated_at=timezone.now(),
                            stock_ngrs=ngr_numbers, storage=storage, commodities=commodities, grade_names=grade_names,
                            seasons=seasons, load_identifier=identifier, storages=storages
                        )
                        alert_sent = True
                if not empty:
                    commodity_id = commodity_ids[0]
                    grade_id = grade_ids[0] if grade_ids else None
                    season = seasons[0] if seasons else None
                    tonnage = Commodity.convert_to_self_unit(commodity_id, tonnage)

                    is_regrade = bool(regrade_id or reseason or revariety_id)
                    skip_shrinkage = is_regrade

                    filtered_data = dict(filter(lambda x: compact(x[1]), filters.items()))
                    filter_params = models.Q(**filtered_data)
                    existing_tonnage, existing_quantity = cls.get_current_stock(empty_and_add, storage_view, farm_id,
                                                                                storage_id, commodity_id, filter_params)
                    if not alert_sent and not is_regrade and (storage := Storage.objects.filter(id=storage_id).first()):
                        old_tonnage = f"{old_tonnage} {get(user, 'country.display_unit')}"
                        updated_tonnage = f"{tonnage} {get(user, 'country.display_unit')}"
                        Farm.send_stock_update_alert(
                            operation_name=option_type_to_operation_mapping[option_type],
                            farm=get(storage, 'farm'), user=user, updated_at=timezone.now(),
                            changes={"tonnage": (old_tonnage, updated_tonnage)},
                            stock_ngrs=ngr_numbers, storage=storage, commodities=commodities, grade_names=grade_names,
                            seasons=seasons, load_identifier=identifier, storages=storages
                        )
                        alert_sent = True
                    if is_regrade:
                        Storage.update_balance_to(
                            existing_tonnage, float(existing_tonnage) - float(tonnage), commodity_id, grade_id, ngr_id,
                            season, storage_id, user_id, skip_shrinkage, transaction_date_time, identifier, comment,
                            is_regraded=is_regrade, differential=differential, specs=specs,
                            field_identifier=identifier + '_' + str(identifier_suffix),
                            current_quantity=existing_quantity,
                            target_quantity=float(existing_quantity) - float(quantity), external_system=external_system,
                            variety_id=variety_id, external_reference=(
                                external_reference + '_' + str(identifier_suffix) if external_reference else None),
                            throughput=throughput
                        )
                        identifier_suffix += 1
                        existing_tonnage = 0
                        existing_quantity = 0
                        grade_id = regrade_id or grade_id
                        season = reseason or season
                        variety_id = revariety_id or variety_id
                        commodity_id = recommodity_id or commodity_id
                    else:
                        transaction_date_time = transaction_date_time + timedelta(seconds=4)
                    loads_to_be_created = Storage.update_balance_to(
                        existing_tonnage, tonnage, commodity_id, grade_id, ngr_id, season, storage_id, user_id,
                        skip_shrinkage, transaction_date_time, identifier, comment, is_regraded=is_regrade,
                        differential=differential, specs=specs,
                        field_identifier=identifier + '_' + str(identifier_suffix), current_quantity=existing_quantity,
                        target_quantity=quantity, external_system=external_system, variety_id=variety_id,
                        external_reference=(
                            external_reference + '_' + str(identifier_suffix) if external_reference else None),
                        read_only=read_only, option_type=option_type, update_stock_params=update_stock_params,
                        throughput=throughput
                    )
                    identifier_suffix += 1
                    result_loads.extend(loads_to_be_created)

                if storage_view:
                    for commodity_id in commodity_ids:
                        Storage.handle_negative_stocks(
                            {'storage_id__in': [storage_id], 'commodity_id__in': [commodity_id]}, 1, True)
                else:
                    Storage.objects.get(id=storage_id).recreate_storage_levels_from_loads(
                        {'storage_id': storage_id, 'commodity_id': commodity_ids[0]})
        return result_loads

    @classmethod
    def empty_stocks(cls, farm_id, storage_id, ngr_id, commodity_ids, grade_ids, seasons, storage_view, loads,  # pylint: disable=too-many-locals
                     identifier, identifier_suffix, transaction_date_time, specs, empty, external_system,
                     external_reference, user_id, option_type, read_only=False, update_stock_params=None, comment='',
                     throughput=False):
        empty_everything = not(empty and (grade_ids or seasons))
        result_loads = []
        stocks_before_date_time = update_stock_params.get('stocks_before_date_time', None)
        for commodity_id in commodity_ids:
            filter_params = Q(commodity_id=commodity_id, ngr_id=ngr_id) if ngr_id else Q(commodity_id=commodity_id)

            existing_commodity_tonnage, existing_commodity_quantity = cls.get_current_stock(
                not empty_everything, storage_view, farm_id, storage_id, commodity_id, filter_params,
                stocks_before_date_time)

            _grade_ids = (empty and grade_ids) or list(set(loads.filter(
                commodity_id=commodity_id).values_list('grade_id', flat=True).order_by('grade_id'))) or [None]

            for grade_id in _grade_ids:
                grade_filter = Q(grade_id=grade_id) if grade_id else Q(grade_id__isnull=True)
                grade_filter_params = filter_params & grade_filter
                _seasons = (empty and seasons) or list(set(loads.filter(commodity_id=commodity_id).filter(
                    grade_filter).values_list('season', flat=True).order_by('season'))) or [None]
                for season in _seasons:
                    season_filter = Q(season=season) if season else Q(season__isnull=True)
                    season_filter_params = grade_filter_params & season_filter
                    existing_filtered_tonnage, existing_filtered_quantity = cls.get_current_stock(
                        False, storage_view, farm_id, storage_id, commodity_id, season_filter_params,
                        stocks_before_date_time
                    )

                    if existing_filtered_tonnage != 0:
                        loads_to_be_created = Storage.update_balance_to(
                            existing_filtered_tonnage, 0, commodity_id, grade_id, ngr_id, season, storage_id, user_id,
                            False, transaction_date_time, identifier, comment=comment, specs=specs,
                            field_identifier=identifier + '_' + str(identifier_suffix),
                            current_quantity=existing_filtered_quantity, target_quantity=0,
                            external_system=external_system, external_reference=(
                                external_reference + '_' + str(identifier_suffix) if external_reference else None),
                            read_only=read_only, option_type=option_type, update_stock_params=update_stock_params,
                            throughput=throughput
                        )
                        result_loads.extend(loads_to_be_created)
                        identifier_suffix += 1
                        if empty_everything:
                            existing_commodity_tonnage = existing_commodity_tonnage - existing_filtered_tonnage

            if existing_commodity_tonnage != 0:
                loads_to_be_created = Storage.update_balance_to(
                    existing_commodity_tonnage, 0, commodity_id, None, ngr_id, None, storage_id, user_id, False,
                    transaction_date_time, identifier, specs=specs,
                    field_identifier=identifier + '_' + str(identifier_suffix),
                    current_quantity=existing_commodity_quantity, target_quantity=0, external_system=external_system,
                    external_reference=(
                        external_reference + '_' + str(identifier_suffix) if external_reference else None),
                    read_only=read_only, option_type=option_type, update_stock_params=update_stock_params,
                    throughput=throughput
                )
                result_loads.extend(loads_to_be_created)
                identifier_suffix += 1

        return identifier_suffix, result_loads

    @classmethod
    def update_balance_to(
            cls, current, target, commodity_id, grade_id, ngr_id, season, storage_id, user_id, skip_shrinkage=False,
            transaction_date_time=None, identifier='', comment='', is_regraded=False, differential=None, specs=None,
            field_identifier=None, current_quantity=None, target_quantity=None, variety_id=None, external_system=None,
            external_reference=None, read_only=False, option_type=None, update_stock_params=None, throughput=False
    ):  # pylint: disable=too-many-locals, too-many-branches, too-many-statements
        current = float(current)
        target = float(target)
        transaction_date_time = transaction_date_time or timezone.now()
        transaction_date_time = transaction_date_time if isinstance(
            transaction_date_time, datetime) else DateTimeUtil.get_datetime_from_string(transaction_date_time)
        stocks_before_date_time = get(update_stock_params, 'stocks_before_date_time')
        if stocks_before_date_time:
            transaction_date_time = DateTimeUtil.get_datetime_from_string(stocks_before_date_time)
        load = None
        commodity = None
        loads = []
        Load = apps.get_model('loads', 'Load')
        params = {'commodity_id': commodity_id, 'date_time': transaction_date_time,
                  'option_type': option_type,
                  'grade_id': grade_id, 'season': season, 'source': Load.SYSTEM_LOAD,
                  'created_by_id': user_id, 'updated_by_id': user_id,
                  'ngr_id': ngr_id, 'skip_shrinkage': skip_shrinkage, 'storage_id': storage_id, 'throughput': throughput
                  }
        if variety_id:
            params['variety_id'] = variety_id
        if specs:
            params['specs'] = specs
        if comment:
            params['comment'] = comment
        if identifier:
            params['extras'] = {'identifier': identifier}
        if is_regraded:
            params['option_type'] = OPTION_TYPE_REGRADE_RESEASON_LOAD
            params['extras']['is_regraded'] = True
        if differential:
            params['extras']['differential'] = differential
        if field_identifier:
            params['identifier'] = field_identifier
        if external_system and external_reference:
            params.update({'external_system': external_system, 'external_reference': external_reference})
        commodity = Commodity.objects.filter(id=commodity_id).first()
        if read_only:
            params['commodity_name'] = get(commodity, 'display_name')
            params['grade_name'] = get(Grade.objects.filter(id=grade_id).first(), 'name')
            params['storage_name'] = get(Storage.objects.filter(id=storage_id).first(), 'name')
            if ngr_id:
                from core.ngrs.models import Ngr
                params['ngr_number'] = get(Ngr.objects.filter(id=ngr_id).first(), 'ngr_number')
        if current > target:
            params.update({
                'type': Load.OUTLOAD,
                'estimated_net_weight': current - target,
                'quantity': max(current_quantity - target_quantity, 0)
            })
            if read_only:
                params['estimated_net_weight'] = round(params['estimated_net_weight'], 2)
                params['tonnage_display_value'] = f"{params['estimated_net_weight']} {get(commodity, 'price_unit')}"
                params['price_unit'] = get(commodity, 'price_unit')
                loads.append(params)
            else:
                params['estimated_net_weight'] = commodity.convert_to(
                    params['estimated_net_weight'], commodity.price_unit, commodity.country.display_unit
                )
                load = Load.create(params)
        elif target > current:
            params.update({
                'type': Load.INLOAD,
                'estimated_net_weight': target - current,
                'date_time': transaction_date_time - timedelta(seconds=1),
                'quantity': max(target_quantity - current_quantity, 0)
            })
            if read_only:
                params['estimated_net_weight'] = round(params['estimated_net_weight'], 2)
                params['tonnage_display_value'] = f"{params['estimated_net_weight']} {get(commodity, 'price_unit')}"
                params['price_unit'] = get(commodity, 'price_unit')
                loads.append(params)
            else:
                params['estimated_net_weight'] = commodity.convert_to(
                    params['estimated_net_weight'], commodity.price_unit, commodity.country.display_unit
                )
                load = Load.create(params)
        if not read_only and update_stock_params:
            from core.stocks.models import StockMeta
            stock_update_meta = StockMeta.objects.filter(
                identifier__iexact=identifier, status=COMPLETED_STATUS
            ).first() or StockMeta()
            if not get(stock_update_meta, 'id'):
                stock_update_meta.storage_ids = compact(update_stock_params.get('storage_id__in', []))
                stock_update_meta.commodity_ids = compact(update_stock_params.get('commodity_id__in', []))
                stock_update_meta.grade_ids = compact(update_stock_params.get('grade_id__in', []))
                stock_update_meta.variety_ids = compact(update_stock_params.pop('variety_id__in', []))
                stock_update_meta.ngr_ids = compact(update_stock_params.get('ngr_id__in', []))
                stock_update_meta.seasons = compact(update_stock_params.get('season__in', []))
                stock_update_meta.tonnage = float(update_stock_params.pop('tonnage', 0) or 0)
                stock_update_meta.quantity = float(update_stock_params.pop('quantity', 0) or 0)
                stock_update_meta.comment = update_stock_params.pop('comment', None)
                stock_update_meta.farm_id = get(load, 'farm_id')
                stock_update_meta.identifier = identifier
                stock_update_meta.status = COMPLETED_STATUS
                stock_update_meta.stocks_before_date_time = update_stock_params.pop('stocks_before_date_time', None)
                stock_update_meta.save()
        if external_reference and load and load.errors == {'__all__': [EXTERNAL_LOAD_EXISTS_ERROR_MESSAGE]}:
            raise ErrorException(error_dict={'errors': 'Load with this external reference already exists.'})
        if (load and load.pk) or read_only:
            if ngr_id:
                only_for_storage_identifier = None
                if field_identifier:
                    only_for_storage_identifier = field_identifier + '_only_storage'
                if external_reference:
                    external_reference += '_only_storage'
                loads_with_ngr = Storage.update_balance_to(
                    target, current, commodity_id, grade_id, None, season, storage_id, user_id,
                    skip_shrinkage, transaction_date_time, identifier, comment=comment, is_regraded=is_regraded,
                    differential=differential, field_identifier=only_for_storage_identifier,
                    current_quantity=target_quantity, target_quantity=current_quantity, variety_id=variety_id,
                    external_system=external_system,
                    external_reference=external_reference, read_only=read_only, option_type=option_type,
                    update_stock_params=update_stock_params, throughput=throughput
                )
                loads.extend(loads_with_ngr)
        return loads

    @classmethod
    def silo_to_silo_transfer(cls, params, user_id):
        Load = apps.get_model('loads', 'Load')
        outload = params.pop('outload', None)
        inload = params.pop('inload', None)
        identifier = inload.pop('identifier', None)
        comment = inload.pop('comment', None)
        external_args = inload.pop('external_args', None)
        outload.pop('comment', None)
        outload.pop('identifier', None)
        outload.pop('identifier', None)
        inload_commodity_id = get(inload, 'commodity_id')
        should_use_entered_value = get(
            Commodity.objects.filter(id=inload_commodity_id).first(), 'should_use_entered_value'
        )
        if should_use_entered_value:
            outload['_entered_estimated_net_weight'] = get(outload, 'estimated_net_weight')
            outload['estimated_net_weight'] = None
            inload['_entered_estimated_net_weight'] = get(inload, 'estimated_net_weight')
            inload['estimated_net_weight'] = None
        if outload and inload:
            params = {'option_type': OPTION_TYPE_SILO_TO_SILO_TRANSFER, 'source': Load.SYSTEM_LOAD,
                      'created_by_id': user_id, 'updated_by_id': user_id, 'skip_shrinkage': True}
            with transaction.atomic():
                inload.update({
                    **params,
                    'extras': {'identifier': identifier, 'comment': comment},
                    'type': Load.INLOAD,
                    'identifier': identifier + '_0'
                })
                outload.update({
                    **params,
                    'extras': {'identifier': identifier, 'comment': comment},
                    'type': Load.OUTLOAD,
                    'identifier': identifier + '_1'
                })
                if external_args:
                    inload.update({**external_args})
                    outload.update({**external_args})
                created_outload = Load.create(outload)
                created_inload = Load.create(inload)
                if created_outload.errors or created_inload.errors:
                    raise ErrorException(
                        error_dict=[*getattr(created_inload, 'errors', None),
                                    *getattr(created_outload, 'errors', None)])
                return created_outload, created_inload
        return None, None

    @classmethod
    def update_silo_to_silo_transfer(cls, inload_params, outload_params):
        from core.loads.models import Load
        inload = Load.objects.filter(pk=inload_params.pop('id', None)).first()
        outload = Load.objects.filter(pk=outload_params.pop('id', None)).first()
        extras_comment = outload_params.pop('comment', None)
        extras_identifier = outload_params.pop('identifier', None)
        inload_params.pop('comment', None)
        inload_params.pop('identifier', None)
        should_use_entered_value = get(inload, 'commodity.should_use_entered_value')
        if should_use_entered_value:
            inload_params['_entered_estimated_net_weight'] = get(inload_params, 'estimated_net_weight')
            inload_params['estimated_net_weight'] = None
            outload_params['_entered_estimated_net_weight'] = get(outload_params, 'estimated_net_weight')
            outload_params['estimated_net_weight'] = None
        for key, value in inload_params.items():
            if get(inload, key) != value:
                setattr(inload, key, value)
        for key, value in outload_params.items():
            if get(outload, key) != value:
                setattr(outload, key, value)
        inload_extras = inload.extras
        outload_extras = outload.extras
        inload_extras['comment'] = extras_comment
        outload_extras['comment'] = extras_comment
        inload_extras['identifier'] = extras_identifier
        outload_extras['identifier'] = extras_identifier
        inload.save()
        outload.save()
        if inload.errors or outload.errors:
            return {'errors': [*inload.errors, *outload.errors]}

    @classmethod
    def delete_silo_to_silo_transfer(cls, load_ids, user=None):
        from core.loads.models import Load
        loads = Load.objects.filter(id__in=load_ids)
        storage_level_combos = []
        if user and (load := loads.first()):
            if load.is_regraded_load:
                Farm.send_stock_update_alert(operation_name=REGRADED_LOAD, farm=load.farm, user=user,
                                             updated_at=timezone.now(), is_void=True, load=load)
            elif load.is_storage_transfer_load:
                Farm.send_stock_update_alert(operation_name=STORAGE_TRANSFER_LOAD, farm=load.farm, user=user,
                                             updated_at=timezone.now(), is_void=True, load=load)
            elif load.is_stock_swap_load:
                Farm.send_stock_update_alert(operation_name=STOCK_SWAP_LOAD, farm=load.farm, user=user,
                                             updated_at=timezone.now(), is_void=True, load=load)

        for load in loads:
            combo = {'storage_id': load.storage_id, 'commodity_id': load.commodity_id}
            storage_level_combos.append(combo) if combo not in storage_level_combos else None
            load.system_loads.all().update(status='void', updated_at=timezone.now())
        loads.update(status='void', updated_at=timezone.now())
        for load in loads:
            load.update_related_models_on_void()
        for combo in storage_level_combos:
            Storage.objects.get(id=combo['storage_id']).recreate_storage_levels_from_loads(combo)

    @classmethod
    def handle_negative_stocks(cls, filters, user_id, do_not_create_system_loads_if_total_negative=False):  # pylint: disable=too-many-locals,too-many-branches,too-many-nested-blocks
        Load = apps.get_model('loads', 'Load')
        filtered_data = dict(filter(lambda x: compact(x[1]), filters.items()))
        filter_params = models.Q(**filtered_data)
        loads = Load.weighed.exclude(status='void').filter(filter_params)
        loads = loads.exclude(source__in=[Load.SHRINKAGE_LOAD, Load.TITLE_TRANSFER_SOURCE]).exclude(
            source=Load.SYSTEM_LOAD, option_type__in=[OPTION_TYPE_STOCK_SWAP]
        ).filter(storage__type__in=Storage.FIXED_STORAGE_TYPES)
        if not do_not_create_system_loads_if_total_negative or (
                tonnage_sum_from_loads_ignoring_shrinkage(loads) > 0
        ):  # pylint: disable=too-many-nested-blocks
            for _storage_id in list(set(list(loads.values_list('storage_id', flat=True)))):
                s_loads = loads.filter(storage_id=_storage_id)
                for _commodity_id in list(set(list(s_loads.values_list('commodity_id', flat=True)))):
                    c_loads = s_loads.filter(commodity_id=_commodity_id).order_by('date_time', 'type')
                    current_tonnage = 0
                    system_tonnage = 0
                    params = {'source': Load.SYSTEM_LOAD, 'option_type': -1, 'created_by_id': user_id,
                              'updated_by_id': user_id, 'storage_id': _storage_id}
                    for _load in c_loads:
                        params.update({'commodity_id': _commodity_id, 'date_time': _load.date_time})
                        if _load.type == Load.INLOAD:
                            current_tonnage = round(current_tonnage + round(_load.shrunk_tonnage, 2), 2)
                        elif _load.type == Load.OUTLOAD:
                            current_tonnage = round(current_tonnage - round(_load.net_weight, 2), 2)
                        if current_tonnage < 0:
                            params.update({'type': Load.INLOAD, 'estimated_net_weight': abs(current_tonnage)})
                            load = Load.create(params)
                            current_tonnage = round(current_tonnage + load.shrunk_tonnage, 2)
                            system_tonnage = round(system_tonnage + load.shrunk_tonnage, 2)
                        if current_tonnage >= system_tonnage > 0:
                            params.update({'type': Load.OUTLOAD, 'estimated_net_weight': system_tonnage})
                            load = Load.create(params)
                            current_tonnage = round(current_tonnage - load.shrunk_tonnage, 2)
                            system_tonnage = round(system_tonnage - load.shrunk_tonnage, 2)
                    if system_tonnage > 0 and not do_not_create_system_loads_if_total_negative:
                        params.update({'type': Load.OUTLOAD, 'estimated_net_weight': system_tonnage})
                        Load.create(params)
                    storage = Storage.objects.filter(id=_storage_id).first()
                    if storage and c_loads.exists():
                        storage.recreate_storage_levels_from_loads(c_loads.first().storage_level_combo)

    @staticmethod
    def load_msg(sender, farm, load):
        msg = '<b>' + sender.name + '</b> from <b>' + sender.company.name + '</b> has added an '
        msg += load.type + ' (' + get(load, "commodity.display_name") + ' | '
        msg += str(load.net_weight or 0) + ') '
        msg += ' to the farm <b>' + farm.name + '</b>'
        return msg

    def notify_users(self, current_user, load):
        sender = current_user
        if self.farm_id:
            farm = self.farm
            admins = []

            if current_user.company.is_broker:
                admins = farm.company_and_farm_admins()
            elif farm.broker_company:
                admins = farm.broker_and_farm_admins([sender.id])

            description = self.load_msg(sender, farm, load)
            for recipient in admins:
                notify.send(
                    sender=sender,
                    recipient=recipient,
                    verb=load.type,
                    action_object=farm,
                    description=description,
                    entity=load.type,
                    app='farms',
                    load_id=load.id,
                    load_type=load.type,
                    commodity_id=load.commodity_id,
                    season=load.season,
                    ngr_id=load.ngr_id,
                    grade_id=load.grade_id,
                    storage_id=self.id,
                )

    def get_commodity_details(self, unit):  # pylint: disable=too-many-locals
        from core.loads.models import Load, StorageLevel
        loads = Load.objects.filter(storage_id=self.id).exclude(status='void').exclude(
            source__in=[Load.SHRINKAGE_LOAD, Load.TITLE_TRANSFER_SOURCE]
        )
        commodity_ids = compact(list(set(loads.values_list('commodity_id', flat=True))))
        commodities = []
        for commodity_id in commodity_ids:
            commodity = Commodity.objects.filter(id=commodity_id).first()
            commodity_specific_loads = loads.filter(commodity=commodity.id)
            levels = StorageLevel.objects.filter(load__in=commodity_specific_loads)
            from core.stocks.models import Stock
            stock = commodity.convert_to(
                round(Stock.objects.filter(storage_id=self.id, commodity_id=commodity_id).aggregate(
                    total_tonnage=Sum('tonnage')
                )['total_tonnage'] or 0, commodity.precision), commodity.unit, unit, True)
            season = get_season_by_storage_levels(levels)
            grades = list(set(levels.order_by('load__grade_id').values_list('load__grade_id', flat=True)))
            count = len(grades)
            grade = None
            if count == 1:
                grade = get_grade_display_name(commodity_id, None, get(grades, '0'))
            elif count > 1:
                grade = 'Multiple'
            capacity = commodity.convert_to(
                self.size, Country.get_requesting_country().display_unit, unit, True) or 0
            commodities.append({
                'id': get(commodity, 'id'),
                'displayName': get(commodity, 'display_name'),
                'grade': grade,
                'season': season,
                'tonnage': stock,
                'space': round(capacity - float(stock), 2)
            }) if stock and stock > 0 else None

        return commodities

    @property
    def capacity(self):
        return 'N/A' if self.is_gate else self.size

    @property
    def country_unit_conversion_default_commodity_id(self):
        return self.farm.country.config.get('unit_conversion_default_commodity_id', None)

    @property
    def country_unit_conversion_default_commodity(self):
        commodity_id = self.country_unit_conversion_default_commodity_id
        if commodity_id:
            return Commodity.objects.filter(id=commodity_id).first()

    def to_csv_row(self, commodity, grade_display_name, season, tonnage, combo_loads, custom_csv_headers, unit):
        from core.loads.models import StorageLevel, Load
        spec_params = commodity_specs_for_csv(
            get_average_spec_params_by_storage_levels(StorageLevel.objects.filter(load__in=combo_loads)))
        tonnage_label = self.farm.country.get_label('tonnage')

        columns_mapping = {
            'Farm Name': self.farm.name,
            'Storage Name': self.name,
            'Commodity': commodity.display_name if commodity else None,
            'Grade': grade_display_name,
            'Season': season,
            'Stock': tonnage,
            'Capacity': self.get_capacity_in_unit(unit),
            **Load.to_specs_csv_row_with_none(spec_params),
            f'{tonnage_label} Unit': unit
        }

        if custom_csv_headers:
            return [columns_mapping.get(column) for column in custom_csv_headers]

        return list(columns_mapping.values())

    def get_capacity_in_unit(self, unit):
        if self.unit == unit:
            return self.capacity
        if not self.capacity or self.capacity in ['N/A']:
            return self.capacity
        country = self.farm.country
        if country.should_convert_unit:
            conversion_commodity = self.country_unit_conversion_default_commodity
            return conversion_commodity.convert_to(self.capacity, self.unit, unit, True)
        return self.capacity

    def merge_storage(self, merge_from_storage_id):
        from core.contracts.models import Site
        Storage.merge_entities('storages', merge_from_storage_id, self.id)
        Site.objects.filter(
            location_id=merge_from_storage_id, location_type__model='storage'
        ).update(**{'location_id': self.id})

    def clean_fields(self, exclude=None):
        if not self.size:
            self.size = 0
        super().clean_fields(exclude=exclude)

    def update_distance_in_associated_movements_and_orders_if_required(self, old_latitude, old_longitude, user):
        if self.address.latitude != old_latitude or self.address.longitude != old_longitude:
            from core.jobs.models import Job
            Job.schedule_job_for_task(
                'update_distance_in_movements_and_orders',
                params={'entity_id': self.id, 'entity_type': 'storage', 'user_id': user.id}
            )


class EmptyContainer(BaseModel):
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE)
    container_number = models.TextField()
    truck_tare = models.FloatField(default=0)
    truck = models.ForeignKey(
        'trucks.Truck',
        null=True,
        blank=True,
        on_delete=models.SET_NULL
    )


class FarmMessage(BaseModel):
    class Meta:
        db_table = 'farm_messages'

    FILLABLES = [
        'farm_id',
        'farm_ids',
        'message',
        'plain_text',
    ]

    farm = models.ForeignKey(Farm, on_delete=models.CASCADE)
    message = models.TextField()
    plain_text = models.TextField()

    def update_message(self, message_text, plain_text, user):
        if not message_text:
            return
        self.message = message_text
        self.plain_text = plain_text
        self.updated_by = user
        self.save()

    @classmethod
    def persist(cls, message_text, plain_text, farm_ids, user):
        messages = []
        for farm_id in farm_ids:
            message = FarmMessage.objects.filter(farm_id=farm_id).first()
            if message:
                message.update_message(message_text, plain_text, user)
            else:
                messages.append(
                    cls(farm_id=farm_id, message=message_text, plain_text=plain_text, created_by=user, updated_by=user)
                )
        if messages:
            cls.objects.bulk_create(messages)

        from core.jobs.models import Job
        Job.schedule_job_for_task(
            'broadcast_site_manager_message',
            {
                'message_text': message_text,
                'plain_text': plain_text,
                'sender_id': user.id
            }
        )


class ShrinkageMixin(BaseModel):
    class Meta:
        abstract = True

    @classmethod
    def check_for_overlapping_tenures(cls, data):
        if not isinstance(data, list):
            data = [data]
        overlapping_tenure = False
        for shrinkage_row in data:
            start_date = get(shrinkage_row, 'start_date') or timezone.now().date()
            end_date = get(shrinkage_row, 'end_date') or datetime.max.date()
            if issubclass(cls, Shrinkage):
                filters = {
                    "site_id": get(shrinkage_row, 'site_id'),
                    "commodity_id": get(shrinkage_row, 'commodity_id')
                }
                if get(shrinkage_row, 'for_company_id'):
                    filters.update({"for_company_id": get(shrinkage_row, 'for_company_id')})
                else:
                    filters.update({"for_company_id__isnull": True})
            else:
                filters = {
                    "storage_id": get(shrinkage_row, 'storage_id'),
                    "commodity_id": get(shrinkage_row, 'commodity_id')
                }
            queryset = cls.objects.filter(**filters)
            overlapping_tenure = queryset.filter(
                end_date__gte=start_date,
                start_date__lte=end_date
            ).exists()
        return overlapping_tenure


class Shrinkage(ShrinkageMixin):
    class Meta:
        db_table = 'shrinkages'
        constraints = [
            UniqueConstraint(
                fields=['site', 'commodity', 'for_company', 'start_date', 'end_date'], name='special_unique'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'start_date', 'end_date'],
                condition=Q(for_company=None),
                name='standard_unique'
            ),
        ]

    percentage = models.FloatField(default=0, validators=[MaxValueValidator(100), MinValueValidator(0)])
    site = models.ForeignKey(Farm, related_name='shrinkage_set', on_delete=models.CASCADE)
    commodity = models.ForeignKey('commodities.Commodity', related_name='shrinkage_set', on_delete=models.CASCADE)
    for_company = models.ForeignKey(
        'companies.Company', related_name='shrinkage_set', null=True, blank=True, on_delete=models.CASCADE
    )
    start_date = models.DateTimeField(null=False, blank=False, default=timezone.now)
    end_date = models.DateTimeField(null=False, blank=False, default=datetime.max)

    FILLABLES = [
        'percentage',
        'site_id',
        'site_ids',
        'commodity_id',
        'commodity_ids',
        'for_company_id',
        'start_date',
        'end_date',
        'dates_updated',
    ]

    @classmethod
    def special_set_for_user(cls, user, filters=None):
        queryset = cls.list(is_special=True, filters=filters)
        return queryset.filter(
            models.Q(for_company_id=user.company_id) | models.Q(site__company_id=user.company_id)
        ) if not user.is_staff else queryset

    @classmethod
    def special_set(cls, filters=None):
        return cls.list(is_special=True, filters=filters)

    @classmethod
    def standard_set(cls, filters=None):
        return cls.list(is_special=False, filters=filters)

    @classmethod
    def all_for_user(cls, user, filters):
        return cls.standard_set(filters) | cls.special_set_for_user(user, filters)

    @classmethod
    def list(cls, is_special=False, filters=None):
        if not filters:
            filters = {}

        return cls.objects.filter(**filters).filter(for_company__isnull=not is_special)

    def no_loads_exist(self):
        from core.loads.models import Load
        queryset = Load.objects.exclude(status='void').exclude(storage_id__isnull=True).filter(
            commodity_id=self.commodity_id, farm_id=self.site_id)
        return queryset.exists()

    def can_edit(self):
        start_date = self.start_date.date() if isinstance(self.start_date, datetime) else datetime.strptime(
            self.start_date, '%Y-%m-%d').date()
        return timezone.now().date() < start_date or not self.no_loads_exist()

    @classmethod
    def bulk_create(cls, data):
        if not isinstance(data, list):
            data = [data]

        shrinkages = [Shrinkage(**d) for d in data]

        return cls.objects.bulk_create(shrinkages)

    @classmethod
    def get_shrinkage_for(cls, site_id, commodity_id, for_company_id, **kwargs):
        queryset = cls.objects.filter(site=site_id, commodity_id=commodity_id)
        if kwargs:
            queryset = queryset.filter(**kwargs)
        if for_company_id:
            if queryset.filter(for_company_id=for_company_id).exists():
                queryset = queryset.filter(for_company_id=for_company_id)
            else:
                queryset = queryset.filter(for_company_id__isnull=True)
        return queryset.first()


class WarehouseFees(BaseModel):
    class Meta:
        db_table = 'warehouse_fees'
        constraints = [
            UniqueConstraint(
                fields=['site', 'commodity', 'type',
                        'frequency', 'for_company', 'season', 'start_date', 'end_date'],
                condition=Q(for_group__isnull=True, season__isnull=False, grade__isnull=True),
                name='company_exception_with_season_without_grade'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'type',
                        'frequency', 'for_company', 'season', 'start_date', 'end_date', 'grade'],
                condition=Q(for_group__isnull=True, season__isnull=False, grade__isnull=False),
                name='company_exception_with_season_with_grade'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'type',
                        'frequency', 'for_company', 'start_date', 'end_date'],
                condition=Q(for_group__isnull=True, season__isnull=True, grade__isnull=True),
                name='company_exception'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'type',
                        'frequency', 'for_company', 'start_date', 'end_date', 'grade'],
                condition=Q(for_group__isnull=True, season__isnull=True, grade__isnull=False),
                name='company_exception_with_grade'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'type',
                        'frequency', 'for_group', 'season', 'start_date', 'end_date'],
                condition=Q(for_company__isnull=True, season__isnull=False, grade__isnull=True),
                name='group_exception_with_season_without_grade'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'type',
                        'frequency', 'for_group', 'season', 'start_date', 'end_date'],
                condition=Q(for_company__isnull=True, season__isnull=False, grade__isnull=False),
                name='group_exception_with_season_with_grade'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'type',
                        'frequency', 'for_group', 'start_date', 'end_date'],
                condition=Q(for_company__isnull=True, season__isnull=True, grade__isnull=True),
                name='group_exception_fee'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'type',
                        'frequency', 'for_group', 'start_date', 'end_date', 'grade'],
                condition=Q(for_company__isnull=True, season__isnull=True, grade__isnull=False),
                name='group_exception_fee_with_grade'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'type', 'frequency', 'season', 'start_date', 'end_date'],
                condition=Q(for_company__isnull=True, for_group__isnull=True, season__isnull=False, grade__isnull=True),
                name='standard_with_season'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'type', 'frequency', 'season', 'start_date', 'end_date', 'grade'],
                condition=Q(
                    for_company__isnull=True, for_group__isnull=True, season__isnull=False, grade__isnull=False
                ),
                name='standard_with_season_with_grade'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'type', 'frequency', 'start_date', 'end_date'],
                condition=Q(for_company__isnull=True, for_group__isnull=True, season__isnull=True, grade__isnull=True),
                name='standard'
            ),
            UniqueConstraint(
                fields=['site', 'commodity', 'type', 'frequency', 'start_date', 'end_date', 'grade'],
                condition=Q(for_company__isnull=True, for_group__isnull=True, season__isnull=True, grade__isnull=False),
                name='standard_with_grade'
            ),
        ]

    per_unit = models.FloatField(default=0, validators=[MaxValueValidator(100), MinValueValidator(0)])
    site = models.ForeignKey(Farm, related_name='warehouse_fees_set', on_delete=models.CASCADE)
    commodity = models.ForeignKey('commodities.Commodity', related_name='warehouse_fees_set', on_delete=models.CASCADE)
    type = models.CharField(max_length=255)
    frequency = models.CharField(max_length=255)
    season = models.CharField(max_length=5, null=True, blank=True, validators=[season_validator])
    grace_period = models.IntegerField(choices=[(1, 1), (2, 2)], null=True, blank=True)
    for_company = models.ForeignKey(
        'companies.Company', related_name='warehouse_fees_set', null=True, blank=True, on_delete=models.CASCADE
    )
    for_group = models.ForeignKey(
        'companies.CompanyGroup', related_name='warehouse_fees_set', null=True, blank=True, on_delete=models.CASCADE
    )
    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(default=datetime.max)
    grade = models.ForeignKey('commodities.Grade', on_delete=models.SET_NULL, null=True, blank=True)

    FILLABLES = [
        'site_id',
        'site_ids',
        'commodity_id',
        'commodity_ids',
        'for_company_id',
        'frequency',
        'type',
        'per_unit',
        'season',
        'grace_period',
        'for_group_id',
        'start_date',
        'end_date',
        'grade_id',
        'fees_ids',
    ]

    @classmethod
    def exception_set_for_user(cls, user, filters=None):
        from core.companies.models import CompanyGroup
        queryset = cls.list(is_exception=True, filters=filters)
        group_ids = CompanyGroup.objects.filter(owner_company_id=user.company_id).values_list('id', flat=True)
        return queryset.filter(models.Q(for_company_id=user.company_id) | models.Q(site__company_id=user.company_id) |
                               models.Q(for_group_id__in=group_ids)) if not user.is_staff else queryset

    @property
    def is_monthly(self):
        return self.frequency.lower() == WAREHOUSE_INVOICE_MONTHLY_FREQUENCY

    @property
    def group_url(self):
        if self.for_group_id:
            return '#/companies/groups?group_id={id}'.format(id=self.for_group_id)

    @classmethod
    def exception_set(cls, filters=None):
        return cls.list(is_exception=True, filters=filters)

    @classmethod
    def standard_set(cls, filters=None):
        return cls.list(is_exception=False, filters=filters)

    @classmethod
    def all_for_user(cls, user, filters):
        return cls.standard_set(filters) | cls.exception_set_for_user(user, filters)

    @classmethod
    def get_fee_for(cls, site, commodity_id, warehouse_type, party_company_id, **kwargs): # pylint: disable=too-many-locals
        if not site:
            return None
        def fee_for_season_grade(_queryset):
            _fee = None
            if season and grade_id:
                _fee = _queryset.filter(season=season, grade_id=grade_id).first()
            if not _fee and season:
                _fee = _queryset.filter(season=season, grade_id__isnull=True).first()
            if not _fee and grade_id:
                _fee = _queryset.filter(season__isnull=True, grade_id=grade_id).first()
            if not _fee:
                _fee = _queryset.filter(season__isnull=True, grade_id__isnull=True).first()
            return _fee

        queryset = cls.objects.filter(site_id=site.id, commodity_id=commodity_id, type=warehouse_type)
        season = kwargs.pop('season', None) if kwargs else None
        grade_id = kwargs.pop('grade_id', None) if kwargs else None
        tenure = kwargs.pop("tenure", None) if kwargs else None
        fee = None
        if kwargs:
            queryset = queryset.filter(**kwargs)
        if tenure:
            tenure = tenure.split(' - ')
            start_date = tenure[0].replace('/', '-')
            end_date = tenure[1].replace('/', '-')
            start_date = datetime.strptime(start_date, '%d-%m-%Y')
            end_date = datetime.strptime(end_date, '%d-%m-%Y')
            queryset = queryset.filter(start_date__lte=start_date, end_date__gte=end_date)
        standard_fees_queryset = queryset.filter(for_company_id__isnull=True, for_group_id__isnull=True)
        if party_company_id:
            company_criteria = Q(for_company_id=party_company_id, for_group_id__isnull=True)
            party_company = Company.objects.filter(id=party_company_id).first()
            group = party_company.groups.filter(
                type=FEES, owner_company_id=site.company_id).first() if party_company else None
            if group:
                company_criteria = company_criteria | Q(for_group_id=group.id, for_company_id__isnull=True)
            exception_fees_queryset = queryset.filter(company_criteria)
            fee = fee_for_season_grade(exception_fees_queryset)
        if not fee:
            fee = fee_for_season_grade(standard_fees_queryset)

        return fee

    @classmethod
    def list(cls, is_exception=False, filters=None):
        if not filters:
            filters = {}
        query = cls.objects.filter(**filters)
        if is_exception is True:
            query = query.filter(models.Q(for_company__isnull=not is_exception)
                                 | models.Q(for_group__isnull=not is_exception))
        else:
            query = query.filter(for_company__isnull=not is_exception, for_group__isnull=not is_exception)
        return query

    @classmethod
    def check_redundant_fees(cls, data, user=None):
        if not isinstance(data, list):
            data = [data]
        fees = [WarehouseFees(**d) for d in data]
        redundant_fee_ids = []
        for fee in fees:
            queryset = cls.objects.filter(
                site=fee.site_id, commodity_id=fee.commodity_id, type=fee.type)
            if fee.season:
                queryset = queryset.filter(season=fee.season)
            else:
                queryset = queryset.filter(season__isnull=True)
            if fee.for_company:
                group = fee.for_company.groups.filter(owner_company_id=user.company_id).first()
                if group:
                    queryset = queryset.filter(for_group=group)
                    if queryset.exists():
                        redundant_fee_ids.append(queryset.first().id)
        return redundant_fee_ids


    @classmethod
    def bulk_create(cls, data):
        if not isinstance(data, list):
            data = [data]
        fees = [WarehouseFees(**d) for d in data]
        created_fees = cls.objects.bulk_create(fees)
        for fee in fees:
            queryset = cls.objects.filter(
                site=fee.site_id, commodity_id=fee.commodity_id, type=fee.type)
            if fee.season:
                queryset = queryset.filter(season=fee.season)
            if fee.for_group:
                company_ids = fee.for_group.get_company_ids_belonging_to_group()
                if company_ids:
                    queryset = queryset.filter(for_company_id__in=company_ids)
                    queryset.delete()
        return created_fees

    @classmethod
    def check_for_overlapping_tenures(cls, data):
        if not isinstance(data, list):
            data = [data]
        overlapping_tenure = False
        for fees in data:
            queryset = cls.objects.filter(
                site_id=get(fees, 'site_id'), commodity_id=get(fees, 'commodity_id'), type=get(fees, 'type')
            )
            if get(fees, 'season'):
                queryset = queryset.filter(season=get(fees, 'season'))
            else:
                queryset = queryset.filter(season__isnull=True)
            if get(fees, 'grade_id'):
                queryset = queryset.filter(grade_id=get(fees, 'grade_id'))
            else:
                queryset = queryset.filter(grade__isnull=True)
            if get(fees, 'for_company_id'):
                queryset = queryset.filter(for_company_id=get(fees, 'for_company_id'))
            else:
                queryset = queryset.filter(for_company_id__isnull=True)
            if get(fees, 'for_group_id'):
                queryset = queryset.filter(for_group_id=get(fees, 'for_group_id'))
            else:
                queryset = queryset.filter(for_group_id__isnull=True)
            if queryset.exists():
                warehouse_fees = queryset.filter(
                    end_date__gt=get(fees, 'start_date'), start_date__lt=get(fees, 'end_date')
                )
                if warehouse_fees.exists():
                    overlapping_tenure = True
        return overlapping_tenure

    @classmethod
    def get_group_display_value(cls, queryset, attr=None):
        value = ''
        count = 0

        def get_val(_item):
            return get(_item, attr) if attr else _item

        for item in queryset:
            count += 1
            if count == 1:
                value = get_val(item)
            elif count == 2:
                value += f", {get_val(item)}"
            else:
                value += ' ...'
                break
        return value or None

    @classmethod
    def get_group_display_value_for_customers_or_company_groups(cls, group_customers, group_company_groups):
        customer_names = list(group_customers.values_list('business_name', flat=True))
        company_group_names = list(group_company_groups.values_list('name', flat=True))

        all_names = [*customer_names, *company_group_names]
        return ', '.join(all_names[:2]) + ' ...' if len(all_names) > 2 else ', '.join(all_names)

    @classmethod
    def get_fees_groups(cls, queryset):  # pylint: disable=too-many-locals
        data = []
        queryset = queryset.annotate(
            price_unit=Case(
                When(commodity__unit__in=[BALES, MODULES], then=Value(MT)),
                default=F('commodity__unit'),
                output_field=CharField()
            )
        )
        from core.farms.serializers import WarehouseFeesSerializer
        from core.companies.models import CompanyGroup
        for combo in get_unique_list_of_objects(queryset.values('type', 'per_unit', 'price_unit')):
            group_items_queryset = queryset.filter(**combo)
            group_dict = {**combo, 'items': WarehouseFeesSerializer(group_items_queryset, many=True).data}

            group_commodity_ids = set(group_items_queryset.values_list('commodity_id', flat=True))
            group_seasons = set(group_items_queryset.filter(season__isnull=False).values_list('season', flat=True))
            group_grade_ids = set(group_items_queryset.values_list('grade_id', flat=True))
            group_site_ids = set(group_items_queryset.values_list('site_id', flat=True))
            group_customer_ids = set(group_items_queryset.values_list('for_company_id', flat=True))
            group_company_group_ids = set(group_items_queryset.values_list('for_group_id', flat=True))

            group_commodities = Commodity.objects.filter(id__in=compact(group_commodity_ids))
            group_grades = Grade.objects.filter(id__in=compact(group_grade_ids))
            group_sites = Farm.objects.filter(id__in=compact(group_site_ids))
            group_customers = Company.objects.filter(id__in=compact(group_customer_ids))
            group_company_groups = CompanyGroup.objects.filter(id__in=compact(group_company_group_ids))

            group_dict.update({
                'commodities': cls.get_group_display_value(group_commodities, 'display_name'),
                'grades': cls.get_group_display_value(group_grades, 'name'),
                'sites': cls.get_group_display_value(group_sites, 'name'),
                'seasons': cls.get_group_display_value(group_seasons),
                'customer_group_name': cls.get_group_display_value_for_customers_or_company_groups(
                    group_customers, group_company_groups
                ),
                'group_commodities': ", ".join([commodity.display_name for commodity in group_commodities]),
                'group_grades': ", ".join(list(group_grades.values_list('name', flat=True))),
                'group_seasons': ", ".join(list(compact(group_seasons))),
                'group_sites': ", ".join([site.display_name for site in group_sites])
            })

            data.append(group_dict)
        return data

class StocksUpdate:
    FIELDNAMES = ['Stock Owner', 'NGR', 'Warehouse Company', 'Warehouse Site', 'Commodity', 'Grade',
                  'Season', 'Tonnage', 'Status', 'Failure Reason']

    def __init__(self, csv_file, user):
        from core.company_sites.models import SiteManagementSettings

        self.csv_file = csv_file
        self.reader = None
        self.stock_tonnage_update = []
        self.user = user
        self.buff2 = None
        self.custom_headers = None
        self.custom_field_names = []
        site_mgt = SiteManagementSettings.objects.filter(company=self.user.company,
                                                         update_stock_csv_headers_info__has_key='custom_headers')
        if site_mgt:
            company_settings = site_mgt[0]
            self.custom_headers = get(company_settings.update_stock_csv_headers_info, 'custom_headers')
            self.custom_field_names = get(company_settings.update_stock_csv_headers_info, 'headers')

    def read(self):
        if self.csv_file:
            uploaded_file = requests.get(self.csv_file, timeout=30)
            self.reader = csv.DictReader(io.StringIO(uploaded_file.text))

    def update_stocks(self):
        with transaction.atomic():
            for row in self.reader:
                row = {k.lower(): v for k, v in row.items()}
                stocks_row = StocksRowUpdate(dict(row).copy(), self.user, self.custom_headers)
                try:
                    stocks_row.populate()
                except:  # pylint: disable=bare-except
                    pass
                finally:
                    self.stock_tonnage_update.append(stocks_row)

    def report(self):
        buff = io.StringIO()
        writer = csv.writer(buff, dialect='excel', delimiter=',')
        if self.custom_field_names:
            writer.writerow(self.custom_field_names)
        else:
            writer.writerow(self.FIELDNAMES)
        for item in self.stock_tonnage_update:
            writer.writerow(item.to_csv_row())
        self.buff2 = io.BytesIO(buff.getvalue().encode())

    def sync(self):
        self.read()
        self.update_stocks()
        self.report()


# pylint: disable=too-many-instance-attributes
class StocksRowUpdate:
    def __init__(self, params, user, custom_headers):
        self.user = user
        self.initial_params = copy.deepcopy(params)
        self.params = params
        self.warehouse_site = None
        self.owner = None
        self.grade = None
        self.season = None
        self.commodity = None
        self.ngr = None
        self.warehouse_company = None
        self.tonnage = None
        self.failure_reasons = []
        self.custom_headers = custom_headers

    def is_any_field_empty(self):
        for key, value in self.params.items():
            if not value and key != 'plant name':  # pylint: disable=no-else-break
                self.failure_reasons.append(
                    '{} cannot be blank'.format(
                        key.capitalize()) if key not in ['ba', 'bha'] else \
                            '{} cannot be blank'.format(key.upper()))
                break
            elif value:
                continue

    def populate(self):
        self.is_any_field_empty()
        self.get()
        self.update()

    def get(self):
        self.get_owner()
        self.get_ngr()
        self.get_warehouse_company()
        self.get_warehouse_site()
        self.get_commodity()
        self.get_batch()
        self.get_grade()
        self.get_season()
        self.get_tonnage()
        self.check_is_user_site_or_owner()
        self.check_failure()

    def check_failure(self):
        if self.failure_reasons:  # pylint: disable=raise-missing-from
            raise Exception('failure reason populated!')

    def get_owner(self):
        if 'stock owner' in self.params.keys():
            owner = self.params['stock owner'].strip()
            self.owner = Company.objects.filter(
                business_name__iexact=owner).first()
            if not self.owner:
                self.failure_reasons.append(
                    'Owner not found in system')

    def get_details_from_custom_header(self, param_name):
        if self.custom_headers and param_name in self.custom_headers.keys():
            return self.custom_headers[param_name].get('name'), self.custom_headers[param_name].get('value')
        return param_name, None

    def get_ngr(self):
        from core.ngrs.models import Ngr
        param_name = 'ngr'
        param_name, value_mapping = self.get_details_from_custom_header(param_name)
        ngr = self.params.get(param_name)
        if ngr:
            ngr = ngr.strip().lower()
            if value_mapping and value_mapping.get(ngr):
                self.ngr = Ngr.objects.filter(id=value_mapping[ngr]).first()
            if not self.ngr:
                if ngr:
                    ngrs = Ngr.objects.filter(
                        ngr_number__iexact=ngr)
                    if len(ngrs) > 1:
                        ngr = ngrs.filter(company_id=get(self, 'owner.id')).first() if get(self, 'owner.id') else None
                    else:
                        ngr = ngrs.first()
                elif get(self, 'owner.id'):
                    ngr = Ngr.objects.filter(
                        ngr_number__icontains='unknown', company_id=get(self, 'owner.id')).first()
                self.ngr = ngr
        if not self.ngr:
            self.failure_reasons.append(
                'Unique NGR not found in system')
        elif get(self, 'owner.id') and get(self, 'owner.id') not in self.ngr.owner_company_ids:
            self.failure_reasons.append(
                'NGR not found in system for this stock owner')

    def get_warehouse_company(self):
        param_name = 'warehouse company'
        param_name, value_mapping = self.get_details_from_custom_header(param_name)

        warehouse_company = self.params.get(param_name)
        if warehouse_company:
            warehouse_company = warehouse_company.strip().lower()
            if value_mapping and value_mapping.get(warehouse_company):
                self.warehouse_company = Company.objects.filter(id=value_mapping[warehouse_company]).first()
            elif not self.warehouse_company:
                self.warehouse_company = Company.objects.filter(
                    business_name__iexact=warehouse_company).first()
        if not self.warehouse_company:
            self.failure_reasons.append(
                'Warehouse Company not found in system')

    def get_warehouse_site(self):
        param_name = 'warehouse site'
        param_name, value_mapping = self.get_details_from_custom_header(param_name)
        warehouse_site = self.params.get(param_name)
        if warehouse_site:
            warehouse_site = warehouse_site.strip().lower()
            if value_mapping and value_mapping.get(warehouse_site):
                queryset = Farm.objects.filter(id=value_mapping[warehouse_site])
            else:
                queryset = Farm.objects.filter(name__iexact=warehouse_site)
            if len(queryset) > 1:
                self.warehouse_site = queryset.filter(
                    company_id=get(self.warehouse_company, 'id')).first() if self.warehouse_company else None
            else:
                self.warehouse_site = queryset.first()

            if not self.warehouse_site:
                self.failure_reasons.append('Warehouse Site not found in system')
            elif self.warehouse_company and self.warehouse_site.company_id != get(self.warehouse_company, 'id'):
                self.failure_reasons.append("Warehouse Site doesn't match with warehouse company")

    def get_commodity(self):
        param_name = 'commodity'
        param_name, value_mapping = self.get_details_from_custom_header(param_name)

        commodity = self.params.get(param_name)
        if commodity:
            commodity = commodity.strip().lower()
            if value_mapping:
                commodity = re.findall(r'\d+', commodity)[0]
                if value_mapping.get(commodity):
                    self.commodity = Commodity.objects.filter(id=value_mapping[commodity]).first()
            if not self.commodity:
                commodity = [item for item in Commodity.COMMODITIES if commodity in item]
                if commodity:
                    self.commodity = Commodity.objects.filter(name__iexact=commodity[0][0]).first()
        if not self.commodity:
            self.failure_reasons.append(
                'Commodity not found in system')

    def get_grade(self):
        if 'grade' in self.params.keys():
            grade_name = self.params.get('grade')
            if grade_name:
                grade_name = grade_name.strip().lower()
                if 'grade' in self.custom_headers.keys() and grade_name in self.custom_headers['grade']['value'].keys():
                    grade_name = self.custom_headers['grade']['value'][grade_name]
                grades = Grade.objects.filter(name__iexact=grade_name)
                if len(grades) > 1:
                    self.grade = grades.filter(
                        commodity_id=get(self.commodity, 'id')).first() if self.commodity else None
                else:
                    self.grade = grades.first()
            if not self.grade:
                self.failure_reasons.append(
                    'Grade not found in system')
            elif self.commodity and self.grade.commodity_id != self.commodity.id:
                self.failure_reasons.append(
                    'Grade does not match commodity')

    def get_season(self):
        if 'season' in self.params.keys():
            try:
                season = self.params['season'].strip()
                if season and len(season) == 4:
                    return season[:2] + '/' + season[2:]
                if season and season.find('/') and len(season) > 4:
                    split_result = season.split('/')
                    season = split_result[0][-2:] + \
                        '/' + split_result[1][-2:]
                self.season = season_validator(season)
            except ValidationError:
                self.season = None
                self.failure_reasons.append('Season not found in system')

    def get_batch(self):
        param_name = 'batch'
        if param_name in self.params.keys():
            batch = self.params.get(param_name)
            if batch:
                self.params['grade'] = batch[2:]
                season = batch[0:2]
                if self.custom_headers and \
                    'season' in self.custom_headers.keys() and season in self.custom_headers['season']['value'].keys():
                    self.params['season'] = self.custom_headers['season']['value'][season]
                else:
                    self.params['season'] = ''

    def get_tonnage(self):
        param_name = 'tonnage'
        param_name, _ = self.get_details_from_custom_header(param_name)
        tonnage = self.params.get(param_name)
        if tonnage:
            tonnage = tonnage.strip().lower()
            if re.match(r'^-?\d+(\.+?)?\d+$', tonnage) or re.match(r'^\d+$', tonnage):
                self.tonnage = tonnage
        if not self.tonnage:
            self.failure_reasons.append('Tonnage is not valid.')

    def check_is_user_site_or_owner(self):
        if get(self.ngr, 'company_id') and get(self.warehouse_site, 'company_id'):
            _companies = [get(self.warehouse_site, 'company_id')]
            if not get(self.warehouse_site, 'stocks_management'):
                _companies = _companies + [get(self.ngr, 'company_id')] + get(self.ngr, 'owner_company_ids')
            if get(self.user, 'company.id') not in _companies:
                self.failure_reasons.append('You are not allowed to add this stock. Please contact Site Manager')

    def update(self):
        try:
            filters = {
                'farm_id': get(self.warehouse_site, 'id'),
                'ngr_id__in': [get(self.ngr, 'id')],
                'tonnage': get(self, 'tonnage'),
                'commodity_id__in': [get(self, 'commodity.id')],
                'season__in': [get(self, 'season')],
                'grade_id__in': [get(self, 'grade.id')],
            }
            empty = get(self, 'tonnage') and get(self, 'tonnage') == 0
            Storage.update_stocks(filters, empty=empty, user_id=self.user.id)
        except Exception as ex:
            self.failure_reasons.append('Error while updating')
            raise Exception('failure reason populated!') from ex

    def to_csv_row(self):
        row_values = [val for key, val in self.initial_params.items()]
        row_values.append('Success' if len(self.failure_reasons) == 0 else 'Failure')
        row_values.append('\n'.join(get(self, 'failure_reasons', '')))
        return row_values


class FarmExternalPlantCode(BaseModel):
    class Meta:
        unique_together = ('farm', 'company')

    farm = models.ForeignKey(Farm, on_delete=models.CASCADE, related_name='externalplantcodefarm_set')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='externalplantcodecompany_set')
    code = models.CharField(max_length=100)


class GeoDistance(RawModel):
    class Meta:
        db_table = 'geo_distance'

    origin_latitude = models.FloatField()
    origin_longitude = models.FloatField()
    destination_latitude = models.FloatField()
    destination_longitude = models.FloatField()
    distance = models.FloatField()
    duration = models.FloatField()

    FILLABLES = [
        'destination_latitude',
        'origin_latitude',
        'origin_longitude',
        'destination_longitude',
    ]

    def to_display_distance(self, distance_unit):
        if self.distance:
            if distance_unit == KM:
                distance_in_km = self.distance / 1000
                return f"{int(distance_in_km) if distance_in_km.is_integer() else round(distance_in_km, 2)} {KM}"
            elif distance_unit == MILES:
                distance_in_miles = self.distance / 1609.34
                return f"{int(distance_in_miles) if distance_in_miles.is_integer() else round(distance_in_miles, 2)} {MILES}"  # pylint: disable=line-too-long

    def to_display_duration(self):
        if self.duration:
            hours = self.duration // 3600
            remaining_seconds = self.duration % 3600
            minutes = remaining_seconds // 60
            time_string = ""
            if hours > 0:
                time_string += f"{int(hours)} hour{'s' if hours > 1 else ''}"
            if minutes > 0:
                if time_string:
                    time_string += " and "
                time_string += f"{int(minutes)} minute{'s' if minutes > 1 else ''}"
            return time_string

    def get_response(self, country_code):
        country = Country.get_requesting_country(country_code)
        return {
            "distance": {
                "text": self.to_display_distance(country.config.get('distance_unit')),
                "value": self.distance
            },
            "duration": {
                "text": self.to_display_duration(),
                "value": self.duration
            },
            "status": "OK"
        }

    @classmethod
    def create_or_get_distance(cls, params):
        origin_latitude = params.get('origin_latitude')
        origin_longitude = params.get('origin_longitude')
        destination_latitude = params.get('destination_latitude')
        destination_longitude = params.get('destination_longitude')
        geo_distance = cls.get_geo_distance(
            origin_latitude, origin_longitude, destination_latitude, destination_longitude
        )
        country_code = get_request_country_code()
        if not geo_distance:
            distance_and_duration = GoogleMaps.get_distance(params, country_code)
            distance_value = get(distance_and_duration, 'distance.value')
            duration_value = get(distance_and_duration, 'duration.value')
            if distance_value and duration_value:
                geo_distance = cls(
                    origin_latitude=origin_latitude,
                    origin_longitude=origin_longitude,
                    destination_latitude=destination_latitude,
                    destination_longitude=destination_longitude,
                    distance=distance_value,
                    duration=duration_value
                )
                geo_distance.save()
        if geo_distance:
            return geo_distance.get_response(country_code)

    @classmethod
    def get_geo_distance(cls, origin_latitude, origin_longitude, destination_latitude, destination_longitude):
        return cls.objects.filter(
            origin_latitude=origin_latitude, origin_longitude=origin_longitude,
            destination_latitude=destination_latitude, destination_longitude=destination_longitude
        ).first()


class TargetMoisture(ShrinkageMixin):
    class Meta:
        db_table = 'target_moistures'
        constraints = [
            UniqueConstraint(
                fields=['storage', 'commodity', 'start_date', 'end_date'], name='storage_commodity_tenure_unique'
            ),
        ]

    percentage = models.FloatField(default=0, validators=[MaxValueValidator(100), MinValueValidator(0)])
    storage = models.ForeignKey(Storage, related_name='targetmoisture_set', on_delete=models.CASCADE)
    commodity = models.ForeignKey('commodities.Commodity', related_name='targetmoisture_set', on_delete=models.CASCADE)
    start_date = models.DateTimeField(null=False, blank=False, default=timezone.now)
    end_date = models.DateTimeField(null=False, blank=False, default=datetime.max)

    FILLABLES = [
        'percentage',
        'storage_id',
        'commodity_id',
        'start_date',
        'end_date',
        'dates_updated',
    ]

    @classmethod
    def bulk_create(cls, data):
        if not isinstance(data, list):
            data = [data]
        target_moistures = [cls(**d) for d in data]
        return cls.objects.bulk_create(target_moistures)

    @classmethod
    def all_target_moistures(cls, filters):
        if not filters:
            filters = {}
        return cls.objects.filter(**filters)

    @classmethod
    def get_applicable_target_moisture_for_storage(cls, storage_id, for_date=timezone.now().date()):
        return cls.objects.filter(
            storage_id=storage_id, start_date__lte=for_date, end_date__gte=for_date
        )

    @classmethod
    def get_applicable_target_moisture(cls, storage_id, for_date, commodity_id=None):
        qs = cls.get_applicable_target_moisture_for_storage(storage_id, for_date)
        if commodity_id:
            return qs.filter(commodity_id=commodity_id).first()
        return qs.first()

    @classmethod
    def get_tonnage_with_target_moisture(cls, load):
        """
        If Measured Moisture > Target Moisture
        Quantity after Shrinkage = (Gross - Tare) * (1 - Measured Moisture) / (1-Target Moisture)
        Dockage = Quantity after Shrinkage * Dockage %
        Net Quantity in Stock = Quantity after Shrinkage - Dockage = Quantity after Shrinkage * (1 - Dockage %)
        If Measured Moisture < Target Moisture OR no Target Moisture defined
        Dockage = Quantity after Shrinkage * Dockage %
        Net Quantity in Stock = (Gross - Tare) - Dockage = (Gross - Tare) * (1 - Dockage %)
        """

        specs = get(load, 'specs')
        moisture = float(get(specs, 'm') or 0)
        dockage = float(get(specs, 'dock') or 0)
        tonnage = load._entered_net_weight
        if load.is_inload:
            if moisture:
                target_moisture = load.target_moisture
                if target_moisture and moisture > target_moisture.percentage:
                    tonnage = (tonnage * (1 - (moisture * 0.01))) / (1 - (target_moisture.percentage * 0.01))
            if dockage:
                tonnage = tonnage * (1 - (dockage * 0.01))
        return tonnage


class InarixScenarioFarmMapping(RawModel):
    class Meta:
        db_table = 'inarix_scenario_farm_mappings'
        unique_together = ('inarix_scenario', 'farm')

    inarix_scenario = models.ForeignKey(
        'commodities.InarixScenario', on_delete=models.CASCADE, related_name='inarix_scenario_farm_mappings'
    )
    farm = models.ForeignKey(Farm, on_delete=models.CASCADE, related_name='inarix_scenario_farm_mappings')

    @classmethod
    def create_mapping(cls, data):
        scenario_id = data.get('inarix_scenario_id')
        farm_id = data.get('farm_id')
        if not scenario_id or not farm_id:
            return
        mapping = cls.objects.filter(inarix_scenario_id=scenario_id, farm_id=farm_id).first()
        if mapping:
            return mapping
        mapping = cls(inarix_scenario_id=scenario_id, farm_id=farm_id)
        mapping.save()
        return mapping

    @classmethod
    def get_mapping(cls, scenario_id, farm_id):
        return cls.objects.filter(inarix_scenario_id=scenario_id, farm_id=farm_id).first()

    @classmethod
    def get_mappings_by_scenario(cls, scenario_id):
        return cls.objects.filter(inarix_scenario_id=scenario_id)

    @classmethod
    def get_mappings_by_farm(cls, farm_id):
        return cls.objects.filter(farm_id=farm_id)


class SiteCameraStream(RawModel):
    class Meta:
        db_table = 'site_camera_streams'

    updated_at = None
    updated_by = None
    is_active = None

    site = models.ForeignKey(Farm, on_delete=models.CASCADE, related_name='camera_streams')
    camera_id = models.CharField(max_length=100)
    timestamp = models.DateTimeField()  # frame arrival time
    timestamp_camera = models.DateTimeField(null=True, blank=True)  # frame capture time
    timestamp_local = models.DateTimeField(null=True, blank=True)  # local time of the stream
    filename = models.TextField(null=True, blank=True)
    results = models.JSONField(default=list)
    event = CharField(max_length=50, null=True, blank=True)

    @classmethod
    def persist_stream(cls, stream_data, file, user):
        if isinstance(stream_data, str):
            try:
                stream_data = json.loads(stream_data)
            except json.JSONDecodeError as ex:
                raise ValueError("Invalid JSON data provided for stream_data.") from ex

        data = get(stream_data, 'data')
        hook = get(stream_data, 'hook')
        camera_id = get(data, 'camera_id')
        site = cls._get_site_from_camera_id(camera_id)
        stream = cls(camera_id=camera_id, site=site, event=get(hook, 'event'), created_by=user)
        for field in ['timestamp', 'timestamp_local', 'timestamp_camera','filename', 'results']:
            setattr(stream, field, get(data, field))
        if not stream.timestamp:
            stream.timestamp = timezone.now()
        stream.save()
        if stream.id and file:
            stream._upload_file(file)
        return stream

    @property
    def file_path(self):
        return f"sites/{self.site_id}/cameras/{self.camera_id}/{self.id}/{self.filename}"

    @property
    def file_url(self):
        return S3.url_for(self.file_path) if self.filename else None

    def _upload_file(self, file):
        if self.filename:
            S3.upload(self.file_path, file.read())

    @classmethod
    def _get_site_from_camera_id(cls, camera_id):
        try:
            site_id = camera_id.split('-')[0].strip()
            return Farm.objects.get(id=site_id) if site_id else None
        except Exception as ex:
            return str(ex)
