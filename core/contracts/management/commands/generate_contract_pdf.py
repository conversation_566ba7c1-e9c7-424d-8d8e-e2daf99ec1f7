from django.core.management.base import BaseCommand
from core.contracts.models import Contract


class Command(BaseCommand):
    help = 'Generate contract pdf'

    def handle(self, *args, **kwargs): # pylint: disable=unused-argument
        contracts = Contract.objects.exclude(status__in=['draft', 'template']).order_by('-id')
        for contract in contracts:
            try:
                for party in ['creator', 'seller', 'buyer']:
                    contract.generate_pdf(party=party)
                    self.stdout.write(
                        self.style.SUCCESS('PDF generated for "%s for contract ID:(%s)"!' % (party, contract.id))
                    )
            except Exception as ex: # pylint: disable=broad-except
                self.stdout.write(self.style.ERROR('PDF not generated for "contract ID:(%s)"!' % contract.id))
                self.stdout.write(self.style.ERROR('Error: %s!' % ex.args[0]))
