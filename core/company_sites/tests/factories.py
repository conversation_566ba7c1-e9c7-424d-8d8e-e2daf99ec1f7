import factory
from factory import SubFactory, Sequence

from core.companies.tests.factories import CompanyFactory
from core.company_sites.models import CompanySite
from core.farms.models import Storage
from core.locations.tests.factories import AddressFactory


class CompanySiteFactory(factory.django.DjangoModelFactory):
    name = Sequence("Company Site-{}".format)
    address = SubFactory(AddressFactory)
    company = SubFactory(CompanyFactory)

    class Meta:
        model = CompanySite

    @classmethod
    def _after_postgeneration(cls, instance, create, results=None):
        super()._after_postgeneration(instance, create, results)
        if instance.id:
            Storage.create_gate(instance)
