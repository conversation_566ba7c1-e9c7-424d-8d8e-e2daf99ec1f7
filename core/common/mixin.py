from django.db import models
from pydash import get

from core.common.constants import RESPONSE_EXTERNAL_API_HEADER
from core.common.exceptions import Http400
from core.common.throttling import ThrottleUtil
from core.timezones.utils import DateTimeUtil


class PublicAPIMixin:
    def get_throttles(self):
        return ThrottleUtil.get_throttles_by_user_plan(self.request.user)

    filters = {
        'updated_at__gte': {'field': 'updated_at', 'operator': 'gte', 'type': 'datetime'},
        'updated_at__lt': {'field': 'updated_at', 'operator': 'lt', 'type': 'datetime'},
        'status': {'field': 'status', 'operator': 'in', 'type': 'list', 'case_insensitive': True, 'lower': True},
        'season': {'field': 'season', 'operator': 'in', 'type': 'list'},
        'grade': {'field': 'grade__name', 'operator': 'iexact', 'type': 'list', 'case_insensitive': True},
        'commodity': {'field': 'commodity__name', 'operator': 'iexact', 'type': 'list', 'case_insensitive': True},
    }

    def get_criteria_from_field(self, field_name, definition):
        if not definition:
            return None

        value = self._get_param_value_from_definition(field_name, definition)

        if not value:
            return None

        criteria = models.Q()

        if isinstance(definition['field'], list):
            criteria = models.Q()
            for child_field_definition in definition['field']:
                parent_field_name = field_name
                if definition['operator'] == 'or':
                    criteria |= self.get_criteria_from_field(parent_field_name, child_field_definition)
                elif definition['operator'] == 'and':
                    criteria &= self.get_criteria_from_field(parent_field_name, child_field_definition)
            return criteria

        field_with_operator = self._get_field_name_from_definition(definition)

        applied = False
        if definition.get('case_insensitive', False):
            if definition.get('lower', False):
                value = [val.lower() for val in value] if isinstance(value, list) else value.lower()

            if definition.get('type', None) == 'list' and not definition.get('lower', False):
                for val in value:
                    criteria |= models.Q(**{field_with_operator: val})
                applied = True

        if not applied:
            criteria |= models.Q(**{field_with_operator: value})

        return criteria

    def get_filters_criterion(self):
        criterion = models.Q()
        for field_name, definition in self.filters.items():
            criteria = self.get_criteria_from_field(field_name, definition)
            if not criteria:
                continue
            criterion &= criteria
        return criterion

    def get_timezone(self):
        return self.get_request_timezone() or self.get_user_default_timezone()

    def get_request_timezone(self):
        return self.request.META.get('HTTP_REFERER_TZ', None)

    def get_user_default_timezone(self):
        return self.request.user.country.timezone

    def get_param(self, name, default_value=None):
        return self.request.query_params.get(name, default_value)

    def get_list_param(self, name, default_value=None):
        # ?status=confirmed,open
        value = self.request.query_params.get(name, default_value or [])
        if value:
            return value.split(',')
        return value or default_value

    def filter_queryset(self, queryset):
        return queryset.filter(self.get_filters_criterion())

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)
        response[RESPONSE_EXTERNAL_API_HEADER] = True
        return response

    # private
    @staticmethod
    def _get_field_name_from_definition(definition):
        field_with_operator = definition['field']
        if definition.get('operator', None):
            field_with_operator += f"__{definition['operator']}"
        return field_with_operator

    def _get_param_value_from_definition(self, field, definition):
        field_type = definition.get('type', None)
        if field_type == 'list':
            return self.get_list_param(field)
        if field_type == 'datetime':
            try:
                return DateTimeUtil.get_datetime_from_string(self.get_param(field))
            except Exception as ex:  # pylint: disable=broad-except
                raise Http400(f"Invalid datetime format for field {field}") from ex

        return self.get_param(field)


class FacetMixin:
    facet_key = None
    model = None

    @property
    def is_facets_only(self):
        return self.request.query_params.get('only_facets', None) in ['true', 'True', '1', True]

    def get_facets(self, params):
        facets = {}
        for key, values in self.request.query_params.lists():
            if key.startswith('facets[') and key.endswith(']'):
                params.pop(key, None)
                facet_key = key[7:-1]  # Extract the key within the brackets
                if facet_key not in facets:
                    facets[facet_key] = values
                else:
                    facets[facet_key].extend(values)
        return facets or get(
            self.request.user.employee_view_filters.filter(facets__has_key=self.facet_key),
            f'0.facets.{self.facet_key}'
        )

    def apply_facets(self, queryset, facets):
        user = self.request.user
        if facets:
            user.update_facets(self.facet_key, facets)
            return self.model.apply_facets(queryset, facets)
        return queryset
