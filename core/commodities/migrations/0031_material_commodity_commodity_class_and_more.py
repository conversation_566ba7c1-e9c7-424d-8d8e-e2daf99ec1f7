# Generated by Django 4.0.6 on 2022-07-12 10:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0030_alter_historicalcommodity_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Material',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('material_class', models.CharField(max_length=100)),
            ],
            options={
                'db_table': 'materials',
            },
        ),
        migrations.AddField(
            model_name='commodity',
            name='commodity_class',
            field=models.CharField(default='Class 3', max_length=100),
        ),
        migrations.AddField(
            model_name='historicalcommodity',
            name='commodity_class',
            field=models.CharField(default='Class 3', max_length=100),
        ),
    ]
