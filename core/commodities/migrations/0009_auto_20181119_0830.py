# Generated by Django 2.1.3 on 2018-11-19 08:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0008_auto_20181030_1314'),
    ]

    operations = [
        migrations.AlterField(
            model_name='commodity',
            name='name',
            field=models.CharField(choices=[('wheat', 'Wheat'), ('barley', 'Barley'), ('canola', 'Canola'), ('sorghum', 'Sorghum'), ('lentils', 'Lentils'), ('chickpeas_desi', 'Chickpeas (Desi)'), ('chickpeas_kabuli', 'Chickpeas (Kabuli)'), ('oats', 'Oats'), ('triticale', 'Triticale'), ('maize', 'Maize'), ('broad_beans', 'Broad Beans'), ('faba_beans', 'Faba Beans'), ('lupins', 'Lupins'), ('mungbeans', 'Mungbeans'), ('field_peas', 'Field Peas'), ('vetch', 'Vetch'), ('sunflower', 'Sunflower'), ('soybean', 'Soybean'), ('cottonseed', 'Cottonseed'), ('safflower', 'Safflower')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalcommodity',
            name='name',
            field=models.CharField(choices=[('wheat', 'Wheat'), ('barley', 'Barley'), ('canola', 'Canola'), ('sorghum', 'Sorghum'), ('lentils', 'Lentils'), ('chickpeas_desi', 'Chickpeas (Desi)'), ('chickpeas_kabuli', 'Chickpeas (Kabuli)'), ('oats', 'Oats'), ('triticale', 'Triticale'), ('maize', 'Maize'), ('broad_beans', 'Broad Beans'), ('faba_beans', 'Faba Beans'), ('lupins', 'Lupins'), ('mungbeans', 'Mungbeans'), ('field_peas', 'Field Peas'), ('vetch', 'Vetch'), ('sunflower', 'Sunflower'), ('soybean', 'Soybean'), ('cottonseed', 'Cottonseed'), ('safflower', 'Safflower')], max_length=100),
        ),
    ]
