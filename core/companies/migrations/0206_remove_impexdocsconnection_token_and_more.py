# Generated by Django 4.2.15 on 2025-02-12 08:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0205_alter_impexdocsconnection_company_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='impexdocsconnection',
            name='token',
        ),
        migrations.RemoveField(
            model_name='xeroconnection',
            name='token',
        ),
        migrations.AddField(
            model_name='impexdocsconnection',
            name='access_token',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='impexdocsconnection',
            name='refresh_token',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='xeroconnection',
            name='access_token',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='xeroconnection',
            name='refresh_token',
            field=models.TextField(blank=True, null=True),
        ),
    ]
