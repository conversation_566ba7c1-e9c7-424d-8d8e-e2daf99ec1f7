# Generated by Django 4.0.6 on 2022-09-12 08:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0036_commodity_sustainable_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='historicalcommodity',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical commodity', 'verbose_name_plural': 'historical commoditys'},
        ),
        migrations.AlterModelOptions(
            name='historicalepr',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical epr', 'verbose_name_plural': 'historical eprs'},
        ),
        migrations.AlterModelOptions(
            name='historicalgrade',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical grade', 'verbose_name_plural': 'historical grades'},
        ),
        migrations.AlterModelOptions(
            name='historicalvariety',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical variety', 'verbose_name_plural': 'historical varietys'},
        ),
        migrations.AddField(
            model_name='commodity',
            name='unit',
            field=models.CharField(choices=[('MT', 'MT'), ('Bales', 'Bales'), ('m³', 'm³')], default='MT', max_length=100),
        ),
        migrations.AddField(
            model_name='historicalcommodity',
            name='unit',
            field=models.CharField(choices=[('MT', 'MT'), ('Bales', 'Bales'), ('m³', 'm³')], default='MT', max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalcommodity',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='historicalepr',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='historicalgrade',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='historicalvariety',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
    ]
