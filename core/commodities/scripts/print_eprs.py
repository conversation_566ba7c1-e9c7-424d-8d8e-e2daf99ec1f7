import csv
import json

from core.commodities.models import Variety


def eprs(filepath, commodity_id, starts_with=1):
    csvfile = open(filepath, "r")
    commodity_id = commodity_id
    fieldnames = ("Variety","Royalty Manager","EPR rate (GSTexcl)",)
    reader = csv.DictReader(csvfile, fieldnames)
    res = []
    varities = Variety.objects.filter(commodity_id=commodity_id).only('id', 'name')
    id = starts_with

    for i, row in enumerate(reader):
        if(i!=0):
            _dict = dict(row)
            _json = {
                "fields": {
                    "created_at": "2018-10-24T08:00:00+00:00",
                    "updated_at": "2018-10-24T08:00:00+00:00",
                },
            }
            _json["pk"] = id
            _json["model"] = "commodities.epr"
            _json["fields"]["variety_id"] = list(filter(lambda v: v.name == _dict.get("Variety"), varities))[0].id
            _json["fields"]["rate"] = float(_dict.get("EPR rate (GSTexcl)")[1:])
            _json["fields"]["manager"] = _dict.get("Royalty Manager")
            res.append(_json)
            id += 1

    print(json.dumps(res))
    print("==================================")
    print("Add " + str(len(res)) + " to next")

