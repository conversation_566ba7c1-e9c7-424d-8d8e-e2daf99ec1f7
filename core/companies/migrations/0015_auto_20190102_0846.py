# Generated by Django 2.1.4 on 2019-01-02 08:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('farms', '0014_auto_20190102_0846'),
        ('companies', '0014_auto_20181228_0547'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalfarmdirectory',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_farmdirectorys_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalfarmdirectory',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_farmdirectorys_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='farmdirectory',
            unique_together={('company', 'farm')},
        ),
    ]
