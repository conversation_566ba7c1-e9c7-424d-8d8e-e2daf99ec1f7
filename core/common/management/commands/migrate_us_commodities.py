from django.core.management import BaseCommand
from pydash import get, compact, flatten_deep

from core.cash_board.models import CashPrices
from core.commodities.models import Commodity, Grade
from core.contracts.models import Contract, TitleTransfer
from core.farms.models import Storage
from core.freights.models import FreightContract, FreightOrder
from core.loads.models import Load
from core.stocks.models import Stock
from core.vendor_decs.models import CommodityVendorDec

COMMODITIES = {
    'Organic Barley': [
        "Organic Six-Rowed Barley",
        "Organic Two-Rowed Barley",
        "Organic Malting Barley",
        "Organic Six-Rowed Malting Barley",
        "Organic Two-Rowed Malting Barley"
    ],
    'Organic Corn': [
        "Organic Yellow Corn",
        "Organic White Corn",
        "Organic Mixed Corn",
        "Organic Blue Corn"
    ],
    'Organic Sorghum': [
        "Organic Tannin Sorghum",
        "Organic White Sorghum",
        "Organic Mixed Sorghum"
    ],
    'Organic Soybean': [
        "Organic Yellow Soybeans",
        "Organic Mixed Soybeans"
    ],
    'Organic Spring Wheat': [
        "Organic Red Spring Wheat",
        "Organic Northern Spring Wheat",
        "Organic Dark Northern Spring Wheat"
    ],
    "Organic Wheat": [
        "Organic Durum Wheat",
        "Organic Amber Durum Wheat",
        "Organic Hard Amber Durum Wheat",
        "Organic Hard White Wheat",
        "Organic Soft White Wheat",
        "Organic White Club Wheat",
        "Organic Western White Wheat",
        "Organic Unclassed Wheat",
        "Organic Mixed Wheat"
    ],
    "Organic Winter Wheat": [
        "Organic Soft Red Winter Wheat",
        "Organic Soft White Winter Wheat"
    ],
    "Transitional Barley": [
        "Transitional Six-Rowed Barley",
        "Transitional Two-Rowed Barley",
        "Transitional Malting Barley",
        "Transitional Six-Rowed Malting Barley",
        "Transitional Two-Rowed Malting Barley"
    ],
    "Transitional Corn": [
        "Transitional Yellow Corn",
        "Transitional White Corn",
        "Transitional Mixed Corn",
        "Transitional Blue Corn"
    ],
    "Transitional Sorghum": [
        "Transitional Tannin Sorghum",
        "Transitional White Sorghum",
        "Transitional Mixed Sorghum"
    ],
    "Transitional Soybean": [
        "Transitional Yellow Soybeans",
        "Transitional Mixed Soybeans"
    ],
    "Transitional Spring Wheat": [
        "Transitional Red Spring Wheat",
        "Transitional Northern Spring Wheat",
        "Transitional Dark Northern Spring Wheat"
    ],
    "Transitional Wheat": [
        "Transitional Durum Wheat",
        "Transitional Amber Durum Wheat",
        "Transitional Hard Amber Durum Wheat",
        "Transitional Hard White Wheat",
        "Transitional Soft White Wheat",
        "Transitional White Club Wheat",
        "Transitional Western White Wheat",
        "Transitional Unclassed Wheat",
        "Transitional Mixed Wheat"
    ],
    "Transitional Winter Wheat": [
        "Transitional Soft Red Winter Wheat",
        "Transitional Soft White Winter Wheat"
    ]
}

TO_COMMODITIES = [
    'Organic Barley',
    'Organic Black Beans',
    'Organic Buckwheat',
    'Organic Canola',
    'Organic Corn',
    'Organic Field Pea',
    'Organic Flaxseed',
    'Organic Mixed Grain',
    'Organic Oat',
    'Organic Popcorn',
    'Organic Rye',
    'Organic Sorghum',
    'Organic Soybean',
    'Organic Spring Wheat',
    'Organic Sunflower',
    'Organic Sunflower Black Oil',
    'Organic Triticale',
    'Organic Wheat',
    'Organic Winter Wheat',
    'Sunflower Confectionery',
    'Transitional Barley',
    'Transitional Black Beans',
    'Transitional Buckwheat',
    'Transitional Canola',
    'Transitional Corn',
    'Transitional Field Pea',
    'Transitional Flaxseed',
    'Transitional Mixed Grain',
    'Transitional Oat',
    'Transitional Popcorn',
    'Transitional Rye',
    'Transitional Sorghum',
    'Transitional Soybean',
    'Transitional Spring Wheat',
    'Transitional Sunflower',
    'Transitional Sunflower Black Oil',
    'Transitional Sunflower Confectionery',
    'Transitional Triticale',
    'Transitional Wheat',
    'Transitional Winter Wheat'
]


def get_to_commodities():
    return Commodity.objects.filter(country_id=2, name__in=list(COMMODITIES.keys()))


def get_from_commodities(commodity):
    return Commodity.objects.filter(country_id=2, name__in=COMMODITIES[commodity.name])


def get_grades_not_in_to_commodities():
    details = {}
    for to_commodity in get_to_commodities():
        from_commodities = get_from_commodities(to_commodity)
        from_grades = Grade.objects.filter(commodity__in=from_commodities).values_list('name', flat=True)
        to_grades = to_commodity.grade_set.filter(name__in=from_grades).values_list('name', flat=True)
        diff = set(from_grades) - set(to_grades)
        if len(diff):
            details[to_commodity.name] = diff
    return details


def process(
        klass, to_commodity, grade_field_name='grade', manager='objects'
):
    manager = get(klass, manager)
    from_commodities = get_from_commodities(to_commodity)
    for from_commodity in from_commodities:
        queryset = manager.filter(commodity_id=from_commodity.id)
        count = queryset.count()
        processed = 0
        failed = 0
        for instance in queryset:
            processed += 1
            if get(instance, f"{grade_field_name}_id"):
                grade_name = get(instance, f'{grade_field_name}.name')
                setattr(instance, grade_field_name, get_grade(grade_name, to_commodity))
                instance.save(update_fields=[grade_field_name])
                if instance.errors:
                    failed += 1
                    print(f"{instance.__class__} ERRORS: {instance.errors}")
        queryset.update(commodity_id=to_commodity.id)
        print(f"total={count} | processed={processed} | failed={failed}")


def get_grade(grade_name, to_commodity):
    if 'TBD' in grade_name:
        grade = to_commodity.grade_set.filter(name__icontains='TBD').first()
    else:
        grade = to_commodity.grade_set.filter(name__icontains=grade_name).first()
    return grade


def get_loads(to_commodity):
    loads = []
    for from_commodity in get_from_commodities(to_commodity):
        queryset = Load.objects.filter(commodity_id=from_commodity.id)
        for instance in queryset:
            payload = {
                'id': instance.id,
                'commodity': to_commodity
            }
            if instance.grade_id:
                payload['grade_id'] = get_grade(instance.grade.name, to_commodity).id
            loads.append(payload)

    return loads


class Command(BaseCommand):
    help = 'Migrate old Hay/Straw commodities to new Hay/Straw Commodities'

    def handle(self, *args, **options):
        for commodity in Commodity.objects.filter(country_id=2, name__icontains='Non GMO'):
            commodity.name = commodity.name.replace(' / Non GMO', '')
            commodity.save()

        def __process(klass, manager='objects'):
            print(f"Processing {klass.__name__}s...")
            grade_field_name = 'planned_grade' if klass in [FreightOrder, FreightContract] else 'grade'

            for commodity in get_to_commodities():
                process(klass, commodity, grade_field_name, manager)

        def __process_loads():
            print("Processing Loads...")
            payloads = []

            for commodity in get_to_commodities():
                payloads += get_loads(commodity)

            load_ids = []
            count = len(payloads)
            for payload in payloads:
                load_id = payload.get('id')
                Load.objects.filter(id=load_id).update(**{k: v for k, v in payload.items() if k != 'id'})
                load_ids.append(load_id)

            loads = Load.objects.filter(id__in=load_ids, storage_id__isnull=False)
            storage_ids = compact(set(loads.values_list('storage_id', flat=True)))
            for storage_id in storage_ids:
                commodity_ids = set(loads.filter(storage_id=storage_id).values_list('commodity_id', flat=True))
                storage = Storage.objects.filter(id=storage_id).first()
                if storage:
                    for commodity_id in commodity_ids:
                        storage.recreate_storage_levels_from_loads_inline(
                            {
                                'storage_id': storage_id,
                                'commodity_id': commodity_id
                            })

            print(f"total={count}")

        __process(Contract)
        __process(FreightOrder)
        __process(FreightContract)
        __process(TitleTransfer)
        __process(klass=Storage, manager='all')
        __process(CommodityVendorDec)
        __process(CashPrices)
        __process_loads()
        Stock.recalculate_stocks({'commodity__country_id': 2})
        commodities = Commodity.objects.filter(country_id=2, name__in=flatten_deep(COMMODITIES.values()))
        grade_ids_to_be_deleted = Grade.objects.filter(commodity__in=commodities).values_list('id', flat=True)

        print("********")
        print("Delete Grades IDs:", list(grade_ids_to_be_deleted))
        print("Delete Commodities IDs:", list(commodities.values_list('id', flat=True)))

# Grades to be delete [1041, 1100, 1043, 1044, 1045, 1046, 1047, 1292, 1049, 1318, 1366, 1094, 1053, 1286, 1069, 1065, 1057, 1227, 1061, 1222, 1432, 1330, 1324, 1274, 1217, 1212, 1197, 1426, 1414, 1192, 1438, 1336, 1408, 1187, 1182, 1268, 1402, 1177, 1172, 1312, 1396, 1444, 1136, 1280, 1390, 1130, 1124, 1342, 1384, 1360, 1118, 1262, 1378, 1450, 1112, 1420, 1025, 1026, 1027, 1028, 1029, 1371, 1031, 1032, 1033, 1034, 1035, 1106, 1037, 1038, 1039, 1040, 1287, 1293, 1421, 1313, 1331, 1319, 1325, 1415, 1409, 1403, 1397, 1391, 1385, 1379, 1373, 1367, 1050, 1054, 1058, 1062, 1066, 1070, 1095, 1101, 1107, 1451, 1113, 1361, 1119, 1125, 1131, 1445, 1137, 1343, 1173, 1178, 1183, 1439, 1188, 1193, 1198, 1213, 1433, 1218, 1223, 1228, 1263, 1427, 1269, 1275, 1281, 1337, 1282, 1416, 1276, 1199, 1422, 1434, 1398, 1270, 1214, 1174, 1446, 1326, 1338, 1219, 1380, 1264, 1386, 1179, 1224, 1132, 1059, 1440, 1063, 1404, 1120, 1184, 1067, 1314, 1055, 1320, 1071, 1344, 1392, 1294, 1096, 1189, 1332, 1051, 1368, 1229, 1102, 1410, 1458, 1138, 1288, 1194, 1108, 1126, 1428, 1362, 1374, 1114, 1315, 1115, 1375, 1121, 1447, 1381, 1265, 1127, 1345, 1387, 1283, 1133, 1139, 1393, 1175, 1441, 1399, 1271, 1180, 1295, 1185, 1339, 1405, 1190, 1411, 1195, 1435, 1200, 1327, 1277, 1215, 1423, 1220, 1417, 1321, 1333, 1060, 1225, 1064, 1056, 1429, 1068, 1072, 1052, 1289, 1097, 14459, 1103, 1363, 1369, 1230, 1109, 1290, 1098, 1104, 1110, 1116, 1122, 1128, 1134, 1140, 1176, 1181, 1186, 1191, 1196, 1201, 1216, 1221, 1226, 1231, 1266, 1272, 1278, 1284, 1296, 1316, 1322, 1328, 1334, 1340, 1346, 1364, 1370, 1376, 1382, 1388, 1394, 1400, 1406, 1412, 1418, 1424, 1430, 1436, 1442, 1448, 1460, 1347, 1449, 1419, 1117, 1401, 1365, 1099, 1389, 1425, 1377, 1141, 1135, 1129, 1105, 1407, 1431, 1111, 1395, 1461, 1413, 1443, 1329, 1323, 1317, 1297, 1291, 1437, 1335, 1123, 1285, 1279, 1273, 1383, 1341, 1267, 1048, 1030, 1042, 1036, 963, 960, 959, 958, 923, 957, 956, 955, 944, 922, 943, 942, 941, 940, 921, 939, 938, 919, 937, 928, 1006, 1005, 1004, 920, 1003, 1002, 1001, 1000, 999, 998, 997, 996, 927, 995, 994, 993, 992, 926, 991, 988, 987, 986, 925, 985, 984, 983, 980, 924, 979, 978, 977, 976, 975, 966, 965, 964]
# Commodities to be delete [91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 109, 110, 111, 112, 113, 114, 115, 116, 127, 128, 129, 130, 131, 132, 135, 136, 137, 138, 147, 148, 149, 150, 151, 152, 155, 156, 157, 158, 159, 160, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178]
