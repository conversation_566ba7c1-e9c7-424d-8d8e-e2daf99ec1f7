# Generated by Django 4.1.13 on 2024-05-23 07:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0204_chemicalapplication_load_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicaltolerance',
            name='name',
            field=models.CharField(choices=[('Nil Tolerance', 'Nil Tolerance'), ('5% or 12mt (whichever is lesser)', '5% or 12mt (whichever is lesser)'), ('2% or 2mt (whichever is lesser)', '2% or 2mt (whichever is lesser)'), ('+/- 10%', '+/- 10%'), ('+/- 5%', '+/- 5%'), ('+/- 10mt', '+/- 10mt'), ('+/- 5mt', '+/- 5mt'), ('+/- 15mt', '+/- 15mt'), ('+/- 50mt', '+/- 50mt'), ('+/- 3mt', '+/- 3mt'), ('+/- 40mt', '+/- 40mt'), ('+/- 13mt', '+/- 13mt'), ('+/- 20mt', '+/- 20mt'), ('2.5% or 13mt (whichever is lesser)', '2.5% or 13mt (whichever is lesser)'), ('+/- 25mt', '+/- 25mt'), ('5% or 20mt (whichever is lesser)', '5% or 20mt (whichever is lesser)'), ('Refer to Special Conditions', 'Refer to Special Conditions')], max_length=100),
        ),
        migrations.AlterField(
            model_name='tolerance',
            name='name',
            field=models.CharField(choices=[('Nil Tolerance', 'Nil Tolerance'), ('5% or 12mt (whichever is lesser)', '5% or 12mt (whichever is lesser)'), ('2% or 2mt (whichever is lesser)', '2% or 2mt (whichever is lesser)'), ('+/- 10%', '+/- 10%'), ('+/- 5%', '+/- 5%'), ('+/- 10mt', '+/- 10mt'), ('+/- 5mt', '+/- 5mt'), ('+/- 15mt', '+/- 15mt'), ('+/- 50mt', '+/- 50mt'), ('+/- 3mt', '+/- 3mt'), ('+/- 40mt', '+/- 40mt'), ('+/- 13mt', '+/- 13mt'), ('+/- 20mt', '+/- 20mt'), ('2.5% or 13mt (whichever is lesser)', '2.5% or 13mt (whichever is lesser)'), ('+/- 25mt', '+/- 25mt'), ('5% or 20mt (whichever is lesser)', '5% or 20mt (whichever is lesser)'), ('Refer to Special Conditions', 'Refer to Special Conditions')], max_length=100),
        ),
    ]
