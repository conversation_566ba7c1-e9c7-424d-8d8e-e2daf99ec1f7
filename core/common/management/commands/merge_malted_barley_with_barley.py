# MABA Grade (1694) of Malted Barley to MABA Grade (2074) of Barley
# Malted Barley (226) to Barley (2)


from django.core.management import BaseCommand
from pydash import compact

from core.cash_board.models import CashPrices
from core.commodities.models import Grade, Commodity
from core.companies.models import XeroMapping
from core.company_sites.models import FreightSlot
from core.contracts.models import Contract, TitleTransfer
from core.farms.models import Storage, WarehouseFees
from core.freights.models import FreightContract, FreightOrder
from core.loads.models import Load
from core.stocks.models import Stock
from core.vendor_decs.models import CommodityVendorDec

malted_barley_id = 226
old_maba_id = 1694
barley_id = 2
new_maba_id = 2074

class Command(BaseCommand):
    help = 'Migrate Malted Barley to Barley'

    def handle(self, *args, **options):
        Contract.objects.filter(commodity_id=malted_barley_id).update(commodity_id=barley_id)
        Contract.objects.filter(grade_id=old_maba_id).update(grade_id=new_maba_id)
        FreightOrder.objects.filter(commodity_id=malted_barley_id).update(commodity_id=barley_id)
        FreightOrder.objects.filter(planned_grade_id=old_maba_id).update(planned_grade_id=new_maba_id)
        FreightContract.objects.filter(commodity_id=malted_barley_id).update(commodity_id=barley_id)
        FreightContract.objects.filter(planned_grade_id=old_maba_id).update(planned_grade_id=new_maba_id)
        TitleTransfer.objects.filter(commodity_id=malted_barley_id).update(commodity_id=barley_id)
        TitleTransfer.objects.filter(grade_id=old_maba_id).update(grade_id=new_maba_id)
        Storage.all.filter(commodity_id=malted_barley_id).update(commodity_id=barley_id)
        Storage.all.filter(grade_id=old_maba_id).update(grade_id=new_maba_id)
        CommodityVendorDec.objects.filter(commodity_id=malted_barley_id).update(commodity_id=barley_id)
        CommodityVendorDec.objects.filter(grade_id=old_maba_id).update(grade_id=new_maba_id)
        CashPrices.objects.filter(commodity_id=malted_barley_id).update(commodity_id=barley_id)
        CashPrices.objects.filter(grade_id=old_maba_id).update(grade_id=new_maba_id)
        FreightSlot.objects.filter(commodity_id=malted_barley_id).update(commodity_id=barley_id)
        FreightSlot.objects.filter(grade_id=old_maba_id).update(grade_id=new_maba_id)
        Load.objects.filter(commodity_id=malted_barley_id).update(commodity_id=barley_id)
        Load.objects.filter(grade_id=old_maba_id).update(grade_id=new_maba_id)
        Stock.objects.filter(commodity_id=malted_barley_id).delete()
        Stock.recalculate_stocks({'commodity_id': barley_id, 'grade_id': new_maba_id})
        XeroMapping.objects.filter(commodity_id=malted_barley_id).update(commodity_id=barley_id)
        XeroMapping.objects.filter(grade_id=old_maba_id).update(grade_id=new_maba_id)
        WarehouseFees.objects.filter(commodity_id=malted_barley_id).delete()
        WarehouseFees.objects.filter(grade_id=old_maba_id).delete()
        Grade.objects.filter(id=old_maba_id).delete()
        Commodity.objects.filter(id=malted_barley_id).delete()




