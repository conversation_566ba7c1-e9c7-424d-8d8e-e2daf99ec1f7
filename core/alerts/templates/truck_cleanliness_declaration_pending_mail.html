{% load static %}
{% load app_filters %}
{% with  date_time_format="M d Y, h:i A"  date_format="M d, Y" %}
<html style="-webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; font-family: sans-serif; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);">
    <head>
        <meta charset="UTF-8">
        <style>
            table tr {page-break-inside: avoid}
        </style>
    </head>
    <body style="-webkit-box-sizing: border-box; margin:0; -moz-box-sizing: border-box; box-sizing: border-box; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; line-height: 1.42857; color: #000000; background-color: #fff;">
        <div style="padding: 20px;">
            <span>Hi</span><br/>
            <p>
                This is to notify you that the truck cleanliness declaration has not been filled for the Movement #
                : {{movement.identifier}} scheduled for {{date_time}}. Please find the relevant details below:
            </p><br/>
            <table>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Pickup Site:</td>
                    <td style="width: 50%; font-size: 10pt;">{{movement.consignor_name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Delivery Site:</td>
                    <td style="width: 50%; font-size: 10pt;">{{movement.consignee_name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Freight Provider:</td>
                    <td style="width: 50%; font-size: 10pt;">{{movement.provider.name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">{{'truck_rego' | to_country_label:movement.country_code}}:</td>
                    {% if slot %}
                        <td style="width: 50%; font-size: 10pt;">{{slot.truck.rego}}</td>
                    {% elif movement.planned_truck %}
                        <td style="width: 50%; font-size: 10pt;">{{movement.planned_truck.rego}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Truck Driver:</td>
                    {% if slot %}
                        <td style="width: 50%; font-size: 10pt;">{{slot.driver.name}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Truck Driver Mobile:</td>
                    {% if slot.driver.mobile is not None %}
                        <td style="width: 50%; font-size: 10pt;">{{slot.driver.mobile}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Customer:</td>
                    <td style="width: 50%; font-size: 10pt;">{% firstof movement.customer.name movement.customer.display_name %}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Customer Contact:</td>
                    <td style="width: 50%; font-size: 10pt;">{{movement.customer.contact.name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Customer Contact Mobile:</td>
                    {% if movement.customer.contact.mobile is not None %}
                        <td style="width: 50%; font-size: 10pt;">{{movement.customer.contact.mobile}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Order:</td>
                    {% if movement.order %}
                        <td style="width: 50%; font-size: 10pt;">{{movement.order.identifier}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Booking / Movement:</td>
                    <td style="width: 50%; font-size: 10pt;">{{movement.identifier}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Booking Date and Time:</td>
                    <td style="width: 50%; font-size: 10pt;">{{date_time}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Commodity:</td>
                    <td style="width: 50%; font-size: 10pt;">{{movement.commodity.display_name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Grade:</td>
                    <td style="width: 50%; font-size: 10pt;">{{movement.planned_grade.name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Season:</td>
                    <td style="width: 50%; font-size: 10pt;">{{movement.season}}</td>
                </tr>
            </table>
            <br/>
                Please action accordingly.
            <br/>
                Thank you.
            <br/>
        </div>
    </body>
</html>
{% endwith %}