# Generated by Django 4.1.13 on 2024-04-22 05:01

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0163_company__sftp_configuration_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            """
            create index companies_test_idx on companies (id) where type_id != 6 and (UPPER("companies"."business_name"::text) LIKE UPPER('%agrichain%') OR "companies"."business_name"::text LIKE '%Test%');
            """
        )
    ]
