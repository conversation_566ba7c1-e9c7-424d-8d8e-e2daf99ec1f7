# Generated by Django 2.2.20 on 2021-08-09 10:46

from django.db import migrations, models
import django_fsm


class Migration(migrations.Migration):

    dependencies = [
        ('cash_board', '0010_auto_20210805_1008'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='cashprices',
            name='cash_price_unique',
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='limit',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='price',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='cashprices',
            name='status',
            field=django_fsm.FSMField(choices=[('active', 'Active'), ('completed', 'Completed'), ('inactive', 'Inactive')], db_index=True, default='active', max_length=40),
        ),
        migrations.AlterField(
            model_name='historicalcashprices',
            name='limit',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalcashprices',
            name='price',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalcashprices',
            name='status',
            field=django_fsm.FSMField(choices=[('active', 'Active'), ('completed', 'Completed'), ('inactive', 'Inactive')], db_index=True, default='active', max_length=40),
        ),
    ]
