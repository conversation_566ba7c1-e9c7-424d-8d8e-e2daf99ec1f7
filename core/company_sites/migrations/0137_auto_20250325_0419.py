# Generated by Django 5.1.7 on 2025-03-25 04:19

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0136_sitemanagementsettings_trailer_booking_quantity'),
    ]
operations = [
    migrations.RunSQL('CREATE INDEX if not exists slots_site_start_end_idx ON freight_slots (site_id, start, "end");'),
    migrations.RunSQL("CREATE INDEX if not exists slots_inload_counter_slot_idx ON freight_slots (movement_id, id asc) where not is_trailer_slot and type='inload';"),
    migrations.RunSQL("CREATE INDEX if not exists slots_outload_counter_slot_idx ON freight_slots (movement_id, id asc) where not is_trailer_slot and type='outload';")
]
