from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey

from core.audit_history.constants import ACTOR_DISPLAY
from core.common.models import RawModel
from core.common.utils import abstract_class_without_fields as without


class AuditHistory(without(RawModel, 'updated_by', 'updated_at')):
    class Meta:
        db_table = 'audit_history'

    instance_id = models.PositiveIntegerField()
    instance_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    instance = GenericForeignKey('instance_type', 'instance_id')
    description = models.TextField()
    exclusions = models.JSONField(null=True, blank=True)
    args = models.JSONField(null=True, blank=True)

    @property
    def actor(self):
        return ACTOR_DISPLAY.format(creator=self.created_by.name, creator_company=self.created_by.company.name)

    @classmethod
    def create_record(cls, description, created_by, instance, args=None):
        audit_history_kwargs = {'instance': instance, 'is_active': instance.is_active, 'description': description,
                                'created_by_id': created_by}
        if args:
            audit_history_kwargs.update(args=args)
        cls.objects.create(**audit_history_kwargs)
