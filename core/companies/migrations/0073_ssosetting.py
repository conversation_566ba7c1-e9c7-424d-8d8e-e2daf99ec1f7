# Generated by Django 3.2.10 on 2022-02-07 03:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0072_auto_20220127_1842'),
    ]

    operations = [
        migrations.CreateModel(
            name='SSOSetting',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sso_url', models.TextField()),
                ('agrichain_auth_enabled', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
                ('extras', models.JSONField(blank=True, null=True)),
                ('company', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='sso_setting', to='companies.company')),
            ],
            options={
                'db_table': 'company_sso_settings',
            },
        ),
    ]
