# Generated by Django 2.1.7 on 2019-04-01 05:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0014_auto_20190401_0407'),
    ]

    operations = [
        migrations.AlterField(
            model_name='commodity',
            name='name',
            field=models.CharField(choices=[('wheat', 'Wheat'), ('barley', 'Barley'), ('canola', 'Canola'), ('sorghum', 'Sorghum'), ('lentils', 'Lentils'), ('chickpeas_desi', 'Chickpeas (Desi)'), ('chickpeas_kabuli', 'Chickpeas (Kabuli)'), ('oats', 'Oats'), ('triticale', 'Triticale'), ('maize', 'Maize'), ('broad_beans', 'Broad Beans'), ('faba_beans', 'Faba Beans'), ('lupins', 'Lupins'), ('mungbeans', 'Mungbeans'), ('field_peas', 'Field Peas'), ('vetch', 'Vetch'), ('sunflower', 'Sunflower'), ('soybean', 'Soybean'), ('cottonseed', 'Cottonseed'), ('safflower', 'Safflower'), ('pke', 'PKE'), ('linseed', 'Linseed'), ('canola_meal', 'Canola Meal'), ('soybean_meal', 'Soybean Meal'), ('cereal_hay', 'Cereal Hay & Silage'), ('legume_hay', 'Legume and Pasture Hay & Silage')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalcommodity',
            name='name',
            field=models.CharField(choices=[('wheat', 'Wheat'), ('barley', 'Barley'), ('canola', 'Canola'), ('sorghum', 'Sorghum'), ('lentils', 'Lentils'), ('chickpeas_desi', 'Chickpeas (Desi)'), ('chickpeas_kabuli', 'Chickpeas (Kabuli)'), ('oats', 'Oats'), ('triticale', 'Triticale'), ('maize', 'Maize'), ('broad_beans', 'Broad Beans'), ('faba_beans', 'Faba Beans'), ('lupins', 'Lupins'), ('mungbeans', 'Mungbeans'), ('field_peas', 'Field Peas'), ('vetch', 'Vetch'), ('sunflower', 'Sunflower'), ('soybean', 'Soybean'), ('cottonseed', 'Cottonseed'), ('safflower', 'Safflower'), ('pke', 'PKE'), ('linseed', 'Linseed'), ('canola_meal', 'Canola Meal'), ('soybean_meal', 'Soybean Meal'), ('cereal_hay', 'Cereal Hay & Silage'), ('legume_hay', 'Legume and Pasture Hay & Silage')], max_length=100),
        ),
    ]
