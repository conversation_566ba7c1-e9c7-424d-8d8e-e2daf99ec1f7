from datetime import datetime

from pydash import get
from rest_framework import serializers
from rest_framework.fields import (SerializerMethodField, CharField, ListField, DateTimeField, IntegerField, FloatField,
                                   BooleanField)
from rest_framework.serializers import ModelSerializer

from core.commodities.serializers import CommodityMinimalSerializer, GradeMinimalSerializer
from core.company_sites.models import CompanySite, FreightSlot, SlotComment
from core.contracts.serializers import ChemicalApplicationSerializer
from core.loads.constants import ALL_MOBILE_DEVICES, SYSTEM_NAME_HEADER
from core.locations.serializers import LocationSerializer
from core.profiles.serializers import EmployeeMinimalisticSerializer
from core.timezones.utils import DateTimeUtil
from core.trucks.serializers import TruckCategorySerializer, TruckWithCompanyNameSerializer


class CompanySiteMobileSerializer(ModelSerializer):
    address = LocationSerializer()

    class Meta:
        model = CompanySite
        fields = ('id', 'company_id', 'name', 'phone', 'is_bhc', 'is_active', 'entity', 'address', )


class CompanySiteSyncManagerSerializer(ModelSerializer):
    class Meta:
        model = CompanySite
        fields = ('id', 'company_id', 'name', 'address_id', 'phone', 'is_bhc', 'is_active', 'entity', )


class FreightSlotTruckConfigSerializer(ModelSerializer):
    rego = CharField(source='truck.rego', allow_null=True, allow_blank=True)
    category = TruckCategorySerializer(allow_null=True)
    truck_default = SerializerMethodField()
    load_sharing = SerializerMethodField()

    class Meta:
        model = FreightSlot
        fields = (
            'id', 'type', 'site_id', 'truck_id', 'rego', 'category', 'steer_1_point_1', 'steer_point_5',
            'truck_default', 'category_id', 'permit_number', 'declared_mass_limit', 'accreditation_number',
            'load_sharing', 'notice', 'notice_number', 'restricted'
        )

    def get_load_sharing(self, obj):
        request = self.context['request']
        if request and request.META.get(SYSTEM_NAME_HEADER, '').lower() in ALL_MOBILE_DEVICES:
            return not obj.load_sharing
        return obj.load_sharing

    @staticmethod
    def get_truck_default(obj):
        truck = get(obj, 'truck')
        if truck:
            return {
                'category': TruckCategorySerializer(truck.category).data if truck.category_id else None,
                'steer_point_5': truck.steer_point_5,
                'steer_1_point_1': truck.steer_1_point_1
            }


class FreightSlotSerializer(ModelSerializer):
    from core.companies.serializers import CompanyNamesSerializer

    commodity = CommodityMinimalSerializer()
    grade = GradeMinimalSerializer()
    freight_provider = CompanyNamesSerializer()
    sub_freight_provider = CompanyNamesSerializer()
    truck = TruckWithCompanyNameSerializer()
    driver = EmployeeMinimalisticSerializer()
    is_provider = SerializerMethodField()
    is_sub_provider = SerializerMethodField()
    is_delivery_site = SerializerMethodField()
    delivery_order_number = SerializerMethodField()
    movement_identifier = SerializerMethodField()
    commodity_contract_id = SerializerMethodField()
    commodity_contract_number = SerializerMethodField()
    updated_at = SerializerMethodField()
    settings_updated_at = SerializerMethodField()
    hours_before_cancellation_stops = SerializerMethodField()
    restrict_slot_cancellation = SerializerMethodField()
    movement_status = SerializerMethodField()
    tonnage = FloatField(source='inferred_tonnage', allow_null=True)
    both_sites_order_booking_on = BooleanField(source='is_order_both_site_booking_on')
    variety_name = CharField(source='order.variety.name', read_only=True, allow_null=True)
    has_parent_freight_provider = SerializerMethodField()

    class Meta:
        model = FreightSlot
        fields = (
            'id', 'site_id', 'type', 'booking_number', 'delivery_order_number',
            'status', 'commodity_id', 'grade_id', 'tonnage', 'season', 'truck_id', 'driver_id',
            'customer', 'priority', 'freight_provider_id', 'provider_updated_fields',
            'truck', 'freight_provider', 'grade', 'commodity', 'has_siblings', 'driver', 'pits',
            'start', 'end', 'comments_count', 'is_urgent_for_provider',
            'sub_freight_provider', 'sub_freight_provider_id', 'is_provider', 'is_sub_provider',
            'is_delivery_site', 'order_id', 'movement_id', 'movement_identifier', 'commodity_contract_id',
            'commodity_contract_number', 'is_urgent_for_manager', 'updated_at', 'settings_updated_at', 'on_route',
            'restriction_reason', 'restricted_visible_to_carrier', 'is_trailer_slot', 'trailer_slot_ids',
            'parent_slot_id', 'hours_before_cancellation_stops', 'restrict_slot_cancellation', 'counter_slot_id',
            'excluded_commodity_ids', 'excluded_grade_ids', 'movement_status',
            'both_sites_order_booking_on',
            'variety_name',
            'parent_grade_name',
            'has_parent_freight_provider'
        )

    def get_has_parent_freight_provider(self, obj):
        provider_exists = bool(get(obj, 'order.provider_id'))
        if not provider_exists:
            return False

        is_system_company = get(obj, 'order.provider.is_system', False)
        has_parent_freight_provider = not is_system_company
        return has_parent_freight_provider

    def get_hours_before_cancellation_stops(self, obj):
        return get(obj, 'site.company.hours_before_cancellation_stops')

    def get_restrict_slot_cancellation(self, obj):
        return get(obj, 'site.company.restrict_slot_cancellation')

    def get_is_provider(self, obj):
        return obj.is_provider(self.context.get('request').user)

    def get_is_delivery_site(self, obj):
        return obj.is_delivery_site()

    def get_is_sub_provider(self, obj):
        return obj.is_sub_provider(self.context.get('request').user)

    def get_delivery_order_number(self, obj):
        return obj.order_number(self.context.get('request').user)

    def get_movement_identifier(self, obj):
        return get(obj, 'movement.identifier')

    def get_movement_status(self, obj):
        return get(obj, 'movement.status')

    def get_commodity_contract_number(self, obj):
        return get(
            obj, 'movement.commodity_contract.reference_number'
        ) or get(obj, 'order.external_reference_number')

    def get_commodity_contract_id(self, obj):
        return get(obj, 'movement.commodity_contract_id')

    def get_updated_at(self, obj):
        if updated_at := obj.updated_at:
            return datetime.timestamp(updated_at)

    @staticmethod
    def get_settings_updated_at(obj):
        if updated_at := get(obj, 'settings.updated_at'):
            return datetime.timestamp(updated_at)


class FreightSlotExternalSerializer(FreightSlotSerializer):
    is_grower_delivery = SerializerMethodField()

    class Meta:
        model = FreightSlot
        fields = FreightSlotSerializer.Meta.fields + ('is_grower_delivery',)

    def get_is_grower_delivery(self, obj):
        if obj.order_id:
            from core.freights.models import FreightContract
            stock_owner = FreightContract.get_load_stock_owner(
                load_type='inload', order=obj.order, commodity_contract=None, customer_company_id=None, only_id=False
            )
            return get(stock_owner, 'is_grower')
        return False


class FreightSlotVerboseSerializer(FreightSlotSerializer):
    planned_pickup_date = DateTimeField(source='movement.freight_pickup.date_time', allow_null=True)
    actual_pickup_date = SerializerMethodField()
    planned_delivery_date = DateTimeField(source='movement.freight_delivery.date_time', allow_null=True)
    actual_delivery_date = SerializerMethodField()
    pickup_site_timezone = IntegerField(
        source='movement.freight_pickup.consignor.handler.timezone.utcoffset_seconds', allow_null=True)
    pickup_site_timezone_abbreviation = CharField(
        source='movement.freight_pickup.consignor.handler.timezone.abbreviation', allow_null=True)
    delivery_site_timezone = IntegerField(
        source='movement.freight_delivery.consignee.handler.timezone.utcoffset_seconds', allow_null=True)
    delivery_site_timezone_abbreviation = CharField(
        source='movement.freight_delivery.consignee.handler.timezone.abbreviation', allow_null=True)
    spread = ListField(source='movement.order.spread.details', allow_null=True, allow_empty=True)
    trailer_slots = SerializerMethodField()
    is_pickup_site_user = SerializerMethodField()
    is_delivery_site_user = SerializerMethodField()
    is_slot_booking_company_user = SerializerMethodField()
    counter_slot_updated_at = SerializerMethodField()
    counter_slot_settings_updated_at = SerializerMethodField()
    counter_slot_site_id = SerializerMethodField()
    chemical_applications = SerializerMethodField()

    class Meta:
        model = FreightSlot
        fields = FreightSlotSerializer.Meta.fields + (
            'planned_pickup_date', 'planned_delivery_date', 'counter_slot_settings_updated_at',
            'pickup_site_timezone', 'delivery_site_timezone', 'counter_slot_updated_at',
            'actual_pickup_date', 'actual_delivery_date', 'is_pickup_site_user', 'is_delivery_site_user',
            'pickup_site_timezone_abbreviation', 'delivery_site_timezone_abbreviation', 'trailer_slots',
            'counter_slot_site_id', 'chemical_applications', 'spread', 'is_slot_booking_company_user'
        )

    def get_chemical_applications(self, obj):
        if obj.movement_id:
            return ChemicalApplicationSerializer(
                obj.movement.chemical_applications.filter(is_active=True), many=True
            ).data

    @staticmethod
    def get_actual_pickup_date(obj):
        if outload_slot_start := get(obj.movement, 'outload_slot.start'):
            return outload_slot_start
        return None

    @staticmethod
    def get_actual_delivery_date(obj):
        if inload_slot_start := get(obj.movement, 'inload_slot.start'):
            return inload_slot_start
        return None

    @staticmethod
    def get_trailer_slots(obj):
        trailer_slot_ids = get(obj, 'trailer_slot_ids')
        trailer_slots = []
        if trailer_slot_ids:
            for slot_id in trailer_slot_ids:
                trailer_slot = FreightSlot.objects.filter(id=slot_id).first()
                if trailer_slot:
                    trailer_slots.append({
                        'id': trailer_slot.id,
                        'start': trailer_slot.start,
                        'end': trailer_slot.end,
                        'status': trailer_slot.status,
                        'title': trailer_slot.name()
                    })
        return trailer_slots

    def get_is_pickup_site_user(self, obj):
        return obj.is_pickup_site_user(self.context.get('request').user)

    def get_is_delivery_site_user(self, obj):
        return obj.is_delivery_site_user(self.context.get('request').user)

    def get_is_slot_booking_company_user(self, obj):
        return get(obj.booked_by, 'company_id') == self.context.get('request').user.company_id

    @staticmethod
    def get_counter_slot_updated_at(obj):
        if counter_slot_id := obj.counter_slot_id:
            counter_slot = FreightSlot.objects.filter(id=counter_slot_id).first()
            if updated_at := counter_slot.updated_at:
                return datetime.timestamp(updated_at)

    @staticmethod
    def get_counter_slot_settings_updated_at(obj):
        if counter_slot_id := obj.counter_slot_id:
            counter_slot = FreightSlot.objects.filter(id=counter_slot_id).first()
            if updated_at := get(counter_slot, 'settings.updated_at'):
                return datetime.timestamp(updated_at)

    @staticmethod
    def get_counter_slot_site_id(obj):
        if counter_slot_id := obj.counter_slot_id:
            counter_slot = FreightSlot.objects.filter(id=counter_slot_id).first()
            return get(counter_slot, 'site_id')


class FreightSlotPlannedSerializer(ModelSerializer):
    from core.companies.serializers import CompanyNamesSerializer
    name = SerializerMethodField()
    start_time = SerializerMethodField()
    date_time_display_text = SerializerMethodField()
    sub_freight_provider = CompanyNamesSerializer()
    driver = EmployeeMinimalisticSerializer()
    updated_at = SerializerMethodField()

    class Meta:
        model = FreightSlot
        fields = (
            'id', 'type', 'name', 'commodity_id', 'grade_id', 'start_time', 'start', 'end', 'date_time_display_text',
            'status', 'sub_freight_provider_id', 'driver_id', 'sub_freight_provider', 'driver',
            'excluded_commodity_ids', 'excluded_grade_ids', 'updated_at'
        )

    @staticmethod
    def get_updated_at(obj):
        updated_at = obj.updated_at
        if updated_at:
            return datetime.timestamp(updated_at)

    def get_name(self, obj):
        return obj.time_display_text(
            self.context.get('request').META.get('HTTP_REFERER_TZ', None))

    def get_date_time_display_text(self, obj):
        return obj.date_time_display_text(
            self.context.get('request').META.get('HTTP_REFERER_TZ', None))

    def get_start_time(self, obj):
        return DateTimeUtil.localize_time(
            obj.start, self.context.get('request').META.get('HTTP_REFERER_TZ', obj.timezone), "%H:%M:%S")


class SlotCommentSerializer(ModelSerializer):
    creator_first_name = SerializerMethodField()
    creator_last_name = SerializerMethodField()
    creator_company = SerializerMethodField()

    class Meta:
        model = SlotComment
        fields = (
            'id', 'comment', 'slot_id', 'creator_first_name', 'creator_last_name',
            'creator_company', 'created_at', 'archived',
        )

    @staticmethod
    def get_creator_first_name(obj):
        return obj.created_by.first_name

    @staticmethod
    def get_creator_last_name(obj):
        return obj.created_by.last_name

    @staticmethod
    def get_creator_company(obj):
        return obj.created_by.company.name


class SlotHistorySerializer(serializers.Serializer):  # pylint: disable=abstract-method
    by = CharField(read_only=True)
    heading = CharField(read_only=True)
    items = ListField(read_only=True, allow_null=True, allow_empty=True)
