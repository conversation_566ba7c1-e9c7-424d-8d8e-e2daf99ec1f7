# Generated by Django 4.1.13 on 2024-04-24 04:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('loads', '0144_remove_historicalload_chemical_quantity_and_more'),
        ('contracts', '0203_chemicalapplication_application_fee_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='chemicalapplication',
            name='load',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='chemical_applications', to='loads.load'),
        ),
        migrations.AddField(
            model_name='historicalchemicalapplication',
            name='load',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='loads.load'),
        ),
    ]
