from django.test import tag
from django.urls import reverse
from mock import patch, <PERSON><PERSON>, ANY

from core.audit_history.constants import CONTRACT_MODEL
from core.audit_history.models import AuditHistory
from core.audit_history.tests.factories import AuditHistoryFactory
from core.common.tests import ACTestCase, AuthSetup
from core.common.utils import get_content_type_id
from core.contracts.tests.factories import ContractFactory, ContractAcceptanceRequestFactory
from core.freights.tests.factories import FreightOrderFactory, FreightMovementFactory


@tag('model')
class AuditHistoryTest(ACTestCase):
    def test_actor(self):
        self.assertEqual(
            AuditHistory(created_by_id=1).actor,
            'Admin Root (SR000) (AgriChain)'
        )


@tag('view')
class ContractAuditHistoryViewTest(AuthSetup):
    def test_401_unauthorized(self):
        url = reverse('core.audit_history:audit_history-contract', kwargs={'contract_id': 1})
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token test', format='json')
        self.assertEqual(response.status_code, 401)

    def test_200_ok_empty(self):
        url = reverse('core.audit_history:audit_history-contract', kwargs={'contract_id': 1000})
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, [])

    @patch('core.audit_history.views.Contract')
    @patch('core.audit_history.views.AuditHistory')
    def test_200_ok_empty_when_user_cannot_view(self, mock_audit_history, contract_mock):
        audit_history1 = AuditHistory(
            id=1, description='Test 123', created_by_id=1, created_at='2019-01-02T08:43:24.914605Z'
        )
        audit_history2 = AuditHistory(
            id=2, description='Test 13', created_by_id=1, created_at='2019-02-02T08:43:24.914605Z'
        )

        manager_mock = Mock()
        select_related_mock = Mock()

        mocked_contract = Mock()
        mocked_contract.can_view_vendor_dec.return_value = False
        mocked_contract.buyer.company_ids = [self.employee.company_id]
        contract_mock.objects.filter.return_value.first.return_value = mocked_contract

        select_related_mock.filter = Mock(return_value=[audit_history1, audit_history2])
        manager_mock.select_related = Mock(return_value=select_related_mock)
        mock_audit_history.objects = manager_mock

        url = reverse('core.audit_history:audit_history-contract', kwargs={'contract_id': 1})
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, [])

    # @patch('core.audit_history.views.AuditHistory')
    def test_200_ok(self):
        contract = ContractFactory(id=1)
        audit_history1 = AuditHistory(
            id=1, description='Test 123', created_by_id=1, created_at='2019-01-02T08:43:24.914605Z', instance=contract
        )
        audit_history1.save()
        audit_history2 = AuditHistory(
            id=2, description='Test 13', created_by_id=1, created_at='2019-02-02T08:43:24.914605Z', instance=contract
        )
        audit_history2.save()

        # manager_mock = Mock()
        # select_related_mock = Mock()

        # select_related_mock.filter = Mock(return_value=[audit_history1, audit_history2])
        # manager_mock.select_related = Mock(return_value=select_related_mock)
        # mock_audit_history.objects = manager_mock

        url = reverse('core.audit_history:audit_history-contract', kwargs={'contract_id': 1})
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')

        self.assertEqual(response.status_code, 200)

        sorted_response = sorted(response.data, key=lambda x: x['id'])
        expected_response =  [
            {
                'id': -2, 'description': 'Test 13', 'actor': 'Admin Root (SR000) (AgriChain)',
                'createdAt': '2019-02-02T08:43:24.914605Z',
                'amendedDetails': {},
                'args': None,
                'createdBy': {
                    'firstName': 'Admin',
                    'lastName': 'Root'
                },
                'communication': None,
                'entity': {'entityId': 1, 'entityType': 'contract'},
            },
            {
                'id': -1, 'description': 'Test 123', 'actor': 'Admin Root (SR000) (AgriChain)',
                'createdAt': '2019-01-02T08:43:24.914605Z',
                'amendedDetails': {},
                'args': None,
                'createdBy': {
                    'firstName': 'Admin',
                    'lastName': 'Root'
                },
                'communication': None,
                'entity': {'entityId': 1, 'entityType': 'contract'},
            },
        ]
        expected_response = sorted(response.data, key=lambda x: x['id'])
        self.assertEqual(
            sorted_response,
            expected_response
        )

    @patch('core.contracts.models.Contract.can_view_vendor_dec')
    def test_200_ok_with_amended_details(self, can_view_vendor_dec_mock):
        contract = ContractFactory()
        contract.save()

        contract_acceptance_request = ContractAcceptanceRequestFactory(contract=contract, args={"buyer": {"ngr_id": 8}})
        contract_acceptance_request.save()

        contract_content_type_id = get_content_type_id(CONTRACT_MODEL)
        audit_history1 = AuditHistoryFactory(instance_id=contract.id, instance_type_id=contract_content_type_id,
                                             args={'contract_acceptance_request_id': contract_acceptance_request.id})
        audit_history1.save()
        can_view_vendor_dec_mock = Mock(return_value=True)
        contract.can_view_vendor_dec = can_view_vendor_dec_mock

        url = reverse('core.audit_history:audit_history-contract', kwargs={'contract_id': contract.id})
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')

        self.assertEqual(response.status_code, 200)

        audit_history_details = response.data[0]
        self.assertEqual(
            audit_history_details['amendedDetails'],
            {
                'amended': {
                    'buyer': {
                        'entityName': ANY,
                        'isSharedNgr': ANY,
                        'ngrId': None,
                    }
                }
            }
        )
        self.assertEqual(audit_history_details['description'], 'Contract amended')


@tag('view')
class OrderAuditHistoryViewTest(AuthSetup):
    @patch('core.audit_history.views.FreightOrder.can_view_vendor_dec')
    def test_200_ok_empty_when_user_cannot_view(self, can_view_vendor_dec_mock):
        order = FreightOrderFactory(id=1)
        audit_history1 = AuditHistory(
            id=1, description='Test 123', created_by_id=1, created_at='2019-01-02T08:43:24.914605Z', instance=order
        )
        audit_history1.save()


        can_view_vendor_dec_mock.return_value = True

        url = reverse('core.audit_history:audit_history_order', kwargs={'order_id': 1})
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.data,
            [
                {
                    'id': -1, 'description': 'Test 123', 'actor': 'Admin Root (SR000) (AgriChain)',
                    'createdAt': '2019-01-02T08:43:24.914605Z',
                    'amendedDetails': {},
                    'args': None,
                    'createdBy': {
                        'firstName': 'Admin',
                        'lastName': 'Root'
                    },
                    'communication': None,
                    'entity': {'entityId': 1, 'entityType': 'freightorder'},
                }
            ]
        )

        can_view_vendor_dec_mock.return_value = False
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, [])

    def test_200_ok(self):
        order = FreightOrderFactory(id=1)
        audit_history1 = AuditHistory(
            id=1, description='Test 123', created_by_id=1, created_at='2019-01-02T08:43:24.914605Z',
            instance=order
        )
        audit_history1.save()

        url = reverse('core.audit_history:audit_history_order', kwargs={'order_id': 1})
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')

        self.assertEqual(response.status_code, 200)

        self.assertEqual(
            response.data,
            [
                {
                    'id': -1, 'description': 'Test 123', 'actor': 'Admin Root (SR000) (AgriChain)',
                    'createdAt': '2019-01-02T08:43:24.914605Z',
                    'amendedDetails': {},
                    'args': None,
                    'createdBy': {
                        'firstName': 'Admin',
                        'lastName': 'Root'
                    },
                    'communication': None,
                    'entity': {'entityId': 1, 'entityType': 'freightorder'},
                }
            ]
        )


@tag('view')
class MovementAuditHistoryViewTest(AuthSetup):
    @patch('core.audit_history.views.FreightContract.can_view_vendor_dec')
    def test_200_ok_empty_when_user_cannot_view(self, can_view_vendor_dec_mock):
        movement = FreightMovementFactory(id=1, commodity_contract=None, freight_pickup=None, freight_delivery=None)
        audit_history1 = AuditHistory(
            id=1, description='Test 123', created_by_id=1, created_at='2019-01-02T08:43:24.914605Z',
            instance=movement
        )
        audit_history1.save()
        audit_history2 = AuditHistory(
            id=2, description='Test 13', created_by_id=1, created_at='2019-02-02T08:43:24.914605Z',
            instance=movement
        )
        audit_history2.save()

        url = reverse('core.audit_history:audit_history_movement', kwargs={'movement_id': 1})

        can_view_vendor_dec_mock.return_value = True
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            sorted(response.data, key=lambda data: data['id']),
            sorted([
                {
                    'id': -1, 'description': 'Test 123', 'actor': 'Admin Root (SR000) (AgriChain)',
                    'createdAt': '2019-01-02T08:43:24.914605Z',
                    'amendedDetails': {},
                    'args': None,
                    'createdBy': {
                        'firstName': 'Admin',
                        'lastName': 'Root'
                    },
                    'communication': None,
                    'entity': {'entityId': 1, 'entityType': 'freightcontract'},
                },
                {
                    'id': -2, 'description': 'Test 13', 'actor': 'Admin Root (SR000) (AgriChain)',
                    'createdAt': '2019-02-02T08:43:24.914605Z',
                    'amendedDetails': {},
                    'args': None,
                    'createdBy': {
                        'firstName': 'Admin',
                        'lastName': 'Root'
                    },
                    'communication': None,
                    'entity': {'entityId': 1, 'entityType': 'freightcontract'},
                }
            ], key=lambda data: data['id'])
        )

        can_view_vendor_dec_mock.return_value = False
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, [])

    def test_200_ok(self):
        movement = FreightMovementFactory(id=1, commodity_contract=None, freight_pickup=None, freight_delivery=None)
        audit_history1 = AuditHistory(
            id=1, description='Test 123', created_by_id=1, created_at='2019-01-02T08:43:24.914605Z',
            instance=movement
        )
        audit_history1.save()
        audit_history2 = AuditHistory(
            id=2, description='Test 13', created_by_id=1, created_at='2019-02-02T08:43:24.914605Z',
            instance=movement
        )
        audit_history2.save()


        url = reverse('core.audit_history:audit_history_movement', kwargs={'movement_id': 1})
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')

        self.assertEqual(response.status_code, 200)

        self.assertEqual(
            sorted(response.data, key=lambda data: data['id']),
            sorted([
                {
                    'id': -1, 'description': 'Test 123', 'actor': 'Admin Root (SR000) (AgriChain)',
                    'createdAt': '2019-01-02T08:43:24.914605Z',
                    'amendedDetails': {},
                    'args': None,
                    'createdBy': {
                        'firstName': 'Admin',
                        'lastName': 'Root'
                    },
                    'communication': None,
                    'entity': {'entityId': 1, 'entityType': 'freightcontract'},
                },
                {
                    'id': -2, 'description': 'Test 13', 'actor': 'Admin Root (SR000) (AgriChain)',
                    'createdAt': '2019-02-02T08:43:24.914605Z',
                    'amendedDetails': {},
                    'args': None,
                    'createdBy': {
                        'firstName': 'Admin',
                        'lastName': 'Root'
                    },
                    'communication': None,
                    'entity': {'entityId': 1, 'entityType': 'freightcontract'},
                }
            ], key=lambda data: data['id'])
        )


@tag('view')
class InvoiceAuditHistoryViewTest(AuthSetup):
    @patch('core.audit_history.views.AuditHistory')
    def test_200_ok(self, mock_audit_history):
        audit_history1 = AuditHistory(
            id=1, description='Test 123', created_by_id=1, created_at='2019-01-02T08:43:24.914605Z'
        )

        manager_mock = Mock()
        select_related_mock = Mock()

        select_related_mock.filter = Mock(return_value=[audit_history1])
        manager_mock.select_related = Mock(return_value=select_related_mock)
        mock_audit_history.objects = manager_mock

        url = reverse('core.audit_history:audit_history_invoice', kwargs={'invoice_id': 1})
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')

        self.assertEqual(response.status_code, 200)

        self.assertEqual(
            response.data,
            [
                {
                    'id': -1, 'description': 'Test 123', 'actor': 'Admin Root (SR000) (AgriChain)',
                    'createdAt': '2019-01-02T08:43:24.914605Z',
                    'amendedDetails': {},
                    'args': None,
                    'createdBy': {
                        'firstName': 'Admin',
                        'lastName': 'Root'
                    },
                    'communication': None,
                    'entity': {'entityId': None, 'entityType': None},
                }
            ]
        )


@tag('view')
class VendorDecAuditHistoryViewTest(AuthSetup):
    @patch('core.audit_history.views.AuditHistory')
    def test_post_200_vendor_dec(self, audit_history_mock):
        audit_history1 = AuditHistory(
            id=1, description='Test 123', created_by_id=1, created_at='2019-01-02T08:43:24.914605Z'
        )

        manager_mock = Mock()
        select_related_mock = Mock()

        select_related_mock.filter = Mock(return_value=[audit_history1])
        manager_mock.select_related = Mock(return_value=select_related_mock)
        audit_history_mock.objects = manager_mock


        url = reverse('core.audit_history:audit_history_vendor_dec', kwargs={'vendor_dec_id': 1})
        response = self.client.get(path=url, HTTP_AUTHORIZATION='Token ' + self.token, format='json')

        self.assertEqual(response.status_code, 200)

        self.assertEqual(
            response.data,
            [
                {
                    'id': -1, 'description': 'Test 123', 'actor': 'Admin Root (SR000) (AgriChain)',
                    'createdAt': '2019-01-02T08:43:24.914605Z',
                    'amendedDetails': {},
                    'args': None,
                    'createdBy': {
                        'firstName': 'Admin',
                        'lastName': 'Root'
                    },
                    'communication': None,
                    'entity': {'entityId': None, 'entityType': None},
                }
            ]
        )
