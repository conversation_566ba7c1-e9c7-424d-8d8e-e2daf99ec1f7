import inflection
from django.conf import settings
from django.db import transaction, models, IntegrityError
from django.db.models import Prefetch, Count, Q
from django.db.models.functions import TruncMonth
from django.http import Http404
from django.http.request import QueryDict
from django.shortcuts import get_object_or_404
from django.utils import timezone
from pydash import get, compact, flatten
from rest_framework import status
from rest_framework.exceptions import PermissionDenied
from rest_framework.generics import ListAPIView, RetrieveAPIView, RetrieveUpdateDestroyAPIView, DestroyAPIView
from rest_framework.permissions import AllowAny, IsAdminUser
from rest_framework.response import Response
from rest_framework.views import APIView

from core.cash_board.models import CashPrices
from core.cash_board.serializers import CashPricesSerializer
from core.common.constants import (SYSTEM_TYPE_ID, OBSERVER_TYPE_ID, COMPANY_ADMIN_TYPE_ID, B<PERSON>_TYPE_ID,
                                   SYSTEM_COMPANY_IDS, INLOAD, EMPTY_VALUE, G<PERSON><PERSON>ER_TYPE_ID, UNKNOWN)
from core.common.exceptions import Http403
from core.common.models import CustomPageNumberPagination, CommonFilters
from core.common.permissions import (IsSystemCompany, IsNotProduction, IsSuperUser, IsSystemCompanyOrCompanyAdmin,
                                     IsSuperUserOrCompanyAdmin, IsStaffOrCompanyUser)
from core.common.utils import (get_business_name_filter, get_model_class_from_name, decode_string,
                               get_request_country_id)
from core.common.utils import to_query_filters, assign_key_contact, generate_random_password
from core.common.views.web_views import AbstractEntityStatsView, BaseHistoryView
from core.companies.constants import (COMPANY_CLAIM_SLACK_MESSAGE, NEW_UNREG_COMPANY_SLACK_MESSAGE,
                                      OLD_UNREG_COMPANY_SLACK_MESSAGE, SIMILARITY_SEARCH_SCORE)
from core.companies.models import (Company, CompanyType, SSOSetting, XeroConnection, ImpexDocsConnection,
                                   XeroMapping, ExternalPortal, XeroTrackingCategory, ApplicationRate,
                                   ImpexDocsProductMapping)
from core.companies.serializers import (
    CompanySerializer, CompanyMinimalisticSerializer, CompanyNamesSerializer, CompanyGlobalSerializer,
    CompanySiteLoadsSerializer, CompanyApprovedBuyersSerializer, CompanyNameWithABNSerializer, SSOSettingSerializer,
    XeroMappingSerializer, ExternalPortalSerializer, CompanyGroupsSerializer, CompanyNameWithGroupNameSerializer,
    ApplicationRateSerializer, CompanyEmployeeNamesSerializer, CompanyLookupSerializer,
    ImpexDocsProductMappingSerializer
)
from core.company_sites.models import CompanySite, SiteManagementSettings
from core.contract_bids.models import ContractBid
from core.contract_bids.serializers import ContractBidSerializer
from core.countries.models import Country
from core.farms.models import Farm
from core.farms.serializers import FarmSerializer, FarmNameSerializer
from core.freights.constants import VOID_STATUS
from core.freights.serializers import FreightOrderMinimalWithCustomerProviderSerializer
from core.jobs.models import Job
from core.key_contacts.models import KeyContact
from core.loads.models import Load
from core.locations.models import Location
from core.ngrs.models import Ngr, NgrPortalCredential
from core.ngrs.serializers import NgrListingSerializer, NGRPortalCredentialsSerializer, NGRServiceResponseSerializer
from core.ngrs.serializers import NgrSerializerWithShareHolderNames
from core.profiles.models import Employee, Download
from core.profiles.serializers import EmployeeMinimalisticSerializer, EmployeeAllFieldsSerializer
from core.services.external.google import GoogleMaps
from core.services.external.ngr import NGRLookUp
from core.services.internal.ngr import NGRService
from core.services.internal.slack import Slack
from core.stocks.models import Stock
from core.timezones.utils import DateTimeUtil
from core.trucks.constants import FLEET_REGO
from core.trucks.models import Truck
from core.trucks.serializers import TruckMinimalSerializer, TruckWebListingSerializer, EmployeeTruckMinimalSerializer

WEB_SEARCH_URL = "companies/{company_id}/companies/web/search"
FARM_WEB_SEARCH_URL = "companies/{company_id}/farms/web/search"


class CompanyTypesView(APIView):
    def get(self, _):
        return Response(
            CompanyType.qs2dict(
                CompanyType.objects.exclude(name='system').all().only('id', 'name')
            ),
            status=status.HTTP_200_OK
        )


class CompaniesNameView(ListAPIView):
    permission_classes = (AllowAny,)
    serializer_class = CompanyNamesSerializer
    def get_queryset(self):
        return Company.objects.only('business_name', 'type_id', 'transaction_participation')


class GroupCompaniesView(ListAPIView):
    """Returns all companies that are part of a group"""

    serializer_class = CompanySerializer
    pagination_class = CustomPageNumberPagination
    def get_object(self):
        return get_object_or_404(Company.objects.filter(id=self.kwargs.get('company_id')))

    def get_queryset(self):
        group_id = self.kwargs.get('group_id')
        group = self.get_object().owned_groups.filter(id=group_id).first()
        if group:
            companies = Company.directory_companies_only(self.kwargs.get('company_id')).filter(groups=group)
            common_filters = CommonFilters(self.request.user)
            filter_params = common_filters.conditional_filtering(key='company_filters')
            companies = companies.filter(filter_params)
            try:
                return companies.order_by('business_name')
            except Exception as e: # pylint: disable=broad-except
                return Response({'errors': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({'errors': 'Group does not exist'}, status=status.HTTP_400_BAD_REQUEST)


class GroupCompaniesMinimalisticView(GroupCompaniesView, APIView):
    serializer_class = CompanyNameWithABNSerializer
    def get(self, request, *args, **kwargs):
        queryset = super().get_queryset()
        serializer = self.serializer_class(queryset, many=True, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)


class CompanyGroupsView(ListAPIView, APIView):
    serializer_class = CompanyGroupsSerializer
    def get_object(self):
        return get_object_or_404(Company.objects.filter(id=self.kwargs.get('company_id')))

    def get_queryset(self):
        return self.get_object().owned_groups.filter()

    def post(self, request, company_id):  # pylint: disable=too-many-locals, too-many-branches, unused-argument
        # Removes companies from the group
        # Rename the selected group
        # Creates a new group,
        # Adds companies to the group
        from core.companies.models import CompanyGroup
        group_name = request.data.get('group_name', None)
        group_type = request.data.get('group_type', None)
        group_id = request.data.get('group_id', None)
        business_type_id = request.data.get('business_type_id', None)
        company_owned_groups = self.get_queryset()
        remove_companies = self.request.query_params.get('remove_companies', None)
        company_ids = request.data.get('company_ids', None)
        companies = []
        if remove_companies and company_ids:
            for group in company_owned_groups:
                group.companies.remove(*company_ids)
        elif group_id:
            company_owned_groups.filter(
                id=group_id
            ).update(name=group_name, type=group_type, updated_at=timezone.now(), updated_by=request.user)
        else:
            group = company_owned_groups.filter(name=group_name, type=group_type).first()
            if not group:
                group = CompanyGroup.create_group(request.user, group_name, group_type, business_type_id)
                if not group.persisted:
                    return Response({'errors': group.errors}, status=status.HTTP_400_BAD_REQUEST)
                if business_type_id:
                    group.add_directory_companies_of_business_type()
            if company_ids:
                companies = group.add_companies(company_ids)

        if companies:
            serializer = CompanyNameWithGroupNameSerializer(companies, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

        return Response(status=status.HTTP_204_NO_CONTENT)


class CompaniesDirectoryView(ListAPIView):
    serializer_class = CompanyNameWithABNSerializer
    def get_queryset(self):
        user_company_id = get(self.request, 'user.company_id')
        include_self = self.request.query_params.get('include_self', None) in ['true', 'True']
        country = Country.get_requesting_country()
        queryset = Company.directory_companies_only(
            company_id=user_company_id,
            include_self=include_self
        )
        search_str = self.request.query_params.get('search', None)
        company_id = self.request.query_params.get('company_id', None)
        if company_id:
            if not queryset.filter(id=company_id).exists():
                Company.add_companies_to_directory(user_company_id, [company_id])
                queryset = Company.directory_companies_only(company_id=user_company_id, include_self=include_self)
            queryset = queryset.filter(id=company_id)
            if not queryset and int(company_id) in SYSTEM_COMPANY_IDS:
                queryset = Company.moderators.filter(id=country.system_company_id)
        if search_str:
            search_str = decode_string(search_str)
            queryset = queryset.filter(
                models.Q(business_name__icontains=search_str)
            )
        if search_str and search_str.lower() in UNKNOWN.lower():
            queryset = queryset | Company.moderators.filter(id=country.system_company_id)
        return queryset


class AddCompaniesToDirectoryView(APIView):
    def post(self, request, company_id):
        company = Company.objects.filter(id=company_id).first()
        company_ids = request.data.get('company_ids') or []
        abns = request.data.get('abns') or []
        ngr_details = request.data.get('ngr_details') or []
        ngr_number = request.data.get('ngr_number', None)
        ngr = ngr_number and Ngr.objects.filter(ngr_number__iexact=ngr_number).first()

        if not company:
            return Response(status=status.HTTP_404_NOT_FOUND)
        for abn in abns:
            abn_company = Company.objects.filter(abn=abn).first()
            if abn_company:
                company_ids.append(abn_company.id)
            else:
                abn_company = Company.create_by_abn(abn, request.user)
            if ngr_number and not ngr:
                ngr = Ngr.find_or_transfer_or_create(ngr_number, abn_company.id, create=True,
                                                     ngr_type='shared', user=request.user)
        if len(ngr_details) > 1:
            if ngr.ngr_type == 'single':
                ngr.ngr_type = 'shared'
                ngr.save()
            ngr.update_shared_ngr(company, ngr_details)

        if company_ids:
            Company.add_companies_to_directory(company.id, company_ids)

        return Response(status=status.HTTP_200_OK)


class ManagedCompaniesView(ListAPIView):
    serializer_class = CompanyNamesSerializer
    def get_queryset(self):
        user = self.request.user
        return Company.objects.filter(id__in=[*user.company.managed_farms_company_ids(), user.company_id])


class GlobalCompaniesView(APIView):
    def get(self, request, search_str):
        companies = request.user.directory_companies_queryset_with_search(
            decode_string(search_str)).order_by('business_name')[:10]

        data = CompanyGlobalSerializer(companies, many=True).data
        return Response({'companies': data}, status=status.HTTP_200_OK)


class CompaniesView(APIView):
    def get(self, request):  # pylint: disable=too-many-locals
        query_params = dict(request.query_params)  # returns every value in list
        prefetch_related = [v for v in query_params.pop('prefetch_related', [''])[0].split(',') if v]
        properties = [v for v in query_params.pop('properties', [''])[0].split(',') if v]
        assets = [v for v in query_params.pop('assets', [''])[0].split(',') if v != '']
        exclusions = [int(v) for v in query_params.pop('exclude', [''])[0].split(',') if v != '']
        updated_at = DateTimeUtil.get_updated_at_filter(query_params.pop('from', None))
        business_name = get_business_name_filter(query_params.pop('business_name', None))
        ngr_number = query_params.pop('ngr_number', None)

        one_to_many_fields_with_relations = {}
        for field in query_params.pop('one_to_many_fields_with_relations', [''])[0].split(','):
            if field:
                indirect_relation = field.split('__')[0]
                direct_relation = '__'.join(field.split('__')[1:])
                if indirect_relation in one_to_many_fields_with_relations:
                    one_to_many_fields_with_relations[indirect_relation]['relations'].append(direct_relation)
                else:
                    one_to_many_fields_with_relations[indirect_relation] = {'relations': [direct_relation]}

        filter_params = {
            **to_query_filters({k: v for (k, v) in query_params.items() if not k.endswith('relations')}),
            **{asset + '__isnull': False for asset in assets}, **updated_at, **business_name
        }
        relations = {k: v[0].split(',') for (k, v) in query_params.items() if k.endswith('relations')}
        queryset = Company.objects.filter(**filter_params)
        ngr = None
        is_system_ngr = False

        if ngr_number:
            ngr = Ngr.objects.filter(ngr_number__iexact=ngr_number[0].strip()).first()
            company_ids = ngr.get_owner_company_ids() if ngr else []
            is_system_ngr = any(id in SYSTEM_COMPANY_IDS for id in company_ids)
            queryset = queryset.filter(id__in=company_ids)
        if prefetch_related:
            queryset = queryset.prefetch_related(*prefetch_related)
        if exclusions:
            queryset = queryset.exclude(id__in=exclusions)
        queryset = queryset.prefetch_related(*Company.common_many_to_one_relations).distinct('id')

        companies = Company.qs2dict(
            queryset=queryset,
            properties=properties,
            many_to_one_relations=Company.common_many_to_one_relations,
            one_to_many_fields_with_relations=one_to_many_fields_with_relations or None,
            **relations
        )
        if is_system_ngr:
            return Response({'companies': companies, 'system_ngr_id': ngr.id}, status=status.HTTP_200_OK)
        return Response(companies, status=status.HTTP_200_OK)

    def post(self, request):
        try:
            company_params = request.data.get('company')
            company = Company.moderators.filter(abn=request.data.get('abn', None)).first()
            if company_params and company_params.get('type_id') == SYSTEM_TYPE_ID and not (
                    getattr(request.user, 'company', None) and request.user.company.is_agrichain
            ) or (company and company.is_system):
                return Response(
                    {'alert': 'One Does Not Simply Walk into Mordor!'},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            abn = request.data.get('abn', None)
            business_name = request.data.get('business_name', None)
            filters = {'business_name': business_name} if abn == EMPTY_VALUE else {'abn': abn}

            data = request.data
            if Company.objects.filter(**filters).exists():
                data.pop('created_by_id', None)
                company = Company.update_with_location(
                    instance_id=Company.objects.get(**filters).id,
                    params=data,
                    user=request.user,
                )
                is_created = False
            else:
                company = Company.register({**data, 'owner_company_id': request.user.company_id})
                is_created = True

            if company.persisted:
                if is_created:
                    company.log_new_company()
                request.user.company.add_company_to_directory(company.id)
                request.user.company.add_company_to_respective_business_type_group(company)
                # Adding Company into the system directory
                Company.add_company_to_super_directory(company.id)
                return Response(
                    company.to_dict(
                        many_to_one_relations=['type'],
                        one_to_one_relations=['address'],
                        properties=['is_independent_farmer', 'independent_farm_id'],
                    ),
                    status=status.HTTP_201_CREATED if is_created else status.HTTP_200_OK
                )

            return Response(company.to_dict(), status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError as ex:
            exception_arg = get(ex, 'args[0]', '')
            if 'duplicate' in exception_arg and 'comp_name_country_uniq' in exception_arg:
                return Response({'errors': {'business_name': ['Company with this business name already exists']}},
                                status=status.HTTP_400_BAD_REQUEST)
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class CompanyCompaniesWebView(ListAPIView):
    serializer_class = CompanySerializer
    pagination_class = CustomPageNumberPagination
    def get_queryset(self):
        query_params = self.request.query_params.dict()
        query_params.pop('order_by', None)
        query_params.pop('order', None)
        query_params.pop('search', None)
        is_groups = query_params.pop('is_groups', None)
        if self.request.user.is_staff:
            queryset = Company.objects.select_related('address', 'type')
            queryset = queryset.filter(country_id=get_request_country_id())
        else:
            queryset = Company.directory_companies(self.kwargs.get('company_id'))
        if is_groups == 'true':
            ungrouped = self.request.query_params.get('ungrouped', None)  #companies not part of any group
            if ungrouped == 'true':
                queryset = queryset.exclude(groups__owner_company_id=self.request.user.company_id)
            common_filters = CommonFilters(self.request.user)
            filter_params = common_filters.conditional_filtering(key='company_filters')
            queryset = queryset.filter(filter_params)

        if self.request.user.is_staff:
            queryset = queryset.select_related('created_by__company')

        return queryset.order_by('business_name').exclude(id__in=SYSTEM_COMPANY_IDS)


class CompanyCompaniesWebMinimalisticView(CompanyCompaniesWebView, APIView):
    serializer_class = CompanyNameWithABNSerializer
    def get(self, request, *args, **kwargs):
        queryset = super().get_queryset()
        serializer = self.serializer_class(queryset, many=True, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)


class CompanyCompaniesSearchView(ListAPIView):
    serializer_class = CompanySerializer
    pagination_class = CustomPageNumberPagination
    def get_queryset(self):
        request = self.request
        company_id = self.kwargs['company_id']
        params = request.query_params.dict().copy()
        search_str = params.pop('search', None)
        order_by = params.pop('order_by', None)
        order = params.pop('order', None)
        is_groups = params.pop('is_groups', None)
        ungrouped = params.pop('ungrouped', None)
        params.pop('page', None)
        params.pop('page_size', None)

        if self.request.user.is_staff:
            queryset = Company.objects.select_related('address', 'type')
            queryset = queryset.filter(country_id=get_request_country_id())
        else:
            queryset = Company.directory_companies(company_id)
        if is_groups == 'true':
            common_filters = CommonFilters(self.request.user)
            filter_params = common_filters.conditional_filtering(key='company_filters')
            queryset = queryset.filter(filter_params)
            if ungrouped == 'true':
                queryset = queryset.exclude(groups__owner_company_id=request.user.company_id)
        if search_str:
            search_str = decode_string(search_str)
            queryset = Company.similarity_search(
                queryset, search_str, ['business_name', 'entity_name'], ['extras__alias']
            )
            queryset = queryset.filter(
                models.Q(business_name__icontains=search_str) |
                models.Q(entity_name__icontains=search_str) |
                models.Q(abn__icontains=search_str) |
                models.Q(mobile__icontains=search_str) |
                models.Q(address__name__icontains=search_str) |
                models.Q(address__address__icontains=search_str) |
                models.Q(type__name__icontains=search_str) |
                models.Q(website__icontains=search_str) |
                models.Q(extras__alias__icontains=search_str) |
                models.Q(business_name_similarity__gt=SIMILARITY_SEARCH_SCORE) |
                models.Q(entity_name_similarity__gt=SIMILARITY_SEARCH_SCORE)
            )
        if self.request.user.is_staff:
            queryset = queryset.select_related('created_by__company')

        order_by_map = {
            'name': [
                'exact_match_score', 'contains', 'business_name_similarity', 'entity_name_similarity'
            ] if search_str else ['business_name'],
            'abn': ['abn'],
            'id': ['id'],
            'mobile': ['mobile'],
            'website': ['website'],
            'address': ['address__name', 'address__address'],
            'business_type': ['type__name'],
            'created_at': ['created_at'],
        }
        if not order_by:
            order_by = 'name'

        _order_by = order_by_map.get(inflection.underscore(order_by), None) or order_by_map['name']
        if order_by == 'name' and search_str:  # similarity indexed results are ordered in terms of similarity value
            order = '' if order == 'desc' else '-'
        else:
            order = '-' if order == 'desc' else ''

        return queryset.order_by(*[order + f for f in _order_by])


class GroupCompaniesSearchView(CompanyCompaniesSearchView):
    serializer_class = CompanySerializer
    pagination_class = CustomPageNumberPagination

    def get_queryset(self):
        group_id = self.kwargs['group_id']
        queryset = super().get_queryset()
        queryset = queryset.filter(groups=group_id)
        common_filters = CommonFilters(self.request.user)
        filter_params = common_filters.conditional_filtering(key='company_filters')
        queryset = queryset.filter(filter_params)
        return queryset


class CompaniesMinimalisticView(ListAPIView):
    serializer_class = CompanyMinimalisticSerializer
    def get_queryset(self):
        query_params = dict(self.request.query_params)
        query_params.pop('with_ranking', None)
        query_params.pop('rank_limit', None)
        query_params.pop('search', None)
        query_params.pop('company_id', None)
        query_params.pop('excludeGroups', None)

        params = self.request.query_params.dict()
        search_str = params.pop('search', None)
        selected_company_id = params.pop('company_id', None)
        if selected_company_id:
            return Company.objects.filter(id=selected_company_id)

        company = self.request.user.company
        with_ranking = not company.is_system and params.pop('with_ranking', None) in ['true', True, 'True']
        rank_limit = params.pop('rank_limit', None) or None

        assets = [
            v for v in query_params.pop('assets', [''])[0].split(',') if v
        ]
        exclusion_types = [
            int(v) for v in query_params.pop('exclude_types', [''])[0].split(',') if v
        ]
        filter_params = {
            **to_query_filters(
                {
                    k: v for (k, v) in query_params.items() if not k.endswith('relations')
                }
            ),
            **{
                asset + '__isnull': False for asset in assets
            },
        }
        queryset = Company.objects.filter(**filter_params).filter(country_id=get_request_country_id())
        if exclusion_types:
            queryset = queryset.exclude(type_id__in=exclusion_types)

        if search_str:
            search_str = decode_string(search_str)
            queryset = Company.get_similarity_queryset(queryset, search_str)

        if with_ranking:
            queryset = company.assign_ranking(
                queryset=queryset,
                limit=rank_limit,
                order_by_fields=[
                    '-exact_match_score', '-contains', '-business_name_similarity', '-entity_name_similarity'
                ] if search_str else []
            )
        elif rank_limit:
            queryset = queryset.order_by('business_name', 'entity_name')[:int(rank_limit)]


        queryset = queryset.select_related('platformfeatures').prefetch_related('external_booking_connections')

        return queryset


class AllCompaniesMinimalisticView(ListAPIView):
    serializer_class = CompanyNamesSerializer
    def get_queryset(self):
        search_str = self.request.query_params.get('search', None)
        queryset = Company.all
        if search_str:
            search_str = decode_string(search_str)
            queryset = queryset.filter(business_name__icontains=search_str)

        return queryset.filter(country_id=get_request_country_id())


class AllRegisteredCompaniesMinimalisticView(ListAPIView):
    serializer_class = CompanyNamesSerializer
    def get_queryset(self):
        params = self.request.query_params.dict()

        company = self.request.user.company
        with_ranking = not company.is_system and params.pop('with_ranking', None) in ['true', True, 'True']
        rank_limit = params.pop('rank_limit', None) or None

        search_str = self.request.query_params.get('search', None)

        queryset = Company.objects.filter(owner_company_id__isnull=True)
        only_top_results = True
        if search_str:
            only_top_results = False
            search_str = decode_string(search_str)
            queryset = Company.get_similarity_queryset(queryset, search_str)
        queryset = queryset.filter(country_id=get_request_country_id())
        if with_ranking:
            queryset = company.assign_ranking(
                queryset=queryset, limit=rank_limit, ranking_module='site_booking', only_top_results=only_top_results,
                order_by_fields=[
                    '-exact_match_score', '-contains', '-business_name_similarity', '-entity_name_similarity'
                ] if search_str else []
            )

        return queryset


class CompanyCompaniesMinimalisticView(ListAPIView):
    serializer_class = CompanyMinimalisticSerializer
    def get_queryset(self):
        from core.contracts.models import Brokerage
        company_id = self.kwargs.get('company_id')
        company = Company.objects.filter(id=company_id).first()
        user_properties = self.request.query_params.get('user_properties', [])
        if user_properties:
            user_properties = user_properties.split(',')
        params = self.request.query_params.dict()
        with_ranking = not company.is_system and params.pop('with_ranking', None) in ['true', True, 'True']
        rank_limit = params.pop('rank_limit', None) or None
        include_parent_company = params.pop('include_parent_company', None) == 'true'
        include_system_company = params.pop('include_system_company', None) == 'true'
        search_str = params.pop('search', None)
        selected_company_id = params.pop('company_id', None)
        if selected_company_id:
            return Company.objects.filter(id=selected_company_id)
        assets = [asset for asset in params.pop('assets', '').split(',') if asset]
        filters = {'object_company__' + asset + '__isnull': False for asset in assets}
        queryset = Company.directory_companies_only(
            company_id, filters, include_parent_company, include_system_company
        )
        if search_str:
            search_str = decode_string(search_str)
            queryset = Company.get_similarity_queryset(queryset, search_str)

        if with_ranking:
            queryset = company.assign_ranking(
                queryset=queryset,
                limit=rank_limit,
                order_by_fields=[
                    '-exact_match_score', '-contains', '-business_name_similarity', '-entity_name_similarity'
                ] if search_str else []
            )

        if self.request.query_params.get('excludeGroups', None) not in ['true', True, 'True']:
            queryset = queryset.prefetch_related('groups')

        queryset = queryset.prefetch_related('external_booking_connections')

        if 'brokerages_for_user' in user_properties and self.request.user:
            queryset = queryset.prefetch_related(
                Prefetch(
                    'brokerages_set',
                    to_attr='user_brokerages',
                    queryset=Brokerage.objects.filter(broker_company_id=self.request.user.company_id)
                )
            )

        return queryset if with_ranking else queryset.order_by('business_name')


class CompanyCompaniesView(APIView):
    def get(self, request, company_id): # pylint: disable=too-many-locals
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        query_params = request.query_params.dict()
        properties = [v for v in query_params.pop('properties', '').split(',') if v]
        user_properties = [v for v in query_params.pop('user_properties', '').split(',') if v]
        prefetch_related = [
            v for v in query_params.pop('prefetch_related', '').split(',') if v
        ]
        assets = [_v for _v in query_params.pop('assets', '').split(',') if _v != '']
        include_parent_company = query_params.pop('include_parent_company', None) == 'true'

        filter_params = {
            **{k: v for (k, v) in query_params.items() if not k.endswith('relations')},
            **{_asset + '__isnull': False for _asset in assets},
        }
        relations = {
            k: v.split(',') for (k, v) in query_params.items() if k.endswith('relations')
        }
        added_companies_ids = company.get_companies(return_ids=True)
        if include_parent_company:
            added_companies_ids.append(company_id)

        queryset = Company.objects.select_related(
            *Company.common_many_to_one_relations
        ).distinct('id')

        if prefetch_related:
            queryset = queryset.prefetch_related(*prefetch_related)

        if request.user.is_staff:
            queryset = queryset.filter(**filter_params)
            queryset = queryset.filter(country_id=get_request_country_id())
            user_properties.append('can_register')
        else:
            queryset = queryset.filter(id__in=added_companies_ids, **filter_params)

        return Response(
            Company.qs2dict(
                queryset=queryset,
                properties=properties,
                user_properties=user_properties,
                user=request.user,
                many_to_one_relations=Company.common_many_to_one_relations + relations.get('many_to_one_relations', []),
                one_to_many_relations=relations.get('one_to_many_relations', []),
            ),
            status=status.HTTP_200_OK
        )


class CompanyFarmsWebView(ListAPIView):
    serializer_class = FarmSerializer
    pagination_class = CustomPageNumberPagination
    def get_queryset(self):
        company = Company.objects.filter(
            id=self.kwargs.get('company_id')
        ).select_related('platformfeatures').first()
        if not company:
            raise Http404()
        query_params = self.request.query_params.dict()
        include_unaccepted = query_params.pop('include_unaccepted', None)
        is_active = query_params.pop('is_active', None)
        is_global = query_params.get('is_global', None)
        if include_unaccepted:
            include_unaccepted = include_unaccepted == 'true'

        if is_global and get(company, 'platformfeatures.farms'):
            queryset = Farm.objects.select_related(*Farm.COMMON_RELATIONS)
        elif self.request.user.is_staff:
            queryset = company.farm_set.select_related(*Farm.COMMON_RELATIONS)
        else:
            queryset = company.get_farms(
                include_unaccepted=include_unaccepted,
                self_farms_only=True,
                use_is_active_flag=False
            ).select_related(
                'company', 'address', 'market_zone', 'region', 'broker_company'
            )

        queryset = queryset.filter(company__country_id=get_request_country_id())
        if is_active:
            queryset = queryset.filter(is_active=is_active == 'true')

        return queryset.order_by('name')


class CompanyFarmsSearchView(ListAPIView):
    serializer_class = FarmSerializer
    pagination_class = CustomPageNumberPagination
    def get_queryset(self):
        company_id = self.kwargs['company_id']
        request = self.request
        company = Company.objects.filter(
            id=company_id
        ).select_related('platformfeatures').first()
        if not company:
            raise Http404()
        params = request.query_params.dict().copy()
        search_str = params.pop('search', None)
        order_by = params.pop('order_by', None)
        order = params.pop('order', None)
        params.pop('page', None)
        params.pop('page_size', None)
        is_active = params.pop('is_active', None)
        include_unaccepted = params.pop('include_unaccepted', None)
        is_global = params.pop('is_global', None)
        if include_unaccepted:
            include_unaccepted = include_unaccepted == 'true'

        if is_global and get(company, 'platformfeatures.farms'):
            queryset = Farm.objects.select_related(*Farm.COMMON_RELATIONS)
        elif self.request.user.is_staff:
            queryset = company.farm_set.select_related(*Farm.COMMON_RELATIONS)
        else:
            queryset = company.get_farms(
                include_unaccepted=include_unaccepted,
                self_farms_only=True,
                use_is_active_flag=False
            )

        queryset = queryset.filter(company__country_id=get_request_country_id())
        if is_active:
            queryset = queryset.filter(is_active=is_active == 'true')

        if search_str:
            search_str = decode_string(search_str)
            queryset = queryset.filter(
                models.Q(name__icontains=search_str) |
                models.Q(company__business_name__icontains=search_str) |
                models.Q(mobile__icontains=search_str) |
                models.Q(address__name__icontains=search_str) |
                models.Q(address__address__icontains=search_str) |
                models.Q(market_zone__name__icontains=search_str) |
                models.Q(region__name__icontains=search_str)
            )

        order_by_map = {
            'id': ['id'],
            'name': ['name'],
            'company_name': ['company__business_name'],
            'mobile': ['mobile'],
            'address': ['address__address', 'address__name'],
            'market_zone_name': ['market_zone__name'],
            'region_name': ['region__name'],
        }

        if order_by:
            _order_by = order_by_map.get(inflection.underscore(order_by), ['created_at'])
            order = '-' if order == 'desc' else ''
            queryset = queryset.order_by(*[order + f for f in _order_by])

        return queryset


class CompanyFarmsView(APIView):
    def get(self, request, company_id):
        try:
            _company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        _query_params = request.query_params.dict()
        _include_unaccepted = _query_params.pop('include_unaccepted', None)
        _broker_company_id = _query_params.pop('broker_company_id', None)
        is_active = _query_params.pop('is_active', None)
        if _include_unaccepted:
            _include_unaccepted = _include_unaccepted == 'true'
        _relations = [_r for _r in _query_params.pop('relations', '').split(',') if _r != '']
        if request.user.is_staff:
            if _company.is_system:
                _query_set = Farm.objects.select_related(*Farm.COMMON_RELATIONS).all()
            else:
                _query_set = _company.farm_set.select_related(
                    'company__platformfeatures', 'farmacceptancerequest', 'created_by',
                ).prefetch_related(
                    'company__added_company_set_subject'
                )
            user_properties = []
            one_to_one_relations = Farm.COMMON_RELATIONS
        else:
            _query_set = _company.get_farms(
                include_unaccepted=_include_unaccepted, broker_company_id=_broker_company_id
            )
            _query_set = _query_set.select_related(
                'company__platformfeatures', 'farmacceptancerequest', 'created_by',
            ).prefetch_related(
                'company__added_company_set_subject'
            )
            user_properties = [
                'is_associated',
                'is_in_added_companies',
                'key_contact_name_for_user',
                'is_managed_by_user',
                'is_creator',
                'is_in_user_directory',
                'is_highlighted',
                'is_pending_request',
                'is_pending_request_for_grower',
            ]
            one_to_one_relations = _relations if _relations else Farm.COMMON_RELATIONS
        if is_active:
            _query_set = _query_set.filter(is_active=is_active == 'true')
        return Response(
            Company.qs2dict(
                queryset=_query_set,
                one_to_one_relations=one_to_one_relations,
                user_properties=user_properties,
                user=request.user,
                properties=['is_managed'],
            ),
            status=status.HTTP_200_OK
        )

    def post(self, request, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        try:
            farm = company.upsert_farm(request.data, request.user)
            return Response(
                farm.to_dict(
                    properties=['is_grower_acceptance_required'],
                    many_to_one_relations=Farm.COMMON_RELATIONS if farm.persisted else [],
                    user_properties=[
                        'is_associated',
                        'is_in_added_companies',
                        'is_pending_request',
                        'is_pending_request_for_grower',
                        'is_highlighted',
                        'is_managed_by_user',
                        'is_creator',
                        'is_in_user_directory',
                    ],
                    user=request.user,
                ),
                status=status.HTTP_201_CREATED if farm.persisted else status.HTTP_400_BAD_REQUEST
            )
        except IntegrityError as ex:
            exception_arg = get(ex, 'args[0]', '')
            if 'duplicate' in exception_arg and 'farms_name_uniq' in exception_arg:
                return Response({'errors': {'name': ['Farm with this name already exists in this company']}},
                                status=status.HTTP_400_BAD_REQUEST)
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class CompanyReportRecipientsView(APIView):
    permission_classes = [IsSystemCompany]
    def post(self, request, company_id):
        company = Company.objects.filter(id=company_id)
        if not company:
            return Response(
                status=status.HTTP_404_NOT_FOUND
            )
        company.update(**request.data)
        return Response(
            status=status.HTTP_200_OK
        )


class CompanyView(APIView):
    def get(self, request, company_id):
        Company.assign_system_manager()
        queryset = Company.objects.filter(id=company_id)
        if not queryset.exists():
            raise Http404()

        company = queryset.select_related(
            'type', 'address',
        ).prefetch_related(
            *Company.common_many_to_one_relations
        ).first()

        company.abn_status = company.fetch_updated_abn_status()

        data = company.to_dict(
            properties=[
                'logo_url', 'is_default_directory_company', 'is_xero_account_linked', 'is_valid_xero_connection',
                'has_tracking_categories', 'country_code', 'warehouse_fees_with_grades_combo_exists',
                'is_impex_docs_account_linked', 'impex_docs_client_id', 'impex_docs_client_secret',
            ],
            user=request.user,
            user_properties=['brokerages_for_user'],
            many_to_one_relations=Company.common_many_to_one_relations,
        )

        if request.user.company_id == company_id or request.user.is_staff:
            data['xero_client_secret'] = company.xero_client_secret
            data['xero_client_id'] = company.xero_client_id
            data['xero_tenant_id'] = company.xero_tenant_id

        return Response(data, status=status.HTTP_200_OK)

    def put(self, request, company_id):
        try:
            updated_type_id = request.data.get('type_id', None)
            grower_to_non_grower, non_grower_to_non_grower = False, False
            companies_with_affecting_stocks = []
            if updated_type_id and (company := Company.objects.filter(id=company_id).first()):
                existing_type_id = company.type_id
                grower_to_non_grower = existing_type_id is GROWER_TYPE_ID and updated_type_id is not GROWER_TYPE_ID
                non_grower_to_non_grower = existing_type_id is not GROWER_TYPE_ID and updated_type_id is GROWER_TYPE_ID
            if grower_to_non_grower or non_grower_to_non_grower:
                stocks = Stock.objects.filter(
                    Stock.company_all_stocks_criteria(company_id),
                    load__shrinkage__isnull=False, load__shrinkage__gt=0, storage__is_active=True
                )
                unique_stocks = Stock.objects.filter(id__in=stocks.values_list('id', flat=True))
                companies_with_affecting_stocks = list(set(unique_stocks.values_list('farm__company_id', flat=True)))

            company = Company.update_with_location(
                instance_id=company_id,
                params=request.data,
                user=request.user,
                schedule_job_for_update=True
            )
            if (grower_to_non_grower or non_grower_to_non_grower) and request.user.is_staff:
                Job.schedule_job_for_task('update_shrinkage_with_updated_business_type',
                                          params={'company_id': company_id,
                                                  'companies_with_affecting_stocks': companies_with_affecting_stocks})

        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        except IntegrityError as ex:  # pylint: disable=broad-except
            exception_arg = get(ex, 'args[0]', '')
            if 'duplicate' in exception_arg and 'comp_name_country_uniq' in exception_arg:
                return Response({'errors': {'business_name': ['Company with this business name already exists']}},
                                status=status.HTTP_400_BAD_REQUEST)
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)

        if company.errors:
            return Response(company.to_dict(),
                            status=status.HTTP_400_BAD_REQUEST)
        data = company.to_dict(
            properties=['logo_url'],
            user_properties=['brokerages_for_user'],
            user=request.user,
            many_to_one_relations=Company.common_many_to_one_relations + ['platformfeatures']
        )
        if request.user.company_id == company_id or request.user.is_staff:
            data['xero_client_secret'] = company.xero_client_secret
            data['xero_client_id'] = company.xero_client_id
            data['xero_tenant_id'] = company.xero_tenant_id

        return Response(data, status=status.HTTP_200_OK)


class CompanyOverviewView(APIView):  # pragma: no cover
    permission_classes = (IsAdminUser, )

    def get(self, _, company_id):
        company = get_object_or_404(Company, id=company_id)
        user = self.request.user
        return Response(
            company.get_overview(user.is_staff or user.company_id == company.id),
            status=status.HTTP_200_OK
        )


class CompanyEmployeesMinimalisticView(ListAPIView):
    serializer_class = EmployeeMinimalisticSerializer
    def get_queryset(self):
        return Employee.objects.filter(company_id=self.kwargs.get('company_id')).exclude(type_id=OBSERVER_TYPE_ID)


class CompanyEmployeesView(APIView):
    @staticmethod
    def get(request, company_id):
        archived = bool('archived' in request.query_params)
        if Company.all.filter(id=company_id).exists():
            manager = Employee.archived if archived else Employee.objects
            queryset = manager.select_related(
                'company__platformfeatures', 'type'
            ).filter(company_id=company_id)
            if (company_id not in SYSTEM_COMPANY_IDS or not request.user.is_staff) and not request.user.is_observer:
                queryset = queryset.exclude(type_id=OBSERVER_TYPE_ID)

            key_employee_id = None
            if request.user.company_id != company_id:
                key_employee_id = KeyContact.get_for_company(
                    requester_company_id=request.user.company_id,
                    company_id=company_id,
                )

            employees = Company.qs2dict(
                queryset=queryset,
                properties=['name_with_mobile', 'linked_farm_names', 'farm_ids', 'signature_url'],
                many_to_one_relations=['company', 'type']
            )
            employees = assign_key_contact(
                employees, key_employee_id) if key_employee_id else [dict(e, key_contact=False) for e in employees]

            return Response(employees, status=status.HTTP_200_OK)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)

    @staticmethod
    def post(request, company_id):
        try:
            company = Company.objects.get(id=company_id)
            data = request.data
            data.pop('transaction_password', None)
            if 'site_id' in data:
                data['farm_id'] = data.pop('site_id')
            type_id = data.get('type_id', None)
            if type_id == OBSERVER_TYPE_ID:
                return Response({
                    'errors': {
                        'type_id': ["Observer type employees can't be created by users"],
                        }
                    }, status=status.HTTP_400_BAD_REQUEST
                )
            user = request.user
            is_admin = bool(type_id and type_id == COMPANY_ADMIN_TYPE_ID)
            permission_func = user.can_do_admin_operations_in_company if is_admin else user.can_action_in_company

            if not permission_func(company):
                raise Http403()

            employee = Employee.add_to_company(company, data, user)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        except IntegrityError as ex:
            exception_arg = get(ex, 'args[0]', '')
            if 'duplicate' in exception_arg and 'employee_name_uniq' in exception_arg:
                return Response({
                    'errors': {
                        'first_name': ['Employee with this first name already exists in this company'],
                        'last_name': ['Employee with this last name already exists in this company']
                        }
                    }, status=status.HTTP_400_BAD_REQUEST)
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)

        employee.token = None
        _json = employee.to_dict(many_to_one_relations=['type', 'farm'])

        if employee.id:
            _json['key_contact'] = False
            return Response(_json, status=status.HTTP_201_CREATED)

        return Response(_json, status=status.HTTP_400_BAD_REQUEST)


class UnregisteredCompanyEmployeesView(APIView):
    permission_classes = (AllowAny, )
    def get(self, _, company_id):
        company = get_object_or_404(Company.objects, pk=company_id, owner_company_id__isnull=False)
        return Response(
            EmployeeAllFieldsSerializer(
                company.employee_set.exclude(Q(type_id=OBSERVER_TYPE_ID) | Q(is_active=False)).all(),
                many=True
            ).data,
            status=status.HTTP_200_OK
        )

    def post(self, request, company_id):
        try:
            company = get_object_or_404(Company.objects, pk=company_id, owner_company_id__isnull=False)
            keep_unregistered = request.data.pop('keep_unregistered', False)
            company_from_ngr = request.data.pop('company_from_ngr', False)
            company_details = request.data.pop('company', None)
            username = ''.join(request.data.get('username', '').split(' '))
            employee = Employee(**request.data)

            employee.company_id = company.id
            employee.username = username

            with transaction.atomic():
                employee._save()
                if company.is_independent_farmer:
                    employee.farms.set([company.independent_farm_id])
                if company_from_ngr and not company.is_registered and company.type_id != GROWER_TYPE_ID:
                    company.type_id = GROWER_TYPE_ID
                    company.updated_by = employee
                    company.save()
                    company.update_all_ngr_relations()
                if not keep_unregistered:
                    company.mark_registered(user=employee, set_password=False)
                if company_details and company:
                    company = Company.update_with_location(
                        company_id,
                        company_details,
                        employee,
                    )

            employee.token = None

            response = employee.to_dict(properties=['farm_ids'])
            status_code = status.HTTP_400_BAD_REQUEST
            if employee.id:
                form_type = 'logistics lite' if company.plan_type == 'logistics_lite' else company.type.name
                Slack(
                    Slack.NEW_USER_REGISTRATION_CHANNEL
                ).log(
                    NEW_UNREG_COMPANY_SLACK_MESSAGE.format(
                        form_type=form_type,
                        env=settings.ENV,
                        name=employee.name,
                        mobile=employee.mobile,
                        email=employee.email,
                        username=employee.username,
                        business_name=company.name,
                        abn=company.abn,
                        country=company.country.name,
                        business_type=company.type
                    )
                )
                status_code = status.HTTP_201_CREATED
            return Response(response, status=status_code)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        except IntegrityError as ex:
            exception_arg = get(ex, 'args[0]', '')
            if 'duplicate' in exception_arg and 'employee_name_uniq' in exception_arg:
                return Response({
                    'errors': {
                        'first_name': ['Employee with this first name already exists in this company'],
                        'last_name': ['Employee with this last name already exists in this company']
                        }
                    }, status=status.HTTP_400_BAD_REQUEST)
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class UnregisteredCompanyEmployeeView(APIView):
    permission_classes = (AllowAny,)
    def put(self, request, company_id, employee_id):   # pylint: disable=too-many-locals
        company = get_object_or_404(Company.objects, pk=company_id, owner_company_id__isnull=False)
        employee = get_object_or_404(Employee.objects, pk=employee_id, company_id=company_id)
        keep_unregistered = request.data.pop('keep_unregistered', False)
        company_from_ngr = request.data.pop('company_from_ngr', False)
        company_details = request.data.pop('company', None)
        assign_credentials = True

        for key, value in request.data.items():
            setattr(employee, key, value)

        if 'password' in request.data:
            assign_credentials = False
            employee.set_password(raw_password=request.data.get('password'))
            employee.save()

        if company.is_independent_farmer:
            employee.farms.set([company.independent_farm_id])

        with transaction.atomic():
            if assign_credentials:
                employee._save()
            if company_from_ngr and not company.is_registered and company.type_id != GROWER_TYPE_ID:
                company.type_id = GROWER_TYPE_ID
                company.updated_by = employee
                company.save()
                company.update_all_ngr_relations()
            if not keep_unregistered:
                company.mark_registered(user=request.user, set_password=False)
            if company_details and company:
                company = Company.update_with_location(
                    company_id,
                    company_details,
                    employee,
                )

        response = employee.to_dict(properties=['farm_ids'])
        status_code = status.HTTP_400_BAD_REQUEST
        if employee.id:
            form_type = 'logistics lite' if company.plan_type == 'logistics_lite' else company.type.name
            Slack(
                Slack.NEW_USER_REGISTRATION_CHANNEL
            ).log(
                OLD_UNREG_COMPANY_SLACK_MESSAGE.format(
                    form_type=form_type,
                    env=settings.ENV,
                    name=employee.name,
                    mobile=employee.mobile,
                    email=employee.email,
                    username=employee.username,
                    business_name=company.name,
                    abn=company.abn,
                    country=company.country.name,
                    business_type=company.type,
                )
            )
            status_code = status.HTTP_201_CREATED

        return Response(response, status=status_code)


class CompanyEmployeeView(APIView):
    def get(self, request, company_id, employee_id):
        params = request.query_params.dict().get('many_to_one_relations', '')
        many_to_one_relations = []

        if params:
            many_to_one_relations = params.split(',')

        employee = Employee.objects.select_related(
            'type'
        ).filter(
            id=employee_id, company_id=company_id
        )

        if many_to_one_relations:
            employee = employee.prefetch_related(*many_to_one_relations)

        if not employee:
            return Response(status=status.HTTP_404_NOT_FOUND)

        return Response(
            employee[0].to_dict(many_to_one_relations=['type', 'company__platformfeatures&address']),
            status=status.HTTP_200_OK
        )

    def put(self, request, company_id, employee_id):  # pylint: disable=too-many-locals,too-many-branches,too-many-return-statements
        user = request.user
        try:
            company = Company.objects.get(id=company_id)
            employee = company.employee_set.get(id=employee_id)
            data = request.data
            user_set_password = data.pop('user_set_password', False)

            sensitive_fields = ['username', 'email']
            is_username_or_email_changed = any(
                data.get(field, None) and get(employee, field) != data.get(field, None) for field in sensitive_fields
            )

            password = data.get('password')
            transaction_password = data.pop('transaction_password', '')
            data.pop('office', None)

            type_id = data.get('type_id', None)
            is_role_updated = type_id and type_id != employee.type_id
            if is_role_updated and not user.is_staff and user.id == employee.id:  # employee cant change their own role
                raise Http403()
            is_role_updated_with_admin = (
                    is_role_updated and any(
                        _type_id == COMPANY_ADMIN_TYPE_ID for _type_id in [type_id, employee.type_id]))
            permission_func = user.can_action_in_company
            if (is_username_or_email_changed and user.id != employee.id) or is_role_updated_with_admin:
                permission_func = user.can_do_admin_operations_in_company

            if permission_func(company):
                if is_username_or_email_changed:
                    if not transaction_password:
                        raise Http403()
                    if not user.check_password(transaction_password):
                        return Response(
                            {'errors': {'transaction_password': ['Bad Credentials']}},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                if employee.is_company_admin and type_id and type_id != COMPANY_ADMIN_TYPE_ID and len(
                        company.get_company_admins()) == 1:
                    employee.errors = {'type_id': ['Atleast one company admin needed']}
                elif password and employee.check_password(password):
                    return Response(
                        {
                            'errors': {'password': ['New password cannot be same as old password']}
                        }, status=status.HTTP_400_BAD_REQUEST)
                elif employee.type_id == OBSERVER_TYPE_ID:
                    return Response({
                        'errors': {
                            'type_id': ["Observer type employees can't be updated by users"],
                        }
                    }, status=status.HTTP_400_BAD_REQUEST
                    )
                else:
                    employee = Employee.update(instance_id=employee.id, data=data, current_user=user)
                if user_set_password:
                    employee.reset_password()
            else:
                raise Http403()

        except (Company.DoesNotExist, Employee.DoesNotExist):
            return Response(status=status.HTTP_404_NOT_FOUND)

        except IntegrityError as ex: # pylint: disable=broad-except
            exception_arg = get(ex, 'args[0]', '')
            if 'duplicate' in exception_arg and 'employee_name_uniq' in exception_arg:
                return Response(
                    {'errors': {
                        'first_name': ['Employee with this first name already exists in this company'],
                        'last_name': ['Employee with this last name already exists in this company']
                        }
                    }, status=status.HTTP_400_BAD_REQUEST)
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)

        _json = employee.to_dict(
            many_to_one_relations=['type'], properties=['farm_ids', 'linked_farm_names', 'farm_id', 'signature_url']
        )

        key_employee_id = KeyContact.get_for_company(
            requester_company_id=user.company_id,
            company_id=company_id,
        )
        _json['key_contact'] = key_employee_id == employee_id

        _status = status.HTTP_400_BAD_REQUEST if getattr(
            employee, 'errors', None
        ) else status.HTTP_200_OK
        return Response(_json, status=_status)


class CompanyTrucksWebView(ListAPIView):
    serializer_class = TruckWebListingSerializer
    pagination_class = CustomPageNumberPagination
    def get_queryset(self):
        search_str = self.request.query_params.get('search')
        is_archived = self.request.query_params.get('is_archived', None)
        queryset = Truck.objects.filter(company_id=self.kwargs['company_id']).exclude(rego=FLEET_REGO)
        if search_str:
            search_str = decode_string(search_str)
            queryset = queryset.filter(
                models.Q(code__icontains=search_str) |
                models.Q(rego__icontains=search_str) |
                models.Q(trailer__rego__icontains=search_str)
            ).distinct('rego')
        return queryset.filter(is_active=is_archived != 'true').order_by('rego')


class CompanyTrucksView(APIView):
    def get(self, _, company_id):
        query_params = self.request.query_params
        minimal = query_params.get('minimal', False) in ['true', 'True']
        all_trucks = query_params.get('all_trucks', None)
        include_truck_id = query_params.get('include_truck_id', None)

        queryset = Truck.objects.filter(company_id=company_id).select_related('company')
        if not minimal:
            queryset = queryset.prefetch_related('trailer_set').select_related('farm__company', 'driver')

        if all_trucks == 'true':
            from core.freights.models import FreightContract
            inactive_trucks = queryset.filter(is_active=False)
            truck_rego_active_in_other_company = Truck.objects.filter(
                rego__in=[inactive_trucks.values_list('rego', flat=True)], is_active=True
            ).values_list('rego', flat=True)
            contracts = FreightContract.objects.filter(
                status__in=['confirmed', 'in_progress'],
                planned_truck_id__in=inactive_trucks.exclude(
                    rego__in=truck_rego_active_in_other_company).values_list('id', flat=True))
            in_active_truck_ids = contracts.values_list('planned_truck_id', flat=True)
            queryset = queryset.filter(Q(is_active=True) | Q(id__in=in_active_truck_ids))
        else:
            condition = Q(is_active=True)
            if include_truck_id:
                condition |= Q(id=include_truck_id)
            queryset = queryset.filter(condition)

        trucks = Truck.qs2dict(
            queryset=queryset,
            one_to_many_relations=[] if minimal else ['trailer'],
            many_to_one_relations=[] if minimal else ['farm'],
            properties=['company_name'] if minimal else [
                'total_weights', 'mobile_participation', 'transaction_participation',
                'assigned_driver_id'
            ]
        )

        return Response(trucks, status=status.HTTP_200_OK)


class CompanyTrucksMinimalisticView(ListAPIView):
    serializer_class = TruckMinimalSerializer
    def get_serializer_class(self):
        linked_employees = get(self.request.query_params, 'linked_employees') in ['true', 'True']
        return EmployeeTruckMinimalSerializer if linked_employees else TruckMinimalSerializer

    def get_queryset(self):
        return Truck.objects.filter(company_id=self.kwargs.get('company_id'))


class CompanyTruckView(APIView):
    def get(self, _, company_id, truck_id):
        try:
            _company = Company.all.get(id=company_id)
            _truck = _company.truck_set.get(id=truck_id)
        except (Company.DoesNotExist, Truck.DoesNotExist):
            return Response(status=status.HTTP_404_NOT_FOUND)
        else:
            return Response(
                _truck.to_dict(properties=['total_weights']),
                status=status.HTTP_200_OK
            )


class CompanyTrailersView(APIView):
    def get(self, _, company_id):
        try:
            company = Company.all.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        else:
            is_archived = self.request.query_params.get('is_archived', None)
            queryset = company.trailer_set.select_related('truck').filter(is_active=is_archived != 'true')
            return Response(
                Company.qs2dict(
                    queryset=queryset,
                    properties=[
                        'truck_rego',
                        'net_weight_display_value',
                        'tare_weight_display_value',
                        'gross_weight_display_value'
                    ],
                ),
                status=status.HTTP_200_OK
            )


class CompanyUnassignedTrailersView(APIView):
    def get(self, _, company_id):
        try:
            company = Company.all.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        else:
            return Response(
                Company.qs2dict(
                    queryset=company.unassigned_trailers()
                ),
                status=status.HTTP_200_OK
            )


class BHCSitesView(APIView):
    def get(self, _):
        _locations = CompanySite.qs2dict(
            queryset=CompanySite.bhcs.select_related(
                'company__platformfeatures', 'address', 'company__address'
            ),
            many_to_one_relations=['company', 'address'],
            one_to_one_relations=['address'],
            properties=['company_address']
        )

        response = Response(_locations, status=status.HTTP_200_OK)
        response['Cache-Control'] = 'private, max-age=86400'
        return response


class CompanyBHCSitesView(APIView):
    def get(self, _, company_id):
        sites = CompanySite.qs2dict(
            queryset=CompanySite.bhcs.filter(
                company_id=company_id
            ).select_related(
                'address', 'market_zone', 'region'
            ),
            many_to_one_relations=['address', 'market_zone', 'region']
        )

        return Response(sites, status=status.HTTP_200_OK)


class CompanyNgrsView(APIView):
    permission_classes = [AllowAny]
    def post(self, request, company_id):
        try:
            _company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        ngr = Ngr.create_with_banks(company_id, request.data)

        if ngr.id:
            ngr.merge_with_unknown_ngr()
            return Response(NgrListingSerializer(ngr).data, status=status.HTTP_201_CREATED)

        data = ngr.to_dict()
        bank_accounts = []
        if getattr(ngr, 'bank_accounts', None):
            for bank_account in ngr.bank_accounts:
                bank_accounts.append(bank_account.to_dict())
            data['bank_accounts'] = bank_accounts
        return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, company_id):
        params = request.query_params.dict()
        params['company_id'] = company_id
        attrs = params.dict() if isinstance(params, QueryDict) else params
        query_filter = models.Q(**attrs) | models.Q(bank_account_set__company_id=company_id)

        queryset = Ngr.objects.filter(query_filter).prefetch_related(
            'bank_account_set'
        ).distinct('id').order_by('id')

        return Response(NgrListingSerializer(queryset, many=True).data, status=status.HTTP_200_OK)


class CompanyNgrsMinimalisticView(ListAPIView):
    serializer_class = NgrSerializerWithShareHolderNames
    def get_queryset(self):
        company_id = self.kwargs.get('company_id')
        query_filter = models.Q(company_id=company_id) | models.Q(bank_account_set__company_id=company_id)
        if company_id in SYSTEM_COMPANY_IDS:
            return [Ngr.get_unknown_ngr(company_id)]
        return Ngr.objects.prefetch_related(
            'bank_account_set',
            'ngrsustainabilitydeclaration_set'
        ).filter(query_filter).distinct('id').order_by('id')


class CompanyNGRCredentialsView(RetrieveUpdateDestroyAPIView):
    serializer_class = NGRPortalCredentialsSerializer
    def get_company(self):
        return get_object_or_404(Company.objects.filter(id=self.kwargs['company_id']))

    def get_object(self):
        company = self.get_company()
        instance = NgrPortalCredential.get_credentials(company.id)
        if instance:
            return instance
        return None

    def put(self, request, *args, **kwargs):
        company = self.get_company()
        instance = NgrPortalCredential.upsert(company.id, request.data.copy())
        return Response(self.get_serializer(instance).data)


class CompanyNGRNumberValidateView(RetrieveAPIView):
    serializer_class = NGRServiceResponseSerializer
    def get(self, _, *args, **kwargs):  # pylint: disable=arguments-differ
        company_id = self.kwargs['company_id']
        ngr_number = self.kwargs['number']

        response = NGRLookUp.fetch(company_id, ngr_number, True)

        if response and 'error' in response:
            return Response(response)
        payees = NGRLookUp.get_payees_from_record(response)
        if payees:
            single_payee_kwargs = {
                'latitude': get(response, 'gps_latitude') or None,
                'longitude': get(response, 'gps_longitude') or None
            }
            company = Company.objects.filter(id=company_id).first()
            NGRService.create_companies_and_ngr_from_payees(
                company, ngr_number, payees, self.request.user, upsert_ngr=False,
                primary_payee_id=get(response, 'primary_payee_id'), **single_payee_kwargs
            )

        return Response({'ngr_details': self.get_serializer(payees, many=True).data})


class ValidateAllCompaniesNGRView(APIView):
    @staticmethod
    def get(_):
        Job.schedule_job_for_task('trigger_update_company_ngr_from_portal')
        return Response(status.HTTP_200_OK)


class CompanyFarmNgrsView(APIView):
    def get(self, request, company_id):
        _params = request.query_params.dict()
        _params['company_id'] = company_id
        _attrs = _params.dict() if isinstance(_params, QueryDict) else _params
        _queryset = Ngr.objects.filter(
            **_attrs
        ).prefetch_related(
            'bank_account_set'
        ).order_by('id')
        _ngrs = NgrListingSerializer(_queryset, many=True).data
        return Response(_ngrs, status=status.HTTP_200_OK)


class CompanyNgrView(RetrieveAPIView):
    serializer_class = NgrListingSerializer
    def get_object(self):
        instance = Ngr.objects.filter(
            owner_company_ids__contains=[self.kwargs['company_id']],
            id=self.kwargs['ngr_id']
        ).first()
        if not instance:
            raise Http404()
        return instance

    def put(self, request, company_id, ngr_id):
        ngr = self.get_object()
        data = request.data
        sustainable_declaration_data = data.pop('sustainable_declaration', None)
        if sustainable_declaration_data:
            ngr.update_sustainable_declarations(sustainable_declaration_data)
        ngr = Ngr.update_with_banks(ngr_id, data, company_id)

        if getattr(ngr, 'errors', None):
            result = ngr.to_dict()
            bank_accounts = []
            if getattr(ngr, 'bank_accounts', None):
                for _bank_account in ngr.bank_accounts:
                    bank_accounts.append(_bank_account.to_dict())
                result['bank_accounts'] = bank_accounts
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        ngr.refresh_from_db()

        return Response(self.get_serializer(ngr).data, status=status.HTTP_200_OK)


class CompanyLocationsView(APIView):
    def post(self, request, company_id):
        try:
            _company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        _data = request.data
        _data['owner_id'] = _company.id
        _data['location_type'] = 'company'
        _location = Location.create(request.data)
        _json = _location.to_dict()
        if _location.id:
            _select_related = ['owner', 'marketzone', 'region']
            _queryset = Location.objects.filter(pk=_location.id).prefetch_related(*_select_related)
            _locations = Location.qs2dict(_queryset, _select_related)
            return Response(_locations[0], status=status.HTTP_201_CREATED)

        return Response(_json, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, company_id):
        _params = request.query_params.dict()
        _params['owner_id'] = company_id
        _select_related = ['owner', 'marketzone', 'region']
        _attrs = _params.dict() if isinstance(_params, QueryDict) else _params
        _queryset = Location.objects.filter(**_attrs)\
            .prefetch_related(*_select_related)
        _locations = Location.qs2dict(_queryset, _select_related)
        return Response(_locations, status=status.HTTP_200_OK)


class CompanyLocationView(APIView):
    def get(self, _, company_id, location_id):   # pylint: disable=unused-argument
        _select_related = ['owner', 'marketzone', 'region']
        _locations = Location.objects.filter(id=location_id) \
            .prefetch_related(*_select_related).all()

        _locations = Location.qs2dict(
            _locations,
            many_to_one_relations=_select_related
        )

        if not _locations:
            return Response(status=status.HTTP_404_NOT_FOUND)

        return Response(_locations[0], status=status.HTTP_200_OK)

    def put(self, request, company_id, location_id):  # pylint: disable=unused-argument
        try:
            _location = Location.update(location_id, request.data)
        except Location.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        if getattr(_location, 'errors', None):
            return Response(_location.to_dict(), status=status.HTTP_400_BAD_REQUEST)

        _select_related = ['owner', 'marketzone', 'region']
        _filter = {'id': _location.id}
        _queryset = Location.objects.filter(**_filter).prefetch_related(*_select_related)
        _data = Location.qs2dict(
            queryset=_queryset,
            many_to_one_relations=_select_related
        )
        return Response(_data[0], status=status.HTTP_200_OK)


class CompanySitesMinimalView(ListAPIView):
    serializer_class = FarmNameSerializer
    def get_queryset(self):
        return Farm.objects.filter(company_id=self.kwargs['company_id']).order_by('name')


class CompanySitesView(APIView):
    def post(self, request, company_id):
        try:
            if company_id in SYSTEM_COMPANY_IDS:
                _company = Company.moderators.get(id=company_id)
            else:
                _company = Company.objects.get(id=company_id)
        except (Company.DoesNotExist, Employee.DoesNotExist):
            return Response(status=status.HTTP_404_NOT_FOUND)
        try:
            data = request.data
            data['company_id'] = _company.id
            company_site = CompanySite.create_with_location(request.data)
            _json = company_site.to_dict()
            if company_site.id:
                select_related = ['address', 'market_zone', 'region']
                queryset = CompanySite.objects.filter(pk=company_site.id)\
                    .prefetch_related(*select_related)
                company_sites = CompanySite.qs2dict(queryset, select_related)
                return Response(company_sites[0], status=status.HTTP_201_CREATED)

            return Response(_json, status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError as ex: # pylint: disable=broad-except
            exception_arg = get(ex, 'args[0]', '')
            if 'duplicate' in exception_arg and 'farms_name_uniq' in exception_arg:
                return Response({'errors': {'name': ['Site with this name already exists in this company']}},
                                status=status.HTTP_400_BAD_REQUEST)
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, company_id):
        params = request.query_params.dict()
        user_properties = [v for v in params.pop('user_properties', '').split(',') if v]
        properties = [v for v in params.pop('properties', '').split(',') if v]
        params['company_id'] = company_id
        select_related = ['address', 'market_zone', 'region', 'company']
        attrs = params.dict() if isinstance(params, QueryDict) else params
        if get(attrs, 'is_active') is not None:
            attrs['is_active'] = attrs['is_active'].lower() == 'true'
        queryset = CompanySite.objects.filter(**attrs).prefetch_related(*select_related)
        company_sites = CompanySite.qs2dict(
            queryset=queryset,
            many_to_one_relations=select_related,
            user_properties=user_properties,
            properties=properties,
            user=request.user,
        )
        return Response(company_sites, status=status.HTTP_200_OK)


class CompanySiteView(APIView):
    def get(self, _, company_id, company_site_id):  # pylint: disable=unused-argument
        _select_related = ['address', 'market_zone', 'region']
        _company_sites = CompanySite.objects.filter(id=company_site_id) \
            .prefetch_related(*_select_related).all()

        _company_sites = CompanySite.qs2dict(
            _company_sites, many_to_one_relations=_select_related, one_to_one_relations=['address']
        )

        if not _company_sites:
            return Response(status=status.HTTP_404_NOT_FOUND)

        return Response(_company_sites[0], status=status.HTTP_200_OK)

    def put(self, request, company_id, company_site_id):  # pylint: disable=unused-argument
        try:
            _company_site = CompanySite.update_with_location(
                company_site_id, request.data, request.user
            )
        except (CompanySite.DoesNotExist, Employee.DoesNotExist):
            return Response(status=status.HTTP_404_NOT_FOUND)

        if getattr(_company_site, 'errors', None):
            return Response(_company_site.to_dict(), status=status.HTTP_400_BAD_REQUEST)

        _select_related = ['address', 'market_zone', 'region']
        _filter = {'id': _company_site.id}
        _queryset = CompanySite.objects.filter(**_filter).prefetch_related(*_select_related)
        _data = CompanySite.qs2dict(
            queryset=_queryset,
            many_to_one_relations=_select_related
        )
        return Response(_data[0], status=status.HTTP_200_OK)


class CompanySiteLoadsView(ListAPIView):
    pagination_class = CustomPageNumberPagination
    serializer_class = CompanySiteLoadsSerializer
    def get_queryset(self):
        user = self.request.user
        company_site_id = self.kwargs['company_site_id']
        queryset = Load.objects.filter(farm_id=company_site_id, storage_id__isnull=False).exclude(status=VOID_STATUS)
        if not user.is_staff and not user.company.owned_and_managed_farms.filter(id=company_site_id).exists():
            queryset = queryset.filter(Load.owner_stocks_criteria([user.company.id]))
        common_filters = CommonFilters(user)
        filter_params = common_filters.conditional_filtering(
                key='site_loads_filters', tz=self.request.META.get('HTTP_REFERER_TZ', user.country.timezone)
        )
        queryset = queryset.filter(filter_params).exclude(
            Load.regrade_reseason_system_loads_criteria()
        ).exclude(
            Load.stock_swap_system_loads_criteria()
        ).exclude(
            Load.update_stock_system_loads_criteria()
        )
        return queryset


class CompanySiteLoadsSearchView(ListAPIView):
    serializer_class = CompanySiteLoadsSerializer
    pagination_class = CustomPageNumberPagination
    def get_queryset(self): # pylint: disable=too-many-locals
        request = self.request
        user = self.request.user
        company_site_id = self.kwargs['company_site_id']
        params = request.query_params.dict().copy()
        search_str = params.pop('search', None)
        order_by = params.pop('order_by', None)
        order = params.pop('order', None)
        params.pop('page', None)
        params.pop('page_size', None)
        queryset = Load.objects.filter(farm_id=company_site_id, storage_id__isnull=False).exclude(
            status=VOID_STATUS).prefetch_related('regrade_reseason_inload', 'regrade_reseason_outload')
        if not user.is_staff and not user.company.owned_and_managed_farms.filter(id=company_site_id).exists():
            queryset = queryset.filter(Load.owner_stocks_criteria([user.company.id]))
        common_filters = CommonFilters(user)
        filter_params = common_filters.conditional_filtering(
                key='site_loads_filters', tz=self.request.META.get('HTTP_REFERER_TZ', user.country.timezone))
        queryset = queryset.filter(filter_params).exclude(
            Load.regrade_reseason_system_loads_criteria()
        ).exclude(
            Load.stock_swap_system_loads_criteria()
        ).exclude(
            Load.update_stock_system_loads_criteria()
        )

        if search_str:
            search_str = decode_string(search_str)
            queryset = queryset.filter(
                models.Q(type__icontains=search_str) |
                models.Q(source__icontains=search_str) |
                models.Q(created_by__first_name__icontains=search_str) |
                models.Q(created_by__last_name__icontains=search_str) |
                models.Q(storage__name__icontains=search_str) |
                models.Q(ngr__company__business_name__icontains=search_str) |
                models.Q(ngr__ngr_number__icontains=search_str) |
                models.Q(freight_provider__business_name__icontains=search_str) |
                models.Q(truck__rego__icontains=search_str) |
                models.Q(commodity__name__icontains=search_str) |
                models.Q(grade__name__icontains=search_str) |
                models.Q(variety__name__icontains=search_str) |
                models.Q(farm__name__icontains=search_str, storage_id__isnull=False) |
                models.Q(external_reference__icontains=search_str) |
                models.Q(season__icontains=search_str) |
                models.Q(title_transfer__identifier__icontains=search_str) |
                models.Q(extras__identifier__icontains=search_str) |
                models.Q(regrade_reseason_inload__identifier__icontains=search_str) |
                models.Q(regrade_reseason_outload__identifier__icontains=search_str) |
                models.Q(movement__identifier=search_str) |
                models.Q(movement__commodity_contract__identifier__icontains=search_str) |
                models.Q(movement__commodity_contract__contract_number__icontains=search_str)
            )
            queryset = queryset | Load.objects.filter(
                id__in=queryset.values_list('system_loads', flat=True), type=INLOAD, source=Load.SHRINKAGE_LOAD)

        order_by_map = {
            'freight_provider': ['freight_provider__business_name'],
            'ngr': ['ngr__ngr_number'],
            'stock_owner': ['ngr__company__business_name'],
            'rego': ['truck__rego'],
            'commodity': ['commodity__name'],
            'grade': ['grade__name'],
            'variety': ['variety__name'],
            'season': ['season'],
            'creator': ['created_by__first_name', 'created_by__last_name'],
            'net_weight': ['estimated_net_weight'],
            'tonnage_with_shrinkage': ['estimated_net_weight'],
            'created_at': ['date_time'],
            'site_name': ['farm__name'],
            'storage': ['storage__name'],
            'sub_type': ['type']
        }

        if order_by:
            _order_by = order_by_map.get(inflection.underscore(order_by), ['created_at'])
            order = '-' if order == 'desc' else ''
            queryset = queryset.order_by(*[order + f for f in _order_by])

        return queryset


class CompanyUnRegisterView(APIView):
    permission_classes = [IsSystemCompany]
    def put(self, _, company_id):
        if company_id:
            queryset = Company.objects.filter(id=company_id)
            queryset.update(
                owner_company_id=Country.get_system_company_id(),
                transaction_participation=False, mobile_participation=False,
                can_represent=False
            )
            company = queryset.first()
            if not company:
                return Response(status=status.HTTP_403_FORBIDDEN)

            company.expire_token()

        return Response(status=status.HTTP_200_OK)


class CompanyRegisterView(APIView):
    permission_classes = [IsSystemCompany]
    def put(self, request, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        is_silent_register = request.query_params.get('is_silent_register', False) == 'true'
        if request.data.get('register'):
            set_password = request.data.get('set_password', True)
            company.mark_registered(
                user=request.user, set_password=set_password, should_send_mail=not is_silent_register)

        if request.data.get('mail'):
            for admin in company.get_company_admins():
                admin.schedule_mark_registered_mail()

        return Response(status=status.HTTP_200_OK)


class CompanyPurgeView(APIView):
    permission_classes = [IsSuperUser]
    def delete(self, _, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        try:
            with transaction.atomic():
                company.save_related_models()
                Company.objects.filter(id=company_id).delete()

        except Exception:  # pylint: disable=broad-except
            return Response(
                {
                    'alert': "Couldn't delete. Please contact AgriChain Back Office Support Team."
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        return Response(status=status.HTTP_202_ACCEPTED)


class CompanyMergeView(APIView):
    permission_classes = [IsSystemCompany]
    def post(self, request, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        purge_company_id = request.query_params.get('purge_company_id', None)
        try:
            with transaction.atomic():
                company.merge_and_transfer(request.data, purge_company_id)
                if purge_company_id:
                    purge_company = Company.objects.filter(id=purge_company_id).first()
                    if purge_company:
                        purge_company.save_related_models()
                        Company.objects.filter(id=purge_company_id).delete()
        except Exception as ex:  # pylint: disable=broad-except
            return Response(
                {
                    'alert': "Couldn't delete. Please contact AgriChain Back Office Support Team." + str(ex)
                },
                status=status.HTTP_422_UNPROCESSABLE_ENTITY
            )

        return Response(data={"success": True}, status=status.HTTP_200_OK)


class CompanyAdminsView(APIView):
    permission_classes = (AllowAny, )
    def get(self, _, company_id):
        company = get_object_or_404(Company, pk=company_id)
        company_admins_emails = list(company.get_company_admins().values_list('email', flat=True))
        return Response({'emails': company_admins_emails}, status=status.HTTP_200_OK)


class ClaimCompanyView(APIView):
    permission_classes = (AllowAny, )
    def post(self, request, company_id):
        company = get_object_or_404(Company.objects.all(), pk=company_id)

        name = request.data['first_name'] + ' ' + request.data['last_name']
        claim_details_args = {
            'status': 'registered' if company.is_registered else 'unregistered',
            'env': settings.ENV,
            'name': name,
            'mobile': request.data.get('mobile', ''),
            'email': request.data.get('email', ''),
            'username': request.data.get('username', ''),
            'abn': company.abn,
            'business_name': company.business_name,
            'claim_details': request.data.pop('claim_details')
        }

        response = {"success": True}
        status_code = status.HTTP_200_OK
        if not company.is_registered:
            employee = Employee.add_to_company(company, request.data)
            if not employee.id:
                response = {"success": False}
                status_code = status.HTTP_400_BAD_REQUEST

        if response['success']:
            Slack(Slack.NEW_USER_REGISTRATION_CHANNEL).log(COMPANY_CLAIM_SLACK_MESSAGE.format(**claim_details_args))
            Job.schedule_job_for_task('send_email_for_claim_company', params={'params': claim_details_args})

        return Response(data=response, status=status_code)


class CompanyCanCreateEmployeeView(APIView):
    def get(self, request, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        reasons = company.cannot_create_employee_reasons(request.user)

        return Response(
            {
                'result': len(reasons) == 0,
                'reasons': reasons,
            },
            status=status.HTTP_200_OK,
        )


class CompanyCanCreateNgrView(APIView):
    def get(self, request, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        reasons = company.cannot_create_ngr_reasons(request.user)

        return Response(
            {
                'result': len(reasons) == 0,
                'reasons': reasons,
            },
            status=status.HTTP_200_OK,
        )


class CompanySiteManagementSettingsView(APIView):
    def get_object(self):
        instance = SiteManagementSettings.objects.filter(company_id=self.kwargs['company_id']).first()

        if not instance:
            try:
                company = Company.objects.get(id=self.kwargs['company_id'])
                SiteManagementSettings(company_id=company.id, minimum_tonnage=0.1).save()
                instance = SiteManagementSettings.objects.filter(company_id=self.kwargs['company_id']).first()
            except Company.DoesNotExist:
                raise Http404()  # pylint: disable=raise-missing-from
        return instance

    def get(self, _, company_id):  # pylint: disable=unused-argument
        return Response(self.get_object().to_dict(), status=status.HTTP_200_OK)

    def put(self, request, company_id):  # pylint: disable=unused-argument,too-many-branches
        sm_settings = self.get_object()
        prev_order_booking = sm_settings.order_booking
        if 'left_border_color_by_pits' in request.data:
            sm_settings.left_border_color_by_pits = request.data.get('left_border_color_by_pits')
        if 'order_booking' in request.data:
            sm_settings.order_booking = request.data.get('order_booking')
        if 'trailer_booking' in request.data:
            sm_settings.trailer_booking = request.data.get('trailer_booking')
        if 'minimum_tonnage' in request.data:
            sm_settings.minimum_tonnage = request.data.get('minimum_tonnage') or 0.1
        if 'trailer_booking_quantity' in request.data:
            sm_settings.trailer_booking_quantity = request.data.get('trailer_booking_quantity', None)
        if 'pickup_order_number_required' in request.data:
            sm_settings.pickup_order_number_required = request.data.get('pickup_order_number_required') or False
        if 'activity_log' in request.data:
            sm_settings.activity_log = request.data.get('activity_log') or False
        if 'inload_sms' in request.data:
            sm_settings.inload_sms = request.data.get('inload_sms') or False
        if 'load_by_load_transfer' in request.data:
            sm_settings.load_by_load_transfer = request.data.get('load_by_load_transfer') or False
        if 'customer_optional' in request.data:
            sm_settings.customer_optional = request.data.get('customer_optional') or False
        if 'delivery_order_number_required' in request.data:
            sm_settings.delivery_order_number_required = request.data.get('delivery_order_number_required') or False
        if 'checkpoint_order_creation_permission' in request.data:
            sm_settings.checkpoint_order_creation_permission = request.data.get(
                'checkpoint_order_creation_permission') or None

        for attr in [
                'statuses', 'fields', 'title_order', 'default_view', 'tooltip_order', 'pits'
        ]:
            setattr(sm_settings, attr, request.data.get(attr, getattr(sm_settings, attr, None)))

        sm_settings.save()
        if prev_order_booking != sm_settings.order_booking:
            Farm.objects.filter(
                company_id=company_id
            ).update(
                allow_inload_slot_order_booking=sm_settings.order_booking,
                allow_outload_slot_order_booking=sm_settings.order_booking,
                updated_at=timezone.now()
            )
        Company.objects.filter(id=sm_settings.company_id).update(updated_at=timezone.now())
        return Response(sm_settings.to_dict(), status=status.HTTP_200_OK)


class CompanyOrdersView(ListAPIView):
    serializer_class = FreightOrderMinimalWithCustomerProviderSerializer

    def get_serializer_context(self):
        return {"user": self.request.user}

    def get_queryset(self):
        from core.freights.models import FreightOrder

        params = self.request.query_params
        identifier = params.get('identifier', None)
        booking_start_date = params.get('booking_date', None) or params.get('booking_start_date', None)
        booking_end_date = params.get('booking_end_date', None) or booking_start_date
        queryset = FreightOrder.for_company_only_queryset(
            company_id=self.kwargs['company_id'],
            provider_id=params.get('provider_id', None),
            site_id=params.get('site_id', None),
            load_type=params.get('load_type', None),
            booking_start_date=booking_start_date,
            booking_end_date=booking_end_date,
        )
        if identifier:
            queryset = queryset.filter(identifier__iexact=identifier)
        return queryset


class CompanyOrdersSearchView(APIView):
    def get(self, _, company_id, search_str):  # pylint: disable=too-many-branches,too-many-statements, too-many-locals
        from core.freights.models import FreightOrder
        params = self.request.query_params
        booking_start_date = params.get('booking_date', None) or params.get('booking_start_date', None)
        booking_end_date = params.get('booking_end_date', None) or booking_start_date
        load_type = params.get('load_type', None)
        provider_id = params.get('provider_id', None)
        site_id = params.get('site_id', None)

        queryset = FreightOrder.for_company_only_queryset(
            company_id=company_id,
            provider_id=provider_id,
            apply_status_filter=False,
        ).exclude(status='void')
        filter_criteria = (models.Q(identifier__iexact=search_str) |
                           FreightOrder.search_by_identifier_criteria(search_str))
        queryset = FreightOrder.objects.filter(id__in=queryset.filter(filter_criteria).values_list('id', flat=True))
        count = queryset.count()

        res = {
            'errors': [], 'success': [], 'both_sites_order_booking_on': False,
            'order_id': None, 'unaccounted_tonnage': None, 'commodity_id': None,
        }

        if count == 0:
            res['errors'].append('This order is not valid')
        elif count > 1:
            res['errors'].append('Multiple orders exists with this input')
        else:
            original_queryset = queryset
            if load_type and site_id:
                queryset = FreightOrder.apply_site_filter(queryset, site_id, load_type)
                if queryset.count() == 0:
                    res['errors'].append('This order is not valid for this site')
                else:
                    res['success'].append('This order is valid for this site')
            else:
                if not load_type:
                    res['errors'].append('Please select load type')
                if not res.get('errors') and not site_id:
                    res['errors'].append('Please select site')
            if not res.get('errors') and booking_start_date:
                queryset = original_queryset
                queryset = FreightOrder.apply_date_filter(queryset, booking_start_date, booking_end_date)
                if queryset.count() == 0:
                    res['errors'].append('This order is not valid for this date')
                else:
                    res['success'].append('This order is valid for these dates')
            if not res.get('errors'):
                queryset = original_queryset
                if queryset.count() == 1:
                    order = queryset.first()
                    if not order.unassigned_tonnage > 0:
                        res['errors'].append('There is no tonnage left on this order please update the order tonnage')
                queryset = queryset.filter(status__in=['confirmed', 'open', 'in_progress', 'delivered', 'completed'])
                if not res.get('errors'):
                    if queryset.count() == 1 and queryset.first().status in ['delivered', 'completed']:
                        order = queryset.first()
                        order.unaccounted_tonnage = round(order.unaccounted_tonnage, 2)
                        if order.unaccounted_tonnage <= 0:
                            res['errors'].append(f"This order is already {order.status}.")
                    elif queryset.count() == 0:
                        res['errors'].append('This order is not in confirmed/open/in-progress status')
                    else:
                        res['success'].append('This order is in confirmed/open/in-progress status')
        if not res['errors'] and queryset.count() == 1:
            if order := queryset.first():
                is_both_site_order_booking = order.is_pickup_site_slot_order_booking_on and \
                                             order.is_delivery_site_slot_order_booking_on
                order.unaccounted_tonnage = round(order.unaccounted_tonnage, 2)
                res['both_sites_order_booking_on'] = is_both_site_order_booking
                res['order_id'] = order.id
                res['unaccounted_tonnage'] = order.unaccounted_tonnage
                res['commodity_id'] = order.commodity_id

                params = {}
                freight_pickup = get(order, 'freight_pickup')
                consignor_address = get(freight_pickup, 'consignor.handler.address')
                if consignor_address:
                    params['origin_latitude'] = get(consignor_address, 'latitude')
                    params['origin_longitude'] = get(consignor_address, 'longitude')

                freight_delivery = get(order, 'freight_delivery')
                consignee_address = get(freight_delivery, 'consignee.handler.address')
                if consignee_address:
                    params['destination_latitude'] = get(consignee_address, 'latitude')
                    params['destination_longitude'] = get(consignee_address, 'longitude')

                distance_and_time = GoogleMaps.get_distance(params, get(order, 'country_code'))
                if distance_and_time and distance_and_time.get('status') == 'OK':
                    res['distance'] = distance_and_time['distance']
                    res['duration'] = distance_and_time['duration']
                res['order_start_date'] = get(freight_pickup, 'date_time')
                res['order_end_date'] = get(freight_delivery, 'date_time')
            else:
                res['errors'].append('This order is not in confirmed/open/in-progress status')

        if queryset.count() == 1:
            order = queryset.first()
            order.unaccounted_tonnage = round(order.unaccounted_tonnage, 2)
            res[
                'both_sites_order_booking_on'
            ] = order.is_pickup_site_slot_order_booking_on and order.is_delivery_site_slot_order_booking_on
            freight_pickup = get(order, 'freight_pickup')
            freight_delivery = get(order, 'freight_delivery')
            consignor = get(freight_pickup, 'consignor.handler')
            consignee = get(freight_delivery, 'consignee.handler')
            res['order_id'] = order.id
            res['unaccounted_tonnage'] = order.unaccounted_tonnage
            res['unassigned_tonnage'] = order.unassigned_tonnage
            res['inferred_tonnage'] = order.inferred_tonnage
            res['delivered_tonnage'] = order.delivered_tonnage
            res['progress_tonnage'] = order.progress_tonnage
            res['total_tonnage_with_tolerance'] = order.total_tonnage_with_tolerance
            res['pickup_site'] = {}
            is_both_site_order_booking = order.is_pickup_site_slot_order_booking_on and \
                                         order.is_delivery_site_slot_order_booking_on
            if consignor:
                res['pickup_site'] = consignor.to_dict(
                    properties=['timezone', 'additional_mass_limit_codes', 'market_zone_state_code']
                )
                if is_both_site_order_booking:
                    sm_settings = consignor.sitemanagementsettings
                    if sm_settings:
                        res['pickup_site']['settings'] = sm_settings.to_dict()
            res['delivery_site'] = {}
            if consignee:
                res['delivery_site'] = consignee.to_dict(
                    properties=['timezone', 'additional_mass_limit_codes', 'market_zone_state_code']
                )
                if is_both_site_order_booking:
                    sm_settings = consignee.sitemanagementsettings
                    if sm_settings:
                        res['delivery_site']['settings'] = sm_settings.to_dict()
            res['can_user_amend'] = order.can_raise_amend_request(user=self.request.user)
            res['is_independent'] = order.is_independent
            max_allowed_tonnage, reason = order.max_allowed_tonnage()
            pickup_order = order.get_own_pickup_order()
            delivery_order = order.get_own_delivery_order()
            res['can_amend_related_entity'] = order.can_amend_entity(reason, self.request.user)
            res['max_allowed_tonnage_for_amend'] = max_allowed_tonnage
            res['reason'] = reason
            res['commodity_contract_id'] = get(order, 'commodity_contract_id')
            res['contract_number'] = get(order, 'commodity_contract.reference_number')
            res['pickup_order_id'] = get(pickup_order, 'id')
            res['pickup_order_identifier'] = get(pickup_order, 'identifier')
            res['delivery_order_id'] = get(delivery_order, 'id')
            res['delivery_order_identifier'] = get(delivery_order, 'identifier')
            res['parent_order_id'] = get(order, 'parent_order_id')
            res['parent_order_identifier'] = get(order, 'parent_order.identifier')
            res['order_start_date'] = get(freight_pickup, 'date_time')
            res['order_end_date'] = get(freight_delivery, 'date_time')
            res['spread'] = get(order, 'spread.details')
            if order.is_blended:
                res['is_blended'] = order.is_blended
                res['grade_name'] = get(order, 'grade_name')
                from core.contracts.serializers import ChemicalApplicationSerializer
                res['chemical_applications'] = ChemicalApplicationSerializer(
                    order.chemical_applications.filter(is_active=True), many=True
                ).data

        return Response(res, status=status.HTTP_200_OK)


class CompanyTransferAssetView(APIView):
    permission_classes = (AllowAny,)
    def put(self, request, company_id):
        asset_id = request.data.get('asset_id')
        asset = request.data.get('asset')
        include_parties = request.data.get('include_parties')
        asset_list = ['employee', 'truck', 'ngr', 'trailer', 'farm']

        try:
            if asset_id and asset in asset_list:
                asset_class = get_model_class_from_name(asset)
                asset_class.objects.filter(id=asset_id).update(company_id=company_id, updated_at=timezone.now())
                if asset == 'employee':
                    from rest_framework.authtoken.models import Token
                    KeyContact.objects.filter(employee_id=asset_id).delete()
                    Token.objects.filter(user_id=asset_id).delete()
                if asset in ['truck', 'ngr', 'farm']:
                    asset_class.objects.get(id=asset_id).update_relations(include_parties)
                    if asset == 'ngr':
                        ngr = Ngr.objects.get(id=asset_id)
                        ngr.merge_with_unknown_ngr()
                        ngr.recreate_warehouse_draft_invoices()
                return Response({'id': asset_id}, status=status.HTTP_200_OK)
            else:
                return Response(status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError as ex:
            exception_arg = get(ex, 'args[0]', '')
            if 'duplicate' in exception_arg and 'farms_name_uniq' in exception_arg:
                return Response({'error': 'Farm with this name already exists in this company'},
                                status=status.HTTP_400_BAD_REQUEST)
            return Response({'error': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class CompanyDirectoryTransferView(APIView):
    permission_classes = [IsSystemCompany]
    def put(self, request, company_id):
        data = request.data
        if data:
            if data.get('add', None):
                Company.add_companies_to_directory(company_id, data.get('add', []))
            if data.get('delete', None):
                Company.delete_companies_from_directory(
                    company_id, data.get('delete', [])
                )
            return Response(status=status.HTTP_200_OK)
        else:
            return Response(status=status.HTTP_400_BAD_REQUEST)


class CompanyCashPricesView(ListAPIView):
    serializer_class = CashPricesSerializer
    pagination_class = CustomPageNumberPagination
    def get_queryset(self):
        company_id = self.kwargs['company_id']
        cash_price_tab = self.kwargs['tab']
        user = self.request.user
        params = self.request.query_params.dict().copy()
        search_str = params.pop('search', None)
        params.pop('page', None)
        params.pop('page_size', None)
        order_by = params.pop('order_by', None)
        order = params.pop('order', None)
        cash_price = CashPrices.objects.none()
        common_filters = CommonFilters(user)
        filter_params = None
        if cash_price_tab == 'active':
            filter_params = common_filters.conditional_filtering(
                key='company_active_cash_price')
            cash_price = CashPrices.active.filter(
                buyer__company_id=company_id)
        elif cash_price_tab == 'site-active-prices':
            filter_params = common_filters.conditional_filtering(
                key='site_active_cash_price')
            cash_price = CashPrices.active.filter(
                site__company_id=company_id)
        elif cash_price_tab == 'archived':
            filter_params = common_filters.conditional_filtering(
                key='company_archived_cash_price')
            cash_price = CashPrices.archived.filter(
                buyer__company_id=company_id)
        elif cash_price_tab == 'siteArchived':
            filter_params = common_filters.conditional_filtering(
                key='site_archived_cash_price')
            cash_price = CashPrices.archived.filter(
                site__company_id=company_id)
        if cash_price_tab in ['active', 'archived']:
            cash_price = cash_price.filter(
                models.Q(site__company_id=user.company_id) | ~models.Q(
                    site__company__show_cash_prices_to_all=False) | models.Q(created_by__company_id=user.company_id)
            ).order_by('-updated_at')

        cash_price = cash_price.filter(filter_params) if filter_params is not None else cash_price
        if search_str:
            search_str = decode_string(search_str)
            cash_price = cash_price.filter(
                models.Q(status__icontains=search_str) |
                models.Q(status__icontains=search_str.replace(' ', '_')) |
                models.Q(buyer__company__business_name__icontains=search_str) |
                models.Q(start_date_time__icontains=search_str) |
                models.Q(end_date_time__icontains=search_str) |
                models.Q(commodity__name__icontains=search_str) |
                models.Q(grade__name__icontains=search_str) |
                models.Q(season__icontains=search_str) |
                models.Q(price__icontains=search_str) |
                models.Q(track__icontains=search_str) |
                models.Q(created_at__icontains=search_str) |
                models.Q(limit__icontains=search_str) |
                models.Q(site__name__icontains=search_str) |
                models.Q(site__company__business_name__icontains=search_str)
            )
        order_by_map = {
            'site_name': ['site__company__business_name'],
            'track': ['track'],
            'id': ['id'],
            'buyer.company_name': ['buyer__company__business_name'],
            'commodity_name': ['commodity__name'],
            'grade_name': ['grade__name'],
            'season': ['season'],
            'price': ['price'],
            'payment_term.name': ['payment_term__name'],
            'limit': ['limit'],
            'status': ['status']
        }

        if order_by:
            _order_by = order_by_map.get(
                inflection.underscore(order_by), ['created_at'])
            order = '-' if order == 'desc' else ''
            cash_price = cash_price.distinct(*_order_by, 'id')
            cash_price = cash_price.order_by(*[order + f for f in _order_by])

        return cash_price.select_related('site', 'commodity', 'grade', 'buyer', 'payment_term')


class CompanyContractBidsView(ListAPIView):
    serializer_class = ContractBidSerializer
    pagination_class = CustomPageNumberPagination
    def get_queryset(self):
        company_id = self.kwargs['company_id']
        bid_tab = self.kwargs['tab']
        user = self.request.user
        params = self.request.query_params.dict().copy()
        search_str = params.pop('search', None)
        params.pop('page', None)
        params.pop('page_size', None)
        order_by = params.pop('order_by', 'created_at')
        order = params.pop('order', 'desc')
        contract_bids = ContractBid.objects.none()
        common_filters = CommonFilters(user)
        filter_params = None
        if bid_tab == 'active':
            filter_params = common_filters.conditional_filtering(key='active_contract_bids')
            contract_bids = ContractBid.active.filter(commodity__country_id=user.country_id).filter(
                models.Q(group__isnull=True) |
                models.Q(group__isnull=False, group__companies=company_id)
            ).exclude(buyer__company_id=company_id)
        elif bid_tab == 'my_active':
            filter_params = common_filters.conditional_filtering(key='self_contract_bids')
            contract_bids = ContractBid.active.filter(created_by__company_id=company_id)
        elif bid_tab == 'archived':
            filter_params = common_filters.conditional_filtering(key='archived_contract_bids')
            contract_bids = ContractBid.archived.filter(created_by__company_id=company_id)

        contract_bids = contract_bids.filter(filter_params) if filter_params is not None else contract_bids

        if search_str:
            search_str = decode_string(search_str)
            contract_bids = contract_bids.filter(
                models.Q(status__icontains=search_str) |
                models.Q(status__icontains=search_str.replace(' ', '_')) |
                models.Q(buyer__company__business_name__icontains=search_str) |
                models.Q(start_date_time__icontains=search_str) |
                models.Q(end_date_time__icontains=search_str) |
                models.Q(commodity__name__icontains=search_str) |
                models.Q(grade__name__icontains=search_str) |
                models.Q(season__icontains=search_str) |
                models.Q(price__icontains=search_str) |
                models.Q(track__icontains=search_str) |
                models.Q(created_at__icontains=search_str) |
                models.Q(limit__icontains=search_str) |
                models.Q(site__name__icontains=search_str) |
                models.Q(site__company__business_name__icontains=search_str)
            )
        order_by_map = {
            'site_name': ['site__company__business_name'],
            'track': ['track'],
            'id': ['id'],
            'buyer.company_name': ['buyer__company__business_name'],
            'commodity_name': ['commodity__name'],
            'grade_name': ['grade__name'],
            'season': ['season'],
            'price': ['price'],
            'payment_term.name': ['payment_term__name'],
            'limit': ['limit'],
            'status': ['status']
        }
        if order_by:
            _order_by = order_by_map.get(
                inflection.underscore(order_by), ['created_at'])
            order = '-' if order == 'desc' else ''
            contract_bids = contract_bids.distinct(*_order_by, 'id')
            contract_bids = contract_bids.order_by(*[order + f for f in _order_by])

        return contract_bids.select_related('site', 'commodity', 'grade', 'buyer', 'payment_term', 'group')


class CompanyBrokersView(APIView):
    @staticmethod
    def get(_, company_id):
        company_ids = set(
            Farm.objects.filter(broker_company_id=company_id).values_list('company_id', flat=True))
        return Response({'companies': company_ids}, status=status.HTTP_200_OK)


class CreateBulkInvoiceNonProdProcessView(APIView):
    permission_classes = [IsNotProduction, AllowAny]
    def get(self, _):
        params = self.request.query_params.dict().copy()
        current_month = params.pop('current_month', None)
        from core.companies.tasks import create_warehouse_invoices
        create_warehouse_invoices(current_month == 'true', False)

        return Response(status=status.HTTP_200_OK)


class CompanyApprovedBuyersByABNView(APIView):

    def post(self, request, abn):
        try:
            company = Company.objects.get(abn=abn)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        try:
            abns = request.data['abns']
            company_ids = Company.objects.filter(abn__in=abns).values_list('id', flat=True)
            company.approved_buyers.add(*company_ids)
            return Response(status=status.HTTP_200_OK)
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class CompanyApprovedBuyersView(APIView):
    def get(self, _, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        return Response({'companyIds': company.approved_buyers.values_list('id', flat=True)}, status=status.HTTP_200_OK)

    def post(self, request, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        try:
            company_ids = request.data['company_ids']
            company.approved_buyers.add(*company_ids)
            return Response(status=status.HTTP_200_OK)
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, _, company_id):
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        try:
            params = self.request.query_params.dict().copy()
            company_id = params.pop('company_id', None)
            company.approved_buyers.remove(company_id)
            return Response(status=status.HTTP_200_OK)
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class CompanyApprovedBuyersListView(ListAPIView):
    serializer_class = CompanyApprovedBuyersSerializer
    pagination_class = CustomPageNumberPagination
    def get_queryset(self):

        try:
            company_id = self.kwargs['company_id']
            params = self.request.query_params.dict().copy()
            search_str = params.pop('search', None)
            order_by = params.pop('order_by', None)
            order = params.pop('order', None)
            params.pop('page', None)
            params.pop('page_size', None)
            company = Company.objects.filter(id=company_id).first()
            queryset = company.approved_buyers.all()
            if search_str:
                search_str = decode_string(search_str)
                queryset = queryset.filter(
                    models.Q(abn__icontains=search_str) |
                    models.Q(business_name__icontains=search_str) |
                    models.Q(entity_name__icontains=search_str) |
                    models.Q(mobile__icontains=search_str) |
                    models.Q(address__name__icontains=search_str) |
                    models.Q(address__address__icontains=search_str) |
                    models.Q(type__name__icontains=search_str) |
                    models.Q(website__icontains=search_str)
                ).order_by('business_name')

            order_by_map = {
                'name': ['business_name'],
                'abn': ['abn'],
                'id': ['id'],
                'mobile': ['mobile'],
                'website': ['website'],
                'address': ['address__name', 'address__address'],
                'business_type': ['type__name'],
            }

            if order_by:
                _order_by = order_by_map.get(
                    inflection.underscore(order_by), ['created_at'])
                order = '-' if order == 'desc' else ''
                queryset = queryset.order_by(*[order + f for f in _order_by])
            return queryset
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class CompaniesCSVView(APIView):
    @staticmethod
    def get(request):
        Download.orchestrate(
            module='companies',
            user=request.user,
            task='generate_companies_csv'
        )

        return Response(status.HTTP_200_OK)


class CompanySSOSettingView(RetrieveAPIView):
    permission_classes = [AllowAny]
    serializer_class = SSOSettingSerializer
    queryset = SSOSetting.objects.filter(is_active=True)
    def get_object(self):
        company = self.queryset.filter(company_id=self.kwargs['company_id'])
        return company.first() if company else None


class CompanySSOEmployeeUpsertView(APIView):
    permission_classes = [AllowAny]
    def post(self, request, **_):
        company = get_object_or_404(Company.objects.filter(id=self.kwargs['company_id']))
        username = request.data.get('username')
        jwt_token = request.data.pop('jwt_id_token', None)
        if not username or not jwt_token:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        employee = company.employee_set.filter(username=username).first()
        if not employee:
            employee = Employee.create({**request.data, 'company': company, 'password': generate_random_password()})

        if employee.persisted:
            employee.last_login = timezone.now()
            employee.save()
            return Response({
                'token': employee.refresh_token(),
                'user': employee.to_dict(
                    many_to_one_relations=['company__address&platformfeatures', 'type'],
                    properties=[
                        'my_company_logo_url',
                        'is_slot_booking_on',
                        'is_default_auth_enabled',
                        'country_code'
                    ]
                )
            })

        return Response(get(employee, 'errors', {}), status=status.HTTP_400_BAD_REQUEST)


class CompanySiteLoadsCSV(APIView):
    @staticmethod
    def get(request, company_id, company_site_id):  # pylint: disable=unused-argument
        Download.orchestrate(
            module='stocks',
            user=request.user,
            task='generate_company_site_loads_csv',
            task_params={
                'tz': request.META.get('HTTP_REFERER_TZ', request.user.country.timezone),
                'site_id': company_site_id,
                'query_params': request.query_params.copy()
            },
            file_name_prefix='Site Loads'
        )
        return Response(status=status.HTTP_200_OK)


class CompaniesPaymentRunEprLevyPreferencesView(APIView):
    def get_object(self):
        return get_object_or_404(Company.objects.filter(id=self.kwargs['company_id']))

    def get(self, request, company_id):  # pylint: disable=unused-argument
        company = self.get_object()
        result = company.get_payment_run_epr_levy_preference
        default_levy_epr = {'default_value': company.default_levy_epr}
        if result:
            result.update(default_levy_epr)
        else:
            result = default_levy_epr
        return Response(result, status=status.HTTP_200_OK)

    def put(self, request, company_id):  # pylint: disable=unused-argument
        company = self.get_object()
        company.upsert_payment_run_epr_levy_preferences(request.data)
        return Response(status=status.HTTP_200_OK)


class AbstractCompanyImpexDocsView(APIView):
    swagger_schema = None

    def get_object(self):
        company_id = self.kwargs.get('company_id')
        if not self.request.user.is_staff and self.request.user.company_id != company_id:
            raise PermissionDenied()

        return get_object_or_404(Company.objects.filter(id=company_id))


class CompanyImpexDocsAuthorizeView(AbstractCompanyImpexDocsView):

    def post(self, request, **kwargs):  # pylint: disable=unused-argument
        company = self.get_object()
        if not company.impex_docs_enabled:
            return Response({'error': 'Impex Docs is not enabled for this company'}, status=status.HTTP_400_BAD_REQUEST)
        result = ImpexDocsConnection.exchange_code(company, request.data)
        if get(result, 'access_token') and company.impex_docs_connection:
            company.impex_docs_connection.set_impex_docs_user()
        return Response({'is_connected': bool(get(result, 'access_token'))})


class CompanyImpexDocsConnectionView(AbstractCompanyImpexDocsView):
    def get(self, request, **kwargs):  # pylint: disable=unused-argument
        return Response({'valid': self.get_object().verify_impex_docs_connection})

    def put(self, request, **kwargs):  # pylint: disable=unused-argument
        company = self.get_object()
        connection = company.impex_docs_connection
        if not connection:
            return Response(status.HTTP_404_NOT_FOUND)
        try:
            result = connection.refresh()
        except Exception as e:  # pylint: disable=broad-except
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

        return Response(result)

    def delete(self, request, **kwargs):  # pylint: disable=unused-argument
        permitted = request.user.is_superuser or (
                request.user.is_company_admin and request.user.company_id == int(self.kwargs.get('company_id'))
        )
        if not permitted:
            raise PermissionDenied()

        company = self.get_object()
        connection = company.impex_docs_connection
        if not connection:
            return Response(status.HTTP_404_NOT_FOUND)
        connection.delete()
        return Response(status.HTTP_204_NO_CONTENT)


class CompanyImpexDocsProductMappingsView(AbstractCompanyImpexDocsView):
    serializer_class = ImpexDocsProductMappingSerializer

    def post(self, request, company_id):  # pylint: disable=unused-argument
        company = self.get_object()
        results = []
        payload = get(self.request.data, 'impex_docs_mappings', [])
        any_success = False
        for data in payload:
            try:
                mapping = ImpexDocsProductMapping.upsert({**data, 'company': company})
                if mapping.errors:
                    results.append({**data, 'errors': mapping.errors})
                else:
                    any_success = True
                    results.append(ImpexDocsProductMappingSerializer(mapping).data)
            except Exception as ex:  # pylint: disable=broad-except
                results.append({**data, 'errors': ex.message_dict})

        return Response(results, status=status.HTTP_201_CREATED if any_success else status.HTTP_400_BAD_REQUEST)

    def get(self, request, company_id):  # pylint: disable=unused-argument
        company = self.get_object()
        if company.is_impex_docs_account_linked:
            queryset = company.impex_product_mappings.filter()
            return Response(self.serializer_class(queryset, many=True).data, status=status.HTTP_200_OK)
        return Response(status=status.HTTP_200_OK)


class CompanyImpexDocsProductMasterView(AbstractCompanyImpexDocsView):

    def get(self, request, company_id):  # pylint: disable=unused-argument
        company = self.get_object()
        if company.has_impex_docs_connection:
            service = company.get_impex_docs_service(validate=True)
            result = service.get_product_master('status=active')
            if get(result, 'data'):
                filtered_data = list(map(lambda item: {
                    "product_id": get(item, "product_id"),
                    "name": get(item, "product_code"),
                    "product_description": get(item, "product_description")
                }, result['data']))
                return Response({'products': filtered_data}, status=status.HTTP_200_OK)
        return Response(status=status.HTTP_404_NOT_FOUND)


class CompanyImpexDocsProductMappingView(AbstractCompanyImpexDocsView, RetrieveAPIView, DestroyAPIView):
    serializer_class = ImpexDocsProductMappingSerializer

    def get_object(self):
        company = super().get_object()
        return get_object_or_404(company.impex_product_mappings.filter(id=self.kwargs.get('mapping_id')))

    def put(self, request, company_id, mapping_id):  # pylint: disable=unused-argument
        company = super().get_object()
        mapping = self.get_object()
        commodity_id = request.data.get('commodity_id')
        variety_id = request.data.get('variety_id')
        grade_id = request.data.get('grade_id')
        product_code = request.data.get('product_code')
        if (company.check_product_mapping_exists(commodity_id, variety_id, grade_id, mapping_id) or
                company.is_existing_product_code(product_code, mapping_id)):
            return Response({'error': 'Mapping already exists'},
                            status=status.HTTP_400_BAD_REQUEST)
        try:
            ImpexDocsProductMapping.update(instance=mapping, data=request.data)
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'error': str(ex)}, status=status.HTTP_400_BAD_REQUEST)

        return Response(status=status.HTTP_200_OK)


class AbstractCompanyXeroView(APIView):
    def get_object(self):
        company_id = self.kwargs.get('company_id')
        if not self.request.user.is_staff and self.request.user.company_id != company_id:
            raise PermissionDenied()

        return get_object_or_404(Company.objects.filter(id=company_id))


class CompanyXeroExchangeCodeView(AbstractCompanyXeroView):
    def post(self, request, **kwargs):  # upserts  # pylint: disable=unused-argument
        company = self.get_object()
        code = request.data.get('code')
        redirect_uri = request.data.get('redirect_uri')
        if not code or not redirect_uri:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        result = XeroConnection.exchange_code(company, {'code': code, 'redirect_uri': redirect_uri})
        if get(result, 'access_token') and get(company, 'xero_connection'):
            company.set_xero_tenant_id()
        return Response(result)


class CompanyXeroConnectionView(AbstractCompanyXeroView):
    def get(self, request, **kwargs):  # refreshes token using refresh_token  # pylint: disable=unused-argument
        return Response({'valid': self.get_object().verify_xero_connection})

    def put(self, request, **kwargs):  # refreshes token using refresh_token  # pylint: disable=unused-argument
        company = self.get_object()
        connection = XeroConnection.objects.filter(company=company).first()
        if not connection:
            return Response(status.HTTP_404_NOT_FOUND)
        try:
            result = connection.refresh()
        except Exception as e:  # pylint: disable=broad-except
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

        return Response(result)

    def delete(self, request, **kwargs):  # pylint: disable=unused-argument
        permitted = request.user.is_superuser or (
                request.user.is_company_admin and request.user.company_id == int(self.kwargs.get('company_id'))
        )
        if not permitted:
            raise PermissionDenied()

        company = self.get_object()
        connection = XeroConnection.objects.filter(company=company).first()
        if not connection:
            return Response(status.HTTP_404_NOT_FOUND)
        connection.delete()
        return Response(status.HTTP_204_NO_CONTENT)


class CompanyXeroFixturesView(AbstractCompanyXeroView):
    def get(self, request, **kwargs):  # pylint: disable=unused-argument
        company = self.get_object()
        connection = company.xero_connection
        if not connection:
            raise Http404()
        try:
            result = connection.get_accounts_and_items()
        except Exception as e:  # pylint: disable=broad-except
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

        return Response(result)


class CompanyXeroMappingsView(AbstractCompanyXeroView, ListAPIView):
    serializer_class = XeroMappingSerializer

    def post(self, request, company_id):  # pylint: disable=unused-argument
        company = self.get_object()

        results = []
        request_data = flatten([request.data.copy()])
        payload = get(request_data, '0.xero_mappings', [])
        tracking_categories = get(request_data, '0.tracking_categories', [])
        tracking_category_ids = None
        if tracking_categories:
            tracking_category_ids = XeroTrackingCategory.upsert(tracking_categories, company)
        any_success = False
        for data in payload:
            try:
                mapping = XeroMapping.upsert(
                    {**data, 'company': company, 'tracking_category_ids': tracking_category_ids}
                )
                if mapping.errors:
                    results.append({**data, 'errors': mapping.errors})
                else:
                    any_success = True
                    results.append(XeroMappingSerializer(mapping).data)
            except Exception as ex:  # pylint: disable=broad-except
                results.append({**data, 'errors': ex.message_dict})

        return Response(
            results, status=status.HTTP_201_CREATED if any_success else status.HTTP_400_BAD_REQUEST)

    def get_queryset(self):
        queryset = self.get_object().xero_mappings
        transaction_types = compact(self.request.query_params.get('transaction_type', '').split(','))
        commodity_ids = compact(self.request.query_params.get('commodity_id', '').split(','))
        if transaction_types:
            queryset = queryset.filter(transaction_type__in=transaction_types)
        if commodity_ids:
            queryset = queryset.filter(models.Q(commodity_id__in=commodity_ids) | models.Q(commodity__isnull=True))
        return queryset


class CompanyXeroMappingView(AbstractCompanyXeroView, RetrieveAPIView, DestroyAPIView):
    serializer_class = XeroMappingSerializer

    def get_object(self):
        company = super().get_object()
        return get_object_or_404(company.xero_mappings.filter(id=self.kwargs.get('mapping_id')))

    def put(self, request, _):
        mapping = self.get_object()

        try:
            XeroMapping.update(instance=mapping, data=request.data)
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'error': str(ex)}, status=status.HTTP_400_BAD_REQUEST)

        return Response(status=status.HTTP_200_OK)


class CompanyXeroTrackingCategoriesView(AbstractCompanyXeroView):
    def get(self, request, **kwargs):  # pylint: disable=unused-argument
        company = self.get_object()
        connection = company.xero_connection
        if not connection:
            raise Http404()
        try:
            result = connection.get_tracking_categories()
        except Exception as e:  # pylint: disable=broad-except
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

        return Response(result)


class CompaniesStockUploadCSV(APIView):
    @staticmethod
    def get(request):
        if request.user.is_staff:
            Download.orchestrate(
                module='stocks',
                user=request.user,
                task='generate_stocks_uploads_csv',
                task_params={
                    'query_params': {**request.query_params.copy(), 'country_id': get_request_country_id()}
                },
                file_name_prefix='Stocks Upload'
            )
            return Response(status=status.HTTP_200_OK)
        else:
            return Response(status=status.HTTP_401_UNAUTHORIZED)


class CompanyAcquisitionRecipientsView(APIView):
    def get_object(self):
        return get_object_or_404(Company.objects.filter(id=self.kwargs['company_id']))

    def get(self, request, company_id):  # pylint: disable=unused-argument
        company = self.get_object()
        acquisition_recipients = company.acquisition_recipients
        if acquisition_recipients:
            return Response(acquisition_recipients, status=status.HTTP_200_OK)
        return Response(status=status.HTTP_200_OK)

    def put(self, request, company_id):  # pylint: disable=unused-argument
        company = self.get_object()
        company.upsert_acquisition_recipients(request.data)
        return Response(company.to_dict(), status=status.HTTP_200_OK)


class CompanyTaggedNgrView(APIView):
    def get_object(self):
        return get_object_or_404(Company.objects.filter(id=self.kwargs['company_id']))

    def get(self, request, company_id, tag_name):  # pylint: disable=unused-argument
        company = self.get_object()

        if tag_name == 'warehouse_invoice' and \
                not Ngr.objects.filter(company_id=company_id, tags__contains=['warehouse_invoice']).exists():
            return Response({'ngr_id': company.get_tagged_ngr_or_logical('title_transfer')}, status=status.HTTP_200_OK)
        return Response({'ngr_id': company.get_tagged_ngr_or_logical(tag_name)}, status=status.HTTP_200_OK)


class CompanyExternalPortalMixin:
    serializer_class = ExternalPortalSerializer
    def get_permissions(self):
        if self.request.method == 'GET':
            return [IsSystemCompanyOrCompanyAdmin()]
        return [IsSuperUserOrCompanyAdmin()]


class CompanyExternalPortalsView(CompanyExternalPortalMixin, ListAPIView):
    def get_queryset(self):
        return ExternalPortal.objects.filter(company_id=self.kwargs['company_id'])

    def post(self, request, company_id):
        company = Company.objects.filter(id=company_id).first()
        self.check_object_permissions(request, company)

        portal = ExternalPortal(**request.data, company_id=company.id)
        portal.save()

        return Response(
            self.get_serializer(portal).data,
            status=status.HTTP_201_CREATED if portal.persisted else status.HTTP_400_BAD_REQUEST
        )


class CompanyExternalPortalView(CompanyExternalPortalMixin, RetrieveUpdateDestroyAPIView):
    lookup_url_kwarg = 'portal_id'

    def get_queryset(self):
        return ExternalPortal.objects.filter(
            company_id=self.kwargs['company_id'], id=self.kwargs['portal_id']
        )


class BHCListView(ListAPIView):
    queryset = Company.objects.filter(type_id=BHC_TYPE_ID)
    serializer_class = CompanyNamesSerializer


class CompanyStatsView(AbstractEntityStatsView):
    def get_queryset(self):
        exclude_company_ids = list(Company.get_test_company_ids())
        queryset = Company.objects.filter(is_active=True).exclude(id__in=exclude_company_ids)
        queryset = queryset.filter(country_id=get_request_country_id())
        return self.filter_queryset(queryset)

    def get_monthly_distribution(self, queryset):
        return queryset.annotate(
            month=TruncMonth(self.created_date_field)
        ).values('month').annotate(
            **self.get_aggregate_criteria()
        ).order_by('month').values(
            'month', *self.get_aggregate_criteria().keys()
        ) if queryset is not None else None

    def get_aggregate_criteria(self):
        return {
            'count': Count('id'),
            'registered': Count('id', filter=Q(owner_company_id__isnull=True)),
            'subscriber': Count('id', filter=Q(transaction_participation=True)),
            'unregistered': Count('id', filter=Q(owner_company_id__isnull=False))
        }


class CompanyApplicationRatesView(APIView):
    def get(self, _, company_id):
        try:
            company = Company.all.get(id=company_id)
        except Company.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        return Response(
            ApplicationRateSerializer(company.application_rates.filter(is_active=True), many=True).data,
            status=status.HTTP_200_OK
        )

    def post(self, request, company_id):
        try:
            is_redundant = ApplicationRate.is_duplicate(request.data.copy(), company_id)
            if is_redundant:
                return Response({'errors': 'Application Rate already exists'}, status=status.HTTP_400_BAD_REQUEST)
            application_rates = ApplicationRate.persist_many(request.data.copy(), company_id)
            return Response(
                ApplicationRateSerializer(application_rates, many=True).data, status=status.HTTP_201_CREATED
            )
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'details': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class CompanyApplicationRateView(APIView):
    def delete(self, _, application_rate_id):
        try:
            application_rate = ApplicationRate.objects.get(id=application_rate_id)
        except ApplicationRate.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        application_rate.mark_inactive()
        return Response(status=status.HTTP_204_NO_CONTENT)


class CompanyLookupByABNOrNGRView(ListAPIView):
    model = Company
    serializer_class = CompanyLookupSerializer
    permission_classes = (AllowAny, )
    def get_queryset(self):
        identifier = self.kwargs.get('identifier')
        if not identifier:
            raise Http404()

        companies = Company.objects.filter(abn__iexact=identifier.strip())
        if companies:
            return companies.order_by('business_name')

        ngr = Ngr.objects.filter(ngr_number__iexact=identifier.strip()).first()
        if not ngr:
            return Company.objects.none()

        company_ids = ngr.get_owner_company_ids()
        companies = Company.objects.filter(id__in=company_ids)
        return companies.order_by('business_name')


class CompanyEmployeeNamesView(APIView):
    permission_classes = (AllowAny, )
    def post(self, request, company_id):
        company_id = self.kwargs.get('company_id')
        employee_email = request.data.get('email')

        if not company_id or not employee_email:
            return Response({'error': 'Company ID and Employee Email are required'}, status=status.HTTP_400_BAD_REQUEST)

        employees = Employee.objects.filter(
            company_id=company_id, email=employee_email
        ).exclude(type_id=OBSERVER_TYPE_ID).order_by('created_at')

        serializer = CompanyEmployeeNamesSerializer(employees, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class CompanyHistoryView(BaseHistoryView):
    model = Company
    lookup_url_kwarg = 'company_id'

    def get_object(self):
        company_id = self.kwargs.get('company_id')
        if company_id in SYSTEM_COMPANY_IDS:
            return Company.moderators.get(id=company_id)
        return super().get_object()


class CompanyPowerBIReportView(APIView):
    def get(self, request, company_id):  # pylint: disable=unused-argument
        company = get_object_or_404(Company.objects.filter(id=company_id))
        user = self.request.user
        if not user.is_staff and not user.company_id == company.id:
            raise Http403()

        if not company.power_bi_report_id:
            return Response(
                {'error': 'Power BI report not configured for this company'},
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response({'report': company.get_powerbi_report_metadata()}, status=status.HTTP_200_OK)


class CompanyCommoditiesMostUsedListView(APIView):
    permission_classes = (IsStaffOrCompanyUser,)

    def get(self, _, company_id):  # pylint: disable=useless-option-value
        instance = get_object_or_404(Company.objects.filter(id=company_id))
        self.check_object_permissions(self.request, instance)
        return Response(
            instance.get_most_used_commodity_ids(),
            status=status.HTTP_200_OK
        )


class CompanyCommoditiesSpecsMostUsedListView(APIView):
    permission_classes = (IsStaffOrCompanyUser,)

    def get_object(self):
        instance = get_object_or_404(Company.objects.filter(id=self.kwargs['company_id']))
        self.check_object_permissions(self.request, instance)
        return instance

    def get(self, _, company_id):  # pylint: disable=useless-option-value,unused-argument
        return Response(
            self.get_object().get_most_used_specs_by_commodity(self.request.query_params.get('commodity_id', None)),
            status=status.HTTP_200_OK
        )
