# Generated by Django 5.1.8 on 2025-07-16 07:03

from django.db import migrations


def merge_upload_empty_container_and_customised_pack_ship_settings(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    Company.objects.filter(upload_empty_containers=True).update(customised_pack_and_ship=True)


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0224_company_customised_pack_and_ship_and_more'),
    ]

    operations = [
        migrations.RunPython(merge_upload_empty_container_and_customised_pack_ship_settings)
    ]
