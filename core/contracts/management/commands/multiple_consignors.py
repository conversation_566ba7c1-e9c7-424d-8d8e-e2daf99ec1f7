from django.core.management.base import BaseCommand
from core.contracts.models import ContractAcceptanceRequest
from django.db.models import Q


class Command(BaseCommand):
    help = 'change consignor to consignors and consignee to consignees'

    def handle(self, *args, **kwargs):  # pylint: disable=unused-argument
        requests = ContractAcceptanceRequest.objects.filter(type='amend').filter(
            Q(args__consignor__isnull=False) | Q(args__consignee__isnull=False)
        )
        for request in requests:
            try:
                if 'consignor' in request.args.keys():
                    existing_consignor = request.args.pop('consignor')
                    request.args['consignors'] = [existing_consignor] if existing_consignor else []
                if 'consignee' in request.args.keys():
                    existing_consignee = request.args.pop('consignee')
                    if 'consignees' in request.args.keys():
                        request.args['consignees'].append(
                            existing_consignee
                        ) if existing_consignee not in request.args['consignees'] else None
                        print("Appended for", request.id)
                    else:
                        request.args['consignees'] = [existing_consignee] if existing_consignee else []
                        print("Added consignees for", request.id)
                request.save()
                print("Success for", request.id)
            except Exception as ex:  # pylint: disable=broad-except
                print("Not updated for", request.id)
                print("Error", ex.args[0])
