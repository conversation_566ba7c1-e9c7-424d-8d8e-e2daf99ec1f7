# Generated by Django 4.2.15 on 2025-02-07 04:20

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0201_remove_company_impex_docs_client_id_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='impexdocsconnection',
            old_name='impex_docs_client_id',
            new_name='client_id',
        ),
        migrations.RenameField(
            model_name='impexdocsconnection',
            old_name='impex_docs_client_secret',
            new_name='client_secret',
        ),
        migrations.RenameField(
            model_name='impexdocsconnection',
            old_name='impex_docs_user_email',
            new_name='user_email',
        ),
        migrations.RenameField(
            model_name='impexdocsconnection',
            old_name='impex_docs_user_id',
            new_name='user_id',
        ),
    ]
