# Generated by Django 4.2.15 on 2024-09-24 05:33

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0055_grade_gta_code_historicalgrade_gta_code'),
    ]

    operations = [
        migrations.RunSQL('CREATE INDEX idx_commodities_name_trgm ON commodities USING gin(upper(name) gin_trgm_ops);'),
        migrations.RunSQL('CREATE INDEX idx_grades_name_trgm ON commodity_grades USING gin(upper(name) gin_trgm_ops);'),
        migrations.RunSQL('CREATE INDEX idx_varieties_name_trgm ON commodity_varieties USING gin(upper(name) gin_trgm_ops);'),
    ]
