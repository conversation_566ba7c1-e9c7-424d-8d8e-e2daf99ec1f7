from django.db import models

from core.common.models import BaseModel
from core.countries.constants import AUSTRALIA_COUNTRY_ID


class Bank(BaseModel):
    class Meta:
        db_table = 'banks'
        ordering = ['name']
        unique_together = ('name', 'code', 'bsb_number')
        indexes = [
            models.Index(fields=['country_id', 'name']),
        ]

    FILLABLES = ['name, country_id']

    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, null=True, blank=True)
    bsb_number = models.CharField(max_length=15, null=True, blank=True)
    head_branch = models.BooleanField(default=False)
    country = models.ForeignKey('countries.Country', on_delete=models.SET_DEFAULT, default=AUSTRALIA_COUNTRY_ID)

    @classmethod
    def find_branch(cls, code, bsb_number=None):
        queryset = cls.find_by_bsb_number(bsb_number, code) or cls.find_head_branch(code) or cls.find_by_code(code)
        return queryset.first()

    @classmethod
    def find_by_bsb_number(cls, bsb_number, code=None):
        if not bsb_number:
            return cls.objects.none()
        if not code:
            return cls.objects.filter(bsb_number=bsb_number)
        return cls.objects.filter(code=code, bsb_number=bsb_number)

    @classmethod
    def find_head_branch(cls, code):
        return cls.objects.filter(code=code, head_branch=True)

    @classmethod
    def find_by_code(cls, code):
        return cls.objects.filter(code=code)
