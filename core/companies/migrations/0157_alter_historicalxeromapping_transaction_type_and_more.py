# Generated by Django 4.1.13 on 2023-12-21 05:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0156_company_fill_docket_historicalcompany_fill_docket'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalxeromapping',
            name='transaction_type',
            field=models.CharField(choices=[('purchase_contract', 'Purchase Contract'), ('sale_contract', 'Sale Contract'), ('deductions', 'Deductions'), ('warehouse', 'Warehouse'), ('freight_invoice_payable', 'Freight Invoices Payable'), ('freight_invoice_receivable', 'Freight Invoices Receivable')], max_length=100),
        ),
        migrations.AlterField(
            model_name='xeromapping',
            name='transaction_type',
            field=models.CharField(choices=[('purchase_contract', 'Purchase Contract'), ('sale_contract', 'Sale Contract'), ('deductions', 'Deductions'), ('warehouse', 'Warehouse'), ('freight_invoice_payable', 'Freight Invoices Payable'), ('freight_invoice_receivable', 'Freight Invoices Receivable')], max_length=100),
        ),
    ]
