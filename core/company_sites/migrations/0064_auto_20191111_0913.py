# Generated by Django 2.1.9 on 2019-11-11 09:13

from django.db import migrations

def update_corn_commodity_id(apps, schema_editor):
    FreightSlot = apps.get_model('company_sites', 'FreightSlot')
    HistoricalFreightSlot = apps.get_model('company_sites', 'HistoricalFreightSlot')
    FreightSlot.objects.filter(commodity_id=5030).update(commodity_id=41)
    HistoricalFreightSlot.objects.filter(commodity_id=5030).update(commodity_id=41)



class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0063_auto_20191029_1126'),
    ]

    operations = [
        migrations.RunPython(update_corn_commodity_id)
    ]
