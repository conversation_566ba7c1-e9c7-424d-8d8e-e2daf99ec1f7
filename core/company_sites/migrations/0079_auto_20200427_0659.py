# Generated by Django 2.2 on 2020-04-27 06:59

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0078_auto_20200421_0900'),
    ]

    operations = [
        migrations.AddField(
            model_name='slotcomment',
            name='archived',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='slotcomment',
            name='seen_by',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), default=list, size=None),
        ),
    ]
