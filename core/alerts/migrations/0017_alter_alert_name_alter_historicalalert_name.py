# Generated by Django 4.1.9 on 2023-07-13 05:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0016_alter_historicalrecipient_party_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='alert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit Breach'), ('inload_done', 'Delivery Confirmation - SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Confirmation - Email'), ('delivery_information', 'Delivery Confirmation - Email'), ('outload_report', 'Outload Report'), ('slot_booking_alert', 'Slot Booking Updates'), ('truck_cleanliness_declaration_alert', 'Truck Cleanliness Declaration Not Filled')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalalert',
            name='name',
            field=models.Char<PERSON>ield(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit Breach'), ('inload_done', 'Delivery Confirmation - SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Confirmation - Email'), ('delivery_information', 'Delivery Confirmation - Email'), ('outload_report', 'Outload Report'), ('slot_booking_alert', 'Slot Booking Updates'), ('truck_cleanliness_declaration_alert', 'Truck Cleanliness Declaration Not Filled')], max_length=100),
        ),
    ]
