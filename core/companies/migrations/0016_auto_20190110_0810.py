# Generated by Django 2.1.3 on 2019-01-10 08:10

from django.db import migrations

def farm_directory_updates(apps, schema_editor):
    Farm = apps.get_model('farms', 'Farm')
    FarmDirectory = apps.get_model('companies', 'FarmDirectory')
    Farm.objects.filter(company__owner_company_id__isnull=False).update(
        office_id=None,
        broker_company_id=None
    )
    reg_managed_farms = Farm.objects.filter(
        office_id__isnull=False,
        broker_company_id__isnull=False,
        company__owner_company_id__isnull=True,
    )
    for farm in reg_managed_farms:
        if not FarmDirectory.objects.filter(farm_id=farm.id, company_id=farm.broker_company_id).exists():
            directory = FarmDirectory(farm_id=farm.id, company_id=farm.broker_company_id)
            directory.save()

class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0015_auto_20190102_0846'),
    ]

    operations = [
        migrations.RunPython(farm_directory_updates),
    ]
