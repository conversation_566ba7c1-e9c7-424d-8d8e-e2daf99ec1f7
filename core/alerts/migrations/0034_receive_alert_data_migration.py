# Generated by Django 5.1.8 on 2025-06-16 08:51

import logging
from django.db import migrations
from django.db.models import Q
from pydash import get
from core.common.constants import (
    DRIVER_TYPE_ID, COMPANY_ADMIN_TYPE_ID, LOGISTICS_TYPE_ID,
    FARM_ADMIN_TYPE_ID, OFFICE_EMPLOYEE_TYPE_ID, FARM_EMPLOYEE_TYPE,
    OFFICE_ADMIN_TYPE_ID
)

KEY_CONTACT_ROLE = 'key_contact'
COMPANY_ADMIN_ROLE = 'company_admin'
SITE_ADMIN_ROLE = 'site_admin'
OFFICE_ADMIN_ROLE = 'office_admin'
OFFICE_EMPLOYEE_ROLE = 'office_employee'
SITE_EMPLOYEE_ROLE = 'site_employee'
DRIVER_ROLE = 'driver'

ROLES = {
    SITE_ADMIN_ROLE: FARM_ADMIN_TYPE_ID,
    COMPANY_ADMIN_ROLE: COMPANY_ADMIN_TYPE_ID,
    KEY_CONTACT_ROLE: None,
    OFFICE_ADMIN_ROLE: OFFICE_ADMIN_TYPE_ID,
    OFFICE_EMPLOYEE_ROLE: OFFICE_EMPLOYEE_TYPE_ID,
    SITE_EMPLOYEE_ROLE: FARM_EMPLOYEE_TYPE,
    DRIVER_ROLE: DRIVER_TYPE_ID,
}

def get_alert_mapping():
    return [
        {
            'type_ids': [DRIVER_TYPE_ID],
            'alerts': ['pickup_information', 'delivery_information', 'slot_booking_alert'],
            'roles': ['freight_provider', 'sub_freight_provider']
        },
        {
            'company_type_ids': [LOGISTICS_TYPE_ID],
            'alerts': ['slot_booking_alert', 'pickup_information', 'delivery_information'],
            'roles': ['freight_provider', 'sub_freight_provider']
        }
    ]

def get_employees(apps, alert, employee_roles):
    type_ids = []
    recipient_company = alert.company
    for role in employee_roles:
        if role != KEY_CONTACT_ROLE:
            type_ids.append(ROLES.get(role))

    employee_ids = list(recipient_company.employee_set.filter(type_id__in=type_ids).values_list('id', flat=True))

    if KEY_CONTACT_ROLE in employee_roles and alert.company_id:
        from core.key_contacts.models import KeyContact
        key_contact_employee_id = KeyContact.get_for_company(alert.company_id, recipient_company.id)
        employee_ids.append(key_contact_employee_id) if key_contact_employee_id else None
    return employee_ids

def create_employee_alert_data(apps, schema_editor):
    logger = logging.getLogger(__name__)
    logger.info("Starting data migration for employee alerts...")

    SendAlert = apps.get_model('alerts', 'Alert')
    ReceiveAlert = apps.get_model('alerts', 'ReceiveAlert')
    Recipient = apps.get_model('alerts', 'Recipient')
    SiteRecipient = apps.get_model('alerts', 'SiteRecipient')
    Employee = apps.get_model('profiles', 'Employee')
    EmployeeAlert = apps.get_model('alerts', 'EmployeeAlert')

    BATCH_SIZE = 1000

    alert_mapping = get_alert_mapping()
    default_config =  {
            'is_active': True,
            'include_internal_sites': True,
            'include_external_sites': True,
    }
    alerts = SendAlert.objects.filter(
        name__in=['slot_booking_alert', 'pickup_information', 'delivery_information']
    )
    receive_alert_lookup = {
        alert.name: alert for alert in ReceiveAlert.objects.all()
    }

    employee_alert_map = {}
    processed_employees = set()

    config_filters = Q()

    for config in alert_mapping:
        current_config_filter = Q()

        if 'type_ids' in config and 'company_type_ids' in config:
            current_config_filter = (
                Q(type__id__in=config['type_ids']) &
                Q(company__type__id__in=config['company_type_ids'])
            )
        elif 'type_ids' in config:
            current_config_filter = Q(type__id__in=config['type_ids'])
        elif 'company_type_ids' in config:
            current_config_filter = Q(company__type__id__in=config['company_type_ids'])

        config_filters |= current_config_filter

    mapped_employees_query = Employee.objects.filter(config_filters).select_related('type', 'company')

    for employee in mapped_employees_query.iterator(chunk_size=BATCH_SIZE):
        if employee.is_staff:
            continue

        processed_employees.add(employee.id)

        for config in alert_mapping:
            matches = True
            if 'type_ids' in config and get(employee,'type.id') not in config['type_ids']:
                matches = False
            if 'company_type_ids' in config and get(employee, 'company.type.id') not in config['company_type_ids']:
                matches = False

            if matches:
                for alert_name in config['alerts']:
                    receive_alert = receive_alert_lookup.get(alert_name)
                    key = f"{employee.id}-{receive_alert.id}"

                    if key not in employee_alert_map:
                        employee_alert_map[key] = EmployeeAlert(
                            employee=employee,
                            alert=receive_alert,
                            roles=config['roles'],
                            **default_config
                        )
                    else:
                        existing_alert = employee_alert_map[key]
                        existing_roles = set(existing_alert.roles)
                        new_roles = set(config['roles'])
                        existing_alert.roles = list(existing_roles.union(new_roles))

    recipient_ids_from_site = SiteRecipient.objects.filter(
        alert__in=alerts
    ).values_list('recipient_id', flat=True)

    recipients_query = Recipient.objects.filter(
        Q(alert__in=alerts) | Q(id__in=recipient_ids_from_site),
        party='own_company',
        alert__is_active=True
    ).select_related('alert').prefetch_related('employees')

    for recipient in recipients_query:
        alert = recipient.alert
        alert_name = get(alert, 'name')
        receive_alert = receive_alert_lookup.get(alert_name)

        employee_ids = get_employees(apps, recipient.alert, recipient.employee_roles)
        company_employees = Employee.objects.filter(
            id__in=employee_ids, is_staff=False
        )

        employees_to_process = set(recipient.employees.all()) | set(company_employees)

        for employee in employees_to_process:
            if employee.is_staff:
                continue

            processed_employees.add(employee.id)
            key = f"{employee.id}-{receive_alert.id}"

            role = recipient.party
            if alert_name == 'pickup_information' and recipient.party == 'own_company':
                role = 'pickup_site'
            elif alert_name == 'delivery_information' and recipient.party == 'own_company':
                role = 'delivery_site'
            if key not in employee_alert_map:
                employee_alert_map[key] = EmployeeAlert(
                    employee=employee,
                    alert=receive_alert,
                    roles=[role],
                    **default_config
                )
            else:
                existing_alert = employee_alert_map[key]
                existing_roles = set(existing_alert.roles)
                existing_roles.add(role)
                existing_alert.roles = list(existing_roles)

    if employee_alert_map:
        alerts_to_create = list(employee_alert_map.values())
        no_of_alerts_to_create = len(alerts_to_create)
        for i in range(0, len(alerts_to_create), BATCH_SIZE):
            batch = alerts_to_create[i:i + BATCH_SIZE]
            EmployeeAlert.objects.bulk_create(batch)
        logger.info(f"Number of Employee Alerts created: {no_of_alerts_to_create}")

    logger.info("Data migration for employee alerts completed.")

def remove_send_alert_data(apps, schema_editor):
    SendAlert = apps.get_model('alerts', 'Alert')
    Recipient = apps.get_model('alerts', 'Recipient')
    SiteRecipient = apps.get_model('alerts', 'SiteRecipient')
    ALERTS = [
        'slot_booking_alert',
        'pickup_information',
        'delivery_information'
    ]
    alerts_to_delete = SendAlert.objects.filter(name__in=ALERTS)
    recipients = Recipient.objects.filter(alert__in=alerts_to_delete)
    site_recipients = SiteRecipient.objects.filter(alert__in=alerts_to_delete)
    recipients.delete()
    site_recipients.delete()
    alerts_to_delete.delete()

class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0033_add_receive_alerts'),
    ]

    operations = [
    ]
