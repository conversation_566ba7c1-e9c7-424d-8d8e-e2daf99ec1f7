# Generated by Django 4.1.13 on 2024-06-12 11:35

from django.db import migrations
from core.contracts.constants import (
    CONTRACT_DOCUMENT_TYPE_ID,
    BROKER_NOTE_DOCUMENT_TYPE_ID,
    SALES_CONFIRMATION_DOCUMENT_TYPE_ID
)

document_types = [
    {
        'pk': CONTRACT_DOCUMENT_TYPE_ID,
        'name': 'contract'
    },
    {
        'pk': BROKER_NOTE_DOCUMENT_TYPE_ID,
        'name': 'broker_note'
    },
    {
        'pk': SALES_CONFIRMATION_DOCUMENT_TYPE_ID,
        'name': 'sales_confirmation'
    },
]

def update_document_type_ids_for_contracts(apps, schema_editor):
    Contract = apps.get_model('contracts', 'Contract')
    ContractDocumentTypes = apps.get_model('contracts', 'ContractDocumentType')

    document_type_mapping = {
        'contract': CONTRACT_DOCUMENT_TYPE_ID,
        'broker_note': BR<PERSON><PERSON>_NOTE_DOCUMENT_TYPE_ID,
        'sales_confirmation': SALES_CONFIRMATION_DOCUMENT_TYPE_ID
    }

    for document_type in document_types:
        ContractDocumentTypes.objects.get_or_create(
            pk=document_type['pk'],
            name = document_type['name']
        )

    #Updating Contract Document Type IDs for contracts
    for document_type_name, new_document_type_id in document_type_mapping.items():
        contracts_to_update = Contract.objects.filter(document_type__name=document_type_name)
        contracts_to_update.update(document_type_id=new_document_type_id)

    #Deleting the old document types data
    contract_document_types = ContractDocumentTypes.objects.exclude(id__in=[12, 13, 14])
    contract_document_types.delete()

class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0208_alter_contractdocumenttype_company_type'),
    ]

    operations = [
        migrations.RunPython(update_document_type_ids_for_contracts)
    ]