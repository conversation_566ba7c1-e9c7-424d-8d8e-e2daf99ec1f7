# Generated by Django 2.2.13 on 2020-07-20 05:18

from django.db import migrations

SLOT_RESTRICTED_COLOR = "#9d9d9d"
RESTRICTED_STATUS_SETTINGS = {
    'id': 'restricted',
    'name': 'Restricted',
    'label': 'Restricted',
    'color': SLOT_RESTRICTED_COLOR,
}


def update_site_management_settings_status(apps, schema_editor):
    SiteManagementSettings = apps.get_model('company_sites', 'SiteManagementSettings')
    for setting in SiteManagementSettings.objects.all():
        setting.statuses.append(RESTRICTED_STATUS_SETTINGS)
        setting.save()


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0084_auto_20200720_0516'),
    ]

    operations = [
        migrations.RunPython(update_site_management_settings_status)
    ]
