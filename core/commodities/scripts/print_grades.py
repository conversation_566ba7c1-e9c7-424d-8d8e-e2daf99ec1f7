import csv
import json

res = []
count = 1

#Wheat
_w_res = []
_specs = [{'order': 1, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 2, 'code': 'PRGR', 'name': 'Protein (%)', 'min': 0.0, 'max': 20.0}, {'order': 3, 'code': 'TWT', 'name': 'Test Weight (kg/hl)', 'min': 40.0, 'max': 90.0}, {'order': 4, 'code': 'SCRN', 'name': 'Screenings (% by weight)', 'min': 0.0, 'max': 100.0}, {'order': 5, 'code': 'UNM', 'name': 'Unmillable Material Above the Screen (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'FALL', 'name': 'Falling Number (seconds)', 'min': 0.0, 'max': 700.0}]
grades = [
    ("APH1","AH14","APH2","H1","H2","AUH2","APW1","ASW1","AGP1","HPS1","SFW1","AH9","FED1","FED2","FED3"),
    ("ANW1", "ANW2", "ASWS", "SFE1", "SFE2", "SGP1", "SGP2", "FED1", "FED2", "FED3",),
    ("DR1", "DR2", "DR3", "FED1", "FED2", "FED3",),
]
_files = ['./WheatGeneralGrades.csv', './WheatSoftGrades.csv', './WheatDurumGrades.csv']
for idx, category in enumerate(["General", "Soft", "Durum"]):
    csvfile = open(_files[idx], "r")
    fieldnames = ("Grades","MOGR","PRGR", "TWT","SCRN","UNM","FALL")
    current_grades = grades[idx]
    for _gr in current_grades:
        _json = {
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": _gr,
                "category": category,
                "specs": [],
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 1,
                "created_by": 1,
                "updated_by": 1,
            }
        }
        _w_res.append(_json)
        count += 1
    reader = csv.DictReader(csvfile, fieldnames)

    for i, row in enumerate(reader):
        if(i!=0):
            _dict = dict(row)
            _grade_parts = _dict['Grades'].split(" ")
            _grade_name = _grade_parts[0]
            _bound = _grade_parts[1].lower()
            _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name and _j['fields']['category'] == category , _w_res))[0]
            _js["fields"]["order"] = current_grades.index(_grade_name) + 1
            for k, v in _dict.items():
                if(k != 'Grades'):
                    _default_spec = list(filter(lambda _s: _s['code'] == k, _specs))[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_w_res.append({
    "model": "commodities.grade",
    "pk": count,
    "fields": {
        "name": 'UNGRADED',
        "category": category,
        "specs": _specs,
        "order": 32,
        "created_at": "2018-04-02T08:00:00+00:00",
        "commodity_id": 1,
    }
})
count += 1
res+=_w_res
#Barley
_specs = [{'order': 1, 'code': 'VARP', 'name': 'Varietal Purity (% by count)', 'min': 0.0, 'max': 100.0}, {'order': 2, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 3, 'code': 'PRGR', 'name': 'Protein (%)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'TWT', 'name': 'Test Weight (kg/hl)', 'min': 50.0, 'max': 100.0}, {'order': 5, 'code': 'RET', 'name': 'Retention minimum (% by weight)', 'min': 0.0, 'max': 100.0}, {'order': 6, 'code': 'SCRN', 'name': 'Screenings (% by weight)', 'min': 0.0, 'max': 100.0}]
_b_res  = []
grades = ("Grade1", "Grade2", "Grade3", "F1", "F2", "F3",)
csvfile = open('./BarleyGrades.csv', "r")
fieldnames = ("Grades","VARP","MOGR","PRGR","TWT","RET","SCRN")
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-04-02T08:00:00+00:00",
            "commodity_id": 2,
        }
    }
    _b_res.append(_json)
    count += 1

for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = _grade_parts[0]
        _bound = _grade_parts[1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _b_res))[0]
        _js["fields"]["order"] = i
        for k, v in _dict.items():
            if(k != 'Grades'):
                _default_spec = list(filter(lambda _s: _s['code'] == k, _specs))[0]
                _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                if not _existing_spec:
                    _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                    _data[_bound.lower()] = float(v)
                    _js["fields"]["specs"].append(_data)
                else:
                    _existing_spec[0][_bound] = float(v)
_b_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 2,
            }
        })
res+=_b_res
count += 1
#Canola
_c_res=[]
_specs = [{'order': 1, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 2, 'code': 'PRGR', 'name': 'Protein (%)', 'min': 0.0, 'max': 20.0}, {'order': 3, 'code': 'COIL', 'name': 'Oil (%)', 'min': 0.0, 'max': 50.0}, {'order': 4, 'code': 'TWT', 'name': 'Test Weight (kg/hl)', 'min': 50.0, 'max': 100.0}, {'order': 5, 'code': 'IMPU', 'name': 'Impurities (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'BRSH', 'name': 'Split or Broken (%)', 'min': 0.0, 'max': 20.0}]
grades = ("CAN", "CANG")
csvfile = open('./CanolaGrades.csv', "r")
fieldnames = ("Grades","MOGR","PRGR","COIL","TWT","IMPU","BRSH")
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-04-02T08:00:00+00:00",
            "commodity_id": 3,
        }
    }
    _c_res.append(_json)
    count += 1

for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = _grade_parts[0]
        _bound = _grade_parts[1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _c_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1

        for k, v in _dict.items():
            if(k != 'Grades'):
                _default_spec = list(filter(lambda _s: _s['code'] == k, _specs))[0]
                _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                if not _existing_spec:
                    _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                    _data[_bound.lower()] = v
                    _js["fields"]["specs"].append(_data)
                else:
                    _existing_spec[0][_bound] = v
_c_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 3,
            }
        })
res+=_c_res
count += 1
#Lentils
_l_res = []
_specs = [{'order': 1, 'code': 'VARP', 'name': 'Varietal Purity (% by count)', 'min': 90.0, 'max': 100.0}, {'order': 2, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 3, 'code': 'DEFG', 'name': 'Total Defective (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'PSCC', 'name': 'Poor Seed Coat Colour (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 5, 'code': 'PKC', 'name': 'Poor Kernel Colour (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'ODEF', 'name': 'Other Defective max (% by weight)', 'min': 0.0, 'max': 20.0}]
grades = ("LRS1", "LRS2",)
csvfile = open('./LentilsGrades.csv', "r")
fieldnames = ("Grades","VARP","MOGR","DEFG","PSCC","PKC","ODEF")
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-04-02T08:00:00+00:00",
            "commodity_id": 5,
        }
    }
    _l_res.append(_json)
    count += 1

for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = _grade_parts[0]
        _bound = _grade_parts[1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _l_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1

        for k, v in _dict.items():
            if(k != 'Grades'):
                _default_spec = list(filter(lambda _s: _s['code'] == k, _specs))[0]
                _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                if not _existing_spec:
                    _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                    _data[_bound.lower()] = float(v)
                    _js["fields"]["specs"].append(_data)
                else:
                    _existing_spec[0][_bound] = float(v)
_l_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 5,
            }
        })
res+=_l_res
count += 1
#Sorghum
_s_res = []
_specs = [{'order': 1, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 2, 'code': 'TWT', 'name': 'Test Weight (kg/hl)', 'min': 50.0, 'max': 100.0}, {'order': 3, 'code': 'FGNM', 'name': 'Foreign Material (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'SCRN', 'name': 'Screenings (% by weight)', 'min': 0.0, 'max': 100.0}, {'order': 5, 'code': 'DFCT', 'name': 'Total Defective (% by count)', 'min': 0.0, 'max': 50.0}, {'order': 6, 'code': 'FFUN', 'name': 'Field Fungi (% by count)', 'min': 0.0, 'max': 20.0}]
grades = ("SOR", "SOR2", "SOR3")
csvfile = open('./SorghumGrades.csv', "r")
fieldnames = ("Grades","MOGR","TWT","FGNM","SCRN","DFCT","FFUN")
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "commodity_id": 4,
            "created_at": "2018-04-02T08:00:00+00:00",
        }
    }
    _s_res.append(_json)
    count += 1

for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = _grade_parts[0]
        _bound = _grade_parts[1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _s_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1

        for k, v in _dict.items():
            if(k != 'Grades'):
                _default_spec = list(filter(lambda _s: _s['code'] == k, _specs))[0]
                _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                if not _existing_spec:
                    _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                    _data[_bound.lower()] = float(v)
                    _js["fields"]["specs"].append(_data)
                else:
                    _existing_spec[0][_bound] = float(v)
_s_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 4,
            }
        })
res+=_s_res
count += 1
#chickpeas_desi
_specs = [{'order': 1, 'code': 'VARP', 'name': 'Varietal Purity (% by count)', 'min': 90.0, 'max': 100.0}, {'order': 2, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 3, 'code': 'DEFG', 'name': 'Total Defective (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'PCOL', 'name': 'Poor Colour (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 5, 'code': 'VASC', 'name': 'Ascochyta (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'MLD', 'name': 'Storage Mould (% by weight per half litre)', 'min': 0.0, 'max': 30.0}]

_c_res = []
grades = ("CHK1", "CHK2", "CHK3",)
csvfile = open('./ChickpeasGrades.csv', "r")
fieldnames = ("Grades","VARP","MOGR","DEFG","PCOL","VASC","MLD",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-04-02T08:00:00+00:00",
            "commodity_id": 6,
        }
    }
    _c_res.append(_json)
    count += 1
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = _grade_parts[0]
        _bound = _grade_parts[1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _c_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1

        for k, v in _dict.items():
            if(k != 'Grades'):
                _default_spec = list(filter(lambda _s: _s['code'] == k, _specs))[0]
                _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                if not _existing_spec:
                    _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                    _data[_bound.lower()] = float(v)
                    _js["fields"]["specs"].append(_data)
                else:
                    _existing_spec[0][_bound] = float(v)
_c_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 6,
            }
        })
res+=_c_res
count += 1
#chickpeas_kabuli
_c_res = []
grades = ("CHK1", "CHK2", "CHK3",)
csvfile = open('./ChickpeasGrades.csv', "r")
fieldnames = ("Grades","VARP","MOGR","DEFG","PCOL","VASC","MLD",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-04-02T08:00:00+00:00",
            "commodity_id": 7,
        }
    }
    _c_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'VARP', 'name': 'Varietal Purity (% by count)', 'min': 90.0, 'max': 100.0}, {'order': 2, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 3, 'code': 'DEFG', 'name': 'Total Defective (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'PCOL', 'name': 'Poor Colour (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 5, 'code': 'VASC', 'name': 'Ascochyta (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'MLD', 'name': 'Storage Mould (% by weight per half litre)', 'min': 0.0, 'max': 30.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = _grade_parts[0]
        _bound = _grade_parts[1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _c_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _default_spec = list(filter(lambda _g: _g['code'] == k, _specs))[0]
                _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                if not _existing_spec:
                    _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                    _data[_bound.lower()] = float(v)
                    _js["fields"]["specs"].append(_data)
                else:
                    _existing_spec[0][_bound] = float(v)
_c_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 7,
            }
        })
res+=_c_res

#Cereal Hay
_c_res = []
_specs = [{"max": 100.0, "min": 0.0, "code": "CP", "name": "Crude Protein (% of DM)", "order": 1}, {"max": 100.0, "min": 0.0, "code"
: "DM", "name": "Dry Matter Digestibility (%)", "order": 2}, {"max": 100.0, "min": 0.0, "code": "ME", "name": "Metaboliseable Energy (MJ/Kg of DM)", "order": 3}]
grades = ('A1', 'A2', 'A3', 'A4', 'B1', 'B2', 'B3', 'B4', 'C1', 'C2', 'C3', 'C4', 'D1', 'D2', 'D3', 'D4')
csvfile = open('./CerealHayGrades.csv', "r")
fieldnames = ("Grades","CP","DM","ME")
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2019-04-01T08:00:00+00:00",
            "commodity_id": 25,
        }
    }
    _c_res.append(_json)
    count += 1
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = _grade_parts[0]
        _bound = _grade_parts[1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _c_res))[0]
        _js["fields"]["order"] = i
        for k, v in _dict.items():
            if(k != 'Grades'):
                _default_spec = list(filter(lambda _s: _s['code'] == k, _specs))[0]
                _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                if not _existing_spec:
                    _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                    _data[_bound.lower()] = float(v)
                    _js["fields"]["specs"].append(_data)
                else:
                    _existing_spec[0][_bound] = float(v)
_c_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 25,
            }
        })
res+=_c_res
count += 1
#Legume Hay
_l_res = []
_specs = [{"max": 100.0, "min": 0.0, "code": "CP", "name": "Crude Protein (% of DM)", "order": 1}, {"max": 100.0, "min": 0.0, "code"
: "DM", "name": "Dry Matter Digestibility (%)", "order": 2}, {"max": 100.0, "min": 0.0, "code": "ME", "name": "Metaboliseable Energy (MJ/Kg of DM)", "order": 3}]
grades = ('A1', 'A2', 'A3', 'A4', 'B1', 'B2', 'B3', 'B4', 'C1', 'C2', 'C3', 'C4', 'D1', 'D2', 'D3', 'D4')
csvfile = open('./LegumHayGrades.csv', "r")
fieldnames = ("Grades","CP","DM","ME")
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2019-04-01T08:00:00+00:00",
            "commodity_id": 26,
        }
    }
    _l_res.append(_json)
    count += 1
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = _grade_parts[0]
        _bound = _grade_parts[1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _l_res))[0]
        _js["fields"]["order"] = i
        for k, v in _dict.items():
            if(k != 'Grades'):
                _default_spec = list(filter(lambda _s: _s['code'] == k, _specs))[0]
                _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                if not _existing_spec:
                    _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                    _data[_bound.lower()] = float(v)
                    _js["fields"]["specs"].append(_data)
                else:
                    _existing_spec[0][_bound] = float(v)
_l_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 25,
            }
        })
res+=_l_res
count += 1

print(json.dumps(res))
