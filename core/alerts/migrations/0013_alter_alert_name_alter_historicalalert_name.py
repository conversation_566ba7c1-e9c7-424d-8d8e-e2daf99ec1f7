# Generated by Django 4.1.7 on 2023-05-03 07:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0012_alter_alert_name_alter_historicalalert_name'),
    ]

    operations = [
        migrations.AlterField(
            model_name='alert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit Breach'), ('inload_done', 'Delivery Confirmation - SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Confirmation - Email'), ('delivery_information', 'Delivery Confirmation - Email'), ('outload_report', 'Outload Report')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalalert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit Breach'), ('inload_done', 'Delivery Confirmation - SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Confirmation - Email'), ('delivery_information', 'Delivery Confirmation - Email'), ('outload_report', 'Outload Report')], max_length=100),
        ),
    ]
