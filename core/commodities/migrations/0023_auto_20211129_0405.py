# Generated by Django 3.2.6 on 2021-11-29 04:05

from django.db import migrations


# Moving Lupins (Albus) LUP1/2 data to Lupins (Angustifolius) LUP1/2
# Moving Lupins (Albus) ALBUS1/2 data to Lu<PERSON>s (Albus) LUP1/2
def update_lupins(apps, schema_editor):
    Load = apps.get_model('loads', 'Load')
    Contract = apps.get_model('contracts', 'Contract')
    TitleTransfer = apps.get_model('contracts', 'TitleTransfer')
    FreightContract = apps.get_model('freights', 'FreightContract')
    FreightOrder = apps.get_model('freights', 'FreightOrder')
    Storage = apps.get_model('farms', 'Storage')
    CashPrices = apps.get_model('cash_board', 'CashPrices')  # no variety
    FreightSlot = apps.get_model('company_sites', 'FreightSlot')  # no variety
    CommodityVendorDec = apps.get_model('vendor_decs', 'CommodityVendorDec')

    for klass in [
        Load, Contract, TitleTransfer, Storage,
        CashPrices, FreightSlot, CommodityVendorDec
    ]:
        klass.objects.filter(grade_id=126).update(commodity_id=54, grade_id=72)
        klass.objects.filter(grade_id=265).update(commodity_id=54, grade_id=271)
        klass.objects.filter(grade_id=73).update(grade_id=126)
        klass.objects.filter(grade_id=74).update(grade_id=265)

    for klass in [
        FreightContract, FreightOrder
    ]:
        klass.objects.filter(planned_grade_id=126).update(commodity_id=54, planned_grade_id=72)
        klass.objects.filter(planned_grade_id=265).update(commodity_id=54, planned_grade_id=271)
        klass.objects.filter(planned_grade_id=73).update(planned_grade_id=126)
        klass.objects.filter(planned_grade_id=74).update(planned_grade_id=265)


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0022_auto_20211129_0351'),
    ]

    operations = [
        migrations.RunPython(update_lupins)
    ]
