# Generated by Django 2.1.4 on 2019-01-16 06:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('conditions', '0006_auto_20181218_1003'),
    ]

    operations = [
        migrations.AlterField(
            model_name='generalcondition',
            name='type',
            field=models.CharField(choices=[('contract', 'contract'), ('freight', 'freight'), ('invoice', 'invoice')], max_length=20),
        ),
        migrations.AlterField(
            model_name='historicalgeneralcondition',
            name='type',
            field=models.CharField(choices=[('contract', 'contract'), ('freight', 'freight'), ('invoice', 'invoice')], max_length=20),
        ),
        migrations.AlterField(
            model_name='historicalspecialcondition',
            name='type',
            field=models.CharField(choices=[('contract', 'contract'), ('freight', 'freight'), ('invoice', 'invoice')], max_length=20),
        ),
        migrations.AlterField(
            model_name='specialcondition',
            name='type',
            field=models.CharField(choices=[('contract', 'contract'), ('freight', 'freight'), ('invoice', 'invoice')], max_length=20),
        ),
    ]
