# Generated by Django 4.1.13 on 2024-09-17 04:41

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0188_applicationrate'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='applicationrate',
            name='rate',
        ),
        migrations.AddField(
            model_name='applicationrate',
            name='rates',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.FloatField(default=0), default=list, size=None),
        ),
    ]
