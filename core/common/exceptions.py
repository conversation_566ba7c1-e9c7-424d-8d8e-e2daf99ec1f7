from rest_framework import status
from rest_framework.exceptions import APIException
from django.utils.translation import gettext_lazy as _


class ErrorException(Exception):
    """
    Exception class that adds the error dictionary along with exception
    """
    def __init__(self, error_dict, *args, **kwargs):
        self.error_dict = error_dict
        super().__init__(args, kwargs)


class InvalidModelException(Exception):
    """
    Raised when a disallowed model is requested in sync-manager api
    """
    pass  # pylint: disable=unnecessary-pass


class Http403(APIException):
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = _('Forbidden.')
    default_code = 'forbidden'


class Http400(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = _('Bad Request.')
    default_code = 'bad_request'
