import inflection
from django.core.exceptions import ValidationError
from django.db import IntegrityError, models
from django.http import Http404
from django.utils import timezone
from pydash import compact, get
from rest_framework import parsers
from rest_framework import status
from rest_framework.generics import ListAPIView, RetrieveAPIView, DestroyAPIView, get_object_or_404
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView

from core.cash_board.models import CashPrices
from core.cash_board.serializers import CashPricesSerializer, ExternalCashPriceSerializer
from core.common.constants import PAYMENT_TERM_30_DEOW_OF_DELIVERY
from core.common.exceptions import Http400
from core.common.models import CustomPageNumberPagination, CommonFilters
from core.common.utils import get_filename_with_timestamp, validate_csv, decode_string
from core.contracts.constants import PAYMENT_SCALE_FLAT
from core.contracts.models import TitleTransfer, Contract, PaymentTerm, PaymentScale
from core.contracts.serializers import CashBoardTitleTransferSerializer
from core.countries.constants import AUSTRALIA_COUNTRY_ID
from core.countries.models import Country
from core.loads.constants import ALL_MOBILE_DEVICES, SYSTEM_NAME_HEADER
from core.loads.models import Load
from core.ngrs.models import Ngr
from core.profiles.models import Download, Employee
from core.services.internal.errbit import ERRBIT_LOGGER


class CashPricesView(ListAPIView):
    permission_classes = (AllowAny,)
    pagination_class = CustomPageNumberPagination
    def get_serializer_class(self):
        if (get(self.request.user, 'is_authenticated') and (self.request.query_params.get('grades', '') or
                                                            self.request.query_params.get('commodities', '') or
                                                            self.request.query_params.get('status', ''))):
            return ExternalCashPriceSerializer
        return CashPricesSerializer

    def get_queryset(self):  # pylint: disable=too-many-locals,too-many-statements,too-many-branches
        params = self.request.query_params.dict().copy()
        user = self.request.user
        country_id = get(user, 'country_id')
        if not country_id:
            country_id = get(
                Country.get_country_from_timezone(self.request.META.get('HTTP_REFERER_TZ', None)), 'id'
            ) or AUSTRALIA_COUNTRY_ID
        search_str = params.pop('search', None)
        params.pop('page', None)
        params.pop('page_size', None)
        order_by = params.pop('order_by', None)
        order = params.pop('order', None)
        load_id = params.pop('load_id', None)
        is_presentation = params.pop('is_presentation', None)
        if is_presentation:
            self.pagination_class = None
        truthy = ['true', '1', True, 'True', 1]
        only_my_stocks = params.pop('only_my_stocks', None) in truthy
        sustainable = None
        if 'sustainable' in params:
            sustainable = params.pop('sustainable')
            sustainable = sustainable in truthy
        commodity_id = params.pop('commodity_id', None)
        buyer_company_ids = compact(
            dict(self.request.query_params).pop('buyer__company__id__in', []))
        site_ids = compact(
            dict(self.request.query_params).pop('site__id__in', []))
        tracks = compact(dict(self.request.query_params).pop('track__in', []))
        commodity_ids = compact(
            dict(self.request.query_params).pop('commodity__id__in', []))
        grade_ids = compact(
            dict(self.request.query_params).pop('grade__id__in', []))
        seasons = compact(
            dict(self.request.query_params).pop('season__in', []))
        load = None
        is_mobile = self.request.META.get(SYSTEM_NAME_HEADER, '').lower() in ALL_MOBILE_DEVICES
        grades = self.request.query_params.get('grades', '')
        is_autoweigh = False
        if not grade_ids and grades:
            grades = grades.split(',') if grades else []
            from core.commodities.models import Grade
            grade_ids = compact([get(Grade.find_by_name(grade), 'id') for grade in grades])
            is_autoweigh = True
        _status = self.request.query_params.get('status', None)
        if _status:
            _status = _status.lower()
            is_autoweigh = True
        if _status and _status not in ['pending', 'active', 'inactive', 'completed']:
            raise Http400('status needs to be in pending, active, inactive or completed')
        commodities = self.request.query_params.get('commodities', '')
        if not commodity_ids and commodities:
            commodities = commodities.split(',') if commodities else []
            from core.commodities.models import Commodity
            commodity_ids = compact([get(Commodity.find_by_name(commodity), 'id') for commodity in commodities])
            is_autoweigh = True
        site_id = self.request.query_params.get('site_id', '')
        if not site_ids and site_id:
            site_ids = site_id.split(',') if site_id else []
            is_autoweigh = True
        if load_id:
            try:
                load = Load.objects.get(pk=load_id)
                seasons.append(load.season)
                grade_ids.append(load.grade_id)
                commodity_ids.append(load.commodity_id)
                site_ids.append(get(load.storage, 'farm_id'))
            except Load.DoesNotExist:
                return CashPrices.active.none()
        is_authenticated = user.is_authenticated
        stocks_criteria = models.Q()
        has_stocks = False
        if is_authenticated and not load_id and not is_mobile and not is_autoweigh:
            filter_params = CommonFilters(user).conditional_filtering(key='all_cash_price')
        else:
            from core.stocks.models import Stock
            filter_params = {
                'buyer__company__id__in': buyer_company_ids,
                'site__id__in': site_ids,
                'track__in': tracks,
                'commodity__id__in': commodity_ids,
                'grade__id__in': grade_ids,
                'season__in': seasons
            }
            if only_my_stocks and not load:
                stocks = Stock.objects.filter(ngr__company_id=user.company_id, tonnage__gte=0)
                if commodity_ids:
                    stocks = stocks.filter(commodity_id__in=commodity_ids)
                if grade_ids:
                    stocks = stocks.filter(grade_id__in=grade_ids)
                if seasons:
                    stocks = stocks.filter(season__in=seasons)
                if site_ids:
                    stocks = stocks.filter(farm_id__in=site_ids)
                has_stocks = stocks.exists()
                if not has_stocks:
                    return CashPrices.active.none()
                for stock in stocks:
                    stocks_criteria |= models.Q(
                        commodity_id=stock.commodity_id,
                        grade_id=stock.grade_id,
                        season=stock.season,
                        site_id=stock.farm_id,
                    )
            if sustainable is not None:
                filter_params['sustainable_commodity'] = sustainable
            filter_params = models.Q(**{k: v for k, v in filter_params.items() if v})
        if _status:
            cash_prices = CashPrices.objects.filter(filter_params).filter(stocks_criteria).filter(
                commodity__country_id=country_id)
            now = timezone.now()
            if _status == 'pending':
                cash_prices = cash_prices.filter(
                    CashPrices.get_internally_created_external_cash_prices_criteria()
                )
            elif _status == 'active':
                cash_prices = cash_prices.filter(
                    start_date_time__lte=now, end_date_time__gte=now
                ).filter(status=_status)
            elif _status == 'inactive':
                cash_prices = cash_prices.filter(
                    models.Q(
                        start_date_time__gte=now
                    ) | models.Q(
                        end_date_time__lte=now
                    ) | models.Q(status=_status)
                )
                cash_prices.filter(status='active').update(status=_status)
            else:
                cash_prices = cash_prices.filter(status=_status)
        else:
            cash_prices = CashPrices.active.filter(filter_params).filter(stocks_criteria).filter(
                commodity__country_id=country_id)
        if is_authenticated:
            cash_prices = cash_prices.filter(
                models.Q(
                    site__company_id=user.company_id
                ) | ~models.Q(
                    site__company__show_cash_prices_to_all=False
                ) | models.Q(
                    created_by__company_id=user.company_id
                )
            )
        else:
            cash_prices = cash_prices.exclude(site__company__show_cash_prices_to_all=False)

        cash_prices = cash_prices.order_by('commodity', 'grade__order', '-price')

        if search_str:
            search_str = decode_string(search_str)
            cash_prices = cash_prices.filter(
                models.Q(status__icontains=search_str) |
                models.Q(status__icontains=search_str.replace(' ', '_')) |
                models.Q(buyer__company__business_name__icontains=search_str) |
                models.Q(start_date_time__icontains=search_str) |
                models.Q(end_date_time__icontains=search_str) |
                models.Q(commodity__name__icontains=search_str) |
                models.Q(grade__name__icontains=search_str) |
                models.Q(season__icontains=search_str) |
                models.Q(price__icontains=search_str) |
                models.Q(track__icontains=search_str) |
                models.Q(created_at__icontains=search_str) |
                models.Q(limit__icontains=search_str) |
                models.Q(site__name__icontains=search_str) |
                models.Q(site__company__business_name__icontains=search_str)
            )
            cash_prices = CashPrices.objects.filter(id__in=cash_prices.values_list('id')).exclude(status='pending')

        if commodity_id:
            cash_prices = cash_prices.filter(commodity_id=commodity_id)

        if has_stocks:
            cash_prices = CashPrices.objects.filter(
                id__in=cash_prices.values_list('id', flat=True)).exclude(status='pending')

        if not is_presentation:
            order_by_map = {
                'site_name': ['site__company__business_name'],
                'track': ['track'],
                'id': ['id'],
                'buyer.company_name': ['buyer__company__business_name'],
                'commodity_name': ['commodity__name'],
                'grade_name': ['grade__name'],
                'season': ['season'],
                'price': ['price'],
                'payment_term.name': ['payment_term__name']
            }

            if order_by:
                _order_by = order_by_map.get(
                    inflection.underscore(order_by), ['created_at'])
                order = '-' if order == 'desc' else ''
                cash_prices = cash_prices.order_by(*[order + f for f in _order_by])
        if load_id and load:
            cash_prices = cash_prices.annotate(
                remaining_limit=(models.F('limit') - (
                    Contract.objects.filter(
                        cash_price_id=models.F('pk')).aggregate(
                            total_tonnage=models.Sum('tonnage')
                        )['total_tonnage'] or 0)) or 0).filter(
                            remaining_limit__gte=get(load, 'checkpoint.net_weight', get(load, 'net_weight', 0)))
        return cash_prices.select_related('site', 'commodity', 'grade', 'buyer', 'payment_term')

    def post(self, request):
        from core.farms.models import Farm
        try:
            cash_price = CashPrices.persist(request.data.copy())
            cash_price.full_clean()
            farm = Farm.objects.filter(id=request.data.get('site_id')).first()
            if farm:
                farm.company.add_buyer_to_site_approved_buyers_if_not_present(request.data['buyer']['company_id'])
            return Response(cash_price.to_dict(), status=status.HTTP_201_CREATED)
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class CashPriceView(APIView):
    serializer_class = CashPricesSerializer
    def get_object(self):
        cash_price = CashPrices.objects.filter(id=self.kwargs['cash_price_id']).first()
        if not cash_price:
            raise Http404()
        return cash_price

    def get(self, request, cash_price_id):  # pylint: disable=unused-argument
        cash_price = self.get_object()
        if cash_price.can_user_view(request.user):
            return Response(self.serializer_class(cash_price).data, status=status.HTTP_200_OK)
        return Response(status=status.HTTP_403_FORBIDDEN)


    def delete(self, _, cash_price_id):
        try:
            cash_price = CashPrices.archived.get(id=cash_price_id)
        except CashPrices.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        try:
            if cash_price.limit == cash_price.limit_remaining:
                cash_price.delete()
                return Response(status=status.HTTP_200_OK)
            else:
                return Response(status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError:
            return Response(status=status.HTTP_400_BAD_REQUEST)


class ExternalCashPricesView(ListAPIView):
    serializer_class = ExternalCashPriceSerializer
    pagination_class = CustomPageNumberPagination
    def exists(self):
        external_reference = self.request.data.get('external_reference')
        if external_reference:
            return self.get_queryset().filter(
                external_reference=external_reference).exclude(status='inactive').exists()
        return None

    def get_queryset(self):
        return CashPrices.objects.filter(external_system=self.kwargs['external_system'].lower())

    def filter_queryset(self, queryset):
        from core.commodities.models import Grade, Commodity

        if site_id := self.request.query_params.get('site_id', None):
            queryset = queryset.filter(site_id=site_id)

        commodities = self.request.query_params.get('commodities', '')
        grades = self.request.query_params.get('grades', '')
        commodities = commodities.split(',') if commodities else []
        grades = grades.split(',') if grades else []
        criteria = models.Q()
        if commodity_ids := [Commodity.find_by_name(commodity) for commodity in commodities]:
            criteria |= models.Q(commodity_id__in=commodity_ids)
        if grade_ids := [Grade.find_by_name(grade) for grade in grades]:
            criteria |= models.Q(grade_id__in=grade_ids)
        queryset = queryset.filter(criteria)

        if _status := self.request.query_params.get('status', None):
            _status = _status.lower()
            if _status not in ['pending', 'active', 'inactive', 'completed']:
                raise Http400('status needs to be in pending, active, inactive or completed')
            now = timezone.now()
            if _status == 'pending':
                queryset = queryset.filter(
                    CashPrices.get_internally_created_external_cash_prices_criteria()
                )
            elif _status == 'active':
                queryset = queryset.filter(start_date_time__lte=now, end_date_time__gte=now).filter(status=_status)
            elif _status == 'inactive':
                queryset = queryset.filter(
                    models.Q(
                        start_date_time__gte=now
                    ) | models.Q(
                        end_date_time__lte=now
                    ) | models.Q(status=_status)
                )
                queryset.filter(status='active').update(status=_status)
            else:
                queryset = queryset.filter(status=_status)

        return queryset.select_related(
            'commodity', 'grade', 'buyer__company', 'buyer__contact', 'payment_term', 'payment_scale',
            'created_by', 'updated_by'
        )

    def get_payload(self):
        data = self.request.data
        user = self.request.user
        buyer = self.get_buyer(data)
        self.update_commodity(data)
        self.update_payment_term(data)
        self.update_payment_scale(data)
        transaction_user = Employee.get_or_create_employee(user.company, data.pop('user', None))
        return {
            **data,
            'start_date_time': timezone.now().strftime('%Y-%m-%d'),
            'buyer': buyer,
            'created_by_id': get(transaction_user, 'id') or get(user, 'id'),
            'updated_by_id': get(transaction_user, 'id') or get(user, 'id'),
            'external_system': self.kwargs['external_system'].lower()
        }

    @staticmethod
    def update_payment_scale(data):
        payment_scale = data.pop('payment_scale', None)
        data['payment_scale_id'] = get(
            PaymentScale.find_by_name(payment_scale), 'id'
        ) or PAYMENT_SCALE_FLAT if payment_scale else PAYMENT_SCALE_FLAT

    @staticmethod
    def update_payment_term(data):
        payment_term = data.pop('payment_term', None)
        data['payment_term_id'] = get(
            PaymentTerm.find_by_name(payment_term), 'id'
        ) or PAYMENT_TERM_30_DEOW_OF_DELIVERY if payment_term else PAYMENT_TERM_30_DEOW_OF_DELIVERY

    @staticmethod
    def get_buyer(data):
        buyer_abn = data.pop('buyer_abn', None)
        buyer = None
        if buyer_abn:
            from core.companies.models import Company
            buyer = Company.objects.filter(abn=buyer_abn).first()
        if not buyer:
            raise ValidationError({'buyer_abn': 'must be mandatory and needs to be present in AC.'})
        return {
            'company_id': buyer.id,
            'contact_id': get(buyer.employee_set.order_by('id').first(), 'id'),
            'ngr_id': buyer.get_tagged_ngr_or_logical('title_transfer')
        }

    @staticmethod
    def update_commodity(data):
        commodity = data.pop('commodity', None)
        grade = data.pop('grade', None)
        variety = data.pop('variety', None)
        if commodity:
            from core.commodities.models import Commodity
            data['commodity'] = Commodity.find_by_name(commodity)
            if grade and data['commodity']:
                from core.commodities.models import Grade
                data['grade'] = Grade.find_by_name(grade, data['commodity'].grade_set)
            if variety and data['commodity']:
                from core.commodities.models import Variety
                data['variety'] = Variety.find_by_name(variety, data['commodity'].variety_set)

    def post(self, request, **kwargs):  # pylint: disable=unused-argument
        from core.farms.models import Farm
        if self.exists() in [True, None]:
            return Response(
                {'external_reference': 'must be mandatory and unique.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        farm = Farm.objects.filter(id=request.data.get('site_id')).first()
        if not farm:
            return Response({'site_id': 'Not Found'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            cash_price = CashPrices.persist(self.get_payload())
            cash_price.full_clean()
            if cash_price.persisted and cash_price.buyer_id:
                farm.company.add_buyer_to_site_approved_buyers_if_not_present(cash_price.buyer.company_id)
            return Response(ExternalCashPriceSerializer(cash_price).data, status=status.HTTP_201_CREATED)
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'errors': ex.args}, status=status.HTTP_400_BAD_REQUEST)


class ExternalCashPricesActivateView(ListAPIView):
    serializer_class = ExternalCashPriceSerializer
    def get_queryset(self):
        queryset = CashPrices.objects.filter(
            external_system=self.kwargs['external_system'].lower()
        ).filter(
            CashPrices.get_internally_created_external_cash_prices_criteria()
        ).order_by('-created_at')
        site_id = self.request.query_params.get('site_id', None)
        if site_id:
            queryset = queryset.filter(site_id=site_id)
        return queryset

    def put(self, request, **kwargs):  # pylint: disable=unused-argument
        external_reference = self.request.data.get('external_reference', None)
        if not external_reference:
            return Response({'errors': 'Provide a list of external_reference'}, status=status.HTTP_400_BAD_REQUEST)

        if not isinstance(external_reference, list):
            external_reference = [external_reference]

        queryset = self.get_queryset().filter(external_reference__in=external_reference)

        if queryset.count() != len(set(external_reference)):
            invalid_references = set(external_reference) - set(queryset.values_list('external_reference', flat=True))
            return Response({'errors': f'Invalid {", ".join(invalid_references)}'}, status=status.HTTP_400_BAD_REQUEST)

        CashPrices.approve_from_external_system(queryset)

        return Response(status=status.HTTP_204_NO_CONTENT)


class ExternalCashPriceView(RetrieveAPIView, DestroyAPIView):
    serializer_class = ExternalCashPriceSerializer
    def get_object(self):
        return get_object_or_404(CashPrices.objects.filter(
            external_reference=self.kwargs['external_reference'],
            external_system=self.kwargs['external_system'].lower()
        ))

    def get_payload(self):
        payload = self.request.data
        ngr_number = payload.pop('seller_ngr_number', None)
        payload.pop('seller_abn', None)
        if not ngr_number:
            raise Http400("Seller ngr is mandatory.")
        ngr = Ngr.objects.filter(ngr_number__iexact=ngr_number.strip()).first()
        if ngr:
            company = ngr.company
            payload['seller'] = {
                'company_id': ngr.company_id,
                'ngr_id': ngr.id,
                'contact_id': get(company.employee_set.order_by('id').first(), 'id')
            }
        else:
            raise Http400('Seller ngr not found')
        variety_name = payload.pop('variety', None)
        if variety_name:
            cash_price = self.get_object()
            from core.commodities.models import Variety
            variety = Variety.find_by_name(variety_name, cash_price.commodity.variety_set)
            if variety:
                payload['variety_id'] = variety.id
        return payload

    def put(self, request, **kwargs):  # pylint: disable=unused-argument
        """Cash out from External System"""
        try:
            cash_price = self.get_object()
            if cash_price.status in ['completed', 'inactive']:
                return Response({'errors': f'cash price is already {cash_price.status}'}, status=status.HTTP_200_OK)
            payload = request.data.copy()
            tonnage = payload.get('tonnage', None)
            transaction_user = Employee.get_or_create_employee(request.user.company, payload.pop('user', None))
            if not tonnage or cash_price.limit_remaining < tonnage:
                return Response({'errors': 'Tonnage needs to be less than limit remaining.'}, status=status.HTTP_200_OK)

            title_transfer = cash_price.create_contract_and_title_transfer(
                transaction_user or request.user, self.get_payload(),
                request.META.get('HTTP_REFERER_TZ', request.user.country.timezone), False, True
            )
            if not title_transfer:
                return Response(status=status.HTTP_400_BAD_REQUEST)
            if not get(title_transfer, 'persisted'):
                return Response(
                    {'errors': get(title_transfer, 'errors') or 'Cannot cash out more than remaining'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            cash_price.mark_complete()
            title_transfer.send_notification(request.user, cash_price)
            cash_price.refresh_from_db()
            return Response(ExternalCashPriceSerializer(cash_price).data, status=status.HTTP_200_OK)
        except (Exception, Http400) as ex:  # pylint: disable=broad-except
            return Response(
                {'errors': get(ex, 'detail' if isinstance(ex, Http400) else 'message_dict', {})},
                status=status.HTTP_400_BAD_REQUEST
            )

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.status in ['completed', 'inactive']:
            return Response({'errors': f'cash price is already {instance.status}'}, status=status.HTTP_400_BAD_REQUEST)

        instance.status = 'inactive'
        instance.save()

        if self.request.user.company_id == instance.buyer.company_id:
            instance.audit('inactive', self.request.user)
        else:
            instance.audit('inactive_on_behalf', self.request.user)

        return Response(status=status.HTTP_204_NO_CONTENT)


class CanCreateCashBoardView(APIView):
    def get(self, _, cash_price_id):
        try:
            cash_price = CashPrices.active.get(id=cash_price_id)
        except CashPrices.DoesNotExist:
            return Response({
                'result': False,
                'reasons': "Cash Price might not be active. Please refresh your page to load active cash prices.",
            }, status=status.HTTP_400_BAD_REQUEST)

        reasons = cash_price.cannot_create_title_transfer_reasons()

        return Response(
            {
                'result': len(reasons) == 0,
                'reasons': reasons,
            },
            status=status.HTTP_200_OK,
        )


class TitleTransfersView(APIView, CustomPageNumberPagination):
    def get(self, request, cash_price_id):
        try:
            CashPrices.objects.get(id=cash_price_id)
        except CashPrices.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        try:
            queryset = TitleTransfer.objects.filter(
                commodity_contract__cash_price_id=cash_price_id,
                viewer_company_ids__contains=[request.user.company_id]
            )
            if queryset.exists():
                queryset = queryset.select_related(
                    'storage__farm__company', 'grade', 'variety', 'seller', 'buyer',
                    'commodity_contract', 'bhc_site__company'
                )
            results = self.paginate_queryset(queryset, request, view=self)
            serializer = CashBoardTitleTransferSerializer(
                results, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'errors': get(ex, 'message_dict', {})}, status=status.HTTP_400_BAD_REQUEST)

    def post(self, request, cash_price_id):  # pylint: disable=too-many-locals,too-many-return-statements
        cash_price = CashPrices.active.filter(id=cash_price_id).first()

        if not cash_price:
            raise Http404('Cash Price might not be active.')

        try:
            load_id = request.data.get('load_id', None)
            if load_id and TitleTransfer.filter_by_load_id(load_id).exists():
                return Response(
                    {'errors': 'This load is already cashed out.'}, status=status.HTTP_400_BAD_REQUEST
                )

            ngr_id = get(request.data, 'seller.ngr_id')
            if not ngr_id and load_id:
                ngr_id = get(Load.objects.filter(id=load_id).first(), 'ngr_id')

            if cash_price.sustainable_commodity:
                ngr = Ngr.objects.filter(id=ngr_id).first()
                if not ngr:
                    return Response({'errors': "NGR is not valid"}, status=status.HTTP_400_BAD_REQUEST)
                if not ngr.ngrsustainabilitydeclaration_set.filter(
                        commodity_id=cash_price.commodity_id, season=cash_price.season).exists():
                    return Response(
                        {
                            'errors': "Sorry, we are unable to complete your request. "
                                      "Cashing out against a sustainable commodity cash price requires a "
                                      "self declaration to be completed on the NGR.com website prior to cashing."
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

            has_stock, load = cash_price.has_enough_stock(load_id, ngr_id, get(request.data, 'tonnage', 0))

            if not has_stock:
                return Response({'errors': "You don't have enough stock"}, status=status.HTTP_400_BAD_REQUEST)
            tz = request.META.get('HTTP_REFERER_TZ', request.user.country.timezone)
            title_transfer = cash_price.create_contract_and_title_transfer(request.user, request.data,
                                                                           tz, is_cashed_out=True)
            if not get(title_transfer, 'persisted'):
                return Response({'errors': 'Cannot cash out more than remaining'}, status=status.HTTP_400_BAD_REQUEST)

            cash_price.mark_complete()
            if load_id and load:
                load.update_cash_price_details(cash_price.id, title_transfer.id)

            is_mobile = self.request.META.get(SYSTEM_NAME_HEADER, '').lower() in ALL_MOBILE_DEVICES
            if is_mobile:
                title_transfer.send_notification_to_company_admins(request.user, True, cash_price)

            title_transfer.send_notification(request.user, cash_price)

            return Response(title_transfer.to_dict(), status=status.HTTP_201_CREATED)
        except Exception as ex:  # pylint: disable=broad-except
            return Response({'errors': get(ex, 'message_dict', {})}, status=status.HTTP_400_BAD_REQUEST)


class CashPriceStatusTransitionView(APIView):
    def get(self, _, cash_price_id, new_status):
        try:
            cash_price = CashPrices.objects.get(id=cash_price_id)
            if cash_price.status == 'completed':
                return Response({'errors': 'cash price is already completed'}, status=status.HTTP_200_OK)
            cash_price.status = new_status
            cash_price.save()
            if cash_price.status == 'inactive':
                if self.request.user.company_id == cash_price.buyer.company_id:
                    cash_price.audit('inactive', self.request.user)
                else:
                    cash_price.audit('inactive_on_behalf', self.request.user)
            return Response({'result': True}, status=status.HTTP_200_OK)
        except CashPrices.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)


class CashPriceExistsInStatusView(APIView):
    def get(self, _, cash_price_id, cash_price_status):
        cash_price_exists = False

        if cash_price_status == 'active':
            cash_price_exists = CashPrices.active.filter(id=cash_price_id).exists()
        elif cash_price_status == 'archived':
            cash_price_exists = CashPrices.archived.filter(id=cash_price_id).exists()

        return Response({'result': cash_price_exists}, status=status.HTTP_200_OK)


class UploadCashPricesView(APIView):
    parser_class = (parsers.MultiPartParser,)
    def post(self, request):
        file_entry = request.FILES['file']
        download = Download.create({
            'employee_id': request.user.id,
            'name': get_filename_with_timestamp('cash_prices' + '_'),
            'module': 'cash_prices',
        })
        if download.persisted:
            content = b'\n'.join(line for line in file_entry.read().splitlines() if line.strip(b','))
            try:
                validate_csv(content)
                content = content.decode('utf-8-sig', errors='replace')
                if reasons := CashPrices.validate_cash_price_upload_csv(content):
                    return Response({'reasons': reasons}, status=status.HTTP_200_OK)
                tz = request.META.get('HTTP_REFERER_TZ', request.user.country.timezone)
                result = CashPrices.generate_cash_price_csv(content, download.id, tz)
                return Response(result, status=status.HTTP_201_CREATED)
            except Exception as ex: # pylint: disable=broad-except
                ERRBIT_LOGGER.log(ex)
                download.status = 'failed'
                download.failure_reason = str(ex)
                download.save()
        return Response(status=status.HTTP_400_BAD_REQUEST)


class CashPricesCommodityView(APIView):
    permission_classes = (AllowAny,)
    def get(self, request):  # pylint: disable=too-many-locals
        params = request.query_params.dict().copy()
        user = request.user
        params.pop('page', None)
        params.pop('page_size', None)
        buyer_company_ids = compact(
            dict(self.request.query_params).pop('buyer__company__id__in', []))
        site_ids = compact(
            dict(self.request.query_params).pop('site__id__in', []))
        tracks = compact(dict(self.request.query_params).pop('track__in', []))
        commodity_ids = compact(
            dict(self.request.query_params).pop('commodity__id__in', []))
        grade_ids = compact(
            dict(self.request.query_params).pop('grade__id__in', []))
        seasons = compact(
            dict(self.request.query_params).pop('season__in', []))
        is_authenticated = user.is_authenticated
        if is_authenticated:
            filter_params = CommonFilters(user).conditional_filtering(key='all_cash_price')
        else:
            filter_params = {
                'buyer__company__id__in': buyer_company_ids,
                'site__id__in': site_ids,
                'track__in': tracks,
                'commodity__id__in': commodity_ids,
                'grade__id__in': grade_ids,
                'season__in': seasons
            }
            filter_params = models.Q(**{k: v for k, v in filter_params.items() if v})
        cash_prices = CashPrices.active.filter(filter_params)
        if is_authenticated:
            cash_prices = cash_prices.filter(
                models.Q(
                    site__company_id=user.company_id
                ) | ~models.Q(
                    site__company__show_cash_prices_to_all=False
                ) | models.Q(
                    created_by__company_id=user.company_id
                )
            )
        else:
            cash_prices = cash_prices.exclude(site__company__show_cash_prices_to_all=False)

        commodity_ids = cash_prices.order_by('-updated_at').values_list('commodity_id', flat=True)
        return Response({'commodity_ids': list(set(commodity_ids))}, status=status.HTTP_200_OK)


class CashPricesInactiveView(APIView):
    def put(self, request):
        cash_price_ids = get(request.data, 'cash_price_ids')
        if not cash_price_ids:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        active_cash_prices = CashPrices.active.filter(
            buyer__company_id=get(request.user, 'company_id')
        ).values_list('id', flat=True)
        if not set(cash_price_ids).issubset(set(active_cash_prices)):
            return Response({'errors': 'Invalid Transaction'}, status=status.HTTP_400_BAD_REQUEST)
        CashPrices.mark_inactive(cash_price_ids, request.user)
        return Response({"success": True}, status=status.HTTP_200_OK)
