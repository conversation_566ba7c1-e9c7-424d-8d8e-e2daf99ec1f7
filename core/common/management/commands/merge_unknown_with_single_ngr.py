import requests
from django.core.management import BaseCommand
from django.db.models import Count

from core.companies.models import Company
from core.ngrs.models import Ngr
from django.conf import settings

from core.profiles.models import Employee


class Command(BaseCommand):
    help = 'Merge Unknown NGR with Single NGR in the company'

    def handle(self, *args, **options):
        company_ids = Ngr.objects.values('company_id').annotate(
            count=Count('id')).filter(count=2).values_list('company_id', flat=True)
        outliers = []
        unknown_with_bank_accounts = []
        user = Employee.objects.get(id=1)
        token = user.refresh_token()
        for company in Company.objects.filter(id__in=company_ids).order_by('business_name'):
            unknown = company.ngr_set.filter(ngr_number__icontains='unknown').first()
            if not unknown:
                outliers.append(company.id)
                continue
            if unknown.bank_account_set.exists():
                unknown_with_bank_accounts.append(company.id)
                continue
            known = company.ngr_set.exclude(id=unknown.id).filter(ngr_type='single').first()
            if known and len(known.ngr_number) == 8 and known.ngr_number.isdigit():
                state = {
                    'company': company.id,
                    'unknown': unknown.id,
                    'known': known.id
                }
                response = requests.post(
                    settings.SELF_PUBLIC_URL + f'/ngrs/{known.id}/merge/',
                    json={
                        'merge_from': unknown.id
                    },
                    headers={
                        'Authorization': f'Token {token}'
                    },
                    timeout=1800
                )
                print(f"Merge {unknown.ngr_number} with {known.ngr_number} for {company.name}: {response.status_code}")
                state['response'] = response.status_code
            else:
                outliers.append(company.id)
        print("**OUTLIERS***", outliers)
        print("**UNKNOWN WITH BANK ACCOUNTS***", unknown_with_bank_accounts)



