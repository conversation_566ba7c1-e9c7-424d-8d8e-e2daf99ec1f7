import csv
import json
import sys

csvfile = open(sys.argv[-1], "r")
fieldnames = ("Variety","Classification / Maximum Grade")
reader = csv.DictReader(csvfile, fieldnames)
res = []
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _json = {
            "fields": {
                "created_at": "2018-04-02T08:00:00+00:00",
            },
        }
        _json["pk"] = i
        _json["model"] = "commodities.canolavariety"
        _json["fields"]["name"] = _dict["Variety"]
        _json["fields"]["maximum_grade"] = _dict["Classification / Maximum Grade"]
        _json["fields"]["commodity_id"] = 3
        res.append(_json)

print(json.dumps(res))
