# Generated by Django 2.2 on 2020-03-26 07:42

from django.db import migrations

def update_minimum_tonnage(apps, schema_editor):
    SiteManagementSettings = apps.get_model('company_sites', 'SiteManagementSettings')
    SiteManagementSettings.objects.update(minimum_tonnage=0.1)


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0073_auto_20200326_0735'),
    ]

    operations = [
        migrations.RunPython(update_minimum_tonnage)
    ]
