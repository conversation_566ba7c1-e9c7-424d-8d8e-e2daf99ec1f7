# Generated by Django 4.1.13 on 2024-04-01 08:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0199_throughwarehouseallocation_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='contract',
            name='blending_fee',
            field=models.FloatField(blank=True, default=0, null=True),
        ),
        migrations.AddField(
            model_name='historicalcontract',
            name='blending_fee',
            field=models.FloatField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='contracttype',
            name='name',
            field=models.CharField(choices=[('fixed_grade', 'Fixed Grade'), ('multi_grade', 'Multi Grade'), ('floating_multi_grade', 'Floating Multi Grade'), ('pool_cash_and_call', 'Pool – Cash & Call'), ('pool_strategic_6_month', 'Pool – Strategic 6 month'), ('pool_strategic_12_month', 'Pool – Strategic 12 month'), ('area', 'Area'), ('spot', 'Spot'), ('blended', 'Blended')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalcontracttype',
            name='name',
            field=models.CharField(choices=[('fixed_grade', 'Fixed Grade'), ('multi_grade', 'Multi Grade'), ('floating_multi_grade', 'Floating Multi Grade'), ('pool_cash_and_call', 'Pool – Cash & Call'), ('pool_strategic_6_month', 'Pool – Strategic 6 month'), ('pool_strategic_12_month', 'Pool – Strategic 12 month'), ('area', 'Area'), ('spot', 'Spot'), ('blended', 'Blended')], max_length=100),
        ),
    ]
