# Generated by Django 5.1.8 on 2025-06-16 08:47

import dirtyfields.dirtyfields
import django.contrib.postgres.fields
import django.db.models.deletion
import django.utils.timezone
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0031_alter_alert_name_alter_historicalalert_name'),
        ('farms', '0155_farm_autoweigh_on_gross_farm_autoweigh_on_tare_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeAlert',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('include_internal_sites', models.BooleanField(default=True)),
                ('include_external_sites', models.BooleanField(default=True)),
                ('roles', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('customer', 'Customer'), ('freight_provider', 'Freight Provider'), ('pickup_site', 'Pickup Site'), ('delivery_site', 'Delivery Site'), ('sub_freight_provider', 'Sub Freight Provider'), ('stock_owner', 'Stock Owner'), ('own_company', 'Own Company'), ('own_company_site', 'Own Company Site'), ('seller', 'Seller'), ('buyer', 'Buyer'), ('creator', 'Creator')], max_length=50), blank=True, default=list, size=None)),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'employee_alerts',
            },
            bases=(dirtyfields.dirtyfields.DirtyFieldsMixin, models.Model),
        ),
        migrations.CreateModel(
            name='EmployeeAlertSite',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('alert', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='alerts.employeealert')),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('site', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='farms.farm')),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'employee_alert_sites',
            },
            bases=(dirtyfields.dirtyfields.DirtyFieldsMixin, models.Model),
        ),
        migrations.AddField(
            model_name='employeealert',
            name='sites',
            field=models.ManyToManyField(through='alerts.EmployeeAlertSite', to='farms.farm'),
        ),
        migrations.CreateModel(
            name='HistoricalEmployeeAlertSite',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('alert', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='alerts.employeealert')),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('site', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='farms.farm')),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical employee alert site',
                'verbose_name_plural': 'historical employee alert sites',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalReceiveAlert',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('name', models.CharField(choices=[('missing_docket_alert', 'Missing Docket Alert for Freight Providers'), ('missing_docket_pickup_site_alert', 'Missing Docket Alert for Pickup Site'), ('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit Breach'), ('pickup_confirmation_sms', 'Pickup Confirmation - SMS'), ('inload_done', 'Delivery Confirmation - SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Confirmation - Email'), ('delivery_information', 'Delivery Confirmation - Email'), ('outload_report', 'Outload Report'), ('slot_booking_alert', 'Slot Booking Updates'), ('truck_cleanliness_declaration_alert', 'Truck Cleanliness Declaration Not Filled'), ('docket_clarification_alert', 'Docket Clarifications'), ('stock_operations_update_alert', 'Stock Operations Update'), ('stock_auto_update_alert', 'Stock Auto Update'), ('acquisition_report_alert', 'Acquisition Report'), ('new_cash_price_for_my_stocks_alert', 'New Cash Price For My Stocks'), ('leading_cash_price_warning_alert', 'Leading Cash Price Warning'), ('bulk_invoice_alert', 'Automatic Invoice Emails')], max_length=100)),
                ('channel', models.CharField(choices=[('sms', 'SMS'), ('email', 'Email'), ('mobile_push_notification', 'Mobile Push Notification')], max_length=100)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical receive alert',
                'verbose_name_plural': 'historical receive alerts',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='ReceiveAlert',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(choices=[('missing_docket_alert', 'Missing Docket Alert for Freight Providers'), ('missing_docket_pickup_site_alert', 'Missing Docket Alert for Pickup Site'), ('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit Breach'), ('pickup_confirmation_sms', 'Pickup Confirmation - SMS'), ('inload_done', 'Delivery Confirmation - SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Confirmation - Email'), ('delivery_information', 'Delivery Confirmation - Email'), ('outload_report', 'Outload Report'), ('slot_booking_alert', 'Slot Booking Updates'), ('truck_cleanliness_declaration_alert', 'Truck Cleanliness Declaration Not Filled'), ('docket_clarification_alert', 'Docket Clarifications'), ('stock_operations_update_alert', 'Stock Operations Update'), ('stock_auto_update_alert', 'Stock Auto Update'), ('acquisition_report_alert', 'Acquisition Report'), ('new_cash_price_for_my_stocks_alert', 'New Cash Price For My Stocks'), ('leading_cash_price_warning_alert', 'Leading Cash Price Warning'), ('bulk_invoice_alert', 'Automatic Invoice Emails')], max_length=100)),
                ('channel', models.CharField(choices=[('sms', 'SMS'), ('email', 'Email'), ('mobile_push_notification', 'Mobile Push Notification')], max_length=100)),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'receive_alerts',
            },
            bases=(dirtyfields.dirtyfields.DirtyFieldsMixin, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalEmployeeAlert',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('include_internal_sites', models.BooleanField(default=True)),
                ('include_external_sites', models.BooleanField(default=True)),
                ('roles', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('customer', 'Customer'), ('freight_provider', 'Freight Provider'), ('pickup_site', 'Pickup Site'), ('delivery_site', 'Delivery Site'), ('sub_freight_provider', 'Sub Freight Provider'), ('stock_owner', 'Stock Owner'), ('own_company', 'Own Company'), ('own_company_site', 'Own Company Site'), ('seller', 'Seller'), ('buyer', 'Buyer'), ('creator', 'Creator')], max_length=50), blank=True, default=list, size=None)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
                ('alert', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='alerts.receivealert')),
            ],
            options={
                'verbose_name': 'historical employee alert',
                'verbose_name_plural': 'historical employee alerts',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.AddField(
            model_name='employeealert',
            name='alert',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='alerts.receivealert'),
        ),
        migrations.AddIndex(
            model_name='employeealertsite',
            index=models.Index(fields=['alert', 'site'], name='emp_alert_site_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='employeealertsite',
            unique_together={('alert', 'site')},
        ),
        migrations.AlterUniqueTogether(
            name='employeealert',
            unique_together={('employee', 'alert')},
        ),
    ]
