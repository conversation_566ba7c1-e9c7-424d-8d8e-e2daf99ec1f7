# Generated by Django 4.0.4 on 2022-07-07 02:46

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0115_alter_freightslot_commodity_alter_freightslot_driver_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='freightslot',
            name='excluded_commodity_ids',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), blank=True, default=None, null=True, size=None),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='excluded_commodity_ids',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), blank=True, default=None, null=True, size=None),
        ),
    ]
