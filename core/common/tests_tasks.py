from django.core import mail
from django.test import tag
from mock import patch, Mock
from core.companies.tests.factories import CompanyFactory
from core.farms.tests.factories import FarmFactory, StorageFactory
from core.freights.models import FreightOrder
from core.loads.models import StockSwap, RegradeReseason
from core.ngrs.tests.factories import NgrFactory
from core.profiles.tests.factories import EmployeeFactory
from .tasks import accept_and_reject_void_url
from .tests import ACTestCase, AuthSetup
from ..freights.tests.factories import FreightMovementFactory
from ..jobs.models import Job
from ..loads.tests.factories import LoadFactory
from ..settings import WEB_URL


@tag('job')
class SendPersistMailTest(ACTestCase):
    @patch('core.services.internal.pdf.HTMLToPDF.from_string')
    def test_send_persist_mail_stock_swap(self, mock_from_string):
        company = CompanyFactory(type_id=1, business_name='growerfoo')
        employee = EmployeeFactory(company=company)
        ngr = NgrFactory(company=company, ngr_number='12345678')
        farm = FarmFactory(company=company, stocks_management=True, name='farm1')
        farm2 = FarmFactory(company=company, stocks_management=True, name='farm2')
        storage = StorageFactory(is_gate=True, farm=farm)
        StorageFactory(is_gate=True, farm=farm2)
        LoadFactory(
            storage=storage, estimated_net_weight=120, commodity_id=1, grade_id=1, season="24/25", type='inload',
            ngr=ngr
        )
        mock_from_string.return_value = b'Send Persist Email'
        stock_swap_data = {
            "commodity_id": 1,
            "grade_id": 1,
            "season": "24/25",
            "current_site_id": farm.id,
            "swap_site_id": farm2.id,
            "estimated_net_weight": "23",
            "freight_differential": "3",
            "quality_differential": None,
            "ngr_id": ngr.id,
            "comment": "",
            "identifier": "L24102447882AC",
            "specs": {},
            "quantity": "",
            "date_time": "2024-10-24 11:04:00",
        }
        stock_swap = StockSwap.persist(stock_swap_data, employee.id)
        job = Job.create(
            {
                'status': 'pending',
                'params': {
                    'object_id': stock_swap.id,
                    'raised_for': 'stock_swap',
                    'subject': 'Test Persist Email',
                    'recipients': {'recipients': ['<EMAIL>']}
                 },
                'type': 'send_persist_mail',
            },
        )
        self.assertTrue(job.persisted)
        job.run_now()
        job.refresh_from_db()
        self.assertEqual(job.status, 'finished')
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, 'Test Persist Email')
        self.assertIn(f'<h2 style="font-size: 20pt; color: #343534">{farm.company.name}</h2>', sent_mail.body)
        self.assertIn(f'<td style="width: 50%; font-size: 10pt;"> {stock_swap.identifier} </td>', sent_mail.body)
        self.assertIn('<td style="width: 50%; font-size: 10pt;">23.00 </td>', sent_mail.body)
        self.assertIn('<td style="width: 50%; font-size: 10pt;">$ 3.00</td>', sent_mail.body)

    @patch('core.services.internal.pdf.HTMLToPDF.from_string')
    def test_send_persist_mail_regrade_reseason(self, mock_from_string):
        company = CompanyFactory(type_id=1, business_name='growerfoo')
        employee = EmployeeFactory(company=company)
        ngr = NgrFactory(company=company, ngr_number='12345678')
        farm = FarmFactory(company=company, stocks_management=True, name='farm1')
        farm2 = FarmFactory(company=company, stocks_management=True, name='farm2')
        storage = StorageFactory(is_gate=True, farm=farm)
        StorageFactory(is_gate=True, farm=farm2)
        LoadFactory(
            storage=storage, estimated_net_weight=120, commodity_id=1, grade_id=1, season="23/24", type='inload',
            ngr=ngr
        )
        mock_from_string.return_value = b'Send Persist Email'
        regrade_reseason_data = {
            "commodity_id__in": [1],
            "farm_id": farm.id,
            "ngr_id__in": [ngr.id],
            "season__in": ["24/25"],
            "grade_id__in": [1],
            "variety_id__in": [],
            "tonnage": "24",
            "recommodity_id": 1,
            "regrade_id": [2],
            "reseason": ["24/25"],
            "identifier": "R2410245817168",
            "comment": "",
            "differential": "6",
            "specs": {
                "MOGR": "",
                "PRGR": "",
                "TWT": "",
                "SCRN": "",
                "UNM": "",
                "FALL": ""
            },
            "quantity": "",
            "reseasoned_date_time": "2024-10-24 11:12:00",
        }
        regrade_reseason = RegradeReseason.persist(regrade_reseason_data, employee.id)
        job = Job.create(
            {
                'status': 'pending',
                'params': {
                    'object_id': regrade_reseason.id,
                    'raised_for': 'regrade_reseason',
                    'subject': 'Test Persist Email',
                    'recipients': {'recipients': ['<EMAIL>']}
                 },
                'type': 'send_persist_mail',
            },
        )
        self.assertTrue(job.persisted)
        job.run_now()
        job.refresh_from_db()
        self.assertEqual(job.status, 'finished')
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, 'Test Persist Email')
        self.assertIn(f'<h2 style="font-size: 20pt; color: #343534">{farm.company.name}</h2>', sent_mail.body)
        self.assertIn(f'<td style="width: 50%; font-size: 10pt;"> {regrade_reseason.identifier} </td>', sent_mail.body)


@tag('job')
class SendVoidMailTest(ACTestCase):

    @patch('core.freights.models.FreightContract.get_void_recipients_for_mail')
    def test_send_void_mail(self, mock_get_void_recipients):
        company = CompanyFactory(type_id=1, business_name='growerfoo')
        employee = EmployeeFactory(company=company)
        movement = FreightMovementFactory(created_by=employee)
        mock_get_void_recipients.return_value = ({'provider': ['<EMAIL>']}, {'customer': ['<EMAIL>']})
        readonly_recipients, actor_recipients = movement.get_void_recipients_for_mail()
        self.assertEqual(readonly_recipients, {'provider': ['<EMAIL>']})
        self.assertEqual(actor_recipients, {'customer': ['<EMAIL>']})
        job = Job.create({'status': 'pending',
            'params': {'object_id': movement.id, 'raised_for': 'movement', 'acted_by': employee.id,
                'acceptance_required': False, 'subject': 'Void Request', 'request_reason': 'Test Void', },
            'type': 'send_void_mail', })
        self.assertTrue(job.persisted)

        job.run_now()
        job.refresh_from_db()
        self.assertEqual(job.status, 'finished')
        self.assertEqual(len(mail.outbox), 1)
        sent_mail = mail.outbox[0]
        self.assertEqual(sent_mail.subject, 'Void Request')
        self.assertIn(f'{movement.identifier}', sent_mail.body)
        self.assertIn('Test Void', sent_mail.body)


class AcceptRejectURL(AuthSetup):
    @patch('core.common.tasks.uuid')
    def test_accept_and_reject_order_url(self, uuid_mock):
        order = FreightOrder(id=2)
        order.unresolved_amend_request = Mock(return_value=True)
        hex_mock = Mock()
        hex_mock.hex = 'some-hex-code'
        uuid_mock.uuid4 = Mock(return_value=hex_mock)
        recipient = Mock()
        recipient.id = 1
        company_mock = Mock()
        company_mock.is_registered = True
        recipient.company = company_mock
        recipient.refresh_token = Mock(return_value='some-key')
        bash_url = f'{WEB_URL}#'

        accept_url, reject_url = accept_and_reject_void_url(order, recipient, 'order')
        self.assertEqual(accept_url, f'{bash_url}/?referrerUrl=/freights/orders/{str(order.id)}/order')
        self.assertEqual(reject_url, f'{bash_url}/?referrerUrl=/freights/orders/{str(order.id)}/order')

        company_mock.is_registered = False

        accept_url, reject_url = accept_and_reject_void_url(order, recipient, 'order')
        self.assertEqual(accept_url, f'{bash_url}/some-key/some-hex-code/freights/orders/{str(order.id)}/void/confirm')
        self.assertEqual(reject_url, f'{bash_url}/some-key/some-hex-code/freights/orders/{str(order.id)}/void/reject')

        accept_url, reject_url = accept_and_reject_void_url(order, recipient, 'order')
        self.assertEqual(accept_url, f'{bash_url}/some-key/some-hex-code/freights/orders/{str(order.id)}/void/confirm')
        self.assertEqual(reject_url, f'{bash_url}/some-key/some-hex-code/freights/orders/{str(order.id)}/void/reject')

        self.assertEqual(uuid_mock.uuid4.call_count, 2)
        self.assertEqual(recipient.refresh_token.call_count, 2)
