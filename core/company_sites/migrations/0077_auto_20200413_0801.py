# Generated by Django 2.2 on 2020-04-13 08:01

from django.db import migrations


def update_slot_on_route(apps, schema_editor):
    FreightSlot = apps.get_model('company_sites', 'FreightSlot')
    FreightSlot.objects.filter(movement__status='in_progress', on_route=False, type='inload').update(on_route=True)


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0076_auto_20200413_0759'),
    ]

    operations = [
        migrations.RunPython(update_slot_on_route)
    ]
