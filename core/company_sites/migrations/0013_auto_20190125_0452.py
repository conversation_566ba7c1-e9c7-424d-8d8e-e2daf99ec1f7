# Generated by Django 2.1.3 on 2019-01-25 04:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.db.models.manager


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0012_auto_20190124_1132'),
    ]

    operations = [
        migrations.AlterModelManagers(
            name='companysite',
            managers=[
                ('bhcs', django.db.models.manager.Manager()),
            ],
        ),
        migrations.AlterField(
            model_name='historicalcompanysite',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalcompanysite',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
    ]
