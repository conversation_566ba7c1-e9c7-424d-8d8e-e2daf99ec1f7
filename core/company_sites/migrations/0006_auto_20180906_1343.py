# Generated by Django 2.1.1 on 2018-09-06 13:43

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0005_auto_20180827_0550'),
    ]

    operations = [
        migrations.AlterField(
            model_name='companysite',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AlterField(
            model_name='companysite',
            name='marketzone',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='marketzones.Marketzone'),
        ),
        migrations.AlterField(
            model_name='companysite',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator('^(02|03|07|08)[0-9]+$', 'Must start with 02, 03, 07 or 08')]),
        ),
        migrations.Alter<PERSON>ield(
            model_name='historicalcompanysite',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AlterField(
            model_name='historicalcompanysite',
            name='phone',
            field=models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator('^(02|03|07|08)[0-9]+$', 'Must start with 02, 03, 07 or 08')]),
        ),
    ]
