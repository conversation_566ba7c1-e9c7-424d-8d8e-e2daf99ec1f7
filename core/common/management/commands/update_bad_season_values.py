from django.core.management import BaseCommand
from django.db import models



class Command(BaseCommand):
    help = 'replace  invalid season with null'

    def handle(self, *args, **options):
        season_range = ["{}/{}".format(20 + date - 1, 20 + date) for date in range(-15, 15)]
        from core.contracts.models import Contract
        from core.freights.models import FreightContract, FreightOrder
        from core.loads.models import Load
        from core.vendor_decs.models import CommodityVendorDec
        filter_param = models.Q(season__in=season_range) | models.Q(season=None) | models.Q(season='')

        Contract.objects.exclude(filter_param).update(season=None)
        FreightContract.objects.exclude(filter_param).update(season=None)
        FreightOrder.objects.exclude(filter_param).update(season=None)
        Load.objects.exclude(filter_param).update(season=None)
        CommodityVendorDec.objects.exclude(filter_param).update(season=None)
