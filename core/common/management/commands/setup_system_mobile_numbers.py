from functools import reduce

from django.core.management import BaseCommand

from core.common.utils import is_production, is_staging
from core.mobile_messages.models import SystemNumber as Number


AU_PROD_NUMBERS = [
    '61472880249',
    '61472880508',
    '61472880932',
    '61472880173',
    '61472880331'
]
US_PROD_NUMBERS = [
    '18335532872',
    '18335532873',
    '18335532874',
    '18335532875',
    '18335532876',
    '18335532877',
    '18335532879',
]
AU_TEST_NUMBERS = [
    '61472880509',
]
US_TEST_NUMBERS = [
    '18335533143',
]


class Command(BaseCommand):
    help = 'Setup system mobile numbers for each country per env'

    def handle(self, *args, **options):
        numbers = []
        if is_production():
            for number in AU_PROD_NUMBERS:
                numbers.append(Number(country_id=1, number=number))
            for number in US_PROD_NUMBERS:
                numbers.append(Number(country_id=2, number=number))
        if is_staging():
            for number in AU_TEST_NUMBERS:
                numbers.append(Number(country_id=1, number=number))
            for number in US_TEST_NUMBERS:
                numbers.append(Number(country_id=2, number=number))

        new_numbers = []

        if numbers:
            for number in numbers:
                if not Number.objects.filter(country_id=number.country_id, number=number.number).exists():
                    new_numbers.append(number)

        if new_numbers:
            print("**Creating Country System Numbers**")
            Number.objects.bulk_create(new_numbers)
