import re

from pydash import get, set_

from core.commodities.models import Commodity

def convert_to_unit(item, unit, country=None):  # pylint: disable=too-many-branches
    fields = [
        'inferredTonnage', 'tonnage', 'totalDeliveredTonnage', 'accountedTonnage',
        'allocatedTonnageForUser', 'deliveredTonnage', 'outstandingTonnage', 'remainingTonnage',
        'progressTonnage', 'invoicedTonnage', 'plannedTonnage', 'tonnageDisplayValue', 'inferredPlannedTonnage',
        'parentAccountedTonnage', 'parentUnaccountedTonnage', 'unaccountedTonnage', 'actualTonnage', 'netWeight',
        'freightDelivery.load.0.netWeight', 'freightDelivery.load.0.grossWeight', 'freightDelivery.load.0.tareWeight',
        'freightPickup.load.0.netWeight', 'freightPickup.load.0.grossWeight', 'freightPickup.load.0.tareWeight',
        'invoicedTonnageDisplayName', 'totalTonnage',
        'inload.netWeight', 'inload.grossWeight', 'inload.tareWeight',
        'outload.netWeight', 'outload.grossWeight', 'outload.tareWeight', 'tonnageWithShrinkage',
        'storageLevels', 'estimatedNetWeight', 'grossWeight', 'tareWeight', 'confirmedTonnage',
        'parentTotalTonnageWithTolerance', 'parentTotalDeliveredTonnage', 'totalTonnageWithTolerance', 'maxTonnage',
        'parentTotalProgressTonnage', 'maxAllowedTonnageOnOrder', 'maxAllowedTonnageForAmend',
        'tonnageWithTargetMoisture', 'maxAllowedTonnageAndReason', 'fmTtLoadTonnage',
        'freightContracts', 'titleTransfers', 'capacity', 'stock', 'size', 'balance', 'quantityBalance',
        'throughputBalance'
    ]
    should_convert = False
    if ('commodityId' in item or 'size' in item) and any(field for field in fields if get(item, field)):  # pylint: disable=too-many-nested-blocks
        commodity_id = get(item, 'commodityId') or get(
            country, 'config.unit_conversion_default_commodity_id', None)
        commodity = Commodity.objects.filter(id=commodity_id).first()
        show_conversions = commodity.country.should_convert_unit if commodity else False
        if commodity and show_conversions:
            should_convert = True
            for field in [field for field in fields if get(item, field)]:
                tonnage = get(item, field)
                if (field in ['maxAllowedTonnageAndReason'] and isinstance(tonnage, dict) and
                        'maxAllowedTonnageOnOrder' in tonnage):
                    tonnage['maxAllowedTonnageOnOrder'] = commodity.convert_to(
                        tonnage['maxAllowedTonnageOnOrder'], commodity.unit, unit, True
                    )
                    set_(item, field, tonnage)
                elif field in ['tonnageDisplayValue', 'invoicedTonnageDisplayName']:
                    convert_in_string(commodity, field, item, tonnage, unit)
                elif field in []:  # only change unit
                    replace_unit_only(field, item, tonnage, unit)
                elif field in ['storageLevels'] and isinstance(tonnage, list):
                    for val in tonnage:
                        set_(val, 'tonnage', commodity.convert_to(val.get('tonnage'), commodity.unit, unit, True))
                elif field in ['freightContracts', 'titleTransfers'] and isinstance(tonnage, list):
                    for val in tonnage:
                        if 'tonnage' in val:
                            convert_in_string(commodity, 'tonnage', val, val.get('tonnage'), unit)
                elif field in ['size', 'capacity']:
                    set_(item, field, commodity.convert_to(tonnage, get(item, 'unit'), unit, True))
                else:
                    set_(item, field, commodity.convert_to(tonnage, commodity.unit, unit, True))
    elif isinstance(item, dict) and (  # pylint: disable=too-many-nested-blocks
            isinstance(next(iter(item.values())), list) or  # For Stocks API
            isinstance(item.get('items', None), list)  # for receivables/payables
    ):
        for _, value in item.items():
            if (isinstance(value, list) and value and get(value, '0.commodityId') and
                    (get(value, '0.tonnage') or get(value, '0.estimatedNetWeight'))):
                commodity_id = get(value, '0.commodityId')
                commodity = Commodity.objects.filter(id=commodity_id).first()
                show_conversions = commodity.country.should_convert_unit
                if commodity and show_conversions:
                    should_convert = True
                    for i in value:
                        for field in ['tonnage', 'remainingSpace', 'tonnageDisplayName', 'estimatedNetWeight',
                                      'tonnageDisplayValue']:
                            value = get(i, field)
                            if value:
                                if field in ['tonnageDisplayName', 'tonnageDisplayValue']:
                                    convert_in_string(commodity, field, i, value, unit)
                                else:
                                    set_(i, field, commodity.convert_to(value, commodity.unit, unit, True))

    return {**item, 'requestedUnit': unit} if should_convert else item


def replace_unit_only(field, item, tonnage, unit):
    matches = re.findall(r'\d+\.\d+', tonnage)
    if matches:
        try:
            tonnage = '{:.2f}'.format(float(matches[0]))
            set_(item, field, f"{tonnage} {unit}")
        except:  # pylint: disable=bare-except
            pass


def convert_in_string(commodity, field, item, tonnage, unit):
    matches = re.findall(r'\d+\.\d+', tonnage)
    if matches:
        from_unit = tonnage.replace(matches[0], '').replace(' ', '')
        try:
            new_tonnage = '{:.2f}'.format(commodity.convert_to(float(matches[0]), from_unit, unit, True))
            set_(item, field, f"{new_tonnage} {unit}")
        except:  # pylint: disable=bare-except
            pass
