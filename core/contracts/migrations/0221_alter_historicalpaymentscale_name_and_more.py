# Generated by Django 5.1.7 on 2025-03-25 09:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0220_contract_sales_order_id_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalpaymentscale',
            name='name',
            field=models.CharField(choices=[('Flat', 'Flat'), ('AOF Pay Scale Basis 42% Oil and 0% Admix', 'AOF Pay Scale Basis 42% Oil and 0% Admix'), ('AOF Pay Scale Basis 42% Oil (Cap 46%) and 0% Admix', 'AOF Pay Scale Basis 42% Oil (Cap 46%) and 0% Admix'), ('AOF - Basis 42% Oil (Cap 44%), 0% Admix', 'AOF - Basis 42% Oil (Cap 44%), 0% Admix'), ('AOF - Basis 42% Oil (Cap 45%), 0% Admix', 'AOF - Basis 42% Oil (Cap 45%), 0% Admix'), ('Flat Price, Deductions for Oil below 42% and Admix above 3%', 'Flat Price, Deductions for Oil below 42% and Admix above 3%')], max_length=100),
        ),
        migrations.AlterField(
            model_name='paymentscale',
            name='name',
            field=models.CharField(choices=[('Flat', 'Flat'), ('AOF Pay Scale Basis 42% Oil and 0% Admix', 'AOF Pay Scale Basis 42% Oil and 0% Admix'), ('AOF Pay Scale Basis 42% Oil (Cap 46%) and 0% Admix', 'AOF Pay Scale Basis 42% Oil (Cap 46%) and 0% Admix'), ('AOF - Basis 42% Oil (Cap 44%), 0% Admix', 'AOF - Basis 42% Oil (Cap 44%), 0% Admix'), ('AOF - Basis 42% Oil (Cap 45%), 0% Admix', 'AOF - Basis 42% Oil (Cap 45%), 0% Admix'), ('Flat Price, Deductions for Oil below 42% and Admix above 3%', 'Flat Price, Deductions for Oil below 42% and Admix above 3%')], max_length=100),
        ),
    ]
