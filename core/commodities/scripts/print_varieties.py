import csv
import json

res = []
count = 1

csvfile = open('./WheatVarieties.csv', "r")
fieldnames = ("Variety","Western Category","Southern Category","South-Eastern Category","Northern Category","Western Maximum Grade","Southern Maximum Grade","South-Eastern Maximum Grade","Northern Maximum Grade")
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _json = {
            "pk": count,
            "model": "commodities.variety",
            "fields": {
                "name": _dict["Variety"].title(),
                "details": {
                    "categories": {},
                    "maximum_grades": {},
                },
                "created_at": "2018-05-11T08:00:00+00:00",
                "updated_at": "2018-05-11T08:00:00+00:00",
                "created_by": 1,
                "updated_by": 1,
                "commodity_id": 1
            }
        }
        _json["fields"]["details"]["categories"]["western"] = _dict["Western Category"]
        _json["fields"]["details"]["categories"]["southern"] = _dict["Southern Category"]
        _json["fields"]["details"]["categories"]["south_eastern"] = _dict["South-Eastern Category"]
        _json["fields"]["details"]["categories"]["northern"] = _dict["Northern Category"]
        _json["fields"]["details"]["maximum_grades"]["western"] = _dict["Western Maximum Grade"]
        _json["fields"]["details"]["maximum_grades"]["southern"] = _dict["Southern Maximum Grade"]
        _json["fields"]["details"]["maximum_grades"]["south_eastern"] = _dict["South-Eastern Maximum Grade"]
        _json["fields"]["details"]["maximum_grades"]["northern"] = _dict["Northern Maximum Grade"]
        res.append(_json)
        count += 1
print(len(res))
print(count)
csvfile = open('./BarleyVarieties.csv', "r")
fieldnames = ("Variety","Grade 1 Name","Grade 2 Name","Grade 3 Name","Maximum Grade")
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _json = {
            "pk": count,
            "model": "commodities.variety",
            "fields": {
                "name": _dict["Variety"].title(),
                "commodity_id": 2,
                "details": {},
                "created_at": "2018-05-11T08:00:00+00:00",
                "updated_at": "2018-05-11T08:00:00+00:00",
                "created_by": 1,
                "updated_by": 1,
            },
        }
        _json["fields"]["name"] = _dict["Variety"].title()
        _json["fields"]["details"]["grade_1"] = _dict["Grade 1 Name"]
        _json["fields"]["details"]["grade_2"] = _dict["Grade 2 Name"]
        _json["fields"]["details"]["grade_3"] = _dict["Grade 3 Name"]
        _json["fields"]["details"]["maximum_grade"] = _dict["Maximum Grade"]
        res.append(_json)
        count += 1

print(len(res))
print(count)
csvfile = open('./CanolaVarieties.csv', "r")
fieldnames = ("Variety","Classification / Maximum Grade")
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _json = {
            "pk": count,
            "model": "commodities.variety",
            "fields": {
                "name": _dict["Variety"].title(),
                "commodity_id": 3,
                "details": {},
                "created_at": "2018-05-11T08:00:00+00:00",
                "updated_at": "2018-05-11T08:00:00+00:00",
                "created_by": 1,
                "updated_by": 1,
            },
        }
        _json["fields"]["details"]["maximum_grade"] = _dict["Classification / Maximum Grade"]
        count += 1
        res.append(_json)
print(len(res))
print(count)

csvfile = open('./SorghumVarieties.csv', "r")
fieldnames = ("Variety",)
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _json = {
            "pk": count,
            "model": "commodities.variety",
            "fields": {
                "name": _dict["Variety"].title(),
                "commodity_id": 4,
                "details": {},
                "created_at": "2018-05-11T08:00:00+00:00",
                "updated_at": "2018-05-11T08:00:00+00:00",
                "created_by": 1,
                "updated_by": 1,
            },
        }
        count += 1
        res.append(_json)
print(len(res))

csvfile = open('./LentilsVarieties.csv', "r")
fieldnames = ("Variety",)
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _json = {
            "pk": count,
            "model": "commodities.variety",
            "fields": {
                "name": _dict["Variety"].title(),
                "commodity_id": 5,
                "details": {},
                "created_at": "2018-05-11T08:00:00+00:00",
                "updated_at": "2018-05-11T08:00:00+00:00",
                "created_by": 1,
                "updated_by": 1,
            },
        }
        count += 1
        res.append(_json)

print(len(res))
print(count)
csvfile = open('./ChickpeasVarieties.csv', "r")
fieldnames = ("Variety","Classification",)
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _json = {
            "pk": count,
            "model": "commodities.variety",
            "fields": {
                "name": _dict["Variety"].title(),
                "details": {},
                "created_at": "2018-05-11T08:00:00+00:00",
                "updated_at": "2018-05-11T08:00:00+00:00",
                "created_by": 1,
                "updated_by": 1,
            },
        }
        _json["fields"]["details"]["type"] = _dict["Classification"]
        _json["fields"]["commodity_id"] = 6 if(_dict["Classification"] == 'Desi') else 7
        count += 1
        res.append(_json)

print(len(res))
print(count)
print(json.dumps(res))
