# Generated by Django 2.0.7 on 2018-08-16 10:18

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('companies', '0002_auto_20180816_1018'),
        ('marketzones', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('regions', '0001_initial'),
        ('company_sites', '0002_auto_20180816_1018'),
        ('locations', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalcompanysite',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalcompanysite',
            name='history_user',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalcompanysite',
            name='marketzone',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='marketzones.Marketzone'),
        ),
        migrations.AddField(
            model_name='historicalcompanysite',
            name='region',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='regions.Region'),
        ),
        migrations.AddField(
            model_name='historicalcompanysite',
            name='site_operator',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='historicalcompanysite',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='companysite',
            name='address',
            field=models.OneToOneField(on_delete=django.db.models.deletion.DO_NOTHING, related_name='company_site', to='locations.Location'),
        ),
        migrations.AddField(
            model_name='companysite',
            name='company',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.Company'),
        ),
        migrations.AddField(
            model_name='companysite',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='company_sites_companysite_related_created_by', related_query_name='company_sites_companysites_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='companysite',
            name='marketzone',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='marketzones.Marketzone'),
        ),
        migrations.AddField(
            model_name='companysite',
            name='region',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='regions.Region'),
        ),
        migrations.AddField(
            model_name='companysite',
            name='site_operator',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='companysite',
            name='updated_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='company_sites_companysite_related_updated_by', related_query_name='company_sites_companysites_updated_by', to=settings.AUTH_USER_MODEL),
        ),
    ]
