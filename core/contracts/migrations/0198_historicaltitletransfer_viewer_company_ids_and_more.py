# Generated by Django 4.1.13 on 2024-02-06 05:09

import django.contrib.postgres.fields
import django.contrib.postgres.indexes
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0197_auto_20240102_0958'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicaltitletransfer',
            name='viewer_company_ids',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), blank=True, null=True, size=None),
        ),
        migrations.AddField(
            model_name='titletransfer',
            name='viewer_company_ids',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), blank=True, null=True, size=None),
        ),
        migrations.AddIndex(
            model_name='titletransfer',
            index=models.Index(fields=['status'], name='title_trans_status_ba0e76_idx'),
        ),
        migrations.AddIndex(
            model_name='titletransfer',
            index=django.contrib.postgres.indexes.GinIndex(fields=['viewer_company_ids'], name='title_trans_viewer__e27db5_gin'),
        ),
    ]
