FREQUENCY_DAILY = 'daily'
MISSING_DOCKET_ALERT = 'Missing Docket Alert for Freight Providers'
MISSING_DOCKET_PICKUP_SITE_ALERT = 'Missing Docket Alert for Pickup Site'
SPEC_AVG_REPORT_ALERT = 'Receival Report'
MASS_LIMIT_BREACH_ALERT = 'Mass Limit Breach'
DELIVERY_CONFIRMATION_SMS_ALERT = 'Delivery Confirmation - SMS'
PICKUP_CONFIRMATION_SMS_ALERT = 'Pickup Confirmation - SMS'
CLASS_1_MATERIAL_ALERT = 'Class 1 material'
PICKUP_CONFIRMATION_ALERT = 'Pickup Confirmation - Email'
DELIVERY_CONFIRMATION_ALERT = 'Delivery Confirmation - Email'
OUTLOAD_REPORT_ALERT = 'Outload Report'
SLOT_BOOKING_UPDATE_ALERT = 'Slot Booking Updates'
TRUCK_CLEANLINESS_DECLARATION_NOT_FILLED_ALERT = 'Truck Cleanliness Declaration Not Filled'
DOCKET_CLARIFICATION_ALERT = 'Docket Clarifications'
STOCK_OPERATIONS_UPDATE_ALERT = 'Stock Operations Update'
STOCK_AUTO_UPDATE_ALERT = 'Stock Auto Update'
ACQUISITION_REPORT_ALERT = 'Acquisition Report'
NEW_CASH_PRICE_FOR_MY_STOCKS_ALERT = 'New Cash Price For My Stocks'
LEADING_CASH_PRICE_WARNING_ALERT = 'Leading Cash Price Warning'
BULK_INVOICE_ALERT = 'Automatic Invoice Emails'

MISSING_DOCKET_ALERT_CODE = 'missing_docket_alert'
MISSING_DOCKET_PICKUP_SITE_ALERT_CODE = 'missing_docket_pickup_site_alert'
SPEC_AVG_REPORT_ALERT_CODE = 'spec_avg_report'
MASS_LIMIT_BREACH_ALERT_CODE = 'mass_limit'
DELIVERY_CONFIRMATION_SMS_ALERT_CODE = 'inload_done'
PICKUP_CONFIRMATION_SMS_ALERT_CODE = 'pickup_confirmation_sms'
CLASS_1_MATERIAL_ALERT_CODE = 'class_1_material'
PICKUP_CONFIRMATION_ALERT_CODE = 'pickup_information'
DELIVERY_CONFIRMATION_ALERT_CODE = 'delivery_information'
OUTLOAD_REPORT_ALERT_CODE = 'outload_report'
SLOT_BOOKING_UPDATE_ALERT_CODE = 'slot_booking_alert'
TRUCK_CLEANLINESS_DECLARATION_NOT_FILLED_ALERT_CODE = 'truck_cleanliness_declaration_alert'
DOCKET_CLARIFICATION_ALERT_CODE = 'docket_clarification_alert'
STOCK_OPERATIONS_UPDATE_ALERT_CODE = 'stock_operations_update_alert'
STOCK_AUTO_UPDATE_ALERT_CODE = 'stock_auto_update_alert'
ACQUISITION_REPORT_ALERT_CODE = 'acquisition_report_alert'
NEW_CASH_PRICE_FOR_MY_STOCKS_ALERT_CODE = 'new_cash_price_for_my_stocks_alert'
LEADING_CASH_PRICE_WARNING_ALERT_CODE = 'leading_cash_price_warning_alert'
BULK_INVOICE_ALERT_CODE = 'bulk_invoice_alert'

EMAIL_CHANNEL = 'email'
SMS_CHANNEL = 'sms'
MOBILE_PUSH_NOTIFICATION_CHANNEL = 'mobile_push_notification'

STOCK_ALERT_DATA_LIMIT = 50

ALERT_NAMES = (
    (MISSING_DOCKET_ALERT_CODE, MISSING_DOCKET_ALERT),
    (MISSING_DOCKET_PICKUP_SITE_ALERT_CODE, MISSING_DOCKET_PICKUP_SITE_ALERT),
    (SPEC_AVG_REPORT_ALERT_CODE, SPEC_AVG_REPORT_ALERT),
    (MASS_LIMIT_BREACH_ALERT_CODE, MASS_LIMIT_BREACH_ALERT),
    (PICKUP_CONFIRMATION_SMS_ALERT_CODE, PICKUP_CONFIRMATION_SMS_ALERT),
    (DELIVERY_CONFIRMATION_SMS_ALERT_CODE, DELIVERY_CONFIRMATION_SMS_ALERT),
    (CLASS_1_MATERIAL_ALERT_CODE, CLASS_1_MATERIAL_ALERT),
    (PICKUP_CONFIRMATION_ALERT_CODE, PICKUP_CONFIRMATION_ALERT),
    (DELIVERY_CONFIRMATION_ALERT_CODE, DELIVERY_CONFIRMATION_ALERT),
    (OUTLOAD_REPORT_ALERT_CODE, OUTLOAD_REPORT_ALERT),
    (SLOT_BOOKING_UPDATE_ALERT_CODE, SLOT_BOOKING_UPDATE_ALERT),
    (TRUCK_CLEANLINESS_DECLARATION_NOT_FILLED_ALERT_CODE, TRUCK_CLEANLINESS_DECLARATION_NOT_FILLED_ALERT),
    (DOCKET_CLARIFICATION_ALERT_CODE, DOCKET_CLARIFICATION_ALERT),
    (STOCK_OPERATIONS_UPDATE_ALERT_CODE, STOCK_OPERATIONS_UPDATE_ALERT),
    (STOCK_AUTO_UPDATE_ALERT_CODE, STOCK_AUTO_UPDATE_ALERT),
    (ACQUISITION_REPORT_ALERT_CODE, ACQUISITION_REPORT_ALERT),
    (NEW_CASH_PRICE_FOR_MY_STOCKS_ALERT_CODE, NEW_CASH_PRICE_FOR_MY_STOCKS_ALERT),
    (LEADING_CASH_PRICE_WARNING_ALERT_CODE, LEADING_CASH_PRICE_WARNING_ALERT),
    (BULK_INVOICE_ALERT_CODE, BULK_INVOICE_ALERT),
)

RECEIVE_ALERT_NAMES = (
    (PICKUP_CONFIRMATION_ALERT_CODE, PICKUP_CONFIRMATION_ALERT),
    (DELIVERY_CONFIRMATION_ALERT_CODE, DELIVERY_CONFIRMATION_ALERT),
    (SLOT_BOOKING_UPDATE_ALERT_CODE, SLOT_BOOKING_UPDATE_ALERT),
)

CHANNEL_TYPES = (
    (SMS_CHANNEL, 'SMS'),
    (EMAIL_CHANNEL, 'Email'),
    (MOBILE_PUSH_NOTIFICATION_CHANNEL, 'Mobile Push Notification'),
)

RECIPIENT_PARTY_TYPES = (
    ('site', 'Site'),
    ('customer', 'Customer'),
    ('freight_provider', 'Freight Provider'),
    ('sub_freight_provider', 'Sub Freight Provider'),
    ('own_company', 'Own Company'),
    ('stock_owner', 'Stock Owner'),
    ('delivery_site', 'Delivery Site'),
    ('pickup_site', 'Pickup Site'),
    ('seller', 'Seller'),
    ('buyer', 'Buyer'),
    ('truck_owner', 'Truck Owner'),
)

ALERT_FREQUENCY = (
    (FREQUENCY_DAILY, 'Daily'),
    ('weekly', 'Weekly'),
    ('monthly', 'Monthly'),
    ('daily_and_weekly', 'Daily and Weekly'),
)

ALERT_DESCRIPTION = {
    MISSING_DOCKET_ALERT_CODE: 'Get a weekly email with all the missing dockets for movements where you are '
                               'the freight provider',
    MISSING_DOCKET_PICKUP_SITE_ALERT_CODE: 'Get a weekly email with all the missing dockets for movements where '
                                           'you are the pickup site.',
    SPEC_AVG_REPORT_ALERT_CODE: 'Daily Receival Report',
    MASS_LIMIT_BREACH_ALERT_CODE: 'OverWeight Pickup/Delivery at my site',
    DELIVERY_CONFIRMATION_SMS_ALERT_CODE: 'Send SMS when inload is done at my site',
    PICKUP_CONFIRMATION_SMS_ALERT_CODE: 'Send SMS when Pickup is done from my site',
    CLASS_1_MATERIAL_ALERT_CODE: ('Alerts to notify the site if the truck has carried a dangerous Class 1 material as'
                                  ' one of its previous 3 loads'),
    PICKUP_CONFIRMATION_ALERT_CODE: 'Alerts to notify the parties about pickup information from your site',
    DELIVERY_CONFIRMATION_ALERT_CODE: 'Alerts to notify the parties about delivery information at your site',
    OUTLOAD_REPORT_ALERT_CODE: 'Get daily updates of all the outloads completed at your sites',
    SLOT_BOOKING_UPDATE_ALERT_CODE: 'Get instant updates every time a booking is created or modified on your site',
    TRUCK_CLEANLINESS_DECLARATION_NOT_FILLED_ALERT_CODE: 'Get notified when a truck cleanliness declaration is not '
                                                         'filled in advance of a planned movement',
    DOCKET_CLARIFICATION_ALERT_CODE: 'Get alerts when there is an issue on a docket attached to your movement',
    STOCK_OPERATIONS_UPDATE_ALERT_CODE: 'Get an email whenever a stock operation is performed at your site.',
    STOCK_AUTO_UPDATE_ALERT_CODE: 'Get an email whenever stocks at your site are auto updated due '
                                  'to change in stock owner business type.',
    ACQUISITION_REPORT_ALERT_CODE: 'Total Acquisition of the day',
    NEW_CASH_PRICE_FOR_MY_STOCKS_ALERT_CODE: 'Get alerted when a leading cash price is posted for your stock',
    LEADING_CASH_PRICE_WARNING_ALERT_CODE: ('Get alerted when you are no longer the leading cash price on a site for'
                                            ' given stock combination'),
    BULK_INVOICE_ALERT_CODE: 'Automatic emails on creation of invoices in bulk via Payable, '
                             'Receivable and Warehouse Dashboard.'
}

RECEIVE_ALERT_DESCRIPTION = {
    PICKUP_CONFIRMATION_ALERT_CODE: 'Alert to get pickup information for your movements',
    DELIVERY_CONFIRMATION_ALERT_CODE: 'Alert to get delivery information for your movements',
    SLOT_BOOKING_UPDATE_ALERT_CODE: '''
        Get instant updates every time a booking is created or modified for your movements
    ''',
}

RECEIVE_ALERT_ROLES = {
    PICKUP_CONFIRMATION_ALERT_CODE: [
        'customer',
        'freight_provider',
        'delivery_site',
        'pickup_site',
        'sub_freight_provider',
        'stock_owner'
    ],
    DELIVERY_CONFIRMATION_ALERT_CODE: [
        'customer',
        'freight_provider',
        'delivery_site',
        'pickup_site',
        'sub_freight_provider',
        'stock_owner'
    ],
    SLOT_BOOKING_UPDATE_ALERT_CODE: [
        'own_company_site',
        'customer',
        'freight_provider',
        'sub_freight_provider',
        'seller',
        'buyer'
    ]
}
