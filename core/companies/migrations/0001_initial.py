# Generated by Django 2.0.7 on 2018-08-16 10:18

import django.contrib.postgres.fields.jsonb
import django.core.validators
import simple_history.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AddedCompany',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
            ],
            options={
                'db_table': 'added_companies',
            },
        ),
        migrations.CreateModel(
            name='BHCSite',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.<PERSON>oleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('site', models.CharField(max_length=50)),
                ('state', models.CharField(max_length=50)),
                ('track', models.CharField(max_length=50)),
                ('ntp', models.CharField(max_length=50)),
                ('mode', models.CharField(max_length=255)),
                ('ld', models.FloatField()),
                ('area', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('mobile', models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')])),
                ('phone', models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')])),
                ('site_manager', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'bhc_sites',
            },
        ),
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('abn', models.CharField(max_length=11, unique=True, validators=[django.core.validators.RegexValidator('^[0-9]+$', 'Must be numbers only'), django.core.validators.MinLengthValidator(11)])),
                ('entity_name', models.CharField(max_length=255)),
                ('business_name', models.CharField(max_length=255)),
                ('mobile', models.CharField(max_length=10, validators=[django.core.validators.RegexValidator('^(02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')])),
                ('fax', models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(02|03|07|08)[0-9]+$', 'Must start with 02, 03, 07 or 08')])),
                ('website', models.CharField(blank=True, max_length=100, null=True, validators=[django.core.validators.RegexValidator('^((ftp|http|https):\\/\\/)?(www.)?(?!.*(ftp|http|https|www.))[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+((\\/)[\\w#]+)*(\\/\\w+\\?[a-zA-Z0-9_]+=\\w+(&[a-zA-Z0-9_]+=\\w+)*)?$', 'Must be a website address')])),
                ('owner_company_id', models.IntegerField(blank=True, null=True)),
                ('start_of_week', models.IntegerField(choices=[(0, 'Sunday'), (1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday')], default=1)),
                ('end_of_week', models.IntegerField(choices=[(0, 'Sunday'), (1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday')], default=5)),
            ],
            options={
                'db_table': 'companies',
            },
        ),
        migrations.CreateModel(
            name='CompanyType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('name', models.CharField(choices=[('grower', 'Grower'), ('broker', 'Broker'), ('logistics', 'Logistics'), ('bhc', 'BHC'), ('trader', 'Trader')], max_length=50)),
            ],
            options={
                'db_table': 'company_types',
            },
        ),
        migrations.CreateModel(
            name='HistoricalAddedCompany',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_date', models.DateTimeField()),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
            ],
            options={
                'verbose_name': 'historical added company',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalBHCSite',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('site', models.CharField(max_length=50)),
                ('state', models.CharField(max_length=50)),
                ('track', models.CharField(max_length=50)),
                ('ntp', models.CharField(max_length=50)),
                ('mode', models.CharField(max_length=255)),
                ('ld', models.FloatField()),
                ('area', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('mobile', models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')])),
                ('phone', models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')])),
                ('site_manager', models.CharField(blank=True, max_length=255, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_date', models.DateTimeField()),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
            ],
            options={
                'verbose_name': 'historical bhc site',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalCompany',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('abn', models.CharField(db_index=True, max_length=11, validators=[django.core.validators.RegexValidator('^[0-9]+$', 'Must be numbers only'), django.core.validators.MinLengthValidator(11)])),
                ('entity_name', models.CharField(max_length=255)),
                ('business_name', models.CharField(max_length=255)),
                ('mobile', models.CharField(max_length=10, validators=[django.core.validators.RegexValidator('^(02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')])),
                ('fax', models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(02|03|07|08)[0-9]+$', 'Must start with 02, 03, 07 or 08')])),
                ('website', models.CharField(blank=True, max_length=100, null=True, validators=[django.core.validators.RegexValidator('^((ftp|http|https):\\/\\/)?(www.)?(?!.*(ftp|http|https|www.))[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+((\\/)[\\w#]+)*(\\/\\w+\\?[a-zA-Z0-9_]+=\\w+(&[a-zA-Z0-9_]+=\\w+)*)?$', 'Must be a website address')])),
                ('owner_company_id', models.IntegerField(blank=True, null=True)),
                ('start_of_week', models.IntegerField(choices=[(0, 'Sunday'), (1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday')], default=1)),
                ('end_of_week', models.IntegerField(choices=[(0, 'Sunday'), (1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday')], default=5)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_date', models.DateTimeField()),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
            ],
            options={
                'verbose_name': 'historical company',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalCompanyType',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(blank=True, editable=False)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('name', models.CharField(choices=[('grower', 'Grower'), ('broker', 'Broker'), ('logistics', 'Logistics'), ('bhc', 'BHC'), ('trader', 'Trader')], max_length=50)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_date', models.DateTimeField()),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
            ],
            options={
                'verbose_name': 'historical company type',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
