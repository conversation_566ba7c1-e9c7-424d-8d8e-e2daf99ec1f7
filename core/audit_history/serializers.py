import json

from rest_framework.fields import Serializer<PERSON><PERSON>od<PERSON><PERSON>
from rest_framework.serializers import ModelSerializer

from pydash import get
from core.audit_history.models import AuditHistory


class AuditHistorySerializer(ModelSerializer):
    created_by = SerializerMethodField()
    id = SerializerMethodField()
    amended_details = SerializerMethodField()
    communication = SerializerMethodField()
    entity = SerializerMethodField()

    class Meta:
        model = AuditHistory
        fields = (
            'id', 'description', 'actor', 'created_at', 'created_by', 'amended_details', 'args',
            'communication', 'entity',
        )

    @staticmethod
    def get_created_by(obj):
        # Not making Employee Serializer for this because Audit History is an independent module and Employee Model
        # from other module should not be imported here.
        return {
            'first_name': obj.created_by.first_name,
            'last_name': obj.created_by.last_name,
        }

    @staticmethod
    def get_communication(obj):
        acceptance_request_id = get(obj, 'args.acceptance_request_id', get(obj, 'args.contract_acceptance_request_id'))
        if acceptance_request_id:
            communication_dict = obj.instance.request_communication_dict(acceptance_request_id)
            if not isinstance(communication_dict, dict):
                return communication_dict

            mail_status = communication_dict.get('mail_status')
            if mail_status:
                try:
                    communication_dict['mail_status'] = json.loads(mail_status)
                except (ValueError, TypeError):
                    pass
            return communication_dict

    @staticmethod
    def get_id(obj):
        return -obj.id

    def get_amended_details(self, obj):
        amended_details = {}
        if obj.description and obj.description.startswith('Mail sent'):
            return {}

        acceptance_request_id = get(obj, 'args.acceptance_request_id', get(obj, 'args.contract_acceptance_request_id'))
        if acceptance_request_id:
            amended_details = obj.instance.amended_details_for_acceptance_request(acceptance_request_id)
        return amended_details

    def get_entity(self, obj):
        return {'entity_id': get(obj, 'instance.id'), 'entity_type': get(obj, 'instance.entity')}
