# Generated by Django 2.2.20 on 2021-07-22 09:36

from django.db import migrations

def setCashBoardsTrue(apps, schema_editor):
    PlatformFeatures = apps.get_model('companies', 'PlatformFeatures')
    PlatformFeatures.objects.filter(plan_type='premium').update(cash_boards=True)

class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0058_platformfeatures_cash_boards'),
    ]

    operations = [
        migrations.RunPython(setCashBoardsTrue),
    ]
