# Generated by Django 4.1.9 on 2023-08-01 06:49

import core.common.models
import core.validation.validators
import dirtyfields.dirtyfields
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0045_material_country'),
        ('farms', '0114_alter_historicalstorage_type_alter_storage_type'),
        ('ngrs', '0044_alter_bankaccount_bank_alter_bankaccount_created_by_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contracts', '0187_alter_contracttype_name_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='StockAllocation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('tonnage', core.common.models.RoundedFloatField()),
                ('season', models.CharField(blank=True, max_length=5, null=True, validators=[core.validation.validators.season_validator])),
                ('commodity', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='commodities.commodity')),
                ('contract', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_allocations', to='contracts.contract')),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('grade', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='commodities.grade')),
                ('ngr', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='ngrs.ngr')),
                ('site', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='farms.farm')),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
                ('variety', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='commodities.variety')),
            ],
            options={
                'db_table': 'stock_allocations',
            },
            bases=(dirtyfields.dirtyfields.DirtyFieldsMixin, models.Model),
        ),
    ]
