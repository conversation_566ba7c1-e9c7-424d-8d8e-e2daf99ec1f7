from django.urls import path

from core.cash_board import views

app_name = 'core.cash_board'

urlpatterns = [
    path(
        'cash-prices/',
        views.CashPricesView.as_view(),
        name='cash_prices'
    ),
    path(
        'cash-prices/external/<str:external_system>/',
        views.ExternalCashPricesView.as_view(),
        name='cash_prices-externals'
    ),
    path(
        'cash-prices/external/<str:external_system>/activate/',
        views.ExternalCashPricesActivateView.as_view(),
        name='cash_prices-external-activate'
    ),
    path(
        'cash-prices/external/<str:external_system>/<str:external_reference>/',
        views.ExternalCashPriceView.as_view(),
        name='cash_prices-external'
    ),
    path(
        'cash-prices/<int:cash_price_id>/',
        views.CashPriceView.as_view(),
        name='cash_prices-detail'
    ),
    path(
        '<int:cash_price_id>/can-create-title-transfer/',
        views.CanCreateCashBoardView.as_view(),
        name='cash_prices-can-create'
    ),
    path(
        '<int:cash_price_id>/title-transfers/',
        views.TitleTransfersView.as_view(),
        name='cash_prices-title-transfers'
    ),
    path(
        'cash-prices/<int:cash_price_id>/<str:new_status>/',
        views.CashPriceStatusTransitionView.as_view(),
        name='cash_prices-title-transfers'
    ),
    path(
        'cash-prices/<int:cash_price_id>/<str:cash_price_status>/check/',
        views.CashPriceExistsInStatusView.as_view(),
        name='cash_prices-status-check'
    ),
    path(
        'cash-prices/csv/',
        views.UploadCashPricesView.as_view(),
        name='cash_prices-upload'
    ),
    path(
        'cash-prices/commodities/',
        views.CashPricesCommodityView.as_view(),
        name='cash_prices-commodities'
    ),
    path(
        'cash-prices/inactive/',
        views.CashPricesInactiveView.as_view(),
        name='cash_prices-inactive'
    )
]
