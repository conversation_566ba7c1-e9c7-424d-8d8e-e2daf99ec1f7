{% load static %}
{% load app_filters %}
{% with  date_time_format="M d Y, h:i A"  date_format="M d, Y" %}
<html style="-webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; font-family: sans-serif; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);">
    <head>
        <meta charset="UTF-8">
        <style>
            table tr {page-break-inside: avoid}
        </style>
    </head>
    <body style="-webkit-box-sizing: border-box; margin:0; -moz-box-sizing: border-box; box-sizing: border-box; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; line-height: 1.42857; color: #000000; background-color: #fff;">
        <div style="padding: 20px;">
            <h3>URGENT ACTION REQUIRED - A class 1 restricted material has been reported in a truck cleanliness declaration.</h3>
            <table>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Site:</td>
                    <td style="width: 50%; font-size: 10pt;">{{site.display_name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Load Type:</td>
                    <td style="width: 50%; font-size: 10pt;">{{load_type|capfirst}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Freight Provider:</td>
                    <td style="width: 50%; font-size: 10pt;">{{contract.provider.name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">{{'truck_rego' | to_country_label:site.country_code}}:</td>
                    <td style="width: 50%; font-size: 10pt;">{{vendor_dec.assigned_rego}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Truck Driver:</td>
                    {% if vendor_dec.driver %}
                        <td style="width: 50%; font-size: 10pt;">{{vendor_dec.driver.name}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Truck Driver Mobile:</td>
                    {% if vendor_dec.driver.mobile %}
                        <td style="width: 50%; font-size: 10pt;">{{vendor_dec.driver.mobile}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Customer:</td>
                    {% if contract.customer %}
                        <td style="width: 50%; font-size: 10pt;">{{contract.customer.company.name}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Customer Contact:</td>
                    <td style="width: 50%; font-size: 10pt;">{{contract.customer.contact.name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Customer Contact Mobile:</td>
                    {% if contract.customer.contact.mobile %}
                        <td style="width: 50%; font-size: 10pt;">{{contract.customer.contact.mobile}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Order:</td>
                    {% if contract.order %}
                        <td style="width: 50%; font-size: 10pt;">{{contract.order.identifier}}</td>
                    {% endif %}
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Booking / Movement:</td>
                    <td style="width: 50%; font-size: 10pt;">{{contract.identifier}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Booking Date:</td>
                    <td style="width: 50%; font-size: 10pt;">{{booking_date}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Booking Time:</td>
                    <td style="width: 50%; font-size: 10pt;">{{booking_time}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Commodity:</td>
                    <td style="width: 50%; font-size: 10pt;">{{contract.commodity.display_name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Grade:</td>
                    <td style="width: 50%; font-size: 10pt;">{{contract.planned_grade.name}}</td>
                </tr>
                <tr>
                    <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">Season:</td>
                    <td style="width: 50%; font-size: 10pt;">{{contract.season}}</td>
                </tr>
            </table>
            <h4>Last 3 Loaded Commodities:</h4>
            <table>
                {% for prior_loads_row in vendor_dec.prior_loads_commodity_with_classes %}
                <tr>
                    {% with counter=0 %}
                        {% for prior_load in prior_loads_row %}
                            {% if forloop.counter == 1 %}
                                <td style="width: 50%; font-size: 11pt; font-weight: 500; color: #666666; margin: 0;">{{prior_load|capfirst}}:</td>
                            {% else %}
                                <td style="width: 50%; font-size: 10pt;">{{prior_load|capfirst}}</td>
                            {% endif %}
                        {% endfor %}
                    {% endwith %}
                </tr>
                {% endfor %}
            </table>
            {% if load_type == 'outload'%}
                <h4>Please ensure this truck is NOT loaded at your site.</h4>
            {% else %}
                <h4>Please ensure this truck is NOT tipped at your site.</h4>
            {% endif %}
        </div>
    </body>
</html>
{% endwith %}