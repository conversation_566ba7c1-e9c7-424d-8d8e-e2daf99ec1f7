# Generated by Django 2.1.2 on 2018-10-24 10:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0011_auto_20181016_0938'),
        ('companies', '0003_auto_20180906_1343'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='payment_term',
            field=models.ForeignKey(default=10, on_delete=django.db.models.deletion.DO_NOTHING, to='contracts.PaymentTerm'),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='payment_term',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='contracts.PaymentTerm'),
        ),
    ]
