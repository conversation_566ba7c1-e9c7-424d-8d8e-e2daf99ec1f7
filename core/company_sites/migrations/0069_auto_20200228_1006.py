# Generated by Django 2.2 on 2020-02-28 10:06

from django.db import migrations


def update_site_in_slots(apps, schema_editor):
    FreightSlot = apps.get_model('company_sites', 'FreightSlot')
    for slot in FreightSlot.objects.all():
        slot._site_id = slot.site_id
        slot.save()


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0068_auto_20200228_1003'),
    ]

    operations = [
        migrations.RunPython(update_site_in_slots)
    ]
