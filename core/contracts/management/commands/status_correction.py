from django.core.management.base import BaseCommand
from core.contracts.models import Contract


class Command(BaseCommand):
    help = "Recalculate all contracts status"

    def handle(self, *args, **kwargs):  # pylint: disable=unused-argument

        to_be_updated = Contract.status_to_be_updated()
        for status in to_be_updated.keys():
            if to_be_updated[status]:
                print('**********Updating status to', status)
                print('**********for contracts', to_be_updated[status])
                # Contract.objects.filter(id__in=to_be_updated[status]).update(status=status)
