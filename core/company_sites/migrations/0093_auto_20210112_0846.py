# Generated by Django 2.2.13 on 2021-01-12 08:46

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0092_auto_20210112_0812'),
    ]

    operations = [
        migrations.AddField(
            model_name='freightslot',
            name='trailer_slot_ids',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), blank=True, default=list, null=True, size=None),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='trailer_slot_ids',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), blank=True, default=list, null=True, size=None),
        ),
    ]
