# Generated by Django 4.1.7 on 2023-04-26 06:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0122_merge_20230421_1134'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalxeromapping',
            name='item_type',
            field=models.CharField(blank=True, choices=[('title_transfers', 'Title Transfers'), ('movements', 'Freight Movements'), ('carry', 'Carry Charges'), ('custom', 'Custom Items'), ('epr', 'EPR'), ('levy', 'Grain Levy'), ('inload_fees', 'Inload Fees'), ('outload_fees', 'Outload Fees'), ('storage_fees', 'Storage Fees'), ('transfer_fees', 'Transfer Fees'), ('stock_swap', 'Stock Swap'), ('regrade_reseason', 'Regrade Reseason')], max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='historicalxeromapping',
            name='transaction_type',
            field=models.CharField(choices=[('purchase_contract', 'Purchase Contract'), ('sale_contract', 'Sale Contract'), ('deductions', 'Deductions'), ('warehouse', 'Warehouse')], max_length=100),
        ),
        migrations.AlterField(
            model_name='xeromapping',
            name='item_type',
            field=models.CharField(blank=True, choices=[('title_transfers', 'Title Transfers'), ('movements', 'Freight Movements'), ('carry', 'Carry Charges'), ('custom', 'Custom Items'), ('epr', 'EPR'), ('levy', 'Grain Levy'), ('inload_fees', 'Inload Fees'), ('outload_fees', 'Outload Fees'), ('storage_fees', 'Storage Fees'), ('transfer_fees', 'Transfer Fees'), ('stock_swap', 'Stock Swap'), ('regrade_reseason', 'Regrade Reseason')], max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='xeromapping',
            name='transaction_type',
            field=models.CharField(choices=[('purchase_contract', 'Purchase Contract'), ('sale_contract', 'Sale Contract'), ('deductions', 'Deductions'), ('warehouse', 'Warehouse')], max_length=100),
        ),
    ]
