from django.core.management import BaseCommand

from core.common.constants import BHC_TYPE_ID
from core.companies.models import Company, PlatformFeatures


class Command(BaseCommand):
    help = 'Create Platform Features for BHCs that doesn\'t already have any Platform Features'

    def handle(self, *args, **options):
        bhc_company_ids_without_features = Company.objects.filter(
            type_id=BHC_TYPE_ID, platformfeatures__isnull=True
        ).values_list('id', flat=True)

        for bhc_company_id in bhc_company_ids_without_features:
            platform_features = PlatformFeatures.persist(company_id=bhc_company_id)

            if platform_features.errors:
                self.stdout.write(
                    "Following Errors Occurred for company_id: {id}\n{errors}".format(
                        id=bhc_company_id, errors=platform_features.errors
                    )
                )
            else:
                self.stdout.write("Successfully created Platform Features for company_id: {}".format(bhc_company_id))
