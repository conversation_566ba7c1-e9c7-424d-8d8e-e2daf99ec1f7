# Generated by Django 4.0.6 on 2022-09-19 06:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0099_alter_xeromapping_unique_together'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalxeromapping',
            name='item_type',
            field=models.CharField(blank=True, choices=[('title_transfers', 'Title Transfers'), ('movements', 'Freight Movements'), ('carry', 'Carry Charges'), ('custom', 'Custom Items'), ('epr', 'EPR'), ('levy', 'Grain Levy')], max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='xeromapping',
            name='item_type',
            field=models.CharField(blank=True, choices=[('title_transfers', 'Title Transfers'), ('movements', 'Freight Movements'), ('carry', 'Carry Charges'), ('custom', 'Custom Items'), ('epr', 'EPR'), ('levy', '<PERSON><PERSON> Levy')], max_length=250, null=True),
        ),
    ]
