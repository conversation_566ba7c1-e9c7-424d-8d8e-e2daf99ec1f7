# Generated by Django 2.0.7 on 2018-08-16 10:18

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('companies', '0001_initial'),
        ('locations', '0001_initial'),
        ('company_sites', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalcompanysite',
            name='address',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='locations.Location'),
        ),
        migrations.AddField(
            model_name='historicalcompanysite',
            name='company',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.Company'),
        ),
    ]
