import os

from core.loads.constants import NOTICE_NUMBER_19, NOTICE_NUMBER_20, NOTICE_NUMBER_23, NOTICE_NUMBER

# --------------------------------------------- GENERAL CONSTANTS ------------------------------------------------------

STATIC_URL_PREFIX = 'file:///code/core/contracts'


PRODUCTION_ENVIRONMENT = 'production'
STAGING_ENVIRONMENT = 'staging'
QA_ENVIRONMENT = 'qa'

CHUNK_SIZE = 50
CURRENT_USER = 'CURRENT_USER'
REQUEST_URL = 'REQUEST_URL'
REQUEST_TZ = 'REQUEST_TZ'
REQUEST_COUNTRY = 'REQUEST_COUNTRY'
REQUEST_UNIT = 'REQUEST_UNIT'
DEFAULT_PASSWORD_LENGTH = 8
DEFAULT_PASSWORD = 'pass@123'
DEFAULT_BASE64_DATATYPE = 'data:image/png;'
URL_FORMAT = "{domain}{suffix}"
SEARCH_PAGINATION_NEXT_PAGE_URL = (
    '{url}/{search_url}/?search={search_text}&page_size={page_size}'
    '&page={next_page}&order_by={order_by}&order={order}'
)
SEARCH_PAGINATION_CURRENT_PAGE_URL = (
    '{url}/{search_url}/?search={search_str}&page_size={page_size}'
    '&page={page}&order_by={order_by}&order={order}'
)
SEARCH_STRING_NONE_TEXT = 'None'

DEFAULT_PAYMENT_TERM_ID = 10
PAYMENT_TERM_30_DEOW_OF_DELIVERY = 12

DEFAULT_GOOGLE_MAP_DISTANCE_RESPONSE = {
    "distance": {
        "text": "50 km",
        "value": 50000
    },
    "duration": {
        "text": "2 hours",
        "value": 7200
    },
    "status": "OK"
}

UNKNOWN = 'Unknown Company'

# ----------------------------------------------- DATABASE CONSTANTS ---------------------------------------------------

FARM_MODEL = 'farm'
COMPANY_MODEL = 'company'
CUSTOM_ITEM_MODEL = 'customitem'
CARRY_ITEM_MODEL = 'carryitem'
GRAIN_LEVY_ITEM_MODEL = 'grainlevy'
EPR_ITEM_MODEL = 'epr'
EPR_ITEM_DB_MODEL = 'epritem'
SUBSCRIPTION_ITEM_MODEL = 'subscriptionitem'
CONTRACT_MODEL = 'contract'
TITLE_TRANSFER_MODEL = 'titletransfer'
FREIGHT_MOVEMENT_MODEL = 'freightcontract'
FREIGHT_ORDER_MODEL = 'freightorder'
WAREHOUSE_INLOAD_FEES_MODEL = 'warehouseinloaditem'
WAREHOUSE_OUTLOAD_FEES_MODEL = 'warehouseoutloaditem'
WAREHOUSE_THROUGHPUT_INLOAD_FEES_MODEL = 'warehousethroughputinloaditem'
WAREHOUSE_THROUGHPUT_OUTLOAD_FEES_MODEL = 'warehousethroughputoutloaditem'
WAREHOUSE_STORAGE_FEES_MODEL = 'warehousestorageitem'
WAREHOUSE_TRANSFER_FEES_MODEL = 'warehousetransferitem'
LOAD_MODEL = 'load'
WAREHOUSE_STOCK_SWAP_MODEL = 'warehousestockswapitem'
WAREHOUSE_REGRADE_RESEASON_MODEL = 'warehouseregradereseasonitem'
# ----------------------------------------------- PLATFORM CONFIG ------------------------------------------------------
BHC_TYPE_ID = 4
GROWER_TYPE_ID = 1
BROKER_TYPE_ID = 2
LOGISTICS_TYPE_ID = 3
TRADER_TYPE_ID = 5
SYSTEM_TYPE_ID = 6
AU_SYSTEM_COMPANY_ID = 1
US_SYSTEM_COMPANY_ID = -1
CA_SYSTEM_COMPANY_ID = -2
NZ_SYSTEM_COMPANY_ID = -3
AU_ROOT_USER_ID = 1
US_ROOT_USER_ID = -1
CA_ROOT_USER_ID = -2
NZ_ROOT_USER_ID = -3
ROOT_USER_IDS = [AU_ROOT_USER_ID, US_ROOT_USER_ID, CA_ROOT_USER_ID, NZ_ROOT_USER_ID]
SYSTEM_COMPANY_IDS = [AU_SYSTEM_COMPANY_ID, US_SYSTEM_COMPANY_ID, CA_SYSTEM_COMPANY_ID, NZ_SYSTEM_COMPANY_ID]
WHEAT_COMMODITY_ID = 1
BARLEY_COMMODITY_ID = 2
COTTONSEED_COMMODITY_ID = 19
ORGANIC_CORN_COMMODITY_ID = 107
CANOLA_CANADA_COMMODITY_ID = 187
AGRICHAIN_ABN = '***********'

OBSERVER_TYPE_ID = 10
COMPANY_ADMIN_TYPE_ID = 1
FARM_ADMIN_TYPE_ID = 4
OFFICE_ADMIN_TYPE_ID = 2
OFFICE_EMPLOYEE_TYPE_ID = 3
DRIVER_TYPE_ID = 9
UNASSIGNED_TYPE_ID = 12
FARM_EMPLOYEE_TYPE = 5
PAGE_SIZE_QUERY_PARAM = 'page_size'
PAGE_QUERY_PARAM = 'page'
LAST_PAGE = 'last'

ABNS_FOR_POOL_CONTRACTS = ['***********']

# ----------------------------------------------- EMAIL CONFIG ---------------------------------------------------------

FROM_EMAIL = '<EMAIL>'
SUPPORT_EMAIL = '<EMAIL>'
FROM_EMAIL_WITH_NAME = f'AgriChain Support <{FROM_EMAIL}>'
FROM_EMAIL_ADMIN_WITH_SENDER_NAME = '{sender_name} (via AgriChain) <<EMAIL>>'
FROM_EMAIL_ADMIN_WITH_AGRICHAIN_ADMIN_NAME = 'Information AgriChain <<EMAIL>>'

# ----------------------------------------------------------------------------------------------------------------------

#------------------------------------------ appModelMapper----------------------------------------

MODEL_MAPPER = {
    'contract': {'app': 'contracts', 'model': 'Contract'},
    'order': {'app': 'freights', 'model': 'FreightOrder'},
    'movement': {'app': 'freights', 'model': 'FreightContract'},
    'invoice': {'app': 'invoices', 'model': 'Invoice'},
    'stock_swap': {'app':'loads', 'model': 'StockSwap' },
    'regrade_reseason': {'app':'loads', 'model': 'RegradeReseason'}
}
COMMUNICATION_CLASS_MAPPER = {
    'contract': {'app': 'contracts', 'model': 'AcceptanceRequestCommunication'},
    'title_transfer': {'app': 'contracts', 'model': 'TitleTransferCommunication'},
    'order': {'app': 'freights', 'model': 'FreightOrderCommunication'},
    'movement': {'app': 'freights', 'model': 'FreightContractCommunication'},
    'invoice': {'app': 'invoices', 'model': 'InvoiceCommunication'},
}

INLOAD = 'inload'
OUTLOAD = 'outload'
KEY_INFO_MISSING = 'key_info_missing'
DOCKET_ISSUE = 'docket_issue'
VALUE_MISSING_IN_AC = 'value_missing_in_ac'
TONNAGE_EXHAUSTED = 'tonnage_exhausted'
SITE = 'site'
GRADE = 'grade'
STOCK_OWNER = 'stock_owner'
NET_WEIGHT = 'net_weight'
TRUCK = 'truck'
SEASON = 'season'
SEASON_NA = 'N/A'
CANOLA_SPEC = 'canola_spec'
IRRELEVANT_IMAGE = 'irrelevant_image'
WRONG_DOCKET = 'wrong_docket'
MULTIPLE_DOCKETS = 'multiple_dockets'
DOCKET_NOT_READABLE = 'docket_not_readable'
CONTRACT_TONNAGE_EXHAUSTED = 'contract_tonnage_exhausted'
ORDER_TONNAGE_EXHAUSTED = 'order_tonnage_exhausted'
LOAD_TYPES = (
    (INLOAD, 'Inload'),
    (OUTLOAD, 'Outload'),
)
DOCKET_ISSUE_CATEGORY = (
    (KEY_INFO_MISSING, 'Key Info Missing'),
    (DOCKET_ISSUE, 'Docket Issue'),
    (VALUE_MISSING_IN_AC, 'Value Missing in AgriChain'),
    (TONNAGE_EXHAUSTED, 'Tonnage Exhausted')
)
DOCKET_ISSUE_SUB_CATEGORIES = (
    (SITE, 'Site'),
    (GRADE, 'Grade'),
    (STOCK_OWNER, 'Stock Owner'),
    (NET_WEIGHT, 'Net Weight'),
    (TRUCK, 'Truck'),
    (SEASON, 'Season'),
    (CANOLA_SPEC, 'Canola Spec'),
    (IRRELEVANT_IMAGE, 'Irrelevant Image'),
    (WRONG_DOCKET, 'Wrong Docket'),
    (MULTIPLE_DOCKETS, 'Multiple Dockets'),
    (DOCKET_NOT_READABLE, 'Docket Not Available'),
    (CONTRACT_TONNAGE_EXHAUSTED, 'Contract Tonnage Exhausted'),
    (ORDER_TONNAGE_EXHAUSTED, 'Order Tonnage Exhausted')
)
KEY_INFO_SUB_CATEGORY = (
    (SITE, 'Site'),
    (GRADE, 'Grade'),
    (STOCK_OWNER, 'Stock Owner'),
    (NET_WEIGHT, 'Net Weight'),
    (TRUCK, 'Truck'),
    (SEASON, 'Season'),
    (CANOLA_SPEC, 'Canola Spec'),
)
DOCKET_ISSUE_SUB_CATEGORY = (
    (IRRELEVANT_IMAGE,'Irrelevant Image'),
    (WRONG_DOCKET, 'Wrong Docket'),
    (MULTIPLE_DOCKETS, 'Multiple Dockets'),
    (DOCKET_NOT_READABLE, 'Docket Not Available'),
)
VALUE_MISSING_IN_AC_SUB_CATEGORY = (
    (SITE, 'Site'),
    (STOCK_OWNER, 'Stock Owner'),
    (GRADE,'Grade'),
)
TONNAGE_EXHAUSTED_SUB_CATEGORY = (
    (CONTRACT_TONNAGE_EXHAUSTED, 'Contract Tonnage Exhausted'),
    (ORDER_TONNAGE_EXHAUSTED, 'Order Tonnage Exhausted'),
)

DOCKET_ISSUE_CATEGORY_DICT = {
    'key_info_missing': 'Key Info Missing',
    'docket_issue': 'Docker Issue',
    'value_missing_in_ac': 'Value Missing in AgriChain',
    'tonnage_exhausted': 'Tonnage Exhausted'
}

CURRENCIES = (
    ('AU$', 'AU$'),
    ('US$', 'US$'),
    ('CA$', 'CA$'),
    ('NZ$', 'NZ$'),
)


# ----------------------------------------------------------------------------------------------------------------------
MIDNIGHT_TIME = "00:00:00"
AGRICHAIN_LOGO_PUBLIC_URL = 'https://agrichain-api-production.s3-ap-southeast-2.amazonaws.com/assets/agrichain-logo-icon.png' # pylint: disable=line-too-long
AEST_TZ = 'Australia/Sydney'
AWST_TZ = 'Australia/Perth'
AEST_AEDT_TZ = 'AEST/AEDT'
US_CT_TZ = 'America/Chicago'
BROKER_NOTE_PREFIX = 'B'
SALES_CONFIRMATION_PREFIX = 'S'
CONTRACT_PREFIX = 'C'
FREIGHT_MOVEMENT_PREFIX = 'M'
FREIGHT_ORDER_PREFIX = 'F'
GRAIN_ORDER_PREFIX = 'G'
PICKUP_ORDER_PREFIX = 'P'
DELIVERY_ORDER_PREFIX = 'D'
TITLE_TRANSFER_PREFIX = 'T'
INVOICE_PREFIX = 'I'
REGRADE_LOAD_PREFIX = 'R'
LOAD_PREFIX = 'L'
SILO_TO_SILO_TRANSFER_PREFIX = 'S'
STOCK_SWAP_PREFIX = 'W'
METER_CUBE = 'm³'
BUSHEL = 'BU'
POUND = 'LB'
SHORT_TONNE = 'ST'
LB_TO_SHORT_TONNE_MULTIPLIER = 1/2000
LB_TO_MT_MULTIPLIER = 1/2204.622
LB_TO_CWT_MULTIPLIER = 1/100
CENTURY_TONNE = 'CWT'
KG = 'kg'
LITRE = 'litre'
LITRE_ABBREVIATION = 'L'
BALES = 'Bales'
MODULES = 'Modules'
MT = 'MT'
NUMBER_OF_BALES = "Number of {bales}".format(bales=BALES)
NUMBER_OF_MODULES = "Number of {modules}".format(modules=MODULES)
VOLUME = 'Volume'
LOAD = 'Load'
BIG_NUMBER = 99999999999999999
MEMOMIZE_TIMEOUT = 2 if os.environ.get('ENV') else 0
DATETIME_FORMAT = '%m/%d/%Y %H:%M %p'
# considering season starting from 1st october
CENTURY_BASE_YEAR = 2000
SEASON_MONTH = 10
SEASON_DATE = 1

# ---------------------------------CSV MAPPING FOR FILTERS------------------------------
FILTER_MAPPINGS = {
    "commodity__id__in": {'name': 'Commodity', 'app': 'commodities', 'model': 'Commodity'},
    "grade__id__in": {'name': 'Grade', 'app': 'commodities', 'model': 'Grade'},
    "status__in": {'name': 'Status'},
    "price_point__id__in": {'name': 'Price Point', 'app': 'contracts', 'model': 'ContractPricePoint'},
    "administration__invoicing__in": {'name': 'Invoicing'},
    "buyer__company__id__in": {'name': 'Buyer', 'app': 'companies', 'model': 'company', 'property': 'business_name'},
    "seller__company__id__in": {'name': 'Seller', 'app': 'companies', 'model': 'company', 'property': 'business_name'},
    "delivery_start_date__gte": {'name': "Delivery Start Date"},
    "delivery_end_date__lte": {'name': "Delivery End Date"},
    "season__in": {'name': 'Season'},
    "invoicing__in": {'name': 'Invoicing'},
    "freight_pickup__consignor__handler__id__in":{'name': 'Pickup Site', 'app': 'farms', \
        'model': 'farm', 'property': 'name'},
    "freight_delivery__consignee__handler__id__in":{'name': 'Delivery Site', 'app': 'farms', \
        'model': 'farm', 'property': 'name'},
    "customer__company__id__in": {'name': 'Customer', 'app': 'companies', \
        'model': 'company', 'property': 'business_name'},
    "provider__id__in": {'name': 'Freight Provider', 'app': 'companies', \
        'model': 'company', 'property': 'business_name'},
    "freight_pickup__date_time__gte": {'name': "Delivery Start Date"},
    "freight_delivery__date_time__lte": {'name': "Delivery End Date"},
    "freight_pickup__loads_set__truck__id__in": {"name": "Pickup Rego", "app": 'trucks',
                                                 'model': 'Truck', 'property': 'rego'},
    "freight_delivery__loads_set__truck__id__in": {"name": "Delivery Rego", "app": 'trucks',
                                                   'model': 'Truck', 'property': 'rego'},
    "planned_grade__id__in": {"name": "Planned Grade", 'app': 'commodities', 'model': 'Grade'},
    "updated_at__gte":  {"name": "Last modified date"},
    "updated_at__lte":  {"name": "Last modified date"},
    "type_id__in": {"name": "Order Type"},
    "payee__company_id__in": {"name": "Payee", 'app': 'companies', 'model': 'company', 'property': 'business_name'},
    "contract__commodity__id__in": {'name': 'Description', 'app': 'commodities', 'model': 'Commodity'},
    "payment_due_date__gte": {'name': "Payment Due"},
    "payment_due_date__lte": {'name': "Payment Due"},
}

CANNOT_CREATE_COMMODITY_VENDOR_DEC_SELLER_REASON = 'Only Seller can create Commodity Vendor Declaration'
CANNOT_CREATE_COMMODITY_VENDOR_DEC_STATUS_REASON = 'replace_with_entity is in draft/void/rejected status'
CANNOT_REQUEST_COMMODITY_VENDOR_DEC_SELLER_REASON = 'Only Buyer or Consignee can request for Vendor Declaration'

ALLIED_PLANT_CODES = {
    'tennyson': '4100',
    'tamworth': '2300',
    'picton': '2400',
    'north fremantle': '8100',
    'mile end': '5100',
    'kensington': '3100',
    'ballarat': '3200',
}

MAURI_ABN = "***********"

# HEADERS
REQUEST_USER_HEADER = 'X-AC-REQUEST-USER'
REQUEST_USER_COMPANY_HEADER = 'X-AC-REQUEST-COMPANY'
RESPONSE_TIME_HEADER = 'X-AC-RESPONSE-TIME'
REQUEST_URL_HEADER = 'X-AC-REQUEST-URL'
REQUEST_METHOD_HEADER = 'X-AC-REQUEST-METHOD'
RESPONSE_API_VERSION_HEADER = 'X-AC-API-VERSION'
RESPONSE_EXTERNAL_API_HEADER = 'X-AC-API-EXTERNAL'
REQUEST_DEVICE_ID_HEADER = 'X-AC-REQUEST-DEVICE-ID'
REQUEST_DEVICE_MODEL_HEADER = 'X-AC-REQUEST-DEVICE-MODEL'
REQUEST_DEVICE_SYSTEM_NAME_HEADER = 'X-AC-REQUEST-DEVICE-SYSTEM-NAME'
REQUEST_DEVICE_SYSTEM_VERSION_HEADER = 'X-AC-REQUEST-DEVICE-SYSTEM-VERSION'
REQUEST_DEVICE_APP_VERSION_HEADER = 'X-AC-REQUEST-DEVICE-APP-VERSION'
REQUEST_DEVICE_CONFIG_VERSION_HEADER = 'X-AC-REQUEST-DEVICE-CONFIG-VERSION'

MASS_LIMIT_MAPPING_FOR_EXTERNAL_BOOKING = {
    'GML': 'G',
    'RFS': 'RFS',
    'GHMS': 'GHMS',
    'CML': 'C',
    'HML': 'H',
    'PBS': 'PBS',
    'PBS - CML': 'CPBS',
    'PBS - GML': 'GPBS',
    'PBS - HML': 'HPBS',
    'GML+/Permit': '',
    'Notice': ''
}

NOTICE_NUMBER_MAPPINGS = {
    NOTICE_NUMBER: 'notice.notice.states',
    NOTICE_NUMBER_19: 'notice.notice_19.states',
    NOTICE_NUMBER_20: 'notice.notice_20.states',
    NOTICE_NUMBER_23: 'notice.notice_23.states'
}

MATERIALS_MAPPING_FOR_EXTERNAL_BOOKING = {
    '1': 'COR',
    '2': 'RAD',
    '3': 'ANW',
    '4': 'ANW',
    '5': 'GAP',
    '6': 'AGG',
    '7': 'TOX',
    '8': 'SCM',
    '9': 'SCM',
    '11': 'BIO',
    '12': 'BIO',
    '13': 'SUW',
    '14': 'UFW',
    '15': 'ASH',
    '16': 'ASH',
    '17': 'MMP',
    '18': 'MMP',
    '19': 'ANW',
    '20': 'ANW',
    '21': 'TAL',
    '22': 'MCD',
    '23': 'INA',
    '24': 'GAP',
    '25': 'DBF',
    '27': 'MFP',
    '28': 'INF',
    '29': 'MMP',
    '30': 'MMP',
    '31': 'MMP',
    '32': 'TPW',
    '33': 'DBC',
    '34': 'DBC',
    '35': 'DBC',
    '36': 'DBC',
    '37': 'DBC',
    '38': 'DBC',
    '39': 'DBC',
    '40': 'DBC',
    '41': 'DBC',
    '42': 'DBC',
    '43': 'DBC',
    '44': 'DBC',
    '45': 'DBC',
    '46': 'DBC',
    '47': 'DBC',
    '48': 'DBC',
    '49': 'DBC',
    '50': 'DBC',
    '51': 'DBC',
    '52': 'DBC',
    '53': 'DBC',
    '54': 'DBC',
    '55': 'DBC',
    '56': 'FDG',
    '57': 'AGG',
    '58': 'NWP',
    '59': 'SAL',
    '60': 'AFS',
    '61': 'AGG',
    '62': 'AGP',
    '63': 'DBC',
    '64': 'DBF',
    '65': 'FAV',
    '66': 'FDG',
    '67': 'GAP',
    '68': 'GCU',
    '69': 'INA',
    '70': 'NTP',
    '71': 'NWP',
}

COMMODITY_MAPPING_FOR_EXTERNAL_BOOKING = {
    '1': 'GCU', '2': 'GCU', '3': 'GCU', '4': 'GCU', '5': 'GCU', '6': 'GCU', '7': 'GCU', '8': 'GCU',
    '9': 'GCU', '10': 'GCU', '11': 'GCU', '12': 'GCU', '13': 'GCU', '14': 'GCU', '15': 'GCU', '16': 'GCU',
    '17': 'GCU', '18': 'GCU', '19': 'GCU', '20': 'GCU', '21': 'GCU', '22': 'AFS', '23': 'AFS', '24': 'AFS',
    '27': 'FDG', '28': 'GCU', '29': 'GCU', '30': 'GCU', '31': 'AGG', '32': 'GAP',
    '33': 'NWP', '34': 'AFS', '35': 'NTP', '36': 'NTP', '38': 'ANW', '39': 'AFS', '40': 'AFS',
    '41': 'GCU', '42': 'GCU', '43': 'AFS', '44': 'AFS', '45': 'AFS', '46': 'GCU', '47': 'GCU',
    '49': 'AFS', '50': 'AFS', '51': 'AFS', '52': 'AFS', '53': 'AFS', '54': 'GCU', '55': 'ADG', '56': 'AGG',
    '59': 'AFS', '60': 'AFS', '61': 'AFS', '62': 'AFS', '63': 'AFS', '64': 'AFS',
    '65': 'AFS', '66': 'AFS', '67': 'AFS', '68': 'AFS', '69': 'AFS', '70': 'AFS', '71': 'AFS', '72': 'AFS',
    '73': 'AFS', '74': 'AFS', '75': 'GCU', '76': 'TWP', '77': 'AFS', '78': 'AFS', '79': 'GCU',
    '81': 'AFS', '82': 'GCU', '83': 'AFS', '84': 'AFS', '85': 'AFS', '86': 'GCU', '87': 'GCU', '88': 'AFS'
}

CLEANING_METHODS_MAPPING_FOR_EXTERNAL_BOOKING = {
    'Sweep': 'A',
    'Pressure Cleaned': 'B',
    'Steam Cleaned': 'C',
    'Air': 'F',
    'Wash': 'H'
}

CLEANING_METHOD_CODE_FOR_NULL_VALUE = 'G'

LAST_COMMODITY_CARRIED_ERROR_MESSAGE = 'LastCommodityCarried is required'
ADDITIONAL_ERROR_INFORMATION_MESSAGE = ('Please ensure that only Viterra-approved commodities or materials are '
                                       'selected in the last commodity-carried fields.')
EMPTY_VALUE = "-"
PROVIDER_CODE_MAPPING_FOR_EXTERNAL_BOOKING = {
    "***********": "bctsp",
    "***********": "callery",
    "***********": "sctran",
    "***********": "carron",
    "***********": "wish",
    "***********": "cedblk",
    "***********": "quinn",
    "***********": ["epbulk", "epbulkharv"],
}
VITERRA_ABN = "***********"
VITERRA_AUSTRALIA_ABN = "***********"

SPEC_NAME_REMOVE_PARENTHESIS_AND_CONTENT_REGEX = r'\s*\([^)]*\)\s*'
CSV_TIME_FORMAT = r'^(\d|0\d|1\d|2[0-3]):([0-5]\d)$'
DEFAULT_TIME = '12:00'

CONTRACT_PREDEFINED_DATE_RANGE_FILTER_KEYS = [
    'delivery_start_date_range', 'delivery_end_date_range', 'updated_at_date_range'
]

ORDER_PREDEFINED_DATE_RANGE_FILTER_KEYS = [
    'delivery_start_date_range', 'delivery_end_date_range', 'updated_at_date_range'
]

PACK_ORDER_PREDEFINED_DATE_RANGE_FILTER_KEYS = [
    'pack_by_date_range', 'deliver_by_date_range'
]

SITE_LOADS_DATE_RANGE_FILTER_KEYS = ['load_date_range']

TITLE_TRANSFER_DATE_RANGE_FILTER_KEYS = ['process_on_date_range']

CONTRACT_BIDS_DATE_RANGE_FILTER_KEYS = [
    'delivery_start_date_range', 'delivery_end_date_range'
]

FREIGHT_MOVEMENT_DATE_RANGE_FILTER_KEYS = [
    'freight_pickup_date_range', 'freight_delivery_date_range', 'updated_at_date_range'
]

INVOICE_DATE_RANGE_FILTER_KEYS = ['payment_due_date_range', 'updated_at_date_range']

PREDEFINED_DATE_RANGE_FILTER_KEYS = [
    'today', 'yesterday', 'current_week', 'last_week', 'last_7_days', 'last_10_days', 'last_15_days',
    'last_30_days', 'current_month', 'last_month', 'current_quarter', 'last_quarter', 'current_year',
    'last_year', 'custom'
]

PREDEFINED_DATE_RANGE_FILTER_FOR_NUMBER_OF_DAYS_KEYS = [
    'today', 'yesterday', 'last_7_days', 'last_10_days', 'last_15_days', 'last_30_days'
]

PREDEFINED_DATE_RANGE_FILTER_FOR_TIME_PERIOD_KEYS = [
    'current_week', 'current_month', 'current_quarter', 'current_year',
    'last_week', 'last_month', 'last_quarter', 'last_year'
]

FILTERS_KEY_MAPPING = {
    'delivery_start_date_range': {
        'contract': {'field': 'delivery_start_date', 'type': 'date'},
        'order': {'field': 'freight_pickup__date_time', 'type': 'datetime'},
         'contract_bids': {'field': 'delivery_start_date', 'type': 'date'}
    },
    'delivery_end_date_range': {
        'contract': {'field': 'delivery_end_date', 'type': 'date'},
        'order': {'field': 'freight_delivery__date_time', 'type': 'datetime'},
        'contract_bids': {'field': 'delivery_end_date', 'type': 'date'}
    },
    'updated_at_date_range': {
        'contract': {'field': 'updated_at', 'type': 'datetime'},
        'order': {'field': 'updated_at', 'type': 'datetime'},
        'movement': {'field': 'updated_at', 'type': 'datetime'},
        'pack_movement': {'field': 'updated_at', 'type': 'datetime'},
        'invoice': {'field': 'updated_at', 'type': 'datetime'}
    },
    'load_date_range': {
        'site_loads': {'field': 'date_time', 'type': 'datetime'}
    },
    'process_on_date_range': {
        'title_transfer': {'field': 'process_on', 'type': 'date'}
    },
    'freight_pickup_date_range': {
        'movement': {'field': 'freight_pickup__date_time', 'type': 'datetime'}
    },
    'freight_delivery_date_range': {
        'movement': {'field': 'freight_delivery__date_time', 'type': 'datetime'}
    },
    'outload_date_range': {
        'movement': {'field': 'loads__date_time', 'type': 'datetime'}
    },
    'inload_date_range': {
        'movement': {'field': 'loads__date_time', 'type': 'datetime'}
    },
    'pack_by_date_range': {
        'pack_movement': {'field': 'loads__date_time', 'type': 'datetime'},
        'pack_order': {'field': 'freight_container__pack_by_date', 'type': 'date'}
    },
    'deliver_by_date_range': {
        'pack_order': {'field': 'freight_container__deliver_by_date', 'type': 'date'}
    },
    'payment_due_date_range': {
        'invoice': {'field': 'payment_due_date', 'type': 'datetime'},
        'invoice_payable': {'field': 'payment_due_date', 'type': 'datetime'},
        'invoice_receivable': {'field': 'payment_due_date', 'type': 'datetime'},
        'freight_invoice_payable': {'field': 'payment_due_date', 'type': 'datetime'},
        'freight_invoice_receivable': {'field': 'payment_due_date', 'type': 'datetime'},

    }
}

KM = "km"
MILES = "mi"
SITE_EXTERNALLY_MANAGED_ERROR = "Cannot perform this operation as site is externally managed"

TRANSPORT_MODE_TRUCK = "Truck"
TRANSPORT_MODE_WAGON = "Wagon"
TRANSPORT_MODES = (
    (TRANSPORT_MODE_TRUCK, TRANSPORT_MODE_TRUCK),
    (TRANSPORT_MODE_WAGON, TRANSPORT_MODE_WAGON)
)

MOBILE_DOCKETS_PATH = 'loads/mobile_dockets/'
CONSIGNOR_ROLE = 'Consignor'
CONSIGNEE_ROLE = 'Consignee'
CONSIGNEE_CODE = 'consignee'
CONSIGNOR_CODE = 'consignor'
STRICT_QUANTITIES = [METER_CUBE, KG, LITRE]
GOOGLE_MAPS_STATE_CODES_MAPPING = {
  "Québec": "Quebec"
}
