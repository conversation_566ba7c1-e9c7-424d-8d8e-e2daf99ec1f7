import django_filters
from django.db.models import Prefetch
from rest_framework import status
from rest_framework.generics import ListAPIView
from rest_framework.response import Response
from rest_framework.views import APIView

from pydash import get
from core.timezones.utils import DateTimeUtil
from core.companies.models import Company
from core.companies.serializers import CompanyMobileSerializer
from core.company_sites.models import CompanySite


class CompaniesMobileView(ListAPIView):
    serializer_class = CompanyMobileSerializer
    filter_backends = (django_filters.rest_framework.DjangoFilterBackend, )
    filterset_fields = ('type_id', )
    def get_queryset(self):
        epoch = self.request.query_params.get('from')
        return Company.objects.select_related('address').prefetch_related(
            Prefetch('farm_set', queryset=CompanySite.objects.select_related('address'))
        ).filter(**DateTimeUtil.get_updated_at_filter(epoch))


class CompanyOwnershipStocks(APIView):
    @staticmethod
    def get(request):
        user = request.user
        from core.stocks.models import Stock
        from_param = request.query_params.get('from')

        owner_stocks = Stock.objects.filter(Stock.company_all_stocks_criteria(user.company_id))
        only_third_party_stocks = not user.company.enable_my_stocks_on_mobile
        if only_third_party_stocks:
            owner_stocks = owner_stocks.exclude(farm__company_id=user.company_id)
        if not get(user, 'company.transaction_participation'):
            owner_stocks = owner_stocks.filter(farm__stocks_management=True)

        only_containers = request.query_params.get('only_containers', '').lower() == 'true'

        owner_stocks = Stock.owner_groups_for_mobile(
            owner_stocks, user.company_id, only_containers, False, True, from_param)

        return Response(owner_stocks, status=status.HTTP_200_OK)
