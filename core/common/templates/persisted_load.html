<!doctype html>
{% load static %}
{% load app_filters %}
{% with  date_time_format="M d Y, h:i A"  date_format="M d, Y" time_format="h:i A" %}
<html style="-webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; font-family: sans-serif; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; font-size: 10px; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);">
    <head>
      <meta charset="utf-8">
    </head>
    <style>
			table tr {page-break-inside: avoid}
    </style>
    <body style="-webkit-box-sizing: border-box; margin:0; -moz-box-sizing: border-box; box-sizing: border-box; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; line-height: 1.42857; color: #000000; background-color: #fff;">
		{% with date_fmt="%b %d, %Y" tz=instance.inload.country.timezone %}
		{% with date_tz=date_fmt|add:"|"|add:tz %}
		{% if create_msg %}
			<p style="font-size: 12pt;">Hello,</p>
			<p style="font-size: 12pt;">Please find the details of the {{entity_display_name}} executed by {{instance.inload.created_by.name}} 
				from {{instance.inload.created_by.company.name}} on {{instance.created_at | localize_tz:date_tz}} below:
			</p>
			<p style="font-size: 12pt;">Thanks</p>
			<p></p>
			<p>Regards, <br/>{{ instance.inload.created_by.name }}</p>
		{% endif %}
		<div style="min-height:1300px;position:relative;">
			<div style="padding-bottom:100px;">
				<table style="width: 900px; margin:0 auto;">
					<tbody>
						<tr>
							<th colspan="2" align="center">
								{% if instance.created_by.my_company_logo_url %}
										<img style="width: 150px;" src="{{instance.outload.ngr.company.my_company_logo_url}}" />
								{% else %}
										<h2 style="font-size: 20pt; color: #343534">{{instance.outload.storage.farm.company.business_name}}</h2>
								{% endif %}
							</th>
						</tr>
						<tr>
							<td style="display: flex; flex-direction: column-reverse;">
								<table>
									<tr>
										<th align="left" style="font-size: 16pt;">{{entity_display_name}}</th>
									</tr>
									<tr>
										<td style="width: 50%; font-size: 8pt; font-weight: 500; color: #666666; margin: 0;">IDENTIFIER:</td>
										<td style="width: 50%; font-size: 10pt;"> {{instance.identifier}} </td>
									</tr>
									<tr>
										<td style="width: 50%; font-size: 8pt; font-weight: 500; color: #666666; margin: 0;">DATE ISSUED:</td>
										<td style="width: 50%; font-size: 10pt;">{{instance.created_at | localize_tz:date_tz}}</td>
									</tr>
									<tr></tr>
									<tr></tr>
								</table>
							</td>
							<td align="right">
								<table style="width: 100%;">
									<tr>
										<th></th>
										<th align="right" style="font-size: 16pt;">{{instance.outload.storage.farm.company.business_name}}</th>
									</tr>
									{%if instance.outload.storage.farm.company.address.address %}
									<tr align="right">
										<td></td>
										<td align="right" style="font-size: 10pt;">{{instance.outload.storage.farm.company.address.address}}</td>
									</tr>
									{%endif%}
									<tr>
									 <td></td>
										<td align="right" style="font-size: 10pt;">
												<label style="font-size: 8pt; font-weight: 500; color: #666666; margin: 0;">PHONE: </label>
											{{instance.outload.storage.farm.company.mobile}}
										</td>
									</tr>
									{% if 'abn'|is_visible_for_country:instance.outload.country_code %}
									<tr>
										<td></td>
										<td align="right" style="font-size: 10pt;">
											<label style="font-size: 8pt; font-weight: 500; color: #666666; margin: 0;">ABN: </label>
											{{instance.outload.storage.farm.company.abn}}
										</td>
									</tr>
									{% endif %}
								</table>
							</td>
						</tr>
						<tr><td colspan="2"> <hr style="margin-bottom: 5px;"></td></tr>
						<tr style="font-size: 10pt; color: #000000; padding: 5pt;">
							<td colspan="2">
								<p style="line-height: 15pt; margin: 5px 0">This document is a record of a {{entity_display_name}} executed by {{instance.inload.created_by.name}} 
									from {{instance.inload.created_by.company.name}} on {{instance.created_at | localize_tz:date_tz}}.</p>
								<p style="line-height: 15pt; margin: 5px 0">For further information please contact {{instance.outload.storage.farm.company.business_name}} on {{instance.outload.storage.farm.company.mobile}}.</p>
							</td>
						</tr>
						<tr><td colspan="2"> <hr style="margin-bottom: 5px;"></td></tr>
						<tr>
              <td>
                <table>
                  <tr>
                    <th align="left" style="font-size: 16pt; line-height: 60px">{{entity_display_name|upper}} DETAILS</th>
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">OWNER:</td>
											<td style="width: 50%; font-size: 10pt;"> {{instance.inload.ngr.owner_company}} </td>
										</tr>
										{% if entity != 'stock_swap' %}
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">FARM/SITE NAME:</td>
											<td style="width: 50%; font-size: 10pt;"> {{instance.inload.storage.farm.name}} </td>
										</tr>
										{% endif %}
										{% if 'ngr'|is_visible_for_country:instance.inload.country_code %}
											<tr style="line-height: 35px;">
												<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">NGR:</td>
												<td style="width: 50%; font-size: 10pt;">{{instance.inload.ngr.ngr_number}}</td>
											</tr>
										{% endif %}
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">COMMODITY:</td>
											<td style="width: 50%; font-size: 10pt;">{{instance.outload.commodity.display_name}}</td>
										</tr>
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">GRADE:</td>
											<td style="width: 50%; font-size: 10pt;">{{instance.outload.grade.name}}</td>
										</tr>
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">SEASON:</td>
											<td style="width: 50%; font-size: 10pt;">{{instance.outload.season}}</td>
										</tr>
					                    {% if entity == 'regrade_reseason' %}
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">RECOMMODITY TO:</td>
											<td style="width: 50%; font-size: 10pt;">{{instance.inload.commodity.display_name}}</td>
										</tr>
					                    {% endif %}
										{% if entity == 'stock_swap' %}
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">CURRENT SITE:</td>
											<td style="width: 50%; font-size: 10pt;">{{instance.outload.storage.farm.display_name}}</td>
										</tr>
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">SWAP SITE:</td>
											<td style="width: 50%; font-size: 10pt;">{{instance.inload.storage.farm.display_name}}</td>
										</tr>
										{% else %}
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">REGRADE TO:</td>
											<td style="width: 50%; font-size: 10pt;">{{instance.inload.grade.name}}</td>
										</tr>
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">RESEASON TO:</td>
											<td style="width: 50%; font-size: 10pt;">{{instance.inload.season}}</td>
										</tr>
										{% endif %}
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">{{'tonnage' | to_country_label:instance.inload.country_code | upper}}:</td>
											<td style="width: 50%; font-size: 10pt;">{{instance.inload.estimated_net_weight|floatformat:"2"}} {{instance.inload.truck.unit}}</td>
										</tr>
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">DIFFERENTIAL:</td>
											{% if entity == 'stock_swap' %}
											<td style="width: 50%; font-size: 10pt;">$ {{instance.freight_differential|floatformat:"2"}}</td>
											{% else %}
											<td style="width: 50%; font-size: 10pt;">$ {{instance.regrade_reseason_differential|floatformat:"2"}}</td>
											{% endif %}
										</tr>
										{% if instance.quality_differential %}
											<tr style="line-height: 35px;">
												<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">QUALITY DIFFERENTIAL:</td>
												<td style="width: 50%; font-size: 10pt;">$ {{instance.quality_differential|floatformat:"2"}}</td>
											</tr>
										{% endif %}
										<tr style="line-height: 35px;">
											<td style="width: 50%; font-size: 8pt; font-weight: bold; color: #666666; margin: 0;">COMMENTS:</td>
											<td style="width: 50%; font-size: 10pt;"> 
												{% if instance.outload.extras.comment %} 
													{{instance.outload.extras.comment}}
												{% else %}
													{{instance.comment}}
												{% endif %}
												</td>
										</tr>
                  </tr>
								</table>
							</td>
						</tr>
						<tr><td colspan="2"> <hr style="margin-bottom: 5px;"></td></tr>
					</tbody>
				</table>
			</div>
			{% if footer %}
      <div style="position:absolute;bottom:0px;height:100px;width:100%;">
				<div style="text-align:center;width:900px;margin: 0vh auto 0;position:relative;">
					<div style="width:100%;text-align:left;">
						<p style="font-size:6pt; color:#000000; position: absolute; margin-top: 0px; padding-left: 38px;">Movement generated by</p>
						<img width="140" style="padding-top: 3px" src="{{agrichain_logo_url}}" alt="AgriChain Logo"/>
					</div>
					<div style="width:100%;position:absolute;top:0;display: flex;justify-content: center;flex-direction: column;">
						<h4 style="font-size: 8pt; font-weight: 500; color: #000000; margin: 0;">IDENTIFIER: {{instance.identifier}}</h4>
						<h5 style="font-size: 8pt; font-weight: 500; color: #000000; margin: 0">DATE ISSUED: {{instance.created_at | localize_tz:date_tz}}</h5>
					</div>
				</div>
      </div>
      {% endif %}
		</div>
	{% endwith %}
	{% endwith %}
    </body>
</html>
{% endwith %}