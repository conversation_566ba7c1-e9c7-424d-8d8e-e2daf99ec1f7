# Generated by Django 4.2.15 on 2024-12-30 11:15

from django.db import migrations
from django.utils import timezone

def enable_additional_mass_limit_codes(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')

    Company.objects.filter(
        additional_mass_limit_codes=False
    ).update(
        additional_mass_limit_codes=True,
        updated_at=timezone.now()
    )

class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0197_alter_company_additional_mass_limit_codes'),
    ]

    operations = [
        migrations.RunPython(enable_additional_mass_limit_codes)
    ]
