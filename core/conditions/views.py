from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
# pylint: disable=redefined-builtin
from rest_framework.views import APIView

from .models import Condition
from ..common.permissions import IsAllowedToDeleteConditions


class ConditionsView:
    def get(self, request, filter_params):
        query_params = request.query_params.dict()
        return Response(
            Condition.qs2dict(Condition.objects.filter(**{**query_params, **filter_params})),
            status=status.HTTP_200_OK
        )

    def post(self, request, type):
        data = request.data
        data['type'] = type
        data['company_id'] =  data['company_id'] if (
                request.user.company.is_system and
                data.get('company_id')
            ) else request.user.company_id
        condition = Condition.create(data)
        _dict = condition.to_dict()

        if condition.persisted:
            _status = status.HTTP_201_CREATED
        else:
            _status = status.HTTP_400_BAD_REQUEST

        return Response(_dict, status=_status)

    def put(self, request, id):
        data = request.data
        data['company_id'] =  data['company_id'] if (
                request.user.company.is_system and
                data.get('company_id')
            ) else request.user.company_id
        try:
            condition = Condition.update(
                instance_id=id,
                data=data
            )
        except Condition.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        _dict = condition.to_dict()
        if condition.errors:
            return Response(
                _dict,
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response(_dict, status=status.HTTP_200_OK)

    def getOne(self, id):
        try:
            condition = Condition.objects.get(id=id)
        except Condition.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        return Response(condition.to_dict(), status=status.HTTP_200_OK)


class GeneralConditionsView(APIView):
    def get(self, request):
        return ConditionsView().get(request, {'type': 'general'})

    def post(self, request):
        return ConditionsView().post(request, 'general')


class GeneralConditionView(APIView):
    def get_permissions(self):
        if self.request.method == 'DELETE':
            return [IsAllowedToDeleteConditions()]
        return [IsAuthenticated()]

    def put(self, request, id):
        return ConditionsView().put(request, id)

    def get(self, _, id):
        return ConditionsView().getOne(id)

    def delete(self, request, id):
        try:
            condition = Condition.objects.get(id=id)
            self.check_object_permissions(request, condition)
        except Condition.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        condition.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class SpecialConditionsView(APIView):
    def get(self, request):
        return ConditionsView().get(request, {'type': 'special'})

    def post(self, request):
        return ConditionsView().post(request, 'special')


class SpecialConditionView(APIView):
    def get_permissions(self):
        if self.request.method == 'DELETE':
            return [IsAllowedToDeleteConditions()]
        return [IsAuthenticated()]

    def put(self, request, id):
        return ConditionsView().put(request, id)

    def get(self, _, id):
        return ConditionsView().getOne(id)

    def delete(self, request, id):
        try:
            condition = Condition.objects.get(id=id)
            self.check_object_permissions(request, condition)
        except Condition.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)

        condition.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
