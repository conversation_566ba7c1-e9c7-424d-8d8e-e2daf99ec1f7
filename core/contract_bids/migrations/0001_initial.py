# Generated by Django 4.1.13 on 2024-08-01 06:15

import core.validation.validators
import dirtyfields.dirtyfields
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_fsm
import simple_history.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contracts', '0211_alter_contractpricepoint_name_and_more'),
        ('companies', '0176_alter_companygroup_type_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('commodities', '0054_remove_commodity_specs_remove_grade_specs_and_more'),
        ('farms', '0148_farm_delivery_mandatory_fields_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalContractBid',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('season', models.CharField(max_length=5, validators=[core.validation.validators.season_validator])),
                ('price', models.FloatField(blank=True, null=True)),
                ('limit', models.FloatField(blank=True, null=True)),
                ('track', models.CharField(blank=True, max_length=255, null=True)),
                ('start_date_time', models.DateTimeField()),
                ('end_date_time', models.DateTimeField()),
                ('status', django_fsm.FSMField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('completed', 'Completed'), ('partial', 'Partially Fulfilled')], db_index=True, default='active', max_length=40)),
                ('delivery_start_date', models.DateField(blank=True, null=True)),
                ('delivery_end_date', models.DateField(blank=True, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('buyer', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='contracts.party')),
                ('commodity', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='commodities.commodity')),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('grade', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='commodities.grade')),
                ('group', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.companygroup')),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('payment_scale', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='contracts.paymentscale')),
                ('payment_term', models.ForeignKey(blank=True, db_constraint=False, default=10, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='contracts.paymentterm')),
                ('site', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='farms.farm')),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical contract bid',
                'verbose_name_plural': 'historical contract bids',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='ContractBid',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('season', models.CharField(max_length=5, validators=[core.validation.validators.season_validator])),
                ('price', models.FloatField(blank=True, null=True)),
                ('limit', models.FloatField(blank=True, null=True)),
                ('track', models.CharField(blank=True, max_length=255, null=True)),
                ('start_date_time', models.DateTimeField()),
                ('end_date_time', models.DateTimeField()),
                ('status', django_fsm.FSMField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('completed', 'Completed'), ('partial', 'Partially Fulfilled')], db_index=True, default='active', max_length=40)),
                ('delivery_start_date', models.DateField(blank=True, null=True)),
                ('delivery_end_date', models.DateField(blank=True, null=True)),
                ('buyer', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='contract_bid_buyer', to='contracts.party')),
                ('commodity', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='commodities.commodity')),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('grade', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='commodities.grade')),
                ('group', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='companies.companygroup')),
                ('payment_scale', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='contracts.paymentscale')),
                ('payment_term', models.ForeignKey(default=10, on_delete=django.db.models.deletion.DO_NOTHING, to='contracts.paymentterm')),
                ('site', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='farms.farm')),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'contract_bids',
            },
            bases=(dirtyfields.dirtyfields.DirtyFieldsMixin, models.Model),
        ),
    ]
