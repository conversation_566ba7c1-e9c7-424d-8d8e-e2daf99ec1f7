# Generated by Django 4.1.7 on 2023-04-21 03:57

from django.db import migrations
from pydash import get


def set_default_warehouse_invoice_ngr(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    Ngr = apps.get_model('ngrs', 'Ngr')
    for company in Company.objects.all():
        ngrs_qs = Ngr.objects.filter(company_id=company.id)
        ngrs = ngrs_qs.exclude(ngr_number__icontains='UNKNOWN_').filter(ngr_type='single')
        if ngrs.count() == 1:
            logical_ngr_id = ngrs.first().id
        else:
            unknown_ngr = ngrs_qs.filter(ngr_number__icontains='UNKNOWN_').first()
            logical_ngr_id = unknown_ngr.id if unknown_ngr else get(ngrs_qs.first(), 'id')
        if logical_ngr_id:
            ngr = Ngr.objects.get(id=logical_ngr_id)
            if not ngr.tags:
                ngr.tags = []
            ngr.tags += ['warehouse_invoice']
            ngr.save()


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0119_company_restrict_contract_creation_and_more'),
    ]

    operations = [
        migrations.RunPython(set_default_warehouse_invoice_ngr)
    ]
