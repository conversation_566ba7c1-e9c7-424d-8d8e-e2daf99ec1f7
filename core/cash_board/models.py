import csv
import io
import uuid
import re
from copy import deepcopy

from dateutil import parser, relativedelta
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.utils import timezone
from django_fsm import <PERSON><PERSON><PERSON>ield
from pydash import get

from core.alerts.constants import NEW_CASH_PRICE_FOR_MY_STOCKS_ALERT_CODE, LEADING_CASH_PRICE_WARNING_ALERT_CODE
from core.cash_board import constants
from core.cash_board.constants import CASH_PRICE_UPLOAD_CSV_HEADERS
from core.cash_board.interfaces import CashPriceAudit
from core.commodities.models import Grade
from core.common.constants import (DEFAULT_PAYMENT_TERM_ID, FARM_ADMIN_TYPE_ID,
    FARM_EMPLOYEE_TYPE, COMPANY_ADMIN_TYPE_ID)
from core.services.internal.errbit import ERRBIT_LOGGER
from core.common.models import BaseModel
from core.common.utils import generate_identifier, get_grade_name, save_download_locally
from core.contracts.constants import (BUYER_RCTI_INVOICING, PRICE_POINTS_DELIVERED_SITE_ID, PRICE_POINTS_EX_FARM_ID,
                                      BROKER_NOTE_DOCUMENT_TYPE, BROKER_NOTE_DOCUMENT_TYPE_ID, CONTRACT_DOCUMENT_TYPE,
                                      CONTRACT_DOCUMENT_TYPE_ID, NIL_BROKERAGES, PLANNED)
from core.timezones.utils import DateTimeUtil
from core.countries.models import Country
from core.profiles.models import Download
from core.services.external.aws import S3
from core.validation.model_validations import is_unique_cash_price
from core.validation.validators import season_validator


class ActiveCashPricesManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):
        now = timezone.now()
        return super().get_queryset().filter(
            start_date_time__lte=now,
            end_date_time__gte=now
        ).exclude(
            status__in=['inactive', 'completed', 'partial'])


class ArchivedCashPricesManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):
        now = timezone.now()
        return super().get_queryset().filter(
            models.Q(start_date_time__gte=now) |
            models.Q(end_date_time__lte=now) |
            models.Q(status__in=['inactive', 'completed', 'partial'])
        )


class DefaultCompanyManager(models.Manager):
    use_in_migrations = True

    def get_queryset(self):  # pylint: disable=useless-super-delegation
        return super().get_queryset()


class CashPrices(BaseModel):
    class Meta:
        db_table = 'cash_prices'

    EXTERNAL_CREATED_INTERNALLY_IDENTIFIER_PREFIX = 'AC-EX-'
    EXTERNAL_CREATED_INTERNALLY_APPROVED_EXTERNALLY_PREFIX = 'EX-'

    active = ActiveCashPricesManager()
    archived = ArchivedCashPricesManager()
    objects = DefaultCompanyManager()

    site = models.ForeignKey('farms.Farm', on_delete=models.PROTECT)
    commodity = models.ForeignKey(
        'commodities.Commodity', on_delete=models.DO_NOTHING)
    season = models.CharField(max_length=5, validators=[season_validator])
    grade = models.ForeignKey('commodities.Grade', on_delete=models.DO_NOTHING)
    payment_term = models.ForeignKey(
        'contracts.PaymentTerm', on_delete=models.DO_NOTHING, default=DEFAULT_PAYMENT_TERM_ID)
    payment_scale = models.ForeignKey(
        'contracts.PaymentScale',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
    )
    price = models.FloatField(null=True, blank=True)
    limit = models.FloatField(null=True, blank=True)
    track = models.CharField(max_length=255, null=True, blank=True)
    buyer = models.ForeignKey(
        'contracts.Party', on_delete=models.DO_NOTHING, related_name='cash_prices_buyer')
    start_date_time = models.DateTimeField()
    end_date_time = models.DateTimeField()
    status = FSMField(
        max_length=40,
        choices=constants.STATUSES,
        null=False,
        blank=False,
        default='active',
        db_index=True,
    )
    sustainable_commodity = models.BooleanField(default=False)
    external_system = models.CharField(max_length=255, null=True, blank=True)
    external_reference = models.CharField(max_length=255, null=True, blank=True)
    comments = models.TextField(null=True, blank=True)

    FILLABLES = [
        'site_id',
        'commodity_id',
        'track',
        'grade_id',
        'price',
        'limit',
        'season',
        'buyer',
        'payment_term_id',
        'end_date_time',
        'start_date_time',
        "variety_id",
        "tonnage",
        "seller",
        "identifier",
        "total_sale",
        "mark_complete",
        "status",
        "communication",
        "payment_scale_id",
        "load_id",
        "impu",
        "coil",
        "canola_load_ids",
        "sustainable_commodity",
        "cash_price_ids",
        # For External Cash Price
        'external_system',
        'external_reference',
        'comments',
        'buyer_abn',
        'seller_abn',
        'seller_ngr_number',
        'payment_term',
        'payment_scale',
        'commodity',
        'grade',
        'variety',
        'created_at',
        'updated_at',
        'user',
        'quantity',
    ]

    def clean(self):
        is_unique_cash_price(self)

    @staticmethod
    def build_party(params, role):
        from core.contracts.models import Party
        if params:
            return Party(role=role, **params)

    @property
    def limit_remaining(self):
        return round(self.limit - self.total_transaction_tonnage, 2)

    @property
    def total_transaction_tonnage(self):
        return self.contract_set.exclude(status='void').aggregate(
            total_tonnage=models.Sum('tonnage'))['total_tonnage'] or 0

    @property
    def grade_name(self):
        return get_grade_name(self)

    def get_available_stock(self, load_id=None, seller_ngr_id=None):
        if not load_id and not seller_ngr_id:
            return -1

        from core.loads.models import Load
        from core.farms.models import tonnage_sum_from_loads_considering_shrinkage

        if load_id:
            loads = Load.objects.filter(id=load_id)
        else:
            loads = self.get_loads().filter(ngr_id=seller_ngr_id).order_by('-id')

        load = loads.first()

        available_tonnage = tonnage_sum_from_loads_considering_shrinkage(loads)
        if load_id:
            loads_created_after_this_load = self.get_loads().filter(
                ngr_id=seller_ngr_id or get(load, 'ngr_id')).order_by('-id').filter(
                created_at__gte=load.created_at).exclude(id=load_id)
            unprocessed_title_transfers_created_after_this_load = self.get_unprocessed_title_transfers(
                seller_ngr_id or get(load, 'ngr_id')).filter(created_at__gte=load.created_at)
            if loads_created_after_this_load or unprocessed_title_transfers_created_after_this_load:
                past_loads = self.get_loads().filter(
                    ngr_id=seller_ngr_id or get(load, 'ngr_id')).order_by('-id').exclude(id=load_id)
                past_tonnage = tonnage_sum_from_loads_considering_shrinkage(past_loads)
                past_tonnage -= self.get_unprocessed_title_transfers_tonnage(
                    seller_ngr_id=seller_ngr_id or get(load, 'ngr_id'))
                if round(float(past_tonnage), 2) < 0:
                    available_tonnage += past_tonnage
        else:
            available_tonnage -= self.get_unprocessed_title_transfers_tonnage(seller_ngr_id=seller_ngr_id)

        return available_tonnage, load

    def get_unprocessed_title_transfers(self, seller_ngr_id):
        from core.contracts.models import TitleTransfer
        queryset = TitleTransfer.objects.filter(status='planned').filter(self.get_title_transfers_criteria())
        if seller_ngr_id:
            queryset = queryset.filter(
                models.Q(seller__ngr_id=seller_ngr_id) | models.Q(commodity_contract__seller__ngr_id=seller_ngr_id)
            )
            queryset = TitleTransfer.objects.filter(id__in=queryset.values_list('id', flat=True))

        return queryset

    def get_unprocessed_title_transfers_tonnage(self, seller_ngr_id=None):
        return self.get_unprocessed_title_transfers(seller_ngr_id).aggregate(
            total_tonnage=models.Sum('tonnage')
        )['total_tonnage'] or 0

    def get_loads(self):
        from core.loads.models import Load
        return Load.objects.filter(self.get_loads_criteria()).exclude(status='void')

    def get_loads_criteria(self):
        from core.farms.models import Storage
        return models.Q(
            farm_id=self.site_id, commodity_id=self.commodity_id, grade_id=self.grade_id, season=self.season,
            storage__type__in=Storage.FIXED_STORAGE_TYPES
        )

    def get_title_transfers_criteria(self):
        return models.Q(
            storage__farm_id=self.site_id, commodity_id=self.commodity_id, grade_id=self.grade_id, season=self.season,
        )

    def has_enough_stock(self, load_id=None, seller_ngr_id=None, cash_out_tonnage=0):
        available_tonnage, load = self.get_available_stock(load_id=load_id, seller_ngr_id=seller_ngr_id)
        return round(float(available_tonnage), 2) >= round(float(cash_out_tonnage), 2), load

    def mark_complete(self):
        if self.is_completable:
            self.status = 'completed'
            self.save()
            self.audit('completed')

    def audit(self, action, user=None, **kwargs):
        CashPriceAudit(action, self, user, **kwargs).send()

    @property
    def is_completable(self):
        return self.limit > self.limit_remaining == 0

    def calculate_halt_status_for_repost(self):
        limit_remaining = self.limit_remaining

        # limit = 100 and total_tonnage = 50 -> limit_remaining = 50 ->  partial
        # limit = 100 and total_tonnage = 100 -> limit_remaining = 0 ->  completed
        # limit = 100 and total_tonnage = 0 -> limit_remaining = 100 ->  can be marked as inactive
        # not calling is_completable -- saving limit_remaining call again

        if limit_remaining == 0:
            self.audit('completed')
            return 'completed'
        if self.limit > limit_remaining > 0:
            return 'partial'
        return 'inactive'  # this might be wrong or un-complete logic

    @classmethod
    def persist(cls, data):
        mark_complete_id = data.pop('mark_complete', None)
        if mark_complete_id:
            old_cash_price = CashPrices.objects.filter(id=mark_complete_id).first()
            old_cash_price.status = old_cash_price.calculate_halt_status_for_repost()
            old_cash_price.save()
        buyer = cls.build_party(data.pop('buyer', None), 'Buyer')
        cash_price = CashPrices(**data)
        buyer.save()
        cash_price.buyer = buyer
        cash_price.save()
        user = cash_price.created_by

        if user.company_id == get(cash_price, 'buyer.company_id'):
            cash_price.audit('create', user)
        else:
            cash_price.audit('create_on_behalf', user)
        if not cash_price.errors:
            cash_price.send_mobile_notification_if_required()
        return cash_price

    def get_available_tonnage_for_combination(self, company):
        from core.stocks.models import Stock
        from core.farms.models import Storage
        existing_stock = Stock.objects.filter(
            season=self.season, commodity_id=self.commodity_id, grade_id=self.grade_id,
            ngr__owner_company_ids__contains=[company.id], farm_id=self.site_id,
            storage__type__in=Storage.FIXED_STORAGE_TYPES
        ).aggregate(total_tonnage=models.Sum('tonnage'))
        tonnage = existing_stock['total_tonnage'] or 0
        from core.contracts.models import TitleTransfer
        ngr_ids = company.ngr_set.values_list('id', flat=True)
        pending_tt_tonnage = TitleTransfer.objects.filter(
            commodity_id=self.commodity_id, season=self.season, grade_id=self.grade_id, seller__ngr_id__in=ngr_ids,
            storage__farm_id=self.site_id, status=PLANNED
        ).aggregate(total_tonnage=models.Sum('tonnage'))['total_tonnage'] or 0
        return round(tonnage - pending_tt_tonnage, self.commodity.precision)

    def send_mobile_notification_if_required(self):
        from core.alerts.models import Alert
        new_cash_price_for_my_stocks_alerts = Alert.get_active_alert_by_name(NEW_CASH_PRICE_FOR_MY_STOCKS_ALERT_CODE)
        for alert in new_cash_price_for_my_stocks_alerts:
            if self.get_available_tonnage_for_combination(alert.company) > 0:
                if not CashPrices.active.filter(
                    season=self.season, commodity_id=self.commodity_id, grade_id=self.grade_id,
                    site_id=self.site_id, price__gte=self.price, sustainable_commodity=self.sustainable_commodity
                ).exclude(buyer__company_id=self.buyer.company_id).exists():
                    alert.process(cash_price=self)

        highest_cash_price_till_now = CashPrices.active.filter(
            season=self.season, commodity_id=self.commodity_id, grade_id=self.grade_id,
            site_id=self.site_id, sustainable_commodity=self.sustainable_commodity
        ).exclude(buyer__company_id=self.buyer.company_id).order_by('-price').first()
        # checking if newly posted cash price is highest or not
        if highest_cash_price_till_now and get(highest_cash_price_till_now, 'price') < self.price:
            buyer_company_id = get(highest_cash_price_till_now, 'buyer.company_id')
            leading_cash_price_warning_alert = Alert.get_active_alert_by_name_and_company(
                LEADING_CASH_PRICE_WARNING_ALERT_CODE, buyer_company_id
            ).first()
            if leading_cash_price_warning_alert:
                leading_cash_price_warning_alert.process(cash_price=self)


    def save(self, *args, **kwargs):  # pylint: disable=arguments-differ,signature-differs
        self.set_external_system()
        super().save(*args, **kwargs)

    def set_external_system(self):
        externally_sync_source = get(self, 'site.externally_sync_source')
        if externally_sync_source and externally_sync_source != self.external_system:
            self.external_system = externally_sync_source.lower()
            self.external_reference = self.external_reference or self.generate_external_reference()
        if self.external_system:
            if self.start_date_time and not isinstance(self.start_date_time, str):
                self.start_date_time = self.start_date_time.replace(microsecond=0)
            if self.end_date_time and not isinstance(self.end_date_time, str):
                self.end_date_time = self.end_date_time.replace(microsecond=0)

    @staticmethod
    def generate_external_reference():
        return f'AC-EX-{str(uuid.uuid4())[0:7]}'

    @property
    def is_external_created_internally(self):
        return self.external_system and self.external_reference and self.external_reference.startswith(
            self.EXTERNAL_CREATED_INTERNALLY_IDENTIFIER_PREFIX)

    @classmethod
    def get_internally_created_external_cash_prices_criteria(cls):
        return models.Q(
            external_system__isnull=False,
            external_reference__startswith=cls.EXTERNAL_CREATED_INTERNALLY_IDENTIFIER_PREFIX
        )

    @classmethod
    def approve_from_external_system(cls, queryset):
        """Processes internal cash outs that were pending waiting for approval from external system"""
        from core.contracts.models import TitleTransfer
        for cash_price in queryset:
            cash_price.external_reference = cash_price.external_reference.replace(
                CashPrices.EXTERNAL_CREATED_INTERNALLY_IDENTIFIER_PREFIX,
                CashPrices.EXTERNAL_CREATED_INTERNALLY_APPROVED_EXTERNALLY_PREFIX
            )
            cash_price.save()
            for title_transfer in TitleTransfer.objects.filter(
                    commodity_contract__cash_price_id=cash_price.id, status='planned'
            ):
                title_transfer.queue_mail_and_process()
                user = title_transfer.created_by
                seller_company = get(
                    title_transfer, 'seller.company') or get(title_transfer.commodity_contract, 'seller.company')
                cash_price.audit(
                    'cash_out' if user.company_id == seller_company.id else 'cash_out_on_behalf',
                    user,
                    tonnage=title_transfer.tonnage,
                    seller=seller_company.name
                )

    @staticmethod
    def validate_cash_price_upload_csv(content): # pylint: disable=too-many-locals
        buff = io.StringIO(content)
        reasons = []
        header = [item.lower() for item in CASH_PRICE_UPLOAD_CSV_HEADERS]
        header_length = len(header)
        is_first_row = True
        optional_header = 'Buyer NGR (optional)'
        for row in buff:
            data_row = row.split(',')
            item = data_row[len(data_row) - 1]
            data_row.remove(item)
            data_row.append(item.rstrip())
            if is_first_row and optional_header not in data_row and optional_header.lower() in header:
                header.remove(optional_header.lower())
                header_length = len(header)
            if len(data_row) == header_length:
                if is_first_row:
                    if not [item.lower() for item in data_row] == header:
                        print("***HEADER****", header)
                        print("***Actual Row****", row)
                        print("***Data Row****", data_row)
                        print("***Manipulated Row****", [item.lower() for item in data_row])
                        reasons.append("Headers should match the template")
                    is_first_row = False
                    continue
            elif len(data_row) > header_length:
                reasons.append("Entered data exceeds expected fields.")
            else:
                reasons.append("Missing Items in the header.")
        return list(set(reasons))

    @staticmethod
    def generate_cash_price_csv(content, download_id, tz):
        download_qs = Download.objects.filter(
            id=download_id).select_related('employee')
        download = download_qs.first()
        try:
            cash_prices_parser = CashPricesCSVParser(content, download.employee, tz=tz)
            failures, success = cash_prices_parser.sync()
            save_download_locally(download, cash_prices_parser.buff2)
            S3.upload(download.file_path, cash_prices_parser.buff2)
            download_qs.update(status='ready')
            return {'failures': failures, 'success': success, 'url': download.url}
        except Exception as ex:  # pylint: disable=broad-except
            ERRBIT_LOGGER.log(ex)
            download_qs.update(status='failed', failure_reason=str(ex))

    def cannot_create_title_transfer_reasons(self):
        reasons = []
        if self.limit_remaining == 0:
            reasons.append("Cash Price Completed")
        return reasons

    def create_contract_and_title_transfer(self, user, params, tz, is_cashed_out=False, force_process_now=False):  # pylint: disable=too-many-locals, too-many-statements
        from core.contracts.models import Contract
        from core.loads.models import Load
        from core.companies.models import Company
        title_transfer_identifier = params.pop('identifier', None)
        communication = params.pop('communication', None)
        load_id = params.pop('load_id', None)
        impu = params.pop('impu', None)
        coil = params.pop('coil', None)
        canola_load_ids = params.pop('canola_load_ids', None)
        tt_extras = params.pop('extras', None)

        if load_id:
            load = Load.objects.filter(id=load_id).first()
            if load:
                params['seller'] = {'company_id': load.ngr.company_id, 'ngr_id': load.ngr_id}
                params['variety_id'] = params.get('variety_id') or load.variety_id
            params['tonnage'] = get(load, 'checkpoint.shrunk_tonnage', load.shrunk_tonnage) or 0
        seller = deepcopy(params.get('seller', None))
        if float(params.get('tonnage', 0) or 0) <= self.limit_remaining:
            price_point_id = PRICE_POINTS_DELIVERED_SITE_ID
            site_type = "consignees"
            if not self.site.company.show_cash_prices_to_all:
                price_point_id = PRICE_POINTS_EX_FARM_ID
                site_type = "consignors"
            document_type_id = CONTRACT_DOCUMENT_TYPE_ID
            administration = {'invoicing': BUYER_RCTI_INVOICING}
            identifier = generate_identifier(CONTRACT_DOCUMENT_TYPE)
            if user.company.is_broker:
                document_type_id = BROKER_NOTE_DOCUMENT_TYPE_ID
                administration = {'invoicing': BUYER_RCTI_INVOICING, 'brokered_by': user.company,
                                  'broker_contact': user}
                identifier = generate_identifier(BROKER_NOTE_DOCUMENT_TYPE)
                params["seller"]["represented_by_id"] = user.company_id
            with transaction.atomic():
                today = DateTimeUtil.localize(timezone.now(), tz).date()
                params.update({
                    "document_type_id": document_type_id,  # contract
                    "type_id": 1,  # fixed_grade
                    "price_point_id": price_point_id,
                    "delivery_onus": "Seller",
                    "owner_id": Country.get_root_user_id(),
                    "identifier": identifier,
                    "commodity_id": self.commodity_id,
                    "sustainable_commodity": self.sustainable_commodity,
                    "grade_id": self.grade_id,
                    "season": self.season,
                    "price": self.price,
                    "payment_term_id": self.payment_term_id,
                    "payment_scale_id": self.payment_scale_id,
                    "delivery_start_date": today,
                    "delivery_end_date": today,
                    "administration": administration,
                    site_type: [
                        {
                            "handler_id": self.site_id,
                            "position": 1,
                            "ld": "0.00",
                        }
                    ],
                    "buyer": {
                        "company_id": self.buyer.company_id,
                        "contact_id": self.buyer.contact_id,
                        "ngr_id": self.buyer.ngr_id,
                    },
                    "communication": {"acceptance_required": False},
                    'cash_price_id': self.id,
                    "tolerance_id": 1,  # Nil Tolerance
                    "brokerages": NIL_BROKERAGES,
                })

                params.pop('created_by_id', None)
                params.pop('updated_by_id', None)
                contract = Contract.persist(params, can_send_create_email=True)
                if contract.errors:
                    return {'errors': contract.errors}
                if contract.id:
                    contract.add_parties_to_companies_directory(False)
                    title_transfer_params = {
                        "commodity_id": self.commodity_id,
                        "grade_id": self.grade_id,
                        "season": self.season,
                        "seller": seller,
                        "buyer": {
                            "company_id": self.buyer.company_id,
                            "contact_id": self.buyer.contact_id,
                            "ngr_id": self.buyer.ngr_id,
                        },
                        "handler_id": self.site_id,
                        "process_on": today,
                        "tonnage": params.get('tonnage', None),
                        "identifier": title_transfer_identifier,
                        "variety_id": params.get('variety_id', None),
                        "communication": communication,
                        "created_by_id": user.id,
                        "updated_by_id": user.id,
                        "impu": impu,
                        "coil": coil,
                        "canola_load_ids": canola_load_ids,
                        'quantity': params.get('quantity', None)
                    }
                    if load_id:
                        extras = {'load_id': load_id}
                        if tt_extras and isinstance(tt_extras, dict):
                            extras.update(tt_extras)
                        title_transfer_params['extras'] = extras
                    title_transfer = contract.handle_title_transfer_request(
                        args=title_transfer_params, process=False, is_cashed_out=is_cashed_out)
                    if title_transfer.errors:
                        raise Exception(title_transfer.errors)
                    if force_process_now or (not self.external_system and not title_transfer.external_system):
                        transaction.on_commit(title_transfer.queue_mail_and_process)
                        seller_company = Company.objects.filter(id=seller['company_id']).first()
                        self.audit(
                            'cash_out' if user.company_id == seller['company_id'] else 'cash_out_on_behalf',
                            user,
                            tonnage=params.get('tonnage', None),
                            seller=seller_company.name
                        ) if seller_company else None
                    return title_transfer
        return

    @classmethod
    def mark_inactive(cls, cash_price_ids, user=None):
        cls.objects.filter(id__in=cash_price_ids).exclude(status='completed').update(status='inactive')
        cash_prices = cls.objects.filter(id__in=cash_price_ids)
        for cash_price in cash_prices:
            cash_price.audit('inactive', user)

    def can_user_view(self, user):
        return get(self, 'site.company.show_cash_prices_to_all') or user.company_id in [
            get(self, 'site.company_id'), get(self, 'buyer.company_id'), get(self, 'created_by.company_id')]


class CashPricesMapping(BaseModel):
    class meta:
        db_table = 'cash_prices_mapping'

    upload_cashboard_info = models.JSONField(blank=True, null=True)


BUYER_COLUMN = 'Buyer (Optional) - Leave blank if uploading for your company. Enter buyer company name if posting on behalf of Buyer'  # pylint: disable=line-too-long


class CashPricesCSVParser:
    FIELDNAMES = ['Location', 'Location Owner', 'Grade', 'Season',
                  'Contract payment terms', 'Contract payment scales',
                  'Contract terms and conditions', 'Price', 'Qty',
                  'Start date', 'End date', 'Sustainable', BUYER_COLUMN, 'Buyer NGR (optional)',
                  'Status', 'Failure Reason']
    OPTIONAL_FIELD = 'Buyer NGR (optional)'

    def __init__(self, content, user, tz):
        self.content = content
        self.reader = None
        self.cash_prices = []
        self.user = user
        self.tz = tz
        self.buff2 = None
        self.field_names = self.FIELDNAMES.copy()

    def read(self):
        if self.content:
            self.reader = csv.DictReader(io.StringIO(self.content))

    def to_cash_prices(self):
        for row in self.reader:
            if self.OPTIONAL_FIELD not in row and self.OPTIONAL_FIELD in self.field_names:
                self.field_names.remove(self.OPTIONAL_FIELD)
            row = {k.lower(): v for k, v in row.items()}
            cash_prices_row = CashPriceCSVRow(dict(row).copy(), self.user, self.tz,
                                              self.field_names, self.OPTIONAL_FIELD)
            try:
                cash_prices_row.populate()
            except Exception as ex:  # pylint: disable=broad-except
                cash_prices_row.failure_reasons.append('Failure Reason: ' + str(ex))
            finally:
                self.cash_prices.append(cash_prices_row)

    def report(self):
        buff = io.StringIO()
        writer = csv.writer(buff, dialect='excel', delimiter=',')
        writer.writerow(self.field_names.copy())
        success, failures = 0, 0
        for cash_price in self.cash_prices:
            if cash_price.failure_reasons:
                failures += 1
            else:
                success += 1
            writer.writerow(cash_price.to_csv_row())
        self.buff2 = io.BytesIO(buff.getvalue().encode())
        return failures, success

    def sync(self):
        self.read()
        self.to_cash_prices()
        return self.report()


class CashPriceCSVRow:  # pylint: disable=too-many-instance-attributes
    def __init__(self, params, user, tz, field_names, optional_field):
        self.user = user
        self.tz = tz
        self.params = params
        self.field_names = field_names
        self.optional_field = optional_field
        self.location = None
        self.location_owner = None
        self.grade = None
        self.season = None
        self.contract_payment_terms = None
        self.contract_payment_scales = None
        self.price = None
        self.qty = None
        self.start_date = None
        self.end_date = None
        self.sustainable = False
        self.buyer = None
        self.buyer_ngr = None
        self.failure_reasons = []
        self.cash_prices_mapping = CashPricesMapping.objects.first()

    def is_any_field_empty(self):
        buyer_field = BUYER_COLUMN.lower()
        self.params['buyer'] = self.params.get(buyer_field, None)
        del self.params[buyer_field]
        for key, value in self.params.items():
            if not value and key not in [  # pylint: disable=no-else-break
                'contract terms and conditions', 'sustainable', 'expected status', 'failure reason', 'buyer',
                'buyer ngr (optional)'
            ]:
                self.failure_reasons.append(f'{key.capitalize()} cannot be blank')
                break
            elif value:
                continue
        if self.failure_reasons:
            raise Exception('failure reason populated!')

    def populate(self):
        self.is_any_field_empty()
        self.get()
        self.create_cash_price()

    def get(self):
        self.get_location()
        self.get_grade()
        self.get_season()
        self.get_contract_payment_terms()
        self.get_contract_payment_scales()
        self.get_price()
        self.get_qty()
        self.get_start_date()
        self.get_end_date()
        self.get_sustainable()
        self.get_buyer()
        if self.optional_field in self.field_names:
            self.get_buyer_ngr()

    def get_sustainable(self):
        if self.grade:
            sustainable_value = self.params['sustainable'].strip().lower()
            if sustainable_value not in ['y', 'yes', 't', 'true', 'n', 'no', 'f', 'false', '']:
                self.failure_reasons.append(
                    "Sustainable Value is not amongst the allowed Values:; Y/Yes/T/True/N/No/F/False")
            else:
                sustainable = sustainable_value.strip().lower() in ['y', 'yes', 't', 'true']
                if sustainable and not self.grade.commodity.sustainable:
                    self.failure_reasons.append(
                        'Sustainable check is not allowed for this commodity')
                    if self.failure_reasons:  # pylint: disable=raise-missing-from
                        raise Exception('failure reason populated!')
                else:
                    self.sustainable = sustainable

    def get_location_owner(self):
        from core.companies.models import Company

        owner = self.params['location owner'].strip()
        self.location_owner = Company.get_by_name(owner)

    def get_location_owner_errors(self):
        if not self.location_owner:
            self.failure_reasons.append(
                'Location Owner did not match a company name on AgriChain')
        elif not self.location:
            self.failure_reasons.append(
                'Location did not match a site name on AgriChain')
        elif self.location.company_id != self.location_owner.id:
            self.failure_reasons.append(
                'Location did not match location owner')
        elif self.location:
            user_company_id = self.user.company_id
            location_company_id = self.location.company_id
            is_approved = (
                user_company_id == location_company_id or
                self.location.company.approved_buyers.filter(id=self.user.company_id).exists()
            )
            if is_approved:
                if not self.location.is_cash_prices_enabled:
                    self.failure_reasons.append('Cash Price posting is currently disabled for this site.')
            else:
                self.failure_reasons.append(
                    'You are not an approved buyer. Please contact site manager')

    def get_location(self):
        from core.farms.models import Farm
        self.get_location_owner()
        location = self.params['location'].strip()
        self.location = Farm.get_by_name(location, get(self.location_owner, 'id'))
        self.get_location_owner_errors()
        if self.failure_reasons:  # pylint: disable=raise-missing-from
            raise Exception('failure reason populated!')

    def get_buyer(self):
        from core.companies.models import Company
        buyer = self.params['buyer']
        if buyer:
            self.buyer = Company.get_by_name(buyer)
            if not self.buyer:
                self.failure_reasons.append(
                    'Buyer did not match any company name on AgriChain')
                if self.failure_reasons:  # pylint: disable=raise-missing-from
                    raise Exception('failure reason populated!')

    def check_company_in_share_holder_list(self, company_id, ngr):
        if ngr and company_id:
            return company_id in ngr.owner_company_ids
        return True

    def get_buyer_ngr(self):
        from core.ngrs.models import Ngr
        if 'buyer ngr (optional)' in self.params and self.params['buyer ngr (optional)']:
            buyer_ngr = self.params['buyer ngr (optional)'].strip()
            ngrs = Ngr.objects.filter(ngr_number__iexact=buyer_ngr.lower())
            buyer_company_id = get(self.buyer, 'id') or self.user.company_id

            self.buyer_ngr = ngrs.filter(company_id=buyer_company_id).first() if len(ngrs) > 1 else ngrs.first()
            if not self.buyer_ngr:
                self.failure_reasons.append('Buyer NGR not found in system')
            elif buyer_company_id and not self.check_company_in_share_holder_list(buyer_company_id, self.buyer_ngr):
                self.failure_reasons.append('Buyer NGR selected does not belong to the Buyer company')
                self.buyer_ngr = None

    def get_grade(self):
        grade_name = self.params['grade'].strip().lower()
        if grade_name in get(self.cash_prices_mapping.upload_cashboard_info, 'grade', {}).keys():
            grade_name = self.cash_prices_mapping.upload_cashboard_info['grade'][grade_name]
        self.grade = Grade.objects.filter(name__iexact=grade_name).first()
        if not self.grade:
            self.failure_reasons.append(
                'Grade did not match a grade name on AgriChain')
            if self.failure_reasons:  # pylint: disable=raise-missing-from
                raise Exception('failure reason populated!')

    def get_season(self):
        try:
            season = self.params['season'].strip().replace(' ', '')
            if season and len(season) == 4:
                return season[:2] + '/' + season[2:]
            if season and season.find('/') and len(season) > 4:
                split_result = season.split('/')
                season = split_result[0][-2:] + \
                    '/' + split_result[1][-2:]
            self.season = season_validator(season)
        except ValidationError as ex:
            self.failure_reasons.append(
                'Season could not be found on AgriChain')
            if self.failure_reasons:
                raise Exception('failure reason populated!') from ex

    def get_contract_payment_terms(self):
        from core.contracts.models import PaymentTerm
        payment_term = self.params['contract payment terms'].strip(
        ).lower()
        if payment_term in get(self.cash_prices_mapping.upload_cashboard_info, 'payment_term', {}).keys():
            payment_term = self.cash_prices_mapping.upload_cashboard_info[
                'payment_term'][payment_term]
        self.contract_payment_terms = PaymentTerm.find_by_name(payment_term)
        if not self.contract_payment_terms:
            self.failure_reasons.append(
                'Contract Payment Term did not match payment term on AgriChain')
            if self.failure_reasons:
                raise Exception('failure reason populated!')

    def get_contract_payment_scales(self):
        from core.contracts.models import PaymentScale
        payment_scale = self.params['contract payment scales'].strip().lower().replace('  ', ' ')
        if payment_scale in map(
                lambda ps: ps.lower(), get(self.cash_prices_mapping.upload_cashboard_info, 'payment_scale', {}).keys()
        ):
            payment_scale = self.cash_prices_mapping.upload_cashboard_info['payment_scale'][payment_scale]
        self.contract_payment_scales = PaymentScale.find_by_name(payment_scale)
        if not self.contract_payment_scales:
            self.failure_reasons.append(
                'Contract Payment Scale did not match payment scale on AgriChain')
            if self.failure_reasons:
                raise Exception('failure reason populated!')

    def get_price(self):
        price = self.params['price'].strip().strip(Country.get_requesting_country().currency)
        try:
            self.price = self._to_positive_float(price)
        except Exception as ex:  # pylint: disable=broad-except
            self.failure_reasons.append('Price is not valid')
            if self.failure_reasons:
                raise Exception('failure reason populated!') from ex

    @staticmethod
    def _to_positive_float(value):
        _value = float(value)
        if _value < 0:
            raise Exception('Invalid postive float')
        return _value

    def get_qty(self):
        qty = self.params['qty'].strip().strip('MT')
        try:
            self.qty = self._to_positive_float(qty)
        except Exception as ex:  # pylint: disable=broad-except
            self.failure_reasons.append('Qty is not valid')
            if self.failure_reasons:
                raise Exception('failure reason populated!') from ex

    def is_date(self, date_str):
        pattern = r'^\d{1,2}(/|-|\.)\d{1,2}\1\d{2,4}$'
        return re.match(pattern, date_str)

    def get_start_date(self):
        try:
            date = self.params['start date'].strip()
            if self.is_date(date):
                date = f'{date} 00:00'
            self.start_date = DateTimeUtil.timezone_datetime_to_utc(date, self.tz, True)
        except parser.ParserError as ex:
            self.failure_reasons.append(
                'Invalid Start Date. Start Date should be in any of the following formats: \nDD/MM/YY HH:MM \nDD-MM-YY HH:MM \nDD.MM.YY HH:MM')  # pylint: disable=line-too-long
            if self.failure_reasons:
                raise Exception('failure reason populated!') from ex

    def get_end_date(self):
        try:
            date = self.params['end date'].strip()
            if self.is_date(date):
                date = f'{date} 23:59'
            self.end_date = DateTimeUtil.timezone_datetime_to_utc(date, self.tz, True)
            self.get_start_end_date_errors()
        except parser.ParserError as ex:
            self.failure_reasons.append(
                'Invalid End Date. End Date should be in any of the following formats: \nDD/MM/YY HH:MM \nDD-MM-YY HH:MM \nDD.MM.YY HH:MM')  # pylint: disable=line-too-long
            if self.failure_reasons:
                raise Exception('failure reason populated!') from ex

    def get_start_end_date_errors(self):
        now = timezone.now()
        next_five_years = now + relativedelta.relativedelta(years=5)
        if self.start_date and DateTimeUtil.localize(self.start_date, self.tz) > DateTimeUtil.localize(now, self.tz):
            self.failure_reasons.append(
                'Future cash prices cannot be added')
        elif (
            self.end_date and
            DateTimeUtil.localize(self.end_date, self.tz) > DateTimeUtil.localize(next_five_years, self.tz)
        ):
            self.failure_reasons.append(
                f'Invalid End date. Maximum value of End date can be {next_five_years.strftime("%d-%m-%Y")}.')
        elif self.end_date and DateTimeUtil.localize(self.end_date, self.tz) < DateTimeUtil.localize(now, self.tz):
            self.failure_reasons.append("Past Cash Prices are not allowed")
        if self.failure_reasons:  # pylint: disable=raise-missing-from
            raise Exception('failure reason populated!')

    def to_csv_row(self):
        csv_row = [
            get(self.params, 'location', ''),
            get(self.params, 'location owner', ''),
            get(self.params, 'grade', ''),
            get(self.params, 'season', ''),
            get(self.params, 'contract payment terms', ''),
            get(self.params, 'contract payment scales', ''),
            get(self.params, 'contract terms and conditions', ''),
            get(self.params, 'price', ''),
            get(self.params, 'qty', ''),
            get(self.params, 'start date', ''),
            get(self.params, 'end date', ''),
            get(self.params, 'sustainable', ''),
            get(self.params, 'buyer'),
            'Success' if len(self.failure_reasons) == 0 else 'Failure',
            get(self.failure_reasons, '0', '')
        ]
        if self.optional_field.lower() in self.params.keys():
            csv_row.insert(-2, self.params['buyer ngr (optional)'])
        return csv_row

    def check_user_company(self, buyer):
        if self.user.company_id not in [self.location.company.id, buyer.company_id]:
            self.failure_reasons.append('User should belong to either the site company or the buyer company')

        if self.failure_reasons:  # pylint: disable=raise-missing-from
            raise Exception('failure reason populated!')

    def create_cash_price(self):  #pylint: disable=too-many-branches
        company = self.user.company
        ngr_id = get(self.buyer_ngr, 'id') or company.get_tagged_ngr_or_logical('title_transfer')
        try:
            if self.buyer:
                buyer_ngr_id = get(self.buyer_ngr, 'id') or self.buyer.get_tagged_ngr_or_logical('title_transfer')
                buyer_site_admin = self.buyer.employee_set.filter(
                    type_id__in=[FARM_ADMIN_TYPE_ID, FARM_EMPLOYEE_TYPE, COMPANY_ADMIN_TYPE_ID]).first()
                buyer = CashPrices.build_party(
                    {'company_id': self.buyer.company_id, 'contact_id': buyer_site_admin.id, 'ngr_id': buyer_ngr_id},
                    'Buyer'
                )
                if not self.location.company.approved_buyers.filter(id=self.buyer.id).exists():
                    self.location.company.approved_buyers.add(self.buyer)

            else:
                buyer = CashPrices.build_party(
                    {'company_id': self.user.company_id, 'contact_id': self.user.id, 'ngr_id': ngr_id}, 'Buyer'
                )

            today = timezone.now()
            new_cash_price = CashPrices(
                price=self.price,
                limit=self.qty,
                site_id=self.location.id,
                payment_term_id=self.contract_payment_terms.id,
                payment_scale_id=get(self.contract_payment_scales, 'id'),
                commodity_id=self.grade.commodity.id,
                season=self.season,
                grade_id=self.grade.id,
                track=get(self.location, 'default_track'),
                start_date_time=today,  # ignored start date from CSV -- Business Override
                end_date_time=self.end_date,
                sustainable_commodity=self.sustainable,
                created_by=self.user,
                updated_by=self.user
            )

            buyer.save()
            new_cash_price.buyer = buyer
            cash_price = CashPrices.objects.filter(
                site_id=self.location.id, commodity_id=self.grade.commodity.id,
                grade_id=self.grade.id, season=self.season, status='active',
                buyer__company_id=buyer.company_id,
                sustainable_commodity=self.sustainable,
                start_date_time__lte=today, end_date_time__gte=today
            ).first()
            self.check_user_company(buyer)
            if cash_price:
                if cash_price.price != float(new_cash_price.price) or \
                        cash_price.limit != new_cash_price.limit or \
                        cash_price.end_date_time < new_cash_price.end_date_time or \
                        cash_price.limit_remaining < cash_price.limit:
                    cash_price.status = 'inactive'
                    if self.user.company_id == buyer.company_id:
                        cash_price.audit('inactive', self.user)
                    else:
                        cash_price.audit('inactive_on_behalf', self.user)

                    cash_price.save()
                    if cash_price.errors:
                        self.failure_reasons.append(
                            f'Error in existing cash price id {cash_price.id} - {str(cash_price.errors)}')
                else:
                    self.failure_reasons.append('This cash price with exact same details already exists')
            new_cash_price.save()
            if new_cash_price.errors:
                self.failure_reasons.append(f'Error in saving cash price {str(new_cash_price.errors)}')
            else:
                if self.user.company_id == buyer.company_id:
                    new_cash_price.audit('create', self.user)
                else:
                    new_cash_price.audit('create_on_behalf', self.user)
        except:  # pylint: disable=bare-except
            self.failure_reasons.append('Something bad happend!')
        finally:
            if self.failure_reasons:  # pylint: disable=raise-missing-from
                raise Exception('failure reason populated!')
