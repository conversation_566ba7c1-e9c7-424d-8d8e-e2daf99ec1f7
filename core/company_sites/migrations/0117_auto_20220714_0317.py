# Generated by Django 4.0.6 on 2022-07-14 03:17

from django.db import migrations


def remove_unsupported_commodities_from_slot(apps, schema_editor):
    FreightSlot = apps.get_model('company_sites', 'FreightSlot')
    SlotComment = apps.get_model('company_sites', 'SlotComment')
    for slot in FreightSlot.objects.filter(grade_id__gte=5000):
        comment = f"[SYSTEM GENERATED] '{slot.grade.name}' Grade is not supported in AgriChain"
        slot_comment = SlotComment(comment=comment, slot=slot)
        slot_comment.save()
        slot.grade_id = None
        slot.save()

    for slot in FreightSlot.objects.filter(commodity_id__gte=5027):
        commodity_name = slot.commodity.name
        comment = f"[SYSTEM GENERATED] '{commodity_name}' Commodity "
        grade_name = None
        if slot.grade_id and slot.grade_id >= 5000:
            grade_name = slot.grade.name
            comment += f"and '{grade_name}' Grade are "
        else:
            comment += "is "
        comment += "not supported in AgriChain."
        slot_comment = SlotComment(comment=comment, slot=slot)
        slot_comment.save()
        slot.commodity_id = None
        if grade_name:
            slot.grade_id = None
        slot.save()


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0116_freightslot_excluded_commodity_ids_and_more'),
    ]

    operations = [
        migrations.RunPython(remove_unsupported_commodities_from_slot)
    ]
