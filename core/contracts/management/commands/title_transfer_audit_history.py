from django.core.management.base import BaseCommand
from pydash import get

from core.audit_history.models import AuditHistory
from core.contracts.models import TitleTransfer
from core.freights.constants import CONFIRM_REQUEST_TYPE


class Command(BaseCommand):
    help = 'Create audits for existing title transfers'

    def handle(self, *args, **kwargs):  # pylint: disable=unused-argument

        def update_datetime_of_audit(id, created_at, updated_at):
            audit = AuditHistory.objects.filter(instance_id=id).last()
            audit.created_at = created_at
            audit.updated_at = updated_at
            audit.save()

        def create_audit(tt, action, user, created_at, updated_at, acceptance_request_id=None):
            kwargs = {}
            if acceptance_request_id:
                kwargs['acceptance_request_id'] = acceptance_request_id
            tt.audit(action=action, user=user, kwargs=kwargs)
            update_datetime_of_audit(tt.id, created_at, updated_at)

        title_transfers = TitleTransfer.objects.filter()
        total = title_transfers.count()
        count = 0
        for tt in title_transfers:
            count += 1
            print("***********{}/{}".format(count, total))
            acceptance_request = tt.acceptance_requests.filter(type=CONFIRM_REQUEST_TYPE).last()
            if tt.status in ['completed', 'invoiced']:
                create_audit(tt, 'processed', tt.created_by, tt.created_at, tt.created_at)
            create_audit(tt, 'create', tt.created_by, tt.created_at, tt.updated_at, get(acceptance_request, 'id', None))
            if tt.created_at.date() < tt.updated_at.date() and tt.status != 'void':
                create_audit(tt, 'amend', tt.updated_by, tt.updated_at, tt.updated_at)
            if tt.status == 'void':
                create_audit(tt, 'void', tt.updated_by, tt.updated_at, tt.updated_at)


