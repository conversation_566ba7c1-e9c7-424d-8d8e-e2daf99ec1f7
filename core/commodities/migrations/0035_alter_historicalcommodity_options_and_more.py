# Generated by Django 4.0.6 on 2022-08-29 10:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0034_auto_20220721_1540'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='historicalcommodity',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical commodity'},
        ),
        migrations.AlterModelOptions(
            name='historicalepr',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical epr'},
        ),
        migrations.AlterModelOptions(
            name='historicalgrade',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical grade'},
        ),
        migrations.AlterModelOptions(
            name='historicalvariety',
            options={'get_latest_by': 'history_date', 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical variety'},
        ),
        migrations.AlterField(
            model_name='historicalcommodity',
            name='history_date',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='historicalepr',
            name='history_date',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='historicalgrade',
            name='history_date',
            field=models.DateTimeField(),
        ),
        migrations.AlterField(
            model_name='historicalvariety',
            name='history_date',
            field=models.DateTimeField(),
        ),
    ]
