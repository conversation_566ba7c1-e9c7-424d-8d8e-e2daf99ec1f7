# Generated by Django 4.1.10 on 2023-10-13 08:21
from django.contrib.postgres.operations import BtreeGinExtension
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0189_historicaltitletransfer_external_system_and_more'),
    ]

    operations = [
        BtreeGinExtension(),
        migrations.RunSQL('create extension if not exists pg_trgm;'),
        migrations.RunSQL(
            """
            CREATE INDEX contracts_identifier_upper_idx ON contracts
            USING gin (UPPER(identifier) gin_trgm_ops);
            """
        ),
        migrations.RunSQL(
            """
            CREATE INDEX contracts_number_upper_idx ON contracts
            USING gin (UPPER(contract_number) gin_trgm_ops);
            """
        ),
        migrations.RunSQL(
            """
            CREATE INDEX title_transfers_identifier_upper_idx ON title_transfers
            USING gin (UPPER(identifier) gin_trgm_ops);
            """
        ),
    ]
