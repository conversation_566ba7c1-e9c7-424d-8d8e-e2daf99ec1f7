# Generated by Django 4.1.13 on 2024-08-27 05:04

from django.db import migrations

def disable_global_farm_listing_for_all_users(apps, schema_editor):
    PlatformFeatures = apps.get_model('companies', 'PlatformFeatures')
    PlatformFeatures.objects.update(farms=False)

class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0182_company_password_expiry_period'),
    ]

    operations = [
        migrations.RunPython(disable_global_farm_listing_for_all_users)
    ]
