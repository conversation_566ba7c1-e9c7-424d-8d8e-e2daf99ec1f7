# Generated by Django 4.0.6 on 2022-08-01 05:34

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('banks', '0013_alter_bank_bsb_number_alter_bank_code_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='bank',
            name='bsb_number',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=15), blank=True, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='historicalbank',
            name='bsb_number',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=15), blank=True, null=True, size=None),
        ),
    ]
