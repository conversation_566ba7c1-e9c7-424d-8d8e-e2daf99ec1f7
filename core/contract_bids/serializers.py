from rest_framework.serializers import ModelSerializer
from rest_framework.fields import Serializer<PERSON><PERSON>od<PERSON><PERSON>
from rest_framework import serializers

from core.companies.serializers import CompanyGroupsSerializer
from core.contract_bids.models import ContractBid
from core.contracts.serializers import PartyAssetNameSerializer, PaymentTermSerializer


class ContractBidSerializer(ModelSerializer):
    site_name = serializers.CharField(source='site.display_name')
    site_company_id = serializers.IntegerField(source='site.company_id')
    commodity_name = serializers.CharField(source='commodity.display_name')
    buyer = PartyAssetNameSerializer()
    payment_term = PaymentTermSerializer()
    status_display_name = SerializerMethodField()
    group = CompanyGroupsSerializer()
    price_unit = serializers.CharField(source='commodity.price_unit')

    class Meta:
        model = ContractBid
        fields = (
            'id', 'site_id', 'commodity_name', 'site_name', 'end_date_time', 'buyer',
            'commodity_id', 'season', 'grade_id', 'limit', 'price', 'start_date_time',
            'limit_remaining', 'grade_name', 'track', 'payment_term',
            'status', 'status_display_name', 'payment_scale', 'site_company_id', 'delivery_start_date',
            'delivery_end_date', 'group', 'created_by_id', 'price_unit'
        )

    def get_status_display_name(self, obj):
        return obj.status_display_name()
