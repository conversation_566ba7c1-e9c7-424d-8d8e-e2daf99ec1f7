# Generated by Django 4.1.10 on 2023-10-27 04:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0018_alert_hours_before_trigger_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='alert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit Breach'), ('pickup_confirmation_sms', 'Pickup Confirmation - SMS'), ('inload_done', 'Delivery Confirmation - SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Confirmation - Email'), ('delivery_information', 'Delivery Confirmation - Email'), ('outload_report', 'Outload Report'), ('slot_booking_alert', 'Slot Booking Updates'), ('truck_cleanliness_declaration_alert', 'Truck Cleanliness Declaration Not Filled')], max_length=100),
        ),
        migrations.Alter<PERSON>ield(
            model_name='historicalalert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit Breach'), ('pickup_confirmation_sms', 'Pickup Confirmation - SMS'), ('inload_done', 'Delivery Confirmation - SMS'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Confirmation - Email'), ('delivery_information', 'Delivery Confirmation - Email'), ('outload_report', 'Outload Report'), ('slot_booking_alert', 'Slot Booking Updates'), ('truck_cleanliness_declaration_alert', 'Truck Cleanliness Declaration Not Filled')], max_length=100),
        ),
    ]
