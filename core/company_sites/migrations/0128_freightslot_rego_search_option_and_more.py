# Generated by Django 4.1.9 on 2023-08-08 08:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0127_auto_20230427_0308'),
    ]

    operations = [
        migrations.AddField(
            model_name='freightslot',
            name='rego_search_option',
            field=models.CharField(blank=True, choices=[('freight_provider_regos', 'Search In Freight Provider Regos'), ('all_regos', 'Search In All Regos'), ('sub_freight_provider_regos', 'Search By Sub Freight Provider Regos')], max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='rego_search_option',
            field=models.CharField(blank=True, choices=[('freight_provider_regos', 'Search In Freight Provider Regos'), ('all_regos', 'Search In All Regos'), ('sub_freight_provider_regos', 'Search By Sub Freight Provider Regos')], max_length=100, null=True),
        ),
    ]
