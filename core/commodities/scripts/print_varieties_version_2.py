import csv
import json

res = []
from core.commodities.models import Variety
latest_id = Variety.objects.latest('id').id

varities = Variety.objects.filter(commodity_id=6)
csvfile = open('core/commodities/scripts/Desi_version_2.csv', "r")
fieldnames = ("Old","New",)
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if i!=0:
        _dict = dict(row)
        if _dict["Old"]:
            old_variety = list(filter(lambda v: v.name.lower() == _dict["Old"].lower(), varities))[0]
            _json = {
                "pk": old_variety.id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {},
                    "commodity_id": 6,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        else:
            latest_id += 1
            _json = {
                "pk": latest_id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {},
                    "commodity_id": 6,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        res.append(_json)

varities = Variety.objects.filter(commodity_id=7)
csvfile = open('core/commodities/scripts/Kabuli_version_2.csv', "r")
fieldnames = ("Old","New",)
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if i!=0:
        _dict = dict(row)
        if _dict["Old"]:
            old_variety = list(filter(lambda v: v.name.lower() == _dict["Old"].lower(), varities))[0]
            _json = {
                "pk": old_variety.id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {},
                    "commodity_id": 7,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        else:
            latest_id += 1
            _json = {
                "pk": latest_id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {},
                    "commodity_id": 7,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        res.append(_json)

varities = Variety.objects.filter(commodity_id=5)
csvfile = open('core/commodities/scripts/Lentils_version_2.csv', "r")
fieldnames = ("Old","New",)
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if i!=0:
        _dict = dict(row)
        if _dict["Old"]:
            old_variety = list(filter(lambda v: v.name.lower() == _dict["Old"].lower(), varities))[0]
            _json = {
                "pk": old_variety.id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {},
                    "commodity_id": 5,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        else:
            latest_id += 1
            _json = {
                "pk": latest_id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {},
                    "commodity_id": 5,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        res.append(_json)

varities = Variety.objects.filter(commodity_id=2)
csvfile = open('core/commodities/scripts/Barley_version_2.csv', "r")
fieldnames = ("Old","New","Grade 1 Name","Grade 2 Name","Grade 3 Name","Maximum Grade")

reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if i!=0:
        _dict = dict(row)
        if _dict["Old"]:
            old_variety = list(filter(lambda v: v.name.lower() == _dict["Old"].lower(), varities))[0]
            _json = {
                "pk": old_variety.id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {"grade_1": _dict["Grade 1 Name"], "grade_2": _dict["Grade 2 Name"], "grade_3": _dict["Grade 3 Name"], "maximum_grade": _dict["Maximum Grade"]},
                    "commodity_id": 2,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        else:
            latest_id += 1
            _json = {
                "pk": latest_id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {"grade_1": _dict["Grade 1 Name"], "grade_2": _dict["Grade 2 Name"], "grade_3": _dict["Grade 3 Name"], "maximum_grade": _dict["Maximum Grade"]},
                    "commodity_id": 2,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        res.append(_json)

varities = Variety.objects.filter(commodity_id=1)
csvfile = open('core/commodities/scripts/Wheat_version_2.csv', "r")
fieldnames = ("Old","New","Western Category","Southern Category","South-Eastern Category","Northern Category","Western Maximum Grade","Southern Maximum Grade","South-Eastern Maximum Grade","Northern Maximum Grade")

reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if i!=0:
        _dict = dict(row)
        if _dict["Old"]:
            old_variety = list(filter(lambda v: v.name.lower() == _dict["Old"].lower(), varities))[0]
            _json = {
                "pk": old_variety.id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {"categories": {"western": _dict["Western Category"], "northern": _dict["Northern Category"], "southern": _dict["Southern Category"], "south_eastern": _dict["South-Eastern Category"]}, "maximum_grades": {"western": _dict["Western Maximum Grade"], "northern": _dict["Northern Maximum Grade"], "southern": _dict["Southern Maximum Grade"], "south_eastern": _dict["South-Eastern Maximum Grade"]}},
                    "commodity_id": 1,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        else:
            latest_id += 1
            _json = {
                "pk": latest_id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {"categories": {"western": _dict["Western Category"], "northern": _dict["Northern Category"], "southern": _dict["Southern Category"], "south_eastern": _dict["South-Eastern Category"]}, "maximum_grades": {"western": _dict["Western Maximum Grade"], "northern": _dict["Northern Maximum Grade"], "southern": _dict["Southern Maximum Grade"], "south_eastern": _dict["South-Eastern Maximum Grade"]}},
                    "commodity_id": 1,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        res.append(_json)

varities = Variety.objects.filter(commodity_id=3)
csvfile = open('core/commodities/scripts/Canola_version_2.csv', "r")
fieldnames = ("Old","New",)
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if i!=0:
        _dict = dict(row)
        if _dict["Old"]:
            old_variety = list(filter(lambda v: v.name.lower() == _dict["Old"].lower(), varities))[0]
            _json = {
                "pk": old_variety.id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {},
                    "commodity_id": 3,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        else:
            latest_id += 1
            _json = {
                "pk": latest_id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {},
                    "commodity_id": 3,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        res.append(_json)

varities = Variety.objects.filter(commodity_id=4)
csvfile = open('core/commodities/scripts/Sorghum_version_2.csv', "r")
fieldnames = ("Old","New",)
reader = csv.DictReader(csvfile, fieldnames)
for i, row in enumerate(reader):
    if i!=0:
        _dict = dict(row)
        if _dict["Old"]:
            old_variety = list(filter(lambda v: v.name.lower() == _dict["Old"].lower(), varities))[0]
            _json = {
                "pk": old_variety.id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {},
                    "commodity_id": 4,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        else:
            latest_id += 1
            _json = {
                "pk": latest_id,
                "model": "commodities.variety",
                "fields": {
                    "name": _dict["New"],
                    "details": {},
                    "commodity_id": 4,
                    "created_at": "2018-05-11T08:00:00+00:00",
                    "updated_at": "2018-05-11T08:00:00+00:00",
                    "created_by": 1,
                    "updated_by": 1,
                },
            }
        res.append(_json)

# SET session_replication_role = replica;
print(json.dumps(res))
# SET session_replication_role = DEFAULT;
#update loads set variety_id = 327 where variety_id=261;
#update loads set variety_id = 330 where variety_id=262;
#update home_storages set variety_id = 330 where variety_id = 262;
