# Generated by Django 4.0.3 on 2022-05-05 05:51

from django.db import migrations


def add_allied_system_name(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    allied_company = Company.objects.filter(id=57).first()
    if allied_company:
        allied_company.extras = allied_company.extras or {}
        allied_company.extras['system_name'] = 'allied'
        allied_company.save()


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0080_company_bulk_invoicing_from_and_more'),
    ]

    operations = [
        migrations.RunPython(add_allied_system_name)
    ]
