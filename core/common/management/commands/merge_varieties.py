from django.core.management import BaseCommand

from core.commodities.models import EPR, Variety
from core.common.models import MergedEntity
from core.stocks.models import Stock


class Command(BaseCommand):
    help = 'Merge varieties'

    def handle(self, *args, **options):
        pairs = [[649, 650], [4973, 2684], [4860, 1698], [4829, 846], [4499, 2634], [4500, 473], [4558, 1142], [4966, 2672], [4734, 313], [4501, 1686], [4559, 4618], [4502, 6], [4503, 1685], [401, 402], [1524, 1525], [4927, 4729], [4949, 4729], [2660, 217], [4668, 1189], [4671, 1189], [4467, 217], [4718, 1189], [4468, 217], [4719, 1189], [506, 4736], [4897, 10], [4504, 12], [4945, 676], [4505, 2636], [4563, 4620], [4475, 268], [945, 4622], [4506, 25], [4564, 953], [749, 4498], [4507, 38], [4565, 966], [4984, 2662], [480, 4508], [4566, 1149], [4509, 44], [4567, 972], [4510, 48], [4568, 976], [4738, 2671], [4511, 1684], [4478, 2673], [4980, 2673], [4632, 982], [4996, 61], [4998, 61], [4858, 680], [4512, 70], [4570, 995], [1199, 4700], [75, 4896], [4513, 76], [4571, 1001], [4994, 4876], [1004, 4633], [4866, 80], [4926, 916], [1692, 4863], [4514, 483], [4572, 1152], [4891, 89], [4515, 1296], [4573, 2830], [4901, 1678], [4865, 1678], [4862, 376], [4917, 376], [4953, 532], [4480, 550], [4516, 94], [4574, 1019], [2642, 96], [4636, 1021], [2655, 2003], [4517, 2643], [4575, 4637], [4975, 377], [4491, 378], [4974, 378], [4518, 105], [4576, 1030], [4492, 2687], [4519, 109], [4577, 1034], [4521, 487], [4579, 1156], [4522, 114], [4580, 1039], [4907, 2688], [4868, 2667], [4909, 2689], [4523, 118], [4581, 1043], [5001, 2666], [4640, 4561], [4641, 4561], [4995, 4524], [4525, 129], [4583, 1054], [4526, 489], [4584, 1158], [4993, 4470], [4527, 1299], [4585, 4643], [4481, 2675], [4906, 381], [4528, 1300], [4586, 4644], [4529, 133], [4587, 1058], [4530, 1301], [4588, 4645], [4531, 1302], [4589, 4646], [4534, 139], [4979, 927], [4401, 4959], [4991, 4763], [4983, 4959], [4969, 4959], [4990, 4764], [4535, 1687], [4593, 4647], [4912, 4761], [4956, 4761], [4867, 492], [4970, 4910], [4537, 491], [4538, 146], [4539, 148], [4864, 150], [4471, 250], [4541, 152], [4542, 153], [4986, 1306], [4543, 1306], [4972, 252], [4485, 928], [4831, 150], [4869, 254], [4545, 158], [4603, 1083], [4546, 2646], [4604, 4655], [4987, 4547], [341, 4767], [4661, 4556], [4548, 172], [4486, 4732], [4908, 4494], [4549, 495], [4607, 1164], [4487, 4742], [4550, 186], [4608, 1111], [4488, 532], [4977, 4483], [4999, 1688], [4551, 188], [4968, 498], [4490, 272], [4553, 191], [4611, 1116], [4554, 196], [4612, 1121], [4497, 631], [4971, 4761], [2677, 349], [4859, 691], [5000, 4473], [4812, 4473]]
        for relation in Variety._meta._relation_tree:
            klass = relation.model
            name = relation.name
            for pair in pairs:
                print("Processing:", pair)
                if klass.__name__ == 'EPR':
                    old_variety_epr = EPR.objects.filter(variety_id=pair[0]).first()
                    new_variety_epr = EPR.objects.filter(variety_id=pair[1]).first()
                    if old_variety_epr and new_variety_epr:
                        print(f"Both Varieties {pair} have EPRs, need to merge manually, skipping")
                    continue
                print(f"Processing {klass} for {pair}")
                if klass.__name__ == 'Load':
                    klass.objects.filter(**{
                        f"{name}_id": pair[0]
                    }).update(**{
                        f"{name}_id": pair[1],
                        'do_not_update_stock': True
                    })
                else:
                    klass.objects.filter(**{f"{name}_id": pair[0]}).update(**{f"{name}_id": pair[1]})
        for pair in pairs:
            for stock in Stock.objects.filter(variety_ids__contains=[pair[0]]):
                stock.variety_ids.remove(pair[0])
                if pair[1] not in stock.variety_ids:
                    stock.variety_ids.append(pair[1])
                stock.save()
            MergedEntity(from_id=pair[0], to_id=pair[1], entity_model_name='Variety').save()
