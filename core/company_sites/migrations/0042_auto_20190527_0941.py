# Generated by Django 2.1.7 on 2019-05-27 09:41

from django.db import migrations

def update_cancelled_status_label_to_incomplete_in_sm_settings(apps, schema_editor):
    SiteManagementSettings = apps.get_model('company_sites', 'SiteManagementSettings')
    for settings in SiteManagementSettings.objects.all():
        for status in settings.statuses:
            if status['id'] == 'cancelled':
                status['label'] = 'Incomplete'

        settings.save()

class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0041_auto_20190524_1509'),
    ]

    operations = [
        migrations.RunPython(update_cancelled_status_label_to_incomplete_in_sm_settings)
    ]
