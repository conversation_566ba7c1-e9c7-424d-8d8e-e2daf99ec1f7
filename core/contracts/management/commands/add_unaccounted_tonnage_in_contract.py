from django.core.management.base import BaseCommand
from core.contracts.models import Contract

# pylint: disable=line-too-long
class Command(BaseCommand):
    help = "Add unaccounted tonnage for all contracts in the system."

    def handle(self, *args, **kwargs): # pylint: disable=unused-argument
        contracts = Contract.objects.order_by('-id')
        no_of_contracts_without_unaccounted_tonnage = contracts.count()
        self.stdout.write(
            self.style.SUCCESS('Total no of Contracts Without tonnage is (%s)"!' % no_of_contracts_without_unaccounted_tonnage)
        )
        errors = []
        saved = 0
        error_count = 0
        for contract in contracts:
            try:
                contract.unaccounted_tonnage = contract.get_unaccounted_tonnage()
                contract.save()
                if not contract.errors:
                    self.stdout.write('Remaining*********** (%s)' % (no_of_contracts_without_unaccounted_tonnage - saved))
                    saved = saved + 1
                else:
                    error_count = error_count + 1
                    errors.append({contract.id, contract.status, contract.errors})
                    self.stdout.write(self.style.ERROR('"Contract ID:(%s)"!' % contract.id))
                    self.stdout.write(self.style.ERROR('"Contract Status:(%s)"!' % contract.status))
                    self.stdout.write(self.style.ERROR('Error: %s!' % contract.errors))
            except Exception as ex: # pylint: disable=broad-except
                self.stdout.write(self.style.ERROR('"Contract ID:(%s)"!' % contract.id))
                self.stdout.write(self.style.ERROR('Error: %s!' % ex.args[0]))
                error_count = error_count + 1
                errors.append({contract.id, contract.status})

        no_of_contracts_without_unaccounted_tonnage = Contract.objects.filter(
            unaccounted_tonnage__isnull=True
        ).count()
        self.stdout.write(
            self.style.SUCCESS('Total no of contract Without tonnage is (%s)"!' % no_of_contracts_without_unaccounted_tonnage)
        )
        self.stdout.write('*********** (%s)' % errors)
        self.stdout.write('*********** (%s)' % saved)
        self.stdout.write('*********** (%s)' % error_count)
