# Generated by Django 2.1.7 on 2019-06-19 06:03

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('companies', '0033_auto_20190611_0748'),
    ]

    operations = [
        migrations.CreateModel(
            name='HistoricalSubcontractor',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('accepted', models.BooleanField(default=False)),
                ('resolved', models.BooleanField(default=False)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField()),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('contractor', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.Company')),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_subcontractors_created_by', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('subcontractor', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.Company')),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='companies_subcontractors_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical subcontractor',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Subcontractor',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('accepted', models.BooleanField(default=False)),
                ('resolved', models.BooleanField(default=False)),
                ('contractor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subcontractor_set', to='companies.Company')),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_subcontractor_related_created_by', related_query_name='companies_subcontractors_created_by', to=settings.AUTH_USER_MODEL)),
                ('subcontractor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contractor_set', to='companies.Company')),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_subcontractor_related_updated_by', related_query_name='companies_subcontractors_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'subcontractors',
            },
        ),
    ]
