# Generated by Django 4.1.7 on 2023-03-09 10:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='alert',
            name='trigger_type',
        ),
        migrations.RemoveField(
            model_name='historicalalert',
            name='trigger_type',
        ),
        migrations.AlterField(
            model_name='alert',
            name='channel',
            field=models.CharField(choices=[('sms', 'SMS'), ('email', 'Email')], max_length=100),
        ),
        migrations.AlterField(
            model_name='alert',
            name='frequency',
            field=models.CharField(blank=True, choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='alert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Spec Average Report'), ('mass_Limit', 'Mass Limit'), ('inload_done', 'Inload Done')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalalert',
            name='channel',
            field=models.CharField(choices=[('sms', 'SMS'), ('email', 'Email')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalalert',
            name='frequency',
            field=models.CharField(blank=True, choices=[('daily', 'Daily'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='historicalalert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Spec Average Report'), ('mass_Limit', 'Mass Limit'), ('inload_done', 'Inload Done')], max_length=100),
        ),
    ]
