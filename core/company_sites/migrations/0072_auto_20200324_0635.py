# Generated by Django 2.2 on 2020-03-24 06:35

from django.db import migrations


def update_provider_updated_fields(apps, schema_editor):
    FreightSlot = apps.get_model('company_sites', 'FreightSlot')
    for slot in FreightSlot.objects.all():
        if slot.provider_updated_fields:
            slot.provider_updated_fields = [
                f for f in slot.provider_updated_fields if f not in ['type', 'site_id', 'start', 'end', 'created_by_id']
            ]
            slot.save()


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0071_auto_20200228_1011'),
    ]

    operations = [
        migrations.RunPython(update_provider_updated_fields)
    ]
