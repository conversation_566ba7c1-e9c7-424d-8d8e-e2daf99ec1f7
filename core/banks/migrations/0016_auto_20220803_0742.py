# Generated by Django 4.0.6 on 2022-08-03 07:42

from django.db import migrations


def bank_schema_updations(apps, schema_editor):
    Bank = apps.get_model('banks', 'Bank')
    BankAccount = apps.get_model('ngrs', 'BankAccount')
    BankAccount.objects.all().update(bank_id=None)
    Bank.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ('banks', '0015_alter_historicalbank_options_alter_bank_bsb_number_and_more'),
        ('ngrs', '0038_auto_20220804_0622'),
    ]

    operations = [
        migrations.RunPython(bank_schema_updations)
    ]
