# Generated by Django 2.1.7 on 2019-05-13 05:14

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0027_auto_20190509_0659'),
    ]

    operations = [
        migrations.AddField(
            model_name='freightslot',
            name='provider_updated_fields',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=[], null=True),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='provider_updated_fields',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=[], null=True),
        ),
    ]
