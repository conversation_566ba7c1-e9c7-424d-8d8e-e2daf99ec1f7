# Generated by Django 4.1.13 on 2024-04-02 04:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0049_commodity_type_historicalcommodity_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='commodity',
            name='unit',
            field=models.CharField(choices=[('MT', 'MT'), ('Bales', 'Bales'), ('m³', 'm³'), ('BU', 'BU'), ('LB', 'LB'), ('CWT', 'CWT'), ('kg', 'kg'), ('litre', 'litre')], default='MT', max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalcommodity',
            name='unit',
            field=models.CharField(choices=[('MT', 'MT'), ('Bales', 'Bales'), ('m³', 'm³'), ('BU', 'BU'), ('LB', 'LB'), ('CWT', 'CWT'), ('kg', 'kg'), ('litre', 'litre')], default='MT', max_length=100),
        ),
    ]
