# Generated by Django 2.1.7 on 2019-06-20 11:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0034_historicalsubcontractor_subcontractor'),
        ('company_sites', '0049_auto_20190612_0534'),
    ]

    operations = [
        migrations.AddField(
            model_name='freightslot',
            name='sub_freight_provider',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='subcontracted_slot_set', to='companies.Company'),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='sub_freight_provider',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.Company'),
        ),
        migrations.AlterField(
            model_name='freightslot',
            name='freight_provider',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='contracted_slot_set', to='companies.Company'),
        ),
    ]
