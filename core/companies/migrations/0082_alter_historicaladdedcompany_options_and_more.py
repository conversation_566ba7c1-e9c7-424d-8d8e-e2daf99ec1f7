# Generated by Django 4.0.4 on 2022-05-13 10:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0081_auto_20220505_0551'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='historicaladdedcompany',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical added company', 'verbose_name_plural': 'historical added companys'},
        ),
        migrations.AlterModelOptions(
            name='historicalcompany',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical company', 'verbose_name_plural': 'historical companys'},
        ),
        migrations.AlterModelOptions(
            name='historicalcompanysetup',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical company setup', 'verbose_name_plural': 'historical company setups'},
        ),
        migrations.AlterModelOptions(
            name='historicalcompanytype',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical company type', 'verbose_name_plural': 'historical company types'},
        ),
        migrations.AlterModelOptions(
            name='historicalfarmdirectory',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical farm directory', 'verbose_name_plural': 'historical farm directorys'},
        ),
        migrations.AlterModelOptions(
            name='historicalsubcontractor',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical subcontractor', 'verbose_name_plural': 'historical subcontractors'},
        ),
        migrations.AlterField(
            model_name='historicaladdedcompany',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='historicalcompanysetup',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='historicalcompanytype',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='historicalfarmdirectory',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='historicalsubcontractor',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
    ]
