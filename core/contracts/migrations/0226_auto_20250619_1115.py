# Generated by Django 5.1.8 on 2025-06-19 11:15

from django.db import migrations


def update_spread_details(apps, schema_editor):
    Spread = apps.get_model('contracts', 'Spread')
    for spread in Spread.objects.all():
        if spread.details:
            updated = False
            for detail in spread.details:
                if 'price' in detail and not detail['price']:
                    detail['price'] = None
                    updated = True
                if 'value' in detail and not detail['value']:
                    detail['value'] = None
                    updated = True
            if updated:
                spread.save()


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0225_alter_historicalpaymentterm_name_and_more'),
    ]

    operations = [
        migrations.RunPython(update_spread_details),
    ]
