from urllib.parse import urlencode

from django.test import tag
from django.urls import reverse
from rest_framework import status

from core.common.tests import ACTestCase, ACAPITestCase
from .models import Bank


@tag('model')
class BankTest(ACTestCase):
    def test_search(self):
        Bank.create({
            'name': 'AMP Bank Limited',
            'code': 'AMP',
            'bsb_number': '123123'
        })

        Bank.create({
            'name': 'Bank Australia 1264',
            'code': 'BA',
            'bsb_number': '123124'
        })

        Bank.create({
            'name': 'Foo',
            'code': 'Foo',
            'bsb_number': '123125'
        })

        banks = Bank.search({'name': ['Bank Australia 1264']})
        self.assertEqual(len(banks), 1)
        self.assertEqual(banks[0]['name'], 'Bank Australia 1264')

        self.assertEqual(len(Bank.search({'name': ['Foo']})), 1)


@tag('view')
class BankViewTest(ACAPITestCase):
    def setUp(self):
        self.bank_api_url = reverse('core.banks:bank')
        super().setUp()

    def test_get_success(self):
        _bank1 = Bank(name='Bank 1', code='B1', bsb_number="123431")
        _bank1.save()

        _bank2 = Bank(name='FOOBAR', code="FB", bsb_number="123221")
        _bank2.save()

        _query_string = urlencode({'name': 'FOOBAR'})
        _search_url = self.bank_api_url + "?" + _query_string
        _response = self.client.get(_search_url)
        self.assertEqual(_response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(_response.data), 1)
        self.assertEqual(_response.data[0]['name'], 'FOOBAR')
