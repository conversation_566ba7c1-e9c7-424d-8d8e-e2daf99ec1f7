import json
import time
from datetime import datetime, timedelta
from statistics import mean

import pytz
from dateutil.relativedelta import relativedelta
from django.contrib.postgres.fields import Array<PERSON>ield
from django.core.mail import EmailMessage
from django.core.serializers.json import DjangoJ<PERSON>NEncoder
from django.db import models
from django.db import transaction
from django.db.models import Count, Q, Sum
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.functional import cached_property
from inflection import titleize, underscore
from notifications.signals import notify
from pydash import find, get, compact, has, flatten

from core.commodities.models import Commodity, Grade
from core.common.constants import (LOAD_TYPES, COMPANY_ADMIN_TYPE_ID, FROM_EMAIL, MT, BALES, MODULES, METER_CUBE,
                                   DRIVER_TYPE_ID, KG, LITRE, SUPPORT_EMAIL, OUTLOAD, INLOAD)
from core.services.internal.errbit import ERRBIT_LOGGER
from core.common.models import BaseModel, InstanceCustomFuncMixin, RawModel, RoundedFloatField
from core.common.utils import (camelize, get_changeset, is_super_admin,
    is_continuous_list_of_numbers)
from core.timezones.utils import DateTimeUtil
from core.company_sites.constants import (
    PUSH_NOTIFICATION_DICTIONARY,
    BOOKED_SINGLE_SLOT_WITH_FP,
    NEW_BOOKED_RECURRING_SLOTS_WITH_FP,
    CANCELLED_BOOKING_BY_SITE_MANAGER,
    CHANGED_STATUS,
    UPDATED_SLOT_BY_SITE_MANAGER,
    BOOKED_SLOT,
    CANCELLED_BOOKING_BY_BOOKIE,
    UPDATED_SLOT_BY_BOOKIE,
    NEW_COMMENT_BY_BOOKIE,
    STATUSES, DEFAULT_SLOT_STATUSES, DEFAULT_SLOT_FIELDS, SLOT_VIEWS,
    MARK_SLOT_URGENT_HOURS_FROM_START,
    REVERT_TO_BOOKED_STATUSES,
    FLEET_TRUCK_MAIL_SUBJECT, CHECKPOINT_ORDER_CREATION_PERMISSIONS, SLOT_OLD_VERSION, SLOT_SETTING_OLD_VERSION,
    TRUCK_COMPANY_NOT_ALLOWED)
from core.countries.models import Country
from core.devices.constants import (
    NOTIFICATION_TYPE_SLOT, NOTIFICATION_TYPE_SLOT_ID, INTERRUPTION_LEVEL_TIME_SENSITIVE
)
from core.devices.models import MobilePushNotification
from core.farms.models import Farm
from core.freights.constants import CUSTOMER_ONLY_TYPE_ID, REQUEST_TYPE_VOIDED
from core.services.external.ably import ABLY
from core.services.internal.webpush import WebPush
from core.settings import WEB_URL
from core.toggles.models import Toggle
from core.trucks.constants import FLEET_REGO
from core.trucks.models import TruckCheckpointLog, Truck, TruckCategory
from core.validation.validators import season_validator

CompanySite = Farm


class SlotChangeset:
    def __init__(self, slot, history, tz=None, datetime_format=None):
        self.slot = slot
        self.history = history
        self.tz = tz
        self.datetime_format = datetime_format or Country.get_country_format("datetime_with_seconds")

    @cached_property
    def by(self):
        return get(self.history.history_user or self.history.updated_by, 'name')

    @staticmethod
    def get_display_value(value, klass, attr, humanize, multiple):
        if not value:
            return 'None'
        val = 'None'
        if multiple and isinstance(value, list):
            items = klass.objects.filter(id__in=value)
            return ', '.join([get(item, attr) for item in items])
        elif not isinstance(value, str) or value.isdigit():
            val = str(get(klass.objects.filter(id=value).first(), attr) or value or None)
        if humanize:
            return titleize(val)
        return val

    def update_old_new_display_value(self, old, new, klass, attr, humanize=True, multiple=False):
        return (self.get_display_value(old, klass, attr, humanize, multiple),
                self.get_display_value(new, klass, attr, humanize, multiple))

    def status_description(self, old, new):
        by = self.by
        status_display = self.slot.status_display_name(new)
        if old == 'booked' and new == 'planned':
            return f"Booking cancelled by {by}"
        if old in REVERT_TO_BOOKED_STATUSES and new == 'booked':
            return f"Slot reverted to Booked by {by}"
        if old == 'completed' and new == 'planned':
            return f"Booking cancelled by {by} by voiding the movement"
        if (new in ['in_progress', 'cancelled', 'delayed', 'rejected', 'completed'] or
                (old in ['in_progress'] and new in ['booked'])):
            return f"Slot marked {status_display} by {by}"
        if new in ['booked']:
            return f"Slot {status_display} by {by}"


    def generic_updated_description(self):
        return "Slot updated by {}".format(self.by)

    @staticmethod
    def is_none(value):
        return not value or value == 'None'

    def format_change(self, change):  # pylint: disable=too-many-branches
        from core.freights.models import FreightContract, FreightOrder
        from core.companies.models import Company
        from core.profiles.models import Employee

        field = change.field
        old = change.old
        new = change.new

        id_fields_model_mapper = {
            'excluded_grade_ids': {'model': Grade, 'field_name': 'name', 'display_name': 'Excluded Grades'},
            'excluded_commodity_ids': {
                'model': Commodity, 'field_name': 'display_name', 'display_name': 'Excluded Commodities'
            }
        }

        if field in ['movement', 'movement_id']:
            old, new = self.update_old_new_display_value(old, new, FreightContract, 'identifier', False)
        elif field in ['order', 'order_id', 'delivery_order_number']:
            old, new = self.update_old_new_display_value(old, new, FreightOrder, 'identifier', False)
        elif field in ['truck', 'truck_id']:
            old, new = self.update_old_new_display_value(old, new, Truck, 'rego', False)
        elif field in ['commodity', 'commodity_id']:
            old, new = self.update_old_new_display_value(old, new, Commodity, 'display_name')
        elif field in ['grade', 'grade_id']:
            old, new = self.update_old_new_display_value(old, new, Grade, 'name', False)
        elif field in ['driver', 'driver_id']:
            old, new = self.update_old_new_display_value(old, new, Employee, 'name', False)
        elif field in ['sub_freight_provider', 'freight_provider']:
            old, new = self.update_old_new_display_value(old, new, Company, 'name')
        elif field in ['category', 'category_id']:
            old, new = self.update_old_new_display_value(old, new, TruckCategory, 'truck_code')
        elif field in ['tonnage'] and new:
            display_unit = Country.get_requesting_country().display_unit
            old = f'{old} {display_unit}' if old else 'None'
            new = f'{new} {display_unit}' if new else 'None'
        elif field in ['start', 'end']:
            old = DateTimeUtil.localize_date(old, self.tz, self.datetime_format)
            new = DateTimeUtil.localize_date(new, self.tz, self.datetime_format)
        elif field in id_fields_model_mapper:
            old, new = self.update_old_new_display_value(
                old, new, get(id_fields_model_mapper, f'{field}.model'),
                get(id_fields_model_mapper, f'{field}.field_name'), False, True
            )

        display_name = self.slot.attr_display_name(field)
        if field in id_fields_model_mapper:
            display_name = get(id_fields_model_mapper, f"{field}.display_name")

        if old != new:
            items = {'new': new}
            if not self.is_none(old):
                items['old'] = old
            return {display_name: items}

    @cached_property
    def history_date(self):
        return DateTimeUtil.localize_date(self.history.history_date, self.tz, self.datetime_format)

    @staticmethod
    def get_status_changed_delta(changes):
        result = None
        for change in changes:
            if change.field == 'status' and change.old != change.new:
                result = change
                break

        return result

    @staticmethod
    def should_have_no_items(heading):
        return heading and ('Booking cancelled' in heading or 'Reverted to Booked' in heading)

    def is_status_based(self, heading):
        return heading and heading != self.generic_updated_description()

    def changes(self):
        changeset = {'heading': [], 'items': [], 'by': self.by}
        prev_history = self.history.prev_record
        if prev_history:
            delta = self.history.diff_against(prev_history)
            status_changed_delta = self.get_status_changed_delta(delta.changes)
            if status_changed_delta:
                heading = self.status_description(status_changed_delta.old, status_changed_delta.new)
            else:
                heading = self.generic_updated_description()
            changeset['heading'] = heading
            blacklisted_fields = [
                'status', 'updated_by', 'provider_updated_fields', 'is_urgent_for_provider',
                'is_urgent_for_manager', 'siblings', 'site', 'extras', '_entered_tonnage',
                '_last_pushed_at', 'is_trailer_slot', 'trailer_slot_ids',
                'restricted_visible_to_carrier',
            ]
            if self.slot.settings.order_booking:
                blacklisted_fields.append('delivery_order_number')
            for change in delta.changes:
                if change.field in blacklisted_fields or (not change.old and not change.new):
                    continue
                changeset['items'].append(self.format_change(change))
            changeset['items'] = compact(changeset['items'])
        if self.should_have_no_items(changeset.get('heading')):
            changeset['items'] = []

        if changeset.get('heading'):
            return changeset


class SlotAuditHistory:
    def __init__(
            self, slot, tz=None, user=None, datetime_format=None, start=None, end=None):
        self.slot = slot
        self.tz = tz
        self.user = user
        self.datetime_format = datetime_format or Country.get_country_format("datetime_with_seconds")
        self.start = start
        self.end = end

    @property
    def is_site_manager(self):
        return get(self.user, 'company_id') == self.slot.site.company_id

    @cached_property
    def created_date(self):
        return DateTimeUtil.localize_date(self.slot.created_at, self.tz, self.datetime_format)

    def created_event(self):
        first_history = self.first_history()
        items = []
        if first_history:
            items = [
                {'Start': {'new': DateTimeUtil.localize_date(first_history.start, self.tz, self.datetime_format)}},
                {'End': {'new': DateTimeUtil.localize_date(first_history.end, self.tz, self.datetime_format)}},
            ]
            if first_history.movement_id:
                movement = first_history.instance.movement
                heading = 'Created by {} in {}'.format(
                    self.slot.created_by.name, self.slot.status_display_name(first_history.status)
                )
                items = [
                    *items,
                    {'Movement': {'new': movement.identifier}},
                    {self.slot.attr_display_name('delivery_order_number'): {'new': movement.order.identifier}},
                    {self.slot.attr_display_name('tonnage'):
                         {'new': f'{str(first_history.tonnage)} {movement.commodity.unit}'}},
                    {self.slot.attr_display_name('grade'): {'new': first_history.grade.name}},
                    {self.slot.attr_display_name('commodity'): {'new': first_history.commodity.display_name}},
                    {
                        self.slot.attr_display_name('freight_provider'): {
                            'new': get(first_history, 'freight_provider.name')
                        }
                    },
                    {self.slot.attr_display_name('truck'): {'new': get(first_history, 'truck.rego')}},
                    {self.slot.attr_display_name('driver'): {'new': get(first_history, 'driver.name')}},
                ]
                return {self.created_date: {'heading': heading, 'items': items}}
            if first_history.status in ['booked', 'planned']:
                return self.just_created_event(items)
            if first_history.status:
                return self.created_event_with_status(get(first_history, 'status'), items)
        else:
            return self.just_created_event(items)

    def created_event_with_status(self, status, items):
        return {
            self.created_date: {
                'heading': "Created by {} in {} status".format(self.slot.created_by.name, status),
                'items': items
            }
        }

    def just_created_event(self, items):
        return {self.created_date: {'heading': "Created by {}".format(self.slot.created_by.name), 'items': items}}

    def first_history(self):
        return self.histories.last()

    def changeset(self):
        histories = self.histories.order_by('history_id')
        result = {}
        date_time_format = self.datetime_format
        for history in histories:
            changeset = SlotChangeset(self.slot, history, self.tz, date_time_format)
            history_date = changeset.history_date
            changes = changeset.changes()
            if changes:
                if history_date in result:
                    heading = changes.get('heading')
                    if result[history_date]['heading']:
                        result[history_date]['heading'] += f' and {heading}'
                    else:
                        result[history_date]['heading'] = heading
                    result[history_date]['items'] = [*result[history_date]['items'], *changes.get('items')]
                else:
                    result[history_date] = changes

        vendor_decs = self.get_vendor_decs()
        if vendor_decs:
            for vd in vendor_decs:
                result[DateTimeUtil.localize_date(vd.created_at, self.tz, self.datetime_format)] = {
                    'heading': vd.name,
                    'href': "/vendor-decs/{}/details".format(vd.id)
                }
        result = {**result, **self.created_event()}
        result = dict(sorted(
            result.items(),
            key=lambda x: datetime.strptime(x[0], self.datetime_format).timestamp(),
            reverse=True
        ))
        return result

    def get_vendor_decs(self):
        vendor_decs = self.slot.get_vendor_decs()
        if vendor_decs.exists():
            if self.is_site_manager:
                return vendor_decs
            from core.vendor_decs.constants import TRUCK_CLEANLINESS_TYPE
            return vendor_decs.filter(type=TRUCK_CLEANLINESS_TYPE)

    @cached_property
    def histories(self):
        queryset = self.total_history
        if self.start:
            queryset = queryset.filter(history_date__gte=self.start)
        if self.end:
            queryset = queryset.filter(history_date__lte=self.end)
        return queryset.order_by('-history_date')

    @cached_property
    def total_history(self):
        return self.slot.history


class SlotReport:  # pylint: disable=too-many-instance-attributes
    def __init__(
            self, start, end, tz, company_id=None, site_id=None, site_ids=None, provider_ids=None, extra_filters=None):
        self.start = start
        self.end = end
        self.tz = tz
        self.company_id = company_id
        self.site_id = site_id
        self.site_ids = site_ids
        self.provider_ids = provider_ids or []
        self.extra_filters = extra_filters or {}
        self.queryset = None
        self.queryset_without_provider = None
        self.__build_queryset()

    def __build_queryset(self):
        self.queryset_without_provider = FreightSlot.objects.filter(
            start__gte=self.start, end__lte=self.end
        )
        if self.site_ids:
            self.queryset_without_provider = self.queryset_without_provider.filter(site_id__in=self.site_ids)
        if self.site_id:
            self.queryset_without_provider = self.queryset_without_provider.filter(site_id=self.site_id)
        if self.company_id:
            self.queryset_without_provider = self.queryset_without_provider.filter(site__company_id=self.company_id)

        self.queryset = self.queryset_without_provider.filter(**self.extra_filters)

    def loading_times_distribution(self):
        queryset = self.queryset
        loading_times = compact([slot.movement_load_time() for slot in queryset])
        return {
            'max': "{}".format(DateTimeUtil.timedelta_format(max(loading_times, default=0.0))),
            'min': "{}".format(DateTimeUtil.timedelta_format(min(loading_times, default=0.0))),
            'avg': "{}".format(DateTimeUtil.timedelta_format(mean(loading_times) if loading_times else 0.0))
        }  # seconds

    def time_period_distribution(self, exclusions=None):
        exclusions = exclusions or {}

        return self.queryset.exclude(
            **exclusions
        ).values('start').annotate(count=Count('id'))

    @property
    def peak_hours(self):
        hourly_distribution = self.time_period_distribution(exclusions={'status__in': ['planned']})
        hour_aggregate = {}
        for period_stats in hourly_distribution:
            hour = DateTimeUtil.localize(period_stats['start'], self.tz).hour
            if hour not in hour_aggregate:
                hour_aggregate[hour] = 0
            hour_aggregate[hour] += period_stats['count']

        max_count = max(hour_aggregate.values(), default=0)
        hours_with_max_count = [hour for hour, count in hour_aggregate.items() if count == max_count]

        if is_continuous_list_of_numbers(hours_with_max_count):
            return [{'hour_from': hours_with_max_count[0], 'hour_to': hours_with_max_count[-1] + 1}]
        else:
            return [{'hour_from': hour, 'hour_to': hour + 1} for hour in hours_with_max_count]

    @property
    def peak_hours_text(self):
        peak_hours = self.peak_hours
        peak_hours_text = []
        if peak_hours:
            for peak_hour in peak_hours:
                peak_hours_text.append("{} - {}".format(
                    datetime.strptime(str(peak_hour['hour_from']), "%H").strftime("%I:%M %p"),
                    datetime.strptime(str(peak_hour['hour_to']), "%H").strftime("%I:%M %p"),
                ))

        return " & ".join(peak_hours_text)

    @cached_property
    def status_distribution(self):
        return self.queryset.values('status').annotate(count=Count('status'))

    @cached_property
    def total_count(self):
        return self.queryset.count()

    @cached_property
    def incomplete_count(self):
        return self.booked_count - self.complete_count

    @cached_property
    def booked_count(self):
        return sum(self.status_distribution.exclude(status='planned').values_list('count', flat=True) or [0])

    @cached_property
    def complete_count(self):
        return get(self.status_distribution.filter(status='completed').values_list('count', flat=True), '0', 0)

    @cached_property
    def cancelled_count(self):
        return FreightSlot.to_cancelled_count(
            self.queryset_without_provider, self.provider_ids)

    @property
    def complete_percentage(self):
        return self.__get_percentage(self.complete_count)

    @property
    def incomplete_percentage(self):
        return self.__get_percentage(self.incomplete_count)

    @property
    def cancelled_percentage(self):
        return self.__get_percentage(self.cancelled_count)

    def __get_percentage(self, count):
        total_count = self.booked_count
        if not total_count:
            return 0.0

        return round((count/total_count) * 100, 2)

    def to_site_comparison_csv_row(self):
        loading_time = self.loading_times_distribution()
        return [
            self.booked_count,
            self.complete_count,
            self.incomplete_count,
            self.cancelled_count,
            self.complete_percentage,
            self.incomplete_percentage,
            self.cancelled_percentage,
            loading_time['avg'],
            loading_time['max'],
            loading_time['min'],
            self.peak_hours_text
        ]

    @classmethod
    def get_site_comparison_csv_rows(
            cls, start, end, tz, company_id=None, site_ids=None, extra_filters=None
    ):
        farms = None
        extra_filters = extra_filters or {}

        if company_id:
            farms = Farm.objects.filter(company_id=company_id)
        elif site_ids:
            farms = Farm.objects.filter(id__in=site_ids)
        if farms is None:
            raise ValueError('Please provide either site_ids or company_id to generate report on')

        rows = []

        for farm in farms:
            report = cls(start=start, end=end, tz=tz,
                         site_id=farm.id, extra_filters=extra_filters)
            rows.append(
                [
                    farm.name, *report.to_site_comparison_csv_row(),
                    TruckCheckpointLog.aggregate_spent_time_by_trucks_on_sites(
                        start=start, end=end, site_ids=[farm.id]
                    ),
                    TruckCheckpointLog.aggregate_spent_time_by_trucks_on_sites(
                        start=start, end=end, site_ids=[farm.id], aggregate_type=models.Min
                    ),
                    TruckCheckpointLog.aggregate_spent_time_by_trucks_on_sites(
                        start=start, end=end, site_ids=[farm.id], aggregate_type=models.Max
                    ),
                    TruckCheckpointLog.waiting_time(
                        site_ids=[farm.id], start=start, end=end
                    ),
                ]
            )

        report = cls(start=start, end=end, tz=tz, site_ids=site_ids, company_id=company_id, extra_filters=extra_filters)
        farm_ids = list(farms.values_list('id', flat=True))
        rows.append(
            ['Overall', *report.to_site_comparison_csv_row(),
             TruckCheckpointLog.overall_average_time_spent(
                site_ids=farm_ids, start=start, end=end
            ),
                TruckCheckpointLog.aggregate_spent_time_by_trucks_on_sites(
                site_ids=farm_ids, start=start, end=end, aggregate_type=models.Min
            ),
                TruckCheckpointLog.aggregate_spent_time_by_trucks_on_sites(
                site_ids=farm_ids, start=start, end=end, aggregate_type=models.Max
            ),
                TruckCheckpointLog.waiting_time(
                site_ids=farm_ids, start=start, end=end
            ),
            ])
        report.append_providers(rows)

        return rows

    def get_sites(self):
        if self.company_id:
            return Farm.objects.filter(company_id=self.company_id)
        if self.site_ids:
            return Farm.objects.filter(id__in=self.site_ids)
        if self.site_id:
            return Farm.objects.filter(id=self.site_id)

    def get_providers(self):
        provider_ids = self.extra_filters.get('freight_provider_id__in', None)
        if provider_ids:
            from core.companies.models import Company
            return Company.objects.filter(id__in=provider_ids)

    def append_sites(self, rows):
        sites = self.get_sites()
        if sites and sites.exists():
            rows.append(["\n"])
            rows.append(["\n"])
            rows.append(["\n"])
            rows.append(['Sites Included', ])
            for site in sites:
                rows.append([site.name, ])

    def append_providers(self, rows):
        providers = self.get_providers()
        if providers and providers.exists():
            rows.append(["\n"])
            rows.append(["\n"])
            rows.append(["\n"])
            rows.append(['Carriers Included', ])
            for provider in providers:
                rows.append([provider.name, ])

    @classmethod
    def get_carrier_comparison_csv_rows(
            cls, start, end, tz, provider_ids=None, company_id=None, site_ids=None, extra_filters=None
    ):
        extra_filters = extra_filters or {}
        if not provider_ids:
            base_queryset = cls(
                start=start, end=end, tz=tz, site_ids=site_ids, company_id=company_id, extra_filters={**extra_filters}
            ).queryset
            provider_ids = {
                *base_queryset.values_list('freight_provider_id', flat=True),
                *FreightSlot.history.filter(
                    id__in=base_queryset.values_list('id', flat=True)
                ).values_list('freight_provider_id', flat=True)
            }

        from core.companies.models import Company
        providers = Company.objects.filter(id__in=provider_ids)

        rows = []

        for provider in providers:
            report = cls(
                start=start, end=end, tz=tz, site_ids=site_ids, company_id=company_id, provider_ids=[provider.id],
                extra_filters={**extra_filters, 'freight_provider_id': provider.id}
            )
            rows.append([provider.name, *report.to_site_comparison_csv_row()])

        report = cls(
            start=start, end=end, tz=tz, site_ids=site_ids, company_id=company_id, provider_ids=provider_ids,
            extra_filters={**extra_filters, 'freight_provider_id__in': provider_ids}
        )
        rows.append(['Overall', *report.to_site_comparison_csv_row()])
        report.append_sites(rows)

        return rows


class FreightSlot(BaseModel, InstanceCustomFuncMixin):  # pylint: disable=too-many-public-methods,too-many-instance-attributes
    class Meta:
        db_table = 'freight_slots'
        indexes = [
            models.Index(fields=['start']),
            models.Index(fields=['end']),
            models.Index(fields=['status']),
            models.Index(fields=['-updated_at']),
        ]

    type = models.CharField(null=True, blank=True, max_length=7, choices=LOAD_TYPES)
    start = models.DateTimeField()
    end = models.DateTimeField()
    booking_number = models.CharField(max_length=50, null=True, blank=True)
    delivery_order_number = models.CharField(max_length=50, null=True, blank=True)
    status = models.CharField(max_length=50, choices=STATUSES, default='planned', db_index=True)
    season = models.CharField(max_length=5, null=True, blank=True, validators=[season_validator])
    tonnage = RoundedFloatField(null=True, blank=True)
    _entered_tonnage = models.FloatField(null=True, blank=True)
    priority = models.BooleanField(default=False)
    site = models.ForeignKey(Farm, on_delete=models.CASCADE)
    commodity = models.ForeignKey('commodities.Commodity', on_delete=models.DO_NOTHING, null=True, blank=True,)
    excluded_commodity_ids = ArrayField(models.IntegerField(), null=True, blank=True, default=None)
    excluded_grade_ids = ArrayField(models.IntegerField(), null=True, blank=True, default=None)
    grade = models.ForeignKey('commodities.Grade', on_delete=models.DO_NOTHING, null=True, blank=True)
    truck = models.ForeignKey('trucks.Truck', on_delete=models.SET_NULL, null=True, blank=True,)
    driver = models.ForeignKey('profiles.Employee', on_delete=models.SET_NULL, null=True, blank=True,)
    customer = models.CharField(max_length=64, null=True, blank=True)
    cancellation_reason = models.TextField(max_length=5000, null=True, blank=True)
    freight_provider = models.ForeignKey(
        'companies.Company',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='contracted_slot_set',
    )
    sub_freight_provider = models.ForeignKey(
        'companies.Company',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='subcontracted_slot_set',
    )
    siblings = models.JSONField(null=True, blank=True, default=list)
    provider_updated_fields = models.JSONField(null=True, blank=True, default=list)
    pits = models.CharField(max_length=100, null=True, blank=True)
    is_urgent_for_provider = models.BooleanField(default=False)
    is_urgent_for_manager = models.BooleanField(default=False)
    order = models.ForeignKey('freights.FreightOrder', null=True, blank=True, on_delete=models.CASCADE)
    movement = models.ForeignKey('freights.FreightContract', null=True, blank=True, on_delete=models.CASCADE)
    on_route = models.BooleanField(default=False)
    restricted_visible_to_carrier = models.BooleanField(default=False)
    restriction_reason = models.TextField(null=True, blank=True)
    is_trailer_slot = models.BooleanField(default=False)
    trailer_slot_ids = ArrayField(models.IntegerField(), null=True, blank=True, default=list)
    parent_slot_id = models.IntegerField(null=True, blank=True, db_index=True)
    extras = models.JSONField(null=True, blank=True)

    # private
    _last_pushed_at = models.DateTimeField(null=True, blank=True)

    FILLABLES = [
        'type',
        'start',
        'end',
        'booking_number',
        'delivery_order_number',
        'status',
        'commodity_id',
        'excluded_commodity_ids',
        'excluded_grade_ids',
        'grade_id',
        'tonnage',
        'season',
        'truck_id',
        'driver_id',
        'customer',
        'cancellation_reason',
        'priority',
        'freight_provider_id',
        'provider_updated_fields',
        'pits',
        'comment',
        'site_id',
        'is_urgent_for_provider',
        'is_urgent_for_manager',
        'sub_freight_provider_id',
        'order_id',
        'movement_id',
        'is_urgent_for_manager',
        'updated_at',
        'settings_updated_at',
        'counter_slot_updated_at',
        'counter_slot_settings_updated_at',
        'on_route',
        'restricted_visible_to_carrier',
        'restriction_reason',
        'is_trailer_slot',
        'trailer_slots',
        'trailer_slot_ids',
        'parent_slot_id',
        'counter_trailer_slots',
        'id',
        'category_id',
        'steer_1_point_1',
        'steer_point_5',
        'truck_config',
        'permit_number',
        'declared_mass_limit',
        'accreditation_number',
        'load_sharing',
        'notice',
        'notice_number',
        'restricted',
        '_entered_tonnage',
        'amend_order_to_tonnage',
        'chemical_applications',
        'feature',
    ]

    @cached_property
    def country_id(self):
        return self.commodity.country_id if self.commodity_id else self.site.country_id

    @cached_property
    def country_code(self):
        return self.country.code

    @cached_property
    def country(self):
        return Country.objects.filter(id=self.country_id).first()

    @property
    def inferred_tonnage_unit(self):
        return get(self, 'commodity.price_unit') or MT

    @property
    def inferred_tonnage(self):
        return self._entered_tonnage or self.tonnage

    def can_hard_delete(self, user):
        return self.status in ['planned', 'restricted'] and user.company_id == self.site.company_id

    @property
    def counter_slot_id(self):
        return get(self.counter_slots.only('id').first(), 'id')

    @property
    def counter_slot(self):
        return self.counter_slots.first()


    @property
    def counter_slots(self):
        if self.movement_id and self.type:
            queryset = FreightSlot.objects.filter(movement_id=self.movement_id, is_trailer_slot=False)
            return queryset.filter(type=OUTLOAD if self.is_inload else INLOAD)
        return FreightSlot.objects.none()

    @property
    def is_order_both_site_booking_on(self):
        if not self.order_id:
            return False
        order = self.order
        return order.is_pickup_site_slot_order_booking_on and order.is_delivery_site_slot_order_booking_on

    @property
    def booking_history(self):
        return self.history.order_by('-history_date').filter(status='booked').first()

    @property
    def last_planned_history(self):
        return self.history.order_by('-history_date').filter(status='planned').first()

    @property
    def booked_by(self):
        booked_history = self.booking_history
        return get(booked_history, 'history_user') or get(booked_history, 'instance.updated_by')

    @property
    def is_booked_by_bookie(self):
        booked_by = self.booked_by
        return booked_by and booked_by.company_id != self.site.company_id

    @property
    def provider_company_admin(self):
        admin = None
        if self.sub_freight_provider_id:
            admin = self.sub_freight_provider.employee_set.filter(type_id=COMPANY_ADMIN_TYPE_ID).first()

        if not admin and self.freight_provider_id:
            admin = self.freight_provider.employee_set.filter(type_id=COMPANY_ADMIN_TYPE_ID).first()

        return admin

    def send_fleet_truck_booking_mail(self):
        booked_by = self.booked_by
        if not booked_by:
            return

        if booked_by.email:
            recipient = booked_by
        elif get(self, 'driver.email'):
            recipient = self.driver
        else:
            provider_company_admin = self.provider_company_admin
            recipient = provider_company_admin if get(provider_company_admin, 'email') else None

        if not recipient or not recipient.email:
            return

        body = render_to_string(
            'bookie_fleet_truck_mail.html',
            {
                'recipient': recipient,
                'slot': self,
                'slot_url': self.site_booking_url,
                'label': self.name(
                    tz=get(self.site, '_timezone.location', booked_by.country.timezone), include_date=True)
            }
        )
        mail = EmailMessage(
            subject=FLEET_TRUCK_MAIL_SUBJECT,
            body=body,
            from_email=FROM_EMAIL,
            to=[recipient.email],
            reply_to=[SUPPORT_EMAIL],
        )
        mail.content_subtype = "html"
        mail.send()

        self.extras = self.extras or {}
        self.extras['fleet_reminder_sent_at'] = str(timezone.now())
        self.save()

    @property
    def date_after_end(self):
        return self.start + timedelta(days=1)

    @property
    def site_booking_url(self):
        return WEB_URL + '/#/site-bookings?siteId={}&companyId={}&slotId={}&startDate={}&endDate={}'.format(
            self.site_id, self.site.company_id, self.id,
            self.start.strftime('%Y-%m-%d'), self.date_after_end.strftime('%Y-%m-%d')
        )

    @property
    def draft_load_exists_on_booking(self):
        if self.movement_id:
            return self.movement.draft_load_exists
        return False

    @classmethod
    def get_completed_slots_with_given_driver_id(cls, date, employee_id):
        start = date.replace(hour=0, minute=0, second=0)
        end = date.replace(hour=23, minute=59, second=59)
        return FreightSlot.objects.filter(driver_id=employee_id, status='completed', start__gte=start,
                                          end__lte=end, updated_at__gte=date).values_list('id', flat=True)

    @classmethod
    def transition_status_for_draft_loads(cls, loads, status):
        result_list = list(loads.values_list('draft_load_json__freight_movement_number', 'draft_load_json__type'))
        if result_list:
            from functools import reduce
            query = reduce(
                lambda x, load: x | Q(movement__identifier=load[0], type=load[1]),
                result_list,
                Q()
            )
            for slot in cls.objects.filter(query):
                slot.status = status
                slot.save()
                cls.objects.filter(id__in=slot.trailer_slot_ids).update(status=status)


    def edit_trailer_slots(self, new_trailer_slot_ids):
        new_trailer_set = set(new_trailer_slot_ids)
        existing_trailer_set = set(self.trailer_slot_ids)
        existing_trailer_slot_ids_to_cancel = existing_trailer_set - new_trailer_set
        result = new_trailer_set - existing_trailer_set
        if new_trailer_set:
            for slot in FreightSlot.objects.filter(id__in=list(new_trailer_set)):
                slot.parent_slot_id = self.id
                slot.is_trailer_slot = True
                slot.freight_provider_id = self.freight_provider_id
                slot.sub_freight_provider_id = self.sub_freight_provider_id
                slot.commodity_id = self.commodity_id
                slot.grade_id = self.grade_id
                slot.status = self.status
                slot.order_id = self.order_id
                slot.movement_id = self.movement_id
                slot.driver_id = self.driver_id
                slot.truck_id = self.truck_id
                slot.tonnage = self.tonnage
                slot._entered_tonnage = self._entered_tonnage
                slot.type = self.type
                slot.booking_number = self.booking_number
                slot.save()
        exclude_slot_ids = list(result)
        cancel_existing_slot = False
        for slot_id in self.trailer_slot_ids:
            if slot_id in new_trailer_slot_ids:
                exclude_slot_ids.append(slot_id)
            else:
                cancel_existing_slot = True
        if cancel_existing_slot or existing_trailer_slot_ids_to_cancel:
            self.cancel_trailer_slots(exclude_slot_ids)
        self.trailer_slot_ids = new_trailer_slot_ids
        self.save()

    def add_trailer_slots(self, trailer_slots):
        trailer_slot_ids = [value for value in trailer_slots.values() if value is not None]
        if self.trailer_slot_ids:
            self.edit_trailer_slots(trailer_slot_ids)
        else:
            self.trailer_slot_ids = trailer_slot_ids
            for slot in FreightSlot.objects.filter(id__in=self.trailer_slot_ids):
                slot.parent_slot_id = self.id
                slot.is_trailer_slot = True
                slot.freight_provider_id = self.freight_provider_id
                slot.sub_freight_provider_id = self.sub_freight_provider_id
                slot.commodity_id = self.commodity_id
                slot.grade_id = self.grade_id
                slot.status = self.status
                slot.order_id = self.order_id
                slot.movement_id = self.movement_id
                slot.driver_id = self.driver_id
                slot.truck_id = self.truck_id
                slot.tonnage = self.tonnage
                slot._entered_tonnage = self._entered_tonnage
                slot.type = self.type
                slot.booking_number = self.booking_number
                slot.save()
            self.save()

    def get_vendor_decs(self):
        from core.vendor_decs.models import CommodityVendorDec
        has_criteria = False
        criteria = Q()
        if self.movement_id:
            has_criteria = True
            criteria |= Q(movement_id=self.movement_id)
            commodity_contract_id = get(self, 'movement.commodity_contract_id')
            if commodity_contract_id:
                criteria |= Q(contract_id=commodity_contract_id)
        if self.order_id:
            has_criteria = True
            criteria |= Q(order_id=self.order_id)

        if has_criteria:
            return CommodityVendorDec.objects.exclude(status__in=['draft', 'template']).filter(criteria)

        return CommodityVendorDec.objects.none()

    def loading_time(self):  # time in in_progress
        if self.is_completed():
            try:
                in_progress = self.history.filter(status='in_progress').order_by('-history_date').first()
                if in_progress:
                    timedelta_for_in_progress = in_progress.next_record.history_date - in_progress.history_date
                    return timedelta_for_in_progress.total_seconds()
            except:  # pylint: disable=bare-except
                pass

    def movement_load_time(self):
        if self.movement_id and get(self, 'movement.status') in ['delivered', 'completed']:
            movement = self.movement
            outload = movement.outload
            inload = movement.inload
            if outload and inload:
                load_time_diff = inload.date_time - outload.date_time
                return load_time_diff.total_seconds()

    def time_display_text(self, tz):
        tz = tz or self.timezone
        time_format = Country.get_country_format("time")
        return "{} - {}".format(
            DateTimeUtil.localize_time(self.start, tz, time_format),
            DateTimeUtil.localize_time(self.end, tz, time_format)
        )

    def date_time_display_text(self, tz):
        tz = tz or self.timezone
        return "{} - {}".format(
            DateTimeUtil.localize_time(self.start, tz, Country.get_country_format("datetime")),
            DateTimeUtil.localize_time(self.end, tz, Country.get_country_format("time"))
        )

    @property
    def timezone(self):
        return get(self.site, '_timezone.location')

    def name(self, tz=None, include_date=False):
        tz = tz or self.timezone
        name = self.date_time_display_text(tz) if include_date else self.time_display_text(tz)

        if self.commodity_id:
            name += " - {}".format(self.commodity.display_name)
        if self.grade_id:
            name += " - {}".format(self.grade.name)
        if self.tonnage:
            name += " - {}".format(self.inferred_tonnage)
            if self.commodity_id:
                name += self.commodity.unit

        return name

    def status_display_name(self, status=None):
        status = status or self.status
        settings = self.settings
        result = None
        if settings:
            result = get([s for s in settings.statuses if s['id'] == status], '[0].name')

        return result or titleize(status)

    def attr_display_name(self, attr):
        settings = self.settings
        result = None
        if settings:
            result = get([f for f in settings.fields if underscore(f['id']) == attr], '[0].name')

        return result or titleize(attr)

    @cached_property
    def settings(self):
        return get(self, 'site.sitemanagementsettings')

    def audit_history(
            self, tz=None, datetime_format=None, user=None, start=None, end=None):
        return SlotAuditHistory(self, tz=tz, user=user, datetime_format=datetime_format, start=start, end=end)

    def history_changeset(
            self, tz=None, datetime_format=None, user=None, start=None, end=None):
        return self.audit_history(tz, datetime_format, user, start, end).changeset()

    @classmethod
    def to_localized_report_data(cls, queryset, tz):
        return list(map(lambda slot: slot.localized_report_data(tz), queryset))

    def slot_category(self):
        if get(self, 'status') == 'planned':
            return
        load_type = 'External' if get(self, 'order.commodity_contract') else 'Internal'
        if self.type == 'outload' and load_type:
            return load_type + ' Outload'
        elif self.type == 'inload' and load_type:
            return load_type + ' Inload'

    def localized_report_data(self, tz, booking_cancellation=False): # pylint: disable=too-many-locals
        status = find(self.settings.statuses, lambda s: s['id'] == self.status)
        if booking_cancellation:
            booked_tonnage = self.inferred_tonnage
            actual_tonnage = None
        else:
            booked_tonnage = get(self.booking_history, 'tonnage')
            actual_tonnage = self.inferred_tonnage

        load = self.load
        country = self.country
        date_format = country.get_format('date')
        time_format = country.get_format('time')
        datetime_format = country.get_format('datetime')
        actual_start_time = get(load, 'date_time')
        actual_end_time = get(load, 'completed_at')

        truck_entry_time = get(load, 'truck_entry_time')
        truck_exit_time = get(load, 'truck_exit_time')
        delivery_entry_time = (get(self.movement, 'inload.truck_entry_time') or
                                get(self.movement, 'footprint.drop_location_recorded_at'))
        pickup_exit_time = (get(self.movement, 'outload.truck_exit_time') or
                                get(self.movement, 'outload.completed_at'))
        transaction_time, variance, travel_time, time_on_site = None, None, None, None
        if actual_end_time and actual_start_time:
            transaction_time_in_seconds = int((actual_end_time - actual_start_time).total_seconds())
            transaction_time = DateTimeUtil.timedelta_format(transaction_time_in_seconds)
        if self.start and actual_start_time:
            variance_in_seconds = int((actual_start_time - self.start).total_seconds())
            variance = DateTimeUtil.timedelta_format(variance_in_seconds)
        if delivery_entry_time and pickup_exit_time:
            travel_time_in_seconds = int((delivery_entry_time - pickup_exit_time).total_seconds())
            travel_time = DateTimeUtil.timedelta_format(travel_time_in_seconds)
        if truck_entry_time and truck_exit_time:
            time_on_site = "{} Minutes".format(int((truck_exit_time - truck_entry_time).total_seconds() / 60))

        data = [
            get(self, 'site.name'),
            (self.type or '').title(),
            self.slot_category(),
            'Yes' if self.priority else 'No',
            DateTimeUtil.localize_date(self.start, tz, date_format),
            DateTimeUtil.localize_time(self.start, tz, time_format),
            DateTimeUtil.localize_date(actual_start_time, tz, datetime_format),
            DateTimeUtil.localize_time(self.end, tz, time_format),
            DateTimeUtil.localize_date(actual_end_time, tz, datetime_format),
            DateTimeUtil.localize_time(truck_entry_time, tz, time_format) if truck_entry_time else None,
            DateTimeUtil.localize_time(truck_exit_time, tz, time_format) if truck_exit_time else None,
            DateTimeUtil.localize_time(actual_start_time, tz, time_format) if actual_start_time else None,
            DateTimeUtil.localize_time(
                get(load, 'completed_at'), tz, time_format) if get(load, 'completed_at') else None,
            time_on_site,
            get(self, 'commodity.display_name'),
            get(self, 'grade.name'),
            booked_tonnage,
            self.customer,
            get(self, 'movement.identifier'),
            self.order_number(None),
            get(self, 'order.commodity_contract.reference_number'),
            self.booking_number,
            self.pits,
            get(self, 'freight_provider.name'),
            get(self, 'sub_freight_provider.name'),
            get(self, 'truck.rego'),
            get(self, 'driver.name'),
            get(self, 'driver.mobile'),
            transaction_time,
            variance,
            travel_time,
            DateTimeUtil.localize_date(self.created_at, tz, datetime_format),
            self.created_by.name,
            DateTimeUtil.localize_date(self.updated_at, tz, datetime_format),
            self.updated_by.name,
        ]

        if not booking_cancellation:
            data.insert(3, status.get('label', self.status.title()))
            data.insert(18, actual_tonnage)

        return data

    @property
    def site_order(self):
        return self.get_site_order(self.order)

    def get_site_order(self, order):
        if not order:
            return
        if self.is_outload:
            return order.linked_pickup_site_order
        if self.is_inload:
            return order.linked_delivery_site_order

    def get_order_refs(self, user):
        grain_order = None
        freight_order = None
        site_order = None
        if self.order_id:
            direct_order = self.order
            site_order = self.site_order
            if self.order.is_call_on_grain:
                grain_order = direct_order
            else:
                freight_order = direct_order
                top_level_order = direct_order.top_level_order()
                if get(top_level_order, 'is_call_on_grain'):
                    grain_order = top_level_order
        contract = None
        if self.order_id:
            contract = get(
                self.order.buyer_linked_delivery_order(user), 'commodity_contract'
            ) or get(self.order, 'commodity_contract')

        return {
            'id': self.id,
            'grain_order': {
                'id': grain_order.id,
                'identifier': grain_order.get_identifier_for_user(user),
                'can_view': user.company_id in grain_order.viewer_company_ids
            } if grain_order else None,
            'freight_order': {
                'id': freight_order.id,
                'identifier': freight_order.get_identifier_for_user(user),
                'can_view': user.company_id in freight_order.viewer_company_ids
            } if freight_order else None,
            'site_order': {
                'id': site_order.id if user.company_id in site_order.viewer_company_ids else None,
                'identifier': site_order.get_identifier_for_user(user),
                'commodity_contract_id': get(contract, 'id'),
                'commodity_contract_number': get(contract, 'reference_number'),
                'can_view': user.company_id in site_order.viewer_company_ids
            } if site_order else None
        }

    def order_number(self, user=None):
        if self.order_id:
            return self.order.get_identifier_for_user(user)

        return self.delivery_order_number

    @property
    def comments_count(self):
        return self.slotcomment_set.count()

    def get_comments(self, joiner=', '):
        return joiner.join(compact(self.slotcomment_set.values_list('comment', flat=True)))

    def __has_time_updated(self, previous_version):
        return self.__is_any_field_updated(previous_version, ['start', 'end'])

    def clean(self):
        super().clean()
        if not self.tonnage and self._entered_tonnage:
            self._entered_tonnage = self.tonnage
        self.convert_tonnage()

        if self.truck_id and not getattr(self, 'freight_provider_id', None):
            self.freight_provider_id = self.truck.company_id
        if self.is_booked() or self.is_completed() or self.is_in_progress() or self.is_delayed():
            for field in ['freight_provider_id']:
                if getattr(self, field, None):
                    if isinstance(self.errors, dict):
                        self.errors.pop(field, None)
                else:
                    error = {}
                    error[field] = ['This field cannot be null']
                    self.errors.update(error)
        if not self.is_allowed_truck_company():
            self.errors = {**(self.errors or {}), 'truck': [TRUCK_COMPANY_NOT_ALLOWED]}

    def convert_tonnage(self, save=False):
        existing_tonnage = self.tonnage
        if not self.commodity_id:
            return
        to_unit = self.commodity.unit
        from_unit = self.country.truck_unit
        if not get(
                self, 'do_not_convert_tonnage'
        ) and self._entered_tonnage and self.commodity_id and (
                to_unit and from_unit and to_unit not in [MT, BALES, MODULES, METER_CUBE, KG, LITRE]
        ):
            self.tonnage = self.commodity.convert_to(self._entered_tonnage, from_unit, to_unit, True)
            self.do_not_convert_tonnage = True
        if save and existing_tonnage != self.tonnage:
            self.save_only(update_fields=['tonnage'])

    @classmethod
    def build_and_clean(cls, site_id, data):
        category_id = False
        steer_point_5 = data.pop('steer_point_5', False)
        steer_1_point_1 = data.pop('steer_1_point_1', False)
        if 'category_id' in data:
            category_id = data.pop('category_id', None)
        slot = cls(**{**data, 'site_id': site_id})
        slot.clean()
        if category_id is not False:
            slot._category_id = category_id
            slot._steer_1_point_1 = steer_1_point_1
            slot._steer_point_5 = steer_point_5
        return slot

    def is_allowed_truck_company(self, truck_company_id=None):
        result = True
        truck_company_id = truck_company_id or get(self, 'truck.company_id')
        if truck_company_id and get(self, 'settings.allowed_truck_company_ids'):
            result = truck_company_id in self.settings.allowed_truck_company_ids
        return result

    @classmethod
    def is_allowed_truck(cls, slot_id, truck_id):
        result = True
        if slot_id and truck_id:
            slot = cls.objects.filter(id=slot_id).first()
            truck = Truck.objects.filter(id=truck_id).first()
            if slot and truck:
                result = slot.is_allowed_truck_company(truck.company_id)
        return result

    @classmethod
    @transaction.atomic
    def persist_many(cls, site_id, data, create_history=True, return_slots=False, request_origin='Web'):
        if not isinstance(data, list):
            data = [data]

        comment = None
        comments = list(filter(None, [d.pop('comment', None) for d in data]))
        if any(comments):
            comment = comments[0]
        chemical_applications = list(filter(None, [d.pop('chemical_applications', None) for d in data]))
        if any(chemical_applications):
            chemical_applications = chemical_applications[0]
        feature = get(list(filter(None, [d.pop('feature', None) for d in data])), '0')

        data = sorted(data, key=lambda d: d.get('start', ''))
        slots = cls.__build_slots(data, site_id)

        if slots:
            slots = cls.objects.bulk_create(slots, batch_size=200)
            cls.__comment_on(slots, comment)
            slots = sorted(slots, key=lambda s: s.start)
            sibling_ids = [slot.id for slot in slots]
            FreightSlot.objects.filter(id__in=sibling_ids).update(siblings=sibling_ids)

            should_attempt_movement_creation = slots[0].should_have_movement()  # multiple slots are identical
            if should_attempt_movement_creation:
                for slot in slots:
                    slot.__create_movement_from_slot_create(request_origin, feature)
                    slot.upsert_unsaved_truck_config()
                    time.sleep(1)  # so that each FM has different identifier
                    slot.refresh_from_db()
                    slot.update_chemical_applications_in_movement(chemical_applications)

            slots[0].publish_new_slots_to_ably()
            slots[0].notify_created(list(slots))

            if create_history:
                cls.history.bulk_history_create(slots)

            if return_slots:
                return slots

            return True

        return False

    @property
    def truck_config(self):
        if self.movement_id:
            from core.loads.models import LoadTruckConfiguration
            return LoadTruckConfiguration.find_by_movement_id(self.movement_id, self.type)
        if self.load:
            return self.load.truck_config

    @property
    def category(self):
        return get(self.truck_config, 'category')

    @property
    def category_id(self):
        return get(self.truck_config, 'category_id')

    @property
    def steer_point_5(self):
        return get(self.truck_config, 'steer_point_5')

    @property
    def steer_1_point_1(self):
        return get(self.truck_config, 'steer_1_point_1')

    @property
    def permit_number(self):
        return get(self.truck_config, 'permit_number')

    @property
    def declared_mass_limit(self):
        return get(self.truck_config, 'declared_mass_limit')

    @property
    def accreditation_number(self):
        return get(self.truck_config, 'accreditation_number')

    @property
    def load_sharing(self):
        return get(self.truck_config, 'load_sharing')

    @property
    def notice(self):
        return get(self.truck_config, 'notice')

    @property
    def notice_number(self):
        return get(self.truck_config, 'notice_number')

    @property
    def restricted(self):
        return get(self.truck_config, 'restricted')

    def upsert_truck_config(self, **kwargs):
        if self.movement_id:
            from core.loads.models import LoadTruckConfiguration
            load_type = kwargs.pop('load_type', None) or self.type
            config = LoadTruckConfiguration.upsert(movement_id=self.movement_id, load_type=load_type, **kwargs)
            load = get(config, 'load') or self.load
            if load:
                load.sync_mass_limit()
            return config

    def upsert_unsaved_truck_config(self):
        if not has(self, '_category_id'):
            return
        if self.movement_id and self.id:
            if self._category_id is None and not self.load:
                config = self.truck_config
                if config and not config.load_id:
                    config.delete()
            else:
                kwargs = {
                    'category_id': self._category_id,
                    'steer_1_point_1': bool(self._steer_1_point_1),
                    'steer_point_5': bool(self._steer_point_5),
                    'permit_number': get(self, '_permit_number'),
                    'declared_mass_limit': get(self, '_declared_mass_limit'),
                    'accreditation_number': get(self, '_accreditation_number'),
                    'load_sharing': get(self, '_load_sharing'),
                    'notice': get(self, '_notice'),
                    'notice_number': get(self, '_notice_number'),
                    'restricted': get(self, '_restricted'),
                    'load_type': self.type,
                }
                config = self.upsert_truck_config(**kwargs)
                del self._category_id
                del self._steer_1_point_1
                del self._steer_point_5
                if get(self, '_permit_number'):
                    del self._permit_number
                if get(self, '_declared_mass_limit'):
                    del self._declared_mass_limit
                if get(self, '_accreditation_number'):
                    del self._accreditation_number
                if get(self, '_load_sharing'):
                    del self._load_sharing
                if get(self, '_notice'):
                    del self._notice
                if get(self, '_notice_number'):
                    del self._notice_number
                if get(self, '_restricted'):
                    del self._restricted
                return config

    def __create_movement_from_slot_create(self, request_origin='Web', feature=None):  # pylint: disable=unused-private-member
        if self.should_have_movement():
            self.skip_history_when_saving = True  # for no history
            self.__create_movement(request_origin=request_origin, feature=feature)
            del self.skip_history_when_saving

    def __build_comment(self, comment):  # pylint: disable=unused-private-member
        return SlotComment(
            slot=self,
            comment=comment,
            created_by_id=self.created_by_id,
            updated_by_id=self.updated_by_id,
        )

    @classmethod
    def __comment_on(cls, slots, comment):
        if comment and slots:
            SlotComment.objects.bulk_create([slot.__build_comment(comment) for slot in slots])

    @classmethod
    def __build_slots(cls, data, site_id):
        slots = []

        for params in data:
            slot = cls.build_and_clean(site_id, params)
            if not slot.errors:
                slots.append(slot)

        return slots

    def related_driver_criteria(self):
        return ~models.Q(type_id=DRIVER_TYPE_ID) | models.Q(id=self.driver_id)

    @property
    def provider_employees(self):
        if self.freight_provider_id and self.sub_freight_provider_id:
            return self.freight_provider.employee_set.filter(self.related_driver_criteria()).union(
                self.sub_freight_provider.employee_set.filter(self.related_driver_criteria())
            )
        if self.freight_provider_id and not self.sub_freight_provider_id:
            return self.freight_provider.employee_set.filter(self.related_driver_criteria())
        from core.profiles.models import Employee
        return Employee.objects.none()

    @property
    def site_employees(self):
        return self.site.company.employee_set.filter(farms__id=self.site_id)

    @property
    def is_inload(self):
        return self.type == 'inload'

    @property
    def is_outload(self):
        return self.type == 'outload'

    def notify_commented_by_provider(self):
        recipients = self.site_employees
        if recipients and recipients.exists():
            self.__notify(
                NEW_COMMENT_BY_BOOKIE, 'Commented', 'site_management', recipients
            )

    def is_provider(self, user):
        return user and self.freight_provider_id == user.company_id

    def is_delivery_site(self):
        return self.site_id == get(self.order, 'freight_delivery.consignee.handler_id')

    def is_sub_provider(self, user):
        return user and self.sub_freight_provider_id == user.company_id

    def is_pickup_site_user(self, user):
        return user and get(self.movement, 'freight_pickup.consignor.handler.company_id') == user.company_id

    def is_delivery_site_user(self, user):
        return user and get(self.movement, 'freight_delivery.consignee.handler.company_id') == user.company_id

    def __send_webpush_notifications(self, recipients, action_key, end, app):
        if not Toggle.get('WEB_PUSH_NOTIFICATIONS_TOGGLE'):
            return

        if app == 'site_bookings':
            url = WEB_URL + '/#/site-bookings'
        else:
            url = WEB_URL + '/#/site-management'

        WebPush.send_many(recipients, **{
            'head': (self.site.name + " | " + PUSH_NOTIFICATION_DICTIONARY[action_key]['action']),
            'body': PUSH_NOTIFICATION_DICTIONARY[action_key]['web'],
            'url': url,
            'start': str(self.start),
            'end': str(end),
            'site_id': self.site_id,
            'company_id': self.site.company_id
        })

    def __send_mobile_push_notifications(self, recipients, message, start, end, interruption_level=None):
        if not recipients.exists():
            return

        tz = self.timezone
        user_tz_start_date = DateTimeUtil.localize_date(start, tz)
        user_tz_start_time = DateTimeUtil.localize_time(start, tz)
        user_tz_end_date = DateTimeUtil.localize_date(end, tz)
        user_tz_end_time = DateTimeUtil.localize_time(end, tz)
        message = message.replace('<b>', '').replace('</b>', '')
        message = message.replace('start_date', user_tz_start_date).replace('start_time', user_tz_start_time)
        message = message.replace('end_date', user_tz_end_date).replace('end_time', user_tz_end_time)

        args = {
            'notification_type': NOTIFICATION_TYPE_SLOT_ID,
            'message_txt': message,
            'ios_msg_title': 'Slot Notification',
            'extra': {
                'ac_notification_id': self.id, 'ac_notification_type': NOTIFICATION_TYPE_SLOT,
                'movement_id': self.movement_id
            }
        }
        MobilePushNotification.notify_mobile_devices(
            recipients.values_list('id', flat=True),
            args,
            interruption_level=interruption_level
        )

    def notify_created(self, slots):
        if self.has_freight_provider_id():
            from core.profiles.models import Employee
            recipients = Employee.objects.filter(id__in=self.provider_employees.values_list('id', flat=True)
                                                 ).exclude(type_id=DRIVER_TYPE_ID)
            if recipients.exists():
                if len(slots) == 1:
                    self.__notify_single_created(recipients)
                else:
                    self.__notify_recurring_created(recipients, slots[-1].end)

    def __is_updated_by_old_freight_provider(self, last_version):
        return last_version and self.updated_by.company_id in [
            last_version.get('freight_provider_id', None),
            last_version.get('sub_freight_provider_id', None)
        ]

    def __has_provider_changed(self, old):
        return old.has_freight_provider_id() and (
            (
                self.has_freight_provider_id() and old.freight_provider_id != self.freight_provider_id
            ) or not self.has_freight_provider_id()
        )

    def notify_updated(self, last_version):
        if self.is_updated_by_freight_provider() or self.__is_updated_by_old_freight_provider(
                last_version
        ):
            self.__notify_updated_by_bookie(last_version)
        else:
            self.__notify_updated_by_site_operator(last_version)

    def __notify_updated_by_bookie(self, last_version):
        from core.profiles.models import Employee
        recipients = Employee.objects.filter(id__in=[*self.site_employees.values_list('id', flat=True),
                                                     *self.provider_employees.values_list('id', flat=True)])

        old_slot = FreightSlot.__build_from_version(last_version)
        old_driver = old_slot.provider_employees
        if old_driver and old_slot.status != 'planned' and self.status == 'planned':
            recipients = Employee.objects.filter(id__in=[*old_driver.values_list('id', flat=True),
                                                         *recipients.values_list('id', flat=True)])
        if recipients.exists() and last_version:
            changeset = self._get_changeset_fields(last_version)
            if 'status' in changeset:
                if self.is_booked():
                    self.__notify(
                        BOOKED_SLOT, 'Booked', 'site_management', recipients,
                        interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
                    )
                else:
                    self.__notify(
                        CANCELLED_BOOKING_BY_BOOKIE, 'Cancelled', 'site_management', recipients,
                        interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
                    )
            elif changeset:
                self.__notify(
                    UPDATED_SLOT_BY_BOOKIE, 'Changed', 'site_management', recipients,
                    interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
                )

    @classmethod
    def __build_from_version(cls, version):
        blacklisted_attributes = (
            'entity', '_original_state', '_FreightSlot__external_connection_configurations', 'errors',
            'country_id', 'country_code', 'country'
        )
        return cls(**{k: v for k, v in version.items() if k not in blacklisted_attributes})

    def get_status_label(self):
        return self.site.company.sitemanagementsettings.get_status_label(self.status)

    def __notify_updated_by_site_operator(self, last_version):
        old_slot = FreightSlot.__build_from_version(last_version)
        recipients = old_slot.provider_employees
        if self.__has_provider_changed(old_slot) and recipients.exists():
            self.__notify(
                CANCELLED_BOOKING_BY_SITE_MANAGER, 'Cancelled',
                'site_bookings', recipients,
                interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
            )

        recipients = self.provider_employees
        if recipients.exists() and last_version:
            changeset = self._get_changeset_fields(last_version)
            if 'status' in changeset:
                if self.is_booked():
                    self.__notify(
                        BOOKED_SINGLE_SLOT_WITH_FP, 'Booked', 'site_bookings', recipients,
                        interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
                    )
                elif self.is_planned() and old_slot.is_booked():
                    self.__notify(
                        CANCELLED_BOOKING_BY_SITE_MANAGER, 'Cancelled', 'site_bookings', recipients,
                        interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
                    )
                else:
                    self.__notify(
                        CHANGED_STATUS, 'Changed', 'site_bookings', recipients,
                        None, {'status': self.get_status_label()},
                        interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
                    )
            elif self.__has_provider_changed(old_slot):
                self.__notify(
                    BOOKED_SINGLE_SLOT_WITH_FP, 'Booked', 'site_bookings', recipients,
                    interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
                )
            elif changeset and not self.is_planned():
                self.__notify(
                    UPDATED_SLOT_BY_SITE_MANAGER, 'Changed', 'site_bookings', recipients,
                    interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
                )

    def __notify_recurring_created(self, recipients, end):
        if self.is_booked():
            self.__notify(
                NEW_BOOKED_RECURRING_SLOTS_WITH_FP,
                'Booked',
                'site_bookings',
                recipients,
                end,
                interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
            )

    def __notify_single_created(self, recipients):
        if self.is_booked():
            self.__notify(
                BOOKED_SINGLE_SLOT_WITH_FP,
                'Booked',
                'site_bookings',
                recipients,
                interruption_level=INTERRUPTION_LEVEL_TIME_SENSITIVE
            )

    def __notify(
            self, action_key, verb, app,
            recipients, end=None,
            extra_description_args=None,
            interruption_level=None
    ):
        extra_description_args = extra_description_args or {}
        message = PUSH_NOTIFICATION_DICTIONARY[action_key]['app'].format(
            user=self.updated_by.name,
            company=self.updated_by.company.name,
            site_name=self.site.name,
            **extra_description_args
        )
        end = end or self.end
        start = self.start
        if start and not isinstance(start, str):
            start = str(start)
        if end and not isinstance(end, str):
            end = str(end)

        notify.send(
            sender=self.updated_by,
            verb=verb,
            description=message,
            app=app,
            entity='freight_slot',
            action_object=self,
            recipient=recipients,
            start=start,
            end=end,
            site_id=self.site_id,
            company_id=self.site.company_id,
        )
        self.__send_webpush_notifications(recipients, action_key, (end or self.end), app)
        self.__send_mobile_push_notifications(
            recipients,
            message,
            start,
            end,
            interruption_level=interruption_level
        )

    @classmethod
    def get_filter_params(cls, start, end, site_company_id=None, site_id=None, rego=None):
        filter_params = {}
        if not site_id and site_company_id:
            filter_params['site__company_id'] = site_company_id
        if site_id:
            filter_params['site_id'] = site_id
        if start and end:
            filter_params['start__gte'] = start
            filter_params['end__lt'] = end
        elif site_company_id:
            params = SiteManagementSettings.get_default_view_date_range(site_company_id) or {}
            filter_params.update(params)
        if rego:
            filter_params['truck__rego__iexact'] = rego

        return filter_params

    @classmethod
    def open_or_company_booked_slots(
            cls, company_id, start, end, site_company_id, site_id=None, rego=None, include_fleet=False,
            exclude_statuses=None
    ):
        filter_params = cls.get_filter_params(start, end, site_company_id, site_id)
        queryset = cls.objects.filter(**filter_params).select_related('site')
        if is_super_admin(company_id):
            return queryset

        if rego:
            criteria = models.Q(truck__rego__iexact=rego)
            if include_fleet and company_id:  # include fleet for FP only
                criteria |= models.Q(truck__rego=FLEET_REGO)
            queryset = queryset.filter(criteria)

        queryset = queryset.filter(
            models.Q(
                models.Q(
                    freight_provider_id__isnull=True, status='planned'
                ) | models.Q(
                    freight_provider_id=company_id
                ) | models.Q(
                    sub_freight_provider_id=company_id
                ) | models.Q(
                    status='restricted', restricted_visible_to_carrier=True
                ) | models.Q(
                    movement__viewer_company_ids__contains=[company_id]
                )
            )
        )
        if exclude_statuses:
            queryset = queryset.exclude(status__in=exclude_statuses)
        return queryset

    @classmethod
    def get_planned_slots(cls, site_id, start, end, load_type, freight_provider_id, **kwargs):
        filter_params = cls.get_filter_params(start=start, end=end, site_id=site_id)
        queryset = cls.objects.filter(**filter_params).select_related('site')
        commodity_ids = kwargs.pop('commodity_id', None)
        grade_ids = kwargs.pop('grade_id', None)
        if commodity_ids:
            if not isinstance(commodity_ids, list):
                commodity_ids = [commodity_ids]
            queryset = queryset.exclude(excluded_commodity_ids__contains=commodity_ids)
        if grade_ids:
            if not isinstance(grade_ids, list):
                grade_ids = [grade_ids]
            queryset = queryset.exclude(excluded_grade_ids__contains=grade_ids)
        queryset = queryset.filter(**kwargs).filter(models.Q(type=load_type) | models.Q(type__isnull=True))
        queryset = queryset.filter(status='planned')
        if freight_provider_id:
            queryset = queryset.filter(
                models.Q(freight_provider_id__isnull=True) | models.Q(freight_provider_id=freight_provider_id))
        else:
            queryset = queryset.filter(freight_provider_id__isnull=True)

        return queryset

    @classmethod
    def company_slots(cls, company_id, start=None, end=None, _=None, site_id=None, rego=None, include_fleet=False, exclude_statuses=None):  # pylint: disable=unused-argument, line-too-long
        filter_params = cls.get_filter_params(start=start, end=end, site_company_id=company_id,
                                              site_id=site_id, rego=rego)
        slots = cls.objects.select_related('site').filter(**filter_params)
        if exclude_statuses:
            slots = slots.exclude(status__in=exclude_statuses)
        return slots

    @classmethod
    def create(cls, params, kwargs=None, pre_validate_callback=None):
        slot = super().create(params, kwargs, pre_validate_callback)
        if slot.persisted:
            slot.add_slot_booking_company_to_site_owner_company()
            slot.publish_new_slots_to_ably()

        return slot

    def is_updated_by_freight_provider(self):
        return self.updated_by.company_id in [
            self.freight_provider_id, self.sub_freight_provider_id
        ]

    def _get_changeset_fields(self, version):  # version is dict
        return list(get_changeset(self.to_dict(), version).keys())

    @cached_property
    def __external_connection_configurations(self):
        return self.site._external_connection_configurations

    def __has_sftp_connection_configuration(self):
        return bool(self.__external_connection_configurations)

    def __should_push_to_sftp(self, previous_version):
        """
        1. This is specifically written for Mauri/GWF (may apply to all with similar pattern)
        2. This checks for bookings/cancellations/updates in essentials
        """
        return self.__has_sftp_connection_configuration() and (
                       self.__is_just_booked(previous_version) or \
                       self.__is_just_moved_to_planned(previous_version) or \
                       (
                               self.is_booked() and \
                               self.__is_any_field_updated(previous_version, ['driver_id', 'truck_id', 'tonnage'])
                       )
               )

    def __is_just_booked(self, previous_version):
        return previous_version['status'] == 'planned' and self.is_booked()

    def __is_just_completed(self, previous_version):
        return previous_version['status'] != 'completed' and self.is_completed()

    def __is_just_completed_from_planned(self, previous_version):
        return previous_version['status'] == 'planned' and self.is_completed()

    def __is_just_marked_incompleted(self, previous_version):
        return previous_version['status'] != 'cancelled' and self.is_cancelled()

    def __is_just_marked_rejected(self, previous_version):
        return previous_version['status'] != 'rejected' and self.is_rejected()

    def __is_just_marked_in_progress_from_planned(self, previous_version):
        return previous_version['status'] == 'planned' and self.is_in_progress()

    def __has_cancelled_booking(self, previous_version):
        return self.movement_id and self.__is_just_moved_to_planned(previous_version)

    def __is_just_moved_to_planned(self, previous_version):
        return self.is_planned() and previous_version['status'] != 'planned'

    def __is_reverted_to_booked(self, previous_version):
        return previous_version['status'] in REVERT_TO_BOOKED_STATUSES and self.is_booked()

    @property
    def checkpoint(self):
        return get(self, 'movement.freight_pickup') if self.is_outload else get(self, 'movement.freight_delivery')

    def __create_movement(self, quick=False, identifier=None, old_slot_movement_id=None, request_origin='Web',
                          feature='Slot'):
        from core.freights.models import FreightContract
        if old_slot_movement_id:
            self.movement_id = old_slot_movement_id
            self.on_route = self.movement.is_in_progress()
            self.checkpoint.date_time = self.start
            self.checkpoint.save()
            self.save()
            if not quick:
                self.__transition_movement_to_next_status(request_origin=request_origin, feature=feature)
        elif self.has_order_id():
            movement = (FreightContract.quick_create_from_slot(self, identifier=identifier, request_origin=request_origin, feature=feature) if quick else #pylint: disable=line-too-long
                        FreightContract.create_from_slot(self, identifier=identifier, request_origin=request_origin,
                                                         feature=feature))
            if movement.persisted:
                self.movement_id = movement.id
                if not self.customer and self.movement.customer_id:
                    self.customer = self.movement.customer.company.name
                self.save()
                if not quick:
                    self.__transition_movement_to_next_status(request_origin=request_origin, feature=feature)
                return True
            else:
                ERRBIT_LOGGER.raise_errbit(
                    f'Unable to create movement for slot {self.id} with errors {get(movement, "errors", None)}')

    def __void_movement(self, previous_version):
        cancellation_reason = None
        if self.__is_just_marked_incompleted(
                previous_version
        ) or self.__is_just_marked_rejected(previous_version):
            reason = self.slotcomment_set.last()
            if reason:
                cancellation_reason = reason.comment
        from core.freights.models import FreightContractAcceptanceRequest
        if self.has_movement_id():
            request = FreightContractAcceptanceRequest(
                type='void', contract_id=self.movement_id, created_by=self.updated_by,
                updated_by=self.updated_by,
                request_reason=self.cancellation_reason or cancellation_reason,
                resolved=True, accepted=True
            )
            request.save()
            if self.movement_id and not self.movement.is_void():
                if self.__is_just_marked_rejected(previous_version) and self.is_inload:
                    self.movement.rejection_reason = cancellation_reason
                    self.movement.load_rejected(user=self.updated_by)
                    self.movement.rejected_load_details = {'reasons': [], 'attachments': []}
                else:
                    self.movement.void()
                self.movement.save()
                slot_ids = list(self.movement.freightslot_set.all().values_list('id', flat=True))
                slot_ids.remove(self.id) if self.id in slot_ids else None
                self.movement.revert_booked_slots(slot_ids)
            if self.__has_cancelled_booking(previous_version):
                self.movement_id = None

    def create_load(self, _type, checkpoint, request_origin='Web', feature='Slot'):
        if self.has_movement_id() and self.has_order_id():
            load = self.just_create_load(_type, checkpoint, request_origin=request_origin, feature=feature)
            if load.id:
                load.send_load_done_sms()
                from core.jobs.models import Job
                Job.schedule_job_for_task('send_alerts_for_loads', [load.id])

    def just_create_load(self, _type, checkpoint, request_origin='Web', feature='Slot'): # pylint: disable=too-many-locals
        from core.loads.models import Load
        gate = get(self, 'site.gate')
        loads_data = []
        is_blended_movement = get(self.movement, 'is_blended')
        throughput = ((_type == INLOAD and get(self.movement, 'order.inload_throughput_fee')) or
        (_type == OUTLOAD and get(self.movement, 'order.outload_throughput_fee')))
        if is_blended_movement:
            spread_details = get(self.movement, 'order.spread.details')
            for spread in spread_details:
                tonnage = round(get(spread, 'percentage') * 0.01 * self.tonnage, 2)
                loads_data.append({
                    'grade_id': get(spread, 'grade_id'),
                    'tonnage': tonnage,
                    'entered_tonnage': tonnage,
                    'throughput': throughput
                })
        else:
            loads_data.append({
                'grade_id': self.movement.planned_grade_id,
                'tonnage': self.tonnage,
                'entered_tonnage': self._entered_tonnage,
                'throughput': throughput
            })
        loads_created = []
        movement_chemical_applications = self.movement.chemical_applications.filter(is_active=True)
        for load_data in loads_data:
            load = Load(
                type=_type,
                checkpoint=checkpoint,
                date_time=self.end,
                freight_provider_id=self.sub_freight_provider_id or self.freight_provider_id,
                truck_id=self.truck_id,
                driver_id=self.driver_id,
                commodity_id=self.movement.commodity_id,
                variety_id=self.movement.variety_id,
                grade_id=get(load_data, 'grade_id'),
                storage_id=get(gate, 'id'),
                estimated_net_weight=get(load_data, 'tonnage'),
                _entered_estimated_net_weight=get(load_data, 'entered_tonnage'),
                season=self.movement.season,
                ngr_id=self.movement.get_ngr_id_for_load(_type, gate),
                status='delivered',
                created_by_id=self.updated_by_id,
                updated_by_id=self.updated_by_id,
                movement_id=self.movement_id,
                throughput=get(load_data, 'throughput') or False
            )
            load.do_not_convert_weight = True
            load.save()
            loads_created.append(load)
            if self.movement_id:
                self.movement.audit(action='add_outload' if _type == 'outload' else 'add_inload',
                                    user=self.updated_by, request_origin=request_origin, feature=feature)
            if is_blended_movement:
                chemical_application = movement_chemical_applications.filter(
                    grade_id=get(load_data, 'grade_id')
                ).first()
                if chemical_application:
                    load.create_chemical_load({
                        'commodity_id': chemical_application.commodity_id,
                        'storage_id': get(gate, 'id'),
                        'application_fee': chemical_application.application_fee
                    }, self.created_by)
        return get(loads_created, '0')

    def create_outload(self, request_origin='Web', feature='Slot'):
        self.create_load('outload', get(self, 'movement.freight_pickup'),
                         request_origin=request_origin,feature=feature)

    def create_inload(self, request_origin='Web', feature='Slot'):
        self.create_load('inload', get(self, 'movement.freight_delivery'),
                         request_origin=request_origin, feature=feature
                         )

    def __transition_movement_to_next_status(self, request_origin='Web', feature='Slot'):
        if self.has_movement_id() and self.is_completed():
            movement = self.movement
            if self.is_outload:
                if not movement.active_outloads.exists():
                    self.create_outload(request_origin=request_origin, feature=feature)
                if self.movement.is_transitionable_to_in_progress_from_slot():
                    self.movement.in_progress({})
                    self.movement.save()
            if self.is_inload:
                if not movement.active_inloads.exists():
                    self.create_inload(request_origin=request_origin, feature=feature)
                if self.movement.is_transitionable_to_delivered_from_slot():
                    self.movement.delivered({})
                    self.movement.save()

    def __should_update_movement_tonnage(self, previous_version):
        return self.__should_update_movement_attr(previous_version, 'tonnage')

    def __should_update_movement_time(self, previous_version):
        return self.__should_update_movement_attr(previous_version, 'start')

    def __should_update_movement_booking_number(self, previous_version):
        return self.__should_update_movement_attr(previous_version, 'booking_number')

    def __should_update_movement_planned_truck(self, previous_version):
        return self.__should_update_movement_attr(previous_version, 'truck_id')

    def __should_update_movement_provider_contact(self, previous_version):
        return self.__should_update_movement_attr(previous_version, 'driver_id') and get(
            self, 'movement.provider_id'
        )

    def __should_update_movement_attr(self, previous_version, attr):
        attr_value = get(self, attr, None)
        return self.movement_id and attr_value and attr_value != get(previous_version, attr)

    def __should_update_movement_order(self, previous_version):
        return self.__is_any_field_updated(previous_version, ['order_id'])

    def __revert_movement_to_booked(self, feature):
        if self.has_movement_id() and self.has_order_id() and not self.parent_slot_id and self.load:
            self.load.void('Slot reverted to booked', self.updated_by, False, feature=feature)

    def update_movement_tonnage(self):
        if self.has_movement_id():
            self.sync_load_tonnage()
            if not self.is_completed():
                self.movement.planned_tonnage = self.inferred_tonnage

    def sync_load_tonnage(self):
        if get(self.movement, 'is_blended'):
            self.update_all_loads_tonnage()
        else:
            if self.loads.count() == 1:
                self.__update_load_tonnage(self.load)

    def update_all_loads_tonnage(self):
        grade_tonnage_mapping = {}
        spread_details = get(self.movement, 'order.spread.details')
        for spread in spread_details:
            tonnage = round(get(spread, 'percentage') * 0.01 * self.tonnage, 2)
            grade_tonnage_mapping.update({get(spread, 'grade_id'): tonnage})
        for load in self.loads:
            load.estimated_net_weight = get(grade_tonnage_mapping, load.grade_id)
            load._entered_estimated_net_weight = get(grade_tonnage_mapping, load.grade_id)
            load.gross_weight = None
            load.tare_weight = None
            load.save()
            for chemical_load in load.valid_chemical_loads:
                chemical_load.estimated_net_weight = (load.estimated_net_weight * get(
                    chemical_load.chemical_applications.first(), 'application_fee'
                ))
                chemical_load.save()

    def __update_load_tonnage(self, load):
        tonnage = self.inferred_tonnage
        if load and load.net_weight != tonnage:
            load.estimated_net_weight = self.tonnage
            load._entered_estimated_net_weight = self._entered_tonnage
            load.gross_weight = None
            load.tare_weight = None
            load.save()

    def remove_from_trailer_slots(self):
        for slot in FreightSlot.objects.filter(trailer_slot_ids__contains=[self.id]):
            slot.trailer_slot_ids.remove(self.id)
            slot.save()

    def cancel_booking_for_trailer_slot(self):
        self.revert_to_last_planned()
        self.parent_slot_id = None
        self.is_trailer_slot = False
        self.movement_id = None
        self.customer = None
        self.order_id = None
        self.commodity_id = None
        self.grade_id = None
        self.freight_provider_id = None
        self.sub_freight_provider_id = None
        self.truck_id = None
        self.driver_id = None
        self.tonnage = None
        self._entered_tonnage = None
        self.save()
        self.remove_from_trailer_slots()

    def revert_to_last_planned(self):
        history = self.last_planned_history
        if history:
            for field in self._meta.fields:
                if field.name not in [
                    'updated_by', 'created_by', 'created_at', 'updated_at', '_last_pushed_at',
                    'extras', 'parent_slot_id', 'trailer_slot_ids', 'is_trailer_slot'
                ]:
                    setattr(self, field.name, get(history, field.name))

    def __update_movement(self, previous_version, request_origin='Web', feature='Slot'):  # pylint: disable=too-many-branches,too-many-statements,unused-private-member,too-many-locals
        has_cancelled_booking = self.__has_cancelled_booking(previous_version)
        if has_cancelled_booking:
            if get(self.extras, 'fleet_reminder_sent_at'):
                self.extras['fleet_reminder_sent_at'] = None

            self.archive_comments()
            if self.is_trailer_slot:
                self.cancel_booking_for_trailer_slot()
                return
            else:
                self.trailer_slot_ids = []
                self.cancel_trailer_slots()

        if self.has_movement_id():
            checkpoint = self.checkpoint
            save_checkpoint = False
            movement = None
            order = None
            exclusion_ids = [self.id, self.parent_slot_id] if self.parent_slot_id else [self.id]
            counter_slot = self.movement.freightslot_set.exclude(id__in=exclusion_ids).exclude(
                is_trailer_slot=True
            ).first()
            update_counter_slot = bool(counter_slot)
            parent_slot = FreightSlot.objects.filter(id=self.parent_slot_id).first()
            if has_cancelled_booking:
                movement = self.movement
                order = self.order
                movement.audit(action='void_slot_cancel', user=self.updated_by, request_origin=request_origin,
                               feature=feature)
            if has_cancelled_booking or self.__is_just_marked_incompleted(
                    previous_version
            ) or self.__is_just_marked_rejected(
                previous_version
            ):
                self.__void_movement(previous_version)
                update_counter_slot = False
                if has_cancelled_booking:
                    self.order_id = None
                    self.revert_to_last_planned()
                if counter_slot:
                    if has_cancelled_booking:
                        counter_slot.order_id = None
                        counter_slot.movement_id = None
                        counter_slot.trailer_slot_ids = []
                        counter_slot.status = 'planned'
                        counter_slot.revert_to_last_planned()
                    counter_slot.cancel_trailer_slots()
                    counter_slot.save()
            if self.__is_reverted_to_booked(previous_version):
                self.__revert_movement_to_booked(feature)
                self.movement.refresh_from_db() if self.movement else None
            if self.__should_update_movement_tonnage(previous_version):
                self.update_movement_tonnage()
                if counter_slot and not counter_slot.is_completed():
                    counter_slot.tonnage = self.tonnage
                    counter_slot._entered_tonnage = self._entered_tonnage
            if self.__is_just_completed(previous_version):
                self.__transition_movement_to_next_status(request_origin=request_origin, feature=feature)
            if not self.is_completed() and self.__should_update_movement_planned_truck(previous_version):
                self.movement.planned_truck_id = self.truck_id
                if counter_slot and not counter_slot.is_completed():
                    counter_slot.truck_id = self.truck_id
                if parent_slot:
                    parent_slot.truck_id = self.truck_id
            if self.__should_update_movement_provider_contact(previous_version):
                self.movement.assign_to_id = self.driver_id
                if counter_slot and not counter_slot.is_completed():
                    counter_slot.driver_id = self.driver_id
                if parent_slot:
                    parent_slot.driver_id = self.driver_id
            if self.__should_update_movement_time(previous_version):
                if checkpoint:
                    checkpoint.date_time = self.start
                    save_checkpoint = True
            if checkpoint and self.__should_update_movement_booking_number(previous_version):
                checkpoint.booking_number = self.booking_number
                checkpoint.loads_set.update(booking_number=self.booking_number, do_not_update_stock=True)
                save_checkpoint = True

            if not has_cancelled_booking and self.__should_update_movement_order(previous_version):
                # Two steps happening in one, order change -> void prev movement and create new movement/booking
                self.__void_movement(previous_version)
                update_counter_slot = False
                self.movement_id = None
                old_order_id = self.order_id
                self.order_id = None
                self.status = 'planned'
                FreightSlot.unpause_history()
                self.unpause_history()
                self.save()
                self.queue_sftp_push(previous_version)
                FreightSlot.pause_history()
                self.pause_history()
                self.refresh_from_db()
                self.order_id = old_order_id
                self.__create_movement()
                self.status = 'booked'
            if self.has_movement_id():
                self.movement.save()
                if self.movement.order_id:
                    self.movement.order.recalculate_status()
            if save_checkpoint and checkpoint:
                checkpoint.save()
            if update_counter_slot:
                if counter_slot.is_inload:
                    counter_slot.on_route = self.movement.is_in_progress()
                counter_slot.save()
                FreightSlot.objects.filter(id__in=counter_slot.trailer_slot_ids).update(
                    tonnage=self.tonnage, _entered_tonnage=self._entered_tonnage,
                    truck_id=self.truck_id, driver_id=self.driver_id
                )
            if parent_slot:
                parent_slot.save()
                FreightSlot.objects.filter(id__in=parent_slot.trailer_slot_ids).exclude(id=self.id).update(
                    truck_id=self.truck_id, driver_id=self.driver_id
                )
            if self.is_inload and self.movement_id and counter_slot:
                self.on_route = self.movement.is_in_progress()
            self.save()
            if not self.errors:
                params = []
                if has_cancelled_booking and movement and order:
                    params.append({
                        'action': 'Cancel',
                        'outload_slot_ids': [self.id],
                        'inload_slot_ids': [counter_slot.id] if counter_slot else None,
                        'movement_id': [movement.id],
                        'order_id': order.id
                    })
                else:
                    updated_fields = self._get_fields_updated(previous_version)
                    if updated_fields:
                        is_status_changed = next((field for field in updated_fields
                                                  if field['field'].lower() == 'status'), False)
                        params.append({
                            'action': 'Update',
                            'outload_slot_ids': [self.id],
                            'inload_slot_ids': [counter_slot.id] if (counter_slot and not is_status_changed) else None,
                            'updated_fields': updated_fields
                        })
                        if any(get(field, 'field') in ['Tonnage', 'Truck'] for field in updated_fields):
                            self.movement.audit(action='edit_outload' if self.is_outload else 'edit_inload',
                                                user=self.updated_by, request_origin=request_origin,
                                                feature=feature)
                            self.movement.audit(action='amend', user=self.updated_by, request_origin=request_origin,
                                                feature=feature)
                    if self.__has_time_updated(previous_version):
                        params.append({
                            'action': 'Update',
                            'outload_slot_ids': [self.id],
                            'updated_fields': self._get_time_updated(previous_version)
                        })
                        self.movement.audit(action='edit_outload' if self.is_outload else 'edit_inload',
                                            user=self.updated_by, request_origin=request_origin,
                                            feature=feature)
                        self.movement.audit(action='amend', user=self.updated_by, request_origin=request_origin,
                                            feature=feature)
                return params

    def archive_comments(self):
        self.slotcomment_set.update(archived=True)

    def __should_unmark_urgent_for_provider(self, previous_version):
        return self.is_urgent_for_provider and (
            self.is_planned() or (self.has_truck_id() and not get(self, 'truck.is_fleet')) and
            not (self.__has_time_updated(previous_version) and self.is_booked())
        )

    def __should_unmark_urgent_for_manager(self):
        return self.is_urgent_for_manager and (
            self.is_planned() or not get(self, 'truck.is_fleet') or not self.__is_start_time_within_next_6_hours()
        )

    def __is_start_time_within_next_6_hours(self):
        now = timezone.now()
        urgent_hours_from_now = now + timedelta(hours=MARK_SLOT_URGENT_HOURS_FROM_START)
        return self.start and now <= self.start <= urgent_hours_from_now

    def __should_mark_urgent_for_provider(self, previous_version):
        return (not self.is_urgent_for_provider and (self.is_delayed() or
            (self.__has_time_updated(previous_version) and self.is_booked()) or
            (self.__is_start_time_within_next_6_hours() and get(self, 'truck.is_fleet')))
            and self.has_freight_provider_id() and not self.is_planned())

    def __should_mark_urgent_for_manager(self):
        return not self.is_urgent_for_manager and get(
            self, 'truck.is_fleet'
        ) and self.__is_start_time_within_next_6_hours() and not self.is_planned()

    def __toggle_urgent_for_provider(self, previous_version):
        result = self.is_urgent_for_provider

        if self.is_urgent_for_provider:
            if self.__should_unmark_urgent_for_provider(previous_version):
                result = False
        else:
            result = bool(self.__should_mark_urgent_for_provider(previous_version))

        self.is_urgent_for_provider = result

    def __toggle_urgent_for_manager(self):
        result = self.is_urgent_for_manager

        if self.is_urgent_for_manager:
            if self.__should_unmark_urgent_for_manager():
                result = False
        else:
            result = bool(self.__should_mark_urgent_for_manager())

        self.is_urgent_for_manager = result

    def __should_create_movement_from_update(self, previous_version):  # pylint: disable=unused-private-member
        return self.__is_just_booked(
            previous_version
        ) or self.__is_just_completed_from_planned(
            previous_version
        ) or self.__is_just_marked_in_progress_from_planned(
            previous_version
        )

    def should_have_movement(self):
        return self.has_order_id() and self.status in ['booked', 'in_progress', 'completed']

    def is_slot_or_site_old_version(self, user, updated_at, settings_updated_at):
        is_superuser = user.is_superuser
        if not is_superuser and self.is_old_version(updated_at):
            return False, {'version': SLOT_OLD_VERSION}
        if not is_superuser and self.is_setting_updated_since(settings_updated_at):
            return False, {'version': SLOT_SETTING_OLD_VERSION}

    @classmethod
    def update_from_view(cls, user, data, slot_id=None, site=None, quick=True, old_slot_movement_id=None,  # pylint: disable=too-many-locals
                         is_cancel_request=False, is_booking=False, request_origin='Web'):
        from core.jobs.models import Job
        feature = data.pop('feature', 'Slot')
        payload = data.copy()
        if '_entered_tonnage' not in payload:
            payload['_entered_tonnage'] = payload.get('tonnage')
        slot_id = slot_id or payload.get('id')
        if not slot_id:
            return False, None
        slot = FreightSlot.objects.filter(id=slot_id).first()
        if not slot:
            return False, None
        existing_driver_id = slot.driver_id

        if payload.get('status') in ['rejected', 'cancelled'] and slot.movement:
            if reasons := slot.movement.cannot_raise_slot_cancel_request_reasons(user):
                return False, reasons

        site = site or slot.site
        updated_at = payload.pop('updated_at', 0)
        settings_updated_at = payload.pop('settings_updated_at', 0)
        trailer_slots = payload.pop('trailer_slots', None)
        counter_trailer_slots = payload.pop('counter_trailer_slots', None)
        if result := slot.is_slot_or_site_old_version(user, updated_at, settings_updated_at):
            return result

        payload['tonnage'] = payload.get('tonnage', slot.tonnage)
        payload['_entered_tonnage'] = payload.get('_entered_tonnage', slot._entered_tonnage)
        payload['site_id'] = site.id
        update_fn = cls.quick_update if quick else cls.update
        updated_slot = update_fn(instance=slot, data=payload, trailer_slots=trailer_slots,
                                 old_slot_movement_id=old_slot_movement_id, request_origin=request_origin,
                                 feature=feature, request_user=user)
        if not updated_slot.errors and counter_trailer_slots:
            counter_slot = updated_slot.counter_slot
            if counter_slot:
                counter_slot.add_trailer_slots(counter_trailer_slots)
        if updated_slot.driver_id != existing_driver_id and updated_slot.status == 'completed' and updated_slot.load:
            load = updated_slot.load
            if get(load, 'device_source.mobile'):
                load.send_load_done_sms()
        success = not bool(updated_slot.errors)
        if success and is_cancel_request and (movement := get(updated_slot, 'movement')):
            movement.send_notifications(REQUEST_TYPE_VOIDED, user)
            movement.audit('void', user, feature=feature, request_origin='Web')
        if success and is_booking:
            Job.schedule_job_for_task(
                'send_slot_update_alerts', params={'action': 'Book', 'outload_slot_ids': [updated_slot.id]})

        return success, updated_slot

    def _check_update_data_validations(self, last_version, data):
        current_movement_id = last_version.get('movement_id', None)
        new_movement_id = data.get('movement_id', None)
        current_type = last_version.get('type', None)
        new_type = data.get('type', None)
        if current_movement_id and new_movement_id and current_movement_id != new_movement_id:
            self.errors = {'movement_id': 'Movement cannot be updated. Please re-book.'}
        if current_movement_id and new_type and new_type != current_type:
            self.errors = {'type': 'Slot type cannot be updated. Please re-book.'}
        if last_version['status'] == 'planned' and data.get(
                'status'
        ) in ['booked', 'completed', 'in_progress'] and not data.get('order_id') and not self.order_id and get(
            self.settings, 'order_booking'
        ):
            self.errors = {'order_id': 'Slot cannot be booked without order.'}

    def verify_trailer_slots_movement(self, trailer_slots):
        # run this check before assigning/booking trailer slots to parent slot
        if trailer_slots and isinstance(trailer_slots, dict):
            trailer_slot_ids = compact(flatten(trailer_slots.values()))
            trailer_queryset = FreightSlot.objects.filter(id__in=trailer_slot_ids)
            trailers_with_movement = trailer_queryset.filter(movement_id__isnull=False)
            if not self.movement_id and trailers_with_movement.exists():
                return {'trailer': 'Trailer slot with a different movement exists'}
            if self.movement_id and trailers_with_movement.exclude(movement_id=self.movement_id).exists():
                return {'trailer': 'Trailer slot with a different movement exists'}

    @classmethod
    def update(cls, instance_id=None, data=None, update_fields=None, instance=None, trailer_slots=None,
            old_slot_movement_id=None, request_origin='Web', feature='Slot', request_user=None
        ):  # pylint: disable=arguments-differ
        cls.pause_history()
        if instance:
            instance.pause_history()
        last_version = instance.to_dict().copy()
        chemical_applications = data.pop('chemical_applications', None)
        slot = cls.quick_update(
            instance_id, data, update_fields, instance, trailer_slots, False, old_slot_movement_id,
            request_origin, feature, request_user=request_user
        )
        if slot.errors and slot.movement_id and not last_version.get(
                'movement_id') and slot.__should_create_movement_from_update(last_version):
            try:
                slot.movement.delete()
            except Exception as ex:  # pylint: disable=broad-except
                slot.errors = {'exception': str(ex)}
        else:
            slot.refresh_from_db()
            slot.post_quick_update(data, last_version, trailer_slots, old_slot_movement_id, chemical_applications,
                                   request_origin, feature)
        cls.unpause_history()
        if instance:
            instance.unpause_history()

        return slot

    def get_company_with_external_booking_enabled(self):
        site_company = get(self.site, 'company')
        if get(site_company, 'external_booking_connection'):
            return site_company

    def should_movement_be_created_from_update(self, new_status):
        return self.status == 'planned' and new_status in ['booked', 'completed', 'in_progress']

    @classmethod
    def check_for_external_booking(cls, instance, data):
        truck_config = data.pop('truck_config', None)
        if get(data, 'status') == 'cancelled' and instance.movement_id and get(instance, 'movement.status') != 'void':
            company = instance.get_company_with_external_booking_enabled()
            if company:
                response = company.expire_external_booking({
                    'freight_movement_identifier': instance.movement.identifier,
                })
                return response
        elif get(data, 'status') == 'planned' and instance.status != 'planned' and instance.movement_id:
            company = instance.get_company_with_external_booking_enabled()
            if company:
                response = company.cancel_external_booking({
                    'freight_movement_identifier': instance.movement.identifier,
                    'comments': 'Cancelling Booking'  # Hardcoded: we don't have reason field for cancelling booking
                })
                return response
        elif get(data, 'order_id') and get(data, 'status') != 'cancelled':
            company = instance.get_company_with_external_booking_enabled()
            if company:
                if instance.movement_id:
                    response = company.update_slot_information({
                        'freight_movement_identifier': instance.movement.identifier,
                        'tonnage': get(data, 'tonnage'),
                        '_entered_tonnage': get(data, '_entered_tonnage'),
                        'provider_id': get(data, 'freight_provider_id') or instance.freight_provider_id,
                        'truck_id': get(data, 'truck_id'),
                        'driver_id': get(data, 'driver_id'),
                        'truck_config': truck_config,
                        'slot_id': instance.id,
                        'start': get(data, 'start'),
                        'end': get(data, 'end'),
                    })
                    return response
                else:
                    will_movement_be_created = instance.should_movement_be_created_from_update(get(data, 'status'))
                    if will_movement_be_created:
                        slot_type = get(data, 'type')
                        response = company.send_slot_information({
                            f'{slot_type}_slot_id': instance.id,
                            'tonnage': get(data, 'tonnage'),
                            '_entered_tonnage': get(data, '_entered_tonnage'),
                            'order_id': get(data, 'order_id'),
                            'truck_id': get(data, 'truck_id'),
                            'driver_id': get(data, 'driver_id'),
                            'provider_id': get(data, 'freight_provider_id'),
                            'truck_config': truck_config,
                        })
                        return response

    @classmethod
    def quick_update(   # pylint: disable=too-many-locals, too-many-branches, too-many-statements
            cls, instance_id=None, data=None, update_fields=None, instance=None, trailer_slots=None,
            _async=True, old_slot_movement_id=None, request_origin='Web', feature='Slot',
            request_user=None
    ):
        errors = instance.verify_trailer_slots_movement(trailer_slots)
        if errors:
            instance.errors = errors
            return instance

        from core.jobs.models import Job
        last_version = instance.to_dict().copy()

        instance._check_update_data_validations(last_version, data)
        if get(instance, 'errors'):
            return instance

        instance.pause_history()
        comment = None
        if data:
            comment = data.pop('comment', None)
        new_status = data.get('status')
        previous_status = last_version.get('status')
        if new_status and previous_status != new_status:
            data['on_route'] = False

        category_id = False
        steer_point_5 = data.pop('steer_point_5', False)
        steer_1_point_1 = data.pop('steer_1_point_1', False)
        if 'category_id' in data:
            category_id = data.pop('category_id', None)
        # load_sharing is absent is not implemented in mobile -> negating the value passed form mobile payload
        if request_origin == 'Mobile' and 'load_sharing' in (get(data, 'truck_config', {}) or {}):
            data['truck_config']['load_sharing'] = not data['truck_config']['load_sharing']
        response = cls.check_for_external_booking(instance, data)
        movement_identifier = None
        continue_slot_update = True
        if get(response, 'booking_number') and not instance.movement_id:
            movement_identifier = get(response, 'booking_number')
        elif get(response, 'valid_connection') and get(response, 'errors'):
            instance.errors = get(response, 'errors')
            continue_slot_update = False
        elif get(response, 'valid_connection') and not get(response, 'booking_number'):
            instance.errors = ['Booking operation failed. Please try again later.']
            continue_slot_update = False
        if not continue_slot_update:
            instance.unpause_history()
            return instance
        if get(data, 'order_id') and get(data, 'status') == 'booked' and last_version['status'] == 'planned':
            from core.freights.models import FreightOrder
            order = FreightOrder.get_valid_order(get(data, 'order_id'))
            data['order_id'] = order.id if order else get(data, 'order_id')
            if get(order, 'provider_id') and get(data, 'truck_id'):
                truck = Truck.objects.filter(id=get(data, 'truck_id')).first()
                if (truck and not get(data, "sub_freight_provider_id") and
                        get(data, "freight_provider_id") != get(truck, 'company_id')):
                    data["freight_provider_id"] = get(order, 'provider_id')
                    data["sub_freight_provider_id"] = get(truck, 'company_id')

        if get(instance, 'movement_id') and get(data, 'status') == 'planned' and last_version['status'] != 'planned':
            data.pop('order_id', None)
        amend_order_to_tonnage = data.pop('amend_order_to_tonnage', None)
        warehouse_over_delivered = data.pop('warehouse_over_delivered', None)
        chemical_applications = data.pop('chemical_applications', None)
        slot = super().update(instance_id, data, update_fields, instance)
        if amend_order_to_tonnage and slot.is_completed() and slot.movement_id:
            commodity = get(slot, 'commodity')
            to_unit = get(commodity, 'unit')
            from_unit = get(slot, 'country.truck_unit')
            if to_unit and from_unit and to_unit not in [MT, BALES, MODULES, METER_CUBE, KG, LITRE]:
                amend_order_to_tonnage = commodity.convert_to(amend_order_to_tonnage, from_unit, to_unit, True)
            slot.movement.adjust_over_delivered(
                amend_order_to_tonnage, slot.type, slot.updated_by, None, warehouse_over_delivered)
        slot.convert_tonnage(True)
        if get(data, 'booking_number') and instance.trailer_slot_ids:
            FreightSlot.objects.filter(id__in=instance.trailer_slot_ids).update(
                booking_number=get(data, 'booking_number'))
        is_movement_created = False
        should_create_movement_from_update = old_slot_movement_id or slot.__should_create_movement_from_update(
            last_version)
        if not slot.errors:
            if category_id is not False:
                slot._category_id = category_id
                slot._steer_1_point_1 = steer_1_point_1
                slot._steer_point_5 = steer_point_5
                slot.upsert_unsaved_truck_config()
            if comment:
                SlotComment(
                    slot=slot,
                    comment=comment,
                    created_by_id=slot.updated_by_id,
                    updated_by_id=slot.updated_by_id
                ).save()
            if should_create_movement_from_update:
                is_movement_created = slot.__create_movement(True, movement_identifier, old_slot_movement_id,
                                                             request_origin, feature)
                if amend_order_to_tonnage and is_movement_created and slot.is_completed() and slot.movement_id:
                    commodity = get(slot, 'commodity')
                    to_unit = get(commodity, 'unit')
                    from_unit = get(slot, 'country.truck_unit')
                    if to_unit and from_unit and to_unit not in [MT, BALES, MODULES, METER_CUBE, KG, LITRE]:
                        amend_order_to_tonnage = commodity.convert_to(amend_order_to_tonnage, from_unit, to_unit, True)
                    slot.movement.adjust_over_delivered(
                        amend_order_to_tonnage, slot.type, slot.updated_by, None, warehouse_over_delivered)
            if _async:
                params = {
                    'slot_id': slot.id, 'data': data, 'last_version': last_version, 'trailer_slots': trailer_slots,
                    'old_slot_movement_id': old_slot_movement_id, 'chemical_applications': chemical_applications
                }
                params = json.loads(json.dumps(params, default=str))
                Job.schedule_job_for_task('slot_update', params=params)
        if (get(response, 'booking_number') and should_create_movement_from_update and
                (slot.errors or not is_movement_created)):
            company = instance.get_company_with_external_booking_enabled()
            if company:
                cancel_booking_response = company.cancel_external_booking({
                    'freight_movement_identifier': instance.movement.identifier,
                    'comments': 'Booking Failed'
                })
                if not get(cancel_booking_response, 'booking_number'):
                    ERRBIT_LOGGER.log(
                        f"Failed To Cancel Booking {get(response, 'booking_number')} on external system."
                    )
        should_cascade_update = (
                last_version['grade_id'] != slot.grade_id or
                last_version['truck_id'] != slot.truck_id or
                last_version['driver_id'] != slot.driver_id or
                last_version['tonnage'] != slot.tonnage or
                last_version['freight_provider_id'] != slot.freight_provider_id
        )
        if should_cascade_update:
            from core.freights.models import FreightContract
            fields_to_update = {}
            if slot.movement_id and slot.movement.is_confirmed():
                fields_to_update.update({
                    'planned_grade_id': slot.grade_id,
                    'planned_truck_id': slot.truck_id
                })

            if slot.movement_id and last_version['freight_provider_id'] != slot.freight_provider_id:
                if request_user:
                    fields_to_update['created_by'] = request_user
                fields_to_update.update({
                    'planned_truck_id': slot.truck_id,
                    'provider': slot.freight_provider,
                    'assign_to': slot.driver
                })

            if len(fields_to_update) > 0:
                FreightContract.objects.filter(id=slot.movement_id).update(
                    **fields_to_update,
                    updated_at=timezone.now(),
                )

            counter_slot = slot.counter_slot
            if counter_slot:
                counter_slot.grade_id = slot.grade_id
                counter_slot.truck_id = slot.truck_id
                counter_slot.driver_id = slot.driver_id
                counter_slot.updated_by_id = slot.updated_by_id
                counter_slot.save()
        slot.unpause_history()
        return slot

    def post_quick_update(
            self, data, last_version, trailer_slots=None, old_slot_movement_id=None, chemical_applications=None,
            request_origin='Web', feature='Slot'
    ):
        FreightSlot.pause_history()
        self.pause_history()

        if 'is_urgent_for_provider' not in data:
            self.__toggle_urgent_for_provider(last_version)
        self.__toggle_urgent_for_manager()
        if self.is_updated_by_freight_provider() and last_version:
            self.provider_updated_fields = self._get_provider_updated_fields(last_version)
        elif self.status == 'planned':
            self.provider_updated_fields = []
        if self.is_dirty():
            self.save()
        self.add_slot_booking_company_to_site_owner_company()
        self.update_chemical_applications_in_movement(chemical_applications)
        slot_alert_job_params = None
        if old_slot_movement_id or self.__should_create_movement_from_update(last_version):
            self.__create_movement(old_slot_movement_id=old_slot_movement_id,
                                   request_origin=request_origin, feature=feature)
            if old_slot_movement_id:
                self.movement.audit(action='amend', user=self.updated_by, request_origin=request_origin,
                                    feature=feature)
        else:
            slot_alert_job_params = self.__update_movement(last_version, request_origin=request_origin, feature=feature)
        self.update_load(last_version)

        old_trailer_slot_ids = compact(self.trailer_slot_ids)
        self.update_trailer_slots_with_current(trailer_slots, last_version)

        FreightSlot.unpause_history()
        self.unpause_history()
        self.save()
        self.add_truck_company_to_all_related_parties_company_directory()
        if slot_alert_job_params:
            from core.jobs.models import Job
            [
                Job.schedule_job_for_task(
                    'send_slot_update_alerts', params={**param, 'slot_updated_by_id': self.updated_by_id}
                ) for param in slot_alert_job_params
            ]
        self.queue_sftp_push(last_version)
        self.publish_update_to_ably(last_version, old_trailer_slot_ids)
        self.notify_updated(last_version)

    def update_chemical_applications_in_movement(self, chemical_applications):
        if self.movement_id:
            if chemical_applications:
                self.movement.chemical_applications.all().delete()
                from core.contracts.models import ChemicalApplication
                ChemicalApplication.persist_many(chemical_applications, movement_id=self.movement_id)
            else:
                self.movement.chemical_applications.all().delete()

    def queue_sftp_push(self, last_version):
        if not self.errors and self.__should_push_to_sftp(last_version):
            from core.jobs.models import Job
            job = Job(
                type='push_slot_to_sftp', status=Job.TEMP_STATUS,
                params={
                    'site_id': self.site_id,
                    'slot_id': self.id,
                    'csv_row': self.to_external_sftp_csv_row(last_version)
                }
            )
            job.save()

    def should_update_load(self, last_version):
        return self.has_movement_id() and last_version and self.__is_any_field_updated(
            last_version, ['truck_id', 'driver_id'], True)

    def add_truck_company_to_all_related_parties_company_directory(self):
        from core.companies.models import Company
        truck_company_id = get(self.truck, 'company_id')
        movement = self.movement
        related_company_ids = [
            self.freight_provider_id, self.sub_freight_provider_id, get(self, 'site.company_id'),
            get(movement, 'freight_pickup.consignor.handler.company_id'),
            get(movement, 'freight_delivery.consignee.handler.company_id')
        ]
        related_company_ids = compact(list(set(related_company_ids)))
        if truck_company_id:
            Company.add_companies_to_directory(truck_company_id, related_company_ids)
        for company_id in related_company_ids:
            Company.add_companies_to_directory(company_id, [truck_company_id])

    @property
    def date_based_on_timezone(self):
        return DateTimeUtil.localize_date(self.start, self.timezone)

    @property
    def start_time_based_on_timezone(self):
        return DateTimeUtil.localize_time(self.start, self.timezone)

    @property
    def end_time_based_on_timezone(self):
        return DateTimeUtil.localize_time(self.end, self.timezone)

    def _get_time_updated(self, previous_version):
        fields = ['start', 'end']
        tz = get(self.site, '_timezone.location')
        result = []
        for field in fields:
            current = get(self, field)
            prev = get(previous_version, field)
            if prev != current:
                current = DateTimeUtil.localize_date(current, tz) + ", " + DateTimeUtil.localize_time(current, tz)
                prev = DateTimeUtil.localize_date(prev, tz) + ", " + DateTimeUtil.localize_time(prev, tz)
                field = field.replace("_", " ").title() + " Date & Time"
                result.append({'field': field, 'prev_value': prev, 'current_value': current})
        return result

    def _get_fields_updated(self, previous_version):
        from core.profiles.models import Employee
        result = []
        fields = ['status', 'truck_id', 'driver_id', 'tonnage']
        for field in fields:
            current = get(self, field)
            prev = get(previous_version, field)
            if prev != current:
                if field == 'truck_id':
                    current = Truck.objects.filter(id=current).values_list('rego', flat=True).first()
                    prev = Truck.objects.filter(id=prev).values_list('rego', flat=True).first()
                    field = 'Truck'
                elif field == 'driver_id':
                    name = Employee.objects.filter(id=current).values_list('first_name', 'last_name').first()
                    current = ' '.join(name) if name else None
                    name = Employee.objects.filter(id=prev).values_list('first_name', 'last_name').first()
                    prev = ' '.join(name) if name else None
                    field = 'Driver'
                elif field == 'tonnage':
                    country = Country.get_requesting_country()
                    current = f"{current} {country.display_unit}"
                    prev = f"{prev} {country.display_unit}"
                    field = field.replace("_", " ").title()
                elif field == 'status':
                    if current == 'completed':
                        continue
                    current = current.replace("_", " ").title()
                    prev = prev.replace("_", " ").title()
                    field = field.replace("_", " ").title()
                result.append({'field': field, 'prev_value': prev, 'current_value': current})
        return result

    def __is_any_field_updated(self, previous_version, fields, check_exists=False):
        """
        1. checks if field value is updated
        2. Returns True if any one of field is updated
        :param previous_version: dict for slot before update
        :param fields: list of db fields applicable for slot
        :param check_exists: boolean to check existance of current value
        :return: Boolean
        """
        result = False
        for field in fields:
            current = get(self, field)
            prev = get(previous_version, field)
            _res = prev != current
            if check_exists:
                _res = bool(_res and current)
            result = _res
            if result:
                break
        return result

    @property
    def load(self):
        return self.loads.first()

    @property
    def loads(self):
        if self.has_movement_id():
            if self.is_inload:
                return self.movement.active_inloads
            if self.is_outload:
                return self.movement.active_outloads

        from core.loads.models import Load
        return Load.objects.none()

    def update_load(self, last_version):
        if self.should_update_load(last_version):
            load = self.load
            if load:
                load.freight_provider_id = self.sub_freight_provider_id or self.freight_provider_id
                load.truck_id = self.truck_id
                load.driver_id = self.driver_id
                load.save()

    def update_trailer_slots_with_current(self, trailer_slots, last_version):  # pylint: disable=too-many-branches
        saved_ids = []
        if not self.__is_reverted_to_booked(last_version): # pylint: disable=too-many-nested-blocks
            if self.has_movement_id() and isinstance(trailer_slots, dict):
                if self.trailer_slot_ids:
                    new_trailer_slot_ids = compact(list(trailer_slots.values()))
                    self.cancel_trailer_slots(list(set(new_trailer_slot_ids).intersection(set(self.trailer_slot_ids))))
                if self.status in ['booked', 'in_progress', 'completed']:
                    for _, slot_id in trailer_slots.items():
                        if slot_id:
                            trailer_slot = FreightSlot.objects.filter(id=slot_id).first()
                            if trailer_slot:
                                if trailer_slot.movement_id == self.movement_id:
                                    saved_ids.append(int(slot_id))
                                elif trailer_slot.has_movement_id():
                                    ERRBIT_LOGGER.raise_errbit(
                                        'Trailer Slot already has different movement, so skipping it. Parent Slot: '
                                        + str(self.id) + ', Trailer Slot: ' + str(trailer_slot.id) +
                                        ', Trailer Movement ID: ' + str(trailer_slot.movement_id) +
                                        ', New Movement ID: ' + str(self.movement_id)
                                    )
                                    return
                                trailer_slot.freight_provider_id = self.freight_provider_id
                                trailer_slot.sub_freight_provider_id = self.sub_freight_provider_id
                                trailer_slot.commodity_id = self.commodity_id
                                trailer_slot.grade_id = self.grade_id
                                trailer_slot.status = self.status
                                trailer_slot.order_id = self.order_id
                                trailer_slot.movement_id = self.movement_id
                                trailer_slot.driver_id = self.driver_id
                                trailer_slot.truck_id = self.truck_id
                                trailer_slot.tonnage = self.tonnage
                                trailer_slot._entered_tonnage = self._entered_tonnage
                                trailer_slot.is_trailer_slot = True
                                trailer_slot.parent_slot_id = self.id
                                trailer_slot.type = self.type
                                trailer_slot.is_urgent_for_provider = self.is_urgent_for_provider
                                trailer_slot.is_urgent_for_manager = self.is_urgent_for_manager
                                trailer_slot.save()
                                if not trailer_slot.errors and int(slot_id) not in saved_ids:
                                    saved_ids.append(int(slot_id))
                self.trailer_slot_ids = saved_ids
                self.save()
                if self.status not in ['completed', 'booked']:
                    self.cancel_trailer_slots(self.trailer_slot_ids)
        else:
            FreightSlot.objects.filter(parent_slot_id=self.id).update(status='booked')

    def cancel_trailer_slots(self, exclude_ids=None):
        queryset = FreightSlot.objects.filter(parent_slot_id=self.id)
        if exclude_ids:
            queryset = queryset.exclude(id__in=exclude_ids)
        for slot in queryset:
            slot.revert_to_last_planned()
            slot.status = "planned"
            slot.movement_id = None
            slot.order_id = None
            slot.driver_id = None
            slot.truck_id = None
            slot.freight_provider_id = None
            slot.sub_freight_provider_id = None
            slot.commodity_id = None
            slot.grade_id = None
            slot.tonnage = None
            slot._entered_tonnage = None
            slot.is_trailer_slot = False
            slot.parent_slot_id = None
            slot.save()
            slot.remove_from_trailer_slots()

    def book_from_movement(self, movement, sub_freight_provider_id, driver_id=None, in_transaction=False):
        last_version = self.to_dict().copy()
        self.status = 'booked'
        self.tonnage = movement.planned_tonnage
        self.commodity_id = movement.commodity_id
        self.grade_id = movement.planned_grade_id
        self.season = movement.season
        self.order_id = movement.order_id
        order_provider_id = movement.order.provider_id
        self.freight_provider_id = order_provider_id or movement.provider_id
        if sub_freight_provider_id and sub_freight_provider_id != self.freight_provider_id:
            self.sub_freight_provider_id = sub_freight_provider_id
        elif order_provider_id and order_provider_id != movement.provider_id and not sub_freight_provider_id:
            self.sub_freight_provider_id = movement.provider_id
        self.movement_id = movement.id
        self.truck_id = movement.planned_truck_id
        self.driver_id = driver_id
        self.on_route = bool(movement.is_in_progress())
        self.updated_by = movement.created_by
        self.__toggle_urgent_for_provider(last_version)
        self.__toggle_urgent_for_manager()
        self.site.company.add_company_to_directory(self.freight_provider_id)
        if not self.type:
            if self.site == get(movement, 'freight_pickup.consignor.handler'):
                self.type = 'outload'
            if self.site == get(movement, 'freight_delivery.consignee.handler'):
                self.type = 'inload'
        if self.is_updated_by_freight_provider() and last_version:
            self.provider_updated_fields = self._get_provider_updated_fields(last_version)
        checkpoint = self.checkpoint
        if checkpoint and checkpoint.booking_number and checkpoint.booking_number != self.booking_number:
            self.booking_number = checkpoint.booking_number
        self.save()
        self.add_slot_booking_company_to_site_owner_company()
        transaction.on_commit(
            lambda: self.notify_all(last_version)
        ) if in_transaction else self.notify_all(last_version)

    def cancel_booking_from_movement(self, movement, user=None):
        last_version = self.to_dict().copy()
        self.revert_to_last_planned()
        self.status = 'planned'
        self.customer = None
        self.order_id = None
        self.movement_id = None
        self.freight_provider_id = None
        self.delivery_order_number = None
        self.truck_id = None
        self.driver_id = None
        self.on_route = False
        self.parent_slot_id = None
        self.trailer_slot_ids = []
        self.is_trailer_slot = False
        self.updated_by = user or movement.updated_by
        self.__toggle_urgent_for_provider(last_version)
        self.__toggle_urgent_for_manager()
        self.save()
        self.notify_all(last_version)

    def notify_all(self, last_version):
        self.queue_sftp_push(last_version)
        self.publish_update_to_ably(last_version)
        self.notify_updated(last_version)

    @classmethod
    def book_slots_from_movement(
            cls, slot_ids, movement, sub_freight_provider_id=None, driver_id=None, in_transaction=False
    ):
        if movement.persisted:
            for slot in FreightSlot.objects.filter(id__in=compact(slot_ids)).select_related('site__company'):
                slot.book_from_movement(movement, sub_freight_provider_id, driver_id, in_transaction)

    @classmethod
    def revert_booked_slots_from_movement(cls, slot_ids, movement, user=None):
        for slot in FreightSlot.objects.filter(id__in=compact(slot_ids)).select_related('site__company'):
            slot.cancel_booking_from_movement(movement, user)

    def book_with_inload(self, inload_slot, outload_slot_data, inload_slot_data):
        outload_last_version = self.to_dict().copy()
        inload_last_version = inload_slot.to_dict().copy()
        outload_slot_data['tonnage'] = outload_slot_data.get('tonnage', self.tonnage)
        outload_slot_data['id'] = self.id
        updated_outload_slot = FreightSlot.update(instance=self, data=outload_slot_data)
        if not updated_outload_slot or updated_outload_slot.errors:
            return False

        updated_outload_slot.refresh_from_db()
        for field in [
            'tonnage', 'order_id', 'movement_id', 'freight_provider_id', 'grade_id', 'commodity_id',
            'status', 'driver_id', 'truck_id', 'sub_freight_provider_id',
        ]:
            setattr(inload_slot, field, get(updated_outload_slot, field))
        inload_slot.booking_number = get(inload_slot_data, 'booking_number')
        inload_slot.save()
        delivery = inload_slot.movement.freight_delivery
        delivery.date_time = inload_slot.start
        delivery.save()

        self.add_slot_booking_company_to_site_owner_company()
        inload_slot.add_slot_booking_company_to_site_owner_company()  # pylint: disable=protected-access

        self.publish_update_to_ably(outload_last_version)
        inload_slot.publish_update_to_ably(inload_last_version)
        self.notify_updated(outload_last_version)
        inload_slot.notify_updated(inload_last_version)
        return True

    def _get_provider_updated_fields(self, last_version):
        return [f for f in list(set(
            self.provider_updated_fields + self._get_changeset_fields(last_version)
        )) if f not in [
            'site_id', 'start', 'end', 'created_by_id',
            'do_not_convert_tonnage', '_entered_tonnage', 'country_id', 'entered_tonnage'
        ]]

    @property
    def applications_display(self):
        applications = None
        display_name = ''
        if not self.movement_id and self.order_id:
            applications = self.order.applications
        if applications:
            for application in applications:
                commodity = application.commodity
                display_name += f' + {application.application_fee} {commodity.unit_abbr} {commodity.display_name}'
        return display_name


    @property
    def parent_grade_name(self):
        grade_name = ''
        if self.movement_id:
            grade_name = self.movement.grade_name
        if not grade_name and self.order_id:
            grade_name = self.order.grade_name
        return grade_name + self.applications_display

    @property
    def forward_siblings(self):
        if self.has_siblings:
            return self.siblings[self.siblings.index(self.id):]
        else:
            return [self.id]

    @property
    def is_recurring(self):
        return self.has_siblings

    @property
    def has_siblings(self):
        return len(self.siblings) > 1 and self.id in self.siblings  # pylint: disable=unsupported-membership-test

    def add_slot_booking_company_to_site_owner_company(self):
        provider_ids = compact(
            {self.freight_provider_id or get(self, 'truck.company_id'), self.sub_freight_provider_id}
        )
        if provider_ids:
            company = self.site.company
            for provider_id in provider_ids:
                company.add_company_to_directory(provider_id)

    def __set_empty_rels_to_none(self):
        if not self.commodity_id:
            self.commodity = None
        if not self.freight_provider_id:
            self.freight_provider = None
        if not self.sub_freight_provider_id:
            self.sub_freight_provider = None
        if not self.driver_id:
            self.driver = None
        if not self.truck_id:
            self.truck = None

    def is_old_version(self, timestamp):
        if not self.updated_at:
            return False

        if not timestamp:
            return True

        return self.updated_at > datetime.fromtimestamp(timestamp, pytz.utc)

    def is_setting_updated_since(self, timestamp):
        if not self.settings:
            return False

        if not timestamp:
            return True

        return self.settings.updated_at > datetime.fromtimestamp(timestamp, pytz.utc)

    @staticmethod
    def is_slot_updated_since(slot_id, timestamp):
        slot = FreightSlot.objects.filter(id=slot_id).first()
        if slot:
            return slot.is_old_version(timestamp)

    def to_sse_event(self):
        from .serializers import FreightSlotSerializer

        self.__set_empty_rels_to_none()
        self.user = self.updated_by
        data = FreightSlotSerializer(self, context={'request': self}).data
        return json.dumps(
            camelize(data),
            sort_keys=True,
            indent=1,
            cls=DjangoJSONEncoder,
        )

    def publish_new_slots_to_ably(self):
        self._publish_to_ably('freight-slots-new', self.siblings or [self.id])

    def publish_update_to_ably(self, last_version=None, extra_slot_ids=None):
        if last_version:
            old_slot = FreightSlot.__build_from_version(last_version)
            if self.__has_provider_changed(old_slot):
                old_slot.__publish_to_ably_for_provider('freight-slots-update', self.to_sse_event())
        self._publish_to_ably('freight-slots-update', self.to_sse_event())

        if extra_slot_ids:
            for slot in FreightSlot.objects.filter(id__in=extra_slot_ids):
                slot._publish_to_ably('freight-slots-update', slot.to_sse_event())

        self.publish_update_truck_slot()
        self.publish_update_trailer_slots()

    def publish_update_truck_slot(self):
        slot = FreightSlot.objects.filter(id=self.parent_slot_id).first()
        if slot:
            slot._publish_to_ably('freight-slots-update', slot.to_sse_event())

    def publish_update_trailer_slots(self):
        for slot in FreightSlot.objects.filter(parent_slot_id=self.id):
            slot._publish_to_ably('freight-slots-update', slot.to_sse_event())

    def publish_delete_to_ably(self, ids=None):
        ids = ids or [self.id]
        self._publish_to_ably('freight-slots-delete', ids)

    def _publish_to_ably(self, channel, data):
        ABLY.publish(channel, str(self.site.company_id), data)
        self.__publish_to_ably_for_provider(channel, data)

    def __publish_to_ably_for_provider(self, channel, data):
        if self.freight_provider_id:
            ABLY.publish(channel, str(self.freight_provider_id), data)
            self.__publish_to_ably_for_sub_provider(channel, data)

    def __publish_to_ably_for_sub_provider(self, channel, data):
        if self.sub_freight_provider_id:
            ABLY.publish(channel, str(self.sub_freight_provider_id), data)

    @staticmethod
    def get_count_and_tonnage(queryset):
        if isinstance(queryset, list):
            return len(queryset), sum(slot.tonnage or 0 for slot in queryset)
        return queryset.count(), queryset.aggregate(total_tonnage=Sum('tonnage'))['total_tonnage'] or 0

    @staticmethod
    def get_booked_queryset(queryset):
        return queryset.exclude(
            status__in=['planned', 'cancelled', 'rejected', 'restricted']).filter(freight_provider_id__isnull=False)

    @staticmethod
    def to_cancelled_count(queryset, provider_ids):
        count = 0
        for slot in queryset.exclude(status='restricted'):
            for _history in slot.history.order_by('history_date'):
                is_cancelled = _history.status == 'booked' and get(_history, 'next_record.status') == 'planned'
                if is_cancelled and (not provider_ids or _history.freight_provider_id in provider_ids):
                    count += 1
        return count

    @classmethod
    def to_cancelled_booking_report(cls, queryset, tz):
        result = []
        for slot in queryset.exclude(status='restricted'):
            for _history in slot.history.order_by('history_date'):
                if _history.status == 'booked' and get(_history, 'next_record.status') == 'planned':
                    result.append(_history.instance.localized_report_data(tz, True))
        return result

    @staticmethod
    def get_booking_cancelled_slots_stats(queryset, order_queryset):
        slots = queryset.exclude(status='restricted')
        booking_cancelled_slot_stats = {}
        booking_cancelled_never_rebooked = {'count': 0, 'tonnage': 0}
        booking_cancelled_rebooked = {'count': 0, 'tonnage': 0}  # may be duplicate to count number of cancellation
        order_ids = set(order_queryset.values_list('id', flat=True))
        for slot in slots:
            for _history in slot.history.all():
                if _history.status == 'booked' and _history.order_id in order_ids and get(
                        _history, 'next_record.status') == 'planned':
                    if slot.id not in booking_cancelled_slot_stats:
                        booking_cancelled_slot_stats[slot.id] = []
                    booking_cancelled_slot_stats[slot.id].append(_history.tonnage or 0)

        for slot_id, tonnages in booking_cancelled_slot_stats.items():
            if FreightSlot.objects.filter(id=slot_id, status='planned').exists():
                booking_cancelled_never_rebooked['count'] += 1
                booking_cancelled_never_rebooked['tonnage'] += tonnages[0]
            else:
                booking_cancelled_rebooked['count'] += len(tonnages)
                booking_cancelled_rebooked['tonnage'] += sum(tonnages)

        return booking_cancelled_never_rebooked, booking_cancelled_rebooked

    @classmethod
    def order_delivery_stats(
            cls, company_id, site_ids=None, seller_ids=None, start=None, end=None, partial_date_range=False
    ):  # pylint: disable=too-many-statements,too-many-locals
        from core.freights.models import FreightOrder
        from core.companies.models import Company

        now = timezone.now()
        start = start or (now - relativedelta(months=3)).date()
        end = end or now.date()
        orders = FreightOrder.get(company_id, {}, None, None)
        all_slots = cls.company_slots(company_id, start, end)
        if site_ids:
            orders = orders.filter(
                models.Q(freight_pickup__consignor__handler_id__in=site_ids) | models.Q(
                    freight_delivery__consignee__handler_id__in=site_ids)
            )
            all_slots = all_slots.filter(site_id__in=site_ids)

        if partial_date_range:
            orders = orders.filter(
                models.Q(freight_pickup__date_time__gte=start, freight_pickup__date_time__lte=end) |
                models.Q(freight_delivery__date_time__gte=start, freight_delivery__date_time__lte=end) |
                models.Q(freight_pickup__date_time__lte=start, freight_delivery__date_time__gte=end)
            )
            all_slots = all_slots.filter(
                models.Q(start__gte=start, start__lte=end) |
                models.Q(end__gte=start, end__lte=end) |
                models.Q(start__lte=start, end__gte=end)
            )
        else:
            orders = orders.filter(freight_pickup__date_time__gte=start, freight_delivery__date_time__lte=end)
            all_slots = all_slots.filter(start__gte=start, end__lte=end)

        all_seller_orders = orders.exclude(type_id=CUSTOMER_ONLY_TYPE_ID)
        all_customer_orders = orders.filter(type_id=CUSTOMER_ONLY_TYPE_ID)
        customer_ids = FreightOrder.get_customer_company_ids(all_customer_orders)

        if seller_ids:
            all_seller_orders = orders.filter(
                models.Q(seller__company_id__in=seller_ids) |
                models.Q(commodity_contract__seller__company_id__in=seller_ids)
            )
        else:
            seller_ids = compact(FreightOrder.get_seller_company_ids(all_seller_orders))

        seller_order_stats = {}
        customer_order_stats = {}
        totals_dict = {
            'total_tonnage': 0,
            'booked_slots': 0,
            'booked_tonnage': 0,
            'delivered_slots': 0,
            'delivered_tonnage': 0,
            'undelivered_tonnage': 0,
            'incomplete_slots': 0,
            'incomplete_tonnage': 0,
            'rejected_slots': 0,
            'rejected_tonnage': 0,
            'cancelled_not_rebooked_slots': 0,
            'cancelled_not_rebooked_tonnage': 0,
            'cancelled_rebooked_slots': 0,
            'cancelled_rebooked_tonnage': 0
        }

        for seller_id in seller_ids:
            company = Company.objects.filter(id=seller_id).first()
            seller_orders = all_seller_orders.filter(
                models.Q(seller__company_id=seller_id) | models.Q(commodity_contract__seller__company_id=seller_id)
            )
            seller_result = {
                'name': company.name,
                'data': {
                    'count': seller_orders.count(),
                    'total': totals_dict.copy(),
                    'commodities': {}
                }
            }
            for commodity_id, grade_id in set(seller_orders.values_list('commodity_id', 'planned_grade_id')):
                if commodity_id not in seller_result['data']['commodities']:
                    commodity = Commodity.objects.get(id=commodity_id)
                    seller_result['data']['commodities'][commodity_id] = {
                        'name': commodity.display_name,
                        'total': totals_dict.copy(),
                        'grades': {}
                    }
                grade = Grade.objects.get(id=grade_id)
                grade_orders = seller_orders.filter(commodity_id=commodity_id, planned_grade_id=grade_id)
                grade_tonnage = grade_orders.aggregate(total_tonnage=Sum('planned_tonnage'))['total_tonnage'] or 0

                order_slots = cls.objects.filter(order__in=grade_orders)
                booked_slots_count, booked_tonnage = cls.get_count_and_tonnage(
                    cls.get_booked_queryset(order_slots))
                delivered_slots_count, delivered_tonnage = cls.get_count_and_tonnage(
                    order_slots.filter(status='completed'))
                incomplete_slots_count, incomplete_tonnage = cls.get_count_and_tonnage(
                    order_slots.filter(status='cancelled'))
                rejected_slots_count, rejected_tonnage = cls.get_count_and_tonnage(
                    order_slots.filter(status='rejected'))

                cancelled_not_rebooked_slots, cancelled_rebooked_slots = cls.get_booking_cancelled_slots_stats(
                    all_slots, grade_orders)

                undelivered_tonnage = grade_tonnage - delivered_tonnage
                seller_result['data']['commodities'][commodity_id]['grades'][grade_id] = {
                    'name': grade.name,
                    'total_tonnage': grade_tonnage,
                    'booked_slots': booked_slots_count,
                    'booked_tonnage': booked_tonnage,
                    'delivered_slots': delivered_slots_count,
                    'delivered_tonnage': delivered_tonnage,
                    'undelivered_tonnage': undelivered_tonnage,
                    'incomplete_slots': incomplete_slots_count,
                    'incomplete_tonnage': incomplete_tonnage,
                    'rejected_slots': rejected_slots_count,
                    'rejected_tonnage': rejected_tonnage,
                    'cancelled_not_rebooked_slots': cancelled_not_rebooked_slots['count'],
                    'cancelled_not_rebooked_tonnage': cancelled_not_rebooked_slots['tonnage'],
                    'cancelled_rebooked_slots': cancelled_rebooked_slots['count'],
                    'cancelled_rebooked_tonnage': cancelled_rebooked_slots['tonnage']
                }
                seller_result['data']['commodities'][commodity_id]['total']['total_tonnage'] += grade_tonnage
                seller_result['data']['commodities'][commodity_id]['total']['booked_slots'] += booked_slots_count
                seller_result['data']['commodities'][commodity_id]['total']['booked_tonnage'] += booked_tonnage
                seller_result['data']['commodities'][commodity_id]['total']['delivered_slots'] += delivered_slots_count
                seller_result['data']['commodities'][commodity_id]['total']['delivered_tonnage'] += delivered_tonnage
                seller_result['data']['commodities'][commodity_id]['total']['undelivered_tonnage'] += undelivered_tonnage  # pylint: disable=line-too-long
                seller_result['data']['commodities'][commodity_id]['total']['incomplete_slots'] += incomplete_slots_count  # pylint: disable=line-too-long
                seller_result['data']['commodities'][commodity_id]['total']['incomplete_tonnage'] += incomplete_tonnage
                seller_result['data']['commodities'][commodity_id]['total']['rejected_slots'] += rejected_slots_count
                seller_result['data']['commodities'][commodity_id]['total']['rejected_tonnage'] += rejected_tonnage
                seller_result['data']['commodities'][commodity_id]['total']['cancelled_not_rebooked_slots'] += cancelled_not_rebooked_slots['count']  # pylint: disable=line-too-long
                seller_result['data']['commodities'][commodity_id]['total']['cancelled_not_rebooked_tonnage'] += cancelled_not_rebooked_slots['tonnage']  # pylint: disable=line-too-long
                seller_result['data']['commodities'][commodity_id]['total']['cancelled_rebooked_slots'] += cancelled_rebooked_slots['count']  # pylint: disable=line-too-long
                seller_result['data']['commodities'][commodity_id]['total']['cancelled_rebooked_tonnage'] += cancelled_rebooked_slots['tonnage']  # pylint: disable=line-too-long
                seller_result['data']['total']['total_tonnage'] += grade_tonnage
                seller_result['data']['total']['booked_slots'] += booked_slots_count
                seller_result['data']['total']['booked_tonnage'] += booked_tonnage
                seller_result['data']['total']['delivered_slots'] += delivered_slots_count
                seller_result['data']['total']['delivered_tonnage'] += delivered_tonnage
                seller_result['data']['total']['undelivered_tonnage'] += undelivered_tonnage
                seller_result['data']['total']['incomplete_slots'] += incomplete_slots_count
                seller_result['data']['total']['incomplete_tonnage'] += incomplete_tonnage
                seller_result['data']['total']['rejected_slots'] += rejected_slots_count
                seller_result['data']['total']['rejected_tonnage'] += rejected_tonnage
                seller_result['data']['total']['cancelled_not_rebooked_slots'] += cancelled_not_rebooked_slots['count']
                seller_result['data']['total']['cancelled_not_rebooked_tonnage'] += cancelled_not_rebooked_slots['tonnage']  # pylint: disable=line-too-long
                seller_result['data']['total']['cancelled_rebooked_slots'] += cancelled_rebooked_slots['count']
                seller_result['data']['total']['cancelled_rebooked_tonnage'] += cancelled_rebooked_slots['tonnage']

            seller_order_stats[seller_id] = seller_result

        for customer_id in customer_ids:
            company = Company.objects.filter(id=customer_id).first()
            customer_orders = all_customer_orders.filter(customer__company_id=customer_id)
            customer_orders = customer_orders.filter(freight_pickup__consignor__handler_id__isnull=False)
            for handler_id in set(customer_orders.values_list('freight_pickup__consignor__handler_id', flat=True)):
                pickup_site = Farm.objects.filter(id=handler_id).first()
                handler_orders = customer_orders.filter(freight_pickup__consignor__handler_id=handler_id)
                customer_result = {
                    'name': f"{company.name} | {pickup_site.display_name}",
                    'data': {'count': handler_orders.count(), 'total': totals_dict.copy(), 'commodities': {}}
                }
                for commodity_id, grade_id in set(handler_orders.values_list('commodity_id', 'planned_grade_id')):
                    if commodity_id not in customer_result['data']['commodities']:
                        commodity = Commodity.objects.get(id=commodity_id)
                        customer_result['data']['commodities'][commodity_id] = {
                            'name': commodity.display_name,
                            'total': totals_dict.copy(),
                            'grades': {}
                        }
                    grade = Grade.objects.get(id=grade_id)
                    grade_orders = handler_orders.filter(commodity_id=commodity_id, planned_grade_id=grade_id)
                    grade_tonnage = grade_orders.aggregate(total_tonnage=Sum('planned_tonnage'))['total_tonnage'] or 0

                    order_slots = cls.objects.filter(order__in=grade_orders)
                    booked_slots_count, booked_tonnage = cls.get_count_and_tonnage(
                        cls.get_booked_queryset(order_slots))
                    delivered_slots_count, delivered_tonnage = cls.get_count_and_tonnage(
                        order_slots.filter(status='completed'))
                    incomplete_slots_count, incomplete_tonnage = cls.get_count_and_tonnage(
                        order_slots.filter(status='cancelled'))
                    rejected_slots_count, rejected_tonnage = cls.get_count_and_tonnage(
                        order_slots.filter(status='rejected'))

                    cancelled_not_rebooked_slots, cancelled_rebooked_slots = cls.get_booking_cancelled_slots_stats(
                        all_slots, grade_orders)

                    undelivered_tonnage = grade_tonnage - delivered_tonnage
                    customer_result['data']['commodities'][commodity_id]['grades'][grade_id] = {
                        'name': grade.name,
                        'total_tonnage': grade_tonnage,
                        'booked_slots': booked_slots_count,
                        'booked_tonnage': booked_tonnage,
                        'delivered_slots': delivered_slots_count,
                        'delivered_tonnage': delivered_tonnage,
                        'undelivered_tonnage': undelivered_tonnage,
                        'incomplete_slots': incomplete_slots_count,
                        'incomplete_tonnage': incomplete_tonnage,
                        'rejected_slots': rejected_slots_count,
                        'rejected_tonnage': rejected_tonnage,
                        'cancelled_not_rebooked_slots': cancelled_not_rebooked_slots['count'],
                        'cancelled_not_rebooked_tonnage': cancelled_not_rebooked_slots['tonnage'],
                        'cancelled_rebooked_slots': cancelled_rebooked_slots['count'],
                        'cancelled_rebooked_tonnage': cancelled_rebooked_slots['tonnage']
                    }
                    customer_result['data']['commodities'][commodity_id]['total']['total_tonnage'] += grade_tonnage
                    customer_result['data']['commodities'][commodity_id]['total']['booked_slots'] += booked_slots_count
                    customer_result['data']['commodities'][commodity_id]['total']['booked_tonnage'] += booked_tonnage
                    customer_result['data']['commodities'][commodity_id]['total']['delivered_slots'] += delivered_slots_count  # pylint: disable=line-too-long
                    customer_result['data']['commodities'][commodity_id]['total']['delivered_tonnage'] += delivered_tonnage  # pylint: disable=line-too-long
                    customer_result['data']['commodities'][commodity_id]['total']['undelivered_tonnage'] += undelivered_tonnage  # pylint: disable=line-too-long
                    customer_result['data']['commodities'][commodity_id]['total']['incomplete_slots'] += incomplete_slots_count  # pylint: disable=line-too-long
                    customer_result['data']['commodities'][commodity_id]['total']['incomplete_tonnage'] += incomplete_tonnage  # pylint: disable=line-too-long
                    customer_result['data']['commodities'][commodity_id]['total']['rejected_slots'] += rejected_slots_count  # pylint: disable=line-too-long
                    customer_result['data']['commodities'][commodity_id]['total']['rejected_tonnage'] += rejected_tonnage  # pylint: disable=line-too-long
                    customer_result['data']['commodities'][commodity_id]['total']['cancelled_not_rebooked_slots'] += cancelled_not_rebooked_slots['count']  # pylint: disable=line-too-long
                    customer_result['data']['commodities'][commodity_id]['total']['cancelled_not_rebooked_tonnage'] += cancelled_not_rebooked_slots['tonnage']  # pylint: disable=line-too-long
                    customer_result['data']['commodities'][commodity_id]['total']['cancelled_rebooked_slots'] += cancelled_rebooked_slots['count']  # pylint: disable=line-too-long
                    customer_result['data']['commodities'][commodity_id]['total']['cancelled_rebooked_tonnage'] += cancelled_rebooked_slots['tonnage']  # pylint: disable=line-too-long
                    customer_result['data']['total']['total_tonnage'] += grade_tonnage
                    customer_result['data']['total']['booked_slots'] += booked_slots_count
                    customer_result['data']['total']['booked_tonnage'] += booked_tonnage
                    customer_result['data']['total']['delivered_slots'] += delivered_slots_count
                    customer_result['data']['total']['delivered_tonnage'] += delivered_tonnage
                    customer_result['data']['total']['undelivered_tonnage'] += undelivered_tonnage
                    customer_result['data']['total']['incomplete_slots'] += incomplete_slots_count
                    customer_result['data']['total']['incomplete_tonnage'] += incomplete_tonnage
                    customer_result['data']['total']['rejected_slots'] += rejected_slots_count
                    customer_result['data']['total']['rejected_tonnage'] += rejected_tonnage
                    customer_result['data']['total']['cancelled_not_rebooked_slots'] += cancelled_not_rebooked_slots['count']  # pylint: disable=line-too-long
                    customer_result['data']['total']['cancelled_not_rebooked_tonnage'] += cancelled_not_rebooked_slots['tonnage']  # pylint: disable=line-too-long
                    customer_result['data']['total']['cancelled_rebooked_slots'] += cancelled_rebooked_slots['count']
                    customer_result['data']['total']['cancelled_rebooked_tonnage'] += cancelled_rebooked_slots['tonnage']  # pylint: disable=line-too-long

                customer_order_stats[customer_id] = customer_result

        return seller_order_stats, customer_order_stats, orders

    @cached_property
    def agrichain_as_vendor_number(self):
        return self.site.company.agrichain_as_vendor_number

    @staticmethod
    def __get_prev_relation(klass, prev_id):
        return klass.objects.filter(id=prev_id).first()

    def __get_current_or_prev_value(self, previous_version, relation, rel_klass, attr):
        return get(self.__get_current_or_prev_relation(previous_version, relation, rel_klass), attr)

    def __get_current_or_prev_relation(self, previous_version, relation, rel_klass):
        has_cancelled_booking = self.__is_just_moved_to_planned(previous_version)
        value = get(self, relation)
        if not value and has_cancelled_booking:  # on cancellation field values might get reset to None
            value = self.__get_prev_relation(rel_klass, get(previous_version, f'{relation}_id'))
        if not value and self.is_planned():
            previous_version = self.history.filter(
                **{f"{relation}__isnull": False, 'status': 'booked'}
            ).order_by('-history_date').first()
            value = self.__get_prev_relation(rel_klass, get(previous_version, f'{relation}_id'))
        return value

    def to_external_sftp_csv_row(self, previous_version):  # previous_version is only needed for cancellations
        has_cancelled_booking = self.__is_just_moved_to_planned(previous_version)

        from core.freights.models import FreightContract, FreightOrder
        driver_name = get(self, 'driver.name')
        if driver_name:
            driver_name = f'"{driver_name}"'
            driver_name = driver_name.replace(',', '')
        order = self.__get_current_or_prev_relation(previous_version, 'order', FreightOrder)
        slot_type = self.type or self.history.order_by('-history_date').filter(type__isnull=False).first().type
        site_order = self.get_site_order(order) or order
        contract = get(site_order, 'commodity_contract')
        return [
            self.agrichain_as_vendor_number,
            slot_type.upper(),
            self.__get_current_or_prev_value(previous_version, 'movement', FreightContract, 'identifier'),
            get(site_order, 'identifier'),
            get(contract, 'reference_number'),
            self.__get_current_or_prev_value(previous_version, 'commodity', Commodity, 'display_name'),
            get(contract, 'material_code'),
            self.start_time_in_site_tz,
            self.tonnage if self.tonnage is None else float(self.tonnage),
            self.site.owner_plant_code,
            self.__get_current_or_prev_value(previous_version, 'truck', Truck, 'rego'),
            driver_name,
            self.__get_current_or_prev_value(previous_version, 'grade', Grade, 'name'),
            'cancelled' if has_cancelled_booking else 'booked',
        ]

    @property
    def start_time_in_site_tz(self):
        tz_location = self.timezone
        start_time = DateTimeUtil.localize_date(self.start, tz_location, "%Y-%m-%d %H:%M:%S%z")
        start_time = start_time[0:-2] + ':' + start_time[-2:]
        return start_time

    @classmethod
    def update_truck_config(cls, slot_ids, truck_config):
        slots = cls.objects.filter(id__in=slot_ids)
        for slot in slots:
            category_id = truck_config.get('category_id', None)
            steer_1_point_1 = truck_config.get('steer_1_point_1', None)
            steer_point_5 = truck_config.get('steer_point_5', None)
            permit_number = truck_config.get('permit_number', None)
            declared_mass_limit = truck_config.get('declared_mass_limit', None)
            accreditation_number = truck_config.get('accreditation_number', None)
            load_sharing = truck_config.get('load_sharing', None)
            notice = truck_config.get('notice', None)
            notice_number = truck_config.get('notice_number', None)
            restricted = truck_config.get('restricted', None)
            if (
                    (slot.category_id != category_id) or (steer_1_point_1 != slot.steer_1_point_1) or
                    (steer_point_5 != slot.steer_point_5) or (permit_number != slot.permit_number) or
                    (declared_mass_limit != slot.declared_mass_limit) or
                    (accreditation_number != slot.accreditation_number) or (load_sharing != slot.load_sharing) or
                    (notice != slot.notice) or (notice_number != slot.notice_number) or (restricted != slot.restricted)
            ):
                slot._category_id = category_id
                slot._steer_point_5 = steer_point_5
                slot._steer_1_point_1 = steer_1_point_1
                slot._permit_number = permit_number
                slot._declared_mass_limit = declared_mass_limit
                slot._accreditation_number = accreditation_number
                slot._load_sharing = load_sharing
                slot._notice = notice
                slot._notice_number = notice_number
                slot._restricted = restricted
                slot.upsert_unsaved_truck_config()

    def update_external_booking(self):
        company = self.get_company_with_external_booking_enabled()
        if company:
            response = company.update_slot_information({
                'freight_movement_identifier': self.movement.identifier,
                'tonnage': self.tonnage,
                'truck_id': self.truck_id,
                'provider_id': self.freight_provider_id,
                'driver_id': self.driver_id,
                'truck_config': {
                    "category_id": self.category_id,
                    "steer_point_5": self.steer_point_5,
                    "steer_1_point_1": self.steer_1_point_1,
                    "permit_number": self.permit_number,
                    "declared_mass_limit": self.declared_mass_limit,
                    "accreditation_number": self.accreditation_number,
                    "load_sharing": self.load_sharing,
                    "notice_number": self.notice_number,
                    "restricted": self.restricted
                },
                'slot_id': self.id,
                'start': self.start,
                'end': self.end,
            })
            if not get(response, 'booking_number'):
                ERRBIT_LOGGER.log(f"Failed to update {self.type} for external booking {self.movement.identifier}.")
            if get(response, 'valid_connection') and get(response, 'errors'):
                return get(response, 'errors')

    def cannot_cancel_reasons(self):
        reasons = []
        if self.draft_load_exists_on_booking:
            reasons.append('Draft load exists on booking.')
        return reasons

    @classmethod
    def bulk_delete(cls, site_id, slot_ids):
        last_slot = None
        traversed = []
        deleted_slot_ids = []
        slot_ids = compact(slot_ids)
        if cls.objects.filter(id__in=slot_ids).exclude(site_id=site_id).exists():
            raise ValueError('Some of the slots do not belong to the site')
        if cls.objects.filter(id__in=slot_ids).exclude(status__in=['planned', 'restricted']).exists():
            raise ValueError('Some of the slots do not belong to planned/restricted status')
        for slot_id in compact(slot_ids):
            if slot_id not in traversed:
                traversed.append(slot_id)
                slot = FreightSlot.objects.filter(id=slot_id, site_id=site_id).first()
                if slot:
                    last_slot = slot
                    slot.delete()
                    deleted_slot_ids.append(slot_id)
        if last_slot:
            last_slot.publish_delete_to_ably(slot_ids)
        return deleted_slot_ids


def default_statuses():
    return DEFAULT_SLOT_STATUSES


def default_fields():
    return DEFAULT_SLOT_FIELDS


def default_title_order():
    return ['start', 'tonnage', 'commodity', 'grade', 'bookingNumber', 'freightProvider']


def default_tooltip_order():
    return ['start', 'tonnage', 'commodity', 'grade', 'bookingNumber', 'freightProvider', 'truck', 'driver']


class SiteManagementSettings(RawModel):
    class Meta:
        db_table = 'site_management_settings'

    company = models.OneToOneField('companies.Company', on_delete=models.CASCADE)
    statuses = models.JSONField(default=default_statuses)
    fields = models.JSONField(default=default_fields)
    editable_minutes_before_start = RoundedFloatField(default=60)
    title_order = ArrayField(
        models.CharField(max_length=100),
        default=default_title_order
    )
    tooltip_order = ArrayField(
        models.CharField(max_length=100),
        default=default_tooltip_order
    )
    default_view = models.CharField(max_length=50, default="week", choices=SLOT_VIEWS)
    left_border_color_by_pits = models.BooleanField(default=False)
    order_booking = models.BooleanField(default=False)
    trailer_booking = ArrayField(models.IntegerField(), default=list, null=True, blank=True)
    trailer_booking_quantity = RoundedFloatField(null=True, blank=True)
    pits = ArrayField(models.CharField(max_length=255), default=list, null=True, blank=True)
    minimum_tonnage = RoundedFloatField(null=True, blank=True, default=0.1)
    stocks_csv_headers_info = models.JSONField(blank=True, null=True)
    pickup_order_number_required = models.BooleanField(default=False)
    delivery_order_number_required = models.BooleanField(default=False)
    checkpoint_order_creation_permission = models.CharField(
        choices=CHECKPOINT_ORDER_CREATION_PERMISSIONS, null=True, blank=True, max_length=200)
    activity_log = models.BooleanField(default=False)
    load_by_load_transfer = models.BooleanField(default=False)
    update_stock_csv_headers_info = models.JSONField(blank=True, null=True)
    inload_sms = models.BooleanField(default=False)
    customer_optional = models.BooleanField(default=False)
    contract_csv_mapping_info = models.JSONField(blank=True, null=True)
    allowed_truck_company_ids = ArrayField(models.IntegerField(), default=list, null=True, blank=True)

    FILLABLES = [
        'statuses',
        'company_id',
        'fields',
        'editable_minutes_before_start',
        'title_order',
        'default_view',
        'tooltip_order',
        'left_border_color_by_pits',
        'order_booking',
        'trailer_booking',
        'pits',
        'minimum_tonnage',
        'pickup_order_number_required',
        'delivery_order_number_required',
        'checkpoint_order_creation_permission',
        'activity_log',
        'load_by_load_transfer',
        'inload_sms',
        'customer_optional',
        'allowed_truck_company_ids',
        'trailer_booking_quantity'
    ]

    @staticmethod
    def dummy_settings():
        return SiteManagementSettings(updated_at=DateTimeUtil.get_datetime_from_string('2000-01-01'))

    def get_status_label(self, status):
        if not status:
            return

        return [info.get('label') for info in self.statuses if info['id'] == status][0] # pylint: disable=not-an-iterable

    def csv_header(self, booking_cancellation=False):
        header = []
        labels = compact([
            'Site', 'Type', 'Category', 'Status' if not booking_cancellation else None, 'Priority', 'Date',
            'Planned Start Time', 'Actual Start Time', 'Planned End Time', 'Actual End Time', 'Site Entry Time',
            'Site Exit Time', 'Load Start Time', 'Load End Time', 'Time On Site', 'Commodity', 'Grade',
            'Booked Tonnage (MT)', 'Actual Tonnage (MT)' if not booking_cancellation else None, 'Customer',
            'Movement No', 'Order No', 'Contract No', 'Booking No', 'Pits', 'Freight Provider', 'Sub Freight Provider',
            'Truck', 'Driver',
        ])
        for label in labels:
            field_config = find(self.fields, lambda f: get(f, 'name', '').lower() == label.lower())  # pylint: disable=cell-var-from-loop
            header.append(get(field_config, 'label', label))

        header += ['Mobile', 'Transaction Time', 'Variance', 'Travel Time', 'Created At',
        'Created By', 'Last Updated At', 'Last Updated By']

        return header

    @classmethod
    def get_default_view_date_range(cls, company_id):
        settings = cls.objects.filter(company_id=company_id).first()
        if settings:
            if settings.default_view == 'day':
                return {'start__gte': DateTimeUtil.yesterday(), 'end__lt': DateTimeUtil.tomorrow()}
            if settings.default_view == 'week':
                today = timezone.now().date()
                return {
                    'start__gte': DateTimeUtil.days_before_start_of_week(today, 0),
                    'end__lt': DateTimeUtil.days_after_end_of_week(today, 1)
                }
            if settings.default_view == 'month':
                today = timezone.now().date()
                return {
                    'start__gte': DateTimeUtil.days_before_start_of_month(today, 0),
                    'end__lt': DateTimeUtil.days_after_end_of_month(today, 1)
                }


class SlotComment(RawModel):
    class Meta:
        db_table = 'slot_comments'

    slot = models.ForeignKey(FreightSlot, on_delete=models.CASCADE)
    comment = models.TextField(max_length=5000)
    archived = models.BooleanField(default=False)

    FILLABLES = [
        'slot_id',
        'comment',
        'archived',
    ]

    def clean(self):
        if self.archived is None:
            self.archived = False
        super().clean()

    @classmethod
    def create(cls, params, kwargs=None, pre_validate_callback=None):
        comment = super().create(params, kwargs, pre_validate_callback)
        comment.__notify()

        return comment

    def __notify(self):  # pylint: disable=unused-private-member
        if self.slot.is_provider(self.created_by) or self.slot.is_sub_provider(self.created_by):
            self.slot.notify_commented_by_provider()

    def __eq__(self, other):
        if not isinstance(other, SlotComment):
            return NotImplemented

        return self.slot_id == other.slot_id and self.comment == other.comment
