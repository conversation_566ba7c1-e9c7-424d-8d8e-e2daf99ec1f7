# Legume and Pasture Hay & Silage	Lucerne	Remain the same		Lucerne Hay	BLANK	Same Grades of Lecerence hay
# Legume and Pasture Hay & Silage	Vetch	Remain the same		Vetch Hay	BLANK	Remain the same
# Legume and Pasture Hay & Silage	Clover	Remain the same		Clover Hay	BLANK	Remain the same
# Legume and Pasture Hay & Silage	Ryegrass	Remain the same		Rye Hay	BLANK	Remain the same
# Legume and Pasture Hay & Silage	Blank	Remain the same		Vetch Hay	BLANK	Remain the same
# Cereal Hay & Silage	Barley	Remain the same		Barley Hay	BLANK	Remain the same
# Cereal Hay & Silage	Oaten	Remain the same		Oaten Hay	BLANK	Remain the same
# Cereal Hay & Silage	Wheaten	Remain the same		Wheaten Hay	BLANK	Remain the same
# Cereal Hay & Silage	Blank	Remain the same		Wheaten Hay	BLANK	Remain the same
# Straw	Barley	Remain the same		Barley Straw	BLANK	Remain the same
# Straw	Oaten	Remain the same		Oaten Straw	BLANK	Remain the same
# Straw	Wheaten	Remain the same		Wheaten Straw	BLANK	Remain the same
# Straw	Blank	Remain the same		Barley Straw	BLANK	Remain the same


from django.core.management import BaseCommand
from pydash import get, compact

from core.cash_board.models import CashPrices
from core.commodities.models import Commodity
from core.company_sites.models import FreightSlot
from core.contracts.models import Contract, TitleTransfer
from core.farms.models import Storage
from core.freights.models import FreightContract, FreightOrder
from core.loads.models import Load
from core.vendor_decs.models import CommodityVendorDec

# old commodity ids
legume_hay_id = 26
cereal_hay_id = 25
straw_id = 42

# new commodity ids
lucerne_hay_id = 62
clover_hay_id = 63
rye_hay_id = 65
vetch_hay_id = 66
barley_hay_id = 60
oaten_hay_id = 59
wheaten_hay_id = 61
barley_straw_id = 68
oaten_straw_id = 67
wheaten_straw_id = 69

# old commodities
legume_hay = Commodity.objects.filter(id=legume_hay_id).first()
cereal_hay = Commodity.objects.filter(id=cereal_hay_id).first()
straw = Commodity.objects.filter(id=straw_id).first()

# new commodities
lucerne_hay = Commodity.objects.get(id=lucerne_hay_id)
vetch_hay = Commodity.objects.get(id=vetch_hay_id)
clover_hay = Commodity.objects.get(id=clover_hay_id)
rye_hay = Commodity.objects.get(id=rye_hay_id)
barley_hay = Commodity.objects.get(id=barley_hay_id)
oaten_hay = Commodity.objects.get(id=oaten_hay_id)
wheaten_hay = Commodity.objects.get(id=wheaten_hay_id)
barley_straw = Commodity.objects.get(id=barley_straw_id)
oaten_straw = Commodity.objects.get(id=oaten_straw_id)
wheaten_straw = Commodity.objects.get(id=wheaten_straw_id)


def get_grade_name(grade_name):
    name = grade_name.lower()
    if 'a1' in name:
        return 'A1'
    if 'a2' in name:
        return 'A2'
    if 'a3' in name:
        return 'A3'
    if 'a4' in name:
        return 'A4'
    if 'b1' in name:
        return 'B1'
    if 'b2' in name:
        return 'B2'
    if 'b3' in name:
        return 'B3'
    if 'b4' in name:
        return 'B4'
    if 'c1' in name:
        return 'C1'
    if 'c2' in name:
        return 'C2'
    if 'c3' in name:
        return 'C3'
    if 'c4' in name:
        return 'C4'
    if 'd1' in name:
        return 'D1'
    if 'd2' in name:
        return 'D2'
    if 'd3' in name:
        return 'D3'
    if 'd4' in name:
        return 'D4'
    if 'ungraded' in name:
        return 'UNGRADED'
    return 'UNGRADED'


def process(
        klass, from_commodity, from_variety_name, to_commodity,
        has_variety_id=True, grade_field_name='grade', manager='objects'
):
    manager = get(klass, manager)
    queryset = manager.filter(commodity_id=from_commodity.id)
    if has_variety_id:
        if from_variety_name:
            queryset = queryset.filter(variety__name__in=[from_variety_name, from_variety_name.lower()])
        else:
            queryset = queryset.filter(variety_id__isnull=True)
    count = queryset.count()
    processed = 0
    failed = 0
    for instance in queryset:
        processed += 1
        instance.commodity = to_commodity
        if has_variety_id:
            instance.variety_id = None
        if get(instance, f"{grade_field_name}_id"):
            grade_name = get_grade_name(get(instance, f'{grade_field_name}.name'))
            setattr(instance, grade_field_name, to_commodity.grade_set.filter(name__icontains=grade_name).first())
        instance.save()
        if instance.errors:
            failed += 1
            print(f"{instance.__class__} ERRORS: {instance.errors}")
    print(f"total={count} | processed={processed} | failed={failed}")


def get_loads(from_commodity, from_variety_name, to_commodity):
    loads = []
    queryset = Load.objects.filter(commodity_id=from_commodity.id)
    if from_variety_name:
        queryset = queryset.filter(variety__name__in=[from_variety_name, from_variety_name.lower()])
    else:
        queryset = queryset.filter(variety_id__isnull=True)
    for instance in queryset:
        payload = {'id': instance.id, 'commodity': to_commodity, 'variety': None}
        if instance.grade_id:
            grade_name = get_grade_name(instance.grade.name)
            payload['grade_id'] = to_commodity.grade_set.filter(name__icontains=grade_name).first().id
        loads.append(payload)
        
    return loads


def process_slots(from_commodity, to_commodity):
    movement_queryset = FreightSlot.objects.filter(commodity_id=from_commodity.id, movement_id__isnull=False)
    count = movement_queryset.count()
    processed = 0
    failed = 0
    for slot in movement_queryset:
        processed += 1
        movement = slot.movement
        slot.commodity_id = movement.commodity_id
        slot.grade_id = movement.planned_grade_id
        slot.save()
        if slot.errors:
            failed += 1

    order_queryset = FreightSlot.objects.filter(commodity_id=from_commodity.id, order_id__isnull=False)
    count += order_queryset.count()
    for slot in order_queryset:
        processed += 1
        order = slot.order
        slot.commodity_id = order.commodity_id
        slot.grade_id = order.planned_grade_id
        slot.save()
        if slot.errors:
            failed += 1

    remaining_queryset = FreightSlot.objects.filter(commodity_id=from_commodity.id)
    count += remaining_queryset.count()
    for instance in remaining_queryset:
        processed += 1
        instance.commodity = to_commodity
        if instance.grade_id:
            grade_name = get_grade_name(instance.grade.name)
            instance.grade = to_commodity.grade_set.filter(name__icontains=grade_name).first()
        instance.save()
        if instance.errors:
            failed += 1

    print(f"total={count} | processed={processed} | failed={failed}")


def process_spreads():
    print(f"Processing Spreads...")
    queryset = Contract.objects.filter(
        spread__isnull=False,
        commodity_id__in=[
            lucerne_hay_id, clover_hay_id,
            rye_hay_id, vetch_hay_id,
            barley_hay_id, oaten_hay_id,
            wheaten_hay_id, barley_straw_id,
            oaten_straw_id, wheaten_straw_id
        ]
    )
    count = queryset.count()
    processed = 0
    failed = 0
    for contract in queryset:
        processed += 1
        spread = contract.spread
        details = spread.details
        new_details = []
        for detail in details:
            name = detail.get('name')
            grade_name = get_grade_name(name)
            grade = contract.commodity.grade_set.filter(name__icontains=grade_name).first()
            detail['name'] = grade.name
            detail['id'] = grade.id
            new_details.append(detail)
        spread.details = new_details
        spread.save()
        if spread.errors:
            failed += 1
    print(f"total={count} | processed={processed} | failed={failed}")


class Command(BaseCommand):
    help = 'Migrate old Hay/Straw commodities to new Hay/Straw Commodities'

    def handle(self, *args, **options):
        def __process(klass, has_variety_id=True, manager='objects'):
            print(f"Processing {klass.__name__}s...")
            grade_field_name = 'planned_grade' if klass in [FreightOrder, FreightContract] else 'grade'

            # Legume and Pasture Hay & Silage
            if legume_hay:
                process(klass, legume_hay, 'Lucerne', lucerne_hay, has_variety_id, grade_field_name, manager)
                process(klass, legume_hay, 'Vetch', vetch_hay, has_variety_id, grade_field_name, manager)
                process(klass, legume_hay, 'Clover', clover_hay, has_variety_id, grade_field_name, manager)
                process(klass, legume_hay, 'Ryegrass', rye_hay, has_variety_id, grade_field_name, manager)
                process(klass, legume_hay, None, vetch_hay, has_variety_id, grade_field_name, manager)

            # Cereal Hay & Silage
            if cereal_hay:
                process(klass, cereal_hay, 'Barley', barley_hay, has_variety_id, grade_field_name, manager)
                process(klass, cereal_hay, 'Oaten', oaten_hay, has_variety_id, grade_field_name, manager)
                process(klass, cereal_hay, 'Wheaten', wheaten_hay, has_variety_id, grade_field_name, manager)
                process(klass, cereal_hay, None, wheaten_hay, has_variety_id, grade_field_name, manager)

            # Straw
            if straw:
                process(klass, straw, 'Barley', barley_straw, has_variety_id, grade_field_name, manager)
                process(klass, straw, 'Oaten', oaten_straw, has_variety_id, grade_field_name, manager)
                process(klass, straw, 'Wheaten', wheaten_straw, has_variety_id, grade_field_name, manager)
                process(klass, straw, None, barley_straw, has_variety_id, grade_field_name, manager)

        def __process_slots():
            print("Processing FreightSlots...")

            # Legume and Pasture Hay & Silage
            if legume_hay:
                process_slots(legume_hay, vetch_hay)

            # Cereal Hay & Silage
            if cereal_hay:
                process_slots(cereal_hay, wheaten_hay)

            # Straw
            if straw:
                process_slots(straw, barley_straw)
            
        def __process_loads():
            print("Processing Loads...")
            payloads = []

            # Legume and Pasture Hay & Silage
            if legume_hay:
                payloads += get_loads(legume_hay, 'Lucerne', legume_hay)
                payloads += get_loads(legume_hay, 'Vetch', vetch_hay)
                payloads += get_loads(legume_hay, 'Clover', clover_hay)
                payloads += get_loads(legume_hay, 'Ryegrass', rye_hay)
                payloads += get_loads(legume_hay, None, vetch_hay)

            # Cereal Hay & Silage
            if cereal_hay:
                payloads += get_loads(cereal_hay, 'Barley', barley_hay)
                payloads += get_loads(cereal_hay, 'Oaten', oaten_hay)
                payloads += get_loads(cereal_hay, 'Wheaten', wheaten_hay)
                payloads += get_loads(cereal_hay, None, wheaten_hay)

            # Straw
            if straw:
                payloads += get_loads(straw, 'Barley', barley_straw)
                payloads += get_loads(straw, 'Oaten', oaten_straw)
                payloads += get_loads(straw, 'Wheaten', wheaten_straw)
                payloads += get_loads(straw, None, barley_straw)

            load_ids = []
            count = len(payloads)
            for payload in payloads:
                load_id = payload.get('id')
                Load.objects.filter(id=load_id).update(**{k: v for k, v in payload.items() if k != 'id'})
                load_ids.append(load_id)

            loads = Load.objects.filter(id__in=load_ids, storage_id__isnull=False)
            storage_ids = compact(set(loads.values_list('storage_id', flat=True)))
            for storage_id in storage_ids:
                commodity_ids = set(loads.filter(storage_id=storage_id).values_list('commodity_id', flat=True))
                storage = Storage.objects.filter(id=storage_id).first()
                if storage:
                    for commodity_id in commodity_ids:
                        storage.recreate_storage_levels_from_loads_inline(
                            {'storage_id': storage_id, 'commodity_id': commodity_id})

            print(f"total={count}")

        __process(Contract)
        process_spreads()
        __process(FreightOrder)
        __process(FreightContract)
        __process(TitleTransfer)
        __process(klass=Storage, manager='all')
        __process(CommodityVendorDec)
        __process(CashPrices, False)
        __process_slots()
        __process_loads()





