# Generated by Django 2.1.3 on 2019-01-25 18:50

from django.conf import settings
import django.contrib.postgres.fields.jsonb
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import simple_history.models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('regions', '0005_auto_20190125_0452'),
        ('locations', '0007_auto_20190125_0452'),
        ('marketzones', '0006_auto_20190125_0452'),
        ('companies', '0021_auto_20190125_0734'),
    ]

    operations = [
        migrations.CreateModel(
            name='BHCSite',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('site', models.CharField(max_length=50)),
                ('phone', models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')])),
                ('track', models.CharField(max_length=50)),
                ('ld', models.FloatField()),
                ('mode', models.CharField(max_length=255)),
                ('area', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('site_manager', models.CharField(blank=True, max_length=255, null=True)),
                ('state', models.CharField(max_length=50)),
                ('ntp', models.CharField(max_length=50)),
                ('mobile', models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')])),
                ('address', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='bhc_site_location', to='locations.Location')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='companies.Company')),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_bhcsite_related_created_by', related_query_name='companies_bhcsites_created_by', to=settings.AUTH_USER_MODEL)),
                ('market_zone', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='marketzones.Marketzone')),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='regions.Region')),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='companies_bhcsite_related_updated_by', related_query_name='companies_bhcsites_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'bhc_sites',
            },
        ),
        migrations.CreateModel(
            name='HistoricalBHCSite',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('site', models.CharField(max_length=50)),
                ('phone', models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')])),
                ('track', models.CharField(max_length=50)),
                ('ld', models.FloatField()),
                ('mode', models.CharField(max_length=255)),
                ('area', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('site_manager', models.CharField(blank=True, max_length=255, null=True)),
                ('state', models.CharField(max_length=50)),
                ('ntp', models.CharField(max_length=50)),
                ('mobile', models.CharField(blank=True, max_length=10, null=True, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08)[0-9]+$', 'Must start with 02, 03, 04, 07 or 08')])),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_date', models.DateTimeField()),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('address', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='locations.Location')),
                ('company', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='companies.Company')),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('market_zone', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='marketzones.Marketzone')),
                ('region', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='regions.Region')),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical bhc site',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': 'history_date',
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
