from django.http import Http404
from rest_framework import status
from rest_framework.generics import ListAPIView, RetrieveAPIView
from rest_framework.permissions import IsAuthenticatedOrReadOnly, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView
from pydash import get, compact

from core.commodities.serializers import (
    CommodityMobileSerializer,
    GradeMinimalSerializer,
    VarietyMinimalSerializer,
    CommodityMinimalWithSpecsSerializer,
    GradeWithCommodityNameSerializer,
    MaterialSerializer,
    GradeSerializer,
    VarietySerializer, CommoditySerializer
)
from core.common.utils import get_request_country_id
from core.timezones.utils import DateTimeUtil
from .models import Commodity, Grade, Material
from .models import Variety


class CommoditiesView(APIView):
    permission_classes = [IsAuthenticatedOrReadOnly]
    def get(self, request):
        params = request.query_params.get('includes', '')
        inclusions = params.split(',') if params else []
        include_unknown = request.query_params.get('includeUnknown', 'false').lower() == 'true'
        queryset = Commodity.everything(inclusions=inclusions, include_unknown=include_unknown)
        queryset = queryset.filter(country_id=request.query_params.get('countryId', get_request_country_id()))
        return Response(
            CommoditySerializer(queryset, many=True, context={'inclusions': inclusions}).data, status=status.HTTP_200_OK
        )


class CommodityGradesView(APIView):
    permission_classes = [IsAuthenticatedOrReadOnly]
    def get(self, request, commodity_id):
        params = request.query_params
        try:
            commodity = Commodity.objects.get(id=commodity_id)
            result = commodity.grades(params=params)
        except Commodity.DoesNotExist:
            return Response(status=status.HTTP_404_NOT_FOUND)
        except Variety.DoesNotExist:
            return Response({'errors': ['Variety does not exist']}, status=status.HTTP_400_BAD_REQUEST)

        if 'errors' in result:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

        return Response(Commodity.qs2dict(queryset=result), status=status.HTTP_200_OK)


class CommoditiesMobileView(ListAPIView):
    serializer_class = CommodityMobileSerializer
    permission_classes = [AllowAny]
    def get_serializer_context(self):
        context = super().get_serializer_context()
        is_brief = self.request.query_params.get('brief', 'false').lower() == 'true'
        context['is_brief'] = is_brief
        return context

    def get_queryset(self):
        epoch = self.request.query_params.get('from')
        is_brief = self.request.query_params.get('brief', 'false').lower() == 'true'
        country_id = self.request.query_params.get('countryId', get_request_country_id())

        fetch = True

        filter_kwargs = {"country_id": country_id}

        if is_brief and epoch:
            epoch_datetime = DateTimeUtil.get_datetime_from_epoch(epoch)
            filter_kwargs["updated_at__gt"] = epoch_datetime

        if not is_brief and epoch:
            epoch_datetime = DateTimeUtil.get_datetime_from_epoch(epoch)
            commodity_updated_at = Commodity.all.filter(
                country_id=country_id).values_list('updated_at', flat=True).order_by('-updated_at').first()
            variety_updated_at = Variety.objects.filter(
                commodity__country_id=country_id).values_list('updated_at', flat=True).order_by('-updated_at').first()
            grade_updated_at = Grade.objects.filter(
                commodity__country_id=country_id).values_list('updated_at', flat=True).order_by('-updated_at').first()

            updated_ats = compact([commodity_updated_at, variety_updated_at, grade_updated_at])

            if updated_ats:
                max_updated_at = max(updated_ats)
                fetch = max_updated_at > epoch_datetime

        if not fetch:
            return []

        if is_brief:
            return Commodity.all.filter(**filter_kwargs)

        return Commodity.everything(inclusions=['variety', 'grade'], include_unknown=True).filter(**filter_kwargs)


class GradesMobileView(ListAPIView):
    serializer_class = GradeSerializer
    permission_classes = [AllowAny]
    def get_queryset(self):
        epoch = self.request.query_params.get('from')
        commodity_id = self.request.query_params.get('commodity_id')

        country_id = self.request.query_params.get('countryId', get_request_country_id())

        filter_kwargs = { "commodity__country_id": country_id }
        if commodity_id:
            filter_kwargs["commodity__id"] = commodity_id

        if epoch:
            epoch_datetime = DateTimeUtil.get_datetime_from_epoch(epoch)
            filter_kwargs['updated_at__gt'] = epoch_datetime

        return Grade.objects.filter(**filter_kwargs)


class VarietiesMobileView(ListAPIView):
    serializer_class = VarietySerializer
    permission_classes = [AllowAny]
    def get_queryset(self):
        epoch = self.request.query_params.get('from')
        commodity_id = self.request.query_params.get('commodity_id')

        country_id = self.request.query_params.get('countryId', get_request_country_id())

        filter_kwargs = { "commodity__country_id": country_id }
        if commodity_id:
            filter_kwargs["commodity__id"] = commodity_id

        if epoch:
            epoch_datetime = DateTimeUtil.get_datetime_from_epoch(epoch)
            filter_kwargs['updated_at__gt'] = epoch_datetime

        return Variety.objects.filter(**filter_kwargs)


class CommodityMinimalView(RetrieveAPIView):
    serializer_class = CommodityMinimalWithSpecsSerializer
    def get_object(self):
        instance = Commodity.objects.filter(id=self.kwargs['commodity_id']).first()
        if not instance:
            raise Http404()
        return instance


class GradeMinimalView(RetrieveAPIView):
    serializer_class = GradeMinimalSerializer
    def get_object(self):
        instance = Grade.objects.filter(id=self.kwargs['grade_id']).first()
        if not instance:
            raise Http404()
        return instance


class VarietyMinimalView(RetrieveAPIView):
    serializer_class = VarietyMinimalSerializer
    def get_object(self):
        instance = Variety.objects.filter(id=self.kwargs['variety_id']).first()
        if not instance:
            raise Http404()
        return instance


class GradeView(ListAPIView):
    serializer_class = GradeWithCommodityNameSerializer
    def get_queryset(self):
        return Grade.objects.filter(
            commodity__country_id=self.request.query_params.get('countryId', get_request_country_id())
        ).select_related('commodity').order_by('commodity_id', 'order', 'name')


class InterChangeableCommoditiesView(APIView):
    def get(self, _, commodity_id):
        commodities = Commodity.get_interchangeable_commodities(commodity_id)
        return Response(Commodity.qs2dict(
            queryset=commodities, one_to_many_relations=['variety', 'grade'], properties=['display_name'],
        ), status=status.HTTP_200_OK)


class MaterialListView(ListAPIView):
    permission_classes = [IsAuthenticatedOrReadOnly]
    queryset = Material.objects.filter()
    serializer_class = MaterialSerializer
    def filter_queryset(self, queryset):
        params = self.request.query_params
        return queryset.filter(
            **DateTimeUtil.get_updated_at_filter(params.get('from')),
            country_id=params.get('countryId', get_request_country_id())
        )


class VarietiesView(APIView):
    def post(self, request):
        variety = Variety.create(request.data)
        if not get(variety, 'errors'):
            return Response(variety.to_dict(), status=status.HTTP_201_CREATED)
        return Response(status=status.HTTP_400_BAD_REQUEST)
