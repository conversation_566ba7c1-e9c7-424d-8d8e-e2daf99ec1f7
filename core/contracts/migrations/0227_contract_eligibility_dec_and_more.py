# Generated by Django 5.1.8 on 2025-06-23 04:48

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0226_auto_20250619_1115'),
        ('vendor_decs', '0051_eligibilitydecrequestcommunication_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='contract',
            name='eligibility_dec',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='contract_eligibility_declaration', to='vendor_decs.eligibilitydeclaration'),
        ),
        migrations.AddField(
            model_name='historicalcontract',
            name='eligibility_dec',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='vendor_decs.eligibilitydeclaration'),
        ),
    ]
