# Generated by Django 2.1.3 on 2018-12-28 05:47

from django.db import migrations


def company_add_to_directory_sync(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    AddedCompany = apps.get_model('companies', 'AddedCompany')

    added_companies = AddedCompany.objects.filter(
        subject_company_id=1
    ).values_list('object_company_id')

    bulk_creates = []

    for company in Company.objects.exclude(id__in=added_companies):
        bulk_creates.append(AddedCompany(subject_company_id=1, object_company=company))

    AddedCompany.objects.bulk_create(bulk_creates)


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0038_auto_20190911_0247'),
    ]

    operations = [
        migrations.RunPython(company_add_to_directory_sync),
    ]
