# Generated by Django 3.2.6 on 2021-09-01 08:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0098_auto_20210820_0832'),
    ]

    operations = [
        migrations.AddField(
            model_name='sitemanagementsettings',
            name='checkpoint_order_creation_permission',
            field=models.CharField(blank=True, choices=[('site_only', 'Site Only'), ('grain_owner_and_site_acceptance_from_site', 'Grain Owner and Site (Acceptance from Site)'), ('grain_owner_and_site', 'Grain owner and Site (Site Acceptance Not Required)')], max_length=200, null=True),
        ),
        migrations.AddField(
            model_name='sitemanagementsettings',
            name='delivery_order_number_required',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='sitemanagementsettings',
            name='pickup_order_number_required',
            field=models.BooleanField(default=False),
        ),
    ]
