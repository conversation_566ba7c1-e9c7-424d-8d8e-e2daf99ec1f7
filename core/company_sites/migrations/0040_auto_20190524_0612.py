# Generated by Django 2.1.7 on 2019-05-24 06:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('company_sites', '0039_auto_20190524_0547'),
    ]

    operations = [
        migrations.CreateModel(
            name='SlotComment',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('comment', models.TextField(max_length=5000)),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='company_sites_slotcomment_related_created_by', related_query_name='company_sites_slotcomments_created_by', to=settings.AUTH_USER_MODEL)),
                ('slot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='company_sites.FreightSlot')),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='company_sites_slotcomment_related_updated_by', related_query_name='company_sites_slotcomments_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'slot_comments',
            },
        ),
    ]
