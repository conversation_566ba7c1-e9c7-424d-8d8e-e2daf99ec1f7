import glob
import os
from datetime import datetime

import pytz
from colour_runner.django_runner import ColourRunnerMixin
from django.conf import settings
from django.core.management import call_command
from django.db import models, connections
from django.test import override_settings
from django.test import tag
from django.test.runner import DiscoverRunner
from django.test.testcases import TestCase
from django.utils import timezone
from mock import patch, Mock, ANY, call, PropertyMock
from pydash import compact
from rest_framework.test import APITestCase

from core.common.constants import AU_ROOT_USER_ID, STATIC_URL_PREFIX
from core.common.models import BaseModel, HierarchyObject, CommonFilters
from core.common.templatetags.app_filters import (str_to_date, format_price, human_price, aest_date_format,
                                                  localize_tz, to_quantity_unit)
from core.common.utils import (
    is_staging,
    is_production,
    remove_duplicate_elements_by_key,
    chunked_queryset_iterator,
    set_current_user,
    get_current_user,
    snakeize, invert_dict, to_display_attr, replace_nullable_int_with_null, to_query_filters,
    to_fsm_field_filters,
    deepgetattr, flatten, get_unique_object_list,
    get_grade_name, assign_key_contact, camelize,
    get_base64_content,
    is_qa, get_full_function_name, get_changeset,
    send_mail_with_pdf_attachment,
    get_attachment_header_value,
    to_filter_clause,
    format_number,
    generate_identifier, get_filename_with_timestamp, get_grade_display_name, is_super_admin, smart_join,
    is_continuous_list_of_numbers, convert_tuples_to_dict, set_request_url, get_request_url,
    remove_elements_from_list, chunks, get_klass, to_number,
    to_int_list, get_valid_float_from_string
)
from core.common.float_utils import bankers_round
from core.companies.mock_data import COMPANY_VALID_MOCK_DATA, BHC_COMPANY_VALID_MOCK_DATA
from core.companies.models import Company, CompanyType
from core.companies.tests.factories import CompanyFactory
from core.contracts.constants import STATUSES, get_csv_headers, COMMODITY_CONTRACT_INVOICE
from core.contracts.models import Contract, ContractDocumentType
from core.farm_fields.tests.factories import FarmFieldFactory
from core.farms.tests.factories import FarmFactory, StorageFactory
from core.freights.models import FreightOrder, FreightContract
from core.invoices.models import Invoice
from core.loads.models import Load
from core.locations.mock_data import LOCATION_FARM_MOCK_DATA
from core.locations.models import Location
from core.locations.tests.factories import AddressFactory
from core.ngrs.tests.factories import NgrFactory
from core.profiles.mock_data import (EMPLOYEE_VALID_MOCK_DATA, EMPLOYEE_FILTER_MOCK_DATA,
    EMPLOYEE_INVOICE_FILTER_MOCK_DATA)
from core.profiles.models import Employee, EmployeeType, EmployeeViewFilters
from core.profiles.tests.factories import EmployeeFactory
from core.states.models import State
from core.trucks.mock_data import TRUCK_VALID_MOCK_DATA_1
from core.trucks.models import Truck
from .pdfgenerator import PDFGenerator
from ..ngrs.models import Ngr

run_seed = True


def run_seed_if_needed():
    global run_seed  # pylint: disable=global-statement
    if run_seed:
        call_command("loaddata", "core/fixtures/core_entities.yaml")
        call_command("loaddata", "core/test_fixtures/commodities.yaml")
        call_command("loaddata", "core/test_fixtures/commodity_varieties.json")
        call_command("loaddata", "core/test_fixtures/wheat_epr.json")
        call_command("loaddata", "core/test_fixtures/commodity_grades.json")
        call_command("loaddata", "core/fixtures/freight_contract_types.yaml")
        call_command('loaddata', 'core/fixtures/states.yaml')
        call_command('loaddata', 'core/fixtures/marketzones.yaml')
        call_command('loaddata', 'core/fixtures/regions.yaml')
        call_command('loaddata', 'core/test_fixtures/test_banks.json')
        call_command('loaddata', 'core/fixtures/checkpoints.yaml')
        call_command('loaddata', 'core/fixtures/contract_conveyances.yaml')
        call_command('loaddata', 'core/fixtures/contract_document_types.yaml')
        call_command('loaddata', 'core/fixtures/contract_price_points.yaml')
        call_command('loaddata', 'core/fixtures/contract_types.yaml')
        call_command('loaddata', 'core/fixtures/packagings.yaml')
        call_command('loaddata', 'core/fixtures/payment_scales.yaml')
        call_command('loaddata', 'core/fixtures/tolerances.yaml')
        run_seed = False


class ACTestCase(TestCase):
    @staticmethod
    def read_files(prefix):
        if prefix:
            return glob.glob(f'/tmp/{prefix}')
        return []

    @staticmethod
    def remove_temp_files(prefix):
        if prefix:
            files = ACTestCase.read_files(prefix)
            for file in files:
                os.remove(file)

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        if settings.RUN_TEST_FIXTURES:
            run_seed_if_needed()

    def setUp(self):
        super().setUp()
        self.maxDiff = None


class CustomTestRunner(ColourRunnerMixin, DiscoverRunner):
    pass


class ACAPITestCase(APITestCase):
    def setUp(self):
        super().setUp()
        self.maxDiff = None

    @classmethod
    def setUpClass(cls):
        if settings.RUN_TEST_FIXTURES:
            for conn in connections.all():
                conn.connect()
            run_seed_if_needed()
        else:
            super().setUpClass()

    @classmethod
    def tearDownClass(cls):
        if settings.RUN_TEST_FIXTURES:
            if all(conn.features.supports_transactions for conn in connections.all()):
                for conn in connections.all():
                    conn.close()
        else:
            super().tearDownClass()


class AuthSetup(ACAPITestCase):
    def setUp(self):
        super().setUp()
        self.maxDiff = None
        self.company = Company.objects.filter(abn='***********').first() or CompanyFactory(
            abn='***********', type_id=2, business_name='Foo Corporation')
        self.ngr = Ngr.create_unknown(self.company)
        self.employee = Employee.objects.filter(email='<EMAIL>').first() or EmployeeFactory(
            email='<EMAIL>', username='<EMAIL>', company=self.company)
        self.employee.set_password('Password1$')
        self.employee.save()
        self.token = self.employee.refresh_token()


class TestFarmSetup(AuthSetup):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        super().setUp()

        self.grower_company = CompanyFactory()
        self.truck = Truck.create({
            **TRUCK_VALID_MOCK_DATA_1,
            'company_id': self.company.id,
        })
        self.location = Location.create({**LOCATION_FARM_MOCK_DATA})
        self.farm = FarmFactory(address_id=self.location.id, company_id=self.grower_company.id)
        self.location_1 = Location.create({**LOCATION_FARM_MOCK_DATA})
        self.farm_1 = FarmFactory(address_id=self.location_1.id, company_id=self.grower_company.id)
        self.farm_field = FarmFieldFactory(farm=self.farm, company=self.grower_company)
        self.ngr = NgrFactory(company=self.grower_company)
        self.storage = StorageFactory(farm=self.farm)
        self.bhc_company = Company.create_with_location(BHC_COMPANY_VALID_MOCK_DATA)
        self.bhc_site = FarmFactory(mode='Rail', company=self.bhc_company)
        self.storage_wheat = StorageFactory(farm=self.farm)


class ContractSetup(AuthSetup):  # pylint: disable=too-many-instance-attributes
    def setup_contract_data(self):
        self.seller_company = CompanyFactory(type_id=2)
        self.buyer_company = CompanyFactory(type_id=2)
        self.seller_contact = EmployeeFactory(company=self.seller_company)
        params = EMPLOYEE_VALID_MOCK_DATA.copy()
        params.update({'email': '<EMAIL>', 'username': '<EMAIL>'})
        self.buyer_contact = EmployeeFactory(company=self.buyer_company)
        self.buyer_ngr = NgrFactory(company=self.buyer_company)
        self.location = AddressFactory()
        self.seller_farm = FarmFactory(company=self.seller_company)
        self.buyer_farm = FarmFactory(company=self.buyer_company)
        self.seller_ngr = NgrFactory(company=self.seller_company)
        self.seller_farm_field = FarmFieldFactory(farm=self.seller_farm, company=self.seller_company)
        self.buyer_storage = StorageFactory(farm=self.buyer_farm)

    def get_contract_params(self):
        return {
            'seller': {
                'company_id': self.seller_company.id,
                'contact_id': self.seller_contact.id,
                'ngr_id': self.seller_ngr.id,
            },
            'buyer': {
                'company_id': self.buyer_company.id,
                'contact_id': self.seller_contact.id,
                'ngr_id': self.seller_ngr.id,
            },
            'consignors': [{
                'handler_id': self.seller_farm.id,
                'sites': [{
                    'location_type': 'farm_field',
                    'location_id': self.seller_farm_field.id,
                }],
            }],
            'consignees': [
                {
                    'handler_id': self.buyer_farm.id,
                    'sites': [{
                        'location_type': 'storage',
                        'location_id': self.buyer_storage.id,
                    }],
                }
            ],
            'administration': {
                'brokered_by_id': self.seller_company.id,
                'broker_contact_id': self.seller_contact.id,
            },
            'document_type_id': 14,
            'commodity_id': 1,
            'identifier': '1234567890',
            'contract_number': '2234567890',
            'owner_id': self.seller_company.id,
            'type_id': 1,
            'variety_id': 1,
            'grade_id': 1,
            'season': '17/18',
            'price_point_id': 1,
            'tonnage': 172.333,
            'price': 183.567,
            'delivery_onus': 'Seller',
            'payment_scale_id': 1,
            'payment_term_id': 1,
            'tolerance_id': 1,
            'market_zone_id': 1,
            'delivery_start_date': '2019-01-01',
            'delivery_end_date': '2020-01-01',
            'spread': {
                'details': {
                    'SFW1': '2',
                    'AH1': '0',
                    'AH15': '2'
                }
            }
        }


@tag('model')
class BaseModelTest(ACTestCase):
    def test_entity(self):
        self.assertEqual(State().entity, 'state')

    def test_to_dict(self):
        state = State()
        expected = {
            'id': None,
            'is_active': True,
            'created_at': state.created_at,
            'updated_at': None,
            'created_by_id': 1,
            'name': '',
            'updated_by_id': 1,
            'country_id': 1,
            'entity': 'state',
        }
        self.assertEqual(state.to_dict(), expected)

    def test_to_dict_with_properties(self):
        self.assertFalse(State().to_dict(properties=['persisted']).get('persisted'))

    def test_to_dict_with_relations(self):
        _company = Company.get_or_create(COMPANY_VALID_MOCK_DATA)
        _employee = Employee(
            **EMPLOYEE_VALID_MOCK_DATA,
            company_id=_company.id
        )
        _employee.save()

        _company = _company.to_dict(
            many_to_one_relations=['type'],
            one_to_many_relations=['employee']
        )

        self.assertIsNotNone(_company.get('type', None))
        self.assertIsNotNone(_company.get('employees', None))

    def test_qs2dict(self):
        _states = State.qs2dict(
            one_to_many_relations=['marketzone'],
            one_to_many_fields_with_relations={
                'marketzone': {
                    'fields': ['id'],
                    'relations': ['state']
                }
            }
        )
        _dict = {
            'created_by_id': AU_ROOT_USER_ID,
            'is_active': True,
            'updated_by_id': AU_ROOT_USER_ID,
        }
        for _attr, _val in _dict.items():
            self.assertEqual(
                _states[0][_attr],
                _val
            )
        self.assertTrue(
            isinstance(_states[0]['marketzones'], list)
        )
        self.assertEqual(
            sorted(_states[0]['marketzones'][0].keys()),
            ['entity', 'id', 'is_active', 'state_id', 'states', 'wheat_classification_zone_lookup_key']
        )

    def test_qs2dict_with_relations(self):
        _company = Company.get_or_create(COMPANY_VALID_MOCK_DATA)
        _employee = Employee(
            **EMPLOYEE_VALID_MOCK_DATA,
            company_id=_company.id
        )

        _employee.save()

        _employee = _company.employee_set.all()
        _employees = Company.qs2dict(
            queryset=_employee,
            many_to_one_relations=['company']
        )
        self.assertEqual(len(_employees), 1)

    def test_qs2dict_one_to_one_relations(self):
        CompanyType.create({'name': 'broker', 'id': 2})
        EmployeeType.create({'name': 'company_admin', 'id': 1})
        _company = Company.get_or_create(COMPANY_VALID_MOCK_DATA)
        _employee = Employee(
            **EMPLOYEE_VALID_MOCK_DATA,
            company_id=_company.id
        )

        _employee.save()

        _queryset = Company.objects.prefetch_related('address', 'employee_set__type').filter(
            id=_company.id
        )
        _companies = Company.qs2dict(
            queryset=_queryset,
            one_to_one_relations=['address', '*employee__type']
        )
        self.assertEqual(len(_companies), 1)

    def test_qs2dict_nested_one_to_one_relations(self):
        EmployeeFactory()

        queryset = Employee.objects.exclude(is_superuser=True).prefetch_related('company__address').all()
        employees = Employee.qs2dict(
            queryset=queryset,
            one_to_one_relations=['company__address']
        )
        self.assertEqual(len(employees), 1)

    def test_content_type_ids_for(self):
        farm_ct_id, comp_ct_id, emp_ct_id = BaseModel.content_type_ids_for(
            model_names=['farm', 'company', 'employee']
        )
        self.assertIsNotNone(farm_ct_id)
        self.assertIsNotNone(comp_ct_id)
        self.assertIsNotNone(emp_ct_id)

    @patch('core.loads.models.Load.history')
    def test_updated_last_since(self, history_manager_mock):
        loads_history_data = [
            Mock(id=1, history_date=datetime(2011, 1, 1, 11, 1), instance='load-1'),
            Mock(id=1, history_date=datetime(2011, 1, 1, 11), instance='load-1'),
            Mock(id=2, history_date=datetime(2012, 1, 1, 12, 12, 12), instance='load-2'),
        ]
        date_filter_mock = Mock()
        date_filter_mock.filter = Mock(return_value=loads_history_data)
        history_manager_mock.filter = Mock(return_value=date_filter_mock)

        self.assertEqual(
            Load.updated_last_since('2011-01-01 11:00:01', {'status': 'delivered'}),
            ['load-1', 'load-2']
        )
        history_manager_mock.filter.assert_called_once_with(
            history_date__gte='2011-01-01 11:00:01'
        )
        date_filter_mock.filter.assert_called_once_with(status='delivered')

    def test_is_match(self):
        contract = Contract()
        user_mock = Mock()
        self.assertFalse(contract.is_match('foo', user_mock))

        contract.status = 'confirmed'
        self.assertTrue(contract.is_match('Conf', user_mock))
        contract.season = '18/19'
        self.assertTrue(contract.is_match('18', user_mock))
        self.assertFalse(contract.is_match('18100000', user_mock))


@tag('model')
class InstanceCustomFuncMixinTest(TestCase):
    def test_init(self):
        contract = Contract()

        self.assertFalse(contract.has_tonnage())
        contract.tonnage = 10
        self.assertTrue(contract.has_tonnage())

        self.assertFalse(contract.has_price_point_id())
        contract.price_point_id = 6
        self.assertTrue(contract.has_price_point_id())

        self.assertFalse(contract.is_delivered())
        self.assertTrue(contract.is_planned())


@tag('model')
class AppFiltersTest(TestCase):
    def test_str_to_date(self):
        self.assertIsNone(str_to_date(None, '%Y-%m-%d'))
        self.assertEqual(str_to_date('2019-03-18', '%Y-%m-%d'), datetime(year=2019, month=3, day=18).date())

    def test_format_price(self):
        self.assertIsNone(format_price(None))
        self.assertEqual(format_price(123), 'AU$ 123.00')
        self.assertEqual(format_price('123'), 'AU$ 123')
        self.assertEqual(format_price('AU$ 123'), 'AU$ 123')
        self.assertEqual(format_price('foo %'), 'foo %')
        self.assertEqual(format_price(123.33), 'AU$ 123.33')
        self.assertEqual(format_price('foo-bar'), 'AU$ foo-bar')
        self.assertEqual(format_price(123123123), 'AU$ 123,123,123.00')
        self.assertEqual(format_price(123123123.1), 'AU$ 123,123,123.10')
        self.assertEqual(format_price(123123123.091), 'AU$ 123,123,123.09')

    def test_human_price(self):
        self.assertIsNone(human_price(None))
        self.assertEqual(human_price(123), 'AU$ 123.00')
        self.assertEqual(human_price('123'), 'AU$ 123.00')
        self.assertEqual(human_price(123.33), 'AU$ 123.33')

    def test_to_quantity_unit(self):
        self.assertIsNone(to_quantity_unit(None))
        self.assertEqual(to_quantity_unit('foo mt', 'm³'), 'foo m³')
        self.assertEqual(to_quantity_unit('foo mt', 'kg'), 'foo kg')
        self.assertEqual(to_quantity_unit('100mt', 'm³'), '100m³')
        self.assertEqual(to_quantity_unit('100mt', 'kg'), '100kg')
        self.assertEqual(to_quantity_unit('mt', 'm³'), 'm³')
        self.assertEqual(to_quantity_unit('mt', 'kg'), 'kg')

    def test_to_aest_date_format(self):
        self.assertEqual(aest_date_format(''), '')
        utc_date_time = datetime(2020, 9, 29, 23, 4, 42, tzinfo=pytz.UTC)
        self.assertEqual(aest_date_format(utc_date_time), 'Sep 30, 2020')

    @patch('core.common.templatetags.app_filters.get_request_tz')
    def test_localize_tz(self, request_tz_mock):
        request_tz_mock.return_value = 'Asia/Calcutta'
        utc_date_time = datetime(2020, 9, 29, 23, 4, 42, tzinfo=pytz.UTC)
        self.assertEqual(
            localize_tz(utc_date_time, '%d/%m/%Y %I:%M %p'),
            '30/09/2020 04:34 AM'
        )
        request_tz_mock.return_value = None
        utc_date_time = datetime(2020, 9, 29, 23, 4, 42, tzinfo=pytz.UTC)
        self.assertEqual(
            localize_tz(utc_date_time, '%d/%m/%Y %I:%M %p'),
            '30/09/2020 09:04 AM'
        )


@tag('model')
class HierarchyObjectTest(TestCase):
    def test_to_dict(self):
        user = Mock()
        child_obj = Mock(web_href='child-href', inferred_tonnage=5, direct_descendants=[], entity='freightorder')
        obj = Mock(web_href='href', inferred_tonnage=10, direct_descendants=[child_obj], entity='contract')
        obj.tonnage_distribution = Mock(return_value='tonnage-distribution')
        obj.verbose_description = Mock(return_value='verbose-description')
        child_obj.tonnage_distribution = Mock(return_value='child-tonnage-distribution')
        child_obj.verbose_description = Mock(return_value='child-verbose-description')

        self.assertEqual(
            HierarchyObject(obj=obj, user=user, origin=True).to_dict(),
            {
                'entity': 'contract',
                'origin': True,
                'label': 'verbose-description',
                'href': 'href',
                'total': 10,
                'bars': 'tonnage-distribution',
                'children': [
                    {
                        'origin': False,
                        'entity': 'freightorder',
                        'label': 'child-verbose-description',
                        'href': 'child-href',
                        'total': 5,
                        'bars': 'child-tonnage-distribution',
                        'children': []
                    }
                ]
            }
        )
        obj.tonnage_distribution.assert_called_once_with(user)
        obj.verbose_description.assert_called_once_with(user)
        child_obj.tonnage_distribution.assert_called_once_with(user)
        child_obj.verbose_description.assert_called_once_with(user)


@tag('model')
class PDFGeneratorTest(ACTestCase):
    @patch('core.common.pdfgenerator.render_to_string')
    @patch('core.common.pdfgenerator.S3')
    @patch('core.common.pdfgenerator.HTMLToPDF')
    def test_generate_contract(self, mock_html_to_pdf, mock_s3, mock_render_to_string):
        document_type = ContractDocumentType(name='name')
        contract = Contract(id=1, document_type=document_type, identifier='CC')
        mock_render_to_string.return_value = 'Test String'
        mock_html_to_pdf.from_string = Mock(return_value='Content')
        contract.pdf_path = Mock(return_value='PDF PATH')
        mock_s3.upload = Mock()

        pdf_generator = PDFGenerator(contract, party='seller', upload=True)
        self.assertEqual(pdf_generator.get_pdf_name(), 'CC.pdf')
        pdf_generator.generate()
        self.assertTrue(mock_render_to_string.called)
        mock_render_to_string.assert_called_once_with(
            'preview.html',
            {
                'contract': contract,
                'agrichain_logo_preview_url': None,
                'act': False,
                'urlprefix': STATIC_URL_PREFIX,
                'grade_name': None,
                'footer': True,
                'party': 'seller',
            }
        )
        mock_html_to_pdf.from_string.assert_called_with('Test String', None)
        mock_s3.upload.assert_called_with('PDF PATH', 'Content')

    @patch('core.common.pdfgenerator.render_to_string')
    @patch('core.common.pdfgenerator.S3')
    @patch('core.common.pdfgenerator.HTMLToPDF')
    def test_generate_order(self, mock_html_to_pdf, mock_s3, mock_render_to_string):
        user = EmployeeFactory()
        order = FreightOrder(identifier='234234234', type_id=3)
        mock_render_to_string.return_value = 'Test String'
        mock_html_to_pdf.from_string = Mock(return_value='Content')
        order.pdf_path = Mock(return_value='PDF PATH')
        mock_s3.upload = Mock()
        pdf_generator = PDFGenerator(order, party='common', user=user)
        self.assertEqual(pdf_generator.get_pdf_name(), '234234234.pdf')
        pdf_generator.generate()
        self.assertTrue(mock_render_to_string.called)
        mock_render_to_string.assert_called_with(
            order.get_preview_template(),
            {
                'order': order,
                'agrichain_logo_preview_url': None,
                'act': False,
                'urlprefix': STATIC_URL_PREFIX,
                'party': 'common',
                'footer': True,
                'note': '',
                'currency': 'AU$',
                'can_view_pickup': True,
                'can_view_delivery': True,
            }
        )
        order.type_id = 2
        pdf_generator = PDFGenerator(order, party='common', upload=True, user=user)
        pdf_generator.generate()
        self.assertTrue(mock_render_to_string.called)
        mock_render_to_string.assert_called_with(
            order.get_preview_template(),
            {
                'order': order,
                'agrichain_logo_preview_url': None,
                'act': False,
                'urlprefix': STATIC_URL_PREFIX,
                'party': 'common',
                'footer': True,
                'note': '',
                'currency': 'AU$',
                'can_view_pickup': True,
                'can_view_delivery': True,
            }
        )
        mock_html_to_pdf.from_string.assert_called_with('Test String', None)
        mock_s3.upload.assert_called_with('PDF PATH', 'Content')

    @patch('core.common.pdfgenerator.render_to_string')
    @patch('core.common.pdfgenerator.S3')
    @patch('core.common.pdfgenerator.HTMLToPDF')
    def test_generate_movement(self, mock_html_to_pdf, mock_s3, mock_render_to_string):
        movement = FreightContract(identifier='234234234')
        mock_render_to_string.return_value = 'Test String'
        mock_html_to_pdf.from_string = Mock(return_value='Content')
        mock_html_to_pdf.get_footer_url = Mock(return_value='some-url')
        movement.pdf_path = Mock(return_value='PDF PATH')
        mock_s3.upload = Mock()

        user = EmployeeFactory()
        pdf_generator = PDFGenerator(movement, party='common', upload=True, user=user)
        self.assertEqual(pdf_generator.get_pdf_name(), '234234234.pdf')
        pdf_generator.generate()
        self.assertTrue(mock_render_to_string.called)
        mock_render_to_string.assert_called_with(
            'freight_contract_preview.html',
            {
                'movement': movement,
                'agrichain_logo_preview_url': None,
                'act': False,
                'urlprefix': STATIC_URL_PREFIX,
                'party': 'common',
                'footer': True,
                'note': '',
                'currency': 'AU$',
                'can_view_pickup': True,
                'can_view_delivery': True
            }
        )

        mock_html_to_pdf.from_string.assert_called_with('Test String', None)
        mock_s3.upload.assert_called_with('PDF PATH', 'Content')

    @patch('core.common.pdfgenerator.ERRBIT_LOGGER.log')
    @patch('core.invoices.models.Invoice.commodity_contract', new_callable=PropertyMock)
    @patch('core.common.pdfgenerator.render_to_string')
    @patch('core.common.pdfgenerator.S3')
    @patch('core.common.pdfgenerator.HTMLToPDF')
    def test_generate_invoice(self, mock_html_to_pdf, mock_s3, mock_render_to_string, contract_mock, errbit_log_mock):
        invoice = Invoice(identifier='234234234', tonnage=10, type=COMMODITY_CONTRACT_INVOICE,
                          payment_due_date=timezone.now(), total=100.0, sub_total=5.0, gst=5.0)
        invoice.save()
        mock_render_to_string.return_value = 'Test String'
        mock_html_to_pdf.from_string = Mock(return_value='Content')
        mock_html_to_pdf.get_footer_url = Mock(return_value='some-url')
        invoice.pdf_path = Mock(return_value='PDF PATH')
        invoice.movement_item_description = Mock(return_value='FM DESC')
        mock_s3.upload = Mock()
        contract_mock.return_value = 'foo-bar'
        pdf_generator = PDFGenerator(invoice, party='common', upload=True)
        self.assertEqual(pdf_generator.get_pdf_name(), '234234234.pdf')
        pdf_generator.generate()
        self.assertTrue(mock_render_to_string.called)
        party_data = invoice.get_party_and_counter_party_data()
        mock_render_to_string.assert_called_with(
            'commodity_contract_invoice_for_non_seller.html',
            {
                'isPdf': True,
                'contract': 'foo-bar',
                'urlprefix': STATIC_URL_PREFIX,
                'isConfirmable': False,
                'invoice': invoice,
                'movementItems': [],
                'titleTransferItems': [],
                'blendedGradeMovements': [],
                'blendedGradeLoads': [],
                'chemicalApplicationItems': [],
                'carryItems': [],
                'grainLevyItems': [],
                'eprsItems': [],
                'canolaloads': None,
                'showadjustments': True,
                'currency': 'AU$',
                'totalCommodityPrice': 0,
                'customItems': ANY,
                'carryTotal': 0,
                'grainLevyTotal': 0,
                'eprTotal': 0,
                'loadTonnage': 0,
                'grossAmount': 0,
                'invoiceLoads': ANY,
                'subTotal': 5.0,
                'gst': 5.0,
                'totalAmount': 100.0,
                **party_data
            }
        )
        mock_html_to_pdf.from_string.assert_called_with('Test String', None)
        mock_s3.upload.assert_called_with('PDF PATH', 'Content')
        errbit_log_mock.assert_not_called()

        mock_render_to_string.side_effect = IndexError('')
        pdf_generator.generate()
        errbit_log_mock.assert_called()


@tag('view')
class PDFViewTest(AuthSetup, ACTestCase):
    def test_post_400(self):
        response = self.client.post(
            '/common/pdf/',
            {'id': 1, 'entity': None},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.data, {'errors': ['Invalid Request!']})

    @patch('core.common.views.web_views.Contract')
    @patch('core.common.pdfgenerator.PDFGenerator.generate')
    def test_post_200_with_contract(self, pdf_generate_mock, contract_klass_mock):  # pylint: disable=unused-argument
        contract = Contract(id=1, document_type=ContractDocumentType(name='name'), identifier='CC')
        manager_mock = Mock()
        manager_mock.get = Mock(return_value=contract)
        contract_klass_mock.objects = manager_mock
        pdf_generate_mock = Mock(return_value='PDF')

        response = self.client.post(
            '/common/pdf/',
            {'id': 1, 'entity': 'contract', 'party': 'seller'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.filename, 'CC.pdf')
        self.assertTrue(response.as_attachment)

    @patch('core.common.views.web_views.FreightOrder')
    @patch('core.common.pdfgenerator.PDFGenerator.generate')
    def test_post_200_with_order(self, pdf_generate_mock, order_klass_mock):    # pylint: disable=unused-argument
        order = FreightOrder(identifier='234234234', type_id=3)
        manager_mock = Mock()
        manager_mock.get = Mock(return_value=order)
        order_klass_mock.objects = manager_mock
        pdf_generate_mock = Mock(return_value='PDF')

        response = self.client.post(
            '/common/pdf/',
            {'id': 1, 'entity': 'freightorder', 'party': 'common'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.filename, '234234234.pdf')
        self.assertTrue(response.as_attachment)

    @patch('core.common.views.web_views.FreightContract')
    @patch('core.common.pdfgenerator.PDFGenerator.generate')
    def test_post_200_with_movement(self, pdf_generate_mock, contract_klass_mock):  # pylint: disable=unused-argument
        movement = FreightContract(identifier='234234234')
        manager_mock = Mock()
        manager_mock.get = Mock(return_value=movement)
        contract_klass_mock.objects = manager_mock
        pdf_generate_mock = Mock(return_value='PDF')

        response = self.client.post(
            '/common/pdf/',
            {'id': 1, 'entity': 'freightcontract', 'party': 'common'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.filename, '234234234.pdf')
        self.assertTrue(response.as_attachment)

    @patch('core.common.views.web_views.Invoice')
    @patch('core.common.pdfgenerator.PDFGenerator.generate')
    def test_post_200_with_invoice(self, pdf_generate_mock, invoice_klass_mock):    # pylint: disable=unused-argument
        invoice = Invoice(identifier='testinvoice')
        manager_mock = Mock()
        manager_mock.get = Mock(return_value=invoice)
        invoice_klass_mock.objects = manager_mock
        pdf_generate_mock = Mock(return_value='PDF')

        response = self.client.post(
            '/common/pdf/',
            {'id': 1, 'entity': 'invoice', 'party': 'common'},
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.filename, 'testinvoice.pdf')
        self.assertTrue(response.as_attachment)


@tag('model')
class CommonFilterTest(AuthSetup):
    def setUp(self):
        super().setUp()
        EmployeeViewFilters.objects.create(employee=self.employee, filters=EMPLOYEE_FILTER_MOCK_DATA)

    def test_conditional_filtering(self):
        key_filters = CommonFilters(self.employee)
        res, consignee_res, _, consignor_res, consignor_empty_res = key_filters.conditional_filtering(key='contract')
        self.assertEqual(res, models.Q(**{
            'commodity__id__in': [1],
            'season__in': ['17/18'],
            'administration__invoicing__in': ['Seller to Invoice Buyer'],
            'price_point__id__in': [1],
            'status__in': ['in_progress'],
            'grade__id__in': [1],
            'updated_at__lte': '2020-04-04',
            'updated_at__gte': '2020-08-29',
        }))
        self.assertEqual(consignee_res, models.Q(**{
                'contractcommodityhandler_set__handler__id__in': [1],
                'contractcommodityhandler_set__role': 'Consignee'
        }))
        self.assertEqual(consignor_res, models.Q(**{
            'contractcommodityhandler_set__handler__id__in': [1],
            'contractcommodityhandler_set__role': 'Consignor'
        }))
        self.assertEqual(consignor_empty_res, models.Q())

    def test_conditional_filtering_invoice(self):
        EmployeeViewFilters.objects.create(employee=self.employee, filters=EMPLOYEE_INVOICE_FILTER_MOCK_DATA)
        key_filters = CommonFilters(self.employee)
        res = key_filters.conditional_filtering(key='invoice')
        self.assertEqual(res, models.Q(**{
            'invoicepayment__status__in': ['pending', 'rejected']
            }) | models.Q(**{"status__in": ['confirmed']}) & models.Q(**{"payee__company_id__in": [1]}))

    def test_conditional_filtering_invoice_no_status(self):
        self.employee.employee_view_filters.update(filters={"invoice": {"type__in": ["Commodity Contract"]}})
        key_filters = CommonFilters(self.employee)
        res = key_filters.conditional_filtering(key='invoice')
        self.assertEqual(res, models.Q(**{
            "type__in": ["Commodity Contract"]
        }))

    def test_conditional_filtering_empty(self):
        self.employee.employee_view_filters.update(filters={"contract": {"commodity__id__in": []}})
        key_filters = CommonFilters(self.employee)
        res, consignee_res, consignee_empty_res, consignor_res, consignor_empty_res = key_filters.conditional_filtering(
            key='contract')
        self.assertEqual(res, models.Q())
        self.assertEqual(consignee_res, models.Q())
        self.assertEqual(consignee_empty_res, models.Q())
        self.assertEqual(consignor_res, models.Q())
        self.assertEqual(consignor_empty_res, models.Q())

    def test_conditional_filtering_invoice_empty(self):
        self.employee.employee_view_filters.update(filters={})
        key_filters = CommonFilters(self.employee)
        res = key_filters.conditional_filtering(key='invoice') # pylint: disable=line-too-long
        self.assertEqual(res, models.Q())

    def test_conditional_filtering_with_no_update_gt(self):
        self.employee.employee_view_filters.update(filters={"contract": {'updated_at__lte':'2020-04-04'}})
        key_filters = CommonFilters(self.employee)
        res, consignee_res, consignee_empty_res, consignor_res, consignor_empty_res = key_filters.conditional_filtering(
            key='contract')
        self.assertEqual(res, models.Q(**{"updated_at__lte": "2020-04-04"}))
        self.assertEqual(consignee_res, models.Q())
        self.assertEqual(consignee_empty_res, models.Q())
        self.assertEqual(consignor_res, models.Q())
        self.assertEqual(consignor_empty_res, models.Q())
        country = self.company.country
        headers = get_csv_headers(country)
        res = key_filters.filter_to_csv_row(csv_header=headers, statuses=STATUSES)
        self.assertEqual(compact(res), ['Last modified date - 2020-04-04'])

    def test_conditional_filtering_with_no_update_lt(self):
        self.employee.employee_view_filters.update(filters={"contract": {'updated_at__gte':'2020-04-04'}})
        key_filters = CommonFilters(self.employee)
        res, consignee_res, consignee_empty_res, consignor_res, consignor_empty_res = key_filters.conditional_filtering(
            key='contract')
        self.assertEqual(res, models.Q(**{"updated_at__gte": "2020-04-04"}))
        self.assertEqual(consignee_res, models.Q())
        self.assertEqual(consignee_empty_res, models.Q())
        self.assertEqual(consignor_res, models.Q())
        self.assertEqual(consignor_empty_res, models.Q())
        country = self.company.country
        headers = get_csv_headers(country)
        res = key_filters.filter_to_csv_row(csv_header=headers, statuses=STATUSES)
        self.assertEqual(compact(res), ['Last modified date - 2020-04-04'])

    def test_filtering_csv_filters(self):
        key_filters = CommonFilters(self.employee)
        key_filters.conditional_filtering(key='contract')
        country = self.company.country
        headers = get_csv_headers(country)
        res = key_filters.filter_to_csv_row(csv_header=headers, statuses=STATUSES)
        self.assertEqual(compact(res), ['Status - In Progress',
                                        'Commodity - wheat', 'Grade - APH1',
                                        'Season - 17/18', 'Price Point - delivered_site',
                                        'Invoicing - Seller to Invoice Buyer',
                                        'Last modified date - 2020-08-29 to 2020-04-04'
                                        ])

    def test_conditional_filtering_freight_movement(self):
        self.employee.employee_view_filters.update(
            filters={"freight_movement": {"freight_pickup__loads_set__farm_field_id__in": [1],
                                          "freight_delivery__loads_set__storage_id__in": [1]}})
        key_filters = CommonFilters(self.employee)
        self.assertEqual(
            key_filters.conditional_filtering(key='freight_movement'),
            [
                models.Q(
                    models.Q(
                        models.Q(models.Q())
                    ) & models.Q(
                        freight_pickup__loads_set__farm_field_id__in=[1]
                    ) & models.Q(
                        freight_delivery__loads_set__storage_id__in=[1]
                    )
                ),
                models.Q(models.Q()),
                models.Q(models.Q()),
                models.Q(models.Q())
            ]
        )

    def test_conditional_filtering_site_loads(self):
        self.employee.employee_view_filters.update(
            filters={"site_loads_filters":{
                        "commodity__id__in": [1],
                        "planned_grade__id__in": [16],
                        "type__in": ["inload"] }})
        key_filters = CommonFilters(self.employee)
        res = key_filters.conditional_filtering(key='site_loads_filters')
        self.assertEqual(res,
            models.Q(**{ "commodity__id__in": [1] }) &
            models.Q(**{"grade__id__in": [16]}) &
            models.Q(**{"type__in": ["inload"]})
        )


@tag('util')
class UtilsTests(TestCase):  # pylint: disable=too-many-public-methods
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        if settings.RUN_TEST_FIXTURES:
            call_command("loaddata", "core/fixtures/core_entities.yaml")
            call_command("loaddata", "core/test_fixtures/commodities.yaml")
            call_command("loaddata", "core/test_fixtures/commodity_varieties.json")
            call_command("loaddata", "core/test_fixtures/commodity_grades.json")

    @override_settings()
    def test_is_production(self):
        self.assertFalse(is_production())

        settings.ENV = 'production'
        self.assertTrue(is_production())

    @override_settings()
    def test_is_staging(self):
        self.assertFalse(is_staging())

        settings.ENV = 'staging'
        self.assertTrue(is_staging())

    @override_settings()
    def test_is_qa(self):
        self.assertFalse(is_qa())

        settings.ENV = 'qa'
        self.assertTrue(is_qa())

    def test_remove_duplicate_elements_by_key(self):
        elements = [
            {'a': 100, 'b': 200}, {'a': 200, 'b': 100}, {'a': 100, 'b': 300}, {'a': 200, 'b': 100},
            {'a': 500, 'b': 200}
        ]
        self.assertEqual(remove_duplicate_elements_by_key(elements, 'a'), [
            {'a': 100, 'b': 200}, {'a': 200, 'b': 100}, {'a': 500, 'b': 200}
        ])

    def test_chunked_queryset_iterator(self):
        queryset_mock = Mock()
        queryset_mock.aggregate = Mock(return_value={'max_id': 50})
        queryset_mock.order_by = Mock(return_value=queryset_mock)
        mock_list = [Mock(pk=x + 1) for x in range(50)]
        queryset_mock.filter = Mock(return_value=mock_list)

        self.assertListEqual(
            [  # pylint: disable=unnecessary-comprehension
                call_index for call_index in chunked_queryset_iterator(queryset_mock)
            ],
            mock_list
        )
        queryset_mock.aggregate.assert_called_once()
        queryset_mock.order_by.assert_called_once_with('id')
        queryset_mock.filter.assert_called_once_with(pk__gt=0)

    def test_set_and_get_current_user(self):
        set_current_user(lambda self: 'foo')
        self.assertEqual(get_current_user(), 'foo')

    def test_set_and_get_request_url(self):
        set_request_url(lambda self: 'https://foo.comm/foobar/')
        self.assertEqual(get_request_url(), 'https://foo.comm/foobar/')

    def test_camelize(self):
        self.assertEqual(
            camelize({'ab_bc': 'foo', 'bc': 'bar', 'de_e1': 'foobar'}),
            {'abBc': 'foo', 'bc': 'bar', 'deE1': 'foobar'}
        )

    def test_camelize_nested(self):
        self.assertEqual(
            camelize({'ab_bc': 'foo', 'bc': {'de_e1': 'foobar'}}),
            {'abBc': 'foo', 'bc': {'deE1': 'foobar'}}
        )

    def test_camelize_list(self):
        self.assertEqual(
            camelize([
                {'ab_bc': 'foo', 'bc': {'de_e1': 'foobar'}},
                {'ab_bc': 'foo', 'bc': {'de_e1': 'foobar'}},
            ]),
            [
                {'abBc': 'foo', 'bc': {'deE1': 'foobar'}},
                {'abBc': 'foo', 'bc': {'deE1': 'foobar'}},
            ]
        )

    def test_camelize_with_list(self):
        self.assertEqual(
            camelize({'bc': [{'a_b': 'aB'}, "bc_foo"]}),
            {'bc': [{'aB': 'aB'}, "bc_foo"]}
        )
        self.assertEqual(
            camelize({'ab_bc': 'foo', 'bc': [{'a_b': 'aB'}]}),
            {'abBc': 'foo', 'bc': [{'aB': 'aB'}]}
        )

    def test_snakeize(self):
        self.assertEqual(
            snakeize({'abBc': 'foo', 'bc': 'bar', 'deE1': 'foobar'}),
            {'ab_bc': 'foo', 'bc': 'bar', 'de_e1': 'foobar'}
        )

    def test_snakeize_mixed(self):
        self.assertEqual(
            snakeize({'abBc': ['foo'], 'bc': 'bar', 'deE1': 'foobar'}),
            {'ab_bc': ['foo'], 'bc': 'bar', 'de_e1': 'foobar'}
        )

    def test_snakeize_with_list(self):
        self.assertEqual(
            snakeize({'abBc': 'foo', 'bc': [{'aB': 'aB'}]}),
            {'ab_bc': 'foo', 'bc': [{'a_b': 'aB'}]}
        )

    def test_snakeize_nested(self):
        self.assertEqual(
            snakeize({'abBc': 'foo', 'bc': {'deE1': 'foobar'}}),
            {'ab_bc': 'foo', 'bc': {'de_e1': 'foobar'}}
        )

    def test_snakeize_list(self):
        self.assertEqual(
            snakeize([
                {'abBc': 'foo', 'bc': {'deE1': 'foobar'}},
                {'abBc': 'foo', 'bc': {'deE1': 'foobar'}},
            ]),
            [
                {'ab_bc': 'foo', 'bc': {'de_e1': 'foobar'}},
                {'ab_bc': 'foo', 'bc': {'de_e1': 'foobar'}},
            ]
        )

    def test_invert_dict(self):
        self.assertEqual(
            invert_dict({'key': 'value'}),
            {'value': 'key'}
        )

    def test_to_display_attr(self):
        _options = (('foo', 'BAR'), ('bar', 'FOO'))
        self.assertEqual(
            to_display_attr(_options, 'foo'),
            'BAR'
        )
        self.assertEqual(
            to_display_attr(_options, 'bar'),
            'FOO'
        )
        self.assertIsNone(
            to_display_attr(_options, 'BAR')
        )

    def test_replace_nullable_int_with_null(self):
        _data = {'foo': 1, 'bar': ''}
        _data = replace_nullable_int_with_null(_data, ['bar'])
        self.assertIsNone(_data['bar'])

    def test_to_query_filters(self):
        self.assertEqual(
            to_query_filters({}),
            {}
        )
        self.assertEqual(
            to_query_filters(''),
            ''
        )
        self.assertEqual(
            to_query_filters([]),
            []
        )
        self.assertEqual(
            to_query_filters(None),
            None
        )
        self.assertEqual(
            to_query_filters({'foo': 'bar', 'id': [1], 'bar': 2}),
            {'foo': 'bar', 'id__in': [1], 'bar': 2}
        )

        self.assertEqual(
            to_query_filters({'foo': 'bar', 'status': ['completed,delivered']}),
            {'foo': 'bar', 'status__in': ['completed', 'delivered']}
        )

        self.assertEqual(
            to_query_filters(
                {'foo__id__lt': 'bar', 'id': [1], 'bar__tao__gt': [2], 'ching': 'te'}
            ),
            {'foo__id__lt': 'bar', 'id__in': [1], 'bar__tao__gt': 2, 'ching': 'te'}
        )

    def test_to_fsm_field_filters(self):
        for value in [[], {}, '', None]:
            self.assertIsNone(to_fsm_field_filters('foo', value))

        filters = to_fsm_field_filters('status', ['planned', 'confirmed'])

        self.assertTrue(isinstance(filters, models.query_utils.Q))
        self.assertEqual(
            filters.__dict__,
            {
                'children': [('status', 'planned'), ('status', 'confirmed')],
                'connector': 'OR',
                'negated': False
            }
        )

        filters = to_fsm_field_filters('status', ['planned'])

        self.assertTrue(isinstance(filters, models.query_utils.Q))
        self.assertEqual(
            filters.__dict__,
            {'children': [('status', 'planned')], 'connector': 'OR', 'negated': False}
        )

    def test_deepgetattr(self):
        employee = Employee(**EMPLOYEE_VALID_MOCK_DATA, )
        employee.type = EmployeeType(**{'name': 'company_admin', 'id': 1})

        self.assertEqual(deepgetattr(employee, 'type.name'), 'company_admin')
        self.assertIsNone(deepgetattr(employee, 'type.foo.bar.foo'))

    def test_flatten(self):
        self.assertEqual(
            flatten(['a', 'b', ['c'], ['d', 'e']]),
            ['a', 'b', 'c', 'd', 'e']
        )
        self.assertEqual(
            flatten(['a', 'b', ['c'], ['d', 'e'], [None]]),
            ['a', 'b', 'c', 'd', 'e', None]
        )
        self.assertEqual(
            flatten(['a', 'b', 'c', 'd', 'e']),
            ['a', 'b', 'c', 'd', 'e']
        )
        self.assertEqual(flatten([]), [])

    def test_get_unique_object_list(self):
        employee1 = Employee(**EMPLOYEE_VALID_MOCK_DATA)
        obj_list = [employee1, employee1, employee1, employee1, employee1]
        self.assertEqual(list(get_unique_object_list(obj_list)), [employee1])

    def test_get_grade_name_none(self):
        self.assertEqual(get_grade_name(None), None)

    def test_get_barley_F_type_latest_grade_name(self):
        obj = Mock(is_blended=False)
        obj.commodity_id = 2
        obj.variety_id = 123
        obj.grade.name = 'BAR3'
        obj.spread = Mock(details=[])
        obj.variety.details = {'grade_1': 'FOO', 'grade_2': 'SHOO', 'grade_3': 'GOO'}

        self.assertEqual(get_grade_name(obj), 'BAR3')

        for season in ['19/20', '20/21', '21/22']:
            obj.season = season
            self.assertEqual(get_grade_name(obj), 'BAR3')

    def test_get_barley_F_type_old_grade_name(self):
        obj = Mock(is_blended=False)
        obj.commodity_id = 2
        obj.variety_id = 123
        obj.grade.name = 'BAR3'
        obj.spread = Mock(details=[])
        obj.variety.details = {'grade_1': 'FOO', 'grade_2': 'SHOO', 'grade_3': 'GOO'}

        for season in ['16/17', '17/18', '18/19']:
            obj.season = season
            self.assertEqual(get_grade_name(obj), 'F3')

    def test_get_barley_grade_name_no_variety(self):
        obj = Mock(is_blended=False)
        obj.commodity_id = 2
        obj.variety_id = None
        obj.grade.name = 'Grade3'
        obj.spread = Mock(details=[])

        self.assertEqual(get_grade_name(obj), 'Grade3')

    def test_get_non_barley_grade_name(self):
        obj = Mock(is_blended=False)
        obj.commodity_id = 1
        obj.variety_id = 123
        obj.grade.name = 'Grade3'
        obj.spread = Mock(details=[])
        obj.variety.details = {'grade_1': 'FOO', 'grade_2': 'SHOO', 'grade_3': 'GOO'}

        self.assertEqual(get_grade_name(obj), 'Grade3')

    def test_assign_key_contact(self):
        employees = [{'id': 2}, {'id': 3}]

        self.assertEqual(
            assign_key_contact(employees, 1),
            [{'id': 2, 'key_contact': False}, {'id': 3, 'key_contact': False}]
        )
        self.assertEqual(
            assign_key_contact(employees, 2),
            [{'id': 2, 'key_contact': True}, {'id': 3, 'key_contact': False}]
        )

    @patch('core.common.utils.requests.get')
    @patch('core.common.utils.base64.b64encode')
    def test_get_base64_content(self, base64encode_mock, get_mock):
        response_mock = Mock(content='raw-content')
        get_mock.return_value = response_mock
        base64encode_mock.return_value = b'content'

        self.assertEqual(
            get_base64_content('http://some/url'),
            "data:image/png;base64,content"
        )
        get_mock.assert_called_once_with('http://some/url', timeout=ANY)
        base64encode_mock.assert_called_once_with('raw-content')

        self.assertEqual(
            get_base64_content('http://some/url', 'data:image/jpeg;'),
            "data:image/jpeg;base64,content"
        )

    @patch('core.common.utils.requests.get')
    def test_get_base64_content_exception(self, get_mock):
        get_mock.side_effect = Exception('foo')

        self.assertIsNone(
            get_base64_content('http://some/url')

        )
        get_mock.assert_called_once_with('http://some/url', timeout=ANY)

    def test_get_full_function_name(self):
        self.assertEqual(
            get_full_function_name(is_qa), 'core.common.utils.is_qa')

    def test_get_changeset(self):
        self.assertEqual(get_changeset(None, None), {})
        self.assertEqual(get_changeset(Load().to_dict(), None), {})
        self.assertEqual(get_changeset(None, Load().to_dict()), {})
        self.assertEqual(get_changeset(Load().to_dict(), Load().to_dict()), {'created_at': ANY})
        self.assertEqual(
            get_changeset(Load(id=1).to_dict(), Load(id=2).to_dict()), {'created_at': ANY, 'id': 1}
        )
        self.assertEqual(
            get_changeset(Load(id=2).to_dict(), Load(id=1).to_dict()), {'created_at': ANY, 'id': 2}
        )

    @patch('core.common.utils.convert_image_to_mime_type')
    @patch('core.common.utils.EmailMessage')
    def test_send_mail_with_pdf_attachment(self, email_message_mock, mime_type_mock):
        mime_type_mock.return_value = 'mime-type-attachment'
        email_mock = email_message_mock()
        email_mock.send = Mock()

        sender = Mock()
        sender.name = 'sender-name'
        sender.email = '<EMAIL>'

        send_mail_with_pdf_attachment(
            '<html></html>', [None, '<EMAIL>', '<EMAIL>'],
            'subject', 'pdf-name', 'pdf', sender
        )

        self.assertEqual(
            email_message_mock.mock_calls[1],
            call(
                body='<html></html>',
                from_email='Information AgriChain <<EMAIL>>',
                reply_to=['<EMAIL>'],
                subject='subject',
                to=['<EMAIL>', '<EMAIL>'],
                cc=['<EMAIL>'],
            )
        )
        self.assertEqual(
            email_mock.attach.mock_calls,
            [
                call('pdf-name', 'pdf', 'application/pdf'),
                call('mime-type-attachment')
            ]
        )
        email_mock.send.assert_called_once()
        mime_type_mock.assert_called_once()

    def test_get_attachment_header_value(self):
        self.assertEqual(
            get_attachment_header_value('file.csv'),
            'attachment; filename="file.csv"'
        )

    def test_to_filter_clause(self):
        self.assertEqual(
            to_filter_clause({'foo': 'bar', 'bar': None}),
            {'foo': 'bar', 'bar__isnull': True}
        )
        self.assertEqual(
            to_filter_clause(params={'foo': 'bar', 'bar': None}, prefix='load'),
            {'load__foo': 'bar', 'load__bar__isnull': True}
        )

    def test_format_number(self):
        self.assertEqual(format_number(3.44444), 3.44)
        self.assertEqual(format_number(3.44999), 3.45)
        self.assertEqual(format_number(3.409), 3.41)
        self.assertEqual(format_number(3.4), 3.40)

    def test_is_super_admin(self):
        self.assertFalse(is_super_admin(None))
        self.assertFalse(is_super_admin(2))
        self.assertFalse(is_super_admin('2'))
        self.assertTrue(is_super_admin(1))
        self.assertTrue(is_super_admin('1'))

    def test_generate_identifier(self):
        nz_user = EmployeeFactory(company=CompanyFactory(country_id=4))
        self.assertEqual(len(generate_identifier('invoice', nz_user.company.country)), 12)
        self.assertEqual(len(generate_identifier('sales_confirmation')), 14)
        self.assertEqual(len(generate_identifier('broker_note')), 14)
        self.assertEqual(len(generate_identifier('contract')), 14)
        self.assertEqual(len(generate_identifier('order')), 14)
        self.assertEqual(len(generate_identifier('grain_order')), 14)
        self.assertEqual(len(generate_identifier('freight_movement')), 14)
        self.assertEqual(len(generate_identifier('invoice')), 14)
        self.assertEqual(len(generate_identifier('title_transfer')), 14)
        self.assertEqual(len(generate_identifier('invoice')), 14)
        self.assertEqual(len(generate_identifier('pickup_order')), 14)
        self.assertEqual(len(generate_identifier('delivery_order')), 14)
        self.assertTrue(generate_identifier('pickup_order').startswith('P'))
        self.assertTrue(generate_identifier('delivery_order').startswith('D'))
        self.assertTrue(generate_identifier('regrade_load').startswith('R'))
        self.assertTrue(generate_identifier('silo_to_silo_transfer_load').startswith('S'))

    @patch('core.common.utils.core_time')
    def test_get_filename_with_timestamp(self, time_mock):
        time_mock.time = Mock(return_value='123456.789')

        self.assertEqual(get_filename_with_timestamp('prefix_', 'csv'), 'prefix_123456789.csv')

    def test_get_grade_display_name(self):
        self.assertEqual(get_grade_display_name(1, 1, 1), 'APH1')
        self.assertEqual(get_grade_display_name(2, 242, 36), 'BAR1')
        self.assertEqual(get_grade_display_name(2, 242, 36, '16/17'), 'F1')
        self.assertEqual(get_grade_display_name(2, 242, 34), 'Malt2')
        self.assertEqual(get_grade_display_name(2, 242, 34, None, True), 'BAR2')

    def test_smart_join(self):
        self.assertEqual(smart_join([], None, None), None)
        self.assertEqual(smart_join(["foo"], ", ", "&"), "foo")
        self.assertEqual(smart_join(["foo", "bar"], ", ", "&"), "foo & bar")
        self.assertEqual(smart_join(["foo", "bar", "tao"], ", ", "&"), "foo, bar & tao")
        self.assertEqual(smart_join(["foo", "bar", "tao", "ching"], ", ", "and"), "foo, bar, tao and ching")
        self.assertEqual(smart_join(["foo", "bar", "tao", "ching"], ', ', None), "foo, bar, tao, ching")

    def test_is_continuous_list_of_numbers(self):
        self.assertFalse(is_continuous_list_of_numbers([]))
        self.assertFalse(is_continuous_list_of_numbers([1, 4, 5]))
        self.assertFalse(is_continuous_list_of_numbers(["a", "b"]))
        self.assertTrue(is_continuous_list_of_numbers([4, 5]))
        self.assertTrue(is_continuous_list_of_numbers([1, 2, 3, 4, 5]))

    def test_covert_tuples_to_dict(self):
        tuples = (
            ('delivered_site', 'Delivered Site'),
            ('ex_farm_store', 'Ex Farm / Store'),
        )

        self.assertEqual(
            convert_tuples_to_dict(tuples=tuples),
            {'delivered_site': 'Delivered Site', 'ex_farm_store': 'Ex Farm / Store'}
        )

        self.assertEqual(
            convert_tuples_to_dict(tuples=tuples, invert=False),
            {'delivered_site': 'Delivered Site', 'ex_farm_store': 'Ex Farm / Store'}
        )

        self.assertEqual(
            convert_tuples_to_dict(tuples=tuples, invert=True),
            {'Delivered Site': 'delivered_site', 'Ex Farm / Store': 'ex_farm_store'}
        )

    def test_remove_elements_from_list(self):
        original_list = ['foo', 'bar', 'tao', 'ching']
        list_to_remove = ['bar', 'ching']
        self.assertEqual(remove_elements_from_list(original_list, list_to_remove), ['foo', 'tao'])

    def test_chunks(self):
        self.assertEqual(
            list(chunks(list(range(10, 75)), 10)),
            [[10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
             [20, 21, 22, 23, 24, 25, 26, 27, 28, 29],
             [30, 31, 32, 33, 34, 35, 36, 37, 38, 39],
             [40, 41, 42, 43, 44, 45, 46, 47, 48, 49],
             [50, 51, 52, 53, 54, 55, 56, 57, 58, 59],
             [60, 61, 62, 63, 64, 65, 66, 67, 68, 69],
             [70, 71, 72, 73, 74]]
        )
        self.assertEqual(list(chunks([], 10)), [])


    def test_get_klass(self):
        self.assertEqual(get_klass('contract'), Contract)
        self.assertEqual(get_klass('freightorder'), FreightOrder)

    def test_to_number(self):
        self.assertEqual(to_number(''), 0)
        self.assertEqual(to_number(None), 0)
        self.assertEqual(to_number(23), 23)
        self.assertEqual(to_number('23'), 23)
        self.assertEqual(to_number('23.4'), 23.4)
        self.assertEqual(to_number('23.4 / MT'), 23.4)
        self.assertEqual(to_number('23.4%'), 23.4)
        self.assertEqual(to_number('$23.4 / MT'), 23.4)

    def test_to_int_list(self):
        self.assertEqual(to_int_list([]), [])
        self.assertEqual(to_int_list(['undefined']), [])
        self.assertEqual(to_int_list(['foobar']), [])
        self.assertEqual(to_int_list(['None', 'null', 'undefined', None, 0]), [0])
        self.assertEqual(to_int_list(['1', 'undefined']), [1])
        self.assertEqual(to_int_list([1, '2', 0]), [1, 2, 0])
        self.assertEqual(to_int_list(['0', '0', 0, 0]), [0, 0, 0, 0])

    def test_get_valid_float_from_string(self):
        self.assertEqual(get_valid_float_from_string('37.23.45'), 37.2345)
        self.assertEqual(get_valid_float_from_string('37.23'), 37.23)
        self.assertEqual(get_valid_float_from_string('37'), 37.0)
        self.assertEqual(get_valid_float_from_string(37), 37)
        self.assertEqual(get_valid_float_from_string(None), None)

    def test_bankers_round(self):
        # Normal Rounding
        self.assertEqual(bankers_round(2.124, 2), 2.12)
        self.assertEqual(bankers_round(2.126, 2), 2.13)
        self.assertEqual(bankers_round(0, 2), 0)
        self.assertEqual(bankers_round(1.1, 2), 1.1)
        self.assertEqual(bankers_round(1, 2), 1)

        # Halfway Rounding
        self.assertEqual(bankers_round(2.125, 2), 2.12)
        self.assertEqual(bankers_round(2.135, 2), 2.14)
        self.assertEqual(bankers_round(2.145, 2), 2.14)
        self.assertEqual(bankers_round(2.155, 2), 2.16)
        self.assertEqual(bankers_round(-0.005, 2), 0.0)
        self.assertEqual(bankers_round(3.5, 0), 4)
        self.assertEqual(bankers_round(4.5, 0), 4)
        self.assertEqual(bankers_round(5.5, 0), 6)
        self.assertEqual(bankers_round(-2.125, 2), -2.12)
        self.assertEqual(bankers_round(-2.135, 2), -2.14)

        # Edge Cases
        self.assertEqual(bankers_round(-0.00, 2), 0)
        self.assertEqual(bankers_round(None, 2), None)
        self.assertEqual(bankers_round('', 2), '')
