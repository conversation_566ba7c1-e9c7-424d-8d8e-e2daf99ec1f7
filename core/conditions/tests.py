from django.test import tag
from rest_framework import status

from core.common.tests import ACTestCase, AuthSetup
from .models import Condition
from ..profiles.tests.factories import EmployeeFactory


@tag('model')
class ConditionTest(ACTestCase):
    def test_fillables(self):
        self.assertEqual(
            Condition.FILLABLES,
            [
                'name',
                'details',
                'type',
                'company_id',
                'module',
                'default',
            ]
        )

    def test_meta_ordering(self):
        self.assertEqual(Condition()._meta.ordering, ['name'])


@tag('view')
class GeneralConditionsViewTest(AuthSetup):
    def test_post_condition_201(self):
        _res = self.client.post(
            '/conditions/generals/',
            {
                "details": "foobar",
                "name": "foo",
                "module": "contract",
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, 201)
        self.assertIsNotNone(_res.data["id"])
        self.assertEqual(_res.data["companyId"], self.employee.company_id)
        self.assertEqual(Condition.general_objects.count(), 1)

    def test_post_condition_400(self):
        _res = self.client.post(
            '/conditions/generals/',
            {
                "details": "foobar",
                "name": "foo",
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIsNone(_res.data["id"])
        self.assertIsNotNone(_res.data["errors"])
        self.assertEqual(Condition.general_objects.count(), 0)

    def test_get_conditions(self):
        _condition = Condition.create({
            "name": "first",
            "details": "foo",
            "module": "contract",
            "type":  "general",
            "company_id": self.employee.company_id
        })

        _res = self.client.get(
            '/conditions/generals/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertEqual(len(_res.data), 1)
        self.assertIsNotNone(_res.data[0]["id"], _condition.id)

    def test_get_conditions_query(self):
        _condition = Condition.create({
            "name": "first",
            "details": "foo",
            "module": "contract",
            "type": "general",
            "company_id": self.employee.company_id
        })

        _res = self.client.get(
            '/conditions/generals/?module=contract',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertEqual(len(_res.data), 1)
        self.assertIsNotNone(_res.data[0]["id"], _condition.id)

        _res = self.client.get(
            '/conditions/generals/?module=freight',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertEqual(len(_res.data), 0)


@tag('view')
class GeneralConditionViewTest(AuthSetup):
    def setUp(self):
        super().setUp()

        self.condition = Condition.create({
            "name": "first",
            "details": "foo",
            "module": "contract",
            "type": "general",
            "company_id": self.employee.company_id
        })

    def test_put_condition_200(self):
        _res = self.client.put(
            '/conditions/generals/' + str(self.condition.id) + '/',
            {
                "name": "bar",
                "module": "freight"
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(_res.data["id"])
        self.assertEqual(_res.data["type"], "general")
        self.assertEqual(_res.data["module"], "freight")
        self.assertEqual(_res.data["name"], "bar")
        self.assertEqual(_res.data["companyId"], self.condition.company_id)
        self.assertEqual(Condition.general_objects.count(), 1)

    def test_put_condition_404(self):
        _res = self.client.put(
            '/conditions/generals/' + str(22222) + '/',
            {
                "name": "bar",
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_404_NOT_FOUND)

    def test_put_condition_400(self):
        _res = self.client.put(
            '/conditions/generals/' + str(self.condition.id) + '/',
            {
                "details": "foobar",
                "name": "",
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            _res.data["errors"],
            {
                "name": ["This field cannot be blank."]
            }
        )

    def test_get_condition(self):
        _res = self.client.get(
            '/conditions/generals/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertEqual(_res.data["id"], self.condition.id)

    def test_get_condition_404(self):
        _res = self.client.get(
            '/conditions/generals/' + str(222222) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_condition_204(self):
        _res = self.client.get(
            '/conditions/generals/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_200_OK)

        self.assertEqual(_res.data["id"], self.condition.id)
        _res = self.client.delete(
            '/conditions/generals/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_204_NO_CONTENT)

        _res = self.client.get(
            '/conditions/generals/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_condition_403(self):
        employee = EmployeeFactory()
        other_user_token = employee.refresh_token()
        _res = self.client.get(
            '/conditions/generals/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertEqual(_res.data["id"], self.condition.id)

        _res = self.client.delete(
            '/conditions/generals/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + other_user_token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_403_FORBIDDEN)

        _res = self.client.get(
            '/conditions/generals/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_200_OK)


@tag('view')
class SpecialConditionsViewTest(AuthSetup):
    def test_post_condition_201(self):
        _res = self.client.post(
            '/conditions/specials/',
            {
                "details": "foobar",
                "name": "foo",
                "module": "contract",
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, 201)
        self.assertIsNotNone(_res.data["id"])
        self.assertEqual(_res.data["companyId"], self.employee.company_id)
        self.assertEqual(Condition.special_objects.count(), 1)

    def test_post_condition_400(self):
        _res = self.client.post(
            '/conditions/specials/',
            {
                "details": "foobar",
                "name": "foo",
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIsNone(_res.data["id"])
        self.assertIsNotNone(_res.data["errors"])
        self.assertEqual(Condition.special_objects.count(), 0)

    def test_get_conditions(self):
        _condition = Condition.create({
            "name": "first",
            "details": "foo",
            "module": "contract",
            "type": "special",
            "company_id": self.employee.company_id
        })

        _res = self.client.get(
            '/conditions/specials/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertEqual(len(_res.data), 1)
        self.assertIsNotNone(_res.data[0]["id"], _condition.id)

    def test_get_conditions_query(self):
        _condition = Condition.create({
            "name": "first",
            "details": "foo",
            "module": "contract",
            "type": "special",
            "company_id": self.employee.company_id
        })

        _res = self.client.get(
            '/conditions/specials/?module=contract',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertEqual(len(_res.data), 1)
        self.assertIsNotNone(_res.data[0]["id"], _condition.id)

        _res = self.client.get(
            '/conditions/specials/?module=freight',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertEqual(len(_res.data), 0)


@tag('view')
class SpecialConditionViewTest(AuthSetup):
    def setUp(self):
        super().setUp()

        self.condition = Condition.create({
            "name": "first",
            "details": "foo",
            "type": "special",
            "module": "contract",
            "company_id": self.employee.company_id
        })

    def test_put_condition_200(self):
        _res = self.client.put(
            '/conditions/specials/' + str(self.condition.id) + '/',
            {
                "name": "bar",
                "module": "freight"
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(_res.data["id"])
        self.assertEqual(_res.data["module"], "freight")
        self.assertEqual(_res.data["name"], "bar")
        self.assertEqual(_res.data["companyId"], self.condition.company_id)
        self.assertEqual(Condition.special_objects.count(), 1)

    def test_put_condition_404(self):
        _res = self.client.put(
            '/conditions/specials/' + str(22222) + '/',
            {
                "name": "bar",
                "module": "freight"
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_404_NOT_FOUND)

    def test_put_condition_400(self):
        _res = self.client.put(
            '/conditions/specials/' + str(self.condition.id) + '/',
            {
                "details": "foobar",
                "name": "",
            },
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            _res.data["errors"],
            {
                "name": ["This field cannot be blank."]
            }
        )

    def test_get_condition(self):
        _res = self.client.get(
            '/conditions/specials/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertEqual(_res.data["id"], self.condition.id)

    def test_get_condition_404(self):
        _res = self.client.get(
            '/conditions/specials/' + str(222222) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )

        self.assertEqual(_res.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_condition_204(self):
        _res = self.client.get(
            '/conditions/specials/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertEqual(_res.data["id"], self.condition.id)

        _res = self.client.delete(
            '/conditions/specials/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_204_NO_CONTENT)

        _res = self.client.get(
            '/conditions/specials/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_condition_403(self):
        employee = EmployeeFactory()
        other_user_token = employee.refresh_token()
        _res = self.client.get(
            '/conditions/specials/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_200_OK)
        self.assertEqual(_res.data["id"], self.condition.id)

        _res = self.client.delete(
            '/conditions/specials/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + other_user_token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_403_FORBIDDEN)

        _res = self.client.get(
            '/conditions/specials/' + str(self.condition.id) + '/',
            HTTP_AUTHORIZATION='Token ' + self.token,
            format='json',
        )
        self.assertEqual(_res.status_code, status.HTTP_200_OK)
