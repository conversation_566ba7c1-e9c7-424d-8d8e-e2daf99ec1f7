import csv
import json
import sys

csvfile = open(sys.argv[-1], "r")
fieldnames = ("Variety","Western Category","Southern Category","South-Eastern Category","Northern Category","Western Maximum Grade","Southern Maximum Grade","South-Eastern Maximum Grade","Northern Maximum Grade")
reader = csv.DictReader(csvfile, fieldnames)
res = []
pk = 1684
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _json = {
            "pk": pk,
            "model": "commodities.variety",
            "fields": {
                "commodity_id": 1,
                "details": {
                    "categories": {},
                    "maximum_grades": {},
                }
            }
        }
        _json["fields"]["name"] = _dict["Variety"]
        _json["fields"]["details"]["categories"]["western"] = _dict["Western Category"]
        _json["fields"]["details"]["categories"]["southern"] = _dict["Southern Category"]
        _json["fields"]["details"]["categories"]["south_eastern"] = _dict["South-Eastern Category"]
        _json["fields"]["details"]["categories"]["northern"] = _dict["Northern Category"]
        _json["fields"]["details"]["maximum_grades"]["western"] = _dict["Western Maximum Grade"]
        _json["fields"]["details"]["maximum_grades"]["southern"] = _dict["Southern Maximum Grade"]
        _json["fields"]["details"]["maximum_grades"]["south_eastern"] = _dict["South-Eastern Maximum Grade"]
        _json["fields"]["details"]["maximum_grades"]["northern"] = _dict["Northern Maximum Grade"]
        _json["fields"]["created_at"] = "2021-10-19T08:00:00+00:00"
        _json["fields"]["updated_at"] = "2021-10-19T08:00:00+00:00"
        _json["fields"]["created_by"] = 1
        _json["fields"]["updated_by"] = 1
        pk += 1
        res.append(_json)

print(json.dumps(res))
