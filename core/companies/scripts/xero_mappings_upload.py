import csv
from itertools import product

from pydash import get

from core.commodities.models import Commodity, Grade
from core.companies.models import XeroTrackingCategory, XeroMapping
from core.farms.models import Farm
from core.ngrs.models import Ngr

tracking_categories = [
    {
        "trackingCategoryId": "5c17ed4f-26a3-4063-a8d6-dd178d288655",
        "name": "Farm",
        "options": [
            {
                "id": "555e14ce-e834-4e79-b35d-9de5eecacab0",
                "name": "ANK Anunaka"
            },
            {
                "id": "82b74294-e788-4c1a-b603-c6d2468d77c5",
                "name": "BAL Ballandry"
            },
            {
                "id": "15813180-671c-4622-896e-28311b48f60d",
                "name": "<PERSON><PERSON> Boorala"
            },
            {
                "id": "27ad9e0b-21c1-4c5b-a8bd-50755a062913",
                "name": "<PERSON><PERSON> <PERSON><PERSON>"
            },
            {
                "id": "5114c6cd-8980-4557-a41a-b97615bb1872",
                "name": "<PERSON><PERSON> Cairlocup"
            },
            {
                "id": "3de7b78c-3993-43b6-aa38-ae67066963e4",
                "name": "COR Corporate"
            },
            {
                "id": "989f6624-3407-4d8f-88a4-a6946659937c",
                "name": "KAR Karrara"
            },
            {
                "id": "87e595fc-acef-4791-a30c-ab9e65b97e3f",
                "name": "KWP Kulwin Park"
            },
            {
                "id": "fcd47eeb-b70b-4eee-8894-b2f19c18b892",
                "name": "WDS ZZ retired"
            },
            {
                "id": "1df32f16-a89a-452a-ac17-0209d82fb8fb",
                "name": "WIR Wirrinourt"
            }
        ]
    },
    {
        "trackingCategoryId": "63863ce1-c1b5-4e4c-a7ef-e6e2250dcbd6",
        "name": "Ent",
        "options": [
            {
                "id": "92ec0cad-1e56-4d98-ae3f-e31eb02daf27",
                "name": "00 Corporate"
            },
            {
                "id": "1d4f1dd2-11f9-408e-80e6-b0e5298abc9b",
                "name": "00 Parent"
            },
            {
                "id": "9a06febe-0f94-4e44-b287-e37f81fa64b7",
                "name": "10 Wheat 2023"
            },
            {
                "id": "e9b92df6-9a9f-4597-9a50-be447e395815",
                "name": "100 Wheat 2024"
            },
            {
                "id": "da1fb972-4452-431c-a065-fb2031a3c92e",
                "name": "11 Wheat 2021"
            },
            {
                "id": "41cee919-23d5-4531-8812-6930b54b6a21",
                "name": "110 Wheat 2025"
            },
            {
                "id": "af08c702-6dd7-4cab-876f-c27cb4c45968",
                "name": "12 Wheat 2022"
            },
            {
                "id": "fa56f96a-fc88-4053-839f-bd6d7cfabd7d",
                "name": "120 Wheat 2026"
            },
            {
                "id": "a28313bb-379e-4ad0-a478-becfd2379d3d",
                "name": "13 Barley 2023"
            },
            {
                "id": "7ee88390-f634-4d04-97f9-3884ea42bd82",
                "name": "130 Barley 2024"
            },
            {
                "id": "e1472619-3335-4a5b-8a74-7163f60d3187",
                "name": "14 Barley 2021"
            },
            {
                "id": "01ece245-684e-44ac-95d4-b1090bad6572",
                "name": "140 Barley 2025"
            },
            {
                "id": "8871ab69-7798-4255-a0c3-4ee5254b8d59",
                "name": "15 Barley 2022"
            },
            {
                "id": "2a4cc7fd-9829-44ee-b7b5-31f592e49ba4",
                "name": "150 Barley 2026"
            },
            {
                "id": "83981e89-5c42-4f48-a74f-66d17d23eee2",
                "name": "16 Canola 2023"
            },
            {
                "id": "8a6814d0-b390-43da-9b86-a8721024cdc2",
                "name": "160 Canola 2024"
            },
            {
                "id": "a4821b31-12bd-45b8-97ef-d4226c714461",
                "name": "17 Canola 2021"
            },
            {
                "id": "3a8c8bfe-0d25-4ef3-b46e-c159c695143b",
                "name": "170 Canola 2025"
            },
            {
                "id": "e3690f8f-c15b-4b4a-94fd-3c4ca9de99d8",
                "name": "18 Canola 2022"
            },
            {
                "id": "729bb2d4-106d-4241-b4f2-ba6e19f97308",
                "name": "180 Canola 2026"
            },
            {
                "id": "faeee690-4969-4d9d-a348-ee9c4b7f5adc",
                "name": "19 Beans 2023"
            },
            {
                "id": "c9eb80e8-6546-41fa-af76-f26ff05b94ec",
                "name": "190 Beans 2024"
            },
            {
                "id": "55b27d57-65b3-4022-aec3-94774da6b7a0",
                "name": "20 Beans 2021"
            },
            {
                "id": "43e1f0f1-7823-4bd8-8382-c84a9a6eadc0",
                "name": "200 Faba/Broad/Field Bean 2025"
            },
            {
                "id": "48b68250-e283-4a78-94dd-563bb713f19e",
                "name": "21 Beans 2022"
            },
            {
                "id": "a930b893-f66a-462c-b756-c7e3157e471d",
                "name": "210 Beans 2026"
            },
            {
                "id": "056f11a1-5128-401c-afa7-a1f6126b7a1c",
                "name": "22 Oaten Hay 2023"
            },
            {
                "id": "40b7e47c-73f4-4c52-86e7-fe2add82b31a",
                "name": "220 Oaten Hay 2024"
            },
            {
                "id": "79ed0d01-f9d4-4cd5-8cde-7953047f5199",
                "name": "23 Oaten Hay 2021"
            },
            {
                "id": "f5de9433-9abc-42c7-aa62-a2f6aef8004e",
                "name": "230 Oaten Hay 2025"
            },
            {
                "id": "cfdb402b-dd3a-4fbf-8daa-bd195d207a95",
                "name": "24 Oaten Hay 2022"
            },
            {
                "id": "f2df8a7b-79f0-4a73-b635-8eba6d19cf46",
                "name": "240 Oaten Hay 2026"
            },
            {
                "id": "752cb5da-6730-4b76-b428-0267b4cf645e",
                "name": "25 Wheaten Hay 2023"
            },
            {
                "id": "a266b8be-5bd4-498d-ac39-e53710acbd40",
                "name": "250 Wheaten Hay 2024"
            },
            {
                "id": "3aff24eb-0db5-4e6e-96d8-5907ed0248ef",
                "name": "260 Wheaten Hay 2025"
            },
            {
                "id": "ca172aac-5ee3-45d4-8760-b1dba6836cc0",
                "name": "270 Wheaten Hay 2026"
            },
            {
                "id": "58e2f3a0-241a-4411-a804-0727e1b9066a",
                "name": "28 Vetch 2023"
            },
            {
                "id": "24edd272-5a83-4a05-b80d-81a82ef52857",
                "name": "280 Vetch 2024"
            },
            {
                "id": "089147a1-a7dc-40a9-88ef-3d5cbb5b94ed",
                "name": "29 Vetch 2021"
            },
            {
                "id": "9834a803-526c-4ed4-9a5d-179168d65b9b",
                "name": "290 Vetch 2025"
            },
            {
                "id": "b3a39b1b-c3ff-43f0-b99c-3cae625eb9fd",
                "name": "30 Vetch 2022"
            },
            {
                "id": "ca697fa4-59c1-4847-aac7-f9daf5f539dc",
                "name": "300 Vetch 2026"
            },
            {
                "id": "5257301f-b234-47cf-8056-46259a8d67a8",
                "name": "31 Lentils 2023"
            },
            {
                "id": "7f3e2503-3b5f-4c81-ad46-cebbc81213db",
                "name": "310 Lentils 2024"
            },
            {
                "id": "18e7e96c-c60c-418c-9ca0-8209c974304e",
                "name": "32 Lentils 2021"
            },
            {
                "id": "4a947d6e-42fb-445b-a7d6-00e695706561",
                "name": "320 Lentils 2025"
            },
            {
                "id": "8e7b5f66-7e11-4b4e-a909-b53cc5232de3",
                "name": "33 Lentils 2022"
            },
            {
                "id": "ee5c1b36-b42e-4478-a1b1-471dfc4044cd",
                "name": "330 Lentils 2026"
            },
            {
                "id": "e3542377-3bef-4e90-ac34-a8d313c5db81",
                "name": "35 Lupins 2021"
            },
            {
                "id": "0b12dd3b-924c-418f-aaa7-355762755ca7",
                "name": "350 Lupins 2024"
            },
            {
                "id": "1bd11287-7611-4752-9072-d87f69d9da8c",
                "name": "36 Lupins 2022"
            },
            {
                "id": "3949d3fa-93a6-477c-a4bf-0c7f32ff75f7",
                "name": "360 Lupins 2025"
            },
            {
                "id": "e396f695-81c5-4366-ab6a-95ce5bd8766c",
                "name": "37 Lupins 2023"
            },
            {
                "id": "0a782454-fb75-405f-a5d8-5654e7d46bb3",
                "name": "370 Lupins 2026"
            },
            {
                "id": "988c4ef0-17dc-4915-a92c-20df112241c1",
                "name": "38 Feedlot Sheep"
            },
            {
                "id": "ed50085f-b46d-4449-97a8-ecb74d6c8f31",
                "name": "44 Field Peas 2021"
            },
            {
                "id": "818b6240-bfce-4fed-b5da-c86ba3ed5d6a",
                "name": "440 Field Peas 2024"
            },
            {
                "id": "313e24f3-6cbd-4570-a132-06b5f3debcad",
                "name": "45 Field Peas 2022"
            },
            {
                "id": "1e4f7fb6-7a28-46e7-86ca-ceb87e75d600",
                "name": "450 Field Peas 2025"
            },
            {
                "id": "aeb4e90f-97cb-47c8-bd1a-d85fa38582a7",
                "name": "46 Field Peas 2023"
            },
            {
                "id": "65754197-60ff-438b-b288-8d4485643276",
                "name": "460 Field Peas 2026"
            },
            {
                "id": "006ab2e5-7d13-4296-87f2-5909dd4689fd",
                "name": "47 Oats 2021"
            },
            {
                "id": "246a47e9-7206-4b91-889c-d3cdc2884e32",
                "name": "470 Oats 2024"
            },
            {
                "id": "8be28aad-fdb7-44de-8418-4d5f37010b91",
                "name": "475 Oats 2025"
            },
            {
                "id": "0723083f-4c5f-4001-b9a5-b469ca7faa66",
                "name": "480 Oats 2026"
            },
            {
                "id": "42cb790a-64d3-4167-bdd3-17450ec7d757",
                "name": "500 GM Canola 2025"
            },
            {
                "id": "4a373264-a804-4a6a-84ce-08d8979af808",
                "name": "61 Breeding Merino"
            },
            {
                "id": "8157b764-5cad-4d2a-84fe-458edbbd4961",
                "name": "62 Breeding XB"
            },
            {
                "id": "1e34654c-a78f-4b82-bcce-d2fdc6a47658",
                "name": "63 Trade Merino Wether"
            },
            {
                "id": "97a039ee-22d9-40ba-b530-4854ae93fb45",
                "name": "64 Trade XB Lambs"
            },
            {
                "id": "50a1798d-7b8d-4057-87c4-0868d9e90f60",
                "name": "65 Trade Ewe Lambs"
            },
            {
                "id": "80f9a4b4-6d5c-43eb-bd04-77d7c1b58f84",
                "name": "66 Trade Cattle Dairy"
            },
            {
                "id": "6853b2fb-0f65-4602-b119-5f53b921c240",
                "name": "67 Trade Cattle Beef"
            },
            {
                "id": "db82a967-edc5-4dab-b831-a114df2d8098",
                "name": "68 Breeding Cattle"
            },
            {
                "id": "e0edc506-95c4-495f-979d-65c6edd8d3bc",
                "name": "69 Cattle Agistment"
            },
            {
                "id": "46a1e206-ff30-48d7-896f-6551ab1cb6ef",
                "name": "70 Straw"
            },
            {
                "id": "d481cb23-0a16-42a1-a94f-4b0640854a81",
                "name": "71 Cattle Weight Gain"
            },
            {
                "id": "2ec06c44-9a17-45ce-a369-ba71e72e2b11",
                "name": "72 Sheep Agistment"
            },
            {
                "id": "12e27dc0-e652-4086-b4dc-7dea35bc1254",
                "name": "80 Pasture"
            },
            {
                "id": "5d3444d8-eb64-4ab4-83cb-af4653bf935d",
                "name": "81 Fodder Crop"
            },
            {
                "id": "c7fbefba-4ef3-4b38-8725-436ffb44254c",
                "name": "85 Brown Manure"
            },
            {
                "id": "9afe57eb-9d75-4b32-95e2-38e091a39914",
                "name": "88 Wheaten Hay"
            },
            {
                "id": "0e496efd-709c-4cd9-ac30-3fc83cf7260d",
                "name": "90 Fallow"
            },
            {
                "id": "1ae36033-92c6-43eb-82cc-f65b3a17d954",
                "name": "98 Machinery"
            },
            {
                "id": "4e51b86b-55c0-4acc-a0a3-8e94883cffc9",
                "name": "99 General"
            }
        ]
    }
]


def get_xero_mappings(filepath, company):
    csvfile = open(filepath, "r")
    reader = csv.DictReader(csvfile)
    data = []
    total_rows = 0
    total_combos = 0
    def get(r, field, single=False):
        value = r.get(field, "")
        if not isinstance(value, str):
            return value
        value = value.strip()
        value = [v.strip() for v in value.split(",")] if not single else value
        return value
    def at_least_one(lst):
        return lst if lst else [None]
    company_ngrs = Ngr.objects.filter(owner_company_ids__contains=[company.id])
    company_sites = Farm.objects.filter(company=company)
    def get_category(category_type_name, category_option_name):
        category = [cat for cat in tracking_categories if cat['name'].lower() == category_type_name.lower()][0]
        option = [opt for opt in category['options'] if opt['name'].lower() == category_option_name.lower()][0]
        return XeroTrackingCategory(
            name=category['name'],
            company=company,
            xero_option_id=option['id'],
            option_name=category_option_name,
            xero_tracking_category_id=category['trackingCategoryId'],
        )
    for row in reader:
        print("Row: ", row)
        total_rows += 1
        transaction_types = get(row, "Transaction Type") or []
        commodities = get(row, "Commodities") or []
        grades = get(row, "Grades (optional)") or []
        seasons = get(row, "Seasons (optional)") or []
        sites = get(row, "Sites (optional)") or []
        ngrs = get(row, "NGRs") or []
        item_types = get(row, "Item Types") or []
        xero_account = get(row, "Xero Account", True) or None
        xero_item_code = get(row, "Xero Item Code", True) or None
        farm = get(row, "Farm", True) or None
        ent = get(row, "Ent", True) or None
        combinations = product(
            at_least_one(transaction_types),
            at_least_one(commodities),
            at_least_one(grades),
            at_least_one(seasons),
            at_least_one(sites),
            at_least_one(ngrs),
            at_least_one(item_types),
        )
        result = []
        for t, c, g, s, si, n, it in combinations:
            total_combos += 1
            print("Combo: ", t, c, g, s, si, n, it)
            try:
                if t.lower() in ['Freight Payable'.lower()]:
                    t = 'freight_invoice_payable'
                elif t.lower() in ['Freight Receivable'.lower()]:
                    t = 'freight_invoice_receivable'
                else:
                    t = t.lower().replace(' ', '_')
                if it.lower() == 'Transfers'.lower():
                    it = 'title_transfers'
                elif it.lower() == 'Custom item'.lower():
                    it = 'custom'
                elif it.lower() == 'Grain Levy'.lower():
                    it = 'levy'
                elif it.lower() == 'Blending Fees'.lower():
                    it = 'blending_fees'
                elif it.lower() == 'Applications'.lower():
                    it = 'chemical_applications'
                else:
                    it = it.lower()
                c = Commodity.find_by_name(c, 1).id
                g = Grade.find_by_name(g.replace(' %', '%'), Grade.objects.filter(commodity_id=c)).id if g else None
                n = company_ngrs.filter(ngr_number__iexact=n).first().id if n else None
                si = company_sites.filter(name__iexact=si).first().id if si else None
                farm_xero_category = None
                ent_xero_category = None
                if farm:
                    farm_xero_category = get_category('Farm', farm)
                if ent:
                    ent_xero_category = get_category('Ent', ent)
                mapped = {
                    "transaction_type": t,
                    "commodity_id": c,
                    "grade_id": g,
                    "season": s,
                    "site_id": si,
                    "ngr_id": n,
                    "item_type": it,
                    "xero_account": xero_account,
                    "xero_item_code": xero_item_code,
                    "farm": farm_xero_category,
                    "ent": ent_xero_category,
                    'company': company
                }
                result.append(mapped)
            except Exception as e:
                print(f"Exception: {e}")
        data.extend(result)
    print(f"Total Rows: {total_rows}, Total Combinations: {total_combos}")
    return data


def create_mappings(data):
    results = []
    for d in data:
        ent = d.pop('ent', None)
        farm = d.pop('farm', None)
        if ent:
            ent.save()
        if farm:
            farm.save()
        mapping = XeroMapping(**d)
        mapping.save()
        errors = get(ent, 'errors') or get(farm, 'errors') or get(mapping, 'errors')
        if not errors and mapping.id and (farm or ent):
            if ent and ent.id:
                mapping.tracking_categories.add(ent)
            if farm and farm.id:
                mapping.tracking_categories.add(farm)
        result = {'ent': ent, 'farm': farm, 'mapping': mapping, 'errors': errors}
        results.append(result)
    return results, [x for x in results if x['errors']]