import os

from django.conf import settings
from django.db import models
from django.utils import timezone
from pydash import get

from core.services.internal.errbit import ERRBIT_LOGGER
from core.common.models import RawModel
from core.common.tasks import COMMON_TASK_MAPPING
from core.companies.tasks import COMPANY_TASK_MAPPING
from core.company_sites.tasks import COMPANY_SITE_TASK_MAPPING
from core.contracts.tasks import CONTRACT_TASK_MAPPING
from core.farms.tasks import FARM_TASK_MAPPING
from core.freights.tasks import FREIGHT_CONTRACT_TASK_MAPPING
from core.invoices.tasks import INVOICE_TASK_MAPPING
from core.loads.tasks import LOAD_TASK_MAPPING
from core.profiles.tasks import EMPLOYEE_TASK_MAPPING
from core.vendor_decs.tasks import VENDOR_DEC_TASK_MAPPING

TASK_MAPPING = {
    **CONTRACT_TASK_MAPPING,
    **EMPLOYEE_TASK_MAPPING,
    **FARM_TASK_MAPPING,
    **FREIGHT_CONTRACT_TASK_MAPPING,
    **INVOICE_TASK_MAPPING,
    **COMMON_TASK_MAPPING,
    **COMPANY_SITE_TASK_MAPPING,
    **VENDOR_DEC_TASK_MAPPING,
    **COMPANY_TASK_MAPPING,
    **LOAD_TASK_MAPPING,
}


class Job(RawModel):
    TEMP_STATUS = '__temp__'
    BATCH_STATUS = '__batch__'
    BATCH_SUCCESS_STATUS = '__batch_success__'
    BATCH_FAILED_STATUS = '__batch_failed__'
    XERO_PENDING = '__xero_pending__'
    XERO_BATCH_STARTED = '__xero_batch_started__'

    REVOKE_JOB_RESULT = 'REVOKE_JOB'

    class Meta:
        app_label = 'jobs'
        db_table = 'jobs'
        ordering = ['-id']
        indexes = [
            models.Index(
                name='type_status_pending',
                fields=['type', '-id'],
                condition=(models.Q(status='pending'))
            ),
        ]

    STATUSES = (
        ('scheduled', 'scheduled'),
        ('pending', 'pending'),
        ('started', 'started'),
        ('finished', 'finished'),
        ('failed', 'failed'),
        ('revoked', 'revoked'),
        (TEMP_STATUS, TEMP_STATUS),
        (BATCH_STATUS, BATCH_STATUS),
        (BATCH_SUCCESS_STATUS, BATCH_SUCCESS_STATUS),
        (BATCH_FAILED_STATUS, BATCH_FAILED_STATUS),
        (XERO_PENDING, XERO_PENDING),
        (XERO_BATCH_STARTED, XERO_BATCH_STARTED),
    )
    started_at = models.DateTimeField(null=True, blank=True)
    type = models.CharField(max_length=200)
    status = models.CharField(choices=STATUSES, max_length=50, db_index=True)
    params = models.JSONField(null=True, blank=True)
    result = models.IntegerField(null=True, blank=True)
    artifacts = models.JSONField(null=True, blank=True)
    celery_task_id = models.CharField(max_length=200, null=True, blank=True)
    queue = models.CharField(max_length=200, default='default')

    @classmethod
    def schedule_job_for_task(cls, job_type, params=None, ignore_if_pending=False):
        try:
            if ignore_if_pending and cls.objects.filter(status='pending', type=job_type, params=params).exists():
                return False
            cls(status='pending', type=job_type, params=params).save()
        except Exception as ex:  # pylint: disable=broad-except
            ERRBIT_LOGGER.log(ex)

    @property
    def task_fn(self):
        return TASK_MAPPING[self.type]

    @property
    def celery_task_fn(self):
        return self.task_fn.delay

    @property
    def should_run_now(self):
        return settings.RUN_JOBS_INLINE or not os.environ.get('ENV', None)

    def save(self, *args, **kwargs):  # pylint: disable=arguments-differ
        super().save(*args, **kwargs)
        if self.should_trigger_on_save():
            self.trigger()

    def should_trigger_on_save(self):
        ENV = os.environ.get('ENV', None)
        if ENV == 'ci':
            return False
        if self.id and self.status == 'pending':
            if ENV == 'dev':
                return settings.RUN_JOBS_IN_DEV_MODE or settings.RUN_JOBS_INLINE
            return True

    def trigger(self):
        """
        Triggers the task inline or schedules it for later based on settings.
        Also records CELERY TASK ID if the task is not run inline.
        """
        queue = self.params.get('queue', 'default') if isinstance(self.params, dict) else 'default'
        celery_task = self._run()
        task_id = get(celery_task, 'id')
        if task_id and isinstance(task_id, str) and not self.should_run_now:
            self._update(celery_task_id=task_id, queue=queue)

    def revoke(self):
        self._update(status='revoked')
        if self.celery_task_id:
            from core.celeryconf import app
            # revoke job needs to be last as if the worker is executing this, it will die at this point
            app.control.revoke(self.celery_task_id, terminate=True, signal='SIGKILL')

    def run_now(self):
        """
        Triggers the task inline only.
        Should only be used in non-prod environments.
        """
        self._run_inline()

    def _run(self, now=False):
        return self._run_inline() if (now or self.should_run_now) else self._run_async()

    def _run_inline(self):
        return self.__execute(self.task_fn, False)

    def _run_async(self):
        return self.__execute(self.celery_task_fn)

    def __execute(self, fn, assign_queue=True):
        assign_queue = assign_queue or get(fn, '__qualname__') in ['Task.delay', 'Task.apply_async']
        queue = 'default'
        if isinstance(self.params, dict):
            if assign_queue:
                return fn(self.id, **self.params, queue=queue)  # pylint: disable=not-a-mapping
            return fn(self.id, **self.params)  # pylint: disable=not-a-mapping
        if assign_queue:
            return fn(self.id, self.params, queue=queue)
        return fn(self.id, self.params)

    def _update(self, **kwargs):
        Job.objects.filter(id=self.id).update(updated_at=timezone.now(), **kwargs)
