# Generated by Django 3.2.6 on 2021-11-29 03:51

from django.db import migrations


# moving old angustifoloius grade data to Lupins (Angustifoloius) commodity"
def update_lupins(apps, schema_editor):
    Load = apps.get_model('loads', 'Load')
    Contract = apps.get_model('contracts', 'Contract')
    TitleTransfer = apps.get_model('contracts', 'TitleTransfer')
    FreightContract = apps.get_model('freights', 'FreightContract')
    FreightOrder = apps.get_model('freights', 'FreightOrder')
    Storage = apps.get_model('farms', 'Storage')
    CashPrices = apps.get_model('cash_board', 'CashPrices')  # no variety
    FreightSlot = apps.get_model('company_sites', 'FreightSlot')  # no variety
    CommodityVendorDec = apps.get_model('vendor_decs', 'CommodityVendorDec')

    for klass in [
        Load, Contract, TitleTransfer, Storage,
        CashPrices, FreightSlot, CommodityVendorDec
    ]:
        klass.objects.filter(grade_id__in=[72]).update(commodity_id=54)

    for klass in [
        FreightContract, FreightOrder
    ]:
        klass.objects.filter(planned_grade_id__in=[72]).update(commodity_id=54)


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0021_auto_20210820_0832'),
    ]

    operations = [
        migrations.RunPython(update_lupins)
    ]
