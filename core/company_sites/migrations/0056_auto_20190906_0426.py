# Generated by Django 2.1.9 on 2019-09-06 04:26

from django.db import migrations

def update_default_settings_for_slots(apps, schema_editor):
    SiteManagementSettings = apps.get_model('company_sites', 'SiteManagementSettings')
    for settings in SiteManagementSettings.objects.all():
        customer_field = [field for field in settings.fields if field['id'] == 'customer'][0]
        customer_field['customisable'] = True
        settings.save()

class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0055_auto_20190729_0943'),
    ]

    operations = [
        migrations.RunPython(update_default_settings_for_slots)
    ]
