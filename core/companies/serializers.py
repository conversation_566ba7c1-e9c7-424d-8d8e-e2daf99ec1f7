from django.db.models import Q
from pydash import get
from rest_framework.fields import Serial<PERSON>MethodField, Char<PERSON><PERSON>, BooleanField, IntegerField, FloatField
from rest_framework.serializers import ModelSerializer

from core.common.constants import SYSTEM_COMPANY_IDS
from core.companies.models import (
    Company, SSOSetting, XeroMapping, ExternalPortal, XeroTrackingCategory, CompanyGroup, ApplicationRate,
    ImpexDocsProductMapping
)
from core.loads.models import Load, RegradeReseason
from core.profiles.models import Employee
from core.locations.serializers import LocationSerializer


class CompanyDetailSerializer(ModelSerializer):
    class Meta:
        model = Company
        fields = '__all__'


class CompanySerializer(ModelSerializer):
    key_contact_name_for_user = SerializerMethodField()
    address = SerializerMethodField()
    business_type = SerializerMethodField()
    can_register = SerializerMethodField()
    mobile = SerializerMethodField()
    company_groups = SerializerMethodField()
    group_names = SerializerMethodField()
    created_by = SerializerMethodField()

    class Meta:
        model = Company
        ref_name = 'companies-company-serializer'
        fields = (
            'id', 'name', 'abn', 'mobile', 'website', 'type_id', 'is_registered_text',
            'address', 'business_type', 'key_contact_name_for_user', 'can_register',
            'is_default_directory_company', 'company_groups', 'group_names', 'entity_name',
            'transaction_participation', 'created_at', 'created_by',
        )

    def get_created_by(self, obj):
        user = self.context.get('request').user
        if user.is_staff:
            return f"{obj.created_by.name} ({obj.created_by.company.name})"
        return None

    def get_key_contact_name_for_user(self, obj):
        return obj.key_contact_name_for_user(self.context.get('request').user)

    def get_address(self, obj):
        return obj.address.address

    def get_business_type(self, obj):
        return obj.type.display_name

    def get_company_groups(self, obj):
        user = self.context.get('request').user
        return [{'name': group.name, 'owner_company_id': group.owner_company_id}
                for group in obj.groups.filter(owner_company_id=user.company_id)]

    def get_group_names(self, obj):
        user = self.context.get('request').user
        return ', '.join([group.name for group in obj.groups.filter(owner_company_id=user.company_id)])

    def get_can_register(self, obj):
        return obj.can_register(self.context.get('request').user)

    def get_mobile(self, obj):
        user = self.context.get('request').user
        if user.is_staff:
            return obj.mobile

        return obj.key_contact_mobile_for_user(self.context.get('request').user) or obj.mobile


class CompanyNamesSerializer(ModelSerializer):
    name = CharField(source='formatted_name', allow_null=True, allow_blank=True)
    score = FloatField(default=0, read_only=True)
    rank = FloatField(default=0, read_only=True)

    def __init__(self, *args, **kwargs):  # pylint: disable=too-many-branches
        super().__init__(*args, **kwargs)
        query_params = get(self.context, 'request.query_params', False)
        with_ranking = get(query_params, 'with_ranking', False) in ['true', True, 'True']
        if not with_ranking:
            self.fields.pop('score', None)
            self.fields.pop('rank', None)
            self.fields.pop('entity', None)
            self.fields.pop('entity_name', None)

    class Meta:
        model = Company
        fields = ('name', 'id', 'type_id', 'transaction_participation', 'score', 'rank', 'entity', 'entity_name')


class CompanyNameWithABNSerializer(ModelSerializer):
    company_groups = SerializerMethodField()
    name = SerializerMethodField()

    class Meta:
        model = Company
        fields = ('name', 'id', 'abn', 'company_groups', 'type_id', 'transaction_participation')

    def __init__(self, *args, **kwargs):  # pylint: disable=too-many-branches
        super().__init__(*args, **kwargs)
        exclude_groups = get(self.context, 'request.query_params.excludeGroups', False) in ['true', True, 'True']
        if exclude_groups:
            self.fields.pop('company_groups', None)

    @staticmethod
    def get_name(obj):
        return obj.formatted_name

    def get_company_groups(self, obj):
        user = self.context.get('request').user
        return [{'name': group.name,
                 'owner_company_id': group.owner_company_id,
                 'business_type_id': group.business_type_id
                 } for group in obj.groups.filter(owner_company_id=user.company_id)]


class CompanyNameWithGroupNameSerializer(ModelSerializer):
    company_groups = SerializerMethodField()

    class Meta:
        model = Company
        fields = ('name', 'id', 'display_name', 'company_groups')

    def get_company_groups(self, obj):
        return [{'name': group.name, 'owner_company_id': group.owner_company_id} for group in obj.groups.all()]


class CompanyGroupsSerializer(ModelSerializer):
    company_ids_belonging_to_group = SerializerMethodField()

    class Meta:
        model = CompanyGroup
        fields = ('id', 'name', 'type', 'owner_company_id', 'company_ids_belonging_to_group', 'business_type_id')

    def get_company_ids_belonging_to_group(self, obj):
        user = self.context['request'].user
        companies = Company.directory_companies_only(user.company_id)
        return [{'id': company.id, 'name': company.name} for company in companies.filter(groups=obj)]


class CompanyMinimalisticSerializer(ModelSerializer):
    rel = SerializerMethodField()
    brokerages_for_user = SerializerMethodField()
    is_managed_by_user = SerializerMethodField()
    party_type = SerializerMethodField()
    company_groups = SerializerMethodField()
    score = FloatField(default=0, read_only=True)
    rank = FloatField(default=0, read_only=True)

    def __init__(self, *args, **kwargs):  # pylint: disable=too-many-branches
        super().__init__(*args, **kwargs)
        query_params = get(self.context, 'request.query_params', False)
        exclude_groups = get(query_params, 'excludeGroups', False) in ['true', True, 'True']
        with_ranking = get(query_params, 'with_ranking', False) in ['true', True, 'True']
        if exclude_groups:
            self.fields.pop('company_groups', None)
        if not with_ranking:
            self.fields.pop('score', None)
            self.fields.pop('rank', None)

    class Meta:
        model = Company
        fields = (
            'id', 'name', 'display_name', 'abn', 'type_id', 'entity', 'is_registered', 'plan_type',
            'entity_name', 'brokerages_for_user', 'rel', 'company_id', 'is_managed_by_user',
            'is_allowed_as_buyer_for_pool_contract', 'payment_term_id', 'start_of_week', 'end_of_week',
            'party_type', 'transaction_participation', 'is_variety_mandatory', 'load_type_for_variety_mandatory',
            'user_type_for_variety_mandatory', 'purchase_contract_creation_restriction',
            'sale_contract_creation_restriction', 'mobile_orders_within_delivery_range',
            'company_groups', 'is_variety_mandatory_in_commodity_dec', 'additional_mass_limit_codes',
            'contract_number_mandatory_in_transfers', 'has_external_booking_enabled', 'fill_docket',
            'is_halal_declaration_mandatory', 'score', 'rank', 'show_throughput_load', 'payment_term_id',
            'customised_pack_and_ship'
        )

    def get_brokerages_for_user(self, obj):
        from core.contracts.serializers import BrokerageSerializer
        return BrokerageSerializer(getattr(obj, 'user_brokerages', []), many=True).data

    def get_rel(self, _):
        return 'companies'

    def get_company_groups(self, obj):
        return [{'name': group.name, 'owner_company_id': group.owner_company_id} for group in obj.groups.all()]

    def get_party_type(self, _):
        return 'company'

    def get_is_managed_by_user(self, obj):
        return obj.is_managed_by_user(self.context.get('request').user)


class CompanyMobileSerializer(ModelSerializer):
    from core.company_sites.serializers import CompanySiteMobileSerializer

    sites = CompanySiteMobileSerializer(source='farm_set.all', many=True)
    address = LocationSerializer()

    class Meta:
        model = Company
        fields = (
            'id', 'name', 'type_id', 'abn', 'entity_name', 'business_name', 'mobile', 'website', 'entity',
            'is_active', 'sites', 'address',
        )


class CompanySyncManagerSerializer(ModelSerializer):
    customer_optional = SerializerMethodField()
    is_slot_booking_on = SerializerMethodField()
    is_variety_mandatory = SerializerMethodField()
    allowed_truck_company_ids = SerializerMethodField()
    load_by_load_transfer = SerializerMethodField()
    score = FloatField(default=0, read_only=True)
    rank = FloatField(default=0, read_only=True)

    class Meta:
        model = Company
        fields = (
            'id', 'name', 'type_id', 'abn', 'entity_name', 'mobile', 'website', 'address_id', 'is_active',
            'entity', 'customer_optional', 'is_slot_booking_on', 'enable_my_stocks_on_mobile',
            'is_variety_mandatory_in_commodity_dec', 'additional_mass_limit_codes', 'allowed_truck_company_ids',
            'have_field', 'is_variety_mandatory', 'load_type_for_variety_mandatory', 'user_type_for_variety_mandatory',
            'is_halal_declaration_mandatory', 'contract_number_mandatory_in_transfers',
            'enable_transfer_to_approved_buyers', 'load_by_load_transfer', 'pickup_details_warning',
            'delivery_details_warning', 'show_additional_specs', 'cor_texts', 'has_ngr_credentials',
            'show_throughput_load', 'hide_contract_and_order_for_drivers', 'customised_pack_and_ship',
            'rank', 'score'
        )

    def __init__(self, *args, **kwargs):  # pylint: disable=too-many-branches
        super().__init__(*args, **kwargs)
        query_params = get(self.context, 'request.query_params', False)
        with_ranking = get(query_params, 'with_ranking', False) in ['true', True, 'True']
        if not with_ranking:
            self.fields.pop('score', None)
            self.fields.pop('rank', None)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if not instance.is_variety_mandatory:
            data.pop('is_variety_mandatory', None)
            data.pop('load_type_for_variety_mandatory', None)
            data.pop('user_type_for_variety_mandatory', None)
        if not instance.is_halal_declaration_mandatory:
            data.pop('is_halal_declaration_mandatory', None)
        if not instance.contract_number_mandatory_in_transfers:
            data.pop('contract_number_mandatory_in_transfers', None)
        if not instance.enable_transfer_to_approved_buyers:
            data.pop('enable_transfer_to_approved_buyers', None)
        if not get(data, 'load_by_load_transfer'):
            data.pop('load_by_load_transfer', None)
        if not get(data, 'pickup_details_warning'):
            data.pop('pickup_details_warning', None)
        if not get(data, 'delivery_details_warning'):
            data.pop('delivery_details_warning', None)
        if not get(data, 'show_additional_specs'):
            data.pop('show_additional_specs', None)
        if not get(data, 'cor_texts'):
            data.pop('cor_texts', None)
        if not get(data, 'has_ngr_credentials'):
            data.pop('has_ngr_credentials', None)
        if not get(data, 'hide_contract_and_order_for_drivers'):
            data.pop('hide_contract_and_order_for_drivers', None)
        if not get(data, 'customised_pack_and_ship'):
            data.pop('customised_pack_and_ship', None)
        return data

    @staticmethod
    def get_customer_optional(obj):
        return get(obj, 'sitemanagementsettings.customer_optional')

    @staticmethod
    def get_is_variety_mandatory(obj):
        return obj.is_variety_mandatory and 'title_transfers_and_cash_outs' in obj.load_type_for_variety_mandatory

    @staticmethod
    def get_is_slot_booking_on(obj):
        return get(obj, 'sitemanagementsettings.order_booking')

    @staticmethod
    def get_allowed_truck_company_ids(obj):
        return get(obj, 'sitemanagementsettings.allowed_truck_company_ids', [])

    @staticmethod
    def get_load_by_load_transfer(obj):
        return get(obj, 'sitemanagementsettings.load_by_load_transfer')

class CompanyGlobalSerializer(ModelSerializer):
    url = SerializerMethodField()
    name = SerializerMethodField()

    class Meta:
        model = Company
        fields = ('id', 'abn', 'name', 'url')

    @staticmethod
    def get_url(obj):
        return "#/companies/{id}/details".format(id=obj.company_id)

    @staticmethod
    def get_name(obj):
        return "{} | {}".format(obj.abn, obj.name)


class CompanyApprovedBuyersSerializer(ModelSerializer):
    address = SerializerMethodField()
    mobile = SerializerMethodField()

    class Meta:
        model = Company
        fields = ('id', 'abn', 'name', 'mobile', 'address')

    def get_mobile(self, obj):
        user = self.context.get('request').user
        if user.is_staff:
            return obj.mobile

        return obj.key_contact_mobile_for_user(self.context.get('request').user) or obj.mobile

    def get_address(self, obj):
        return obj.address.address


class SSOSettingSerializer(ModelSerializer):
    company_name = CharField(source='company.name')
    logo_url = CharField(source='company.logo_url', allow_blank=True, allow_null=True)
    extras = SerializerMethodField()

    class Meta:
        model = SSOSetting
        fields = (
            'id', 'extras', 'company_id', 'agrichain_auth_enabled', 'is_active', 'company_name', 'logo_url',
            'login_uri'
        )

    def get_extras(self, obj):
        platform = get(self.context, 'view.kwargs.platform', 'web')
        return get(obj.extras, platform)


class CompanySiteLoadsSerializer(ModelSerializer):
    movement_number = CharField(source='freight_movement_number')
    shrinkage_parent = SerializerMethodField()
    stock_swap_identifier = SerializerMethodField()
    regrade_identifier = SerializerMethodField()
    stock_owner = SerializerMethodField()
    sub_type = CharField(source='type')
    storage = CharField(source='storage.name', allow_blank=True, allow_null=True)
    stock_owner_id = IntegerField(source='ngr.company_id', allow_null=True)
    stock_owner_company = CharField(source='ngr.company.formatted_name', allow_blank=True, allow_null=True)
    freight_provider = CharField(source='freight_provider.formatted_name', allow_blank=True, allow_null=True)
    rego = CharField(source='truck.rego', allow_blank=True, allow_null=True)
    creator = CharField(source='created_by.name')
    commodity = CharField(source='commodity.display_name')
    grade = CharField(source='grade.name', allow_blank=True, allow_null=True)
    variety = CharField(source='variety.name', allow_blank=True, allow_null=True)
    ngr = CharField(source='ngr.ngr_number', allow_blank=True, allow_null=True)
    ngr_company_id = IntegerField(source='ngr.company_id', allow_null=True)
    is_cash_priced = BooleanField(source='contract.is_cash_priced', allow_null=True)
    site_name = CharField(source='storage.farm.name', allow_blank=True, allow_null=True)
    farm_id = IntegerField(source='storage.farm_id', allow_null=True)
    farm_company_id = IntegerField(source='storage.farm.company_id', allow_null=True)
    farm_company_name = CharField(source='storage.farm.company.formatted_name', allow_blank=True, allow_null=True)
    title_transfer_creator = CharField(source='title_transfer.created_by.name', allow_blank=True, allow_null=True)
    class Meta:
        model = Load
        fields = (
            'id', 'date_time' ,'sub_type', 'freight_provider',
            'site_name', 'storage' , 'ngr', 'stock_owner', 'rego', 'commodity', 'grade', 'variety',
            'season', 'creator', 'movement_number', 'contract_number',
            'title_transfer_number', 'order_number', 'option_type', 'is_cash_priced',
            'external_reference', 'shrinkage_parent', 'stock_swap_identifier', 'extras',
            'checkpoint_id', 'external_system', 'freight_movement_id', 'storage_id',
            'commodity_id', 'grade_id', 'ngr_id', 'farm_id', 'variety_id', 'title_transfer_id',
            'stock_owner_id', 'specs', 'comment', 'source_field_id', 'title_transfer_creator',
            'tonnage_with_shrinkage', 'farm_company_id', 'regrade_identifier',
            'stock_owner_company', 'net_weight', 'farm_company_name', 'ngr_company_id',
        )

    @staticmethod
    def get_shrinkage_parent(obj):
        if obj.source != Load.SHRINKAGE_LOAD:
            return
        parent = Load.objects.filter(system_loads__id=obj.id).first()
        if not parent:
            return
        return parent.freight_movement_number or parent.title_transfer_number or 'Direct Load'

    @staticmethod
    def get_stock_swap_identifier(obj):
        return get(obj, 'extras.identifier')

    @staticmethod
    def get_regrade_identifier(obj):
        regraded_load = RegradeReseason.objects.filter(Q(inload_id=obj.id) | Q(outload_id=obj.id)).first()
        return get(regraded_load, 'identifier', '')

    def get_stock_owner(self, obj):
        if get(obj, 'ngr.company_id') in SYSTEM_COMPANY_IDS and get(obj, 'extras.warehouse'):
            return 'Warehouse'
        return get(obj, 'ngr.owner_company')


class ImpexDocsProductMappingSerializer(ModelSerializer):
    commodity = CharField(allow_blank=True, allow_null=True, source='commodity.display_name')
    grade = CharField(allow_blank=True, allow_null=True, source='grade.name')
    variety = CharField(allow_blank=True, allow_null=True, source='variety.name')

    class Meta:
        model = ImpexDocsProductMapping
        fields = (
            'id', 'company_id', 'grade_id', 'commodity_id', 'variety_id',  'commodity', 'grade', 'variety',
            'product_code',
        )


class XeroTrackingCategorySerializer(ModelSerializer):
    class Meta:
        model = XeroTrackingCategory
        fields = ('id', 'xero_tracking_category_id', 'name', 'xero_option_id', 'option_name')


class XeroMappingSerializer(ModelSerializer):
    commodity = CharField(allow_blank=True, allow_null=True, source='commodity.display_name')
    grade = CharField(allow_blank=True, allow_null=True, source='grade.name')
    item_type_display_name = CharField(allow_null=True, allow_blank=True, source='get_item_type_display')
    site_name = CharField(allow_null=True, allow_blank=True, source='site.display_name')
    tracking_categories = XeroTrackingCategorySerializer(many=True)
    ngr_number = CharField(source='ngr.ngr_number', read_only=True)
    rego = CharField(source='truck.rego', read_only=True)

    class Meta:
        model = XeroMapping
        fields = (
            'id', 'company_id', 'grade_id', 'commodity_id', 'season', 'xero_account', 'item_type',
            'commodity', 'grade', 'transaction_type', 'item_type', 'item_type_display_name', 'tracking_categories',
            'xero_item_code', 'site_id', 'site_name', 'ngr_id', 'ngr_number', 'rego'
        )


class ExternalPortalSerializer(ModelSerializer):
    created_by = CharField(source='created_by.name', read_only=True)
    updated_by = CharField(source='updated_by.name', allow_null=True, allow_blank=True, read_only=True)

    class Meta:
        model = ExternalPortal
        fields = (
            'id', 'company_id', 'portal', 'url', 'username', 'password', 'created_by', 'created_at',
            'updated_at', 'updated_by', 'created_by_id'
        )


class CompanyMinimalSerializer(ModelSerializer):
    address = SerializerMethodField()

    class Meta:
        model = Company
        fields = ('id', 'name', 'type_id', 'entity_name', 'abn', 'mobile', 'website', 'address', 'business_name',
                  'is_registered', 'country_id', 'country_code', 'start_of_week', 'end_of_week', 'payment_term_id')

    def get_address(self, obj):
        return {
            'address': obj.address.address,
            'latitude': obj.address.latitude,
            'longitude': obj.address.longitude
        } if obj.address else None


class ApplicationRateSerializer(ModelSerializer):
    application_name = CharField(source='commodity.display_name')
    class Meta:
        model = ApplicationRate
        fields = ('id', 'commodity_id', 'company_id', 'rate', 'application_name', 'rate_display', 'updated_at')

class CompanyLookupSerializer(ModelSerializer):
    company_admins = SerializerMethodField()
    class Meta:
        model = Company
        fields = (
            'id',
            'business_name',
            'entity_name',
            'is_registered',
            'transaction_participation',
            'abn',
            'company_admins'
        )

    def get_company_admins(self, obj):
        return list(obj.get_company_admins().values('first_name', 'last_name'))

class CompanyEmployeeNamesSerializer(ModelSerializer):
    class Meta:
        model = Employee
        fields = ('first_name', 'last_name')
