import csv
import datetime
import io
import json
import os
import subprocess

from celery.schedules import crontab
from celery.utils.log import get_task_logger
from django.core.serializers.json import DjangoJ<PERSON>NEncoder
from django.utils import timezone
from pydash import get

from core.celeryconf import app
from core.common.constants import AEST_TZ, AU_ROOT_USER_ID
from core.common.utils import save_download_locally, cd_temp
from core.company_sites.constants import (MARK_SLOT_URGENT_HOURS_FROM_START, FLEET_TRUCK_NOTICE_HOURS_FROM_START,
                                          EXTERNAL_SFTP_SLOT_PUSH_CSV_HEADERS, SITE_COMPARISON_CSV_REPORT_HEADERS,
                                          CARRIER_COMPARISON_CSV_REPORT_HEADERS)
from core.freights.constants import NULL_STATUSES
from core.jobs.decorators import update_job
from core.services.external.ably import ABLY
from core.services.external.aws import S3
from core.services.external.integrations.mauri import MauriSFTPService, MauriTruckScaleReceiptService
from core.services.internal.errbit import ERRBIT_LOGGER
from core.timezones.utils import DateTimeUtil

FORMAT = "%Y-%m-%d %H:%M:%S"
logger = get_task_logger(__name__)


@app.task(ignore_result=True)
def notify_fleet_truck_to_bookie():
    now = timezone.now()
    four_hours_from_now = (now + datetime.timedelta(hours=FLEET_TRUCK_NOTICE_HOURS_FROM_START)).strftime(FORMAT)
    from core.company_sites.models import FreightSlot
    slots = FreightSlot.objects.filter(
        status='booked', truck__rego='FLEET',
        start__lte=four_hours_from_now, start__gte=now.strftime(FORMAT), extras__fleet_reminder_sent_at__isnull=True
    ).order_by('start')
    for slot in slots:
        if slot.is_booked_by_bookie:
            slot.send_fleet_truck_booking_mail()


@app.task(ignore_result=True)
def flash_fleet_booked_slot():
    from core.company_sites.models import FreightSlot
    now = timezone.now()
    six_hours_from_now = (
        now + datetime.timedelta(hours=MARK_SLOT_URGENT_HOURS_FROM_START)
    ).strftime(FORMAT)

    FreightSlot.objects.filter(
        status='booked', truck__rego='FLEET',
        start__lte=six_hours_from_now,
        start__gte=now.strftime(FORMAT)
    ).update(
        is_urgent_for_provider=True,
        is_urgent_for_manager=True
    )


@app.on_after_finalize.connect
def setup_periodic_tasks(**kwargs):  # pylint: disable=unused-argument
    # every 30 minutes
    app.add_periodic_task(60*30, flash_fleet_booked_slot.s(), name='flash_fleet_booked_slot')

    # every 30 minutes
    app.add_periodic_task(
        crontab(minute='*/30'), notify_fleet_truck_to_bookie.s(), name='notify_fleet_truck_to_bookie'
    )

    # every 5 minutes
    app.add_periodic_task(
        crontab(minute='*/5'), push_slots_events_to_sftp.s(), name='push_slots_events_to_sftp'
    )

    # every 15 minutes
    app.add_periodic_task(
        crontab(minute='*/15'), read_slot_receipts_from_sftp.s(), name='read_slot_receipts_from_sftp'
    )

    # Daily at 12:30 AM AEST
    app.add_periodic_task(
        crontab(hour=14, minute=30),
        expire_external_bookings_for_expired_orders.s(),
        name='expire_external_bookings_for_expired_orders'
    )


@app.task(ignore_result=True)
@update_job
def generate_site_comparison_report(download_id, tz, extra_filters, company_id, site_ids=None, start=None, end=None): # pylint: disable=too-many-locals
    from core.company_sites.models import SlotReport
    from core.profiles.models import Download
    download_qs = Download.objects.filter(id=download_id).select_related('employee')
    download = download_qs.first()
    user = download.employee
    try:
        buff = io.StringIO()
        writer = csv.writer(buff, dialect='excel', delimiter=',')
        writer.writerow(SITE_COMPARISON_CSV_REPORT_HEADERS)
        slots = SlotReport.get_site_comparison_csv_rows(
            start=start, end=end, tz=tz, company_id=company_id,
            site_ids=site_ids, extra_filters=extra_filters
        )
        for slot in slots:
            writer.writerow(slot)
        buff2 = io.BytesIO(buff.getvalue().encode())
        save_download_locally(download, buff2)
        S3.upload(download.file_path, buff2)
        download_qs.update(status='ready')
    except Exception as ex:  # pylint: disable=broad-except
        ERRBIT_LOGGER.log(ex)
        download_qs.update(status='failed', failure_reason=str(ex))

    ABLY.publish(
        'slots-csv-ready',
        str(user.id),
        json.dumps(
            download.to_dict(),
            sort_keys=True,
            indent=1,
            cls=DjangoJSONEncoder,
        )
    )


@app.task(ignore_result=True)
@update_job
def generate_carrier_comparison_report(download_id, tz, company_id, provider_ids, site_ids=None, start=None, end=None): # pylint: disable=too-many-locals
    from core.company_sites.models import SlotReport
    from core.profiles.models import Download
    download_qs = Download.objects.filter(id=download_id).select_related('employee')
    download = download_qs.first()
    user = download.employee
    try:
        buff = io.StringIO()
        writer = csv.writer(buff, dialect='excel', delimiter=',')
        writer.writerow(CARRIER_COMPARISON_CSV_REPORT_HEADERS)
        slots = SlotReport.get_carrier_comparison_csv_rows(
            start=start, end=end, tz=tz, company_id=company_id,
            site_ids=site_ids, provider_ids=provider_ids
        )
        for slot in slots:
            writer.writerow(slot)
        buff2 = io.BytesIO(buff.getvalue().encode())
        save_download_locally(download, buff2)
        S3.upload(download.file_path, buff2)
        download_qs.update(status='ready')
    except Exception as ex:  # pylint: disable=broad-except
        ERRBIT_LOGGER.log(ex)
        download_qs.update(status='failed', failure_reason=str(ex))

    ABLY.publish(
        'slots-csv-ready',
        str(user.id),
        json.dumps(
            download.to_dict(),
            sort_keys=True,
            indent=1,
            cls=DjangoJSONEncoder,
        )
    )


@app.task(ignore_result=True)
@update_job
def generate_slots_csv(download_id, tz, site_ids=None, start=None, end=None, force=False):  # pylint: disable=too-many-locals
    from core.company_sites.models import FreightSlot
    from core.profiles.models import Download
    download_qs = Download.objects.filter(id=download_id).select_related('employee')
    download = download_qs.first()
    user = download.employee
    cwd = cd_temp()
    try:
        slots = FreightSlot.company_slots(
            user.company_id, '1970-01-01', '3001-01-01',
        ).order_by('-start')

        if site_ids:
            slots = slots.filter(site_id__in=site_ids)
        if isinstance(start, list):
            start = start[0]
        if isinstance(end, list):
            end = end[0]
        if start and end:
            slots = slots.filter(start__gte=start, end__lte=end)

        if not force and Download.should_request_offline(slots):
            download.revoke()
            from core.jobs.models import Job
            return Job.REVOKE_JOB_RESULT

        logger.info('Writing generate_slots_csv:Download:{} file to tmp directory: {}'.format(download_id, cwd))
        with open(download.name, 'w', newline='', encoding='UTF-8') as csvfile:
            writer = csv.writer(csvfile, dialect='excel', delimiter=',')
            writer.writerow(user.company.sitemanagementsettings.csv_header())
            csv_slots = FreightSlot.to_localized_report_data(slots, tz)
            for slot in csv_slots:
                writer.writerow(slot)
        file_path = os.path.abspath(download.name)
        logger.info("Uploading generate_slots_csv:Download:{} file to S3: {}".format(download_id, file_path))
        result = S3.upload_file_using_stream(
            key=download.file_path,
            file_path=file_path,
            binary=True,
        )
        logger.info("Upload Result generate_slots_csv:Download:{}: {}".format(download_id, result))
        download_qs.update(status='ready')

        save_download_locally(download=download, buff=None, file_path=file_path)

        tmp_dir = os.getcwd()
        if tmp_dir:
            subprocess.call(['rm', '-rf', tmp_dir])
    except Exception as ex:  # pylint: disable=broad-except
        ERRBIT_LOGGER.log(ex)
        download_qs.update(status='failed', failure_reason=str(ex))
    finally:
        os.chdir(cwd)

    ABLY.publish(
        'slots-csv-ready',
        str(user.id),
        json.dumps(
            download.to_dict(),
            sort_keys=True,
            indent=1,
            cls=DjangoJSONEncoder,
        )
    )


@app.task(ignore_result=True)
@update_job
def generate_booking_cancelled_slots_csv(  # pylint: disable=too-many-locals
        download_id, tz, site_ids=None, start=None, end=None):
    from core.company_sites.models import FreightSlot
    from core.profiles.models import Download
    download_qs = Download.objects.filter(id=download_id).select_related('employee')
    download = download_qs.first()
    user = download.employee
    try:
        slots = FreightSlot.company_slots(
            user.company_id, '1970-01-01', '3001-01-01',
        ).order_by('-start')

        if site_ids:
            slots = slots.filter(site_id__in=site_ids)
        if isinstance(start, list):
            start = start[0]
        if isinstance(end, list):
            end = end[0]
        if start and end:
            slots = slots.filter(start__gte=start, end__lte=end)

        buff = io.StringIO()
        writer = csv.writer(buff, dialect='excel', delimiter=',')
        writer.writerow(user.company.sitemanagementsettings.csv_header(booking_cancellation=True))
        csv_slots = FreightSlot.to_cancelled_booking_report(slots, tz)
        for slot in csv_slots:
            writer.writerow(slot)
        buff2 = io.BytesIO(buff.getvalue().encode())
        save_download_locally(download, buff2)
        S3.upload(download.file_path, buff2)
        download_qs.update(status='ready')
    except Exception as ex:  # pylint: disable=broad-except
        ERRBIT_LOGGER.log(ex)
        download_qs.update(status='failed', failure_reason=str(ex))

    ABLY.publish(
        'slots-csv-ready',
        str(user.id),
        json.dumps(
            download.to_dict(),
            sort_keys=True,
            indent=1,
            cls=DjangoJSONEncoder,
        )
    )


@app.task(ignore_result=True)
@update_job
def generate_orders_delivery_csv(
        download_id, tz, company_id, site_ids=None, seller_ids=None, start=None, end=None, partial_date_range=False
):  #  pylint: disable=too-many-locals
    from core.company_sites.models import FreightSlot
    from core.profiles.models import Download
    download_qs = Download.objects.filter(id=download_id).select_related('employee')
    download = download_qs.first()
    user = download.employee
    try:
        seller_order_stats, customer_order_stats, orders = FreightSlot.order_delivery_stats(
            company_id, site_ids, seller_ids, start, end, partial_date_range
        )
        buff = io.StringIO()
        writer = csv.writer(buff, dialect='excel', delimiter=',')
        order_header = [
            'Order Type',
            'Contract No',
            'Order No',
            'Status',
            'Customer',
            'Customer NGR',
            'Seller',
            'Seller NGR',
            'Pickup Site',
            'Pickup Order No',
            'Buyer',
            'Buyer NGR',
            'Delivery Site',
            'Delivery Order No',
            'Delivery Start Date',
            'Delivery End Date',
            'Commodity',
            'Variety',
            'Grade',
            'Season',
            'Tonnage (MT)',
            'Original Tonnage (MT)',
            'Delivered (MT)',
            'Outstanding (MT)'
        ]
        stats_header = [
            'Tonnage Ordered',
            'Number of slots booked',
            'Tonnage Booked',
            'Tonnage Delivered',
            'Undelivered Tonnage',
            'Unbooked Tonnage',
            'Number of No Show / Incomplete Slots',
            'Tonnage of No Show / Incomplete slots',
            'Number of Slots Cancelled',
            'Tonnages of Slots Cancelled',
            'Number of Slots Cancelled & not Rebooked',
            'Tonnage of Slots Cancelled and not rebooked',
            'Number of slots Rejected',
            'Total tonnage rejected'
        ]
        order_header_length = len(order_header)
        diff_header_len = order_header_length - len(stats_header)
        date_range_text = 'The delivery date range of these orders only partially ' \
                          'match with the selected date range of this report' if partial_date_range else ''

        def add_empty_lines(count=1):
            for _ in range(count):
                writer.writerow((order_header_length + 1) * [''])

        def write_to_csv(prefix, _stats):
            writer.writerow(
                [
                    prefix, _stats['total_tonnage'], _stats['booked_slots'], _stats['booked_tonnage'],
                    _stats['delivered_tonnage'], _stats['undelivered_tonnage'],
                    _stats['total_tonnage'] - _stats['booked_tonnage'], _stats['incomplete_slots'],
                    _stats['incomplete_tonnage'], _stats['cancelled_rebooked_slots'],
                    _stats['cancelled_rebooked_tonnage'], _stats['cancelled_not_rebooked_slots'],
                    _stats['cancelled_not_rebooked_tonnage'], _stats['rejected_slots'],
                    _stats['rejected_tonnage'], *(diff_header_len * [''])
                ]
            )

        for _, data in {**seller_order_stats, **customer_order_stats}.items():
            writer.writerow([data['name'], *(order_header_length * [''])])
            writer.writerow(['', *stats_header])
            write_to_csv('Total', data['data']['total'])
            for commodity_id, commodity_stats in data['data']['commodities'].items():  # pylint: disable=unused-variable
                write_to_csv(commodity_stats['name'], commodity_stats['total'])
                for grade_id, grade_stats in commodity_stats['grades'].items():  # pylint: disable=unused-variable
                    write_to_csv(grade_stats['name'], grade_stats)
            add_empty_lines(2)

        writer.writerow(['Orders Included in the Report', date_range_text, *((order_header_length - 1) * [''])])
        writer.writerow(['', *order_header])
        for order in orders:
            seller, buyer, customer = None, None, None
            if order.is_customer_only:
                customer = get(order, 'customer')
                order_type = 'Customer Only Freight Order'
            else:
                seller = get(order, 'seller') or get(order, 'commodity_contract.seller')
                buyer = get(order, 'buyer') or get(order, 'commodity_contract.buyer')
                order_type = 'Seller To Buyer Freight Order'
            writer.writerow([
                '',
                order_type,
                get(order, 'commodity_contract.reference_number'), order.identifier,
                order.order_status_display_name(None),
                get(customer, 'display_name'), get(customer, 'ngr_number'),
                get(seller, 'display_name'), get(seller, 'ngr_number'), order.pickup_site_name,
                get(order, 'freight_pickup.order_number'),
                get(buyer, 'display_name'), get(buyer, 'ngr_number'), order.delivery_site_name,
                get(order, 'freight_delivery.order_number'),
                DateTimeUtil.localize_date(get(order, 'freight_pickup.date_time'), tz),
                DateTimeUtil.localize_date(get(order, 'freight_delivery.date_time'), tz),
                order.commodity.display_name, get(order, 'variety.name'), order.grade_name,
                order.season, order.planned_tonnage, order.original_tonnage,
                order.delivered_tonnage, order.outstanding_tonnage,
            ])

        buff2 = io.BytesIO(buff.getvalue().encode())
        save_download_locally(download, buff2)
        S3.upload(download.file_path, buff2)
        download_qs.update(status='ready')
    except Exception as ex:  # pylint: disable=broad-except
        ERRBIT_LOGGER.log(ex)
        download_qs.update(status='failed', failure_reason=str(ex))

    ABLY.publish(
        'slots-csv-ready',
        str(user.id),
        json.dumps(
            download.to_dict(),
            sort_keys=True,
            indent=1,
            cls=DjangoJSONEncoder,
        )
    )


@app.task(ignore_result=True)
@update_job
def slot_update(slot_id, data, last_version, trailer_slots=None, old_slot_movement_id=None, chemical_applications=None):
    from core.company_sites.models import FreightSlot
    slot = FreightSlot.objects.filter(id=slot_id).first()
    if slot:
        slot.post_quick_update(data, last_version, trailer_slots, old_slot_movement_id, chemical_applications)


@app.task(ignore_result=True)
def push_slots_events_to_sftp(remove_tmp_file=True):  # pylint: disable=too-many-locals
    from core.jobs.models import Job
    from core.farms.models import Farm
    from core.company_sites.models import FreightSlot

    jobs = Job.objects.filter(type='push_slot_to_sftp', status=Job.TEMP_STATUS).order_by('created_at')
    if not jobs.exists():
        return

    job_ids = list(jobs.values_list('id', flat=True))
    jobs.update(status=Job.BATCH_STATUS)  # to lock from other jobs
    jobs = Job.objects.filter(id__in=job_ids)

    site_ids = set(jobs.values_list('params__site_id', flat=True))
    for site_id in site_ids:
        slot_ids = []
        site_jobs = jobs.filter(params__site_id=site_id).order_by('created_at')
        if not site_jobs.exists():
            continue
        site = Farm.objects.filter(id=site_id).first()
        sftp_configurations = site._external_connection_configurations
        if not sftp_configurations:
            continue
        buff = io.StringIO()
        writer = csv.writer(
            buff, dialect='excel', delimiter=',', quoting=csv.QUOTE_NONE, quotechar='\"', escapechar='"')
        writer.writerow(EXTERNAL_SFTP_SLOT_PUSH_CSV_HEADERS)
        for job in site_jobs:
            csv_row = job.params.get('csv_row')
            if csv_row:
                slot_ids.append(job.params.get('slot_id'))
                writer.writerow(csv_row)
        buff2 = io.BytesIO(buff.getvalue().encode())
        now = timezone.now()
        filename = f'slots_{now.strftime("%Y%m%d%H%M%S.%f")}.csv'
        filepath = f'/tmp/{filename}'
        with open(filepath, 'wb') as file:
            content = buff2.read()
            file.write(content.decode('utf-8').replace('""', '"').encode())
            file.close()
        try:
            service = MauriSFTPService(
                hostname=sftp_configurations['hostname'],
                username=sftp_configurations['username'],
                password=sftp_configurations['password'],
            )
            service.put_slot_csv(file)
        except Exception as ex:  # pylint: disable=broad-except
            ERRBIT_LOGGER.raise_errbit(
                f'PushSlotsEventsToSFTP: Unable to push events to SFTP (Exception {str(ex)}) for jobs: {list(site_jobs.values_list("id", flat=True))}'  # pylint: disable=line-too-long
            )
            site_jobs.update(status=Job.BATCH_FAILED_STATUS)
        else:
            site_jobs.update(
                status=Job.BATCH_SUCCESS_STATUS, artifacts={'filename': filename, 'timestamp': str(now)}, result=1)
            if slot_ids:
                FreightSlot.objects.filter(id__in=slot_ids).update(_last_pushed_at=now)
            if remove_tmp_file:
                os.remove(file.name)


@app.task
def read_slot_receipts_from_sftp():
    from core.farms.models import Farm
    for site in Farm.objects.filter(_external_connection_configurations__isnull=False):
        sftp_configurations = site._external_connection_configurations
        service = MauriTruckScaleReceiptService(
            hostname=sftp_configurations['hostname'],
            username=sftp_configurations['username'],
            password=sftp_configurations['password'],
        )
        service.process()
        return service.results


@app.task(ignore_result=True)
@update_job
def send_slot_update_alerts(action, outload_slot_ids=None, inload_slot_ids=None, movement_id=None, order_id=None,  # pylint: disable=too-many-locals
                            updated_fields=None, slot_updated_by_id=None):
    from core.freights.models import FreightContract, FreightOrder
    from core.company_sites.models import FreightSlot
    outload_slots = FreightSlot.objects.filter(id__in=outload_slot_ids) if outload_slot_ids else None
    inload_slots = FreightSlot.objects.filter(id__in=inload_slot_ids) if inload_slot_ids else None
    outload_site = outload_slots.first().site if outload_slots else None
    inload_site = inload_slots.first().site if inload_slots else None
    order = FreightOrder.objects.filter(id=order_id).first() if order_id else None
    movement = FreightContract.objects.filter(id__in=movement_id) if movement_id else None
    if outload_site:
        outload_slots.update(updated_by_id=slot_updated_by_id) if slot_updated_by_id else None
        outload_site.send_slot_update_alert(action, outload_slots, movement, order, updated_fields)
    if inload_site:
        inload_slots.update(updated_by_id=slot_updated_by_id) if slot_updated_by_id else None
        inload_site.send_slot_update_alert(action, inload_slots, movement, order, updated_fields)


@app.task
def expire_external_bookings_for_expired_orders():  # pylint: disable=too-many-locals
    from core.companies.models import ExternalBookingConnection
    from core.farms.models import Farm
    from core.company_sites.models import FreightSlot
    from core.loads.models import Load
    external_booking_company_ids = ExternalBookingConnection.objects.values_list('company_id', flat=True)
    farm_ids = Farm.objects.filter(company_id__in=external_booking_company_ids).values_list('id', flat=True)
    current_date_time = DateTimeUtil.localize(timezone.now(), AEST_TZ)
    current_date = datetime.datetime(
        current_date_time.year, current_date_time.month, current_date_time.day, 0, 0, 0, tzinfo=datetime.timezone.utc
    )
    slots = FreightSlot.objects.filter(
        site_id__in=farm_ids, status='booked', movement__order__freight_delivery__date_time__lt=current_date,
    ).exclude(
        movement__status__in=NULL_STATUSES
    ).exclude(movement__loads__type=Load.OUTLOAD)
    counter_slot_mapping = {}
    for slot in slots:
        slot.refresh_from_db()
        data = {
            "siteId": slot.site_id,
            "type": slot.type,
            "pits": slot.pits,
            "booking_number": slot.booking_number,
            "delivery_order_number": slot.delivery_order_number,
            "order_id": slot.order_id,
            "status": "cancelled",
            "commodity_id": slot.commodity_id,
            "grade_id": slot.grade_id,
            "tonnage": slot.tonnage,
            "driver_id": slot.driver_id,
            "priority": False,
            "truck_id": slot.truck_id,
            "customer": slot.customer,
            "freight_provider_id": slot.freight_provider_id,
            "sub_freight_provider_id": slot.sub_freight_provider_id,
            "restricted_visible_to_carrier": False,
            "season": slot.season,
            "start": slot.start.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
            "end": slot.end.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
            "comment": "Incompletion Reason: Order is expired",
            "updated_at": datetime.datetime.timestamp(timezone.now()),
            "settings_updated_at": datetime.datetime.timestamp(timezone.now()),
        }
        if slot.status == 'planned':  # Adding slot details to the slot moved to planned status
            mapped_counter_slot = get(counter_slot_mapping, slot.id)
            data['order_id'] = get(mapped_counter_slot, 'order_id')
            data["commodity_id"] = get(mapped_counter_slot, 'commodity_id')
            data["grade_id"] = get(mapped_counter_slot, 'grade_id')
            data["tonnage"] = get(mapped_counter_slot, 'tonnage')
            data["driver_id"] = get(mapped_counter_slot, 'driver_id')
            data["truck_id"] = get(mapped_counter_slot, 'truck_id')
            data["freight_provider_id"] = get(mapped_counter_slot, 'freight_provider_id')
            data["sub_freight_provider_id"] = get(mapped_counter_slot, 'sub_freight_provider_id')
        elif slot.status == 'booked':
            counter_slot_type = 'outload' if slot.type == 'inload' else 'inload'
            movement = get(slot, 'movement')
            counter_slot = get(movement, f'{counter_slot_type}_slot')
            if counter_slot and counter_slot.status == 'booked':
                counter_slot_mapping.update({counter_slot.id: slot})
        from core.profiles.models import Employee
        user = Employee.objects.filter(id=AU_ROOT_USER_ID).first()
        success, instance = FreightSlot.update_from_view(user, data, slot.id, None, False)
        if success:
            logger.info("Booking {} expired on {}".format(
                get(instance, 'movement.identifier'), current_date.strftime("%Y-%m-%d %H:%M:%S")
            ))
        else:
            logger.info("Booking expire for {} failed on {}".format(
                get(instance, 'movement.identifier'), current_date.strftime("%Y-%m-%d %H:%M:%S")
            ))
    if counter_slot_mapping:
        planned_slots = FreightSlot.objects.filter(id__in=counter_slot_mapping.keys())
        for slot in planned_slots:
            mapped_counter_slot = get(counter_slot_mapping, slot.id)
            slot.movement_id = get(mapped_counter_slot, 'movement_id')
            slot.save()


COMPANY_SITE_TASK_MAPPING = {
    'generate_slots_csv': generate_slots_csv,
    'generate_site_comparison_report': generate_site_comparison_report,
    'generate_carrier_comparison_report': generate_carrier_comparison_report,
    'generate_booking_cancelled_slots_csv': generate_booking_cancelled_slots_csv,
    'generate_orders_delivery_csv': generate_orders_delivery_csv,
    'slot_update': slot_update,
    'send_slot_update_alerts': send_slot_update_alerts
}
