# Generated by Django 2.1.3 on 2019-01-12 05:17

from django.db import migrations

def farm_and_company_directory_updates(apps, schema_editor):
    FarmDirectory = apps.get_model('companies', 'FarmDirectory')
    AddedCompany = apps.get_model('companies', 'AddedCompany')
    for directory in FarmDirectory.objects.select_related('farm').all():
        AddedCompany.objects.get_or_create(
            subject_company_id=directory.company_id,
            object_company_id=directory.farm.company_id
        )


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0018_auto_20190111_1146'),
    ]

    operations = [
        migrations.RunPython(farm_and_company_directory_updates)
    ]
