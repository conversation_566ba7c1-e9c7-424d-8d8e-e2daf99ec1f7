from rest_framework.fields import Serial<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ield
from rest_framework.serializers import ModelSerializer
from pydash import get

from core.alerts.models import Alert, Recipient, SiteRecipient


class AlertsListingSerializer(ModelSerializer):
    class Meta:
        model = Alert
        fields = ('id', 'alert_display_name', 'description', 'alert_type', 'recipients', 'frequency_display_name',
                  'is_active')


class RecipientSerializer(ModelSerializer):
    employees = SerializerMethodField()

    class Meta:
        model = Recipient
        fields = ('id', 'alert_id', 'party', 'created_at', 'updated_at', 'created_by_id',
                  'updated_by_id', 'employee_roles', 'employees', 'custom_emails')

    def get_employees(self, obj):
        return obj.employees.values_list('id', flat=True) if obj.is_party_own_company else []


class SiteRecipientSerializer(ModelSerializer):
    recipient = RecipientSerializer()
    site = SerializerMethodField()

    class Meta:
        model = SiteRecipient
        fields = ('id', 'site', 'recipient')

    def get_site(self, obj):
        return {
            'id': get(obj, 'site_id'),
            'name': get(obj, 'site.name'),
            'company_id': get(obj, 'site.company_id'),
            'is_active': get(obj, 'site.is_active')
            }


class AlertSerializer(ModelSerializer):
    recipients = SerializerMethodField()
    site_recipients = SerializerMethodField()
    include_own_site_loads_in_acquisition_report = BooleanField(
        source='company.include_own_site_loads_in_acquisition_report')

    class Meta:
        model = Alert
        fields = ('id', 'name', 'alert_type', 'channel', 'recipients', 'frequency_display_name', 'company_id',
                  'is_active', 'site_recipients', 'hours_before_trigger',
                  'created_at', 'updated_at', 'created_by_id', 'updated_by_id',
                  'include_own_site_loads_in_acquisition_report')

    def get_recipients(self, obj):
        return RecipientSerializer(obj.recipient_queryset(), many=True).data

    def get_site_recipients(self, obj):
        return SiteRecipientSerializer(obj.siterecipient_set.filter(), many=True).data
