import csv
import sys

csvfile = open(sys.argv[-1], 'r')
fieldnames = ("Spec CODE","Spec Name","Data Validation: Input Min","Data Validation: Input Max")
reader = list(csv.DictReader(csvfile, fieldnames))
res = []
for i, row in enumerate(reader[:7]):
    if(i!=0 and i < 7):
        _json = {}
        _dict = dict(row)
        _json['order'] = i
        _json['code'] = _dict['Spec CODE']
        _json['name'] = _dict['Spec Name']
        _json['min'] = float(_dict['Data Validation: Input Min'])
        _json['max'] = float(_dict['Data Validation: Input Max'])
        res.append(_json)

print(res)
