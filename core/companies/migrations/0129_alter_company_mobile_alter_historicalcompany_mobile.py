# Generated by Django 4.1.7 on 2023-05-16 10:19

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0128_merge_20230515_1038'),
    ]

    operations = [
        migrations.AlterField(
            model_name='company',
            name='mobile',
            field=models.CharField(max_length=10, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08|5)[0-9]+$', 'Must start with 1, 02, 03, 04, 07 or 08')]),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='mobile',
            field=models.CharField(max_length=10, validators=[django.core.validators.RegexValidator('^(1|02|03|04|07|08|5)[0-9]+$', 'Must start with 1, 02, 03, 04, 07 or 08')]),
        ),
    ]
