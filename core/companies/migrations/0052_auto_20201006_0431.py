# Generated by Django 2.2.13 on 2020-10-06 04:31

from datetime import timedelta
from django.db import migrations


def remove_inappropriate_data(apps, schema_editor):
    model_klass = apps.get_model('companies', 'Company')
    model_klass.objects.filter(
        idle_period_to_logoff__gt=timedelta(days=90)
    ).update(
        idle_period_to_logoff=timedelta(days=60)
    )

class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0051_auto_20200817_1704'),
    ]

    operations = [
        migrations.RunPython(remove_inappropriate_data)
    ]
