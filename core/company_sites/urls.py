from django.urls import path

from .views import (CompanySiteFreightSlotForwardDeleteView, CompanySiteFreightSlotAllDeleteView,
                    CompanySitePlannedFreightSlotsView, SiteComparisonReportView, CarrierComparisonReportView,
                    SlotOrderDeliveryReportView, CompanySiteSettingsView, CompanySiteFreightSlotsHistoryView,
                    SiteBookingCancelledSlotsCSVView, FreightSlotVerboseView, FreightSlotTruckConfigView,
                    FreightSlotStatsView, FreightSlotCanCancelView, SlotRawHistoryView,
                    CompanySiteFreightSlotMixedDeleteView)
from .views import CompanySiteFreightSlotView, SiteSlotsCSVView, SlotOrdersView
from .views import FreightSlotCommentsView, CompanySiteFreightSlotHistoryView
from .views import SiteFreightSlotsView, CompanySiteFreightSlotsView, FreightSlotCommentView
from .views import SiteOpenOrCompanyBookedFreightSlotsView, SlotsMovementView, SlotMarkBookFromForwardStatusView
from .views import FreightSlotView

app_name = 'core.company_sites'

urlpatterns = [
    path('slots/', SiteFreightSlotsView.as_view(), name='freight_slot-site'),
    path('slots/stats/', FreightSlotStatsView.as_view(), name='freight_slot-stats'),
    path('slots/csv/', SiteSlotsCSVView.as_view(), name='freight_slot-site-csv'),
    path(
        'slots/booking-cancelled/csv/',
        SiteBookingCancelledSlotsCSVView.as_view(),
        name='freight_slot-site-booking-cancelled-csv'
    ),
    path('slots/open-or-self/', SiteOpenOrCompanyBookedFreightSlotsView.as_view(), name='freight_slot'),
    path('<int:site_id>/settings/', CompanySiteSettingsView.as_view(), name='site-settings'),
    path('<int:site_id>/slots/', CompanySiteFreightSlotsView.as_view(), name='freight_slot-site-multiple'),
    path('<int:site_id>/slots/planned/', CompanySitePlannedFreightSlotsView.as_view(), name='freight_slot-planned'),
    path('<int:site_id>/slots/<int:slot_id>/', CompanySiteFreightSlotView.as_view(), name='freight_slot-site'),
    path('slots/<int:slot_id>/', FreightSlotView.as_view(), name='freight_slot-details'),
    path('slots/<int:slot_id>/history/', SlotRawHistoryView.as_view(), name='freight_slot-raw-history'),  # raw history
    path(
        '<int:site_id>/slots/<int:slot_id>/history/',
        CompanySiteFreightSlotHistoryView.as_view(),
        name='freight_slot-history'
    ),
    path(
        '<int:site_id>/slots/history/',
        CompanySiteFreightSlotsHistoryView.as_view(),
        name='freight_slot-histories'
    ),
    path(
        '<int:site_id>/slots/<int:slot_id>/forward/',
        CompanySiteFreightSlotForwardDeleteView.as_view(),
        name='freight_slot-site-forward-delete'
    ),
    path(
        '<int:site_id>/slots/<int:slot_id>/all/',
        CompanySiteFreightSlotAllDeleteView.as_view(),
        name='freight_slot-site-all-delete'
    ),
    path(
        '<int:site_id>/slots/mixed-delete/',
        CompanySiteFreightSlotMixedDeleteView.as_view(),
        name='freight_slot-site-mixed-delete'
    ),
    path(
        '<int:site_id>/slots/<int:slot_id>/comments/',
        FreightSlotCommentsView.as_view(),
        name='slot_comment'
    ),
    path(
        'comments/<int:comment_id>/',
        FreightSlotCommentView.as_view(),
        name='slot_comment-single'
    ),
    path(
        'slots/<int:slot_id>/orders/',
        SlotOrdersView.as_view(),
        name='slot-orders-ref'
    ),
    path(
        'slots/movements/',
        SlotsMovementView.as_view(),
        name='slot-movements'
    ),
    path(
        'slots/mark-booked/',
        SlotMarkBookFromForwardStatusView.as_view(),
        name='freight_slot-mark-book'
    ),
    path(
        'reports/site_comparison/',
        SiteComparisonReportView.as_view(),
        name='freight_slot-site-comparison-report'
    ),
    path(
        'reports/carrier_comparison/',
        CarrierComparisonReportView.as_view(),
        name='freight_slot-carrier-comparison-report'
    ),
    path(
        'reports/order_delivery/',
        SlotOrderDeliveryReportView.as_view(),
        name='freight_slot-order-delivery-report'
    ),
    path(
        'slots/<int:slot_id>/verbose/',
        FreightSlotVerboseView.as_view(),
        name='freight_slot--verbose-site'
    ),
    path(
        'slots/<int:slot_id>/truck/',
        FreightSlotTruckConfigView.as_view(),
        name='freight_slot-truck'
    ),
    path(
        'slots/<int:slot_id>/cancel/',
        FreightSlotCanCancelView.as_view(),
        name='freight_slot-cancel'
    )
]
