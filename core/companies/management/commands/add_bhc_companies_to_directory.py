from django.core.management import BaseCommand

from core.common.constants import BHC_TYPE_ID
from core.companies.models import Company, AddedCompany


class Command(BaseCommand):
    help = 'Add all BHC companies in directories of all companies'

    def handle(self, *args, **options):
        bulk_creates = []

        for company in Company.objects.filter(type_id=BHC_TYPE_ID):
            bulk_creates.append(AddedCompany(subject_company=None, object_company=company))

        AddedCompany.objects.bulk_create(bulk_creates)
