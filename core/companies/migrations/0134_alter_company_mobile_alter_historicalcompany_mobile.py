# Generated by Django 4.1.10 on 2023-07-07 03:25

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0133_remove_company_fax_remove_historicalcompany_fax'),
    ]

    operations = [
        migrations.AlterField(
            model_name='company',
            name='mobile',
            field=models.CharField(max_length=10, validators=[django.core.validators.RegexValidator('^[0-9]+$', 'Must be valid phone/mobile number')]),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='mobile',
            field=models.Char<PERSON>ield(max_length=10, validators=[django.core.validators.RegexValidator('^[0-9]+$', 'Must be valid phone/mobile number')]),
        ),
    ]
