# Generated by Django 4.1.7 on 2023-03-30 03:43

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0005_alter_alert_name_alter_historicalalert_name'),
    ]

    operations = [
        migrations.AlterField(
            model_name='historicalrecipient',
            name='custom_emails',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, default=list, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='historicalrecipient',
            name='employee_roles',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, default=list, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='recipient',
            name='custom_emails',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.Char<PERSON><PERSON>(max_length=255), blank=True, default=list, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='recipient',
            name='employee_roles',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=255), blank=True, default=list, null=True, size=None),
        ),
    ]
