# Generated by Django 2.1.3 on 2018-12-28 05:47

from django.db import migrations


def farm_directory_updates(apps, schema_editor):
    Farm = apps.get_model('farms', 'Farm')
    FarmDirectory = apps.get_model('companies', 'FarmDirectory')
    for farm in Farm.objects.iterator():
        if farm.company.owner_company_id:
            directory = FarmDirectory(farm_id=farm.id, company_id=farm.company_id)
            directory.save()

        if farm.company.owner_company_id:
            directory = FarmDirectory(farm_id=farm.id, company_id=farm.company_id)
            directory.save()


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0013_auto_20181227_1014'),
    ]

    operations = [
        migrations.RunPython(farm_directory_updates),
    ]
