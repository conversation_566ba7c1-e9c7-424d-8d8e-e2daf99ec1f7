import csv
import json
import sys

res = []
csvfile = open('./new_commodities.csv', 'r')
fieldnames = ("CommodityName",)
reader = csv.DictReader(csvfile, fieldnames)
_id = 28
existing_commodities = ['wheat', 'barley', 'canola', 'sorghum', 'lentils', 'chickpeas (desi)', 'chickpeas (kabuli)', 'oats', 'triticale', 'maize', 'broad beans', 'faba beans', 'lupins', 'mungbeans', 'field peas', 'vetch', 'sunflower', 'soybean', 'cottonseed', 'safflower', 'linseed', 'canola meal', 'soybean meal', 'pke', 'cereal hay & silage', 'legume and pasture hay & silage', 'fertiliser']
for i, row in enumerate(reader):
    if (i!=0):
        data = dict(row)
        if data['CommodityName'].lower() not in existing_commodities:
            _json = {
                "model": "commodities.commodity",
                "pk": _id,
                "fields": {
                    "name": data['CommodityName'],
                    "specs": [{}],
                    "created_at": "2019-05-01T08:00:00+00:00",
                    "updated_at": "2018-05-01T08:00:00+00:00",
                },
            }
            _id += 1

            res.append(_json)

print(json.dumps(res))
