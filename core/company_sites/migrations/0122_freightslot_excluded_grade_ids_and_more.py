# Generated by Django 4.0.8 on 2023-01-30 05:50

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0121_alter_freightslot_tonnage_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='freightslot',
            name='excluded_grade_ids',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), blank=True, default=None, null=True, size=None),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='excluded_grade_ids',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.IntegerField(), blank=True, default=None, null=True, size=None),
        ),
    ]
