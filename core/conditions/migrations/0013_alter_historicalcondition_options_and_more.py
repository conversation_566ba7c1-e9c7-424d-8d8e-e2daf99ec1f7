# Generated by Django 4.0.4 on 2022-05-13 10:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('conditions', '0012_auto_20190620_0411'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='historicalcondition',
            options={'get_latest_by': ('history_date', 'history_id'), 'ordering': ('-history_date', '-history_id'), 'verbose_name': 'historical condition', 'verbose_name_plural': 'historical conditions'},
        ),
        migrations.AlterField(
            model_name='condition',
            name='created_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='condition',
            name='updated_by',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalcondition',
            name='created_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='historicalcondition',
            name='history_date',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='historicalcondition',
            name='updated_by',
            field=models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL),
        ),
    ]
