# Generated by Django 2.1.7 on 2019-04-29 05:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion

def populate_from_mill_priority(apps, schema_editor):
    FreightSlot = apps.get_model('company_sites', 'FreightSlot')
    for slot in FreightSlot.objects.all():
        slot.priority = slot.mill_priority
        slot.save()

class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0021_auto_20190424_0847'),
    ]

    operations = [
        migrations.AddField(
            model_name='freightslot',
            name='priority',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='historicalfreightslot',
            name='priority',
            field=models.BooleanField(default=False),
        ),
        migrations.RunPython(populate_from_mill_priority),
    ]
