# Generated by Django 4.1.9 on 2023-08-18 04:04

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0143_auto_20230816_0337'),
    ]

    operations = [
        migrations.AlterField(
            model_name='company',
            name='is_variety_mandatory',
            field=models.BooleanField(blank=True, default=False, null=True),
        ),
        migrations.AlterField(
            model_name='company',
            name='load_type_for_variety_mandatory',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('inload', 'Inload'), ('inload_and_outload', 'Inload and Outload'), ('title_transfers_and_cash_outs', 'Title Transfers and Cash Outs')], max_length=255), blank=True, default=list, null=True, size=None),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='is_variety_mandatory',
            field=models.BooleanField(blank=True, default=False, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='historicalcompany',
            name='load_type_for_variety_mandatory',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(choices=[('inload', 'Inload'), ('inload_and_outload', 'Inload and Outload'), ('title_transfers_and_cash_outs', 'Title Transfers and Cash Outs')], max_length=255), blank=True, default=list, null=True, size=None),
        ),
    ]
