from functools import wraps

from pydash import compact, get
from django.utils import timezone
from core.services.internal.errbit import ERRBIT_LOGGER


def update_job(fn):
    @wraps(fn)
    def wrapper(job_id, *args, **kwargs):
        from core.jobs.models import Job
        job = Job.objects.filter(id=job_id).first()
        if job and job.status not in ['started', 'failed', 'finished']:
            job.status = 'started'
            job.started_at = timezone.now()
            job.save()
            args_list = compact(list(args))
            if not len(args_list):
                args = ()
            try:
                kwargs.pop('queue', None) if kwargs else None
                result = fn(*args, **kwargs)
                if not isinstance(result, int):
                    job.artifacts = result
                else:
                    job.result = result
                job.status = 'revoked' if result == Job.REVOKE_JOB_RESULT else 'finished'
            except Exception as ex:  # pylint: disable=broad-except
                ERRBIT_LOGGER.log(ex)
                job.result = None
                if not job.artifacts:
                    job.artifacts = get(ex, 'info') or str(ex)
                job.status = 'failed'
            finally:
                job.save()
    return wrapper
