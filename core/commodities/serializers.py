from rest_framework.serializers import ModelSerializer
from rest_framework.fields import Serializer<PERSON>ethod<PERSON>ield, <PERSON>r<PERSON>ield
from core.commodities.models import Commodity, Variety, Grade, Material


class GradeSerializer(ModelSerializer):
    class Meta:
        model = Grade
        fields = ('id', 'name', 'commodity_id', 'specs', 'gta_code',
                  'order', 'entity', 'is_active', 'category', )


class VarietySerializer(ModelSerializer):
    class Meta:
        model = Variety
        fields = ('id', 'name', 'commodity_id',
                  'details', 'entity', 'is_active', )


class CommodityMobileSerializer(ModelSerializer):
    varieties = VarietySerializer(source='variety_set.all', many=True)
    grades = GradeSerializer(source='grade_set.all', many=True)

    class Meta:
        model = Commodity
        fields = (
            'id', 'name', 'display_name', 'specs', 'varieties', 'grades', 'entity',
            'is_active', 'unit', 'quantity_label', 'short_quantity_label', 'type', 'unit_conversions',
            'is_quantity_based'
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.context.get('is_brief'):
            self.fields.pop('varieties', None)
            self.fields.pop('grades', None)

class CommodityMinimalSerializer(ModelSerializer):
    class Meta:
        model = Commodity
        fields = ('id', 'name', 'display_name', 'unit')


class CommodityMinimalWithSpecsSerializer(ModelSerializer):
    class Meta:
        model = Commodity
        fields = ('id', 'name', 'display_name', 'specs', 'unit', 'is_strict_quantity_based', 'is_quantity_based')


class GradeMinimalSerializer(ModelSerializer):
    class Meta:
        model = Grade
        fields = ('id', 'name', 'commodity_id')


class VarietyMinimalSerializer(ModelSerializer):
    class Meta:
        model = Variety
        fields = ('id', 'name', 'commodity_id', 'epr')


class GradeWithCommodityNameSerializer(ModelSerializer):
    # used where across commodity grades list is required

    name = SerializerMethodField()
    commodity_name = CharField(source='commodity.display_name', read_only=True)
    raw_name = CharField(source='name', read_only=True)

    class Meta:
        model = Grade
        fields = ('id', 'name', 'commodity_id', 'commodity_name', 'raw_name')

    @staticmethod
    def get_name(obj):
        return obj.commodity.display_name + ' - ' + obj.name


class MaterialSerializer(ModelSerializer):
    class Meta:
        model = Material
        fields = ('id', 'name', 'material_class', 'entity')


class CommoditySerializer(ModelSerializer):
    varieties = VarietySerializer(source='variety_set.all', many=True)
    grades = GradeSerializer(source='grade_set.all', many=True)
    levy = SerializerMethodField()

    class Meta:
        model = Commodity
        fields = (
            'id', 'name', 'display_name', 'specs', 'varieties', 'grades', 'entity',
            'is_active', 'unit', 'quantity_label', 'short_quantity_label', 'type', 'unit_conversions',
            'is_quantity_based', 'price_unit', 'is_strict_quantity_based', 'country_id', 'created_at', 'created_by_id',
            'material_class', 'sustainable', 'tonnage_unit', 'updated_at', 'updated_by_id', 'levy'
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        inclusions = self.context.get('inclusions')
        if 'variety' not in inclusions:
            self.fields.pop('varieties', None)
        if 'grade' not in inclusions:
            self.fields.pop('grades', None)

    @staticmethod
    def get_levy(obj):
        return obj.levy()
