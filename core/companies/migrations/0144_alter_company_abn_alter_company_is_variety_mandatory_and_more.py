# Generated by Django 4.1.9 on 2023-08-17 07:37

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0143_auto_20230816_0337'),
    ]

    operations = [
        migrations.<PERSON>er<PERSON>ield(
            model_name='company',
            name='abn',
            field=models.CharField(blank=True, max_length=11, null=True),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='abn',
            field=models.CharField(blank=True, max_length=11, null=True),
        ),
        migrations.AddConstraint(
            model_name='company',
            constraint=models.UniqueConstraint(condition=models.Q(('abn', '-'), _negated=True), fields=('abn',), name='uniq_abn_except_dash'),
        ),
    ]
