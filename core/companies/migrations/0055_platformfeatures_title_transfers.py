# Generated by Django 2.2.13 on 2021-04-09 17:18

from django.db import migrations, models

def setTitleTransferTrue(apps, schema_editor):
    PlatformFeatures = apps.get_model('companies', 'PlatformFeatures')
    PlatformFeatures.objects.filter(plan_type='premium').update(title_transfers=True)


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0054_auto_20210208_0834'),
    ]

    operations = [
        migrations.AddField(
            model_name='platformfeatures',
            name='title_transfers',
            field=models.BooleanField(default=False),
        ),
        migrations.RunPython(setTitleTransferTrue),
    ]
