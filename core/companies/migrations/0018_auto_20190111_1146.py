# Generated by Django 2.1.3 on 2019-01-11 11:46

from django.db import migrations

def farm_updates(apps, schema_editor):
    Farm = apps.get_model('farms', 'Farm')

    for farm in Farm.objects.filter(
            company__owner_company_id__isnull=False,
            office_id__isnull=True,
            broker_company_id__isnull=True,
            created_by__company__type_id=2,
    ):
        farm.broker_company_id = farm.created_by.company_id
        farm.is_grower_acceptance_required = False
        farm.is_grower_request_accepted_by_broker = True
        farm.save()

class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0017_auto_20190111_1007'),
    ]

    operations = [
        migrations.RunPython(farm_updates)
    ]
