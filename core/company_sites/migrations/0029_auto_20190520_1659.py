# Generated by Django 2.1.7 on 2019-05-20 16:59

import core.company_sites.models
from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0032_auto_20190401_0931'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('company_sites', '0028_auto_20190513_0514'),
    ]

    operations = [
        migrations.CreateModel(
            name='SiteManagementSettings',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('colors', django.contrib.postgres.fields.jsonb.JSONField(default=core.company_sites.models.default_statuses)),
                ('company', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='companies.Company')),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='company_sites_sitemanagementsettings_related_created_by', related_query_name='company_sites_sitemanagementsettingss_created_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='company_sites_sitemanagementsettings_related_updated_by', related_query_name='company_sites_sitemanagementsettingss_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'site_management_settings',
            },
        ),
    ]
