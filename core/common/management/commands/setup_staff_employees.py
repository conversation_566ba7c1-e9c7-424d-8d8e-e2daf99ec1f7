from django.conf import settings
from django.core.management import BaseCommand
from django.db import models

from core.common.constants import SYSTEM_COMPANY_IDS
from core.common.utils import generate_random_password
from core.companies.models import Company
from core.profiles.models import Employee, EmployeeType

STAFF = [{
     'Employee': '<PERSON>',
     'FirstName': '<PERSON>',
     'LastName': '<PERSON>',
     'Username': 'laurence',
     'Email': '<EMAIL>',
     'Alias': 'LJ000',
     'Role': 'company_admin',
     'IsSuperUser': 'Yes',
     'Active': 'Yes',
 }, {
     'Employee': 'Pulkit',
     'FirstName': 'System',
     'LastName': 'Admin',
     'Username': 'pulkit',
     'Email': '<EMAIL>',
     'Alias': 'PK000',
     'Role': 'company_admin',
     'IsSuperUser': 'Yes',
     'Active': 'Yes',
 }, {
     'Employee': 'Ra<PERSON>',
     'FirstName': 'System',
     'LastName': 'Support',
     'Username': 'rahul',
     'Email': '<EMAIL>',
     '<PERSON><PERSON>': 'RS000',
     'Role': 'company_admin',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Sunny',
     'FirstName': 'System',
     'LastName': 'Support',
     'Username': 'sny',
     'Email': '<EMAIL>',
     'Alias': 'SNY000',
     'Role': 'company_admin',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Abhishek',
     'FirstName': 'System',
     'LastName': 'Support',
     'Username': 'abhishek',
     'Email': '<EMAIL>',
     'Alias': 'AS000',
     'Role': 'company_admin',
     'IsSuperUser': 'No',
     'Active': 'No',
 }, {
     'Employee': 'Caile',
     'FirstName': 'System',
     'LastName': 'Support',
     'Username': 'caile',
     'Email': '<EMAIL>',
     'Alias': 'CD000',
     'Role': 'company_admin',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Mehak',
     'FirstName': 'System',
     'LastName': 'Operator',
     'Username': 'mehak',
     'Email': '<EMAIL>',
     'Alias': 'MV000',
     'Role': 'Operator',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Samir',
     'FirstName': 'System',
     'LastName': 'Operator',
     'Username': 'samir',
     'Email': '<EMAIL>',
     'Alias': 'SJ000',
     'Role': 'Operator',
     'IsSuperUser': 'No',
     'Active': 'No',
 }, {
     'Employee': 'Raju',
     'FirstName': 'System',
     'LastName': 'Operator',
     'Username': 'raju',
     'Email': '<EMAIL>',
     'Alias': 'RK000',
     'Role': 'Operator',
     'IsSuperUser': 'No',
     'Active': 'No',
 }, {
     'Employee': 'Akshaye',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'akshaye',
     'Email': '<EMAIL>',
     'Alias': 'AK000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Arvind',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'arvind',
     'Email': '<EMAIL>',
     'Alias': 'AS001',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Hemant',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'hemant',
     'Email': '<EMAIL>',
     'Alias': 'HB000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Gourav',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'gourav',
     'Email': '<EMAIL>',
     'Alias': 'GS000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Nikhil',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'nikhil',
     'Email': '<EMAIL>',
     'Alias': 'NS000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Rinu',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'rinu',
     'Email': '<EMAIL>',
     'Alias': 'RA000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Ravinder',
     'FirstName': 'Ravinder',
     'LastName': 'Kumar',
     'Username': 'ravinder',
     'Email': '<EMAIL>',
     'Alias': 'RK001',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Jacinta',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'jacinta',
     'Email': '<EMAIL>',
     'Alias': 'JC000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'No',
 }, {
     'Employee': 'Aysha',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'aysha',
     'Email': '<EMAIL>',
     'Alias': 'AJ000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Ganesh',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'ganesh',
     'Email': '<EMAIL>',
     'Alias': 'GK000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Rajarshee',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'rajarshee',
     'Email': '<EMAIL>',
     'Alias': 'RM000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'No',
 }, {
     'Employee': 'Skye',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'skye',
     'Email': '<EMAIL>',
     'Alias': 'SF000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Vanessa',
     'FirstName': 'Vanessa',
     'LastName': 'Blount',
     'Username': 'vanessa',
     'Email': '<EMAIL>',
     'Alias': 'VB000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Aman',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'aman',
     'Email': '<EMAIL>',
     'Alias': 'AP000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Rajesh',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'rajesh',
     'Email': '<EMAIL>',
     'Alias': 'RJ000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'No',
 }, {
     'Employee': 'Vikram',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'vikram',
     'Email': '<EMAIL>',
     'Alias': 'VS000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'No',
 }, {
     'Employee': 'Tushar',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'tushar',
     'Email': '<EMAIL>',
     'Alias': 'TV000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Akshit',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'akshit',
     'Email': '<EMAIL>',
     'Alias': 'AR000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Manjeet',
     'FirstName': 'Manjeet',
     'LastName': 'Roy',
     'Username': 'manjeet',
     'Email': '<EMAIL>',
     'Alias': 'MR000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Manideep',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'manideep',
     'Email': '<EMAIL>',
     'Alias': 'MS000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Dorothy',
     'FirstName': 'Dorothy',
     'LastName': 'Teng',
     'Username': 'dorothy',
     'Email': '<EMAIL>',
     'Alias': 'DT000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Yogesh',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'yogesh',
     'Email': '<EMAIL>',
     'Alias': 'YD000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Brandon',
     'FirstName': 'Brandon',
     'LastName': 'Wong',
     'Username': 'brandon',
     'Email': '<EMAIL>',
     'Alias': 'BW000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Abhishek',
     'FirstName': 'Abhishek',
     'LastName': 'Holla',
     'Username': 'abhishekh',
     'Email': '<EMAIL>',
     'Alias': 'AH000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'No',
 }, {
     'Employee': 'Gopinath',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'gopinath',
     'Email': '<EMAIL>',
     'Alias': 'GR000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'No',
 }, {
     'Employee': 'Diptendu',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'diptendu',
     'Email': '<EMAIL>',
     'Alias': 'DB000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Andrew',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'andrew',
     'Email': '<EMAIL>',
     'Alias': 'AZ000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Brett',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'brettj',
     'Email': '<EMAIL>',
     'Alias': 'BJ000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Jai',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'jai',
     'Email': '<EMAIL>',
     'Alias': 'JK000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Rushikesh',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'rushikesh',
     'Email': '<EMAIL>',
     'Alias': 'RS001',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Vishal',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'vishal',
     'Email': '<EMAIL>',
     'Alias': 'VP000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }, {
     'Employee': 'Mahavir',
     'FirstName': 'System',
     'LastName': 'Observer',
     'Username': 'mahavir',
     'Email': '<EMAIL>',
     'Alias': 'MA000',
     'Role': 'Observer',
     'IsSuperUser': 'No',
     'Active': 'Yes',
 }]


class Command(BaseCommand):
    help = 'Setup staff employees'

    def handle(self, *args, **options):
        if settings.API_SUPERUSER_PASSWORD:
            user = Employee.objects.get(username='acroot', is_superuser=True)
            user.set_password(settings.API_SUPERUSER_PASSWORD)
            user.save()
            user = Employee.objects.get(username='acroot_us', is_superuser=True)
            user.set_password(settings.API_SUPERUSER_PASSWORD)
            user.save()

        for staff in STAFF:
            email = staff['Email']
            for existing in Employee.objects.filter(email=email).exclude(company_id=1):
                email_parts = existing.email.split('@')
                existing.email = f'{email_parts[0]}1@{email_parts[1]}'
                existing.save()
            for existing in Employee.objects.filter(username=staff['Username']).exclude(company_id=1):
                existing.username = existing.username + '1'
                existing.save()
            for company_id in SYSTEM_COMPANY_IDS:
                company = Company.moderators.get(id=company_id)
                if company_id != 1:  # not AU
                    username = f"{staff['Username']}_{company.country.code}"
                    alias = f"{staff['Alias']}{company.country.code.upper()}"
                else:
                    alias = staff['Alias']
                    username = staff['Username']
                exists = company.employee_set.filter(models.Q(email=email) | models.Q(username=username)).exists()
                if exists:
                    print("Already exists in AC, skipping:", username)
                else:
                    print("Creating:", username)
                    role = EmployeeType.objects.get(name=staff['Role'].lower())
                    employee = Employee(
                        company_id=company_id,
                        first_name=staff['FirstName'],
                        last_name=staff['LastName'],
                        username=username,
                        email=email,
                        alias=alias,
                        type=role,
                        is_staff=True,
                        is_superuser=staff['IsSuperUser'] == 'Yes',
                        is_active=staff['Active'] == 'Yes',
                        password=generate_random_password()
                    )
                    employee.save()
                    if employee.persisted:
                        employee.reset_staff_password()
                    else:
                        print(f"Failed: {username} with errors {employee.errors}")
