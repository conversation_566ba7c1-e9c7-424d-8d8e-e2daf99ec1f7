from pydash import get
from rest_framework.permissions import BasePermission

from core.common.utils import is_production
from core.common.constants import COMPANY_ADMIN_TYPE_ID, OBSERVER_TYPE_ID


class IsNotProduction(BasePermission):
    def has_permission(self, request, view):
        return not is_production()


class IsProduction(BasePermission):
    def has_permission(self, request, view):
        return is_production()


class IsSystemCompany(BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.is_staff


class IsSystemCompanyOrCompanyAdmin(BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and (
                request.user.is_staff or request.user.type_id in [COMPANY_ADMIN_TYPE_ID, OBSERVER_TYPE_ID])


class IsSuperUserOrCompanyAdmin(BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and (
                request.user.is_superuser or request.user.type_id in [COMPANY_ADMIN_TYPE_ID, OBSERVER_TYPE_ID])

class IsStaffOrCompanyUser(BasePermission):
    def has_permission(self, request, view):
        user = request.user
        return user and user.is_authenticated and (user.is_staff or user.company_id == get(view.kwargs, 'company_id'))


class IsCompanyAdmin(BasePermission):
    def has_permission(self, request, view):
        return (request.user and request.user.is_authenticated and
                request.user.type_id in [COMPANY_ADMIN_TYPE_ID, OBSERVER_TYPE_ID])


class IsSuperUser(BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.is_superuser


class IsAllowedToDeleteConditions(BasePermission):
    def has_object_permission(self, request, view, obj):
        user = request.user
        company_id = get(obj, 'company_id', None)
        return user.is_authenticated and (
            user.is_staff
            or ((user.is_company_admin or user.is_observer) and user.company_id == company_id)
        )
