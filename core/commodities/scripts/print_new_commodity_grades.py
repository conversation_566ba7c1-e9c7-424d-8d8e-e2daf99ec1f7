import csv
import json

res = []
count = 58
_c_res = []
grades = ("PRIME MILLING", "MILLING1", "FEED",)
csvfile = open('./OatsGrades.csv', "r")
fieldnames = ("Grades","VARP","TWT","SCRN","UNM","DAMS","STAN",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 8,
        }
    }
    _c_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'VARP', 'name': 'Varietal Purity (% by count)', 'min': 90.0, 'max': 100.0}, {'order': 2, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 3, 'code': 'TWT', 'name': 'Test Weight (kg/hl)', 'min': 40.0, 'max': 90.0}, {'order': 4, 'code': 'SCRN', 'name': 'Screenings (% by weight)', 'min': 0.0, 'max': 100.0}, {'order': 5, 'code': 'UNM', 'name': 'Unmillable Material Above the Screen (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'DAMS', 'name': 'Damaged (%)', 'min': 0.0, 'max': 20.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _c_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_c_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-11-19T08:00:00+00:00",
                "updated_at": "2018-11-19T08:00:00+00:00",
                "commodity_id": 8,
            }
        })
res+=_c_res

_t_res = []
grades = ("TRIT1", "TRIT2",)
csvfile = open('./TriticaleGrades.csv', "r")
fieldnames = ("Grades","MOGR","TWT","SCRN","UNM","SPRO","STAN","PFUN","BURN","FTDG",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 9,
        }
    }
    _t_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 2, 'code': 'TWT', 'name': 'Test Weight (kg/hl)', 'min': 40.0, 'max': 90.0}, {'order': 3, 'code': 'SCRN', 'name': 'Screenings (% by weight)', 'min': 0.0, 'max': 100.0}, {'order': 4, 'code': 'UNM', 'name': 'Unmillable Material Above the Screen (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 5, 'code': 'SPRO', 'name': 'Sprouted (%)', 'min': 0.0, 'max': 10.0}, {'order': 6, 'code': 'STAN', 'name': 'Total Stained (% by count)', 'min': 0.0, 'max': 100.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _t_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_t_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 9,
            }
        })
res+=_t_res

_m_res = []
grades = ("PRIME", "FEED1", "FEED2",)
csvfile = open('./MaizeGrades.csv', "r")
fieldnames = ("Grades","MOGR","TWT","FGNM","SCRN","UNM","DAMS",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 10,
        }
    }
    _m_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 2, 'code': 'TWT', 'name': 'Test Weight (kg/hl)', 'min': 40.0, 'max': 90.0}, {'order': 3, 'code': 'FGNM', 'name': 'Foreign Material (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'SCRN', 'name': 'Screenings (% by weight)', 'min': 0.0, 'max': 100.0}, {'order': 5, 'code': 'UNM', 'name': 'Unmillable Material Above the Screen (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'DAMS', 'name': 'Damaged (%)', 'min': 0.0, 'max': 20.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _m_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_m_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 10,
            }
        })
res+=_m_res

_m_res = []
grades = ("BB1", "BB2",)
csvfile = open('./BroadBeansGrades.csv', "r")
fieldnames = ("Grades","VARP","MOGR","DEFG","MLD","SCRN","PCOL","FGNM","UNM",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 11,
        }
    }
    _m_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'VARP', 'name': 'Varietal Purity (% by count)', 'min': 90.0, 'max': 100.0}, {'order': 2, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 3, 'code': 'DEFG', 'name': 'Total Defective (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'MLD', 'name': 'Storage Mould (% by weight per half litre)', 'min': 0.0, 'max': 30.0}, {'order': 5, 'code': 'SCRN', 'name': 'Screenings (% by weight)', 'min': 0.0, 'max': 100.0}, {'order': 6, 'code': 'PCOL', 'name': 'Poor Colour (% by weight)', 'min': 0.0, 'max': 20.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _m_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_m_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 11,
            }
        })
res+=_m_res

_m_res = []
grades = ("FABC", "FAB1","FAB2","FAB3",)
csvfile = open('./FabaBeansGrades.csv', "r")
fieldnames = ("Grades","VARP","MOGR","DEFG","MLD","PCOL","FGNM","UNM",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 12,
        }
    }
    _m_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'VARP', 'name': 'Varietal Purity (% by count)', 'min': 90.0, 'max': 100.0}, {'order': 2, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 3, 'code': 'DEFG', 'name': 'Total Defective (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'MLD', 'name': 'Storage Mould (% by weight per half litre)', 'min': 0.0, 'max': 30.0}, {'order': 5, 'code': 'PCOL', 'name': 'Poor Colour (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'FGNM', 'name': 'Foreign Material (% by weight)', 'min': 0.0, 'max': 20.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _m_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_m_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 12,
            }
        })
res+=_m_res

_m_res = []
grades = ("ANGUSTIFOLIUS", "ALBUS1","ALBUS2",)
csvfile = open('./LupinsGrades.csv', "r")
fieldnames = ("Grades","VARP","MOGR","DEFG","MLD","PCOL","FGNM","UNM",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 13,
        }
    }
    _m_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'VARP', 'name': 'Varietal Purity (% by count)', 'min': 90.0, 'max': 100.0}, {'order': 2, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 3, 'code': 'DEFG', 'name': 'Total Defective (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'MLD', 'name': 'Storage Mould (% by weight per half litre)', 'min': 0.0, 'max': 30.0}, {'order': 5, 'code': 'PCOL', 'name': 'Poor Colour (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'FGNM', 'name': 'Foreign Material (% by weight)', 'min': 0.0, 'max': 20.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _m_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_m_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 13,
            }
        })
res+=_m_res

_m_res = []
grades = ("SPROUTING","PREMIUM","Grade 1","PROCESSING","MANUFACTURING",)
csvfile = open('./MungbeansGrades.csv', "r")
fieldnames = ("Grades","VARP","MOGR","PSCC","STAN",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 14,
        }
    }
    _m_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'VARP', 'name': 'Varietal Purity (% by count)', 'min': 90.0, 'max': 100.0}, {'order': 2, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 3, 'code': 'PSCC', 'name': 'Poor Seed Coat Colour (% by count)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'STAN', 'name': 'Total Stained (% by count)', 'min': 0.0, 'max': 100.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _m_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_m_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 14,
            }
        })
res+=_m_res

_m_res = []
grades = ("Grade 1","Grade 2","Grade 3",)
csvfile = open('./FieldPeasGrades.csv', "r")
fieldnames = ("Grades","VARP","MOGR","DEFG","MLD","PCOL","FGNM","UNM",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 15,
        }
    }
    _m_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'VARP', 'name': 'Varietal Purity (% by count)', 'min': 90.0, 'max': 100.0}, {'order': 2, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 3, 'code': 'DEFG', 'name': 'Total Defective (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'MLD', 'name': 'Storage Mould (% by weight per half litre)', 'min': 0.0, 'max': 30.0}, {'order': 5, 'code': 'PCOL', 'name': 'Poor Colour (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'FGNM', 'name': 'Foreign Material (% by weight)', 'min': 0.0, 'max': 20.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _m_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_m_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 15,
            }
        })
res+=_m_res

_m_res = []
grades = ("Graded",)
csvfile = open('./VetchGrades.csv', "r")
fieldnames = ("Grades","VARP","MOGR","DEFG","MLD","PCOL","FGNM","UNM",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 16,
        }
    }
    _m_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'VARP', 'name': 'Varietal Purity (% by count)', 'min': 90.0, 'max': 100.0}, {'order': 2, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 3, 'code': 'DEFG', 'name': 'Total Defective (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 4, 'code': 'MLD', 'name': 'Storage Mould (% by weight per half litre)', 'min': 0.0, 'max': 30.0}, {'order': 5, 'code': 'PCOL', 'name': 'Poor Colour (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'FGNM', 'name': 'Foreign Material (% by weight)', 'min': 0.0, 'max': 20.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _m_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_m_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 16,
            }
        })
res+=_m_res

_m_res = []
grades = ("POLYUNSATURATED","MONOUNSATURATED","BIRDSEED",)
csvfile = open('./SunflowerGrades.csv', "r")
fieldnames = ("Grades","MOGR","PRGR","COIL","TWT","IMPU","BRSH","BURN","MLD","DAMS","SPRO",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 17,
        }
    }
    _m_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 2, 'code': 'PRGR', 'name': 'Protein (%)', 'min': 0.0, 'max': 100.0}, {'order': 3, 'code': 'COIL', 'name': 'Oil (%)', 'min': 0.0, 'max': 50.0}, {'order': 4, 'code': 'TWT', 'name': 'Test Weight (kg/hl)', 'min': 50.0, 'max': 100.0}, {'order': 5, 'code': 'IMPU', 'name': 'Impurities (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'BRSH', 'name': 'Split or Broken (%)', 'min': 0.0, 'max': 30.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _m_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_m_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 17,
            }
        })
res+=_m_res

_m_res = []
grades = ("EDIBLE MILLING","EDIBLE MANUFACTURING","CRUSHING",)
csvfile = open('./SoybeanGrades.csv', "r")
fieldnames = ("Grades","MOGR","PRGR","COIL","TWT","IMPU","BRSH","BURN","MLD","DAMS","GREE",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 18,
        }
    }
    _m_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 2, 'code': 'PRGR', 'name': 'Protein (%)', 'min': 0.0, 'max': 100.0}, {'order': 3, 'code': 'COIL', 'name': 'Oil (%)', 'min': 0.0, 'max': 50.0}, {'order': 4, 'code': 'TWT', 'name': 'Test Weight (kg/hl)', 'min': 50.0, 'max': 100.0}, {'order': 5, 'code': 'IMPU', 'name': 'Impurities (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'BRSH', 'name': 'Split or Broken (%)', 'min': 0.0, 'max': 30.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _m_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_m_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 18,
            }
        })
res+=_m_res

_m_res = []
grades = ("Graded",)
csvfile = open('./CottonseedGrades.csv', "r")
fieldnames = ("Grades","MOGR","PRGR","COIL","TWT","IMPU","MLD",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 19,
        }
    }
    _m_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 2, 'code': 'PRGR', 'name': 'Protein (%)', 'min': 0.0, 'max': 20.0}, {'order': 3, 'code': 'COIL', 'name': 'Oil (%)', 'min': 0.0, 'max': 50.0}, {'order': 4, 'code': 'TWT', 'name': 'Test Weight (kg/hl)', 'min': 0.0, 'max': 100.0}, {'order': 5, 'code': 'IMPU', 'name': 'Impurities (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'MLD', 'name': 'Mould - Storage and Field (count by weight per half kg)', 'min': 0.0, 'max': 30.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _m_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_m_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 19,
            }
        })
res+=_m_res

_m_res = []
grades = ("POLYUNSATURATED","MONOUNSATURATED",)
csvfile = open('./SafflowerGrades.csv', "r")
fieldnames = ("Grades","MOGR","PRGR","COIL","TWT","IMPU","BRSH","BURN","MLD","DAMS","SPRO","GREE",)
reader = csv.DictReader(csvfile, fieldnames)
for _gr in grades:
    _json = {
        "model": "commodities.grade",
        "pk": count,
        "fields": {
            "name": _gr,
            "specs": [],
            "created_at": "2018-11-19T08:00:00+00:00",
            "updated_at": "2018-11-19T08:00:00+00:00",
            "commodity_id": 20,
        }
    }
    _m_res.append(_json)
    count += 1
_specs = [{'order': 1, 'code': 'MOGR', 'name': 'Moisture (%)', 'min': 0.0, 'max': 30.0}, {'order': 2, 'code': 'PRGR', 'name': 'Protein (%)', 'min': 0.0, 'max': 100.0}, {'order': 3, 'code': 'COIL', 'name': 'Oil (%)', 'min': 0.0, 'max': 50.0}, {'order': 4, 'code': 'TWT', 'name': 'Test Weight (kg/hl)', 'min': 50.0, 'max': 100.0}, {'order': 5, 'code': 'IMPU', 'name': 'Impurities (% by weight)', 'min': 0.0, 'max': 20.0}, {'order': 6, 'code': 'BRSH', 'name': 'Split or Broken (%)', 'min': 0.0, 'max': 30.0}]
for i, row in enumerate(reader):
    if(i!=0):
        _dict = dict(row)
        _grade_parts = _dict['Grades'].split(" ")
        _grade_name = " ".join(_grade_parts[0:-1])
        _bound = _grade_parts[-1].lower()
        _js = list(filter(lambda _j: _j['fields']['name'] == _grade_name, _m_res))[0]
        _js["fields"]["order"] = grades.index(_grade_name) + 1
        for k, v in _dict.items():
            if(k != 'Grades'):
                _s = list(filter(lambda _g: _g['code'] == k, _specs))
                if _s:
                    _default_spec = _s[0]
                    _existing_spec = list(filter(lambda x: x['code'] == k, _js["fields"]["specs"]))
                    if not _existing_spec:
                        _data = {'code': k, 'name': _default_spec['name'], 'order': _default_spec['order']}
                        _data[_bound.lower()] = float(v)
                        _js["fields"]["specs"].append(_data)
                    else:
                        _existing_spec[0][_bound] = float(v)
_m_res.append({
            "model": "commodities.grade",
            "pk": count,
            "fields": {
                "name": 'UNGRADED',
                "category": '',
                "specs": _specs,
                "order": _js["fields"]["order"] + 1,
                "created_at": "2018-04-02T08:00:00+00:00",
                "commodity_id": 20,
            }
        })
res+=_m_res

print(json.dumps(res))
