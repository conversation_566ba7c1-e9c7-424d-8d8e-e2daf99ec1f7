# Generated by Django 4.0.8 on 2022-12-29 13:12

from django.db import migrations


def update_default_have_field(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    Company.objects.filter(type_id=1).update(have_field=True)


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0111_company_have_field_historicalcompany_have_field'),
    ]

    operations = [
        migrations.RunPython(update_default_have_field)
    ]
