# contains Postgres specific function expression wrappers
from django.db.models import Func, F, Value, IntegerField, FloatField


class Translate(Func):  # pylint: disable=abstract-method
    function = 'translate'
    arity = 3


class RegexpReplace(Func):  # pylint: disable=abstract-method
    function = 'regexp_replace'
    arity = 4


class Lower(Func):  # pylint: disable=abstract-method
    function = 'lower'
    arity = 1


class Round(Func):  # pylint: disable=abstract-method
    function = 'ROUND'
    template = '%(function)s(%(expressions)s::numeric, 2)'


class Round5(Func):  # pylint: disable=abstract-method
    function = 'ROUND'
    template = '%(function)s(%(expressions)s::numeric, 5)'


class CustomRegexWrapper:
    def __init__(self, field, regex='[^[:alnum:]]', mode='ig'):
        self.field = field
        self.regex = regex
        self.mode = mode
        self.expression = None
        self.build()

    def build(self):
        self.translate()
        self.lower()
        self.regexp_replace()

    def translate(self):
        self.expression = Translate(F(self.field), Value(' _-()'), Value(''))

    def lower(self):
        self.expression = Lower(self.expression)

    def regexp_replace(self):
        self.expression = RegexpReplace(self.expression, Value(self.regex), Value(''), Value(self.mode))


class Strip(Func):  # pylint: disable=abstract-method
    function = 'TRIM'
    template = "%(function)s(both '0' FROM  %(expressions)s::varchar)"


class CastAsInteger(Func):  # pylint: disable=abstract-method
    function = 'CAST'
    template = '%(expressions)s::int'
    output_field = IntegerField()


class Similarity(Func):  # pylint: disable=abstract-method
    function = 'SIMILARITY'
    output_field = FloatField()

    def __init__(self, expression1, expression2, **extra):
        super().__init__(expression1, expression2, **extra)
