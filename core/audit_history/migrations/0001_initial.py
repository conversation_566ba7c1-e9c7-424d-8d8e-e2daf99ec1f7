# Generated by Django 2.1.3 on 2018-12-26 11:15

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AuditHistory',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('instance_id', models.PositiveIntegerField()),
                ('description', models.TextField()),
                ('exclusions', django.contrib.postgres.fields.jsonb.J<PERSON><PERSON>ield(blank=True, null=True)),
                ('args', django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True)),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.DO_NOTHING, related_name='audit_history_audithistory_related_created_by', related_query_name='audit_history_audithistorys_created_by', to=settings.AUTH_USER_MODEL)),
                ('instance_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.ContentType')),
            ],
            options={
                'db_table': 'audit_history',
            },
        ),
    ]
