from rest_framework.generics import ListAPIView
from rest_framework.permissions import IsAuthenticatedOrReadOnly

from core.countries.models import Country
from .models import Bank
from .serializers import BankSerializer

class BanksView(ListAPIView):
    permission_classes = [IsAuthenticatedOrReadOnly]
    serializer_class = BankSerializer
    def get_queryset(self):
        country = Country.get_requesting_country()
        query_params = self.request.query_params.copy()
        query_params['country_id'] = country.id
        return Bank.search(query_params)
