import io

from django.db.models import Count, Sum
from django.db.models.functions import TruncMonth, Round
from django.http import FileResponse
from pydash import get
from rest_framework import status
from rest_framework.generics import ListAPIView, get_object_or_404
from rest_framework.permissions import IsAuthenticatedOr<PERSON><PERSON><PERSON><PERSON><PERSON>, Is<PERSON>d<PERSON><PERSON>ser, AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView

from core.contracts.models import Contract
from core.freights.models import <PERSON><PERSON>ght<PERSON><PERSON>r, FreightContract
from core.invoices.models import Invoice
from core.services.internal.errbit import ERRBIT_LOGGER
from ..models import BaseModel
from ..pdfgenerator import PDFGenerator
from ..serializers import HistoryEventSerializer
from ...countries.models import Season, Country


class PDFView(APIView):
    permission_classes = [IsAuthenticatedOrReadOnly]
    def post(self, request):
        try:
            payload, user = request.data, request.user
            entity = None
            entity_id = payload.get('id', None)

            if not entity_id:
                return Response(data={'errors': ['Invalid Request!']}, status=status.HTTP_400_BAD_REQUEST)

            if payload.get('entity') == 'contract':
                entity = self.get_contract(entity_id)
            elif payload.get('entity') == 'freightorder':
                entity = self.get_freight_order(entity_id)
            elif payload.get('entity') == 'freightcontract':
                entity = self.get_freight_contract(entity_id)
            elif payload.get('entity') == 'invoice':
                entity = self.get_invoice(entity_id)

            if not entity:
                return Response(data={'errors': ['Invalid Request!']}, status=status.HTTP_400_BAD_REQUEST)

            data = self.get_pdf_data(entity, user, payload.get('party'))
            pdf_name = data.get_pdf_name()
            pdf_buffer = io.BytesIO(data.pdf)
            pdf_buffer.name = data.get_pdf_name()
            pdf_buffer.seek(0)

            response = FileResponse(pdf_buffer, as_attachment=True, filename=pdf_name)
            response["Content-Disposition"] = f'attachment; filename={pdf_name or "data.pdf"}'
            response['Access-Control-Expose-Headers'] = 'Content-Disposition,Content-Length'
            return response

        except Exception as e:  # pylint: disable=broad-except
            ERRBIT_LOGGER.raise_errbit(f'Exception occurred at PDF View {0}'.format(e))
            return Response(data={'errors': ['Internal Server Error!']}, status=status.HTTP_400_BAD_REQUEST)

    def get_pdf_data(self, entity, user, party=None):
        pdf = PDFGenerator(entity, user=user, party=party)
        pdf.generate()
        return pdf

    def get_contract(self, pk):
        return Contract.objects.get(pk=pk)

    def get_freight_order(self, pk):
        return FreightOrder.objects.get(pk=pk)

    def get_freight_contract(self, pk):
        return FreightContract.objects.get(pk=pk)

    def get_invoice(self, pk):
        return Invoice.objects.get(pk=pk)


class IdentifierExists(APIView):
    def get(self, _, identifier):
        data = Contract.objects.filter(identifier=identifier).exists(
        ) or FreightOrder.objects.filter(identifier=identifier).exists(
        ) or FreightContract.objects.filter(identifier=identifier).exists()

        return Response({'result': data}, status=status.HTTP_200_OK)


class AbstractEntityStatsView(APIView):
    permission_classes = (IsAdminUser, )
    created_date_field = 'created_at'
    tonnage_field = 'tonnage'
    can_apply_created_by = True
    def get_distribution(self):
        queryset = self.get_queryset()
        result = {
            'all': self.get_monthly_distribution(queryset),
            'created': self.get_monthly_distribution(self.get_created_queryset(queryset))
        }
        return result

    def get_monthly_distribution(self, queryset):
        return queryset.annotate(
            month=TruncMonth(self.created_date_field)
        ).values('month').annotate(
            count=Count('id')
        ).order_by('month').values('month', 'count') if queryset is not None else None

    def get_created_queryset(self, queryset):
        company_id = self.get_company_id()
        if self.can_apply_created_by and company_id:
            return queryset.filter(created_by__company_id=company_id)
        return None

    def get_stats(self):
        queryset, created_queryset = self.apply_aggregation(self.get_queryset(), True)
        aggregate_criteria = self.get_aggregate_criteria()
        return {
            'all': {key: queryset[key] for key in aggregate_criteria},
            'created': None if created_queryset is None else {key: created_queryset[key] for key in aggregate_criteria}
        }

    def get_aggregate_criteria(self):
        return {'count': Count('id'), 'tonnage': Round(Sum(self.tonnage_field))}

    def filter_queryset(self, queryset):
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')
        if date_from:
            queryset = queryset.filter(**{f'{self.created_date_field}__gte': date_from})
        if date_to:
            queryset = queryset.filter(**{f'{self.created_date_field}__lte': date_to})

        return queryset

    def apply_aggregation(self, queryset, aggregate=False):
        criteria = self.get_aggregate_criteria()
        created_queryset = self.get_created_queryset(queryset)
        generic_queryset = None
        if criteria:
            if created_queryset is not None:
                created_queryset = created_queryset.aggregate(
                    **criteria) if aggregate else created_queryset.annotate(**criteria)
            generic_queryset = queryset.aggregate(**criteria) if aggregate else queryset.annotate(**criteria)
        return generic_queryset, created_queryset

    def get_company_id(self):
        return self.request.query_params.get('company_id', None)

    def is_distribution(self):
        return self.request.query_params.get('distribution', False) in ['true', True, 'True']

    def get(self, _):
        return Response(
            {'distribution': self.get_distribution()} if self.is_distribution() else self.get_stats()
        )


class BaseHistoryView(ListAPIView):
    permission_classes = (IsAdminUser, )
    serializer_class = HistoryEventSerializer
    model = BaseModel
    default_manager = 'objects'
    lookup_url_kwarg = 'id'
    lookup_field = 'id'
    relations = []

    def get_object(self):
        return get_object_or_404(
            get(self.model, self.default_manager), **{self.lookup_field: self.kwargs.get(self.lookup_url_kwarg)}
        )

    def get_queryset(self):
        return BaseModel.get_history_events(instance=self.get_object(), relations=self.relations)


class SeasonsView(APIView):
    permission_classes = (AllowAny, )

    def get(self, request): # pylint: disable=unused-argument
        return Response(
            data=Season.get_seasons_for_clients(Country.get_requesting_country()),
            status=status.HTTP_200_OK)
