# Generated by Django 3.2.6 on 2021-11-29 04:14

from django.db import migrations


# delete ALBUS1/2 grades
def delete_lupins_grades(apps, schema_editor):
    Grade = apps.get_model('commodities', 'Grade')
    Grade.objects.filter(id__in=[73, 74]).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('commodities', '0023_auto_20211129_0405'),
    ]

    operations = [
        migrations.RunPython(delete_lupins_grades)
    ]
