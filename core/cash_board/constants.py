STATUSES = (
    ('active', 'Active'),
    ('inactive', 'Inactive'),
    ('completed', 'Completed'),
    ('partial', 'Partially Fulfilled'),
)

CASH_PRICE_COMMON_AUDIT_MESSAGE = \
    'Cash price at {site_company}({site}) for ( {commodity} | {grade} | {season} | {limit} MT | {sustainable} '
CASH_PRICE_AUDIT = {
    'create': 'Added the ' + CASH_PRICE_COMMON_AUDIT_MESSAGE + ')',
    'create_on_behalf': 'Added the ' + CASH_PRICE_COMMON_AUDIT_MESSAGE + ' | ${price}) on behalf of {buyer}',
    'cash_out': 'Cashed Out {tonnage} MT against ' + CASH_PRICE_COMMON_AUDIT_MESSAGE + ')',
    'cash_out_on_behalf': 'Cashed Out {tonnage} MT against ' +
                          CASH_PRICE_COMMON_AUDIT_MESSAGE + ') on behalf of {seller}',
    'inactive': CASH_PRICE_COMMON_AUDIT_MESSAGE + ') Inactivated',
    'inactive_on_behalf': CASH_PRICE_COMMON_AUDIT_MESSAGE + ') Inactivated on behalf of {buyer}',
    'completed': CASH_PRICE_COMMON_AUDIT_MESSAGE + ') Completed',
}
CASH_PRICE_UPLOAD_CSV_HEADERS = [
    'Location', 'Location owner', 'Grade', 'Sustainable', 'Season', 'Contract payment terms',
    'Contract payment scales', 'Contract terms and conditions', 'Price','Qty','Start date','End date',
'Buyer (Optional) - Leave Blank if Uploading for your company. Enter Buyer Company Name if posting on behalf of Buyer',
    'Buyer NGR (optional)'
]
