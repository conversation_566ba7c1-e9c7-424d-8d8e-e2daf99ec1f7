# Generated by Django 4.1.13 on 2024-04-03 10:48

from django.db import migrations
from core.companies.models import Company
from core.alerts.models import Alert
from core.common.constants import SYSTEM_TYPE_ID

def setup_default_email_alert_for_missing_docket_sms(apps, schema_editor):
    for company_id in Company.objects.filter().exclude(type_id=SYSTEM_TYPE_ID).values_list('id', flat=True):
        alert_data = {
            "name": "missing_docket_alert",
            "channel": "email",
            "frequency": "weekly",
            "recipients": [
                {
                    "party": "own_company",
                    "employee_roles": [
                        "company_admin"
                    ]
                }
            ],
            "company_id": company_id
        }
        Alert.persist(alert_data)


class Migration(migrations.Migration):

    dependencies = [
        ('alerts', '0022_alter_alert_name_alter_historicalalert_name'),
    ]

    operations = [
        migrations.RunPython(setup_default_email_alert_for_missing_docket_sms),
    ]
