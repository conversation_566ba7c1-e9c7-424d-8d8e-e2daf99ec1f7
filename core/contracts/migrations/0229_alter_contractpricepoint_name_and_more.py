# Generated by Django 5.1.8 on 2025-07-22 10:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0228_merge_20250715_1053'),
    ]

    operations = [
        migrations.AlterField(
            model_name='contractpricepoint',
            name='name',
            field=models.CharField(choices=[('delivered_site', 'Delivered Site'), ('ex_farm_store', 'Ex Farm / Store'), ('delivered_buyer', 'Delivered Buyer'), ('track', 'Track'), ('free_in_store', 'Free In Store (FIS)'), ('free_on_truck', 'Free On Truck (FOT)'), ('free_on_rail', 'Free on Rail (FOR)'), ('delivered_dct', 'Delivered DCT'), ('delivered_market_zone', 'Delivered Market Zone'), ('cost_and_freight', 'Cost And Freight (CFR)'), ('cost_insurance_freight', 'Cost, Insurance, Freight (CIF)'), ('free_on_board', 'Free On Board (FOB)'), ('ex_vessel', 'Ex Vessel'), ('stock_swap', 'Stock Swap'), ('dpu', 'Delivered Place Unloaded (DPU)'), ('in_store', 'In Store')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalcontractpricepoint',
            name='name',
            field=models.CharField(choices=[('delivered_site', 'Delivered Site'), ('ex_farm_store', 'Ex Farm / Store'), ('delivered_buyer', 'Delivered Buyer'), ('track', 'Track'), ('free_in_store', 'Free In Store (FIS)'), ('free_on_truck', 'Free On Truck (FOT)'), ('free_on_rail', 'Free on Rail (FOR)'), ('delivered_dct', 'Delivered DCT'), ('delivered_market_zone', 'Delivered Market Zone'), ('cost_and_freight', 'Cost And Freight (CFR)'), ('cost_insurance_freight', 'Cost, Insurance, Freight (CIF)'), ('free_on_board', 'Free On Board (FOB)'), ('ex_vessel', 'Ex Vessel'), ('stock_swap', 'Stock Swap'), ('dpu', 'Delivered Place Unloaded (DPU)'), ('in_store', 'In Store')], max_length=100),
        ),
    ]
