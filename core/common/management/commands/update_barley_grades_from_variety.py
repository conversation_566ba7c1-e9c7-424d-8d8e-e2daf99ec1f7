from django.core.management import BaseCommand
from pydash import compact, get

from core.common.utils import get_grade_display_name


class Command(BaseCommand):
    help = 'Populate new Barley grades from varieties'

    def handle(self, *args, **options):
        from core.commodities.models import Grade, Variety
        from core.contracts.models import Contract, TitleTransfer, Spread
        from core.freights.models import FreightOrder, FreightContract
        from core.farms.models import Storage
        from core.vendor_decs.models import CommodityVendorDec
        from core.company_sites.models import FreightSlot
        from core.loads.models import Load

        from core.common.constants import BARLEY_COMMODITY_ID
        failures = []
        errors = []
        success = []
        skipped = []
        UNGRADED_BARLEY = 'UNGRADED - BARLEY'

        def process_slots():
            slots_to_update = []
            movement_queryset = FreightSlot.objects.filter(commodity_id=BARLEY_COMMODITY_ID, movement_id__isnull=False)
            for slot in movement_queryset:
                movement = slot.movement
                slot.grade_id = movement.planned_grade_id
                slots_to_update.append(slot)

            order_queryset = FreightSlot.objects.filter(
                commodity_id=BARLEY_COMMODITY_ID, order_id__isnull=False, movement_id__isnull=True)
            for slot in order_queryset:
                order = slot.order
                slot.grade_id = order.planned_grade_id
                slots_to_update.append(slot)
            FreightSlot.objects.bulk_update(slots_to_update, fields=['grade_id'], batch_size=1000)

        def process(
                klass, variety_id_field='variety_id', grade_id_field='grade_id',
                process_spread=False, manager_name='objects'
        ):
            filters = {
                'commodity_id': BARLEY_COMMODITY_ID,
                f"{grade_id_field}__isnull": False
            }
            if klass not in (Contract, FreightOrder, FreightContract):
                filters[f"{variety_id_field}__isnull"] = False

            manager = getattr(klass, manager_name)
            instances_to_update = []
            for instance in manager.filter(**filters):
                print("*Migrating*", instance)
                grade_name = get_grade_display_name(
                    BARLEY_COMMODITY_ID, get(instance, 'variety_id'),
                    get(instance, grade_id_field), get(instance, 'season'),
                    True
                )
                if get(instance, 'variety.f_type') or grade_name in ['F1', 'F2', 'F3']:
                    skipped.append(instance)
                else:
                    grade = Grade.objects.filter(name=grade_name, commodity_id=BARLEY_COMMODITY_ID).first()
                    if grade:
                        setattr(instance, grade_id_field, grade.id)
                        if klass == FreightContract:
                            inload_grade_id = get(instance.inload, 'grade_id')
                            outload_grade_id = get(instance.outload, 'grade_id')
                            if inload_grade_id == outload_grade_id != grade.id:
                                setattr(instance, grade_id_field, inload_grade_id)
                        if klass == FreightOrder:
                            movement_grade_ids = compact(set(
                                FreightContract.objects.filter(
                                    order=instance).values_list('planned_grade_id', flat=True)))
                            if len(movement_grade_ids) == 1 and movement_grade_ids[0] != grade.id:
                                setattr(instance, grade_id_field, movement_grade_ids[0])
                        if process_spread:
                            instance.save()
                        else:
                            instances_to_update.append(instance)
                        if instance.errors:
                            errors.append(instance)
                        else:
                            success.append(instance)
                            if process_spread:
                                movement_grade_ids = list(instance.freight_contracts.values_list(
                                    'planned_grade_id', flat=True))
                                order_grade_ids = list(instance.freight_orders.values_list(
                                    'planned_grade_id', flat=True))
                                tt_grade_ids = list(instance.titletransfer_set.values_list(
                                    'grade_id', flat=True))
                                child_grade_ids = list(compact({*movement_grade_ids, *order_grade_ids, *tt_grade_ids}))
                                should_add_child_grades_in_spread = len(child_grade_ids) and (
                                        child_grade_ids != [instance.grade_id])
                                spread = instance.spread
                                if spread:
                                    details = spread.details
                                    new_details = []
                                    for detail in details:
                                        name = detail.get('name')
                                        if name and name not in [
                                            *Variety.BARLEY_F_TYPES, UNGRADED_BARLEY, 'UNGRADED', 'Ungraded',
                                            'Malt1', 'Malt2', 'Malt3', 'F1', 'F2', 'F3'
                                        ]:
                                            spread_grade = Grade.objects.filter(
                                                name=name.strip(), commodity_id=BARLEY_COMMODITY_ID).first()
                                            if spread_grade:
                                                detail['name'] = spread_grade.name
                                                detail['id'] = spread_grade.id
                                                new_details.append(detail)
                                            else:
                                                failures.append(spread)
                                        else:
                                            new_details.append(detail)
                                    if should_add_child_grades_in_spread:
                                        max_order = max(detail.get('order') for detail in spread.details)
                                        for grade_id in child_grade_ids:
                                            if grade_id != instance.grade_id:
                                                child_grade = Grade.objects.filter(id=grade_id).first()
                                                if child_grade:
                                                    instance.type_id = 2
                                                    max_order += 1
                                                    new_details.append(
                                                        {
                                                            'name': child_grade.name,
                                                            'id': grade_id,
                                                            'order': max_order,
                                                            'value': 0,
                                                            'price_variation': ''
                                                        }
                                                    )
                                    if len(new_details) >= len(spread.details):
                                        instance.save()
                                        spread.details = new_details
                                        spread.save()
                                        if spread.errors:
                                            errors.append(spread)
                                        else:
                                            success.append(spread)
                                else:
                                    if should_add_child_grades_in_spread:
                                        new_details = [{
                                                           'name': grade_name,
                                                           'id': instance.grade_id,
                                                           'order': 0,
                                                           'value': 0,
                                                           'price_variation': ''
                                                       }]
                                        max_order = 0
                                        for grade_id in child_grade_ids:
                                            if grade_id != instance.grade_id:
                                                child_grade = Grade.objects.filter(id=grade_id).first()
                                                if child_grade:
                                                    max_order += 1
                                                    new_details.append(
                                                        {
                                                            'name': child_grade.name,
                                                            'id': grade_id,
                                                            'order': max_order,
                                                            'value': 0,
                                                            'price_variation': ''
                                                        }
                                                    )
                                        if len(new_details) > 1:
                                            spread = Spread(details=new_details)
                                            spread.save()
                                            instance.spread = spread
                                            instance.type_id = 2
                                            instance.save()
                    else:
                        failures.append(instance)
            if instances_to_update:
                klass.objects.bulk_update(instances_to_update, fields=[grade_id_field], batch_size=1000)

        process(klass=Load)
        process(klass=FreightContract, grade_id_field='planned_grade_id')
        process(klass=FreightOrder, grade_id_field='planned_grade_id')
        process_slots()
        process(klass=TitleTransfer)
        process(klass=Contract, process_spread=True)
        process(klass=Storage, manager_name='all')
        process(klass=CommodityVendorDec)

        print("*****Result****")
        print("****Failures****")
        print(failures)
        print("*"*50)
        print("****Errors****")
        print(errors)
        print("*"*50)
        print("****Skipped****")
        print(len(skipped))
        print("*"*50)
        print("****Success****")
        print(len(success))
