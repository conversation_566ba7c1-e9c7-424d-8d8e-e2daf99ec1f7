from django.core.management import BaseCommand

from core.cash_board.models import CashPrices
from core.commodities.models import Grade
from core.common.models import MergedEntity
from core.companies.models import XeroMapping
from core.contract_bids.models import ContractBid
from core.contracts.models import StockAllocation, ChemicalApplication


class Command(BaseCommand):
    help = 'Migrate Sulphate of Ammonia to SOA Grade'

    def handle(self, *args, **options):
        pairs = [[1885, 2085], [2071, 2084]]
        from core.contracts.models import Contract, TitleTransfer
        from core.freights.models import FreightOrder, FreightContract
        from core.vendor_decs.models import CommodityVendorDec
        from core.company_sites.models import FreightSlot
        from core.loads.models import Load
        from core.stocks.models import Stock

        # duplicate_names = Grade.objects.values(
        #     'name', 'commodity_id').annotate(name_count=Count('name')).filter(name_count__gt=1)
        # result = list(Grade.objects.filter(
        #     name__in=duplicate_names.values('name')
        # ).order_by('commodity_id', 'name', 'id').values('id', 'name', 'commodity_id'))
        # grades = {}
        # for res in result:
        #     grades[res['name']] = grades.get(res['name'], [])
        #     grades[res['name']].append(res['id'])
        #
        # duplicate_grade_ids = list(grades.values())
        from_grade_ids = []

        def process(
                klass, grade_id_field='grade_id',
                manager_name='objects', process_spread=False, stock=False
        ):
            print(f"****Processing {klass}****")
            for grade_pair in pairs:
                from_grade_id = grade_pair[0]
                to_grade_id = grade_pair[1]
                from_grade_ids.append(from_grade_id)
                filters = {
                    f"{grade_id_field}": from_grade_id
                }
                manager = getattr(klass, manager_name)
                queryset = manager.filter(**filters)
                if stock:
                    Stock.recalculate_stocks({
                                                 'grade_id': from_grade_id
                                             })
                    continue
                if process_spread:
                    for contract in queryset.filter(spread__isnull=False):
                        spread = contract.spread
                        new_details = []
                        for detail in spread.details:
                            if detail.get('id') == from_grade_id:
                                detail['id'] = to_grade_id
                                detail['name'] = Grade.objects.get(id=to_grade_id).name
                            new_details.append(detail)
                        spread.details = new_details
                        spread.save()
                queryset.update(**{
                    grade_id_field: to_grade_id
                })

        process(klass=Load)
        process(klass=FreightContract, grade_id_field='planned_grade_id')
        process(klass=FreightOrder, grade_id_field='planned_grade_id')
        process(klass=FreightSlot)
        process(klass=TitleTransfer)
        process(klass=ContractBid)
        process(klass=StockAllocation)
        process(klass=ChemicalApplication)
        process(klass=Contract, process_spread=True)
        process(klass=CommodityVendorDec)
        process(klass=CashPrices)
        process(klass=Stock, stock=True)
        process(klass=XeroMapping)

        for pair in pairs:
            MergedEntity(from_id=pair[0], to_id=pair[1], entity_model_name='Grade').save()

        print("***GRADES to DELETE***", set(from_grade_ids))
