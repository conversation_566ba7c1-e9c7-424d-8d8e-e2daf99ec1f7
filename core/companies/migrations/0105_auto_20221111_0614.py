# Generated by Django 4.0.7 on 2022-11-11 06:14

from django.db import migrations


def update_show_cash_prices_to_all_growers(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    Company.objects.filter(type_id=1).update(show_cash_prices_to_all=False)


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0104_company_show_cash_prices_to_all_and_more'),
    ]

    operations = [
        migrations.RunPython(update_show_cash_prices_to_all_growers)
    ]
