from django.core.management.base import BaseCommand
from django_fsm import can_proceed

from core.contracts.models import Contract

class Command(BaseCommand):
    help = "Mark CC to delivered if needed"

    def handle(self, *args, **kwargs):  # pylint: disable=unused-argument
        contracts = Contract.objects.filter(status='in_progress')
        for contract in contracts:
            try:
                status_method = getattr(contract, 'delivered')
                if can_proceed(status_method):
                    status_method()
                    contract.save()
                    self.stdout.write(
                        self.style.SUCCESS(
                            'Contract transition success for ID:(%s)"!' % contract.id)
                    )
            except Exception as ex: # pylint: disable=broad-except
                self.stdout.write(self.style.ERROR('Contract transition failed for ID:(%s)"!' % contract.id))
                self.stdout.write(self.style.ERROR('Error: %s!' % ex.args[0]))
