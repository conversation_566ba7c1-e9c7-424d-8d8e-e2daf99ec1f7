# Generated by Django 4.1.13 on 2024-06-17 08:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0173_company_gst_number_historicalcompany_gst_number'),
    ]

    operations = [
        migrations.AddField(
            model_name='company',
            name='warehouse_invoice_frequency',
            field=models.CharField(blank=True, choices=[('weekly', 'Weekly'), ('monthly', 'Monthly')], default='monthly', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='company',
            name='warehouse_invoice_start_of_week',
            field=models.IntegerField(blank=True, choices=[(0, 'Sunday'), (1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday')], default=1, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='warehouse_invoice_frequency',
            field=models.Char<PERSON>ield(blank=True, choices=[('weekly', 'Weekly'), ('monthly', 'Monthly')], default='monthly', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompany',
            name='warehouse_invoice_start_of_week',
            field=models.IntegerField(blank=True, choices=[(0, 'Sunday'), (1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday')], default=1, null=True),
        ),
    ]
