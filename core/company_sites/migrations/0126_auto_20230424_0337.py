# Generated by Django 4.1.7 on 2023-04-24 03:37
from django.db import migrations


def update_mass_limit_visibility_in_slot(apps, schema_editor):
    SiteManagementSettings = apps.get_model('company_sites', 'SiteManagementSettings')
    for settings in SiteManagementSettings.objects.filter():
        for field in settings.fields:
            if field['id'] == 'massLimitCode' and not field['show']:
                field['show'] = True
                settings.save(update_fields=['fields'])


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0125_auto_20230328_0700'),
    ]

    operations = [
        migrations.RunPython(update_mass_limit_visibility_in_slot)
    ]
