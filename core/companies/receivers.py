# In receivers.py
from django.apps import apps
from django.db.models.signals import post_save
from django.dispatch import receiver

from core.companies.models import Company


@receiver(post_save, sender=Company)
def create_fleet_truck_rego(sender, created, instance, update_fields,
                            **kwargs):  # pylint: disable=unused-argument
    if created:
        apps.get_model('trucks', 'Truck').create_fleet_truck(company=instance)
        if instance.is_grower and instance.show_cash_prices_to_all:
            instance.show_cash_prices_to_all = False
            instance.save()
