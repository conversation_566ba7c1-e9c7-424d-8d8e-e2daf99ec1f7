# Generated by Django 4.1.9 on 2023-08-16 03:37

from django.db import migrations


def update_load_type_and_user_type_for_variety_mandatory_for_companies(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    for company in Company.objects.filter(is_variety_mandatory=True):
        company.load_type_for_variety_mandatory = ['title_transfers_and_cash_outs']
        company.user_type_for_variety_mandatory = ['growers', 'non_growers']
        company.save()


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0142_remove_company_is_variety_mandatory_in_cash_price_and_title_transfer_and_more'),
    ]

    operations = [
        migrations.RunPython(update_load_type_and_user_type_for_variety_mandatory_for_companies)
    ]
