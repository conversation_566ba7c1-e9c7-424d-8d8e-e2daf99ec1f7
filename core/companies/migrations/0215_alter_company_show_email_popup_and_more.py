# Generated by Django 5.1.8 on 2025-05-07 10:32

from django.db import migrations, models

def enable_show_email_popup_for_all_existing_companies(apps, schema_editor):
    Company = apps.get_model('companies', 'Company')
    Company.objects.update(show_email_popup=True)

class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0214_auto_20250505_0432'),
    ]

    operations = [
        migrations.AlterField(
            model_name='company',
            name='show_email_popup',
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name='historicalcompany',
            name='show_email_popup',
            field=models.BooleanField(default=True),
        ),
        migrations.RunPython(enable_show_email_popup_for_all_existing_companies)
    ]
