# Generated by Django 4.1.13 on 2023-12-22 07:32


from django.db import migrations


def remove_duplicate_alerts(apps, schema_editor):
    Alert = apps.get_model('alerts', 'Alert')
    Company = apps.get_model('companies', 'Company')
    Recipient = apps.get_model('alerts', 'Recipient')
    Employee = apps.get_model('profiles', 'Employee')

    for company in Company.objects.all():
        alerts = Alert.objects.filter(company_id=company.id)
        if alerts.count() > 1:
            alert_names = []
            for alert in alerts:
                if alert.name not in alert_names:
                    alert_names.append(alert.name)
                    duplicate_alerts = alerts.filter(name=alert.name).exclude(id=alert.id)
                    if duplicate_alerts.count() > 0:
                        existing_recipients = alert.recipient_set.all()
                        for duplicate_alert in duplicate_alerts:
                            for recipient in duplicate_alert.recipient_set.all():
                                if recipient.party not in existing_recipients.values_list('party', flat=True):
                                    new_recipient = Recipient(party=recipient.party, employees=recipient.employees,
                                                              alert=alert, employee_roles=recipient.employee_roles)
                                    new_recipient.save()
                                else:
                                    recipient_party = alert.recipient_set.filter(party=recipient.party).first()
                                    existing_employee_roles = recipient_party.employee_roles
                                    existing_employee_roles.extend(recipient.employee_roles)
                                    employee_roles = list(set(existing_employee_roles))
                                    recipient_party.employee_roles = employee_roles
                                    if recipient.party == 'own_company':
                                        recipient_party.employees.set(Employee.objects.filter(id__in=[*recipient.employees.values_list('id', flat=True),
                                                                                                      *recipient_party.employees.values_list('id', flat=True)]))
                                    recipient_party.save()
                        alert.save()
                        alerts.filter(id__in=duplicate_alerts.values_list('id', flat=True)).delete()


class Migration(migrations.Migration):
    dependencies = [
        ('alerts', '0020_alter_historicalrecipient_party_and_more'),
    ]

    operations = [
        migrations.RunPython(remove_duplicate_alerts)
    ]