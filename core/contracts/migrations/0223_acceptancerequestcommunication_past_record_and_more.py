# Generated by Django 5.1.7 on 2025-04-17 08:08

import dirtyfields.dirtyfields
import django.db.models.deletion
import django.utils.timezone
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0222_alter_paymentscale_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='acceptancerequestcommunication',
            name='past_record',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AddField(
            model_name='historicalacceptancerequestcommunication',
            name='past_record',
            field=models.J<PERSON>NField(blank=True, default=dict, null=True),
        ),
        migrations.AddField(
            model_name='historicaltitletransfercommunication',
            name='past_record',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AddField(
            model_name='titletransfercommunication',
            name='past_record',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='acceptancerequestcommunication',
            name='recipients',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='acceptancerequestcommunication',
            name='subject',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalacceptancerequestcommunication',
            name='recipients',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalacceptancerequestcommunication',
            name='subject',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicaltitletransfercommunication',
            name='recipients',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicaltitletransfercommunication',
            name='subject',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='titletransfercommunication',
            name='recipients',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='titletransfercommunication',
            name='subject',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='HistoricalTitleTransferAcceptanceRequest',
            fields=[
                ('id', models.IntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(blank=True, editable=False, null=True)),
                ('resolved', models.BooleanField(default=False)),
                ('accepted', models.BooleanField(default=False)),
                ('args', models.JSONField(blank=True, null=True)),
                ('type', models.CharField(choices=[('confirm', 'Confirm'), ('void', 'Void'), ('amend', 'Amend'), ('title_transfer', 'Title Transfer'), ('truck_vd', 'Vendor Dec'), ('grain_vd', 'Vendor Dec')], max_length=100)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('request_reason', models.TextField(blank=True, null=True)),
                ('acceptance_required', models.BooleanField(blank=True, default=True, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('created_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('title_transfer', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='contracts.titletransfer')),
                ('updated_by', models.ForeignKey(blank=True, db_constraint=False, default=1, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical title transfer acceptance request',
                'verbose_name_plural': 'historical title transfer acceptance requests',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='TitleTransferAcceptanceRequest',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('resolved', models.BooleanField(default=False)),
                ('accepted', models.BooleanField(default=False)),
                ('args', models.JSONField(blank=True, null=True)),
                ('type', models.CharField(choices=[('confirm', 'Confirm'), ('void', 'Void'), ('amend', 'Amend'), ('title_transfer', 'Title Transfer'), ('truck_vd', 'Vendor Dec'), ('grain_vd', 'Vendor Dec')], max_length=100)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('request_reason', models.TextField(blank=True, null=True)),
                ('acceptance_required', models.BooleanField(blank=True, default=True, null=True)),
                ('created_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_created_by', related_query_name='%(app_label)s_%(class)ss_created_by', to=settings.AUTH_USER_MODEL)),
                ('title_transfer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='acceptance_requests', to='contracts.titletransfer')),
                ('updated_by', models.ForeignKey(default=1, on_delete=django.db.models.deletion.SET_DEFAULT, related_name='%(app_label)s_%(class)s_related_updated_by', related_query_name='%(app_label)s_%(class)ss_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'title_transfer_acceptance_requests',
            },
            bases=(dirtyfields.dirtyfields.DirtyFieldsMixin, models.Model),
        ),
        migrations.AddField(
            model_name='historicaltitletransfercommunication',
            name='request',
            field=models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='contracts.titletransferacceptancerequest'),
        ),
        migrations.AddField(
            model_name='titletransfercommunication',
            name='request',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='communication', to='contracts.titletransferacceptancerequest'),
        ),
    ]
