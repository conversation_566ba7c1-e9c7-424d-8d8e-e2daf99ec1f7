# Generated by Django 2.1.7 on 2019-05-24 04:37

from django.db import migrations

def update_slot_default_fields_customer(apps, schema_editor):
    SiteManagementSettings = apps.get_model('company_sites', 'SiteManagementSettings')
    for settings in SiteManagementSettings.objects.all():
        fields = settings.fields.copy()
        for field in fields:
            if not field['mandatory']:
                field['label'] = field['label'] +  ' (Optional)'
            if field['id'] == 'customer':
                field['customisable'] = False
            if field['id'] == 'priority':
                field['show'] = False

        settings.fields = fields
        settings.save()

class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0036_auto_20190523_0342'),
    ]

    operations = [
        migrations.RunPython(update_slot_default_fields_customer)
    ]
