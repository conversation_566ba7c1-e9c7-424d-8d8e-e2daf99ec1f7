{% load static %}
{% load app_filters %}

<!doctype html>
<html>
  <head>
    <meta charset=utf-8>
  </head>
  <body>
    <p>Hi {{ recipient.name }},</p>
    <p>
        Please update the truck {{ 'rego' | to_country_label:slot.site.country_code | lower}} for the below bookings before they arrive at {{slot.site.company.name}}({{slot.site.name}}) on
        <br />
        <a href="{{slot_url}}">{{label}}</a>.
    </p>
    <p>
      Please take a minute to watch the below short videos that outline the correct procedure for booking.
    </p>
    <p>
        <a href="https://www.youtube.com/watch?v=FWE_RxKC1nQ" target="_blank" rel="noopener noreferrer">How to add trucks that are owned by your company</a><br/>
        <a href="https://www.youtube.com/watch?v=0EqH0zTZpZ0" target="_blank" rel="noopener noreferrer">How to add your sub freight providers and make a booking on their behalf</a><br/>
        <a href="https://www.youtube.com/watch?v=B8BJf0QYREA" target="_blank" rel="noopener noreferrer">How to add drivers/employees</a><br/>
        <a href="https://www.youtube.com/watch?v=zCme5FhWwSg" target="_blank" rel="noopener noreferrer">How to make a site booking</a><br/>
    </p>
    <br/>
    <p>
      <b>PLEASE NOTE IF THE {{ 'rego' | to_country_label:slot.site.country_code | upper}} AND DRIVER IS NOT ADDED, THE CARRIER MAY BE TURNED AWAY FROM SITE ON ARRIVAL.</b>
    </p>
    <br/>
    <p>
        Kind Regards, <br />
        {{slot.site.company.name}} (via AgriChain)
    </p>
  </body>
</html>
