# Generated by Django 2.1.3 on 2019-01-24 11:13

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('company_sites', '0010_auto_20181218_1003'),
    ]

    operations = [
        migrations.AddField(
            model_name='companysite',
            name='area',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companysite',
            name='ld',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companysite',
            name='mode',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='companysite',
            name='tracks',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompanysite',
            name='area',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompanysite',
            name='ld',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompanysite',
            name='mode',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='historicalcompanysite',
            name='tracks',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, null=True),
        ),
    ]
