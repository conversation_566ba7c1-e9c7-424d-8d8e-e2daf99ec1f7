# Generated by Django 4.1.7 on 2023-04-05 10:36

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('alerts', '0007_alter_alert_name_alter_historicalalert_name'),
    ]

    operations = [
        migrations.AlterField(
            model_name='alert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit'), ('inload_done', 'Inload Done'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Information'), ('delivery_information', 'Delivery Information')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalalert',
            name='name',
            field=models.CharField(choices=[('spec_avg_report', 'Receival Report'), ('mass_limit', 'Mass Limit'), ('inload_done', 'Inload Done'), ('class_1_material', 'Class 1 material'), ('pickup_information', 'Pickup Information'), ('delivery_information', 'Delivery Information')], max_length=100),
        ),
        migrations.AlterField(
            model_name='historicalrecipient',
            name='party',
            field=models.CharField(choices=[('site', 'Site'), ('customer', 'Customer'), ('freight_provider', 'Freight Provider'), ('sub_freight_provider', 'Sub Freight Provider'), ('own_company', 'Own Company'), ('stock_owner', 'Stock Owner')], max_length=100),
        ),
        migrations.AlterField(
            model_name='recipient',
            name='employees',
            field=models.ManyToManyField(related_name='recipients', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='recipient',
            name='party',
            field=models.CharField(choices=[('site', 'Site'), ('customer', 'Customer'), ('freight_provider', 'Freight Provider'), ('sub_freight_provider', 'Sub Freight Provider'), ('own_company', 'Own Company'), ('stock_owner', 'Stock Owner')], max_length=100),
        ),
    ]
